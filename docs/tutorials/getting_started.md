# 🚀 快速开始

欢迎来到稷下学宫！让我们用5分钟时间启动你的AI仙人军团。

## 📋 系统要求

### 基础环境
- **Python**: 3.9+ 
- **Node.js**: 16+ (用于N8N工作流)
- **Git**: 最新版本

### 数据库支持
- **PostgreSQL**: 12+ (关系数据存储)
- **Zilliz/Milvus**: 2.3+ (向量数据库)
- **MongoDB**: 5.0+ (文档数据库，可选)

### API密钥准备
- **OpenAI API Key**: 用于AI仙人对话
- **Mastodon实例**: 用于社交发布（可选）

## ⚡ 快速安装

### 1. 克隆项目
```bash
git clone https://github.com/your-username/jixia-academy.git
cd jixia-academy
```

### 2. 创建虚拟环境
```bash
python -m venv jixia_env
source jixia_env/bin/activate  # Linux/Mac
# 或
jixia_env\Scripts\activate     # Windows
```

### 3. 安装依赖
```bash
pip install -r requirements.txt
```

### 4. 配置环境变量
```bash
cp .env.example .env
```

编辑 `.env` 文件：
```env
# OpenAI配置
OPENAI_API_KEY=your_openai_api_key_here

# 数据库配置
DATABASE_URL=postgresql://user:password@localhost/jixia_academy
ZILLIZ_HOST=your_zilliz_host
ZILLIZ_TOKEN=your_zilliz_token

# Mastodon配置（可选）
MASTODON_INSTANCE_URL=https://your-mastodon-instance.com
MASTODON_TAISHANG_LAOJUN_ACCESS_TOKEN=your_token_here
```

## 🎭 启动AI仙人

### 模式1: Console测试模式
```bash
cd jixia_academy_clean
python test_debate_modes.py
```

你将看到：
```
🎭 稷下学宫辩论开始
📝 话题: 美联储加息对全球市场的影响
📺 输出模式: console
==================================================

太上老君: 🧙‍♂️ 从宏观经济角度看，美联储加息对全球市场的影响需要综合考虑多方面因素...

吕洞宾: ⚔️ 以剑仙之名发誓，美联储加息对全球市场的影响的长期价值值得关注...

张果老: 🐴 倒骑驴看市场，我的量化模型显示美联储加息对全球市场的影响有以下特征...
```

### 模式2: Streamlit网页模式
```bash
streamlit run app/streamlit_app.py
```

访问 `http://localhost:8501` 查看网页界面。

### 模式3: 完整RSS触发模式
```bash
python scripts/start_jixia_academy.py
```

## 🔧 配置指南

### 数据库设置

#### PostgreSQL设置
```sql
-- 创建数据库
CREATE DATABASE jixia_academy;

-- 创建辩论触发表
CREATE TABLE debate_triggers (
    id SERIAL PRIMARY KEY,
    topic VARCHAR(200) NOT NULL,
    impact_score DECIMAL(3,1) NOT NULL,
    rss_events JSONB NOT NULL,
    created_at TIMESTAMP DEFAULT NOW(),
    processed BOOLEAN DEFAULT FALSE
);
```

#### Zilliz设置
```python
# 创建向量集合
from pymilvus import connections, Collection, FieldSchema, CollectionSchema, DataType

# 连接Zilliz
connections.connect(
    alias="default",
    host="your_zilliz_host",
    port="19530",
    token="your_zilliz_token"
)

# 创建集合
fields = [
    FieldSchema(name="id", dtype=DataType.VARCHAR, max_length=100, is_primary=True),
    FieldSchema(name="content", dtype=DataType.VARCHAR, max_length=5000),
    FieldSchema(name="embedding", dtype=DataType.FLOAT_VECTOR, dim=384)
]

schema = CollectionSchema(fields, "稷下学宫共享记忆")
collection = Collection("jixia_shared_memory", schema)
```

### Mastodon配置

#### 创建应用
1. 登录你的Mastodon实例
2. 前往 `设置 → 开发 → 新建应用`
3. 填写应用信息：
   - **应用名称**: 稷下学宫
   - **网站**: https://your-project-url.com
   - **权限**: `read write follow`

#### 获取密钥
```bash
# 为每个AI仙人创建独立的应用和密钥
MASTODON_TAISHANG_LAOJUN_CLIENT_ID=your_client_id
MASTODON_TAISHANG_LAOJUN_CLIENT_SECRET=your_client_secret
MASTODON_TAISHANG_LAOJUN_ACCESS_TOKEN=your_access_token
```

## 🎯 验证安装

### 运行健康检查
```bash
python scripts/health_check.py
```

期望输出：
```
🔍 稷下学宫健康检查
====================
✅ Python环境: 3.11.5
✅ 依赖包: 全部安装
✅ 环境变量: 配置完整
✅ 数据库连接: PostgreSQL正常
✅ 向量数据库: Zilliz正常
✅ AI模型: OpenAI API正常
✅ Mastodon: 连接正常

🎉 稷下学宫准备就绪！
```

### 测试AI仙人对话
```bash
python -c "
from jixia_academy_clean.core.debate_controller import JixiaDebateController
import asyncio

async def test():
    controller = JixiaDebateController()
    result = await controller.start_debate(
        '测试话题', 
        {'impact_score': 7.0}, 
        controller.DebateOutputMode.CONSOLE
    )
    print('✅ AI仙人对话测试成功！')

asyncio.run(test())
"
```

## 🚨 常见问题

### Q: OpenAI API调用失败
```bash
# 检查API密钥
python -c "
import openai
import os
from dotenv import load_dotenv

load_dotenv()
openai.api_key = os.getenv('OPENAI_API_KEY')

try:
    response = openai.ChatCompletion.create(
        model='gpt-3.5-turbo',
        messages=[{'role': 'user', 'content': 'Hello'}],
        max_tokens=10
    )
    print('✅ OpenAI API正常')
except Exception as e:
    print(f'❌ OpenAI API错误: {e}')
"
```

### Q: 数据库连接失败
```bash
# 检查PostgreSQL连接
python -c "
import psycopg2
import os
from dotenv import load_dotenv

load_dotenv()
try:
    conn = psycopg2.connect(os.getenv('DATABASE_URL'))
    print('✅ PostgreSQL连接正常')
    conn.close()
except Exception as e:
    print(f'❌ PostgreSQL连接错误: {e}')
"
```

### Q: Zilliz连接失败
```bash
# 检查Zilliz连接
python -c "
from pymilvus import connections
import os
from dotenv import load_dotenv

load_dotenv()
try:
    connections.connect(
        alias='default',
        host=os.getenv('ZILLIZ_HOST'),
        port='19530',
        token=os.getenv('ZILLIZ_TOKEN')
    )
    print('✅ Zilliz连接正常')
except Exception as e:
    print(f'❌ Zilliz连接错误: {e}')
"
```

## 🎉 下一步

恭喜！你已经成功启动了稷下学宫。现在你可以：

1. **[了解AI仙人](../ai-immortals/)** - 认识11个独特的AI分析师
2. **[探索架构设计](../architecture/)** - 深入理解技术实现
3. **[配置RSS触发](../features/rss-pulse.md)** - 设置自动化市场监控
4. **[部署到生产环境](../deployment/)** - 将系统部署到云端

---

**🎭 欢迎来到AI仙人的世界！让我们一起改变金融分析的未来！**