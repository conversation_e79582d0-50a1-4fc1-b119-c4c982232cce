# 数据库配置指南

## 📋 概述

太公心易BI系统支持两种数据库配置：
- **本地开发**: SQLite（默认）
- **生产环境**: PostgreSQL（推荐用于Heroku部署）

## 🏠 本地开发配置

### SQLite（默认）

系统默认使用SQLite数据库，无需额外配置：

```bash
# 启动系统
python run_morning_briefing.py
```

数据库文件位置：`src/core/data/members.db`

### 默认用户

系统会自动创建默认用户：
- **用户名**: `guest`
- **密码**: `guest`
- **权限**: 免费会员（可查看基础报告）

## 🚀 Heroku PostgreSQL 配置

### 1. 添加PostgreSQL插件

```bash
# 登录Heroku
heroku login

# 为你的应用添加PostgreSQL插件
heroku addons:create heroku-postgresql:hobby-dev -a your-app-name
```

### 2. 获取数据库连接信息

```bash
# 获取DATABASE_URL
heroku config:get DATABASE_URL -a your-app-name

# 输出示例：
# postgresql://username:password@hostname:port/database
```

### 3. 设置环境变量

```bash
# 设置必要的环境变量
heroku config:set MEMBER_SYSTEM_ENABLED=True -a your-app-name
heroku config:set ANALYSIS_ENGINE_ENABLED=True -a your-app-name

# 如果需要，设置其他API密钥
heroku config:set OPENAI_API_KEY=your_openai_key -a your-app-name
```

### 4. 数据同步

如果你有本地SQLite数据需要迁移到Heroku PostgreSQL：

```bash
# 设置本地环境变量
export DATABASE_URL="postgresql://username:password@hostname:port/database"

# 运行同步脚本
python scripts/sync_to_heroku.py
```

### 5. 部署应用

```bash
# 提交代码
git add .
git commit -m "Add database configuration"

# 部署到Heroku
git push heroku main
```

## 🔧 环境变量配置

### 必需的环境变量

```bash
# 数据库配置（Heroku自动设置）
DATABASE_URL=postgresql://username:password@hostname:port/database

# 会员系统
MEMBER_SYSTEM_ENABLED=True

# 分析引擎
ANALYSIS_ENGINE_ENABLED=True
```

### 可选的环境变量

```bash
# API配置
OPENAI_API_KEY=your_openai_api_key
ALPHA_VANTAGE_API_KEY=your_alpha_vantage_key
POLYGON_API_KEY=your_polygon_key

# IB API配置
IB_HOST=127.0.0.1
IB_PORT=7497
IB_CLIENT_ID=1

# 应用配置
DEBUG=False
LOG_LEVEL=INFO
```

## 📊 数据库架构

### 会员表 (members)

| 字段 | 类型 | 说明 |
|------|------|------|
| id | INTEGER/SERIAL | 主键 |
| username | TEXT/VARCHAR | 用户名（唯一） |
| email | TEXT/VARCHAR | 邮箱（唯一） |
| password_hash | TEXT/VARCHAR | 密码哈希 |
| membership_level | TEXT/VARCHAR | 会员等级 |
| created_at | TEXT/TIMESTAMP | 创建时间 |
| last_login | TEXT/TIMESTAMP | 最后登录 |
| is_active | BOOLEAN | 是否激活 |
| subscription_expires | TEXT/TIMESTAMP | 订阅过期时间 |

### 报告访问记录表 (report_access)

| 字段 | 类型 | 说明 |
|------|------|------|
| id | INTEGER/SERIAL | 主键 |
| member_id | INTEGER | 会员ID（外键） |
| report_date | TEXT/VARCHAR | 报告日期 |
| access_time | TEXT/TIMESTAMP | 访问时间 |
| report_type | TEXT/VARCHAR | 报告类型 |

### 日报表 (daily_reports)

| 字段 | 类型 | 说明 |
|------|------|------|
| id | INTEGER/SERIAL | 主键 |
| report_date | TEXT/VARCHAR | 报告日期（唯一） |
| report_data | TEXT | 报告数据（JSON） |
| created_at | TEXT/TIMESTAMP | 创建时间 |

## 🔒 会员权限系统

### 免费会员 (FREE)
- ✅ 查看每日报告
- ❌ AI分析
- ❌ 详细推理
- ❌ 历史报告
- ❌ 导出功能

### 基础会员 (BASIC)
- ✅ 查看每日报告
- ✅ AI分析
- ❌ 详细推理
- ✅ 历史报告（7天）
- ❌ 导出功能

### 高级会员 (PREMIUM)
- ✅ 查看每日报告
- ✅ AI分析
- ✅ 详细推理
- ✅ 历史报告（30天）
- ✅ 导出功能
- ✅ 实时提醒

### VIP会员 (VIP)
- ✅ 所有功能
- ✅ API访问
- ✅ 优质股票池
- ✅ 无限历史报告

## 🛠️ 故障排除

### 常见问题

1. **数据库连接失败**
   ```bash
   # 检查DATABASE_URL格式
   echo $DATABASE_URL
   
   # 测试连接
   python -c "from src.core.member_system import MemberSystem; ms = MemberSystem(); print('连接成功' if ms.use_postgres else '使用SQLite')"
   ```

2. **默认用户未创建**
   ```bash
   # 手动创建默认用户
   python -c "from src.core.member_system import MemberSystem; ms = MemberSystem(); print('用户创建完成')"
   ```

3. **权限问题**
   ```bash
   # 检查用户权限
   python -c "from src.core.member_system import MemberSystem; ms = MemberSystem(); member = ms.authenticate_member('guest', 'guest'); print(ms.get_member_permissions(member) if member else '用户不存在')"
   ```

### 日志查看

```bash
# Heroku日志
heroku logs --tail -a your-app-name

# 本地日志
python run_morning_briefing.py
```

## 📞 技术支持

如果遇到问题，请检查：
1. 环境变量是否正确设置
2. 数据库连接是否正常
3. 依赖包是否完整安装
4. Heroku应用配置是否正确

更多技术支持，请参考项目文档或联系开发团队。