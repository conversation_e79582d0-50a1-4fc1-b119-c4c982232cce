# 项目重构迁移指南

## 概述

本文档描述了 Cauldron 项目从旧的 Django 架构迁移到现代化的 FastAPI + Streamlit 架构的过程和新的项目结构。

## 重构目标

1. **移除Django依赖** - Django仅用于一个webhook端点，过于臃肿
2. **统一依赖管理** - 使用单一的`pyproject.toml`管理所有依赖
3. **简化架构** - 使用FastAPI处理API，Streamlit处理UI
4. **保持功能完整性** - 确保所有现有功能正常工作

## 架构变更

### 旧架构 (Django-based)
```
cauldron_legacy/
├── cauldron_django/        # Django项目配置
│   ├── settings.py
│   ├── urls.py
│   ├── wsgi.py
│   └── asgi.py
├── manage.py               # Django管理工具
└── requirements.txt        # 依赖文件
```

### 新架构 (FastAPI + Streamlit)
```
cauldron/
├── src/                  # 核心源代码
│   ├── api/              # FastAPI 应用
│   ├── scripts/          # 命令行脚本
│   └── ...
├── app/                  # Streamlit 应用
├── jixia_academy/        # 稷下学宫 AI 辩论子系统
└── pyproject.toml        # 项目和依赖管理
```

## 主要变更

### 1. API服务器 (api_server.py)

**替代**: Django的URL路由和视图
**功能**: 
- n8n webhook接收端点
- 健康检查端点
- 系统状态API

**端点映射**:
- `POST /api/webhook/n8n/` ← Django的`n8n_webhook`视图
- `GET /health` ← 新增健康检查
- `GET /api/status` ← 新增状态查询

### 2. 启动脚本 (start.py)

**替代**: Django的`manage.py`
**功能**:
```bash
# 启动API服务器
python start.py api

# 启动Streamlit应用
python start.py streamlit

# 启动稷下学宫
python start.py jixia

# 启动所有服务
python start.py all

# 运行策略
python src/scripts/start_system.py cauldron

# 安装依赖
python start.py install

# 运行测试
python start.py test

# 查看状态
python start.py status
```

### 3. 依赖管理 (pyproject.toml)

**替代**: 分散的`requirements.txt`文件
**优势**:
- 统一管理所有依赖
- 支持可选依赖组 (dev, production, deepnote)
- 现代Python包管理标准
- 包含项目元数据和工具配置

**依赖组**:
- `[project.dependencies]` - 核心依赖
- `[project.optional-dependencies.dev]` - 开发工具
- `[project.optional-dependencies.production]` - 生产环境
- `[project.optional-dependencies.deepnote]` - Deepnote环境

## 迁移步骤

### 第一阶段: 创建新组件 ✅

1. ✅ 创建`api_server.py` - FastAPI服务器
2. ✅ 创建`pyproject.toml` - 统一依赖管理
3. ✅ 创建`start.py` - 统一启动脚本
4. ✅ 更新`.gitignore` - 移除Django相关规则

### 第二阶段: 测试新组件

1. 🔄 测试FastAPI服务器
2. 🔄 验证webhook功能
3. 🔄 测试依赖安装
4. 🔄 验证所有启动选项

### 第三阶段: 移除Django组件

1. ⏳ 删除`cauldron_django/`目录
2. ⏳ 删除`manage.py`
3. ⏳ 移除Django相关依赖
4. ⏳ 更新部署配置

### 第四阶段: 清理和优化

1. ⏳ 清理旧的requirements.txt文件
2. ⏳ 更新文档
3. ⏳ 优化项目结构
4. ⏳ 更新CI/CD配置

## 服务端口分配

| 服务 | 端口 | 描述 |
|------|------|------|
| FastAPI API服务器 | 8001 | 处理webhook和API调用 |
| Streamlit应用 | 8501 | 主要UI界面 |
| 稷下学宫 (Chainlit) | 8000 | AI辩论系统 |

## 兼容性说明

### 保持兼容的功能
- ✅ n8n webhook接收
- ✅ MainRunner股票扫描逻辑
- ✅ Streamlit UI界面
- ✅ 稷下学宫AI辩论系统
- ✅ 所有现有的数据处理逻辑

### 移除的功能
- ❌ Django Admin界面 (不常用)
- ❌ Django ORM (项目未使用数据库模型)
- ❌ Django中间件和认证系统

## 安装和使用

### 安装依赖
```bash
# 使用pip安装
pip install -e .

# 或使用uv (推荐)
uv pip install -e .

# 安装开发依赖
pip install -e ".[dev]"
```

### 启动服务
```bash
# 启动所有服务
python start.py all

# 或分别启动
python start.py api      # API服务器
python start.py streamlit # UI界面
python start.py jixia    # AI辩论系统
```

### 开发模式
```bash
# API服务器 (自动重载)
python start.py api

# 运行测试
python start.py test

# 查看状态
python start.py status
```

## 故障排除

### 常见问题

1. **导入错误**: 确保使用`python start.py install`安装依赖
2. **端口冲突**: 使用`--port`参数指定不同端口
3. **路径问题**: 确保在项目根目录运行命令

### 回滚方案

如果新架构出现问题，可以临时回滚到Django:
1. 恢复`cauldron_django/`目录
2. 使用旧的`requirements.txt`
3. 运行`python manage.py runserver`

## 后续优化

1. **容器化**: 创建Docker配置
2. **监控**: 添加服务监控和日志
3. **测试**: 增加自动化测试覆盖
4. **文档**: 完善API文档
5. **性能**: 优化启动时间和资源使用

## 总结

这次重构简化了项目架构，移除了不必要的Django依赖，统一了依赖管理，并提供了更灵活的服务启动方式。新架构更适合项目的实际需求，维护成本更低，扩展性更好。