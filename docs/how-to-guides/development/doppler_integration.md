# Cauldron项目Doppler集成指南

## 概述

Doppler是现代化的密钥管理服务，为Cauldron项目提供安全、便捷的环境变量管理。相比传统的`.env`文件，Doppler提供：

- 🔐 **企业级安全**：加密存储，访问控制
- 🔄 **实时同步**：配置变更立即生效
- 🌍 **多环境管理**：开发/测试/生产环境隔离
- 👥 **团队协作**：稷下学宫AI代理配置共享
- 🚀 **Heroku集成**：一键同步到Heroku Config Vars

## 安装Doppler CLI

### macOS
```bash
brew install dopplerhq/cli/doppler
```

### Linux/WSL
```bash
curl -Ls https://cli.doppler.com/install.sh | sh
```

### 验证安装
```bash
doppler --version
```

## 项目设置

### 1. 登录Doppler
```bash
doppler login
```

### 2. 创建项目
```bash
doppler projects create cauldron
```

### 3. 配置环境
```bash
# 开发环境
doppler configure set project=cauldron config=development

# 测试环境  
doppler configure set project=cauldron config=staging

# 生产环境
doppler configure set project=cauldron config=production
```

## 配置迁移

### 自动迁移（推荐）
使用我们提供的迁移脚本：

```bash
cd /Users/<USER>/cauldron
python scripts/migrate_to_doppler.py
```

脚本会自动：
- 解析现有`.env`文件
- 按类别分组配置（数据库、AI服务、金融API等）
- 创建Doppler项目和环境
- 上传所有密钥
- 生成迁移报告

### 手动迁移
如果需要精细控制，可以手动添加密钥：

```bash
# 数据库配置
doppler secrets set DATABASE_URL="postgres://..." --config development
doppler secrets set ZILLIZ_TOKEN="6fb75098..." --config development

# AI服务密钥
doppler secrets set OPENROUTER_API_KEY_1="sk-or-v1-..." --config development
doppler secrets set ANTHROPIC_AUTH_TOKEN="sk-BQonAHs8..." --config development

# 金融API
doppler secrets set finnhub="d1g22p1r01..." --config development
doppler secrets set IB_HOST="127.0.0.1" --config development
```

## 配置分类

Cauldron项目的配置按以下类别组织：

### 🗄️ 数据库服务
- `DATABASE_URL` - Heroku PostgreSQL
- `ZILLIZ_*` - 向量数据库
- `mongodb_*` - MongoDB Atlas

### 🤖 AI服务
- `OPENROUTER_API_KEY_*` - 稷下学宫辩论模型
- `ANTHROPIC_*` - Claude API
- `LITELLM_*` - 统一AI接口

### 💰 金融数据API
- `finnhub`, `coingecko` - 市场数据
- `IB_*` - Interactive Brokers
- `coinbase_*` - 加密货币

### 📱 社交平台
- `MASTODON_*` - 稷下学宫社交
- `n8n_*` - 工作流自动化

## 使用方式

### 本地开发
```bash
# 运行Streamlit应用
doppler run -- streamlit run app/streamlit_app.py

# 运行稷下学宫
doppler run -- python scripts/start_jixia_academy.py

# 运行测试
doppler run -- pytest tests/
```

### 环境切换
```bash
# 切换到测试环境
doppler configure set config=staging
doppler run -- python your_script.py

# 切换回开发环境
doppler configure set config=development
```

### 查看配置
```bash
# 列出所有密钥
doppler secrets list

# 查看特定密钥（遮蔽显示）
doppler secrets get DATABASE_URL

# 下载所有配置为.env格式
doppler secrets download --no-file --format env
```

## Heroku部署集成

### 1. 安装Heroku Doppler插件
```bash
heroku plugins:install heroku-doppler
```

### 2. 连接Doppler到Heroku
```bash
# 生成服务令牌
doppler configs tokens create heroku-production --config production

# 在Heroku中设置
heroku config:set DOPPLER_TOKEN="dp.st.production.xxx" -a your-app-name
```

### 3. 更新Procfile
```procfile
web: doppler run -- gunicorn app:app
worker: doppler run -- python scripts/background_worker.py
```

### 4. 同步配置
```bash
# 一键同步Doppler配置到Heroku
doppler secrets sync heroku --app your-app-name --config production
```

## 代码集成

### 更新ConfigManager
你的`ConfigManager`已经支持Doppler，会自动检测并使用：

```python
from src.core.config_manager import ConfigManager

# 自动检测Doppler
config = ConfigManager()

# 强制使用Doppler
config = ConfigManager(use_doppler=True)

# 强制使用.env文件
config = ConfigManager(use_doppler=False)
```

### 环境变量访问
```python
import os

# 正常使用os.getenv，Doppler会自动注入
database_url = os.getenv("DATABASE_URL")
api_key = os.getenv("OPENROUTER_API_KEY_1")
```

## 安全最佳实践

### 1. 访问控制
```bash
# 邀请团队成员
doppler workplace <NAME_EMAIL> --role member

# 设置项目权限
doppler projects <NAME_EMAIL> --role developer
```

### 2. 服务令牌
```bash
# 为生产环境创建只读令牌
doppler configs tokens create production-readonly --config production --access read

# 为CI/CD创建特定权限令牌
doppler configs tokens create ci-cd --config staging --access read
```

### 3. 审计日志
在Doppler Dashboard中查看：
- 配置变更历史
- 访问日志
- 令牌使用情况

## 故障排除

### 常见问题

**Q: Doppler CLI无法连接**
```bash
# 检查网络连接
doppler configure get

# 重新登录
doppler logout
doppler login
```

**Q: 配置未生效**
```bash
# 验证当前配置
doppler configure get

# 检查密钥是否存在
doppler secrets list | grep YOUR_KEY

# 测试运行
doppler run -- env | grep YOUR_KEY
```

**Q: Heroku同步失败**
```bash
# 检查令牌权限
doppler configs tokens list

# 验证Heroku连接
heroku config -a your-app-name
```

### 回退到.env文件
如果需要临时回退：

```python
# 在代码中强制使用.env
config = ConfigManager(use_doppler=False)
```

或设置环境变量：
```bash
export DOPPLER_DISABLED=true
python your_script.py
```

## 迁移检查清单

- [ ] 安装Doppler CLI
- [ ] 创建Cauldron项目
- [ ] 运行迁移脚本
- [ ] 验证所有密钥已上传
- [ ] 测试本地开发环境
- [ ] 配置Heroku集成
- [ ] 更新CI/CD流程
- [ ] 备份原始.env文件
- [ ] 更新团队文档

## 下一步

1. **测试迁移**：确保所有功能正常工作
2. **团队培训**：让团队成员熟悉Doppler工作流
3. **生产部署**：将生产环境迁移到Doppler
4. **监控优化**：使用Doppler的分析功能优化配置管理

---

*这个指南会随着项目发展持续更新。如有问题，请查看Doppler官方文档或联系团队。*
