# 🚀 Quick Start Guide

Get the Cauldron system running in under 5 minutes!

## Prerequisites

- Python 3.8 or higher
- Git
- 4GB+ RAM recommended

## Step 1: <PERSON>lone and Setup

```bash
# Clone the repository
git clone https://github.com/jingminzhang/cauldron.git
cd cauldron

# Create virtual environment (recommended)
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt
```

## Step 2: Configuration

```bash
# Copy environment template
cp .env.example .env

# Edit configuration (use your preferred editor)
nano .env
```

### Required Configuration

```env
# Basic settings
OPENROUTER_API_KEY=your_openrouter_key_here
STREAMLIT_PORT=8501

# Optional: Enhanced features
YAHOO_FINANCE_API_KEY=your_yahoo_key  # For enhanced market data
IB_GATEWAY_HOST=127.0.0.1              # If using Interactive Brokers
```

## Step 3: Launch Application

```bash
# Start the main Streamlit application
streamlit run app/streamlit_app.py

# Or use the Cauldron variant
python app/cauldron_app.py
```

## Step 4: Access the System

1. Open your browser to `http://localhost:8501`
2. You should see the Cauldron dashboard
3. Try the AI debate system in the "稷下学宫 (Jixia Academy)" tab

## What's Next?

### Explore Features
- **AI Debates**: Navigate to the Jixia Academy tab for multi-agent discussions
- **Market Analysis**: Check the financial analysis tools
- **MCP Services**: Explore the Model Context Protocol integrations

### Optional Enhancements

#### Enable Advanced Market Data
```bash
# Install Yahoo Finance MCP (optional)
./scripts/install/install_yahoo_finance_mcp.sh

# Start MCP services
docker-compose -f docker-compose.mcp.yml up -d
```

#### Set up Interactive Brokers (Advanced)
```bash
# See full IB setup guide
cat docs/how-to-guides/ib_fundamentals_guide.md
```

## Troubleshooting

### Common Issues

**Port already in use?**
```bash
# Use different port
streamlit run app/streamlit_app.py --server.port 8502
```

**Missing dependencies?**
```bash
# Install development dependencies
pip install -r requirements/master_requirements.md
```

**AI models not responding?**
- Verify your OpenRouter API key in `.env`
- Check internet connection
- Try different models in the configuration

## Get Help

- 📖 **Documentation**: Browse [docs/](../) for detailed guides
- 🐛 **Issues**: Report problems on [GitHub Issues](https://github.com/jingminzhang/cauldron/issues)
- 💬 **Discussions**: Join [GitHub Discussions](https://github.com/jingminzhang/cauldron/discussions)

## Next Steps

1. **[Architecture Overview](architecture-overview.md)** - Understand the system design
2. **[First Contribution](first-contribution.md)** - Make your first code contribution
3. **[Features Guide](../features/)** - Explore all available features

---

🎉 **Welcome to the Cauldron community!** You're now ready to explore AI-powered financial intelligence.
