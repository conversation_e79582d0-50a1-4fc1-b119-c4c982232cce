# GraphRAG 完整指南

## 🎯 什么是GraphRAG？

GraphRAG是微软开发的一个**知识图谱增强的检索增强生成(Retrieval-Augmented Generation)**系统，它结合了：

- **知识图谱** - 构建实体和关系的图谱
- **向量检索** - 传统的语义搜索
- **LLM推理** - 大语言模型的生成能力

### 核心优势
- 🧠 **多跳推理** - 可以进行复杂的关系推理
- 🔗 **实体关系** - 理解数据中的实体和关系
- 📊 **全局理解** - 不仅仅是局部相似性匹配
- 🎯 **精准回答** - 基于知识图谱的结构化推理

## 🏗️ GraphRAG架构

```
文档输入 → 实体提取 → 关系识别 → 知识图谱构建 → 向量化 → 检索+推理 → 生成回答
```

### 与传统RAG的区别

| 维度 | 传统RAG | GraphRAG |
|------|---------|----------|
| **检索方式** | 向量相似度 | 图谱+向量 |
| **推理能力** | 单跳检索 | 多跳推理 |
| **关系理解** | 弱 | 强 |
| **全局视角** | 局部 | 全局 |
| **复杂查询** | 有限 | 强大 |

## 🚀 部署方案

### 方案1: 官方Docker部署（推荐）

```bash
# 1. 克隆项目
git clone https://github.com/microsoft/graphrag.git
cd graphrag

# 2. 创建配置文件
mkdir -p ./ragtest/input
echo "你的文档内容" > ./ragtest/input/book.txt

# 3. 初始化配置
python -m graphrag.index --init --root ./ragtest

# 4. 配置API密钥
# 编辑 ./ragtest/settings.yaml
```

**settings.yaml 配置示例：**
```yaml
llm:
  api_key: ${GRAPHRAG_API_KEY}
  type: openai_chat
  model: gpt-4
  model_supports_json: true

embeddings:
  api_key: ${GRAPHRAG_API_KEY}
  type: openai_embedding
  model: text-embedding-ada-002

input:
  type: file
  file_type: text
  base_dir: "input"
  file_encoding: utf-8
  file_pattern: ".*\\.txt$"

cache:
  type: file
  base_dir: "cache"

storage:
  type: file
  base_dir: "output"

chunk:
  size: 300
  overlap: 100

entity_extraction:
  prompt: "prompts/entity_extraction.txt"
  entity_types: [person,organization,location,event,concept]
  max_gleanings: 1

summarize_descriptions:
  prompt: "prompts/summarize_descriptions.txt"
  max_length: 500

claim_extraction:
  enabled: true
  prompt: "prompts/claim_extraction.txt"
  description: "Any claims or facts that could be relevant to information discovery."
  max_gleanings: 1

community_detection:
  max_cluster_size: 10

umap:
  enabled: true

snapshots:
  graphml: true
  raw_entities: true
  top_level_nodes: true
```

```bash
# 5. 构建知识图谱
python -m graphrag.index --root ./ragtest

# 6. 查询测试
python -m graphrag.query \
    --root ./ragtest \
    --method global \
    "What are the main themes in this document?"

python -m graphrag.query \
    --root ./ragtest \
    --method local \
    "Tell me about specific entities mentioned"
```

### 方案2: Python环境部署

```bash
# 1. 创建虚拟环境
python -m venv graphrag_env
source graphrag_env/bin/activate  # Linux/Mac
# graphrag_env\Scripts\activate  # Windows

# 2. 安装GraphRAG
pip install graphrag

# 3. 验证安装
python -c "import graphrag; print('GraphRAG installed successfully')"
```

### 方案3: 与你的太公心易系统集成

```python
# 集成到你的项目中
import asyncio
from graphrag.query.llm.oai.chat_openai import ChatOpenAI
from graphrag.query.llm.oai.embedding import OpenAIEmbedding
from graphrag.query.indexer_adapters import read_indexer_entities, read_indexer_reports
from graphrag.query.context_builder.entity_extraction import EntityVectorStoreKey
from graphrag.query.structured_search.global_search.community_context import GlobalCommunityContext
from graphrag.query.structured_search.global_search.search import GlobalSearch

class TaigongXinyiGraphRAG:
    """太公心易 + GraphRAG集成系统"""
    
    def __init__(self, data_dir: str, llm_params: dict, embedding_params: dict):
        self.data_dir = data_dir
        self.llm = ChatOpenAI(**llm_params)
        self.embedding = OpenAIEmbedding(**embedding_params)
        self.setup_search_engines()
    
    def setup_search_engines(self):
        """设置搜索引擎"""
        # 读取索引数据
        self.entities = read_indexer_entities(self.data_dir)
        self.reports = read_indexer_reports(self.data_dir)
        
        # 设置全局搜索
        self.global_context = GlobalCommunityContext(
            community_reports=self.reports,
            entities=self.entities,
            token_encoder=self.llm.get_token_encoder()
        )
        
        self.global_search = GlobalSearch(
            llm=self.llm,
            context_builder=self.global_context,
            token_encoder=self.llm.get_token_encoder(),
            max_tokens=12000
        )
    
    async def xinyi_enhanced_query(self, question: str, gua_context: dict = None):
        """结合易学智慧的增强查询"""
        
        # 1. GraphRAG全局搜索
        global_result = await self.global_search.asearch(question)
        
        # 2. 结合太公心易分析
        if gua_context:
            enhanced_prompt = f"""
            基于以下信息进行分析：
            
            问题: {question}
            
            GraphRAG分析结果:
            {global_result.response}
            
            太公心易卦象指导:
            卦名: {gua_context.get('name', '')}
            卦辞: {gua_context.get('gua_ci', '')}
            判词: {gua_context.get('judgment', '')}
            
            请结合现代数据分析和传统易学智慧，给出综合性的洞察和建议。
            """
            
            # 3. 生成最终回答
            final_response = await self.llm.agenerate([enhanced_prompt])
            
            return {
                "question": question,
                "graphrag_analysis": global_result.response,
                "xinyi_guidance": gua_context,
                "integrated_insight": final_response.generations[0][0].text,
                "confidence": global_result.context_data
            }
        
        return global_result

# 使用示例
async def main():
    # 配置参数
    llm_params = {
        "api_key": "your-openai-key",
        "model": "gpt-4",
        "temperature": 0.1
    }
    
    embedding_params = {
        "api_key": "your-openai-key", 
        "model": "text-embedding-ada-002"
    }
    
    # 初始化系统
    xinyi_graphrag = TaigongXinyiGraphRAG(
        data_dir="./ragtest/output",
        llm_params=llm_params,
        embedding_params=embedding_params
    )
    
    # 查询示例
    result = await xinyi_graphrag.xinyi_enhanced_query(
        "当前市场趋势如何？",
        gua_context={
            "name": "乾",
            "gua_ci": "飞龙在天，利见大人",
            "judgment": "天行健，君子以自强不息"
        }
    )
    
    print(result)

if __name__ == "__main__":
    asyncio.run(main())
```

## 🔧 与你现有系统的集成方案

### 集成到N8N工作流

```javascript
// N8N中的GraphRAG节点代码
const { GraphRAGQuery } = require('graphrag');

const items = $input.all();
const processedItems = [];

for (const item of items) {
    try {
        const data = item.json;
        
        // 使用GraphRAG进行增强分析
        const graphragResult = await GraphRAGQuery.globalSearch({
            query: data.title + " " + data.content,
            dataDir: "./graphrag_data",
            maxTokens: 8000
        });
        
        // 结合原有数据
        const enhancedDocument = {
            pageContent: data.title,
            metadata: {
                title: data.title,
                published_date: data.published_time,
                article_id: data.article_id,
                source: data.source,
                graphrag_analysis: graphragResult.response,
                entities: graphragResult.entities,
                relationships: graphragResult.relationships
            }
        };
        
        processedItems.push(enhancedDocument);
        
    } catch (error) {
        console.log(`GraphRAG处理错误: ${error.message}`);
        // 降级到原有处理方式
        processedItems.push({
            pageContent: item.json.title,
            metadata: {
                title: item.json.title,
                published_date: item.json.published_time,
                article_id: item.json.article_id,
                source: item.json.source
            }
        });
    }
}

return processedItems;
```

## 🎯 适合你项目的部署建议

### 阶段1: 试验部署
1. 使用Docker快速部署
2. 用你的RSS数据测试
3. 对比传统RAG效果

### 阶段2: 集成部署  
1. 集成到现有N8N工作流
2. 与Milvus并行运行
3. A/B测试效果

### 阶段3: 生产部署
1. 优化性能和成本
2. 与稷下学宫深度集成
3. 结合太公心易智慧

## 💡 成本和性能考虑

- **计算成本**: 比传统RAG高2-3倍
- **构建时间**: 初次构建较慢
- **查询速度**: 复杂查询较慢，但结果更准确
- **存储需求**: 需要额外存储知识图谱

GraphRAG特别适合你的太公心易系统，因为它能理解复杂的关系和进行深度推理，这与易学的系统性思维很匹配！

想要我帮你部署和集成吗？