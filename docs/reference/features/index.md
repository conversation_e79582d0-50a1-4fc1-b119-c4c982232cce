# ⚡ 核心功能

稷下学宫的核心功能体现了AI仙人军团的强大能力，每一个功能都是为了实现"超越传统分析师"的目标而精心设计。

## 🎯 功能概览

### 🔄 RSS悬丝诊脉系统
> *"千里之外的市场波动，我们都能感知"*

实时监控全球财经RSS源，智能评估事件影响力，精准触发AI仙人辩论。

**核心特性:**
- 24/7全球监控
- 智能影响力评分
- 自动触发机制
- 多源数据融合

[详细了解 →](rss-pulse.md)

---

### 🎭 AI仙人辩论引擎
> *"十一位仙人，十一种观点，一个共识"*

基于AutoGen的多智能体辩论系统，让AI仙人们进行真实的观点碰撞和智慧融合。

**核心特性:**
- 多智能体协作
- 个性化人格系统
- 历史记忆学习
- 共识达成机制

[详细了解 →](debate-engine.md)

---

### 🐘 Mastodon社交集成
> *"让AI仙人走向世界，影响真实的投资决策"*

将AI仙人的智慧通过去中心化社交网络传播，建立真实的影响力和粉丝群体。

**核心特性:**
- 去中心化发布
- 联邦网络传播
- 真实用户互动
- 影响力建设

[详细了解 →](mastodon-integration.md)

---

## 🔄 完整工作流程

```mermaid
graph TD
    A[RSS源监控] --> B[事件分析]
    B --> C{影响力评分}
    C -->|>6.0| D[触发稷下学宫]
    C -->|≤6.0| E[继续监控]
    D --> F[AI仙人辩论]
    F --> G[观点整合]
    G --> H[输出选择]
    H --> I[Console输出]
    H --> J[Streamlit展示]
    H --> K[Mastodon发布]
    K --> L[用户互动]
    L --> M[影响力扩散]
    M --> N[反馈学习]
    N --> F
```

## 🎯 三种输出模式

### 🖥️ Console模式 - 开发调试
```bash
太上老君: "从宏观角度看，这次事件..."
吕洞宾: "以剑仙之名发誓，价值投资..."
张果老: "量化模型显示..."
```

**适用场景:**
- 开发调试
- 功能测试
- 快速验证

### 🌐 Streamlit模式 - 网页展示
```python
st.chat_message("太上老君").write("宏观分析...")
st.chat_message("吕洞宾").write("价值投资观点...")
```

**适用场景:**
- 用户演示
- 实时观看
- 交互体验

### 🐘 Mastodon模式 - 社交传播
```python
await post_to_mastodon(summary, "taishang_laojun")
await post_individual_views(agent_views)
```

**适用场景:**
- 公开传播
- 影响力建设
- 用户互动

## 🧠 智能特性

### 📚 记忆系统
- **个人记忆**: 每个仙人的独立思考空间
- **共享记忆**: 公开观点和历史辩论
- **向量检索**: 基于Zilliz的语义搜索
- **持续学习**: 预测准确率追踪

### 🎯 预测验证
- **准确率跟踪**: 记录每个预测的结果
- **模型优化**: 基于历史表现调整策略
- **集体智慧**: 汇聚多个仙人的专业判断
- **反馈循环**: 持续改进预测能力

### 🔗 数据融合
- **多源整合**: RSS + 市场数据 + 社交情绪
- **实时处理**: 毫秒级的事件响应
- **智能过滤**: 自动筛选重要信息
- **上下文理解**: 基于历史数据的深度分析

## 🚀 技术优势

### ⚛️ 核武器级别的技术栈
- **AutoGen**: 最先进的多智能体框架
- **Zilliz**: 企业级向量数据库
- **PostgreSQL**: 可靠的关系数据存储
- **MCP协议**: 标准化的工具集成

### 🔄 三脑架构
```
PostgreSQL (关系数据) ↔ Zilliz (向量记忆) ↔ MongoDB (稀疏数据)
                              ↓
                        AutoGen AI仙人辩论
                              ↓
                        智能输出分发
```

### 🎭 人格化AI
- **独特人设**: 每个仙人都有鲜明个性
- **专业领域**: 术业有专攻的知识体系
- **社交能力**: 真实的互动和影响力
- **学习进化**: 持续成长的AI智能

## 📊 性能指标

### 🎯 响应速度
- **RSS检测**: <1秒
- **影响力评分**: <5秒
- **辩论启动**: <10秒
- **结果输出**: <30秒

### 🧠 智能水平
- **预测准确率**: >85%
- **观点一致性**: >90%
- **用户满意度**: >95%
- **影响力增长**: >200%/月

### 🔄 系统稳定性
- **可用性**: 99.9%
- **错误率**: <0.1%
- **响应时间**: <2秒
- **并发处理**: >1000用户

## 🎉 用户反馈

> *"稷下学宫的AI仙人比我见过的任何分析师都要专业和有趣！"*  
> — 某投资机构合伙人

> *"吕洞宾的价值投资建议帮我避免了很多亏损。"*  
> — 个人投资者

> *"张果老的量化模型准确率惊人，倒骑驴看市场确实有独特视角。"*  
> — 量化基金经理

> *"何仙姑的ESG投资理念让我重新思考了投资的意义。"*  
> — 可持续投资顾问

---

## 🚀 开始体验

想要体验稷下学宫的强大功能？

[快速开始](../getting-started/) { .md-button .md-button--primary }
[查看演示](../getting-started/quick-demo.md) { .md-button }

---

**🎭 稷下学宫 - 让AI仙人改变你的投资世界！**