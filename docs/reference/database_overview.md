# 炼妖壶数据库架构概览

## 📊 统一PostgreSQL数据库设计

为了节省成本和简化管理，所有功能都使用单一的Heroku PostgreSQL数据库。

### 🔗 数据库连接信息
- **环境变量**: `DATABASE_URL`
- **计费**: Heroku PostgreSQL Essential-0 (~$5/月)
- **优势**: 统一管理，避免多数据库费用

## 📋 表结构分类

### 1. RSS文章系统 (四字段设计)

#### `rss_articles` - RSS文章主表
```sql
- article_id (VARCHAR(100) PRIMARY KEY)  -- 文章唯一标识
- title (TEXT NOT NULL)                  -- 文章标题
- published_time (TIMESTAMP WITH TIME ZONE) -- 发布时间
- processed (BOOLEAN DEFAULT false)      -- 是否已处理
```

#### `rss_article_vectors` - 向量存储表
```sql
- article_id (VARCHAR(100) PRIMARY KEY)  -- 关联主表
- title (TEXT NOT NULL)                  -- 文章标题
- published_time (TIMESTAMP WITH TIME ZONE) -- 发布时间
- processed (BOOLEAN DEFAULT false)      -- 是否已处理
- embedding (vector(1024))               -- BGE-M3向量
```

#### `rss_sources` - RSS源管理
- RSS源配置和管理

### 2. LiteLLM统一入口系统

#### `api_keys` - API密钥管理
```sql
- id, key_name, api_key, provider
- model_family, rate_limit_per_minute, rate_limit_per_day
- is_active, created_at, updated_at
```

#### `model_configs` - 模型配置
```sql
- id, model_name, provider, api_key_id
- max_tokens, temperature, cost_per_1k_input, cost_per_1k_output
- is_active, created_at
```

#### `api_calls` - API调用日志
```sql
- id, model_name, provider
- input_tokens, output_tokens, total_tokens, cost
- response_time_ms, status, error_message
- request_metadata, created_at
```

#### `usage_stats` - 使用统计
```sql
- id, date, model_name, provider
- total_calls, total_input_tokens, total_output_tokens
- total_cost, avg_response_time_ms, error_count
```

#### `load_balancer_configs` - 负载均衡配置
```sql
- id, config_name, models (JSONB)
- strategy, fallback_model, is_active
```

### 3. 会员系统

#### `members` - 会员信息
- 用户账户和权限管理

#### `user_profiles` - 用户配置
- 个人设置和偏好

#### `report_access` - 报告访问记录
- 访问日志和权限控制

### 4. 七姐妹股票系统

#### `seven_sisters_fundamentals` - 基本面数据
- 股票价格和财务指标

#### `seven_sisters_info` - 股票信息
- 公司基本信息

### 5. 社交媒体和分析

#### `social_media_posts` - 社交媒体数据
- Mastodon、Twitter等平台数据

#### `analysis_cache` - 分析缓存
- 分析结果缓存

#### `analysis_rules` - 分析规则
- 自定义分析规则

#### `decision_logs` - 决策日志
- 系统决策记录

#### `daily_reports` - 日报
- 每日分析报告

## 🎯 设计优势

### 1. 成本效益
- **单数据库**: 避免多数据库费用
- **Essential-0计划**: 每月仅$5
- **统一管理**: 减少运维复杂度

### 2. 四字段RSS设计
- **简洁性**: 只保留核心字段
- **性能**: 更快的查询速度
- **扩展性**: 详细信息存储在向量表

### 3. LiteLLM统一入口
- **API管理**: 统一管理多个API密钥
- **负载均衡**: 智能分配请求
- **成本控制**: 实时监控API使用

### 4. 向量存储
- **pgvector扩展**: 原生向量支持
- **BGE-M3模型**: 1024维中文向量
- **语义搜索**: 高效的相似度查询

## 🔧 N8N集成

### RSS数据流
1. **RSS采集** → **数据清理** → **PostgreSQL插入**
2. **四字段映射**: article_id, title, published_time, processed
3. **向量化处理**: 后续通过N8N触发向量生成

### LiteLLM集成
1. **API调用记录**: 自动记录所有API使用
2. **成本统计**: 实时计算使用成本
3. **负载均衡**: 根据配置分配请求

## 📈 监控和维护

### 性能监控
- 查询性能优化
- 索引使用情况
- 存储空间监控

### 成本控制
- API使用量监控
- 数据库存储优化
- 定期数据清理

### 备份策略
- Heroku自动备份
- 关键数据导出
- 灾难恢复计划
