# 🔄 太公心易有限状态机架构

> *"飞熊入梦山河图，周公拉车八百步。六壬察心炼妖壶，遁甲择时降魔杵。"*

## 🎯 系统概述

太公心易系统是一个融合道家哲学与现代AI技术的有限状态机，通过神话隐喻来构建可解释的AI决策系统。

## 🏛️ 核心理念

### 聚仙楼架构映射
```
聚仙楼即稷下学宫，八仙论道 (Autogen驱动)
    ↓
太上老君负责辩论的整理和汇报 (元神出窍，脱离Streamlit，Webhook调用N8n，即兜率宫)
    ↓
灵宝道君撒豆成兵，用OpenManus到SEC filing等API去核实
    ↓
元始天尊根据呈上的文件，确定标的的多空，板块的十二长生，产业的24节气，乃至于国运的元会运世
```

## 🔄 FSM状态设计

### 当前状态流
```
Collecting → Divergence → Refine → ExternalFetch → Report → Actuate
```

### 状态详细分析

#### 1. Collecting（聚仙楼 - 白虎观会议）
**功能**: 多智能体信息收集  
**技术映射**: AutoGen 多 Agent 协作  
**优势**: 
- 多视角信息汇聚
- 避免单一视角盲点
- 模拟真实的学术讨论环境

#### 2. Divergence（八仙过海 - 各显神通）
**功能**: 观点分化与辩论  
**技术映射**: 多Agent并行推理  
**特点**:
- 鼓励观点多样性
- 防止群体思维
- 激发创新洞察

#### 3. Refine（太上老君 - 炼丹整理）
**功能**: 信息整理与祛魅  
**技术映射**: 信息融合与去噪  
**核心**:
- 去除幻觉信息
- 提取核心观点
- 形成结构化报告

#### 4. ExternalFetch（灵宝道君 - 田野调查）
**功能**: 外部验证与核实  
**技术映射**: OpenManus + Web验证  
**方法**:
- 多源数据交叉验证
- 实时信息获取
- 事实核查

#### 5. Report（信息汇总）
**功能**: 综合分析报告  
**输出**: 结构化决策建议

#### 6. Actuate（元始天尊 - 最终决策）
**功能**: 一槌定音的最终决策  
**要求**: 必须给出明确的多空判断

## 🧠 核心算法

### 有限状态机实现
```python
class TaigongXinyiFSM:
    def __init__(self):
        self.states = {
            'collecting': self.collecting_state,
            'divergence': self.divergence_state,
            'refine': self.refine_state,
            'external_fetch': self.external_fetch_state,
            'report': self.report_state,
            'actuate': self.actuate_state
        }
        self.current_state = 'collecting'
    
    def transition(self, trigger):
        """状态转移逻辑"""
        transitions = {
            ('collecting', 'info_gathered'): 'divergence',
            ('divergence', 'debate_complete'): 'refine',
            ('refine', 'summary_ready'): 'external_fetch',
            ('external_fetch', 'verification_done'): 'report',
            ('report', 'analysis_complete'): 'actuate'
        }
        
        next_state = transitions.get((self.current_state, trigger))
        if next_state:
            self.current_state = next_state
            return self.states[next_state]()
```

## 🎭 八仙角色映射

### 先天八卦布局
```
        乾☰ 吕洞宾 (剑仙投资顾问)
    兑☱ 钟汉离         巽☴ 蓝采和 (情绪分析师)
震☳ 铁拐李                 坤☷ 何仙姑 (风控专家)  
    艮☶ 曹国舅         坎☵ 张果老 (技术分析)
        离☲ 韩湘子 (基本面研究)
```

## 🔮 三清验证体系

### 太清道德天尊（逻辑分析）
- 逻辑一致性检查
- 论证结构分析
- 推理链条验证

### 上清灵宝天尊（田野调查）
- OpenManus API验证
- 实时数据获取
- 多源信息交叉验证

### 玉清元始天尊（最终决策）
- 综合所有信息
- 给出明确判断
- 不允许模糊表态

## 🌟 系统优势

1. **可解释性**: 通过神话隐喻让AI决策过程可理解
2. **多样性**: 八仙角色确保观点多元化
3. **验证性**: 三清体系提供多层验证
4. **决策性**: 强制输出明确的投资建议

## 🚀 技术实现

### 核心组件
- **AutoGen**: 多智能体辩论框架
- **N8N**: 工作流自动化
- **OpenManus**: 外部验证API
- **Streamlit**: 用户界面

### 数据流
```
RSS Feed → 八仙辩论 → 太上老君整理 → 灵宝道君验证 → 元始天尊决策
```

---

*"兼听则明，则需要召集白虎观会议。然而七嘴八舌就需要整理，乃至祛魅（幻觉）。要整理则一定丢失信息，要报告则需要派忠实的执行者去微服私访，不用来源相同的api。我认为，这本质就是一套有限状态机。"*