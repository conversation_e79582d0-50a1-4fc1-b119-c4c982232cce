# 太公心易三脑架构设计

## 🧠 三脑架构概念

太公心易采用创新的"三脑架构"设计，将不同类型的数据存储和处理分配给最适合的数据库系统，实现高效协作。

---

## 🎯 Zilliz - 神经脑

### 核心职责
负责语义理解、相似度计算和智能检索

```mermaid
graph TB
    subgraph "🎯 Zilliz Cloud - 神经脑"
        subgraph "📊 向量存储"
            VectorStore[向量数据库<br/>384维优化向量<br/>100万+ 文档]
            IndexType[索引类型<br/>HNSW + IVF<br/>毫秒级检索]
        end
        
        subgraph "🔍 语义检索"
            SemanticSearch[语义搜索<br/>Cosine相似度<br/>Top-K检索]
            MultiFilter[多维过滤<br/>时间 + 情感 + 主题<br/>复合查询条件]
        end
        
        subgraph "⚡ 实时更新"
            RealTimeInsert[实时插入<br/>1000+向量/小时<br/>秒级生效]
            HotCache[热数据缓存<br/>常用查询<br/>超快响应]
        end
    end

    VectorStore --> SemanticSearch
    IndexType --> SemanticSearch
    SemanticSearch --> MultiFilter
    RealTimeInsert --> VectorStore
    HotCache --> SemanticSearch

    classDef zillizStyle fill:#ff6b6b,stroke:#d63031,stroke-width:3px,color:#fff
    class VectorStore,IndexType,SemanticSearch,MultiFilter,RealTimeInsert,HotCache zillizStyle
```

### 技术特点
- **向量维度**: 384维，平衡性能与精度
- **检索速度**: <200ms P95响应时间
- **更新频率**: 实时向量化，1000+/小时
- **查询能力**: 支持复合条件过滤

---

## 🗂️ MongoDB - 情报脑

### 核心职责
存储原始文档、元数据和结构化情报

```mermaid
graph TB
    subgraph "🗂️ MongoDB Atlas - 情报脑"
        subgraph "📄 文档存储"
            RawDocs[原始文档<br/>RSS全文内容<br/>新闻原始数据]
            FullText[全文索引<br/>中英文混合<br/>关键词搜索]
        end
        
        subgraph "📋 元数据管理"
            Metadata[元数据存储<br/>来源 + 时间 + 标签<br/>分类信息]
            Categories[分类体系<br/>财经 + 科技 + 政策<br/>智能分类]
        end
        
        subgraph "🔄 数据同步"
            RealtimeSync[实时同步<br/>RSS采集器<br/>API数据源]
            DataValidation[数据验证<br/>格式检查<br/>去重处理]
        end
    end

    RawDocs --> FullText
    Metadata --> Categories
    RealtimeSync --> RawDocs
    RealtimeSync --> Metadata
    DataValidation --> RealtimeSync

    classDef mongoStyle fill:#74b9ff,stroke:#0984e3,stroke-width:3px,color:#fff
    class RawDocs,FullText,Metadata,Categories,RealtimeSync,DataValidation mongoStyle
```

### 技术特点
- **文档类型**: JSON格式，灵活schema
- **索引策略**: 全文索引 + 复合索引
- **存储容量**: 支持TB级数据存储
- **查询性能**: 秒级复杂查询响应

---

## ⚖️ PostgreSQL - 秩序脑

### 核心职责
管理业务规则、用户数据和决策审计

```mermaid
graph TB
    subgraph "⚖️ PostgreSQL - 秩序脑"
        subgraph "📏 规则引擎"
            BusinessRules[业务规则<br/>投资策略<br/>风险控制]
            AnalysisLogic[分析逻辑<br/>太公心易算法<br/>决策树]
        end
        
        subgraph "👤 用户管理"
            UserProfiles[用户画像<br/>投资偏好<br/>风险承受度]
            Permissions[权限控制<br/>角色管理<br/>访问控制]
        end
        
        subgraph "📝 审计追踪"
            DecisionLogs[决策日志<br/>分析记录<br/>操作轨迹]
            AuditTrail[审计追踪<br/>合规要求<br/>历史记录]
        end
    end

    BusinessRules --> AnalysisLogic
    UserProfiles --> Permissions
    DecisionLogs --> AuditTrail
    AnalysisLogic --> DecisionLogs
    Permissions --> DecisionLogs

    classDef pgStyle fill:#55a3ff,stroke:#2d3436,stroke-width:3px,color:#fff
    class BusinessRules,AnalysisLogic,UserProfiles,Permissions,DecisionLogs,AuditTrail pgStyle
```

### 技术特点
- **ACID特性**: 强一致性保证
- **关系模型**: 复杂关联查询
- **事务支持**: 多表操作原子性
- **扩展能力**: 支持JSON + 向量扩展

---

## 🔄 三脑协作流程

### 智能查询流程

```mermaid
flowchart TD
    UserQuery[用户查询<br/>比特币走势分析] --> QueryParsing[查询解析<br/>意图识别]
    
    QueryParsing --> ZillizSearch[Zilliz语义检索<br/>相关文档向量匹配]
    QueryParsing --> MongoSearch[MongoDB文档检索<br/>关键词全文搜索]
    QueryParsing --> PGRules[PostgreSQL规则匹配<br/>用户偏好 + 业务规则]
    
    ZillizSearch --> ResultMerge[结果融合<br/>多源数据整合]
    MongoSearch --> ResultMerge
    PGRules --> ResultMerge
    
    ResultMerge --> TaigongAnalysis[太公心易分析<br/>太乙观澜 + 遁甲择时]
    TaigongAnalysis --> FinalResult[最终结果<br/>投资建议 + 风险评估]
    
    FinalResult --> PGLog[PostgreSQL记录<br/>决策日志 + 审计]

    classDef userStyle fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef zillizStyle fill:#ff6b6b,stroke:#d63031,stroke-width:2px,color:#fff
    classDef mongoStyle fill:#74b9ff,stroke:#0984e3,stroke-width:2px,color:#fff
    classDef pgStyle fill:#55a3ff,stroke:#2d3436,stroke-width:2px,color:#fff
    classDef processStyle fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    
    class UserQuery,FinalResult userStyle
    class ZillizSearch zillizStyle
    class MongoSearch mongoStyle
    class PGRules,PGLog pgStyle
    class QueryParsing,ResultMerge,TaigongAnalysis processStyle
```

---

## 💡 架构优势

### 🚀 性能优化
- **专业分工**: 每个数据库做最擅长的事
- **并行处理**: 三脑同时工作，提升效率
- **缓存策略**: 多层缓存，减少重复计算

### 🔒 数据安全
- **分层存储**: 敏感数据隔离保护
- **备份策略**: 多数据库独立备份
- **故障隔离**: 单点故障不影响整体

### 📈 可扩展性
- **水平扩展**: 各数据库独立扩容
- **垂直优化**: 针对性能瓶颈优化
- **弹性伸缩**: 根据负载动态调整

---

## 🔗 相关文档

- **上一步**: [系统概览架构](01-system-overview.md)
- **下一步**: [数据流架构](03-data-flow-architecture.md)
- **实现细节**: [数据库设计](database-design.md)
