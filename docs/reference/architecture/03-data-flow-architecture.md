# 太公心易数据流架构

## 🔄 数据流概览

太公心易采用实时数据处理架构，从多源数据采集到智能分析输出的完整链路。

---

## 📡 数据源层

### 多源数据采集

```mermaid
graph TB
    subgraph "📡 数据源层"
        subgraph "📰 新闻RSS源"
            RSS1[财经新闻<br/>新浪财经 + 网易财经<br/>实时更新]
            RSS2[国际资讯<br/>Reuters + Bloomberg<br/>权威来源]
            RSS3[加密货币<br/>CoinDesk + CoinTelegraph<br/>专业媒体]
        end
        
        subgraph "📊 金融API"
            API1[股票数据<br/>Yahoo Finance<br/>实时行情]
            API2[加密货币<br/>CoinGecko + CoinCap<br/>价格数据]
            API3[宏观数据<br/>Alpha Vantage<br/>经济指标]
        end
        
        subgraph "🌐 社交媒体"
            Social1[Twitter API<br/>市场情绪<br/>KOL观点]
            Social2[Reddit API<br/>散户讨论<br/>热门话题]
        end
    end

    RSS1 --> DataCollection[数据采集层]
    RSS2 --> DataCollection
    RSS3 --> DataCollection
    API1 --> DataCollection
    API2 --> DataCollection
    API3 --> DataCollection
    Social1 --> DataCollection
    Social2 --> DataCollection

    classDef rssStyle fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    classDef apiStyle fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    classDef socialStyle fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    classDef collectionStyle fill:#fff3e0,stroke:#f57c00,stroke-width:3px
    
    class RSS1,RSS2,RSS3 rssStyle
    class API1,API2,API3 apiStyle
    class Social1,Social2 socialStyle
    class DataCollection collectionStyle
```

### 数据特点
- **更新频率**: RSS每15分钟，API每分钟
- **数据量**: 日均10,000+新文档
- **语言支持**: 中英文混合处理
- **格式多样**: JSON、XML、HTML等

---

## 🔄 数据处理管道

### ETL处理流程

```mermaid
flowchart TD
    subgraph "🔄 数据采集层"
        Scheduler[⏰ N8N定时调度<br/>Cron表达式<br/>智能频率调整]
        RSSParser[📖 RSS解析器<br/>Feedparser<br/>多格式支持]
        APIClient[🔌 API客户端<br/>HTTP连接池<br/>重试机制]
    end

    subgraph "🧹 数据清洗层"
        Validator[✅ 数据验证<br/>Schema校验<br/>格式标准化]
        Deduplicator[🔍 智能去重<br/>内容哈希<br/>相似度检测]
        TextCleaner[📝 文本清洗<br/>HTML标签清理<br/>特殊字符处理]
    end

    subgtml "🔬 数据分析层"
        LanguageDetect[🌍 语言检测<br/>langdetect<br/>中英文识别]
        SentimentAnalysis[😊 情感分析<br/>SnowNLP + BERT<br/>情绪量化]
        KeywordExtract[🏷️ 关键词提取<br/>jieba + TF-IDF<br/>主题识别]
        CategoryClassify[📂 智能分类<br/>机器学习模型<br/>多标签分类]
    end

    Scheduler --> RSSParser
    Scheduler --> APIClient
    RSSParser --> Validator
    APIClient --> Validator
    
    Validator --> Deduplicator
    Deduplicator --> TextCleaner
    
    TextCleaner --> LanguageDetect
    LanguageDetect --> SentimentAnalysis
    LanguageDetect --> KeywordExtract
    LanguageDetect --> CategoryClassify

    classDef scheduleStyle fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    classDef cleanStyle fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    classDef analysisStyle fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    
    class Scheduler,RSSParser,APIClient scheduleStyle
    class Validator,Deduplicator,TextCleaner cleanStyle
    class LanguageDetect,SentimentAnalysis,KeywordExtract,CategoryClassify analysisStyle
```

---

## 🧠 向量化处理

### 文本向量化流程

```mermaid
graph TB
    subgraph "🧠 向量化处理层"
        subgraph "📊 文本编码"
            TextPreprocess[文本预处理<br/>分词 + 标准化<br/>停用词过滤]
            SentenceBERT[Sentence-BERT<br/>384维向量<br/>中英文支持]
        end
        
        subgraph "📉 向量优化"
            DimReduction[降维处理<br/>PCA/UMAP<br/>可选优化]
            VectorNorm[向量标准化<br/>L2 Normalization<br/>相似度优化]
        end
        
        subgraph "💾 向量存储"
            VectorValidation[向量验证<br/>维度检查<br/>质量评估]
            ZillizInsert[Zilliz插入<br/>批量写入<br/>索引更新]
        end
    end

    TextPreprocess --> SentenceBERT
    SentenceBERT --> DimReduction
    DimReduction --> VectorNorm
    VectorNorm --> VectorValidation
    VectorValidation --> ZillizInsert

    classDef encodeStyle fill:#ff6b6b,stroke:#d63031,stroke-width:2px,color:#fff
    classDef optimizeStyle fill:#74b9ff,stroke:#0984e3,stroke-width:2px,color:#fff
    classDef storeStyle fill:#55a3ff,stroke:#2d3436,stroke-width:2px,color:#fff
    
    class TextPreprocess,SentenceBERT encodeStyle
    class DimReduction,VectorNorm optimizeStyle
    class VectorValidation,ZillizInsert storeStyle
```

### 向量化特点
- **模型选择**: Sentence-BERT中文优化版
- **向量维度**: 384维，平衡性能与精度
- **批处理**: 批量向量化，提升效率
- **质量控制**: 向量质量评估和过滤

---

## 🗄️ 数据存储分发

### 三脑存储策略

```mermaid
flowchart LR
    subgraph "📥 处理完成数据"
        ProcessedData[已处理数据<br/>文本 + 向量 + 元数据<br/>情感 + 关键词 + 分类]
    end

    ProcessedData --> ZillizBrain[🎯 Zilliz神经脑<br/>向量存储<br/>语义检索]
    ProcessedData --> MongoBrain[🗂️ MongoDB情报脑<br/>文档存储<br/>元数据管理]
    ProcessedData --> PostgresBrain[⚖️ PostgreSQL秩序脑<br/>处理日志<br/>统计信息]

    subgraph "🎯 Zilliz存储"
        VectorData[向量数据<br/>384维embedding<br/>相似度索引]
        VectorMeta[向量元数据<br/>文档ID<br/>时间戳]
    end

    subgraph "🗂️ MongoDB存储"
        OriginalDoc[原始文档<br/>完整内容<br/>HTML/文本]
        Metadata[元数据<br/>来源+时间+标签<br/>情感+关键词]
    end

    subgraph "⚖️ PostgreSQL存储"
        ProcessLog[处理日志<br/>ETL记录<br/>错误追踪]
        Statistics[统计信息<br/>数据质量<br/>性能指标]
    end

    ZillizBrain --> VectorData
    ZillizBrain --> VectorMeta
    MongoBrain --> OriginalDoc
    MongoBrain --> Metadata
    PostgresBrain --> ProcessLog
    PostgresBrain --> Statistics

    classDef dataStyle fill:#fff3e0,stroke:#f57c00,stroke-width:3px
    classDef zillizStyle fill:#ff6b6b,stroke:#d63031,stroke-width:2px,color:#fff
    classDef mongoStyle fill:#74b9ff,stroke:#0984e3,stroke-width:2px,color:#fff
    classDef pgStyle fill:#55a3ff,stroke:#2d3436,stroke-width:2px,color:#fff
    
    class ProcessedData dataStyle
    class ZillizBrain,VectorData,VectorMeta zillizStyle
    class MongoBrain,OriginalDoc,Metadata mongoStyle
    class PostgresBrain,ProcessLog,Statistics pgStyle
```

---

## ⚡ 实时处理特性

### 性能指标
| 处理阶段 | 处理时间 | 吞吐量 | 并发度 |
|----------|----------|--------|--------|
| RSS解析 | <1秒 | 1000文档/分钟 | 10并发 |
| 文本清洗 | <0.5秒 | 2000文档/分钟 | 20并发 |
| 向量化 | <2秒 | 500文档/分钟 | 5并发 |
| 存储写入 | <0.2秒 | 5000文档/分钟 | 50并发 |

### 容错机制
- **重试策略**: 指数退避重试
- **错误隔离**: 单个数据源故障不影响整体
- **数据恢复**: 基于日志的数据重放
- **监控告警**: 实时处理状态监控

---

## 🔗 相关文档

- **上一步**: [三脑架构设计](02-three-brain-architecture.md)
- **下一步**: [服务层架构](04-service-layer-architecture.md)
- **实现细节**: [算法实现](algorithm-implementation.md)
