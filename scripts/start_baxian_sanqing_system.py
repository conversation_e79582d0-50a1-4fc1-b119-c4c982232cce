#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
八仙论道+三清验证系统启动脚本
"""

import asyncio
import logging
import os
import sys
import yaml
from pathlib import Path
from typing import Dict, Any
import argparse

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.integration.autogen_sanqing_bridge import AutoGenSanqingBridge
from src.core.sanqing_verification_orchestrator import VerificationConfig

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('logs/baxian_sanqing.log')
    ]
)

logger = logging.getLogger("BaxianSanqingSystem")

class BaxianSanqingSystemManager:
    """八仙论道+三清验证系统管理器"""
    
    def __init__(self, config_path: str = "config/baxian_sanqing_config.yaml"):
        self.config_path = config_path
        self.config = None
        self.bridge = None
        self.is_running = False
        
    def load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            
            # 替换环境变量
            config = self._substitute_env_vars(config)
            
            # 验证必需的环境变量
            self._validate_required_env_vars(config)
            
            self.config = config
            logger.info("✅ 配置文件加载成功")
            return config
            
        except FileNotFoundError:
            logger.error(f"❌ 配置文件未找到: {self.config_path}")
            raise
        except yaml.YAMLError as e:
            logger.error(f"❌ 配置文件格式错误: {e}")
            raise
        except Exception as e:
            logger.error(f"❌ 加载配置失败: {e}")
            raise
    
    def _substitute_env_vars(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """替换配置中的环境变量"""
        def substitute_recursive(obj):
            if isinstance(obj, dict):
                return {k: substitute_recursive(v) for k, v in obj.items()}
            elif isinstance(obj, list):
                return [substitute_recursive(item) for item in obj]
            elif isinstance(obj, str) and obj.startswith("${") and obj.endswith("}"):
                env_var = obj[2:-1]
                return os.getenv(env_var, obj)
            else:
                return obj
        
        return substitute_recursive(config)
    
    def _validate_required_env_vars(self, config: Dict[str, Any]):
        """验证必需的环境变量"""
        required_vars = config.get("environment_variables", {}).get("required", [])
        missing_vars = []
        
        for var in required_vars:
            if not os.getenv(var):
                missing_vars.append(var)
        
        if missing_vars:
            logger.error(f"❌ 缺少必需的环境变量: {missing_vars}")
            raise ValueError(f"Missing required environment variables: {missing_vars}")
    
    def initialize_system(self):
        """初始化系统"""
        logger.info("🚀 初始化八仙论道+三清验证系统...")
        
        if not self.config:
            self.load_config()
        
        # 创建验证配置
        verification_config = VerificationConfig(
            openmanus_url=self.config["openmanus"]["base_url"],
            openmanus_api_key=self.config["openmanus"]["api_key"],
            zilliz_config=self.config["zilliz"],
            confidence_threshold=self.config["verification"]["confidence_threshold"],
            max_verification_tasks=self.config["verification"]["max_verification_tasks"],
            verification_timeout=self.config["verification"]["verification_timeout"]
        )
        
        # 创建桥接器
        self.bridge = AutoGenSanqingBridge(verification_config)
        
        # 设置回调
        self._setup_callbacks()
        
        logger.info("✅ 系统初始化完成")
    
    def _setup_callbacks(self):
        """设置系统回调"""
        async def verification_complete_callback(report):
            decision = report["executive_summary"]["final_decision"]
            confidence = report["executive_summary"]["verification_confidence"]
            
            logger.info(f"🎉 验证完成 - 决策: {decision}, 置信度: {confidence:.2f}")
            
            # 可以在这里添加更多的后处理逻辑
            # 比如发送通知、保存结果到数据库等
            await self._save_report(report)
            await self._send_notification(report)
        
        self.bridge.add_verification_callback(verification_complete_callback)
    
    async def _save_report(self, report: Dict[str, Any]):
        """保存报告"""
        try:
            report_id = report["report_id"]
            timestamp = report["metadata"]["generation_timestamp"]
            
            # 保存到文件
            report_dir = Path("reports")
            report_dir.mkdir(exist_ok=True)
            
            report_file = report_dir / f"{report_id}.json"
            with open(report_file, 'w', encoding='utf-8') as f:
                import json
                json.dump(report, f, indent=2, ensure_ascii=False)
            
            logger.info(f"📄 报告已保存: {report_file}")
            
        except Exception as e:
            logger.error(f"❌ 保存报告失败: {e}")
    
    async def _send_notification(self, report: Dict[str, Any]):
        """发送通知"""
        try:
            # 这里可以集成各种通知方式
            # 比如邮件、Slack、微信等
            decision = report["executive_summary"]["final_decision"]
            topic = report["executive_summary"]["topic"]
            
            notification_message = f"🌟 八仙论道完成\n主题: {topic}\n决策: {decision}"
            
            # 模拟发送通知
            logger.info(f"📢 通知: {notification_message}")
            
        except Exception as e:
            logger.error(f"❌ 发送通知失败: {e}")
    
    async def run_debate_and_verification(self, 
                                        topic: str, 
                                        context: Dict[str, Any]) -> Dict[str, Any]:
        """运行辩论和验证"""
        if not self.bridge:
            raise RuntimeError("系统未初始化，请先调用 initialize_system()")
        
        logger.info(f"🎭 开始论道主题: {topic}")
        
        try:
            # 执行完整的论道+验证周期
            result = await self.bridge.conduct_full_verification_cycle(topic, context)
            
            logger.info("✅ 论道和验证完成")
            return result
            
        except Exception as e:
            logger.error(f"❌ 论道和验证失败: {e}")
            raise
    
    async def start_interactive_mode(self):
        """启动交互模式"""
        logger.info("🎮 进入交互模式...")
        
        print("=" * 60)
        print("🌟 八仙论道+三清验证系统")
        print("=" * 60)
        
        while True:
            try:
                print("\n请选择操作:")
                print("1. 开始新的论道")
                print("2. 查看历史记录")
                print("3. 系统状态")
                print("4. 退出")
                
                choice = input("\n请输入选择 (1-4): ").strip()
                
                if choice == "1":
                    await self._interactive_new_debate()
                elif choice == "2":
                    self._show_history()
                elif choice == "3":
                    self._show_system_status()
                elif choice == "4":
                    print("👋 再见！")
                    break
                else:
                    print("❌ 无效选择，请重新输入")
                    
            except KeyboardInterrupt:
                print("\n👋 用户中断，退出系统")
                break
            except Exception as e:
                logger.error(f"交互模式错误: {e}")
                print(f"❌ 发生错误: {e}")
    
    async def _interactive_new_debate(self):
        """交互式新建论道"""
        print("\n🎭 创建新的八仙论道")
        
        topic = input("请输入论道主题: ").strip()
        if not topic:
            print("❌ 主题不能为空")
            return
        
        print("请输入背景信息 (可选，直接回车跳过):")
        context_lines = []
        while True:
            line = input().strip()
            if not line:
                break
            context_lines.append(line)
        
        context = {
            "background": "\n".join(context_lines) if context_lines else "无特殊背景",
            "timestamp": "现在",
            "user_input": True
        }
        
        print(f"\n🚀 开始论道: {topic}")
        print("⏳ 请稍候，八仙正在论道中...")
        
        try:
            result = await self.run_debate_and_verification(topic, context)
            
            print("\n✅ 论道完成！")
            print(f"📊 最终决策: {result['executive_summary']['final_decision']}")
            print(f"🎯 置信度: {result['executive_summary']['verification_confidence']:.2f}")
            print(f"💡 建议: {result['executive_summary']['recommendation']}")
            
        except Exception as e:
            print(f"❌ 论道失败: {e}")
    
    def _show_history(self):
        """显示历史记录"""
        if not self.bridge or not self.bridge.debate_history:
            print("📝 暂无历史记录")
            return
        
        print("\n📚 历史论道记录:")
        for i, debate in enumerate(self.bridge.debate_history[-5:], 1):  # 显示最近5条
            print(f"{i}. {debate.topic} (置信度: {debate.confidence_score:.2f})")
    
    def _show_system_status(self):
        """显示系统状态"""
        print("\n🔍 系统状态:")
        print(f"📁 配置文件: {self.config_path}")
        print(f"🔧 系统初始化: {'✅' if self.bridge else '❌'}")
        print(f"📊 历史记录数: {len(self.bridge.debate_history) if self.bridge else 0}")
        
        # 检查环境变量
        required_vars = self.config.get("environment_variables", {}).get("required", [])
        print(f"🌍 环境变量检查:")
        for var in required_vars:
            status = "✅" if os.getenv(var) else "❌"
            print(f"  {var}: {status}")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="八仙论道+三清验证系统")
    parser.add_argument("--config", default="config/baxian_sanqing_config.yaml", 
                       help="配置文件路径")
    parser.add_argument("--topic", help="论道主题")
    parser.add_argument("--context", help="背景信息 (JSON格式)")
    parser.add_argument("--interactive", action="store_true", help="交互模式")
    
    args = parser.parse_args()
    
    # 创建系统管理器
    system = BaxianSanqingSystemManager(args.config)
    
    async def run_system():
        try:
            # 初始化系统
            system.initialize_system()
            
            if args.interactive:
                # 交互模式
                await system.start_interactive_mode()
            elif args.topic:
                # 命令行模式
                import json
                context = json.loads(args.context) if args.context else {}
                result = await system.run_debate_and_verification(args.topic, context)
                print(json.dumps(result, indent=2, ensure_ascii=False))
            else:
                # 默认进入交互模式
                await system.start_interactive_mode()
                
        except Exception as e:
            logger.error(f"系统运行失败: {e}")
            sys.exit(1)
    
    # 运行系统
    asyncio.run(run_system())

if __name__ == "__main__":
    main()