-- LiteLLM统一入口表初始化脚本
-- 在现有PostgreSQL数据库中添加LiteLLM相关表
-- 复用现有数据库，避免额外费用

-- 1. 创建API密钥管理表
CREATE TABLE IF NOT EXISTS api_keys (
    id SERIAL PRIMARY KEY,
    key_name VARCHAR(100) UNIQUE NOT NULL,
    api_key TEXT NOT NULL,
    provider VARCHAR(50) NOT NULL,        -- openai, anthropic, openrouter, etc.
    model_family VARCHAR(50),             -- gpt, claude, llama, etc.
    rate_limit_per_minute INTEGER DEFAULT 60,
    rate_limit_per_day INTEGER DEFAULT 1000,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 2. 创建模型配置表
CREATE TABLE IF NOT EXISTS model_configs (
    id SERIAL PRIMARY KEY,
    model_name VARCHAR(100) UNIQUE NOT NULL,
    provider VARCHAR(50) NOT NULL,
    api_key_id INTEGER REFERENCES api_keys(id),
    max_tokens INTEGER DEFAULT 4096,
    temperature REAL DEFAULT 0.7,
    cost_per_1k_input DECIMAL(10,6) DEFAULT 0,
    cost_per_1k_output DECIMAL(10,6) DEFAULT 0,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 3. 创建API调用日志表
CREATE TABLE IF NOT EXISTS api_calls (
    id SERIAL PRIMARY KEY,
    model_name VARCHAR(100) NOT NULL,
    provider VARCHAR(50) NOT NULL,
    input_tokens INTEGER DEFAULT 0,
    output_tokens INTEGER DEFAULT 0,
    total_tokens INTEGER DEFAULT 0,
    cost DECIMAL(10,6) DEFAULT 0,
    response_time_ms INTEGER,
    status VARCHAR(20) DEFAULT 'success',  -- success, error, timeout
    error_message TEXT,
    request_metadata JSONB,               -- 请求的元数据
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 4. 创建使用统计表
CREATE TABLE IF NOT EXISTS usage_stats (
    id SERIAL PRIMARY KEY,
    date DATE NOT NULL,
    model_name VARCHAR(100) NOT NULL,
    provider VARCHAR(50) NOT NULL,
    total_calls INTEGER DEFAULT 0,
    total_input_tokens BIGINT DEFAULT 0,
    total_output_tokens BIGINT DEFAULT 0,
    total_cost DECIMAL(10,4) DEFAULT 0,
    avg_response_time_ms INTEGER DEFAULT 0,
    error_count INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(date, model_name, provider)
);

-- 5. 创建负载均衡配置表
CREATE TABLE IF NOT EXISTS load_balancer_configs (
    id SERIAL PRIMARY KEY,
    config_name VARCHAR(100) UNIQUE NOT NULL,
    models JSONB NOT NULL,                -- 模型列表和权重配置
    strategy VARCHAR(50) DEFAULT 'round_robin', -- round_robin, weighted, least_used
    fallback_model VARCHAR(100),
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_api_calls_created_at ON api_calls(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_api_calls_model_name ON api_calls(model_name);
CREATE INDEX IF NOT EXISTS idx_api_calls_provider ON api_calls(provider);
CREATE INDEX IF NOT EXISTS idx_usage_stats_date ON usage_stats(date DESC);
CREATE INDEX IF NOT EXISTS idx_usage_stats_model ON usage_stats(model_name);

-- 插入初始API密钥配置
INSERT INTO api_keys (key_name, api_key, provider, model_family, rate_limit_per_minute, rate_limit_per_day) VALUES
('openrouter_1', '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', 'openrouter', 'claude', 60, 1000),
('openrouter_2', '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', 'openrouter', 'claude', 60, 1000),
('openrouter_3', '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', 'openrouter', 'claude', 60, 1000),
('openrouter_4', '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', 'openrouter', 'claude', 60, 1000)
ON CONFLICT (key_name) DO NOTHING;

-- 插入模型配置
INSERT INTO model_configs (model_name, provider, api_key_id, max_tokens, temperature, cost_per_1k_input, cost_per_1k_output) VALUES
('anthropic/claude-3.5-sonnet', 'openrouter', 1, 4096, 0.7, 3.0, 15.0),
('anthropic/claude-3-haiku', 'openrouter', 2, 4096, 0.7, 0.25, 1.25),
('meta-llama/llama-3.1-8b-instruct', 'openrouter', 3, 4096, 0.7, 0.18, 0.18),
('openai/gpt-4o-mini', 'openrouter', 4, 4096, 0.7, 0.15, 0.6)
ON CONFLICT (model_name) DO NOTHING;

-- 插入负载均衡配置
INSERT INTO load_balancer_configs (config_name, models, strategy, fallback_model) VALUES
('jixia_debate', '{"anthropic/claude-3.5-sonnet": 0.4, "anthropic/claude-3-haiku": 0.3, "meta-llama/llama-3.1-8b-instruct": 0.2, "openai/gpt-4o-mini": 0.1}', 'weighted', 'anthropic/claude-3-haiku'),
('rss_analysis', '{"anthropic/claude-3-haiku": 0.6, "openai/gpt-4o-mini": 0.4}', 'round_robin', 'anthropic/claude-3-haiku')
ON CONFLICT (config_name) DO NOTHING;

-- 显示表结构
\d api_keys;
\d model_configs;
\d api_calls;
\d usage_stats;
\d load_balancer_configs;
