import time
from ibapi.interactors.IBMarketData import IBMarketData
from ib_insync import util, Ticker

def main():
    """
    Connects to IB and fetches fundamental and market data for specified symbols.
    """
    # Set up logging
    util.logToConsole()

    market_data_interactor = IBMarketData()

    # --- Stock Fundamental Data ---
    stocks_to_fetch = {
        'US': 'TSLA',
        'HK': '9992',
    }

    print("--- Verifying Stock Fundamental Data ---")
    for market, symbol in stocks_to_fetch.items():
        print(f"\nFetching fundamental data for {symbol} in {market} market...")
        try:
            data = market_data_interactor.get_fundamental_data(symbol, market, report_type='ReportSnapshot')
            print(f"--- Raw XML Data for {symbol} ---")
            print(data[:1000] + "..." if len(data) > 1000 else data) # Print a snippet
            print("--------------------------")
            time.sleep(5)
        except Exception as e:
            print(f"An error occurred while fetching data for {symbol}: {e}")

    # --- Crypto Market Data ---
    crypto_to_fetch = 'BTC'
    print(f"\n--- Verifying Crypto Market Data ---")
    print(f"Fetching market data for {crypto_to_fetch}...")
    try:
        ticker = market_data_interactor.get_crypto_market_data(crypto_to_fetch)
        print(f"--- Ticker Data for {crypto_to_fetch} ---")
        if ticker:
            print(f"  Time: {ticker.time}")
            print(f"  Bid: {ticker.bid}")
            print(f"  Ask: {ticker.ask}")
            print(f"  Last: {ticker.last}")
            print(f"  Volume: {ticker.volume}")
        else:
            print("No ticker data received.")
        print("--------------------------")
    except Exception as e:
        print(f"An error occurred while fetching data for {crypto_to_fetch}: {e}")


    print("\n--- Verification Complete ---")
    market_data_interactor.ibService.ib_client.disconnect()

if __name__ == "__main__":
    main()