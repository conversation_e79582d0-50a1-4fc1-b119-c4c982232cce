#!/usr/bin/env python3
"""
N8N智能测试器 - 处理生产和测试模式的webhook
自动处理测试模式激活问题
"""

import requests
import time
import json
from datetime import datetime
from typing import Dict, Any

class N8NSmartTester:
    """N8N智能测试器 - 自动处理两种模式"""
    
    def __init__(self):
        self.prod_webhook = "https://houzhongxu-n8n-free.hf.space/webhook/ce40f698-832e-475a-a3c7-0895c9e2e90b"
        self.test_webhook = "https://houzhongxu-n8n-free.hf.space/webhook-test/ce40f698-832e-475a-a3c7-0895c9e2e90b"
        self.workflow_url = "https://houzhongxu-n8n-free.hf.space/workflow/5Ibi4vJZjSB0ZaTt"
        
        print("🧪 N8N智能测试器启动")
        print(f"🌐 生产webhook: {self.prod_webhook}")
        print(f"🔧 测试webhook: {self.test_webhook}")
    
    def comprehensive_test(self) -> Dict[str, Any]:
        """全面测试N8N连接"""
        
        print("\n🔍 开始全面测试...")
        
        test_data = {
            "test_type": "comprehensive_test",
            "timestamp": datetime.now().isoformat(),
            "source": "N8N智能测试器",
            "message": "全面连接测试"
        }
        
        results = {
            "test_timestamp": datetime.now().isoformat(),
            "production_mode": {},
            "test_mode": {},
            "recommendations": []
        }
        
        # 1. 测试生产模式
        print("\n1️⃣ 测试生产模式webhook...")
        prod_result = self._test_webhook(self.prod_webhook, test_data, "生产模式")
        results["production_mode"] = prod_result
        
        if prod_result["success"]:
            print("✅ 生产模式完全正常！")
            results["recommendations"].append("✅ 生产webhook可用，建议用于正式集成")
        else:
            print(f"❌ 生产模式失败: {prod_result.get('error', '未知错误')}")
            results["recommendations"].append("⚠️ 生产webhook异常，需要检查N8N状态")
        
        # 2. 测试测试模式（带激活指导）
        print("\n2️⃣ 测试测试模式webhook...")
        test_result = self._test_test_mode_with_guidance(test_data)
        results["test_mode"] = test_result
        
        # 3. 生成建议
        results["recommendations"].extend(self._generate_recommendations(prod_result, test_result))
        
        return results
    
    def _test_webhook(self, url: str, data: Dict, mode_name: str) -> Dict[str, Any]:
        """测试单个webhook"""
        
        try:
            response = requests.post(
                url, 
                json=data, 
                timeout=10,
                headers={'Content-Type': 'application/json'}
            )
            
            result = {
                "success": response.status_code == 200,
                "status_code": response.status_code,
                "response_time": response.elapsed.total_seconds(),
                "url": url,
                "mode": mode_name
            }
            
            if response.status_code == 200:
                try:
                    result["response_data"] = response.json()
                except:
                    result["response_data"] = response.text
            else:
                result["error_message"] = response.text
            
            return result
            
        except requests.exceptions.Timeout:
            return {
                "success": False,
                "error": "请求超时",
                "url": url,
                "mode": mode_name
            }
        except requests.exceptions.ConnectionError:
            return {
                "success": False,
                "error": "连接错误",
                "url": url,
                "mode": mode_name
            }
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "url": url,
                "mode": mode_name
            }
    
    def _test_test_mode_with_guidance(self, data: Dict) -> Dict[str, Any]:
        """测试测试模式并提供激活指导"""
        
        # 首先尝试测试模式
        initial_result = self._test_webhook(self.test_webhook, data, "测试模式")
        
        if initial_result["success"]:
            print("✅ 测试模式正常工作！")
            return initial_result
        
        # 如果失败，检查是否是404错误（需要激活）
        if initial_result.get("status_code") == 404:
            print("⚠️ 测试模式需要激活")
            print(f"💡 请按以下步骤激活测试模式:")
            print(f"   1. 访问: {self.workflow_url}")
            print(f"   2. 点击 'Execute workflow' 按钮")
            print(f"   3. 激活后立即重新测试")
            
            # 询问用户是否已激活
            user_input = input("\n是否已在N8N界面中激活测试模式？(y/n): ").lower().strip()
            
            if user_input == 'y':
                print("🔄 重新测试激活后的测试模式...")
                activated_result = self._test_webhook(self.test_webhook, data, "测试模式(已激活)")
                
                if activated_result["success"]:
                    print("✅ 测试模式激活成功！")
                else:
                    print("❌ 测试模式仍然失败，可能需要重新激活")
                
                return activated_result
            else:
                print("ℹ️ 测试模式跳过，可稍后手动激活测试")
                return {
                    "success": False,
                    "skipped": True,
                    "reason": "用户选择跳过测试模式激活",
                    "mode": "测试模式"
                }
        
        return initial_result
    
    def _generate_recommendations(self, prod_result: Dict, test_result: Dict) -> list:
        """生成测试建议"""
        
        recommendations = []
        
        # 基于生产模式结果
        if prod_result["success"]:
            recommendations.extend([
                "🚀 建议使用生产webhook进行正式集成",
                "📊 可以开始配置自动化数据推送",
                "🔄 建议设置定时任务推送RSS和市场数据"
            ])
        else:
            recommendations.extend([
                "🔧 需要检查N8N工作流配置",
                "📞 建议联系N8N管理员检查服务状态",
                "⏰ 可以稍后重试生产模式测试"
            ])
        
        # 基于测试模式结果
        if test_result.get("success"):
            recommendations.append("🧪 测试模式可用于开发调试")
        elif test_result.get("skipped"):
            recommendations.append("🔧 如需调试，可手动激活测试模式")
        else:
            recommendations.append("⚠️ 测试模式需要在N8N界面中手动激活")
        
        # 通用建议
        recommendations.extend([
            "📋 建议在N8N中将工作流设置为'Active'状态",
            "📈 建议设置监控以跟踪webhook性能",
            "🔒 建议定期检查webhook安全性"
        ])
        
        return recommendations
    
    def quick_production_test(self) -> bool:
        """快速测试生产模式（用于自动化脚本）"""
        
        print("⚡ 快速生产模式测试...")
        
        test_data = {
            "quick_test": True,
            "timestamp": datetime.now().isoformat(),
            "source": "快速测试"
        }
        
        result = self._test_webhook(self.prod_webhook, test_data, "生产模式")
        
        if result["success"]:
            print("✅ 生产模式正常")
            return True
        else:
            print(f"❌ 生产模式失败: {result.get('error', '未知错误')}")
            return False
    
    def generate_integration_code(self, test_results: Dict) -> str:
        """基于测试结果生成集成代码"""
        
        if test_results["production_mode"]["success"]:
            return f'''
# 基于测试结果的N8N集成代码
import requests
from datetime import datetime

class TaiGongXinYiN8NClient:
    def __init__(self):
        # 使用验证可用的生产webhook
        self.webhook_url = "{self.prod_webhook}"
    
    def send_data(self, data):
        """发送数据到N8N"""
        try:
            response = requests.post(
                self.webhook_url,
                json={{
                    "timestamp": datetime.now().isoformat(),
                    "source": "太公心易",
                    "data": data
                }},
                timeout=30,
                headers={{'Content-Type': 'application/json'}}
            )
            
            return {{
                "success": response.status_code == 200,
                "status_code": response.status_code,
                "response": response.text
            }}
        except Exception as e:
            return {{
                "success": False,
                "error": str(e)
            }}

# 使用示例
client = TaiGongXinYiN8NClient()
result = client.send_data({{"test": "integration_ready"}})
print("集成测试:", "成功" if result["success"] else "失败")
'''
        else:
            return '''
# N8N集成暂时不可用
# 请先解决生产webhook的问题，然后重新生成集成代码
print("⚠️ N8N生产webhook不可用，请检查配置")
'''

def main():
    """主测试函数"""
    
    print("🧪 N8N智能测试器")
    print("=" * 50)
    
    tester = N8NSmartTester()
    
    # 执行全面测试
    test_results = tester.comprehensive_test()
    
    # 保存测试结果
    with open("n8n_smart_test_results.json", "w", encoding="utf-8") as f:
        json.dump(test_results, f, ensure_ascii=False, indent=2)
    
    # 生成集成代码
    integration_code = tester.generate_integration_code(test_results)
    with open("n8n_integration_code.py", "w", encoding="utf-8") as f:
        f.write(integration_code)
    
    # 输出总结
    print("\n" + "=" * 50)
    print("📊 测试总结:")
    
    prod_status = "✅ 正常" if test_results["production_mode"]["success"] else "❌ 异常"
    test_status = "✅ 正常" if test_results["test_mode"].get("success") else "⚠️ 需要激活"
    
    print(f"生产模式: {prod_status}")
    print(f"测试模式: {test_status}")
    
    print(f"\n💡 主要建议:")
    for i, rec in enumerate(test_results["recommendations"][:5], 1):
        print(f"  {i}. {rec}")
    
    print(f"\n📁 文件已生成:")
    print(f"• n8n_smart_test_results.json - 详细测试结果")
    print(f"• n8n_integration_code.py - 集成代码")
    
    # 给出明确的下一步指导
    if test_results["production_mode"]["success"]:
        print(f"\n🚀 下一步: 生产webhook正常，可以开始集成！")
        print(f"   运行: python3 n8n_integration_code.py")
    else:
        print(f"\n🔧 下一步: 需要修复生产webhook问题")
        print(f"   检查N8N工作流状态和配置")
    
    return test_results

if __name__ == "__main__":
    results = main()