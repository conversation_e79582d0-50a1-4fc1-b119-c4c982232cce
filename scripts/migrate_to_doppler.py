#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Cauldron项目Doppler迁移脚本
将现有.env配置迁移到Doppler密钥管理服务
"""

import os
import re
import subprocess
import json
from pathlib import Path
from typing import Dict, List, Tuple
from dotenv import load_dotenv

class DopplerMigrator:
    """Doppler迁移工具"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent.parent
        self.env_file = self.project_root / ".env"
        
        # 配置分类
        self.config_categories = {
            "database": [
                "DATABASE_URL", "DB_HOST", "DB_PORT", "DB_NAME", "DB_USER", "DB_PASSWORD",
                "ZILLIZ_ENDPOINT", "ZILLIZ_USER", "ZILLIZ_PASSWD", "ZILLIZ_TOKEN",
                "mongodb_string", "mongodb_passwd"
            ],
            "ai_services": [
                "OPENROUTER_API_KEY_1", "OPENROUTER_API_KEY_2", "OPENROUTER_API_KEY_3", "OPENROUTER_API_KEY_4",
                "ANTHROPIC_AUTH_TOKEN", "ANTHROPIC_BASE_URL",
                "LITELLM_BASE_URL", "LITELLM_API_KEY"
            ],
            "finance_apis": [
                "finnhub", "coingecko", "coindesk", "blockchair",
                "coinbase_id", "coinbase_secret",
                "IB_HOST", "IB_PORT", "IB_CLIENT_ID", "IB_TIMEOUT", "IB_RETRY_COUNT",
                "IB_MARKET_DATA_TYPE", "IB_REQUEST_TIMEOUT"
            ],
            "social_platforms": [
                "MASTODON_APP_ID", "MASTODON_APP_SECRET", "MASTODON_ACCESS_TOKEN",
                "n8n_url", "n8n_token", "n8n_webhook"
            ],
            "heroku_services": [
                "KOREAN_MCP_SERVER_URL", "KOREAN_MCP_API_KEY", "KOREAN_MCP_TIMEOUT"
            ],
            "optional_apis": [
                "COINGECKO_PRO_API_KEY", "ALPHA_VANTAGE_API_KEY", "POLYGON_API_KEY"
            ]
        }
    
    def check_doppler_cli(self) -> bool:
        """检查Doppler CLI是否安装"""
        try:
            result = subprocess.run(['doppler', '--version'], 
                                  capture_output=True, text=True, timeout=5)
            if result.returncode == 0:
                print(f"✅ Doppler CLI已安装: {result.stdout.strip()}")
                return True
        except (subprocess.TimeoutExpired, FileNotFoundError):
            pass
        
        print("❌ Doppler CLI未安装")
        print("请运行: curl -Ls https://cli.doppler.com/install.sh | sh")
        return False
    
    def parse_env_file(self) -> Dict[str, str]:
        """解析.env文件"""
        if not self.env_file.exists():
            print(f"❌ .env文件不存在: {self.env_file}")
            return {}
        
        env_vars = {}
        
        with open(self.env_file, 'r', encoding='utf-8') as f:
            for line_num, line in enumerate(f, 1):
                line = line.strip()
                
                # 跳过注释和空行
                if not line or line.startswith('#'):
                    continue
                
                # 处理export语句
                if line.startswith('export '):
                    line = line[7:]  # 移除'export '
                
                # 解析键值对
                if '=' in line:
                    key, value = line.split('=', 1)
                    key = key.strip()
                    value = value.strip()
                    
                    # 移除引号
                    if value.startswith('"') and value.endswith('"'):
                        value = value[1:-1]
                    elif value.startswith("'") and value.endswith("'"):
                        value = value[1:-1]
                    
                    # 跳过空值
                    if value:
                        env_vars[key] = value
        
        print(f"📊 解析到 {len(env_vars)} 个环境变量")
        return env_vars
    
    def categorize_configs(self, env_vars: Dict[str, str]) -> Dict[str, Dict[str, str]]:
        """将配置按类别分组"""
        categorized = {category: {} for category in self.config_categories}
        categorized["uncategorized"] = {}
        
        for key, value in env_vars.items():
            categorized_flag = False
            
            for category, keys in self.config_categories.items():
                if key in keys:
                    categorized[category][key] = value
                    categorized_flag = True
                    break
            
            if not categorized_flag:
                categorized["uncategorized"][key] = value
        
        return categorized
    
    def create_doppler_project(self, project_name: str = "cauldron") -> bool:
        """创建Doppler项目"""
        try:
            # 检查项目是否已存在
            result = subprocess.run(['doppler', 'projects', 'list', '--json'], 
                                  capture_output=True, text=True, timeout=10)
            
            if result.returncode == 0:
                projects = json.loads(result.stdout)
                existing_projects = [p['name'] for p in projects]
                
                if project_name in existing_projects:
                    print(f"✅ Doppler项目 '{project_name}' 已存在")
                    return True
            
            # 创建新项目
            result = subprocess.run(['doppler', 'projects', 'create', project_name], 
                                  capture_output=True, text=True, timeout=10)
            
            if result.returncode == 0:
                print(f"✅ 创建Doppler项目: {project_name}")
                return True
            else:
                print(f"❌ 创建项目失败: {result.stderr}")
                return False
                
        except Exception as e:
            print(f"❌ 创建项目时出错: {e}")
            return False
    
    def setup_environments(self, project_name: str = "cauldron") -> bool:
        """设置环境"""
        environments = ["development", "staging", "production"]
        
        for env in environments:
            try:
                # 配置项目和环境
                result = subprocess.run([
                    'doppler', 'configure', 'set', 
                    f'project={project_name}', 
                    f'config={env}'
                ], capture_output=True, text=True, timeout=10)
                
                if result.returncode == 0:
                    print(f"✅ 配置环境: {env}")
                else:
                    print(f"⚠️ 配置环境 {env} 时出现问题: {result.stderr}")
                    
            except Exception as e:
                print(f"❌ 设置环境 {env} 时出错: {e}")
                return False
        
        return True
    
    def upload_secrets(self, categorized_configs: Dict[str, Dict[str, str]], 
                      environment: str = "development") -> bool:
        """上传密钥到Doppler"""
        print(f"\n🔐 上传密钥到环境: {environment}")
        
        for category, configs in categorized_configs.items():
            if not configs:
                continue
                
            print(f"\n📂 处理类别: {category}")
            
            for key, value in configs.items():
                try:
                    # 使用doppler secrets set命令
                    result = subprocess.run([
                        'doppler', 'secrets', 'set', 
                        f'{key}={value}',
                        '--config', environment
                    ], capture_output=True, text=True, timeout=10)
                    
                    if result.returncode == 0:
                        print(f"  ✅ {key}")
                    else:
                        print(f"  ❌ {key}: {result.stderr}")
                        
                except Exception as e:
                    print(f"  ❌ {key}: {e}")
        
        return True
    
    def generate_migration_report(self, categorized_configs: Dict[str, Dict[str, str]]) -> str:
        """生成迁移报告"""
        report = []
        report.append("# Cauldron项目Doppler迁移报告")
        report.append(f"迁移时间: {os.popen('date').read().strip()}")
        report.append("")
        
        total_secrets = sum(len(configs) for configs in categorized_configs.values())
        report.append(f"## 总览")
        report.append(f"- 总密钥数量: {total_secrets}")
        report.append(f"- 配置类别: {len([c for c in categorized_configs if categorized_configs[c]])}")
        report.append("")
        
        for category, configs in categorized_configs.items():
            if configs:
                report.append(f"### {category.title()} ({len(configs)}个)")
                for key in configs.keys():
                    # 遮蔽敏感信息
                    masked_key = key if len(key) <= 10 else f"{key[:6]}...{key[-4:]}"
                    report.append(f"- {masked_key}")
                report.append("")
        
        return "\n".join(report)
    
    def run_migration(self, project_name: str = "cauldron", 
                     environment: str = "development") -> bool:
        """执行完整迁移流程"""
        print("🚀 开始Cauldron项目Doppler迁移")
        print("=" * 50)
        
        # 1. 检查Doppler CLI
        if not self.check_doppler_cli():
            return False
        
        # 2. 解析.env文件
        env_vars = self.parse_env_file()
        if not env_vars:
            return False
        
        # 3. 分类配置
        categorized_configs = self.categorize_configs(env_vars)
        
        # 4. 创建Doppler项目
        if not self.create_doppler_project(project_name):
            return False
        
        # 5. 设置环境
        if not self.setup_environments(project_name):
            return False
        
        # 6. 上传密钥
        if not self.upload_secrets(categorized_configs, environment):
            return False
        
        # 7. 生成报告
        report = self.generate_migration_report(categorized_configs)
        report_file = self.project_root / "doppler_migration_report.md"
        
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report)
        
        print(f"\n✅ 迁移完成！")
        print(f"📊 迁移报告: {report_file}")
        print(f"\n🔧 下一步:")
        print(f"1. 运行: doppler configure set project={project_name} config={environment}")
        print(f"2. 测试: doppler run -- python -c 'import os; print(os.getenv(\"DATABASE_URL\"))'")
        print(f"3. 更新部署脚本使用: doppler run -- your_command")
        
        return True

def main():
    """主函数"""
    migrator = DopplerMigrator()
    
    # 交互式选择
    print("🔐 Cauldron项目Doppler迁移工具")
    print("=" * 40)
    
    project_name = input("项目名称 [cauldron]: ").strip() or "cauldron"
    environment = input("环境名称 [development]: ").strip() or "development"
    
    confirm = input(f"\n确认迁移到项目 '{project_name}' 环境 '{environment}'? (y/N): ")
    
    if confirm.lower() in ['y', 'yes']:
        migrator.run_migration(project_name, environment)
    else:
        print("❌ 迁移已取消")

if __name__ == "__main__":
    main()
