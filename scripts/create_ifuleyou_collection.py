#!/usr/bin/env python3
"""
创建 ifuleyou collection - 大话西游中至尊宝的无奈之语
专门为N8N RSS工作流设计的collection
"""

import os
import sys
from pymilvus import connections, Collection, FieldSchema, CollectionSchema, DataType

class IfuleyouCollectionCreator:
    """ifuleyou Collection创建器"""
    
    def __init__(self):
        # 硬编码Zilliz连接信息（从代码中找到的）
        self.zilliz_endpoint = "https://in03-2a809e6ceec742f.serverless.ali-cn-hangzhou.cloud.zilliz.com.cn:443"
        self.zilliz_token = "c6e0fe07956530d9c44256c63107cfa5dc2dae5263eda014e0035f1e62dc7056f31071d000b11f93bae3017add49a63f1b615034"
        self.collection_name = "ifuleyou"
        self.vector_dimension = 1536  # OpenAI embedding维度
        
        print("🎭 ifuleyou Collection创建器初始化")
        print(f"📊 Collection名称: {self.collection_name}")
        print(f"🔢 向量维度: {self.vector_dimension}")
        print("💫 '我佛了you' - 至尊宝的无奈之语")
    
    def connect_zilliz(self) -> bool:
        """连接Zilliz Cloud"""
        try:
            connections.connect(
                alias="default",
                uri=self.zilliz_endpoint,
                token=self.zilliz_token
            )
            
            print("✅ Zilliz Cloud连接成功")
            return True
            
        except Exception as e:
            print(f"❌ Zilliz连接失败: {e}")
            return False
    
    def create_collection_schema(self) -> CollectionSchema:
        """创建Collection Schema"""
        print("🔨 创建Collection Schema...")
        
        # 定义字段
        fields = [
            # 主键字段
            FieldSchema(
                name="id",
                dtype=DataType.INT64,
                is_primary=True,
                auto_id=True,
                description="文章唯一标识符"
            ),
            
            # 标题字段 - 这是N8N报错要求的字段！
            FieldSchema(
                name="title",
                dtype=DataType.VARCHAR,
                max_length=512,
                description="文章标题"
            ),
            
            # 内容字段
            FieldSchema(
                name="content",
                dtype=DataType.VARCHAR,
                max_length=65535,
                description="文章内容"
            ),
            
            # 发布日期
            FieldSchema(
                name="published_date",
                dtype=DataType.VARCHAR,
                max_length=64,
                description="发布日期"
            ),
            
            # 文章ID
            FieldSchema(
                name="article_id",
                dtype=DataType.VARCHAR,
                max_length=128,
                description="文章ID"
            ),
            
            # 向量字段
            FieldSchema(
                name="vector",
                dtype=DataType.FLOAT_VECTOR,
                dim=self.vector_dimension,
                description="文章向量表示"
            )
        ]
        
        # 创建Schema
        schema = CollectionSchema(
            fields=fields,
            description="ifuleyou - RSS文章向量存储，专为N8N工作流设计",
            enable_dynamic_field=True  # 允许动态字段
        )
        
        print("✅ Collection Schema创建完成")
        print("📋 字段列表:")
        for field in fields:
            print(f"   - {field.name}: {field.dtype} ({field.description})")
        
        return schema
    
    def create_collection(self) -> bool:
        """创建Collection"""
        try:
            # 检查collection是否已存在
            from pymilvus import utility
            existing_collections = utility.list_collections()
            if self.collection_name in existing_collections:
                print(f"⚠️  Collection '{self.collection_name}' 已存在")

                # 直接删除重建
                collection = Collection(self.collection_name)
                collection.drop()
                print(f"🗑️  已删除旧的Collection '{self.collection_name}'")
                print("🔄 继续创建新的Collection...")
            
            # 创建Schema
            schema = self.create_collection_schema()
            
            # 创建Collection
            print(f"🔨 创建Collection: {self.collection_name}")
            collection = Collection(
                name=self.collection_name,
                schema=schema,
                using='default'
            )
            
            print("✅ Collection创建成功")
            
            # 创建索引
            self.create_index(collection)
            
            return True
            
        except Exception as e:
            print(f"❌ Collection创建失败: {e}")
            return False
    
    def create_index(self, collection: Collection) -> bool:
        """创建向量索引"""
        try:
            print("🔍 创建向量索引...")
            
            # 为向量字段创建索引
            index_params = {
                "metric_type": "COSINE",
                "index_type": "IVF_FLAT",
                "params": {"nlist": 128}
            }
            
            collection.create_index(
                field_name="vector",
                index_params=index_params
            )
            
            print("✅ 向量索引创建成功")
            return True
            
        except Exception as e:
            print(f"❌ 索引创建失败: {e}")
            return False
    
    def load_collection(self) -> bool:
        """加载Collection到内存"""
        try:
            print("📥 加载Collection到内存...")
            
            collection = Collection(self.collection_name)
            collection.load()
            
            print("✅ Collection加载成功")
            return True
            
        except Exception as e:
            print(f"❌ Collection加载失败: {e}")
            return False
    
    def run(self) -> bool:
        """执行完整的创建流程"""
        print("🚀 开始创建ifuleyou Collection...")
        print("=" * 50)
        
        # 1. 连接Zilliz
        if not self.connect_zilliz():
            return False
        
        # 2. 创建Collection
        if not self.create_collection():
            return False
        
        # 3. 加载Collection
        if not self.load_collection():
            return False
        
        print("=" * 50)
        print("🎉 ifuleyou Collection创建完成！")
        print("💡 现在你可以在N8N中使用这个collection了")
        print(f"📝 Collection名称: {self.collection_name}")
        print("🎭 '我佛了you' - 问题应该解决了！")
        
        return True

def main():
    """主函数"""
    creator = IfuleyouCollectionCreator()
    success = creator.run()
    
    if success:
        print("\n✅ 脚本执行成功")
        sys.exit(0)
    else:
        print("\n❌ 脚本执行失败")
        sys.exit(1)

if __name__ == "__main__":
    main()
