#!/usr/bin/env python3
"""
MCP管理器启动脚本
一键启动和管理所有MCP服务
"""

import os
import sys
import asyncio
import argparse
from pathlib import Path
from mcp_manager import MCPManager

def main():
    parser = argparse.ArgumentParser(description="MCP服务管理器")
    parser.add_argument("--config", "-c", default="mcp_services.yml", 
                       help="配置文件路径")
    parser.add_argument("--port", "-p", type=int, default=8090,
                       help="管理器端口")
    parser.add_argument("--host", default="0.0.0.0",
                       help="绑定地址")
    parser.add_argument("--start-all", action="store_true",
                       help="启动时自动启动所有服务")
    parser.add_argument("--group", "-g", 
                       help="启动指定服务组 (financial/workflow/inference/core)")
    
    args = parser.parse_args()
    
    # 检查配置文件
    if not os.path.exists(args.config):
        print(f"❌ 配置文件不存在: {args.config}")
        print("💡 运行以下命令创建默认配置:")
        print(f"   python mcp_manager.py")
        sys.exit(1)
    
    print("🚀 启动MCP管理器...")
    print(f"📁 配置文件: {args.config}")
    print(f"🌐 管理界面: http://{args.host}:{args.port}")
    print(f"📊 API文档: http://{args.host}:{args.port}/docs")
    
    # 创建管理器
    manager = MCPManager(args.config)
    
    # 如果指定了服务组，启动该组服务
    if args.group:
        asyncio.run(start_service_group(manager, args.group))
    elif args.start_all:
        asyncio.run(start_all_services(manager))
    
    # 启动管理器
    try:
        manager.run(host=args.host, port=args.port)
    except KeyboardInterrupt:
        print("\n🛑 正在停止MCP管理器...")
        asyncio.run(stop_all_services(manager))
        print("✅ MCP管理器已停止")

async def start_service_group(manager, group_name):
    """启动指定服务组"""
    # 这里需要从配置文件读取服务组信息
    service_groups = {
        'financial': ['yahoo-finance', 'cauldron-financial', 'zilliz-truth'],
        'workflow': ['tusita-palace', 'local-conductor'],
        'inference': ['heroku-inference'],
        'core': ['cauldron-financial', 'tusita-palace', 'local-conductor']
    }
    
    if group_name not in service_groups:
        print(f"❌ 未知服务组: {group_name}")
        print(f"💡 可用服务组: {', '.join(service_groups.keys())}")
        return
    
    print(f"🔄 启动服务组: {group_name}")
    services = service_groups[group_name]
    
    for service_name in services:
        print(f"  ⏳ 启动服务: {service_name}")
        success = await manager.start_service(service_name)
        if success:
            print(f"  ✅ {service_name} 启动成功")
        else:
            print(f"  ❌ {service_name} 启动失败")

async def start_all_services(manager):
    """启动所有服务"""
    print("🔄 启动所有MCP服务...")
    
    for service_name in manager.services.keys():
        print(f"  ⏳ 启动服务: {service_name}")
        success = await manager.start_service(service_name)
        if success:
            print(f"  ✅ {service_name} 启动成功")
        else:
            print(f"  ❌ {service_name} 启动失败")

async def stop_all_services(manager):
    """停止所有服务"""
    print("🔄 停止所有MCP服务...")
    
    for service_name in manager.services.keys():
        await manager.stop_service(service_name)

if __name__ == "__main__":
    main()