#!/bin/bash
# 安装Yahoo Finance MCP

echo "🏛️ 安装Yahoo Finance MCP到稷下学宫..."

# 1. 确保目录存在
mkdir -p scripts/mcp

# 2. 克隆仓库
if [ ! -d "scripts/mcp/yahoo-finance-mcp" ]; then
    echo "📦 克隆Yahoo Finance MCP..."
    cd scripts/mcp
    git clone https://github.com/Alex2Yang97/yahoo-finance-mcp.git
    cd ../..
else
    echo "✅ Yahoo Finance MCP已存在"
fi

# 3. 安装依赖
echo "📦 安装依赖..."
cd scripts/mcp/yahoo-finance-mcp
pip install -e .
cd ../../..

# 4. 测试安装
echo "🧪 测试安装..."
python -c "import yfinance; print('✅ yfinance可用')" || pip install yfinance

echo "✅ Yahoo Finance MCP安装完成！"
echo ""
echo "🚀 运行测试:"
echo "   python tests/test_yahoo_finance_mcp_gemini.py"