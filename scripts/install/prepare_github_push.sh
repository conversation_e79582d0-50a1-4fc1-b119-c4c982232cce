#!/bin/bash
# 稷下学宫 GitHub 推送准备脚本

echo "🏛️ 准备稷下学宫 GitHub 推送..."

# 1. 更新 .gitignore (添加新的忽略规则)
echo "📝 更新 .gitignore..."
cat >> .gitignore << 'EOF'

# 稷下学宫大清理后的新规则
# ================================

# 归档目录
archive/
docs_backup_*/

# 临时和测试文件
tmp_*
test_*
debug_*
*_test.*
*_debug.*
*_demo.*
simple_*

# 开发文档和笔记
INTERNAL_*.md
TODO_*.md
*_notes.md
*_draft.md
DEVELOPMENT_LOG.md

# 配置文件中的敏感信息
mcp_client_config.json
lingbao_mcp_test_report.json

# 工作区管理文件
cleanup_*.sh
organize_*.sh
file_lifecycle_policy.md
github_deployment_plan.md

# Ansible开发文件
ansible/
test-*.yml
test-*.sh

# 大文件和数据
*.db
*.sqlite
data/
logs/
models/
*.pkl
*.parquet

EOF

# 2. 检查当前需要移除的文件
echo "🔍 检查需要从Git中移除的文件..."

# 移除归档目录
if [ -d "archive" ]; then
    echo "📦 移除归档目录..."
    git rm -r --cached archive/ 2>/dev/null || true
fi

# 移除文档备份
git rm -r --cached docs_backup_*/ 2>/dev/null || true

# 移除临时文件
git rm --cached tmp_* 2>/dev/null || true
git rm --cached test_* 2>/dev/null || true
git rm --cached debug_* 2>/dev/null || true
git rm --cached simple_* 2>/dev/null || true

# 移除内部文档
git rm --cached INTERNAL_*.md 2>/dev/null || true
git rm --cached TODO_*.md 2>/dev/null || true
git rm --cached DEVELOPMENT_LOG.md 2>/dev/null || true

# 移除工作区管理文件
git rm --cached cleanup_*.sh 2>/dev/null || true
git rm --cached organize_*.sh 2>/dev/null || true
git rm --cached file_lifecycle_policy.md 2>/dev/null || true
git rm --cached github_deployment_plan.md 2>/dev/null || true

# 移除Ansible开发文件
git rm -r --cached ansible/ 2>/dev/null || true
git rm --cached test-*.yml 2>/dev/null || true
git rm --cached test-*.sh 2>/dev/null || true

# 3. 检查Heroku部署必需文件
echo "🚀 检查Heroku部署文件..."

# 确保关键文件存在
required_files=(
    "Procfile"
    "heroku.yml" 
    "pyproject.toml"
    "README.md"
    "app/streamlit_app.py"
    "src/"
    "scripts/"
)

missing_files=()
for file in "${required_files[@]}"; do
    if [ ! -e "$file" ]; then
        missing_files+=("$file")
    fi
done

if [ ${#missing_files[@]} -gt 0 ]; then
    echo "❌ 缺少关键文件:"
    printf '%s\n' "${missing_files[@]}"
    echo "请确保这些文件存在后再推送"
    exit 1
fi

# 4. 创建生产环境的requirements.txt
echo "📦 生成生产requirements.txt..."
if [ -f "pyproject.toml" ]; then
    # 从pyproject.toml提取核心依赖
    cat > requirements.txt << 'EOF'
# 稷下学宫生产环境依赖
streamlit>=1.28.0
pandas>=2.0.0
numpy>=1.24.0
requests>=2.31.0
pymongo>=4.6.0
python-dotenv>=1.0.0
autogen-agentchat>=0.2.0
openai>=1.0.0
asyncio-mqtt>=0.16.0

# Web框架
fastapi>=0.104.0
uvicorn>=0.24.0

# 数据处理
pydantic>=2.5.0
aiohttp>=3.9.0

# 监控和日志
structlog>=23.2.0
EOF
fi

# 5. 更新主要文档
echo "📚 更新README.md..."
cat > README.md << 'EOF'
# 🏛️ 稷下学宫 (Jixia Academy)

> 基于AutoGen的多智能体金融分析与辩论系统

## 🎯 项目简介

稷下学宫是一个创新的AI金融分析平台，通过八仙智能体的多角度辩论，为投资决策提供深度洞察。

### 核心特性

- 🤖 **八仙智能体**: 吕洞宾、何仙姑等8个专业投资角色
- 📊 **实时数据**: RSS新闻流 + MongoDB存储
- 🎭 **多角度辩论**: 技术分析 vs 价值投资 vs 情绪分析
- 🔄 **自动化流程**: N8N工作流 + MCP工具集成
- 📈 **可视化界面**: Streamlit交互式界面

## 🚀 快速开始

### 环境要求
- Python 3.11+
- MongoDB Atlas
- OpenRouter API密钥

### 安装部署
```bash
# 1. 克隆项目
git clone https://github.com/your-username/jixia-academy.git
cd jixia-academy

# 2. 安装依赖
pip install -r requirements.txt

# 3. 配置环境变量
cp .env.example .env
# 编辑.env文件，填入你的API密钥

# 4. 启动应用
streamlit run app/streamlit_app.py
```

### Heroku部署
```bash
# 推送到Heroku
git push heroku main
```

## 📖 文档

- [快速开始](docs/getting_started.md)
- [系统架构](docs/architecture/)
- [API文档](docs/api/)

## 🤝 贡献

欢迎提交Issue和Pull Request！

## 📄 许可证

MIT License
EOF

# 6. 显示即将推送的文件
echo "📋 即将推送到GitHub的文件:"
git add .
git status --porcelain | head -20

echo ""
echo "✅ GitHub推送准备完成！"
echo ""
echo "🚀 下一步执行:"
echo "   git add ."
echo "   git commit -m '🏛️ 稷下学宫生产版本 - 大清理完成'"
echo "   git push origin main"
echo ""
echo "📊 预期效果:"
echo "   - 仓库大小减少80%+"
echo "   - 只包含生产必需文件"
echo "   - Heroku可以正确部署"