#!/usr/bin/env python3
"""
精确修复N8N工作流 - 只修改必要的参数
"""

import requests
import json

def fix_n8n_workflow():
    """精确修复N8N工作流"""
    
    workflow_id = "JDwsHwp7VM9lWic7"
    api_token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJhNzYwZjUxMy0zMWMzLTQwYzMtOTQ0Zi0xZDkyNGQ4ZjM3Y2QiLCJpc3MiOiJuOG4iLCJhdWQiOiJwdWJsaWMtYXBpIiwiaWF0IjoxNzUxNTU0NjY4fQ.YO11K1xJQQ9lmo-VYCdg8Vf0jyvQ5ufoLF-sWJLqE08"
    base_url = "https://n8n.git4ta.fun"
    
    headers = {
        "X-N8N-API-KEY": api_token,
        "Content-Type": "application/json"
    }
    
    print("🎯 精确修复N8N工作流")
    print("=" * 40)
    
    # 1. 获取工作流
    try:
        url = f"{base_url}/api/v1/workflows/{workflow_id}"
        response = requests.get(url, headers=headers, timeout=15)
        
        if response.status_code != 200:
            print(f"❌ 获取工作流失败: {response.status_code}")
            return False
        
        workflow = response.json()
        print(f"✅ 获取工作流成功: {workflow.get('name')}")
        
    except Exception as e:
        print(f"❌ 获取工作流异常: {e}")
        return False
    
    # 2. 只修改Milvus节点的Collection名称
    nodes = workflow.get('nodes', [])
    milvus_fixed = False
    
    for node in nodes:
        if node.get('name') == 'Milvus Zilliz CN':
            print(f"🎯 找到Milvus节点，修复Collection名称...")
            
            if 'parameters' not in node:
                node['parameters'] = {}
            
            # 只修改Collection名称
            old_collection = node['parameters'].get('collectionName', 'Not Set')
            node['parameters']['collectionName'] = 'ifuleyou'
            
            print(f"  📝 Collection: {old_collection} → ifuleyou")
            milvus_fixed = True
            break
    
    if not milvus_fixed:
        print("❌ 未找到Milvus节点")
        return False
    
    # 3. 准备更新数据 - 只包含必要字段
    update_data = {
        "name": workflow.get('name'),
        "nodes": workflow.get('nodes'),
        "connections": workflow.get('connections'),
        "active": workflow.get('active', True),
        "settings": workflow.get('settings', {}),
        "staticData": workflow.get('staticData', {}),
        "pinData": workflow.get('pinData', {}),
        "versionId": workflow.get('versionId')
    }
    
    # 4. 更新工作流
    try:
        print("📡 更新工作流...")
        response = requests.put(url, headers=headers, json=update_data, timeout=30)
        
        print(f"📈 响应状态: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ 工作流更新成功！")
            print("🎭 Collection名称已设置为 'ifuleyou'")
            return True
        else:
            print(f"❌ 更新失败: {response.status_code}")
            print(f"📝 响应: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 更新异常: {e}")
        return False

def print_manual_instructions():
    """打印手动修复说明"""
    print("\n" + "="*50)
    print("📋 手动修复说明")
    print("="*50)
    
    print("\n🎯 如果API修复失败，请手动操作：")
    print("1. 访问: https://n8n.git4ta.fun/workflow/JDwsHwp7VM9lWic7")
    print("2. 找到 'Milvus Zilliz CN' 节点")
    print("3. 设置 Collection Name = 'ifuleyou'")
    print("4. 保存工作流")
    
    print("\n💻 同时检查Code节点 '向量化' 或 'Code test':")
    print("确保代码返回格式为：")
    print("""
return [{
    pageContent: "文章内容",
    metadata: {
        title: "文章标题",  // 必须有这个字段！
        published_date: "2025-01-09T...",
        article_id: "article_123"
    }
}];
""")
    
    print("\n🎭 修复完成后，'i服了you'的问题就解决了！")

def main():
    """主函数"""
    success = fix_n8n_workflow()
    
    # 无论成功与否，都提供手动说明
    print_manual_instructions()
    
    if success:
        print("\n🎉 API修复成功！现在可以测试工作流了")
    else:
        print("\n⚠️ API修复失败，请按照上面的说明手动修复")

if __name__ == "__main__":
    main()
