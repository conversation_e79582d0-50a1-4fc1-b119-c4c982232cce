#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
太公心易BI系统 - 晨会简报启动脚本
包含昨日复盘和封神榜功能
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
# .parent 代表向上找一级目录，从而找到真正的项目根目录
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def main():
    """主启动函数"""
    try:
        # 导入并运行晨会简报UI
        from src.ui.morning_briefing_ui import MorningBriefingUI
        
        # 创建并运行UI实例
        ui = MorningBriefingUI()
        ui.render()
        
    except ImportError as e:
        print(f"导入错误: {e}")
        print("请确保所有依赖模块都已正确安装")
        sys.exit(1)
    except Exception as e:
        print(f"运行错误: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()