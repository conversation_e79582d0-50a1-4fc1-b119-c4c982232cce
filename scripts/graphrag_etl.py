#!/usr/bin/env python3
"""
稷下学宫 GraphRAG ETL 处理器
MongoDB RSS数据 → GraphRAG知识图谱
"""

import asyncio
import logging
import os
import time
from datetime import datetime, timedelta
from typing import List, Dict, Any

import pymongo
import requests
from neo4j import GraphDatabase

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('/app/logs/graphrag_etl.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger('GraphRAG-ETL')

class JixiaGraphRAGETL:
    """稷下学宫 GraphRAG ETL 处理器"""
    
    def __init__(self):
        self.mongo_uri = os.getenv('MONGODB_URI')
        self.graphrag_server = os.getenv('GRAPHRAG_SERVER', 'http://graphrag-server:8080')
        self.neo4j_uri = os.getenv('NEO4J_URI', 'bolt://neo4j:7687')
        self.processing_interval = int(os.getenv('PROCESSING_INTERVAL', 300))
        
        # 初始化连接
        self.mongo_client = pymongo.MongoClient(self.mongo_uri)
        self.db = self.mongo_client.rss_database
        self.articles_collection = self.db.articles
        
        # Neo4j连接
        self.neo4j_driver = GraphDatabase.driver(
            self.neo4j_uri,
            auth=("neo4j", "jixia123")
        )
        
        logger.info("🏛️ 稷下学宫 GraphRAG ETL 初始化完成")
    
    async def process_new_articles(self):
        """处理新的RSS文章"""
        try:
            # 查找未处理的文章
            query = {
                "graphrag_processed": {"$ne": True},
                "published_time": {
                    "$gte": (datetime.now() - timedelta(hours=24)).isoformat()
                }
            }
            
            new_articles = list(self.articles_collection.find(query).limit(50))
            
            if not new_articles:
                logger.info("📰 没有新文章需要处理")
                return
            
            logger.info(f"📰 发现 {len(new_articles)} 篇新文章待处理")
            
            for article in new_articles:
                await self.process_single_article(article)
                
        except Exception as e:
            logger.error(f"❌ 处理新文章时出错: {e}")
    
    async def process_single_article(self, article: Dict[str, Any]):
        """处理单篇文章"""
        try:
            article_id = article.get('article_id', str(article['_id']))
            title = article.get('title', '')
            content = article.get('content', article.get('title', ''))
            
            # 1. 发送到GraphRAG进行知识抽取
            graphrag_result = await self.send_to_graphrag(title, content)
            
            if graphrag_result:
                # 2. 存储到Neo4j
                await self.store_to_neo4j(article_id, title, graphrag_result)
                
                # 3. 标记为已处理
                self.articles_collection.update_one(
                    {"_id": article["_id"]},
                    {
                        "$set": {
                            "graphrag_processed": True,
                            "graphrag_processed_at": datetime.now().isoformat(),
                            "graphrag_entities": graphrag_result.get('entities', []),
                            "graphrag_relationships": graphrag_result.get('relationships', [])
                        }
                    }
                )
                
                logger.info(f"✅ 文章处理完成: {title[:50]}...")
            
        except Exception as e:
            logger.error(f"❌ 处理文章失败 {article.get('title', '')}: {e}")
    
    async def send_to_graphrag(self, title: str, content: str) -> Dict[str, Any]:
        """发送文本到GraphRAG服务器进行处理"""
        try:
            payload = {
                "text": f"{title}\n\n{content}",
                "extract_entities": True,
                "extract_relationships": True,
                "language": "zh"
            }
            
            response = requests.post(
                f"{self.graphrag_server}/extract",
                json=payload,
                timeout=60
            )
            
            if response.status_code == 200:
                return response.json()
            else:
                logger.error(f"GraphRAG服务器错误: {response.status_code}")
                return {}
                
        except Exception as e:
            logger.error(f"发送到GraphRAG失败: {e}")
            return {}
    
    async def store_to_neo4j(self, article_id: str, title: str, graphrag_result: Dict[str, Any]):
        """将GraphRAG结果存储到Neo4j"""
        try:
            with self.neo4j_driver.session() as session:
                # 创建文章节点
                session.run("""
                    MERGE (a:Article {id: $article_id})
                    SET a.title = $title,
                        a.processed_at = datetime()
                """, article_id=article_id, title=title)
                
                # 创建实体节点
                entities = graphrag_result.get('entities', [])
                for entity in entities:
                    session.run("""
                        MERGE (e:Entity {name: $name})
                        SET e.type = $type,
                            e.description = $description
                        
                        MERGE (a:Article {id: $article_id})
                        MERGE (a)-[:MENTIONS]->(e)
                    """, 
                    name=entity.get('name', ''),
                    type=entity.get('type', ''),
                    description=entity.get('description', ''),
                    article_id=article_id)
                
                # 创建关系
                relationships = graphrag_result.get('relationships', [])
                for rel in relationships:
                    session.run("""
                        MERGE (e1:Entity {name: $source})
                        MERGE (e2:Entity {name: $target})
                        MERGE (e1)-[r:RELATES_TO {type: $rel_type}]->(e2)
                        SET r.description = $description,
                            r.confidence = $confidence
                    """,
                    source=rel.get('source', ''),
                    target=rel.get('target', ''),
                    rel_type=rel.get('type', 'RELATED'),
                    description=rel.get('description', ''),
                    confidence=rel.get('confidence', 0.5))
                
                logger.info(f"📊 Neo4j存储完成: {len(entities)}个实体, {len(relationships)}个关系")
                
        except Exception as e:
            logger.error(f"Neo4j存储失败: {e}")
    
    async def run_etl_loop(self):
        """运行ETL循环"""
        logger.info(f"🔄 开始ETL循环，处理间隔: {self.processing_interval}秒")
        
        while True:
            try:
                await self.process_new_articles()
                await asyncio.sleep(self.processing_interval)
                
            except KeyboardInterrupt:
                logger.info("👋 收到停止信号，正在关闭...")
                break
            except Exception as e:
                logger.error(f"❌ ETL循环错误: {e}")
                await asyncio.sleep(60)  # 错误后等待1分钟再重试
    
    def close(self):
        """关闭连接"""
        if hasattr(self, 'mongo_client'):
            self.mongo_client.close()
        if hasattr(self, 'neo4j_driver'):
            self.neo4j_driver.close()

async def main():
    """主函数"""
    etl = JixiaGraphRAGETL()
    
    try:
        await etl.run_etl_loop()
    finally:
        etl.close()

if __name__ == "__main__":
    asyncio.run(main())