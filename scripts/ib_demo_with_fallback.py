#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
IB数据抓取演示（带回退机制）
如果IB连接失败，使用模拟数据演示功能

作者：太公心易BI系统
版本：v1.0
"""

import asyncio
import os
import sys
import pandas as pd
import random
from datetime import datetime, timedelta
from pathlib import Path
from dotenv import load_dotenv

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# 加载环境变量
load_dotenv()

try:
    from ib_insync import IB, Stock, util
    IB_AVAILABLE = True
except ImportError:
    IB_AVAILABLE = False


class IBDataDemo:
    """IB数据演示器"""
    
    def __init__(self):
        self.host = os.getenv("IB_HOST", "127.0.0.1")
        self.port = int(os.getenv("IB_PORT", 4002))
        self.client_id = int(os.getenv("IB_CLIENT_ID", 1))
        self.ib = None
        self.connected = False
    
    async def try_connect(self) -> bool:
        """尝试连接IB"""
        if not IB_AVAILABLE:
            print("⚠️ ib_insync未安装，使用模拟数据")
            return False
        
        try:
            print(f"🔌 尝试连接IB: {self.host}:{self.port}")
            self.ib = IB()
            await self.ib.connectAsync(
                host=self.host, 
                port=self.port, 
                clientId=self.client_id, 
                timeout=5
            )
            self.connected = True
            print("✅ IB连接成功!")
            return True
        except Exception as e:
            print(f"⚠️ IB连接失败: {e}")
            print("💡 使用模拟数据演示功能")
            return False
    
    async def get_stock_data(self, symbols: list) -> pd.DataFrame:
        """获取股票数据（真实或模拟）"""
        if self.connected and self.ib:
            return await self._get_real_data(symbols)
        else:
            return self._get_mock_data(symbols)
    
    async def _get_real_data(self, symbols: list) -> pd.DataFrame:
        """获取真实IB数据"""
        data = []
        
        for symbol in symbols:
            try:
                print(f"📊 获取 {symbol} 数据...")
                contract = Stock(symbol, 'SMART', 'USD')
                self.ib.qualifyContracts(contract)
                
                if contract:
                    # 获取市场数据
                    ticker = self.ib.reqMktData(contract, '', False, False)
                    await asyncio.sleep(2)  # 等待数据
                    
                    # 获取基本面数据（如果可用）
                    try:
                        fundamental_data = self.ib.reqFundamentalData(
                            contract, 'ReportsFinSummary', []
                        )
                    except:
                        fundamental_data = None
                    
                    data.append({
                        'symbol': symbol,
                        'price': ticker.last if ticker.last else 0,
                        'bid': ticker.bid if ticker.bid else 0,
                        'ask': ticker.ask if ticker.ask else 0,
                        'volume': ticker.volume if ticker.volume else 0,
                        'market_cap': getattr(ticker, 'marketCap', None),
                        'pe_ratio': None,  # 需要从基本面数据解析
                        'data_source': 'IB_Real'
                    })
                    
                    self.ib.cancelMktData(contract)
                else:
                    print(f"❌ {symbol} 合约验证失败")
                    
            except Exception as e:
                print(f"❌ 获取 {symbol} 数据失败: {e}")
        
        return pd.DataFrame(data)
    
    def _get_mock_data(self, symbols: list) -> pd.DataFrame:
        """生成模拟数据"""
        print("🎭 生成模拟IB数据...")
        
        data = []
        base_prices = {
            'AAPL': 190.50,
            'MSFT': 415.20,
            'GOOGL': 175.80,
            'AMZN': 185.90,
            'TSLA': 248.50,
            'NVDA': 875.30,
            'META': 485.60,
            'NFLX': 485.20
        }
        
        for symbol in symbols:
            base_price = base_prices.get(symbol, random.uniform(50, 500))
            
            # 模拟价格波动
            price_change = random.uniform(-0.05, 0.05)  # ±5%
            current_price = base_price * (1 + price_change)
            
            # 模拟买卖价差
            spread = current_price * 0.001  # 0.1% spread
            bid = current_price - spread/2
            ask = current_price + spread/2
            
            data.append({
                'symbol': symbol,
                'price': round(current_price, 2),
                'bid': round(bid, 2),
                'ask': round(ask, 2),
                'volume': random.randint(1000000, 50000000),
                'market_cap': random.randint(100, 3000) * 1e9,  # 1000亿到3万亿
                'pe_ratio': round(random.uniform(15, 35), 2),
                'pb_ratio': round(random.uniform(1.5, 8.0), 2),
                'roe': round(random.uniform(10, 25), 2),
                'dividend_yield': round(random.uniform(0, 3.5), 2),
                'data_source': 'Mock_Data'
            })
        
        return pd.DataFrame(data)
    
    async def disconnect(self):
        """断开连接"""
        if self.connected and self.ib:
            self.ib.disconnect()
            self.connected = False
            print("🔌 IB连接已断开")
    
    def display_data(self, df: pd.DataFrame):
        """显示数据"""
        print("\n📊 股票数据汇总:")
        print("=" * 80)
        
        for _, row in df.iterrows():
            print(f"📈 {row['symbol']}:")
            print(f"   价格: ${row['price']:.2f}")
            print(f"   买价/卖价: ${row['bid']:.2f} / ${row['ask']:.2f}")
            print(f"   成交量: {row['volume']:,}")
            
            if 'market_cap' in row and pd.notna(row['market_cap']):
                market_cap_b = row['market_cap'] / 1e9
                print(f"   市值: ${market_cap_b:.1f}B")
            
            if 'pe_ratio' in row and pd.notna(row['pe_ratio']):
                print(f"   PE比率: {row['pe_ratio']}")
            
            if 'pb_ratio' in row and pd.notna(row['pb_ratio']):
                print(f"   PB比率: {row['pb_ratio']}")
            
            if 'roe' in row and pd.notna(row['roe']):
                print(f"   ROE: {row['roe']}%")
            
            if 'dividend_yield' in row and pd.notna(row['dividend_yield']):
                print(f"   股息率: {row['dividend_yield']}%")
            
            print(f"   数据源: {row['data_source']}")
            print()


async def main():
    """主函数"""
    print("🎯 IB数据抓取演示（带回退机制）")
    print("=" * 60)
    
    # 创建演示器
    demo = IBDataDemo()
    
    # 测试股票列表
    test_symbols = ['AAPL', 'MSFT', 'GOOGL', 'TSLA', 'NVDA']
    
    try:
        # 尝试连接
        connected = await demo.try_connect()
        
        if connected:
            print("🎉 使用真实IB数据!")
        else:
            print("🎭 使用模拟数据演示!")
        
        print(f"\n📊 获取 {len(test_symbols)} 只股票数据...")
        
        # 获取数据
        df = await demo.get_stock_data(test_symbols)
        
        if not df.empty:
            # 显示数据
            demo.display_data(df)
            
            # 保存到文件
            output_file = f"ib_data_demo_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
            df.to_csv(output_file, index=False)
            print(f"💾 数据已保存到: {output_file}")
            
            print("\n🎉 IB数据抓取演示完成!")
            
            if not connected:
                print("\n💡 要使用真实IB数据，请确保:")
                print("   1. IB Gateway或TWS正在运行")
                print("   2. API设置已启用")
                print("   3. 端口配置正确")
                print("   4. 网络连接正常")
        else:
            print("❌ 未获取到任何数据")
    
    except Exception as e:
        print(f"❌ 演示过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        await demo.disconnect()


if __name__ == "__main__":
    if IB_AVAILABLE:
        util.patchAsyncio()
    
    try:
        asyncio.run(main())
    except (KeyboardInterrupt, SystemExit):
        print("\n🛑 演示被用户中断")
    except Exception as e:
        print(f"\n❌ 演示过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
