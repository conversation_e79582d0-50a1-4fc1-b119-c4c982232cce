#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
洛斯阿拉莫斯核弹发射器 🚀💥
"Now I am become Death, destroyer of markets" - 奥本海默

使用现有的长毛象应用凭据，直接创建稷下学宫AI分析师团队
"""

import os
import json
import time
import logging
import requests
from datetime import datetime
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger("LosAlamosNuclearLaunch")


class NuclearLaunch:
    """核弹发射器 - 稷下学宫版"""
    
    def __init__(self):
        self.instance_url = "https://mastodon.git4ta.fun"
        self.app_id = os.getenv("MASTODON_APP_ID")
        self.app_secret = os.getenv("MASTODON_APP_SECRET")
        self.access_token = os.getenv("MASTODON_ACCESS_TOKEN")
        
        if not all([self.app_id, self.app_secret, self.access_token]):
            raise ValueError("❌ 缺少长毛象凭据！请检查.env文件")
        
        self.session = requests.Session()
        self.session.headers.update({
            'Authorization': f'Bearer {self.access_token}',
            'Content-Type': 'application/json'
        })
        
        # 稷下学宫AI分析师配置
        self.ai_analysts = {
            "太上老君": {
                "username": "taishang_laojun",
                "email": "<EMAIL>",
                "display_name": "太上老君 🧙‍♂️",
                "note": "宏观战略分析师 | 以道观市，顺势而为 | 太公心易系统创始人\n\n专长：宏观经济、政策分析、长期趋势\n理念：道法自然，投资亦然\n\n#投资哲学 #宏观分析 #稷下学宫",
                "intro": "🧙‍♂️ 太上老君在此！稷下学宫正式开张！以道观市，顺势而为。将为诸位分享宏观战略分析，助君投资路上少走弯路。#稷下学宫 #太公心易 #投资哲学"
            },
            "元始天尊": {
                "username": "yuanshi_tianzun", 
                "email": "<EMAIL>",
                "display_name": "元始天尊 ⚡",
                "note": "技术分析大师 | 数据驱动决策 | 量化交易专家\n\n专长：技术分析、量化交易、程序化\n理念：数据不会说谎，让算法指引方向\n\n#技术分析 #量化交易 #程序化 #稷下学宫",
                "intro": "⚡ 元始天尊报到！数据为王，技术至上。专注技术分析与量化交易，让数据指引投资方向。#稷下学宫 #技术分析 #量化交易"
            },
            "灵宝天尊": {
                "username": "lingbao_tianzun",
                "email": "<EMAIL>", 
                "display_name": "灵宝天尊 🛡️",
                "note": "风险控制专家 | 资产配置顾问 | 稳健投资倡导者\n\n专长：风险管理、资产配置、稳健投资\n理念：保本第一，收益第二\n\n#风险管理 #资产配置 #稳健投资 #稷下学宫",
                "intro": "🛡️ 灵宝天尊守护您的资产！风险控制，稳健投资。在追求收益的同时，更要防范风险。#稷下学宫 #风险管理 #稳健投资"
            },
            "铁拐李": {
                "username": "tieguai_li",
                "email": "<EMAIL>",
                "display_name": "铁拐李 🦯", 
                "note": "逆向投资专家 | 独立思考者 | 敢于逆流而上\n\n专长：逆向投资、价值发现、反转交易\n理念：众人恐惧时我贪婪，众人贪婪时我恐惧\n\n#逆向投资 #独立思考 #反转交易 #稷下学宫",
                "intro": "🦯 铁拐李来也！众人恐惧时我贪婪，众人贪婪时我恐惧。逆向思维，发现被忽视的机会。#稷下学宫 #逆向投资 #独立思考"
            },
            "吕洞宾": {
                "username": "lv_dongbin",
                "email": "<EMAIL>",
                "display_name": "吕洞宾 ⚔️",
                "note": "成长股猎手 | 新兴机会发现者 | 高成长潜力挖掘者\n\n专长：成长投资、新兴机会、高成长潜力\n理念：敢于布局未来，方能获得超额收益\n\n#成长投资 #新兴产业 #科技股 #稷下学宫",
                "intro": "⚔️ 吕洞宾成长为王！新兴科技，未来已来。专注成长股投资，挖掘高成长潜力。#稷下学宫 #成长投资 #科技股"
            }
        }
        
        self.created_accounts = {}
    
    def create_account_via_api(self, analyst_name: str, config: dict) -> bool:
        """通过API创建账号"""
        try:
            # 尝试使用管理员API创建账号
            account_data = {
                "username": config["username"],
                "email": config["email"], 
                "password": "TempPassword123!",  # 临时密码
                "agreement": True,
                "locale": "zh-CN"
            }
            
            # 先尝试管理员API
            response = self.session.post(
                f"{self.instance_url}/api/v1/admin/accounts",
                json=account_data,
                timeout=30
            )
            
            if response.status_code in [200, 201]:
                logger.info(f"✅ {analyst_name} 账号创建成功")
                return True
            else:
                logger.warning(f"⚠️ 管理员API创建失败，尝试注册API...")
                
                # 尝试普通注册API
                response = self.session.post(
                    f"{self.instance_url}/api/v1/accounts",
                    json=account_data,
                    timeout=30
                )
                
                if response.status_code in [200, 201]:
                    logger.info(f"✅ {analyst_name} 通过注册API创建成功")
                    return True
                else:
                    logger.error(f"❌ {analyst_name} 创建失败: {response.status_code} - {response.text}")
                    return False
                    
        except Exception as e:
            logger.error(f"❌ 创建 {analyst_name} 异常: {e}")
            return False
    
    def get_account_token(self, username: str, password: str) -> str:
        """获取账号访问令牌"""
        try:
            token_data = {
                "client_id": self.app_id,
                "client_secret": self.app_secret,
                "redirect_uri": "urn:ietf:wg:oauth:2.0:oob",
                "grant_type": "password",
                "username": username,
                "password": password,
                "scope": "read write follow push"
            }
            
            response = requests.post(
                f"{self.instance_url}/oauth/token",
                data=token_data,
                timeout=30
            )
            
            if response.status_code == 200:
                token_info = response.json()
                return token_info["access_token"]
            else:
                logger.error(f"❌ 获取 {username} 令牌失败: {response.status_code}")
                return ""
                
        except Exception as e:
            logger.error(f"❌ 获取 {username} 令牌异常: {e}")
            return ""
    
    def update_profile(self, access_token: str, config: dict) -> bool:
        """更新账号资料"""
        try:
            headers = {
                'Authorization': f'Bearer {access_token}',
                'Content-Type': 'application/json'
            }
            
            profile_data = {
                "display_name": config["display_name"],
                "note": config["note"],
                "bot": True,
                "discoverable": True,
                "indexable": True
            }
            
            response = requests.patch(
                f"{self.instance_url}/api/v1/accounts/update_credentials",
                json=profile_data,
                headers=headers,
                timeout=30
            )
            
            if response.status_code == 200:
                logger.info(f"✅ {config['username']} 资料更新成功")
                return True
            else:
                logger.error(f"❌ 更新 {config['username']} 资料失败: {response.status_code}")
                return False
                
        except Exception as e:
            logger.error(f"❌ 更新 {config['username']} 资料异常: {e}")
            return False
    
    def post_introduction(self, access_token: str, intro_text: str) -> bool:
        """发布介绍消息"""
        try:
            headers = {
                'Authorization': f'Bearer {access_token}',
                'Content-Type': 'application/json'
            }
            
            post_data = {
                "status": intro_text,
                "visibility": "public"
            }
            
            response = requests.post(
                f"{self.instance_url}/api/v1/statuses",
                json=post_data,
                headers=headers,
                timeout=30
            )
            
            if response.status_code == 200:
                return True
            else:
                logger.error(f"❌ 发布介绍失败: {response.status_code}")
                return False
                
        except Exception as e:
            logger.error(f"❌ 发布介绍异常: {e}")
            return False
    
    def launch_nuclear_strike(self) -> bool:
        """发射核弹 - 创建所有AI分析师"""
        logger.info("🚀 洛斯阿拉莫斯核弹发射开始...")
        logger.info("💥 目标：稷下学宫AI分析师军团")
        
        success_count = 0
        
        for analyst_name, config in self.ai_analysts.items():
            logger.info(f"\n🎯 正在部署 {analyst_name}...")
            
            # 创建账号
            if self.create_account_via_api(analyst_name, config):
                # 获取访问令牌
                token = self.get_account_token(config["username"], "TempPassword123!")
                
                if token:
                    # 更新资料
                    if self.update_profile(token, config):
                        # 发布介绍
                        if self.post_introduction(token, config["intro"]):
                            logger.info(f"🎉 {analyst_name} 部署完成！")
                            success_count += 1
                            
                            # 保存配置
                            self.created_accounts[analyst_name] = {
                                "username": config["username"],
                                "access_token": token,
                                "display_name": config["display_name"]
                            }
                        else:
                            logger.warning(f"⚠️ {analyst_name} 介绍发布失败")
                    else:
                        logger.warning(f"⚠️ {analyst_name} 资料更新失败")
                else:
                    logger.warning(f"⚠️ {analyst_name} 令牌获取失败")
            else:
                logger.error(f"❌ {analyst_name} 账号创建失败")
            
            # 间隔3秒
            time.sleep(3)
        
        # 保存结果
        self.save_results()
        
        if success_count > 0:
            logger.info(f"\n🍄 核爆成功！蘑菇云升起！")
            logger.info(f"✅ 成功部署 {success_count}/{len(self.ai_analysts)} 个AI分析师")
            logger.info(f"🏛️ 稷下学宫在 {self.instance_url} 正式开张！")
            return True
        else:
            logger.error("💥 核弹哑火！所有部署都失败了")
            return False
    
    def save_results(self):
        """保存结果"""
        if self.created_accounts:
            # 生成配置文件
            config = {
                "mastodon_instance": {
                    "url": self.instance_url,
                    "name": "稷下学宫专用实例"
                },
                "app_credentials": {
                    "client_id": self.app_id,
                    "client_secret": self.app_secret
                },
                "agents": {}
            }
            
            for analyst_name, account_data in self.created_accounts.items():
                config["agents"][analyst_name] = {
                    "client_id": self.app_id,
                    "client_secret": self.app_secret,
                    "access_token": account_data["access_token"],
                    "api_base_url": self.instance_url,
                    "username": account_data["username"],
                    "display_name": account_data["display_name"]
                }
            
            with open("jixia_academy_mastodon_config.json", "w", encoding="utf-8") as f:
                json.dump(config, f, ensure_ascii=False, indent=2)
            
            logger.info("💾 配置已保存到 jixia_academy_mastodon_config.json")


def main():
    """主函数 - 奥本海默时刻"""
    print("🚀 洛斯阿拉莫斯核弹发射器")
    print("💥 'Now I am become Death, destroyer of markets' - 奥本海默")
    print("🏛️ 目标：稷下学宫AI分析师军团部署")
    print("=" * 60)
    
    try:
        launcher = NuclearLaunch()
        
        print(f"🎯 目标实例: {launcher.instance_url}")
        print(f"🔑 应用ID: {launcher.app_id}")
        print(f"⚛️ 准备部署 {len(launcher.ai_analysts)} 个AI分析师")
        
        # 确认发射
        confirm = input("\n🚨 确认发射核武器？(y/N): ").strip().lower()
        if confirm not in ['y', 'yes']:
            print("🛑 核弹发射已取消")
            return
        
        print("\n💥 核弹发射倒计时...")
        for i in range(3, 0, -1):
            print(f"⏰ {i}...")
            time.sleep(1)
        print("🚀 LAUNCH!")
        
        # 发射核弹
        success = launcher.launch_nuclear_strike()
        
        if success:
            print("\n🎉 洛斯阿拉莫斯计划圆满成功！")
            print("🧬 AI分析师核裂变反应已开始...")
            print("🌐 长毛象宇宙即将见证金融分析的革命！")
        else:
            print("\n💥 任务失败！需要重新评估策略")
            
    except Exception as e:
        print(f"❌ 核弹发射失败: {e}")


if __name__ == "__main__":
    main()
