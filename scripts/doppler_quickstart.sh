#!/bin/bash
# Cauldron项目Doppler快速开始脚本
# 一键设置Doppler密钥管理

set -e

echo "🔐 Cauldron项目Doppler快速设置"
echo "=================================="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 项目配置
PROJECT_NAME="cauldron"
ENVIRONMENTS=("development" "staging" "production")

# 检查Doppler CLI
check_doppler_cli() {
    echo -e "${BLUE}📋 检查Doppler CLI...${NC}"
    
    if ! command -v doppler &> /dev/null; then
        echo -e "${RED}❌ Doppler CLI未安装${NC}"
        echo -e "${YELLOW}正在安装Doppler CLI...${NC}"
        
        if [[ "$OSTYPE" == "darwin"* ]]; then
            # macOS
            if command -v brew &> /dev/null; then
                brew install dopplerhq/cli/doppler
            else
                curl -Ls https://cli.doppler.com/install.sh | sh
            fi
        else
            # Linux/WSL
            curl -Ls https://cli.doppler.com/install.sh | sh
        fi
        
        if ! command -v doppler &> /dev/null; then
            echo -e "${RED}❌ Doppler CLI安装失败${NC}"
            exit 1
        fi
    fi
    
    echo -e "${GREEN}✅ Doppler CLI已安装: $(doppler --version)${NC}"
}

# 登录Doppler
login_doppler() {
    echo -e "${BLUE}🔑 检查Doppler登录状态...${NC}"
    
    if ! doppler configure get > /dev/null 2>&1; then
        echo -e "${YELLOW}需要登录Doppler...${NC}"
        echo "请在浏览器中完成登录，然后返回终端"
        doppler login
    else
        echo -e "${GREEN}✅ 已登录Doppler${NC}"
    fi
}

# 创建项目
create_project() {
    echo -e "${BLUE}🏗️ 创建Doppler项目...${NC}"
    
    # 检查项目是否已存在
    if doppler projects list --json | jq -r '.[].name' | grep -q "^${PROJECT_NAME}$"; then
        echo -e "${GREEN}✅ 项目 '${PROJECT_NAME}' 已存在${NC}"
    else
        echo -e "${YELLOW}创建新项目: ${PROJECT_NAME}${NC}"
        doppler projects create "$PROJECT_NAME"
        echo -e "${GREEN}✅ 项目创建成功${NC}"
    fi
}

# 设置环境
setup_environments() {
    echo -e "${BLUE}🌍 设置环境配置...${NC}"
    
    for env in "${ENVIRONMENTS[@]}"; do
        echo -e "${YELLOW}配置环境: ${env}${NC}"
        
        # 设置项目和环境
        doppler configure set project="$PROJECT_NAME" config="$env" --scope /Users/<USER>/cauldron
        
        echo -e "${GREEN}✅ 环境 '${env}' 配置完成${NC}"
    done
}

# 运行Python迁移脚本
run_migration() {
    echo -e "${BLUE}📦 运行配置迁移...${NC}"
    
    if [[ -f "scripts/migrate_to_doppler.py" ]]; then
        echo -e "${YELLOW}启动Python迁移脚本...${NC}"
        python3 scripts/migrate_to_doppler.py
    else
        echo -e "${RED}❌ 迁移脚本不存在: scripts/migrate_to_doppler.py${NC}"
        echo -e "${YELLOW}请手动运行迁移或检查文件路径${NC}"
    fi
}

# 验证设置
verify_setup() {
    echo -e "${BLUE}🔍 验证Doppler设置...${NC}"
    
    # 检查当前配置
    echo -e "${YELLOW}当前Doppler配置:${NC}"
    doppler configure get
    
    # 测试密钥访问
    echo -e "${YELLOW}测试密钥访问...${NC}"
    if doppler secrets list > /dev/null 2>&1; then
        secret_count=$(doppler secrets list --json | jq length)
        echo -e "${GREEN}✅ 可以访问 ${secret_count} 个密钥${NC}"
    else
        echo -e "${RED}❌ 无法访问密钥${NC}"
        return 1
    fi
    
    # 测试运行
    echo -e "${YELLOW}测试Doppler运行...${NC}"
    if doppler run -- echo "Doppler测试成功" > /dev/null 2>&1; then
        echo -e "${GREEN}✅ Doppler运行测试通过${NC}"
    else
        echo -e "${RED}❌ Doppler运行测试失败${NC}"
        return 1
    fi
}

# 显示使用指南
show_usage_guide() {
    echo -e "${GREEN}🎉 Doppler设置完成！${NC}"
    echo ""
    echo -e "${BLUE}📖 使用指南:${NC}"
    echo ""
    echo -e "${YELLOW}1. 查看所有密钥:${NC}"
    echo "   doppler secrets list"
    echo ""
    echo -e "${YELLOW}2. 运行应用:${NC}"
    echo "   doppler run -- streamlit run app/streamlit_app.py"
    echo "   doppler run -- python scripts/start_jixia_academy.py"
    echo ""
    echo -e "${YELLOW}3. 切换环境:${NC}"
    echo "   doppler configure set config=staging"
    echo "   doppler configure set config=production"
    echo ""
    echo -e "${YELLOW}4. 添加新密钥:${NC}"
    echo "   doppler secrets set NEW_API_KEY=your_value"
    echo ""
    echo -e "${YELLOW}5. 下载配置为.env格式:${NC}"
    echo "   doppler secrets download --no-file --format env > .env.doppler"
    echo ""
    echo -e "${BLUE}📚 完整文档: docs/doppler_integration_guide.md${NC}"
}

# 错误处理
handle_error() {
    echo -e "${RED}❌ 设置过程中出现错误${NC}"
    echo -e "${YELLOW}请检查错误信息并重试，或查看文档获取帮助${NC}"
    exit 1
}

# 主函数
main() {
    # 设置错误处理
    trap handle_error ERR
    
    echo -e "${BLUE}开始Cauldron项目Doppler设置...${NC}"
    echo ""
    
    # 检查是否在正确的目录
    if [[ ! -f ".env" ]]; then
        echo -e "${RED}❌ 未找到.env文件，请确保在Cauldron项目根目录运行此脚本${NC}"
        exit 1
    fi
    
    # 执行设置步骤
    check_doppler_cli
    echo ""
    
    login_doppler
    echo ""
    
    create_project
    echo ""
    
    setup_environments
    echo ""
    
    # 询问是否运行迁移
    echo -e "${YELLOW}是否运行配置迁移? (y/N):${NC}"
    read -r response
    if [[ "$response" =~ ^[Yy]$ ]]; then
        run_migration
        echo ""
    fi
    
    verify_setup
    echo ""
    
    show_usage_guide
}

# 检查依赖
check_dependencies() {
    # 检查jq
    if ! command -v jq &> /dev/null; then
        echo -e "${YELLOW}⚠️ 建议安装jq以获得更好的体验:${NC}"
        if [[ "$OSTYPE" == "darwin"* ]]; then
            echo "   brew install jq"
        else
            echo "   sudo apt-get install jq"
        fi
        echo ""
    fi
}

# 脚本入口
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    check_dependencies
    main "$@"
fi
