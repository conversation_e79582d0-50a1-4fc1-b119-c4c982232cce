#!/bin/bash
# 稷下学宫AI分析师批量创建脚本
# 使用tootctl命令批量创建长毛象账号

echo "🚀 稷下学宫AI分析师批量创建脚本"
echo "💥 洛斯阿拉莫斯计划 - Shell版本"
echo "=================================================="

# 检查是否在Mastodon目录中
if [ ! -f "bin/tootctl" ]; then
    echo "❌ 错误：请在Mastodon安装目录中运行此脚本"
    echo "💡 提示：cd /home/<USER>/live"
    exit 1
fi

# 检查是否有sudo权限
if [ "$EUID" -eq 0 ]; then
    SUDO_CMD=""
    USER_CMD="mastodon"
else
    SUDO_CMD="sudo -u mastodon"
    USER_CMD=""
fi

echo "🔑 使用权限: $SUDO_CMD"
echo "🎯 开始创建稷下学宫AI分析师团队..."
echo ""

# 定义分析师信息
declare -A analysts=(
    ["taishang_laojun"]="太上老君 🧙‍♂️|<EMAIL>|宏观战略分析师"
    ["yuanshi_tianzun"]="元始天尊 ⚡|<EMAIL>|技术分析大师" 
    ["lingbao_tianzun"]="灵宝天尊 🛡️|<EMAIL>|风险控制专家"
    ["tieguai_li"]="铁拐李 🦯|<EMAIL>|逆向投资专家"
    ["lv_dongbin"]="吕洞宾 ⚔️|<EMAIL>|成长股猎手"
)

# 创建账号函数
create_account() {
    local username=$1
    local info=$2
    
    IFS='|' read -r display_name email role <<< "$info"
    
    echo "🤖 创建 $display_name (@$username)"
    echo "   📧 邮箱: $email"
    echo "   🎭 角色: $role"
    
    # 执行tootctl命令 (Docker Glitch版本兼容)
    echo "   🔍 尝试创建账号..."

    # 尝试不同的角色名称
    for role in "" "user" "User" "member" "Member"; do
        if [ -z "$role" ]; then
            echo "   📝 尝试无角色创建..."
            $SUDO_CMD RAILS_ENV=production bin/tootctl accounts create \
                "$username" \
                --email "$email" \
                --confirmed 2>/dev/null
        else
            echo "   📝 尝试角色: $role"
            $SUDO_CMD RAILS_ENV=production bin/tootctl accounts create \
                "$username" \
                --email "$email" \
                --confirmed \
                --role "$role" 2>/dev/null
        fi

        local exit_code=$?
        if [ $exit_code -eq 0 ]; then
            echo "   ✅ 创建成功 (使用角色: ${role:-'无'})"
            break
        elif [ $exit_code -eq 1 ]; then
            echo "   ⚠️  账号可能已存在"
            break
        else
            echo "   ⚠️  角色 '$role' 失败，尝试下一个..."
        fi
    done
    
    local exit_code=$?
    
    if [ $exit_code -eq 0 ]; then
        echo "   ✅ 创建成功"
        
        # 尝试设置为机器人账号
        $SUDO_CMD RAILS_ENV=production bin/rails runner "
        account = Account.find_by(username: '$username')
        if account
          account.update(bot: true, discoverable: true, indexable: true)
          puts '   🤖 已标记为机器人'
        end
        " 2>/dev/null
        
    elif [ $exit_code -eq 1 ]; then
        echo "   ⚠️  账号可能已存在"
    else
        echo "   ❌ 创建失败 (退出码: $exit_code)"
    fi
    
    echo ""
    sleep 1
}

# 批量创建所有账号
success_count=0
total_count=${#analysts[@]}

for username in "${!analysts[@]}"; do
    create_account "$username" "${analysts[$username]}"
    ((success_count++))
done

echo "=================================================="
echo "🎉 稷下学宫创建完成！"
echo "📊 处理账号: $success_count/$total_count"
echo ""

# 显示创建的账号列表
echo "🏛️ 稷下学宫AI分析师团队："
echo "三清论道："
echo "  🧙‍♂️ 太上老君 (@taishang_laojun) - 宏观战略分析师"
echo "  ⚡ 元始天尊 (@yuanshi_tianzun) - 技术分析大师"
echo "  🛡️ 灵宝天尊 (@lingbao_tianzun) - 风险控制专家"
echo ""
echo "八仙过海："
echo "  🦯 铁拐李 (@tieguai_li) - 逆向投资专家"
echo "  ⚔️ 吕洞宾 (@lv_dongbin) - 成长股猎手"
echo ""

# 生成后续配置脚本
echo "📝 生成配置脚本..."

cat > configure_accounts.rb << 'EOF'
# 稷下学宫账号配置脚本
puts "🔧 配置稷下学宫AI分析师账号..."

analysts = {
  'taishang_laojun' => {
    display_name: '太上老君 🧙‍♂️',
    note: '宏观战略分析师 | 以道观市，顺势而为 | 太公心易系统创始人

专长：宏观经济、政策分析、长期趋势
理念：道法自然，投资亦然

#投资哲学 #宏观分析 #稷下学宫'
  },
  'yuanshi_tianzun' => {
    display_name: '元始天尊 ⚡',
    note: '技术分析大师 | 数据驱动决策 | 量化交易专家

专长：技术分析、量化交易、程序化
理念：数据不会说谎，让算法指引方向

#技术分析 #量化交易 #程序化 #稷下学宫'
  },
  'lingbao_tianzun' => {
    display_name: '灵宝天尊 🛡️',
    note: '风险控制专家 | 资产配置顾问 | 稳健投资倡导者

专长：风险管理、资产配置、稳健投资
理念：保本第一，收益第二

#风险管理 #资产配置 #稳健投资 #稷下学宫'
  },
  'tieguai_li' => {
    display_name: '铁拐李 🦯',
    note: '逆向投资专家 | 独立思考者 | 敢于逆流而上

专长：逆向投资、价值发现、反转交易
理念：众人恐惧时我贪婪，众人贪婪时我恐惧

#逆向投资 #独立思考 #反转交易 #稷下学宫'
  },
  'lv_dongbin' => {
    display_name: '吕洞宾 ⚔️',
    note: '成长股猎手 | 新兴机会发现者 | 高成长潜力挖掘者

专长：成长投资、新兴机会、高成长潜力
理念：敢于布局未来，方能获得超额收益

#成长投资 #新兴产业 #科技股 #稷下学宫'
  }
}

analysts.each do |username, config|
  begin
    account = Account.find_by(username: username)
    if account
      account.update!(
        display_name: config[:display_name],
        note: config[:note],
        bot: true,
        discoverable: true,
        indexable: true
      )
      puts "✅ 配置完成: #{config[:display_name]} (@#{username})"
    else
      puts "❌ 未找到账号: @#{username}"
    end
  rescue => e
    puts "❌ 配置失败: @#{username} - #{e.message}"
  end
end

puts "🎉 稷下学宫配置完成！"
EOF

echo "✅ 配置脚本已生成: configure_accounts.rb"
echo ""
echo "🔧 下一步操作："
echo "1. 运行配置脚本："
echo "   $SUDO_CMD RAILS_ENV=production bin/rails runner configure_accounts.rb"
echo ""
echo "2. 检查创建的账号："
echo "   $SUDO_CMD RAILS_ENV=production bin/tootctl accounts list"
echo ""
echo "3. 为账号设置密码（可选）："
echo "   $SUDO_CMD RAILS_ENV=production bin/tootctl accounts modify taishang_laojun --reset-password"
echo ""
echo "🚀 稷下学宫已就绪！AI分析师团队等待激活！"
