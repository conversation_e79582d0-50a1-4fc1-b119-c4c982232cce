#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Rovodev自动化包装器 - 解决权限提示问题
通过预设回答和输入流控制来实现无人值守运行
"""

import subprocess
import threading
import time
import queue
import signal
import sys
from pathlib import Path

class RovodevAutomationWrapper:
    """Rovodev自动化包装器"""
    
    def __init__(self, config_file=None):
        self.config_file = config_file or "config/rovodev_automation.yml"
        self.process = None
        self.input_queue = queue.Queue()
        self.output_queue = queue.Queue()
        self.running = False
        
        # 预设的自动回答
        self.auto_responses = {
            "Do you want to proceed": "y\n",
            "Continue?": "y\n", 
            "Allow this operation": "y\n",
            "Confirm": "y\n",
            "Are you sure": "y\n",
            "Execute this command": "y\n",
            "Apply changes": "y\n",
            "Save file": "y\n",
            "Overwrite": "y\n",
            "Create file": "y\n",
            "Delete": "n\n",  # 删除操作默认拒绝
            "Remove": "n\n",   # 移除操作默认拒绝
        }
    
    def start_rovodev(self, prompt: str, timeout: int = 300):
        """启动Rovodev进程"""
        try:
            # 构建命令
            cmd = [
                "acli", "rovodev", "run",
                "--shadow",  # 使用shadow模式
                "--config-file", self.config_file,
                prompt
            ]
            
            # 启动进程
            self.process = subprocess.Popen(
                cmd,
                stdin=subprocess.PIPE,
                stdout=subprocess.PIPE, 
                stderr=subprocess.PIPE,
                text=True,
                bufsize=1,
                universal_newlines=True,
                cwd="/Users/<USER>/cauldron"
            )
            
            self.running = True
            
            # 启动输入输出处理线程
            input_thread = threading.Thread(target=self._handle_input)
            output_thread = threading.Thread(target=self._handle_output)
            
            input_thread.daemon = True
            output_thread.daemon = True
            
            input_thread.start()
            output_thread.start()
            
            # 发送初始提示
            self._send_input(prompt + "\n")
            
            # 等待完成或超时
            try:
                self.process.wait(timeout=timeout)
            except subprocess.TimeoutExpired:
                print("⏰ Rovodev执行超时")
                self._terminate()
                return False, "执行超时"
            
            # 收集输出
            stdout, stderr = self.process.communicate()
            
            if self.process.returncode == 0:
                return True, stdout
            else:
                return False, stderr
                
        except Exception as e:
            print(f"💥 Rovodev执行异常: {e}")
            return False, str(e)
        finally:
            self.running = False
    
    def _handle_input(self):
        """处理输入流 - 自动回答权限提示"""
        while self.running and self.process and self.process.poll() is None:
            try:
                # 检查是否有需要发送的输入
                if not self.input_queue.empty():
                    input_text = self.input_queue.get_nowait()
                    if self.process.stdin:
                        self.process.stdin.write(input_text)
                        self.process.stdin.flush()
                
                time.sleep(0.1)
            except Exception as e:
                print(f"输入处理错误: {e}")
                break
    
    def _handle_output(self):
        """处理输出流 - 检测权限提示并自动回答"""
        while self.running and self.process and self.process.poll() is None:
            try:
                if self.process.stdout:
                    line = self.process.stdout.readline()
                    if line:
                        print(f"Rovodev: {line.strip()}")
                        
                        # 检查是否是权限提示
                        for prompt, response in self.auto_responses.items():
                            if prompt.lower() in line.lower():
                                print(f"🤖 自动回答: {prompt} -> {response.strip()}")
                                self._send_input(response)
                                break
                        
                        self.output_queue.put(line)
                
                time.sleep(0.1)
            except Exception as e:
                print(f"输出处理错误: {e}")
                break
    
    def _send_input(self, text: str):
        """发送输入到进程"""
        self.input_queue.put(text)
    
    def _terminate(self):
        """终止进程"""
        if self.process:
            try:
                self.process.terminate()
                self.process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                self.process.kill()
            except Exception as e:
                print(f"终止进程时出错: {e}")
        
        self.running = False

def run_rovodev_task(prompt: str, timeout: int = 300) -> tuple[bool, str]:
    """运行Rovodev任务的便捷函数"""
    wrapper = RovodevAutomationWrapper()
    return wrapper.start_rovodev(prompt, timeout)

# 测试函数
def test_wrapper():
    """测试包装器"""
    print("🧪 测试Rovodev自动化包装器...")
    
    success, result = run_rovodev_task(
        "分析当前项目结构，列出主要的Python文件",
        timeout=60
    )
    
    if success:
        print("✅ 测试成功!")
        print(f"结果: {result[:200]}...")
    else:
        print("❌ 测试失败!")
        print(f"错误: {result}")

if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] == "test":
        test_wrapper()
    else:
        print("Rovodev自动化包装器")
        print("使用方法: python rovodev_automation_wrapper.py test")
