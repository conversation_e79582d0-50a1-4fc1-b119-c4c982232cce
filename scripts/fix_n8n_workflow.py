#!/usr/bin/env python3
"""
直接修复N8N工作流中的Milvus Vector Store问题
通过N8N API直接操作工作流，解决"title"字段问题
"""

import requests
import json
import os
from typing import Dict, Any, List, Optional

class N8NWorkflowFixer:
    """N8N工作流修复器"""
    
    def __init__(self):
        # 从配置中获取N8N连接信息
        self.base_url = "https://n8n.git4ta.fun"
        self.api_key = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJhNzYwZjUxMy0zMWMzLTQwYzMtOTQ0Zi0xZDkyNGQ4ZjM3Y2QiLCJpc3MiOiJuOG4iLCJhdWQiOiJwdWJsaWMtYXBpIiwiaWF0IjoxNzUxNTU0NjY4fQ.YO11K1xJQQ9lmo-VYCdg8Vf0jyvQ5ufoLF-sWJLqE08"
        
        self.headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
        
        print("🔧 N8N工作流修复器初始化")
        print(f"🌐 N8N地址: {self.base_url}")
        print("🎭 准备修复'i服了you'的问题...")
    
    def get_workflows(self) -> List[Dict[str, Any]]:
        """获取所有工作流"""
        try:
            url = f"{self.base_url}/api/v1/workflows"
            response = requests.get(url, headers=self.headers, timeout=10)
            
            if response.status_code == 200:
                workflows = response.json()
                print(f"✅ 获取到 {len(workflows)} 个工作流")
                return workflows
            else:
                print(f"❌ 获取工作流失败: {response.status_code}")
                return []
                
        except Exception as e:
            print(f"❌ 获取工作流异常: {e}")
            return []
    
    def find_rss_workflow(self, workflows: List[Dict[str, Any]]) -> Optional[Dict[str, Any]]:
        """查找RSS相关的工作流"""
        for workflow in workflows:
            name = workflow.get('name', '').lower()
            if any(keyword in name for keyword in ['rss', 'zilliz', '悬丝诊脉', 'daily']):
                print(f"🎯 找到目标工作流: {workflow.get('name')}")
                return workflow
        
        print("❌ 未找到RSS相关工作流")
        return None
    
    def get_workflow_details(self, workflow_id: str) -> Optional[Dict[str, Any]]:
        """获取工作流详细信息"""
        try:
            url = f"{self.base_url}/api/v1/workflows/{workflow_id}"
            response = requests.get(url, headers=self.headers, timeout=10)
            
            if response.status_code == 200:
                workflow = response.json()
                print(f"✅ 获取工作流详情成功")
                return workflow
            else:
                print(f"❌ 获取工作流详情失败: {response.status_code}")
                return None
                
        except Exception as e:
            print(f"❌ 获取工作流详情异常: {e}")
            return None
    
    def fix_milvus_node(self, workflow: Dict[str, Any]) -> Dict[str, Any]:
        """修复Milvus Vector Store节点"""
        print("🔨 开始修复Milvus Vector Store节点...")
        
        nodes = workflow.get('nodes', [])
        fixed = False
        
        for node in nodes:
            node_type = node.get('type', '')
            node_name = node.get('name', '')
            
            # 查找Milvus Vector Store节点
            if 'milvus' in node_type.lower() or 'vector' in node_name.lower():
                print(f"🎯 找到Milvus节点: {node_name}")
                
                # 修复节点参数
                if 'parameters' not in node:
                    node['parameters'] = {}
                
                # 设置正确的collection名称
                node['parameters']['collectionName'] = 'ifuleyou'
                
                print(f"✅ 已设置Collection名称为: ifuleyou")
                fixed = True
        
        # 查找Code节点并修复数据格式
        for node in nodes:
            node_type = node.get('type', '')
            node_name = node.get('name', '')
            
            if 'code' in node_type.lower() and any(keyword in node_name.lower() for keyword in ['format', '格式', 'process', '处理']):
                print(f"🎯 找到数据处理节点: {node_name}")
                
                # 修复代码
                fixed_code = self._get_fixed_code()
                
                if 'parameters' not in node:
                    node['parameters'] = {}
                
                node['parameters']['jsCode'] = fixed_code
                print("✅ 已修复数据处理代码")
                fixed = True
        
        if fixed:
            print("🎉 Milvus节点修复完成")
        else:
            print("⚠️ 未找到需要修复的Milvus节点")
        
        return workflow
    
    def _get_fixed_code(self) -> str:
        """获取修复后的JavaScript代码"""
        return '''
// 修复后的代码 - 解决"title"字段问题
const processedItems = [];
const items = $input.all();

console.log("输入数据数量:", items.length);

for (const item of items) {
    // 确保每个item都有基本的数据结构
    if (item && item.json) {
        
        // 确保title存在，如果没有就用默认值
        const title = item.json.title && item.json.title.trim() !== '' 
            ? String(item.json.title) 
            : "无标题文章";
            
        const publishedDate = item.json.published_time 
            ? new Date(item.json.published_time) 
            : new Date();
        const simpleTime = publishedDate.toISOString();

        // 确保所有必需字段都存在
        const safeMetadata = {
            title: title,  // 这个字段是必需的！
            published_date: String(simpleTime),
            article_id: String(item.json.article_id || `article_${Date.now()}_${Math.random()}`)
        };

        const formattedItem = {
            pageContent: String(item.json.content || item.json.title || "空内容"),
            metadata: safeMetadata
        };
        
        console.log(`处理文档 ${processedItems.length + 1}:`, {
            title: safeMetadata.title,
            contentLength: formattedItem.pageContent.length
        });
        
        // 直接推送文档，不包装在json中
        processedItems.push(formattedItem);
    }
}

// 确保至少返回一个有效文档
if (processedItems.length === 0) {
    console.log("没有有效数据，返回默认文档");
    return [{
        pageContent: "默认测试内容",
        metadata: {
            title: "默认标题",
            published_date: new Date().toISOString(),
            article_id: "default_article"
        }
    }];
}

console.log("最终返回文档数量:", processedItems.length);
return processedItems;
'''
    
    def update_workflow(self, workflow: Dict[str, Any]) -> bool:
        """更新工作流"""
        try:
            workflow_id = workflow.get('id')
            url = f"{self.base_url}/api/v1/workflows/{workflow_id}"
            
            response = requests.put(url, headers=self.headers, json=workflow, timeout=30)
            
            if response.status_code == 200:
                print("✅ 工作流更新成功")
                return True
            else:
                print(f"❌ 工作流更新失败: {response.status_code}")
                print(f"响应: {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ 工作流更新异常: {e}")
            return False
    
    def run_fix(self) -> bool:
        """执行修复流程"""
        print("🚀 开始修复N8N工作流...")
        print("=" * 50)
        
        # 1. 获取工作流列表
        workflows = self.get_workflows()
        if not workflows:
            return False
        
        # 2. 查找RSS工作流
        target_workflow = self.find_rss_workflow(workflows)
        if not target_workflow:
            return False
        
        # 3. 获取工作流详情
        workflow_id = target_workflow.get('id')
        workflow_details = self.get_workflow_details(workflow_id)
        if not workflow_details:
            return False
        
        # 4. 修复Milvus节点
        fixed_workflow = self.fix_milvus_node(workflow_details)
        
        # 5. 更新工作流
        success = self.update_workflow(fixed_workflow)
        
        print("=" * 50)
        if success:
            print("🎉 N8N工作流修复完成！")
            print("💡 现在应该不会再报'title'字段错误了")
            print("🎭 'i服了you'的问题解决了！")
        else:
            print("❌ N8N工作流修复失败")
        
        return success

def main():
    """主函数"""
    fixer = N8NWorkflowFixer()
    success = fixer.run_fix()
    
    if success:
        print("\n✅ 修复成功，可以测试N8N工作流了")
    else:
        print("\n❌ 修复失败，需要手动检查")

if __name__ == "__main__":
    main()
