{"name": "cauldron-mcp-tools", "version": "1.0.0", "description": "炼妖壶金融分析MCP工具集", "author": "Cauldron Team", "license": "MIT", "homepage": "https://github.com/your-username/cauldron", "repository": {"type": "git", "url": "https://github.com/your-username/cauldron.git"}, "server": {"command": "python", "args": ["-m", "src.mcp_server"], "env": {"CAULDRON_API_URL": "https://cauldron.herokuapp.com", "CAULDRON_API_KEY": "${CAULDRON_API_KEY}"}}, "tools": [{"name": "market_sentiment", "description": "分析当前市场情绪和趋势，基于RSS、社交媒体和新闻数据", "inputSchema": {"type": "object", "properties": {"source": {"type": "string", "enum": ["all", "rss", "social", "news"], "default": "all", "description": "数据源选择"}, "timeframe": {"type": "string", "enum": ["1h", "4h", "1d", "1w"], "default": "1d", "description": "分析时间范围"}, "symbols": {"type": "array", "items": {"type": "string"}, "description": "特定股票代码列表（可选）"}}}}, {"name": "trading_signal", "description": "基于技术分析和基本面分析生成交易信号", "inputSchema": {"type": "object", "properties": {"symbol": {"type": "string", "description": "股票代码（必填）"}, "strategy": {"type": "string", "enum": ["momentum", "mean_reversion", "breakout", "default"], "default": "default", "description": "交易策略类型"}, "timeframe": {"type": "string", "enum": ["5m", "15m", "1h", "4h", "1d"], "default": "1d", "description": "分析时间周期"}, "risk_level": {"type": "integer", "minimum": 1, "maximum": 5, "default": 3, "description": "风险等级 (1=保守, 5=激进)"}}, "required": ["symbol"]}}, {"name": "portfolio_analysis", "description": "分析投资组合的风险收益特征和优化建议", "inputSchema": {"type": "object", "properties": {"holdings": {"type": "array", "items": {"type": "object", "properties": {"symbol": {"type": "string"}, "weight": {"type": "number"}, "shares": {"type": "number"}}, "required": ["symbol", "weight"]}, "description": "持仓列表"}, "benchmark": {"type": "string", "default": "000300.SH", "description": "基准指数代码"}, "analysis_period": {"type": "string", "enum": ["1m", "3m", "6m", "1y", "2y"], "default": "1y", "description": "分析周期"}}, "required": ["holdings"]}}, {"name": "risk_assessment", "description": "评估单个股票或投资组合的风险水平", "inputSchema": {"type": "object", "properties": {"symbol": {"type": "string", "description": "股票代码（必填）"}, "position_size": {"type": "number", "minimum": 0, "maximum": 1, "default": 0.1, "description": "仓位大小（占总资产比例）"}, "time_horizon": {"type": "string", "enum": ["short", "medium", "long"], "default": "medium", "description": "投资期限"}, "risk_tolerance": {"type": "string", "enum": ["conservative", "moderate", "aggressive"], "default": "moderate", "description": "风险承受能力"}}, "required": ["symbol"]}}, {"name": "news_impact", "description": "分析新闻事件对市场和特定股票的影响", "inputSchema": {"type": "object", "properties": {"news_text": {"type": "string", "description": "新闻内容文本（必填）"}, "symbols": {"type": "array", "items": {"type": "string"}, "description": "相关股票代码列表（可选）"}, "analysis_depth": {"type": "string", "enum": ["basic", "detailed", "comprehensive"], "default": "detailed", "description": "分析深度"}}, "required": ["news_text"]}}], "capabilities": {"market_analysis": {"sentiment_analysis": true, "trend_detection": true, "volatility_analysis": true}, "trading_support": {"signal_generation": true, "risk_management": true, "portfolio_optimization": true}, "data_sources": {"rss_feeds": true, "social_media": true, "financial_news": true, "market_data": true}}, "pricing": {"free_tier": {"daily_calls": 100, "tools": ["market_sentiment", "news_impact"]}, "premium_tier": {"daily_calls": 1000, "tools": ["market_sentiment", "trading_signal", "news_impact", "risk_assessment"]}, "vip_tier": {"daily_calls": "unlimited", "tools": "all", "custom_tools": true}}, "contact": {"support": "<EMAIL>", "documentation": "https://docs.cauldron.ai/mcp-tools", "github_issues": "https://github.com/your-username/cauldron/issues"}}