{"name": "cauldron-financial-tools", "version": "1.0.0", "description": "炼妖壶金融分析工具集", "server": {"command": "python", "args": ["-m", "src.mcp_server"], "env": {"CAULDRON_API_URL": "http://localhost:8000", "CAULDRON_API_KEY": "${CAULDRON_API_KEY}"}}, "tools": [{"name": "market_sentiment", "description": "分析当前市场情绪和趋势", "endpoint": "http://localhost:8000/api/mcp/tools/market_sentiment/call", "method": "POST", "schema": {"type": "object", "properties": {"source": {"type": "string", "enum": ["all", "rss", "social", "news"], "default": "all"}, "timeframe": {"type": "string", "enum": ["1h", "4h", "1d", "1w"], "default": "1d"}, "symbols": {"type": "array", "items": {"type": "string"}}}}}, {"name": "trading_signal", "description": "生成交易信号和建议", "endpoint": "http://localhost:8000/api/mcp/tools/trading_signal/call", "method": "POST", "schema": {"type": "object", "properties": {"symbol": {"type": "string"}, "strategy": {"type": "string", "enum": ["momentum", "mean_reversion", "breakout", "default"], "default": "default"}, "timeframe": {"type": "string", "enum": ["5m", "15m", "1h", "4h", "1d"], "default": "1d"}, "risk_level": {"type": "integer", "minimum": 1, "maximum": 5, "default": 3}}, "required": ["symbol"]}}, {"name": "portfolio_analysis", "description": "分析投资组合风险和收益", "endpoint": "http://localhost:8000/api/mcp/tools/portfolio_analysis/call", "method": "POST", "schema": {"type": "object", "properties": {"holdings": {"type": "array", "items": {"type": "object", "properties": {"symbol": {"type": "string"}, "weight": {"type": "number"}}}}, "benchmark": {"type": "string", "default": "000300.SH"}}, "required": ["holdings"]}}, {"name": "risk_assessment", "description": "评估投资风险", "endpoint": "http://localhost:8000/api/mcp/tools/risk_assessment/call", "method": "POST", "schema": {"type": "object", "properties": {"symbol": {"type": "string"}, "position_size": {"type": "number", "default": 0.1}, "time_horizon": {"type": "string", "enum": ["short", "medium", "long"], "default": "medium"}}, "required": ["symbol"]}}, {"name": "news_impact", "description": "分析新闻对市场的影响", "endpoint": "http://localhost:8000/api/mcp/tools/news_impact/call", "method": "POST", "schema": {"type": "object", "properties": {"news_text": {"type": "string"}, "symbols": {"type": "array", "items": {"type": "string"}}}, "required": ["news_text"]}}]}