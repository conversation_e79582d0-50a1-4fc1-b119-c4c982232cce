#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
灵宝道君 MCP MongoDB向量检索器
专门用于从MongoDB Atlas检索向量化的RSS内容
"""

import asyncio
import json
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
import numpy as np

import pymongo
from pymongo import MongoClient
import autogen
from autogen import ConversableAgent

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class MongoVectorRetriever:
    """MongoDB向量检索器"""
    
    def __init__(self, mongo_uri: str, database_name: str = "rss_database"):
        """
        初始化MongoDB连接
        
        Args:
            mongo_uri: MongoDB Atlas连接字符串
            database_name: 数据库名称
        """
        self.client = MongoClient(mongo_uri)
        self.db = self.client[database_name]
        self.collection = self.db.rss_vectors  # 假设向量存储在这个集合
        
        logger.info(f"连接到MongoDB: {database_name}")
    
    def get_recent_vectorized_content(self, hours: int = 24, limit: int = 20) -> List[Dict]:
        """
        获取最近的向量化内容
        
        Args:
            hours: 时间范围（小时）
            limit: 返回数量限制
            
        Returns:
            包含向量化内容的列表
        """
        try:
            # 计算时间范围
            start_time = datetime.now() - timedelta(hours=hours)
            
            # 查询条件
            query = {
                "timestamp": {"$gte": start_time},
                "vector": {"$exists": True, "$ne": None}
            }
            
            # 执行查询
            cursor = self.collection.find(query).sort("timestamp", -1).limit(limit)
            
            results = []
            for doc in cursor:
                # 清理和格式化数据
                result = {
                    "id": str(doc.get("_id", "")),
                    "title": doc.get("title", ""),
                    "content": doc.get("content", ""),
                    "source": doc.get("source", "unknown"),
                    "timestamp": doc.get("timestamp", "").isoformat() if isinstance(doc.get("timestamp"), datetime) else str(doc.get("timestamp", "")),
                    "vector_dimension": len(doc.get("vector", [])) if doc.get("vector") else 0,
                    "metadata": doc.get("metadata", {})
                }
                results.append(result)
            
            logger.info(f"检索到 {len(results)} 条向量化内容")
            return results
            
        except Exception as e:
            logger.error(f"MongoDB检索错误: {e}")
            return []
    
    def search_by_keywords(self, keywords: List[str], limit: int = 10) -> List[Dict]:
        """
        根据关键词搜索向量化内容
        
        Args:
            keywords: 搜索关键词列表
            limit: 返回数量限制
            
        Returns:
            匹配的向量化内容列表
        """
        try:
            # 构建搜索模式
            search_pattern = "|".join(keywords)
            
            query = {
                "$and": [
                    {"vector": {"$exists": True, "$ne": None}},
                    {
                        "$or": [
                            {"title": {"$regex": search_pattern, "$options": "i"}},
                            {"content": {"$regex": search_pattern, "$options": "i"}},
                            {"metadata.tags": {"$in": keywords}}
                        ]
                    }
                ]
            }
            
            cursor = self.collection.find(query).sort("timestamp", -1).limit(limit)
            
            results = []
            for doc in cursor:
                result = {
                    "id": str(doc.get("_id", "")),
                    "title": doc.get("title", ""),
                    "content": doc.get("content", "")[:200] + "..." if len(doc.get("content", "")) > 200 else doc.get("content", ""),
                    "source": doc.get("source", "unknown"),
                    "timestamp": doc.get("timestamp", "").isoformat() if isinstance(doc.get("timestamp"), datetime) else str(doc.get("timestamp", "")),
                    "relevance_score": self._calculate_keyword_relevance(doc, keywords),
                    "vector_dimension": len(doc.get("vector", [])) if doc.get("vector") else 0
                }
                results.append(result)
            
            # 按相关性排序
            results.sort(key=lambda x: x["relevance_score"], reverse=True)
            
            logger.info(f"关键词搜索到 {len(results)} 条内容")
            return results
            
        except Exception as e:
            logger.error(f"关键词搜索错误: {e}")
            return []
    
    def get_vector_statistics(self) -> Dict[str, Any]:
        """
        获取向量数据库统计信息
        
        Returns:
            统计信息字典
        """
        try:
            # 总文档数
            total_docs = self.collection.count_documents({})
            
            # 有向量的文档数
            vectorized_docs = self.collection.count_documents({
                "vector": {"$exists": True, "$ne": None}
            })
            
            # 最近24小时的文档数
            recent_time = datetime.now() - timedelta(hours=24)
            recent_docs = self.collection.count_documents({
                "timestamp": {"$gte": recent_time}
            })
            
            # 数据源统计
            source_pipeline = [
                {"$group": {"_id": "$source", "count": {"$sum": 1}}},
                {"$sort": {"count": -1}},
                {"$limit": 10}
            ]
            source_stats = list(self.collection.aggregate(source_pipeline))
            
            # 向量维度统计（如果有向量数据）
            vector_sample = self.collection.find_one({"vector": {"$exists": True, "$ne": None}})
            vector_dimension = len(vector_sample.get("vector", [])) if vector_sample else 0
            
            stats = {
                "total_documents": total_docs,
                "vectorized_documents": vectorized_docs,
                "recent_24h_documents": recent_docs,
                "vectorization_rate": f"{(vectorized_docs/total_docs*100):.1f}%" if total_docs > 0 else "0%",
                "vector_dimension": vector_dimension,
                "top_sources": source_stats,
                "last_updated": datetime.now().isoformat()
            }
            
            return stats
            
        except Exception as e:
            logger.error(f"统计信息获取错误: {e}")
            return {"error": str(e)}
    
    def _calculate_keyword_relevance(self, doc: Dict, keywords: List[str]) -> float:
        """计算关键词相关性得分"""
        try:
            title = doc.get("title", "").lower()
            content = doc.get("content", "").lower()
            
            score = 0.0
            for keyword in keywords:
                keyword_lower = keyword.lower()
                # 标题匹配权重更高
                if keyword_lower in title:
                    score += 2.0
                if keyword_lower in content:
                    score += 1.0
            
            return score
            
        except Exception:
            return 0.0


class LingbaoDaojunMCP:
    """灵宝道君MCP工具"""
    
    def __init__(self, mongo_uri: str):
        """
        初始化灵宝道君MCP
        
        Args:
            mongo_uri: MongoDB Atlas连接字符串
        """
        self.retriever = MongoVectorRetriever(mongo_uri)
        self.setup_agent()
    
    def setup_agent(self):
        """设置灵宝道君智能体"""
        
        # LLM配置
        llm_config = {
            "model": "gpt-4o-mini",
            "api_key": "your-openai-key",  # 需要配置实际的API key
            "temperature": 0.1
        }
        
        # 创建灵宝道君智能体
        self.lingbao_daojun = ConversableAgent(
            name="灵宝道君",
            system_message="""你是灵宝道君，稷下学宫的技术统计专家。

你的职责：
1. 通过MCP工具从MongoDB Atlas检索向量化的RSS数据
2. 分析和验证数据的完整性和质量
3. 提供技术统计报告和数据洞察
4. 确保数据的准确性和可靠性

你的特点：
- 技术导向，数据驱动
- 严谨细致，追求准确
- 善于发现数据中的模式和异常
- 语言简洁专业

可用的MCP工具：
- get_recent_vectors: 获取最近的向量化内容
- search_vectors: 根据关键词搜索向量内容  
- get_vector_stats: 获取向量数据库统计信息
""",
            llm_config=llm_config,
            function_map={
                "get_recent_vectors": self.get_recent_vectors,
                "search_vectors": self.search_vectors,
                "get_vector_stats": self.get_vector_stats
            }
        )
    
    def get_recent_vectors(self, hours: int = 24, limit: int = 20) -> str:
        """
        MCP工具：获取最近的向量化内容
        
        Args:
            hours: 时间范围（小时）
            limit: 返回数量限制
            
        Returns:
            格式化的检索结果
        """
        try:
            results = self.retriever.get_recent_vectorized_content(hours, limit)
            
            if not results:
                return f"未找到最近{hours}小时内的向量化内容"
            
            # 格式化输出
            output = f"📊 最近{hours}小时向量化内容 (共{len(results)}条):\n\n"
            
            for i, item in enumerate(results[:10], 1):  # 只显示前10条
                output += f"{i}. 【{item['source']}】{item['title']}\n"
                output += f"   时间: {item['timestamp']}\n"
                output += f"   向量维度: {item['vector_dimension']}\n"
                if item['content']:
                    content_preview = item['content'][:100] + "..." if len(item['content']) > 100 else item['content']
                    output += f"   内容: {content_preview}\n"
                output += "\n"
            
            if len(results) > 10:
                output += f"... 还有{len(results) - 10}条记录\n"
            
            return output
            
        except Exception as e:
            return f"检索失败: {e}"
    
    def search_vectors(self, keywords: str, limit: int = 10) -> str:
        """
        MCP工具：根据关键词搜索向量内容
        
        Args:
            keywords: 搜索关键词（用逗号分隔）
            limit: 返回数量限制
            
        Returns:
            格式化的搜索结果
        """
        try:
            # 解析关键词
            keyword_list = [k.strip() for k in keywords.split(",") if k.strip()]
            
            if not keyword_list:
                return "请提供有效的搜索关键词"
            
            results = self.retriever.search_by_keywords(keyword_list, limit)
            
            if not results:
                return f"未找到包含关键词 '{keywords}' 的向量化内容"
            
            # 格式化输出
            output = f"🔍 关键词搜索结果: '{keywords}' (共{len(results)}条):\n\n"
            
            for i, item in enumerate(results, 1):
                output += f"{i}. 【{item['source']}】{item['title']}\n"
                output += f"   相关性得分: {item['relevance_score']:.1f}\n"
                output += f"   时间: {item['timestamp']}\n"
                output += f"   向量维度: {item['vector_dimension']}\n"
                output += f"   内容: {item['content']}\n\n"
            
            return output
            
        except Exception as e:
            return f"搜索失败: {e}"
    
    def get_vector_stats(self) -> str:
        """
        MCP工具：获取向量数据库统计信息
        
        Returns:
            格式化的统计信息
        """
        try:
            stats = self.retriever.get_vector_statistics()
            
            if "error" in stats:
                return f"统计信息获取失败: {stats['error']}"
            
            # 格式化输出
            output = "📈 MongoDB向量数据库统计报告:\n\n"
            output += f"📄 总文档数: {stats['total_documents']:,}\n"
            output += f"🔢 向量化文档数: {stats['vectorized_documents']:,}\n"
            output += f"📊 向量化率: {stats['vectorization_rate']}\n"
            output += f"🕐 最近24小时新增: {stats['recent_24h_documents']:,}\n"
            output += f"📐 向量维度: {stats['vector_dimension']}\n"
            output += f"🕒 最后更新: {stats['last_updated']}\n\n"
            
            if stats['top_sources']:
                output += "📰 主要数据源:\n"
                for source in stats['top_sources']:
                    output += f"  • {source['_id']}: {source['count']:,} 条\n"
            
            return output
            
        except Exception as e:
            return f"统计信息获取失败: {e}"
    
    async def demonstrate_mcp_capabilities(self) -> str:
        """演示MCP能力"""
        try:
            print("🚀 灵宝道君MCP演示开始...\n")
            
            # 演示1: 获取统计信息
            print("📊 演示1: 获取向量数据库统计信息")
            stats_result = self.lingbao_daojun.generate_reply(
                messages=[{"role": "user", "content": "请获取向量数据库的统计信息"}]
            )
            print(f"灵宝道君: {stats_result}\n")
            
            # 演示2: 获取最近内容
            print("📋 演示2: 获取最近向量化内容")
            recent_result = self.lingbao_daojun.generate_reply(
                messages=[{"role": "user", "content": "请获取最近24小时的向量化内容"}]
            )
            print(f"灵宝道君: {recent_result}\n")
            
            # 演示3: 关键词搜索
            print("🔍 演示3: 关键词搜索")
            search_result = self.lingbao_daojun.generate_reply(
                messages=[{"role": "user", "content": "请搜索包含'股市,投资'关键词的向量内容"}]
            )
            print(f"灵宝道君: {search_result}\n")
            
            return "✅ MCP演示完成，所有功能正常工作"
            
        except Exception as e:
            return f"❌ MCP演示失败: {e}"


# 使用示例
async def main():
    """主函数演示"""
    
    # MongoDB Atlas连接字符串（需要替换为实际的连接字符串）
    mongo_uri = "mongodb+srv://username:<EMAIL>/database?retryWrites=true&w=majority"
    
    # 创建灵宝道君MCP
    lingbao_mcp = LingbaoDaojunMCP(mongo_uri)
    
    # 演示MCP功能
    result = await lingbao_mcp.demonstrate_mcp_capabilities()
    print(result)
    
    # 直接测试检索功能
    print("\n🧪 直接测试检索功能:")
    
    # 测试统计信息
    stats = lingbao_mcp.get_vector_stats()
    print(f"统计信息:\n{stats}")
    
    # 测试最近内容
    recent = lingbao_mcp.get_recent_vectors(hours=48, limit=5)
    print(f"最近内容:\n{recent}")


if __name__ == "__main__":
    # 运行演示
    asyncio.run(main())