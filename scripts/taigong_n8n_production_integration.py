#!/usr/bin/env python3
"""
太公心易 → N8N 生产环境集成
已验证可用的webhook集成方案
"""

import requests
import json
import os
from datetime import datetime
from typing import Dict, Any, Optional

class TaiGongXinYiN8NProduction:
    """太公心易N8N生产环境集成器"""
    
    def __init__(self):
        # 使用已验证可用的生产webhook
        self.webhook_url = "https://houzhongxu-n8n-free.hf.space/webhook/ce40f698-832e-475a-a3c7-0895c9e2e90b"
        self.base_url = "https://houzhongxu-n8n-free.hf.space"
        self.workflow_id = "5Ibi4vJZjSB0ZaTt"
        
        print("🚀 太公心易N8N生产环境集成器启动")
        print(f"✅ Webhook已验证可用: {self.webhook_url}")
    
    def send_rss_analysis(self, rss_data: Dict[str, Any]) -> Dict[str, Any]:
        """发送RSS分析数据到N8N"""
        
        payload = {
            "timestamp": datetime.now().isoformat(),
            "source": "太公心易RSS分析系统",
            "type": "rss_analysis",
            "workflow": "market_sentiment_analysis",
            "data": {
                "title": rss_data.get("title", ""),
                "content": rss_data.get("content", ""),
                "url": rss_data.get("url", ""),
                "published_time": rss_data.get("published_time", ""),
                "sentiment": rss_data.get("sentiment", "neutral"),
                "keywords": rss_data.get("keywords", []),
                "confidence": rss_data.get("confidence", 0.5)
            },
            "metadata": {
                "system_version": "1.0",
                "analysis_engine": "太公心易",
                "processing_time": datetime.now().isoformat()
            }
        }
        
        return self._send_to_n8n(payload, "RSS分析")
    
    def send_market_data(self, market_data: Dict[str, Any]) -> Dict[str, Any]:
        """发送市场数据到N8N"""
        
        payload = {
            "timestamp": datetime.now().isoformat(),
            "source": "太公心易市场分析系统",
            "type": "market_analysis",
            "workflow": "seven_sisters_analysis",
            "data": {
                "top_performer": market_data.get("top_performer", {}),
                "market_summary": market_data.get("market_summary", {}),
                "detailed_data": market_data.get("detailed_data", []),
                "analysis_timestamp": market_data.get("analysis_timestamp", ""),
                "recommendation": market_data.get("recommendation", "")
            },
            "metadata": {
                "system_version": "1.0",
                "data_source": "七姐妹基本面分析",
                "processing_time": datetime.now().isoformat()
            }
        }
        
        return self._send_to_n8n(payload, "市场数据")
    
    def send_seven_sisters_update(self, sisters_data: list) -> Dict[str, Any]:
        """发送七姐妹数据更新到N8N"""
        
        payload = {
            "timestamp": datetime.now().isoformat(),
            "source": "太公心易七姐妹系统",
            "type": "seven_sisters_update",
            "workflow": "fundamental_analysis",
            "data": {
                "sisters_count": len(sisters_data),
                "update_time": datetime.now().isoformat(),
                "sisters_data": sisters_data,
                "top_performer": sisters_data[0] if sisters_data else None,
                "analysis_summary": self._generate_sisters_summary(sisters_data)
            },
            "metadata": {
                "system_version": "1.0",
                "data_source": "Interactive Brokers + 基本面分析",
                "processing_time": datetime.now().isoformat()
            }
        }
        
        return self._send_to_n8n(payload, "七姐妹更新")
    
    def send_alert(self, alert_data: Dict[str, Any]) -> Dict[str, Any]:
        """发送预警信息到N8N"""
        
        payload = {
            "timestamp": datetime.now().isoformat(),
            "source": "太公心易预警系统",
            "type": "market_alert",
            "workflow": "alert_processing",
            "data": {
                "alert_type": alert_data.get("alert_type", "general"),
                "severity": alert_data.get("severity", "medium"),
                "message": alert_data.get("message", ""),
                "affected_symbols": alert_data.get("affected_symbols", []),
                "recommendation": alert_data.get("recommendation", ""),
                "confidence": alert_data.get("confidence", 0.5)
            },
            "metadata": {
                "system_version": "1.0",
                "alert_engine": "太公心易",
                "processing_time": datetime.now().isoformat()
            }
        }
        
        return self._send_to_n8n(payload, "市场预警")
    
    def _send_to_n8n(self, payload: Dict[str, Any], data_type: str) -> Dict[str, Any]:
        """发送数据到N8N的核心方法"""
        
        try:
            print(f"📤 发送{data_type}数据到N8N...")
            
            response = requests.post(
                self.webhook_url,
                json=payload,
                timeout=30,
                headers={
                    'Content-Type': 'application/json',
                    'User-Agent': 'TaiGongXinYi-Production/1.0'
                }
            )
            
            if response.status_code == 200:
                print(f"✅ {data_type}数据发送成功")
                try:
                    response_data = response.json()
                    return {
                        "success": True,
                        "status_code": 200,
                        "response": response_data,
                        "data_type": data_type,
                        "timestamp": datetime.now().isoformat()
                    }
                except:
                    return {
                        "success": True,
                        "status_code": 200,
                        "response": response.text,
                        "data_type": data_type,
                        "timestamp": datetime.now().isoformat()
                    }
            else:
                print(f"❌ {data_type}数据发送失败: {response.status_code}")
                return {
                    "success": False,
                    "status_code": response.status_code,
                    "error": response.text,
                    "data_type": data_type,
                    "timestamp": datetime.now().isoformat()
                }
                
        except requests.exceptions.Timeout:
            print(f"⏰ {data_type}数据发送超时")
            return {
                "success": False,
                "error": "timeout",
                "data_type": data_type,
                "timestamp": datetime.now().isoformat()
            }
        except Exception as e:
            print(f"❌ {data_type}数据发送异常: {e}")
            return {
                "success": False,
                "error": str(e),
                "data_type": data_type,
                "timestamp": datetime.now().isoformat()
            }
    
    def _generate_sisters_summary(self, sisters_data: list) -> Dict[str, Any]:
        """生成七姐妹数据摘要"""
        if not sisters_data:
            return {"message": "无数据"}
        
        try:
            # 计算平均PE比率
            pe_ratios = [s.get("pe_ratio", 0) for s in sisters_data if s.get("pe_ratio")]
            avg_pe = sum(pe_ratios) / len(pe_ratios) if pe_ratios else 0
            
            # 找出表现最好的
            top_performer = max(sisters_data, key=lambda x: x.get("eps_ttm", 0))
            
            return {
                "total_companies": len(sisters_data),
                "average_pe_ratio": round(avg_pe, 2),
                "top_performer": {
                    "symbol": top_performer.get("symbol", ""),
                    "chinese_name": top_performer.get("chinese_name", ""),
                    "eps_ttm": top_performer.get("eps_ttm", 0)
                },
                "analysis_quality": "high" if len(sisters_data) >= 5 else "medium"
            }
        except Exception as e:
            return {"error": str(e), "message": "摘要生成失败"}
    
    def test_integration(self) -> Dict[str, Any]:
        """测试集成功能"""
        
        print("\n🧪 开始集成功能测试...")
        
        # 测试RSS分析
        rss_test = {
            "title": "太公心易集成测试 - 科技股分析",
            "content": "今日科技股表现强劲，苹果、微软等公司股价上涨。市场情绪乐观，投资者看好科技板块前景。",
            "sentiment": "positive",
            "confidence": 0.85,
            "keywords": ["科技股", "上涨", "苹果", "微软", "乐观"]
        }
        
        rss_result = self.send_rss_analysis(rss_test)
        
        # 测试市场数据
        market_test = {
            "top_performer": {
                "symbol": "AAPL",
                "chinese_name": "苹果公司",
                "price_change": "+3.2%"
            },
            "market_summary": {
                "trend": "上涨",
                "sentiment": "乐观",
                "volume": "高"
            },
            "recommendation": "看好科技板块"
        }
        
        market_result = self.send_market_data(market_test)
        
        # 测试七姐妹数据
        sisters_test = [
            {
                "symbol": "AAPL",
                "chinese_name": "苹果公司",
                "eps_ttm": 6.05,
                "pe_ratio": 28.5,
                "rank": 1
            },
            {
                "symbol": "MSFT", 
                "chinese_name": "微软公司",
                "eps_ttm": 11.05,
                "pe_ratio": 32.1,
                "rank": 2
            }
        ]
        
        sisters_result = self.send_seven_sisters_update(sisters_test)
        
        # 汇总测试结果
        test_summary = {
            "rss_analysis": rss_result["success"],
            "market_data": market_result["success"],
            "seven_sisters": sisters_result["success"],
            "overall_success": all([
                rss_result["success"],
                market_result["success"], 
                sisters_result["success"]
            ]),
            "timestamp": datetime.now().isoformat()
        }
        
        print("\n📊 测试结果:")
        print(f"RSS分析: {'✅' if rss_result['success'] else '❌'}")
        print(f"市场数据: {'✅' if market_result['success'] else '❌'}")
        print(f"七姐妹: {'✅' if sisters_result['success'] else '❌'}")
        print(f"整体状态: {'✅ 全部成功' if test_summary['overall_success'] else '❌ 部分失败'}")
        
        return test_summary

def main():
    """主函数 - 演示完整的集成功能"""
    
    print("🎯 太公心易 → N8N 生产环境集成测试")
    print("=" * 60)
    
    # 初始化集成器
    integrator = TaiGongXinYiN8NProduction()
    
    # 运行完整测试
    test_results = integrator.test_integration()
    
    # 保存测试结果
    with open("n8n_integration_test_results.json", "w", encoding="utf-8") as f:
        json.dump(test_results, f, ensure_ascii=False, indent=2)
    
    print(f"\n📁 测试结果已保存到: n8n_integration_test_results.json")
    
    if test_results["overall_success"]:
        print("\n🎉 恭喜！太公心易与N8N集成完全成功！")
        print("\n💡 现在可以:")
        print("1. 在现有脚本中使用这个集成器")
        print("2. 设置定时任务自动推送数据")
        print("3. 在N8N中配置后续的数据处理流程")
        print("4. 添加更多的数据分析和通知功能")
    else:
        print("\n⚠️ 部分功能测试失败，请检查配置")
    
    return integrator

if __name__ == "__main__":
    integrator = main()