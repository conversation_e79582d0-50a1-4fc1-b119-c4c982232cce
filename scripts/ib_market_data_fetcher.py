import asyncio
import os
import math
from dotenv import load_dotenv
from ib_insync import IB, Stock, util

async def main():
    # Load environment variables from .env file
    load_dotenv('.env', override=True)

    # Get connection details from environment variables
    try:
        port = int(os.getenv("IB_PORT", 4000))
        client_id = int(os.getenv("IB_CLIENT_ID", 1))
    except (ValueError, TypeError):
        print("Error: IB_PORT and IB_CLIENT_ID must be integers.")
        return

    # Determine host based on port
    # If using frp port (5050), connect to frp server. Otherwise, connect to localhost.
    host = os.getenv("IB_HOST", "127.0.0.1")

    print(f"Debug: Final connection details: Host={host}, Port={port}")



    ib = IB()

    try:
        print(f"Attempting to connect to {host}:{port} with Client ID {client_id}...")
        await ib.connectAsync(host, port, clientId=client_id, timeout=20)
        print("Successfully connected to IB.")

        # Define the contract for Tesla (TSLA)
        contract = Stock('TSLA', 'SMART', 'USD')
        await ib.qualifyContractsAsync(contract)
        print("Contract qualified.")

        print("Requesting market data for TSLA...")
        # Request a snapshot of market data
        ticker = ib.reqMktData(contract, '', False, False)
        
        # Wait a moment for the data to arrive
        await asyncio.sleep(5)

        if ticker and ticker.last is not None and not math.isnan(ticker.last):
            print("\n--- Tesla (TSLA) Market Data Snapshot ---")
            print(f"Time:         {ticker.time}")
            print(f"Last Price:   {ticker.last}")
            print(f"Last Size:    {ticker.lastSize}")
            print(f"Bid Price:    {ticker.bid}")
            print(f"Bid Size:     {ticker.bidSize}")
            print(f"Ask Price:    {ticker.ask}")
            print(f"Ask Size:     {ticker.askSize}")
            print(f"Volume:       {ticker.volume}")
            print(f"Close Price:  {ticker.close}")
            print("-----------------------------------------\n")
        else:
            print("Could not retrieve valid market data for TSLA.")
            print("Please check your market data subscriptions and if the market is open.")
            if ticker:
                print(f"Received ticker object: {ticker}")


    except ConnectionRefusedError:
        print(f"Error: Connection refused by {host}:{port}.")
        print("Please check that TWS/Gateway is configured to accept API connections on this port.")
    except Exception as e:
        print(f"An unexpected error occurred: {e}")
    finally:
        if ib.isConnected():
            print("Disconnecting from IB.")
            ib.disconnect()

if __name__ == "__main__":
    # Set up the event loop
    util.patchAsyncio()
    try:
        asyncio.run(main())
    except (KeyboardInterrupt, SystemExit):
        print("\nExecution stopped by user.")