#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Doppler配置验证脚本
验证Cauldron项目的Doppler集成是否正常工作
"""

import os

def main():
    print("🔐 Cauldron项目Doppler配置验证")
    print("=" * 40)
    
    # 检查关键环境变量
    critical_vars = [
        "DATABASE_URL",
        "ZILLIZ_TOKEN", 
        "OPENROUTER_API_KEY_1",
        "MASTODON_ACCESS_TOKEN"
    ]
    
    print("📊 检查关键配置:")
    all_present = True
    
    for var in critical_vars:
        value = os.getenv(var)
        if value:
            # 遮蔽敏感信息
            masked = f"{value[:10]}...{value[-6:]}" if len(value) > 16 else value[:8] + "..."
            print(f"  ✅ {var}: {masked}")
        else:
            print(f"  ❌ {var}: 未找到")
            all_present = False
    
    print(f"\n📈 环境变量总数: {len(os.environ)}")
    print(f"🔐 Doppler变量: {len([k for k in os.environ.keys() if k.startswith('DOPPLER_')])}")
    
    # 检查Doppler特有变量
    doppler_vars = [k for k in os.environ.keys() if k.startswith('DOPPLER_')]
    if doppler_vars:
        print(f"\n🎯 Doppler系统变量:")
        for var in doppler_vars:
            print(f"  • {var}: {os.getenv(var)}")
    
    if all_present:
        print(f"\n🎉 验证成功！所有关键配置都已正确加载")
        print(f"🚀 可以开始使用Doppler运行Cauldron应用:")
        print(f"   doppler run -- streamlit run app/streamlit_app.py")
        print(f"   doppler run -- python scripts/start_jixia_academy.py")
    else:
        print(f"\n⚠️ 验证失败，部分关键配置缺失")
        print(f"🔧 请检查Doppler配置或重新上传密钥")
    
    return all_present

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
