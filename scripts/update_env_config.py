#!/usr/bin/env python3
"""
更新环境配置脚本
"""
import os
import shutil
from datetime import datetime

def update_env_config():
    """更新.env配置文件"""
    
    # 备份现有配置
    if os.path.exists('.env'):
        backup_name = f'.env.backup.{datetime.now().strftime("%Y%m%d_%H%M%S")}'
        shutil.copy('.env', backup_name)
        print(f"✅ 已备份现有配置到: {backup_name}")
    
    # N8N配置
    n8n_config = {
        'N8N_WEBHOOK_PROD': 'https://houzhongxu-n8n-free.hf.space/webhook/ce40f698-832e-475a-a3c7-0895c9e2e90b',
        'N8N_BASE_URL': 'https://houzhongxu-n8n-free.hf.space',
        'N8N_WORKFLOW_ID': '5Ibi4vJZjSB0ZaTt',
        'N8N_ENABLED': 'true'
    }
    
    # 读取现有配置
    env_lines = []
    if os.path.exists('.env'):
        with open('.env', 'r') as f:
            env_lines = f.readlines()
    
    # 更新或添加N8N配置
    updated_lines = []
    n8n_keys = set(n8n_config.keys())
    
    for line in env_lines:
        if '=' in line and not line.strip().startswith('#'):
            key = line.split('=')[0].strip()
            if key in n8n_keys:
                updated_lines.append(f"{key}={n8n_config[key]}\n")
                n8n_keys.remove(key)
            else:
                updated_lines.append(line)
        else:
            updated_lines.append(line)
    
    # 添加新的N8N配置
    if n8n_keys:
        updated_lines.append("\n# N8N生产配置\n")
        for key in n8n_keys:
            updated_lines.append(f"{key}={n8n_config[key]}\n")
    
    # 写入更新的配置
    with open('.env', 'w') as f:
        f.writelines(updated_lines)
    
    print("✅ N8N配置已更新到.env文件")
    
    # 验证配置
    for key, value in n8n_config.items():
        env_value = os.getenv(key)
        if env_value == value:
            print(f"✅ {key}: 配置正确")
        else:
            print(f"⚠️ {key}: 需要重新加载环境变量")

if __name__ == "__main__":
    update_env_config()
