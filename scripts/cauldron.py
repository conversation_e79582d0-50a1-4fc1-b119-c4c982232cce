import streamlit as st
import streamlit as st
import time
import pandas as pd
from datetime import datetime, timedelta
import requests
import yfinance as yf
import json
import threading
import asyncio
import sys
import aiohttp
import os

sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '.')))



# 动态数据获取函数
async def fetch_url(session, url):
    async with session.get(url) as response:
        return await response.json()

async def get_real_time_market_data():
    """获取实时市场数据"""
    try:
        # 获取主要指数数据
        spx = yf.Ticker("^GSPC")
        spx_info = spx.history(period="1d", interval="1m")
        
        nasdaq = yf.Ticker("^IXIC")
        nasdaq_info = nasdaq.history(period="1d", interval="1m")
        
        dow = yf.Ticker("^DJI")
        dow_info = dow.history(period="1d", interval="1m")
        
        if not spx_info.empty:
            spx_current = spx_info['Close'].iloc[-1]
            spx_change = ((spx_current - spx_info['Close'].iloc[0]) / spx_info['Close'].iloc[0]) * 100
        else:
            spx_current, spx_change = "N/A", "N/A"
            
        return {
            'spx': f"{spx_current:.2f} ({spx_change:+.2f}%)",
            'nasdaq': nasdaq_info['Close'].iloc[-1] if not nasdaq_info.empty else "N/A",
            'dow': dow_info['Close'].iloc[-1] if not dow_info.empty else "N/A",
            'timestamp': datetime.now().strftime('%H:%M:%S')
        }
    except Exception as e:
        return {'spx': 'Error', 'nasdaq': 'Error', 'dow': 'Error', 'timestamp': datetime.now().strftime('%H:%M:%S')}

async def get_hot_meme_stocks():
    """获取热门妖股数据"""
    try:
        # 热门妖股列表（可以从API获取或预定义热门股票）
        meme_symbols = ['GME', 'AMC', 'BBBY', 'TSLA', 'NVDA', 'AAPL', 'MSFT', 'GOOGL']
        hot_stocks = []
        
        for symbol in meme_symbols[:5]:  # 限制为前5个
            try:
                ticker = yf.Ticker(symbol)
                info = ticker.history(period="1d", interval="5m")
                if not info.empty:
                    current_price = info['Close'].iloc[-1]
                    volume = info['Volume'].iloc[-1]
                    change_pct = ((current_price - info['Open'].iloc[0]) / info['Open'].iloc[0]) * 100
                    
                    hot_stocks.append({
                        '股票代码': symbol,
                        '当前价格': f"${current_price:.2f}",
                        '涨跌幅': f"{change_pct:+.2f}%",
                        '成交量': f"{volume:,.0f}",
                        '更新时间': datetime.now().strftime('%H:%M')
                    })
            except:
                continue
                
        return pd.DataFrame(hot_stocks)
    except Exception as e:
        return pd.DataFrame([{'股票代码': 'Error', '当前价格': 'N/A', '涨跌幅': 'N/A', '成交量': 'N/A', '更新时间': datetime.now().strftime('%H:%M')}])

async def get_abnormal_volatility_stocks():
    """获取异常波动股票"""
    try:
        # 监控高波动率股票
        volatile_symbols = ['TSLA', 'NVDA', 'AMD', 'PLTR', 'COIN', 'ROKU', 'ZM', 'PTON']
        abnormal_stocks = []
        
        for symbol in volatile_symbols[:5]:
            try:
                ticker = yf.Ticker(symbol)
                info = ticker.history(period="1d", interval="5m")
                if not info.empty:
                    current_price = info['Close'].iloc[-1]
                    high = info['High'].max()
                    low = info['Low'].min()
                    volatility = ((high - low) / current_price) * 100
                    change_pct = ((current_price - info['Open'].iloc[0]) / info['Open'].iloc[0]) * 100
                    
                    if abs(change_pct) > 2 or volatility > 5:  # 异常波动阈值
                        abnormal_stocks.append({
                            '股票代码': symbol,
                            '当前价格': f"${current_price:.2f}",
                            '涨跌幅': f"{change_pct:+.2f}%",
                            '波动率': f"{volatility:.2f}%",
                            '异常类型': '高波动' if volatility > 5 else '大幅变动'
                        })
            except:
                continue
                
        return pd.DataFrame(abnormal_stocks)
    except Exception as e:
        return pd.DataFrame([{'股票代码': 'Error', '当前价格': 'N/A', '涨跌幅': 'N/A', '波动率': 'N/A', '异常类型': 'N/A'}])

def check_service_health():
    health_status = {}
    # Check Blockchair
    try:
        response = requests.get("https://api.blockchair.com/stats", timeout=5)
        if response.status_code == 200:
            health_status['Blockchair'] = "在线"
        else:
            health_status['Blockchair'] = "离线"
    except requests.exceptions.RequestException:
        health_status['Blockchair'] = "离线"

    # Check N8N
    n8n_url = st.session_state.get('n8n_webhook_url', '')
    if n8n_url:
        try:
            # a simple GET to the base URL should be enough to check for life
            base_url = '/'.join(n8n_url.split('/')[:3])
            response = requests.get(base_url, timeout=5)
            if response.status_code == 200:
                health_status['N8N'] = "在线"
            else:
                health_status['N8N'] = "离线"
        except requests.exceptions.RequestException:
            health_status['N8N'] = "离线"
    else:
        health_status['N8N'] = "未配置"
    return health_status

async def get_crypto_meme_data():
    """获取热门meme币数据"""
    try:
        # 使用CoinGecko API获取加密货币数据
        crypto_symbols = ['bitcoin', 'ethereum', 'dogecoin', 'shiba-inu', 'pepe']
        url = f"https://api.coingecko.com/api/v3/simple/price?ids={','.join(crypto_symbols)}&vs_currencies=usd&include_24hr_change=true"
        
        async with aiohttp.ClientSession() as session:
            data = await fetch_url(session, url)
            crypto_data = []
            
            for crypto_id, info in data.items():
                crypto_data.append({
                    '币种': crypto_id.upper(),
                    '价格(USD)': f"${info['usd']:.6f}" if info['usd'] < 1 else f"${info['usd']:.2f}",
                    '24h涨跌': f"{info.get('usd_24h_change', 0):+.2f}%",
                    '更新时间': datetime.now().strftime('%H:%M')
                })
            
            return pd.DataFrame(crypto_data)
    except Exception as e:
        return pd.DataFrame([{'币种': 'Error', '价格(USD)': 'N/A', '24h涨跌': 'N/A', '更新时间': datetime.now().strftime('%H:%M')}])

# 修改ui_refresh函数以更新九宫格内容
async def ui_refresh():
    """刷新UI数据显示"""
    try:
        with open('market_data.json', 'r') as f:
            market_data = json.load(f)
        st.session_state.market_data = market_data
        st.session_state.ib_connected = True  # Assume connected if file exists and is readable
    except FileNotFoundError:
        st.session_state.market_data = {}
        st.session_state.ib_connected = False
        st.info("正在等待 market_data.json 文件生成...")
    except json.JSONDecodeError:
        st.session_state.market_data = {}
        st.session_state.ib_connected = False
        st.warning("market_data.json 文件为空或格式错误。")
    except Exception as e:
        st.error(f"读取数据文件时出错: {e}")
        st.session_state.market_data = {}
        st.session_state.ib_connected = False

    # 设置刷新时间戳
    st.session_state.last_refresh = datetime.now()

    # 获取其他数据
    market_data, hot_stocks, abnormal_stocks, crypto_data = await asyncio.gather(
        get_real_time_market_data(),
        get_hot_meme_stocks(),
        get_abnormal_volatility_stocks(),
        get_crypto_meme_data()
    )
    st.session_state.market_data = market_data
    st.session_state.hot_stocks = hot_stocks
    st.session_state.abnormal_stocks = abnormal_stocks
    st.session_state.crypto_data = crypto_data

    return True


# 在streamlit_main函数中修改布局
def streamlit_main():
    # 设置页面标题
    st.set_page_config(page_title="炼药壶 - AI驱动的量化交易研究平台", page_icon="data:image/jpeg;base64,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...QAUAGaADOaADvQAuaACgBO9ACmgBOaAFoAKAGmgB1ACdKAFzQAUAIKAFJxQAmc0ALQA3+KgB1ACEZoAOBQAcUALQAhFACjpQAUAITQAvWgA6UAIOlABmgAzQAZozQAUAIKAFJxQAmc0ALQA3+KgB1ACEZoAOBQAcUALQAhFACjpQAUAITQAvWgA6UAIOlABmgAzQAZoABQAUAFAC0ABoAQdKADNABj1oAOlABzQAtACZoAWgAoASgAzQACgBaACgBKAAUAFABQAtABQAhoAABQAUAFAC0ABoAQdKADNABj1oAOlABzQAtACZoAWgAoASgAzQACgBaACgBKAAUAFABQAtABQAhoAM0AHvQAuaAE60AGKADPNAAaACgApALQAlMAoABQAg6mgBaAFoAQ9RQAUALQAnNAC0AJ3oAU0AJ70M0AHvQAuaAE60AGKADPNAAaACgApALQAlMAoABQAg6mgBaAFoAQ9RQAUALQAnNAC0AJ3oAU0AJ70ALQAlAC0ANNADqAE96ACgBaAEoAWgBDQAtABQAlAC0AFABQAUAJQAYoAWgAoASgBaACgAoASgBaACgBaAEoAWgAoASgBaACgAoAKAEoAWgAoATFAC0AFABQAmKADFABQAUAFABmgAoAWgAoAQ0AA6UAFAC5oASgBaACgAoASgAoAWgBKACgBaAEoAKACgAoAWgBO9ABQAUAf/2Q==", layout="wide")
    
    # 添加左侧边栏
    with st.sidebar:
        st.title("太公心易BI系统")
        st.markdown("### 🍶 炼药壶")
        
        # 移动设备不友好声明
        st.warning("⚠️ 本应用不适合在移动设备上使用，请使用桌面浏览器访问以获得最佳体验。")
        

        
        # 系统状态
        st.markdown("### 🔄 系统健康度")
        # IB Status
        if 'ib_connected' in st.session_state and st.session_state.ib_connected:
            st.success("IB: 连接成功")
        else:
            st.error("IB: 连接断开")

        health_status = check_service_health()
        # Blockchair Status
        if health_status.get('Blockchair') == "在线":
            st.success(f"Blockchair: {health_status['Blockchair']}")
        else:
            st.error(f"Blockchair: {health_status.get('Blockchair', '未知')}")

        # N8N Status
        if health_status.get('N8N') == "在线":
            st.success(f"N8N: {health_status['N8N']}")
        elif health_status.get('N8N') == "未配置":
            st.warning(f"N8N: {health_status['N8N']}")
        else:
            st.error(f"N8N: {health_status.get('N8N', '未知')}")
        
        # AI助手设置
        st.markdown("### 🤖 AI助手设置")
        n8n_webhook_url = st.text_input(
            "N8N Webhook URL",
            value=st.session_state.get('n8n_webhook_url', ''),
            placeholder="https://your-n8n.com/webhook/..."
        )
        if n8n_webhook_url:
            st.session_state['n8n_webhook_url'] = n8n_webhook_url
        
        # 版权信息
        st.markdown("---")
        st.caption("© 2023 太公心易BI系统. All rights reserved.")
    
    # 页面标题（主内容区域）
    st.title("🍶 太公心易BI系统 - 炼药壶")
    st.markdown("*Alchemy Cauldron - AI-Powered Quantitative Trading Research Platform*")
    

    
    # 刷新数据
    asyncio.run(ui_refresh())
    
    # 创建Tab布局
    tab1, tab2, tab3, tab4, tab5 = st.tabs(["🏠 首页", "🇺🇸 US Stocks", "🇭🇰 HK Stocks", "🪙 币链", "🏛️ 稷下学宫"]) # 添加了稷下学宫
    
    # 初始化session_state
    if 'debate_topic' not in st.session_state:
        st.session_state.debate_topic = ""
    if 'debate_running' not in st.session_state:
        st.session_state.debate_running = False
    if 'debate_log' not in st.session_state:
        st.session_state.debate_log = []
    
    # 首页Tab - 市场概览和重要信息
    with tab1:
        st.markdown("### 📊 市场概览")
        if 'market_data' in st.session_state and st.session_state.market_data:
            df = pd.DataFrame.from_dict(st.session_state.market_data, orient='index')
            st.dataframe(df)
        else:
            st.info("正在等待市场数据...")
        
        # 市场热点和重要公告
        col_left, col_right = st.columns([2, 1])
        
        with col_left:
            st.markdown("### 📈 今日市场热点")
            st.info("🔥 AI概念股持续走强，NVDA创历史新高")
            st.info("📊 美联储会议纪要显示加息周期接近尾声")
            st.info("💰 比特币突破45000美元，加密货币市场回暖")
            st.info("🏢 港股科技股反弹，腾讯、阿里巴巴涨幅超3%")
        
        with col_right:
            st.markdown("### 🔔 重要公告")
            st.warning("⚠️ 今日美股感恩节休市")
            st.success("✅ 系统运行正常")
            st.info("📅 下周重要财报: TSLA, AAPL")
    
    # 美股Tab
    with tab2:
        st.markdown("### 🇺🇸 US Stock Market")
        
        # 创建美股子区域
        us_col1, us_col2 = st.columns(2)
        
        with us_col1:
            st.subheader("🔥 热门妖股")
            if 'hot_stocks' in st.session_state and not st.session_state.hot_stocks.empty:
                st.dataframe(st.session_state.hot_stocks, use_container_width=True, hide_index=True)
            else:
                st.info("正在加载热门股票数据...")
            
            st.subheader("📈 技术分析")
            st.info("技术分析功能开发中...")
        
        with us_col2:
            st.subheader("⚡ 异常波动")
            if 'abnormal_stocks' in st.session_state and not st.session_state.abnormal_stocks.empty:
                st.dataframe(st.session_state.abnormal_stocks, use_container_width=True, hide_index=True)
            else:
                st.info("正在监控异常波动...")
            
            st.subheader("🎯 智能筛选")
            st.info("智能筛选功能开发中...")
        
        # 美股市场情绪和基本面分析
        st.markdown("---")
        sentiment_col1, sentiment_col2 = st.columns(2)
        
        with sentiment_col1:
            st.subheader("📊 市场情绪")
            st.info("市场情绪分析开发中...")
        
        with sentiment_col2:
            st.subheader("🔍 基本面分析")
            st.info("基本面分析功能开发中...")
    
    # 港股Tab
    with tab3:
        st.markdown("### 🇭🇰 HK Stock Market")
        
        # 港股指数
        hk_col1, hk_col2, hk_col3 = st.columns(3)
        
        with hk_col1:
            st.metric("恒生指数", "18,500.00", "-0.3%")
        with hk_col2:
            st.metric("恒生科技", "3,800.00", "+1.5%")
        with hk_col3:
            st.metric("国企指数", "6,200.00", "-0.1%")
        
        # 港股热门板块
        hk_main_col1, hk_main_col2 = st.columns(2)
        
        with hk_main_col1:
            st.subheader("🏢 科技股")
            st.info("港股科技股数据加载中...")
            
            st.subheader("🏦 金融股")
            st.info("港股金融股数据加载中...")
        
        with hk_main_col2:
            st.subheader("🏭 地产股")
            st.info("港股地产股数据加载中...")
            
            st.subheader("⚡ 异动股")
            st.info("港股异动监控中...")
    
    # 币链Tab
    with tab4:
        st.markdown("### 🪙 加密货币市场")
        
        # 主要加密货币指标
        crypto_col1, crypto_col2, crypto_col3, crypto_col4 = st.columns(4)
        
        with crypto_col1:
            st.metric("比特币", "$45,000", "+2.5%")
        with crypto_col2:
            st.metric("以太坊", "$2,800", "+1.8%")
        with crypto_col3:
            st.metric("BNB", "$320", "+0.9%")
        with crypto_col4:
            st.metric("总市值", "$1.8T", "+1.2%")
        
        # Meme币和热门币种
        crypto_main_col1, crypto_main_col2 = st.columns(2)
        
        with crypto_main_col2:
            st.subheader("🪙 Meme币行情")
            if 'crypto_data' in st.session_state and not st.session_state.crypto_data.empty:
                st.dataframe(st.session_state.crypto_data, use_container_width=True, hide_index=True)
            else:
                st.info("正在加载加密货币数据...")

    # 稷下学宫Tab
    with tab5:
        st.header("🏛️ 稷下学宫 - AI辩论平台")
        st.markdown("这是一个思想碰撞的平台,让AI化身辩手,就您关心的话题展开多视角、深层次的辩论。")

        st.session_state.debate_topic = st.text_input("请输入辩论主题", st.session_state.debate_topic)

        col1, col2 = st.columns(2)
        with col1:
            if st.button("开始辩论", key="start_debate_button", disabled=st.session_state.debate_running):
                if st.session_state.debate_topic:
                    st.session_state.debate_running = True
                    st.session_state.debate_log = [f"辩论开始：{st.session_state.debate_topic}"]
                    st.rerun()
                else:
                    st.warning("请输入辩论主题")
        
        with col2:
            if st.button("终止辩论", key="stop_debate_button", disabled=not st.session_state.debate_running):
                st.session_state.debate_running = False
                st.session_state.debate_log.append("辩论已终止。")
                st.rerun()

        if st.session_state.debate_running:
            st.info("辩论正在进行中...")
            # 在这里添加辩论逻辑的占位符
            # 例如，可以模拟AI辩手的发言
            if len(st.session_state.debate_log) < 10: # 限制自动发言次数
                time.sleep(2) # 模拟思考
                st.session_state.debate_log.append(f"AI辩手 {len(st.session_state.debate_log)}: 这是一个有趣的观点，但是...")
                st.rerun()
            else:
                st.session_state.debate_running = False
                st.session_state.debate_log.append("辩论回合结束。")
                st.rerun()


        if st.session_state.debate_log:
            st.markdown("### 辩论记录")
            for message in st.session_state.debate_log:
                st.text(message)
        

    
    # 显示最后更新时间
    if "last_refresh" in st.session_state:
        st.caption(f"最后更新: {st.session_state.last_refresh.strftime('%H:%M:%S')}")

    # 自动刷新
    time.sleep(5)
    st.rerun()
    

# Global variable to hold the runner instance and thread
ib_runner_thread = None
ib_runner_instance = None

def start_ib_runner():
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)

    from strategy.scanning.MainRunner import MainRunner
    from ibapi.services.ib import IBService

    ib_service = IBService.get_instance()
    ib_service.connect()

    global ib_runner_instance
    ib_runner_instance = MainRunner(is_debug=True, full_log=True)
    try:
        ib_runner_instance.start_process()
    except Exception as e:
        print(f"Error in ib_runner_thread: {e}")

def cleanup_ib_runner():
    global ib_runner_instance
    if ib_runner_instance and ib_runner_instance.iBMarketData.ibService.ib_client.isConnected():
        print("Disconnecting IB connection...")
        ib_runner_instance.iBMarketData.ibService.ib_client.disconnect()

if __name__ == "__main__":
    # Initialize session state
    if 'ib_connected' not in st.session_state:
        st.session_state.ib_connected = False
    if 'market_data' not in st.session_state:
        st.session_state.market_data = {}
    if 'last_refresh' not in st.session_state:
        st.session_state.last_refresh = datetime.now()
    if 'chat_messages' not in st.session_state:
        st.session_state.chat_messages = []
    if 'n8n_webhook_url' not in st.session_state:
        st.session_state['n8n_webhook_url'] = ''

    # Start the IB runner in a background thread only once
    if 'ib_runner_thread' not in st.session_state:
        ib_runner_thread = threading.Thread(target=start_ib_runner, daemon=True)
        ib_runner_thread.start()
        st.session_state['ib_runner_thread'] = ib_runner_thread
        # Register cleanup function
        import atexit
        atexit.register(cleanup_ib_runner)

    streamlit_main()

    # 自动刷新逻辑 (暂时禁用)
    # refresh_interval = 10  # 秒
    # if st.session_state.get('auto_refresh', True):
    #     time.sleep(refresh_interval)
    #     st.rerun()
