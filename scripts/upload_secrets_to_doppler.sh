#!/bin/bash
# 批量上传Cauldron项目密钥到Doppler
# 使用方法: ./scripts/upload_secrets_to_doppler.sh

set -e

echo "🔐 批量上传Cauldron项目密钥到Doppler"
echo "=================================="

# 检查当前Doppler配置
echo "📋 当前Doppler配置:"
doppler configure

echo ""
echo "🚀 开始上传密钥..."

# 数据库配置
echo "📊 上传数据库配置..."
doppler secrets set ZILLIZ_ENDPOINT='https://in03-2a809e6ceec742f.serverless.ali-cn-hangzhou.cloud.zilliz.com.cn'
doppler secrets set ZILLIZ_USER='db_2a809e6ceec742f'
doppler secrets set ZILLIZ_PASSWD='#tgG0G%HADP7lS'
doppler secrets set ZILLIZ_TOKEN='6fb75098fcadefdfb491534a682aaa920febea0e03d6e0a46278df35e6b22e9f3290b4075abe9cf30be99730f2e5dc21b2d3bd6b'

doppler secrets set mongodb_string='mongodb+srv://murenmarkxu:<EMAIL>/taigong_xinyi?retryWrites=true&w=majority&appName=cauldron'
doppler secrets set mongodb_passwd='0L2JILasehiSHq4q'

doppler secrets set DATABASE_URL='postgres://ues2v8l0bjsakj:<EMAIL>:5432/dejb5g2nmmnpsq'

# 金融API
echo "💰 上传金融API密钥..."
doppler secrets set finnhub='d1g22p1r01qk4ao0i62gd1g22p1r01qk4ao0i630'
doppler secrets set coingecko='CG-W7NfuJeqYhZ9DD3Eoh8Jd2Aj'
doppler secrets set coindesk='ad79cec7b3e30c314e4ac04332267bb9696f8f76ac10370a4118174383341be2'
doppler secrets set blockchair='G___PUuKwEhFnggyjMRQiIDXjuPbZxEY'
doppler secrets set coinbase_id='0c3ed19f-9992-4a2e-82ba-abe3f98fe09c'
doppler secrets set coinbase_secret='zF8vGXzqmrWljRek/v0RFcVh/xlaKA2/IPZEREdr/iZPm5i7FHTK8y7t0RpmVDjRg1UEnTnGkXUKUP7cpsMliQ=='

# Interactive Brokers
echo "📈 上传Interactive Brokers配置..."
doppler secrets set IB_HOST='127.0.0.1'
doppler secrets set IB_PORT='4002'
doppler secrets set IB_CLIENT_ID='1'
doppler secrets set IB_TIMEOUT='30'
doppler secrets set IB_RETRY_COUNT='3'
doppler secrets set IB_MARKET_DATA_TYPE='1'
doppler secrets set IB_REQUEST_TIMEOUT='10'

# AI服务密钥
echo "🤖 上传AI服务密钥..."
doppler secrets set OPENROUTER_API_KEY_1='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'
doppler secrets set OPENROUTER_API_KEY_2='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'
doppler secrets set OPENROUTER_API_KEY_3='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'
doppler secrets set OPENROUTER_API_KEY_4='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'

doppler secrets set ANTHROPIC_AUTH_TOKEN='sk-BQonAHs8AgFSxk2dt0SuQgWizTa0hoyEJBDWIljP1HuGuLs2'
doppler secrets set ANTHROPIC_BASE_URL='https://anyrouter.top'

# N8N配置
echo "🔄 上传N8N配置..."
doppler secrets set n8n_url='https://n8n.git4ta.fun/workflow/1nD7GgjcaEERPWHV'
doppler secrets set n8n_token='eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJhNzYwZjUxMy0zMWMzLTQwYzMtOTQ0Zi0xZDkyNGQ4ZjM3Y2QiLCJpc3MiOiJuOG4iLCJhdWQiOiJwdWJsaWMtYXBpIiwiaWF0IjoxNzUxNTU0NjY4fQ.YO11K1xJQQ9lmo-VYCdg8Vf0jyvQ5ufoLF-sWJLqE08'
doppler secrets set n8n_webhook='https://n8n.git4ta.fun/webhook/5ee5fee8-22c9-4335-911d-aa62e3f988b7'

# Mastodon配置
echo "🐘 上传Mastodon配置..."
doppler secrets set MASTODON_APP_ID='fBgJ7kfTfuQdB6ZKaq2zXCJc9my5oApUJ5Um5IjYnp0'
doppler secrets set MASTODON_APP_SECRET='44-HtgyaUTMaJmCMI7zIdw_9cUxurFhwfzh3Z0lTGtI'
doppler secrets set MASTODON_ACCESS_TOKEN='KGe_B0nQDCzE8pfOciQ_aFeRP8yw8wGSbe6OeqroTgQ'

# 韩国服务器MCP配置
echo "🇰🇷 上传韩国服务器配置..."
doppler secrets set KOREAN_MCP_SERVER_URL='https://your-korean-server.com'
doppler secrets set KOREAN_MCP_API_KEY='your_api_key_here'
doppler secrets set KOREAN_MCP_TIMEOUT='30'

echo ""
echo "✅ 密钥上传完成！"
echo ""
echo "📊 查看上传的密钥:"
doppler secrets list

echo ""
echo "🧪 测试Doppler配置:"
echo "doppler run -- python scripts/test_doppler_config.py"
