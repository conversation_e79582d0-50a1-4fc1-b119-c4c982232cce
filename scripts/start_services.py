#!/usr/bin/env python3
"""
炼妖壶 + OpenManus 多服务启动器
在单个Heroku dyno中运行多个服务，最大化利用13美元额度
"""

import os
import sys
import subprocess
import threading
import time
from pathlib import Path

class MultiServiceLauncher:
    """多服务启动器 - 薅羊毛到极致"""
    
    def __init__(self):
        self.services = []
        self.port_base = int(os.environ.get('PORT', 8501))
        
    def start_streamlit(self):
        """启动炼妖壶Streamlit应用"""
        print("🌟 启动炼妖壶Streamlit应用...")
        cmd = [
            sys.executable, "-m", "streamlit", "run", 
            "app/streamlit_app.py",
            "--server.port", str(self.port_base),
            "--server.address", "0.0.0.0",
            "--server.headless", "true"
        ]
        
        process = subprocess.Popen(cmd)
        self.services.append(("Streamlit", process))
        return process
    
    def start_openmanus(self):
        """启动OpenManus FastAPI服务"""
        print("🤖 启动OpenManus AI Agent服务...")
        
        # 检查OpenManus是否存在
        openmanus_path = Path("openmanus")
        if not openmanus_path.exists():
            print("⚠️ OpenManus目录不存在，跳过启动")
            return None
            
        cmd = [
            sys.executable, "-m", "uvicorn",
            "openmanus.main:app",
            "--host", "0.0.0.0", 
            "--port", str(self.port_base + 1)
        ]
        
        try:
            process = subprocess.Popen(cmd, cwd="openmanus")
            self.services.append(("OpenManus", process))
            return process
        except Exception as e:
            print(f"⚠️ OpenManus启动失败: {e}")
            return None
    
    def start_nginx_proxy(self):
        """启动Nginx反向代理 (如果需要)"""
        # 在Heroku上通常不需要，但可以用于本地开发
        pass
    
    def setup_database_connections(self):
        """设置数据库连接"""
        print("🗄️ 配置数据库连接...")
        
        # Neon数据库 (主数据库)
        neon_url = os.environ.get('NEON_DATABASE_URL')
        if neon_url:
            os.environ['DATABASE_URL'] = neon_url
            print("✅ Neon数据库已配置为主数据库")
        
        # Supabase数据库 (备份+实时功能)
        supabase_url = os.environ.get('SUPABASE_DATABASE_URL')
        supabase_key = os.environ.get('SUPABASE_ANON_KEY')
        if supabase_url and supabase_key:
            os.environ['SUPABASE_URL'] = supabase_url
            os.environ['SUPABASE_KEY'] = supabase_key
            print("✅ Supabase数据库已配置为辅助数据库")
    
    def monitor_services(self):
        """监控服务状态"""
        while True:
            time.sleep(30)  # 每30秒检查一次
            
            for name, process in self.services:
                if process.poll() is not None:
                    print(f"⚠️ {name} 服务已停止，退出码: {process.returncode}")
                    # 可以在这里添加重启逻辑
            
            if not self.services:
                break
    
    def start_all(self):
        """启动所有服务"""
        print("🚀 炼妖壶多服务启动器 - 薅羊毛到极致！")
        print(f"📍 基础端口: {self.port_base}")
        
        # 设置数据库连接
        self.setup_database_connections()
        
        # 启动Streamlit (主服务)
        streamlit_process = self.start_streamlit()
        
        # 启动OpenManus (如果存在)
        openmanus_process = self.start_openmanus()
        
        # 显示服务信息
        print("\n🎯 服务启动完成:")
        print(f"  🌟 炼妖壶: http://localhost:{self.port_base}")
        if openmanus_process:
            print(f"  🤖 OpenManus: http://localhost:{self.port_base + 1}")
        
        print("\n💰 资源利用情况:")
        print("  📊 Heroku Eco Dyno: $5/月")
        print("  🗄️ Neon数据库: 免费")
        print("  🗄️ Supabase数据库: 免费")
        print("  💸 总成本: $5/月 (省下$8美元额度!)")
        
        # 监控服务
        try:
            self.monitor_services()
        except KeyboardInterrupt:
            print("\n🛑 正在停止所有服务...")
            for name, process in self.services:
                process.terminate()
                print(f"✅ {name} 已停止")

def main():
    """主函数"""
    launcher = MultiServiceLauncher()
    launcher.start_all()

if __name__ == "__main__":
    main()
