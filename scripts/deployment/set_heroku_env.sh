#!/bin/bash
#
# This script sets environment variables on Heroku from a .env file.
#
# Usage: ./set_heroku_env.sh <heroku_app_name>
#
# It reads the .env file in the project root, filters out comments and empty lines,
# and then sets each variable on the specified Heroku app using the Heroku CLI.

set -e

# Check for Heroku app name
if [ -z "$1" ]; then
  echo "Usage: $0 <heroku_app_name>"
  exit 1
fi

HEROKU_APP_NAME=$1
ENV_FILE="./.env"

if [ ! -f "$ENV_FILE" ]; then
    echo "Error: .env file not found at $ENV_FILE"
    exit 1
fi

# Read .env file, filter comments and empty lines, and set config vars
while IFS= read -r line || [[ -n "$line" ]]; do
  # Trim leading/trailing whitespace
  trimmed_line=$(echo "$line" | sed 's/^[[:space:]]*//;s/[[:space:]]*$//')

  # Ignore comments and empty lines
  if [[ -n "$trimmed_line" && ! "$trimmed_line" =~ ^# ]]; then
    echo "Setting $trimmed_line for app $HEROKU_APP_NAME..."
    heroku config:set "$trimmed_line" --app "$HEROKU_APP_NAME"
  fi
done < "$ENV_FILE"

echo "All environment variables from .env have been set on Heroku app '$HEROKU_APP_NAME'."