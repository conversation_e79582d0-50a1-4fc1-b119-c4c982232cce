#!/usr/bin/env python3
"""
太公心易BI系统 - 会员升级工具
用于升级会员等级，特别是将guest用户升级为高级会员
"""

import sys
import os
from pathlib import Path
from dotenv import load_dotenv

# 添加项目根目录到Python路径
project_root = Path(__file__).resolve().parent.parent.parent
sys.path.insert(0, str(project_root))

# 加载 .env 文件并设置 DATABASE_URL
dotenv_path = project_root / '.env'
load_dotenv(dotenv_path=dotenv_path, override=True)

if not os.getenv("DATABASE_URL"):
    db_user = os.getenv("DB_USER")
    db_password = os.getenv("DB_PASSWORD")
    db_host = os.getenv("DB_HOST")
    db_port = os.getenv("DB_PORT")
    db_name = os.getenv("DB_NAME")

    if all([db_user, db_password, db_host, db_port, db_name]):
        database_url = f"postgresql://{db_user}:{db_password}@{db_host}:{db_port}/{db_name}"
        os.environ["DATABASE_URL"] = database_url

from src.core.member_system import MemberSystem, MembershipLevel

def main():
    """主函数"""
    print("🔧 太公心易BI系统 - 会员升级工具")
    print("=" * 50)
    
    # 初始化会员系统
    member_system = MemberSystem()
    
    # 显示当前会员统计
    stats = member_system.get_member_stats()
    print(f"📊 当前会员统计: {stats}")
    print()
    
    # 升级guest用户为VIP
    print("🚀 正在升级guest用户为VIP会员...")
    success = member_system.upgrade_member('guest', MembershipLevel.SUPREME)
    
    if success:
        print("\n✅ 升级成功！")
        print("现在guest用户拥有以下权限:")
        
        # 获取guest用户信息
        guest_member = member_system.authenticate_member('guest', 'guest')
        if guest_member:
            permissions = member_system.get_member_permissions(guest_member)
            for perm, value in permissions.items():
                status = "✅" if value else "❌"
                print(f"  {status} {perm}: {value}")
        
        print("\n🎉 guest用户现在可以访问所有高级功能！")
        print("包括:")
        print("  • AI深度分析")
        print("  • 详细推理过程")
        print("  • 历史报告查看")
        print("  • 稷下学宫辩论系统")
        print("  • 所有高级功能")
        
    else:
        print("❌ 升级失败，请检查错误信息")
    
    print("\n" + "=" * 50)
    print("升级完成！")

if __name__ == "__main__":
    main()