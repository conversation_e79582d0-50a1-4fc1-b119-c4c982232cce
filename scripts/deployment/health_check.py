#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
健康检查脚本
用于Heroku健康检查和监控
"""

import sys
import os
import json
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

try:
    from src.utils.monitoring import HealthChecker
    
    def main():
        """主健康检查函数"""
        try:
            # 执行健康检查
            health_status = HealthChecker.check_system_health()
            
            # 输出结果
            print(json.dumps(health_status, indent=2, ensure_ascii=False))
            
            # 根据状态设置退出码
            if health_status['status'] == 'healthy':
                sys.exit(0)
            elif health_status['status'] == 'warning':
                sys.exit(1)  # 警告状态
            else:
                sys.exit(2)  # 错误状态
                
        except Exception as e:
            error_result = {
                'status': 'error',
                'error': str(e),
                'message': '健康检查脚本执行失败'
            }
            print(json.dumps(error_result, indent=2, ensure_ascii=False))
            sys.exit(3)
    
    if __name__ == "__main__":
        main()
        
except ImportError as e:
    # 如果导入失败，返回基本的健康状态
    basic_health = {
        'status': 'error',
        'error': f'导入模块失败: {e}',
        'message': '系统模块不可用'
    }
    print(json.dumps(basic_health, indent=2, ensure_ascii=False))
    sys.exit(4)