#!/usr/bin/env python3
"""
GitHub Pages Settings Checker and Helper

This script helps diagnose and fix GitHub Pages configuration issues.
"""

import requests
import json
import os
from typing import Dict, Any

def check_github_pages_settings(owner: str, repo: str, token: str = None) -> Dict[str, Any]:
    """
    Check the current GitHub Pages settings for a repository.
    
    Args:
        owner: Repository owner (username or organization)
        repo: Repository name
        token: GitHub personal access token (optional, for private repos)
    
    Returns:
        Dictionary containing Pages settings information
    """
    
    headers = {
        'Accept': 'application/vnd.github.v3+json',
        'User-Agent': 'GitHub-Pages-Checker'
    }
    
    if token:
        headers['Authorization'] = f'token {token}'
    
    # Check Pages settings
    pages_url = f'https://api.github.com/repos/{owner}/{repo}/pages'
    
    try:
        response = requests.get(pages_url, headers=headers)
        
        if response.status_code == 200:
            pages_info = response.json()
            return {
                'status': 'configured',
                'source': pages_info.get('source', {}),
                'url': pages_info.get('html_url'),
                'custom_domain': pages_info.get('cname'),
                'https_enforced': pages_info.get('https_enforced'),
                'build_type': pages_info.get('build_type'),
                'raw_response': pages_info
            }
        elif response.status_code == 404:
            return {
                'status': 'not_configured',
                'message': 'GitHub Pages is not configured for this repository'
            }
        else:
            return {
                'status': 'error',
                'message': f'API request failed with status {response.status_code}',
                'response': response.text
            }
            
    except Exception as e:
        return {
            'status': 'error',
            'message': f'Request failed: {str(e)}'
        }

def print_pages_info(info: Dict[str, Any]):
    """Print GitHub Pages information in a readable format."""
    
    print("🔍 GitHub Pages Configuration Check")
    print("=" * 50)
    
    if info['status'] == 'configured':
        print("✅ GitHub Pages is CONFIGURED")
        print(f"📄 Site URL: {info.get('url', 'N/A')}")
        print(f"🔒 HTTPS Enforced: {info.get('https_enforced', 'N/A')}")
        print(f"🏗️ Build Type: {info.get('build_type', 'N/A')}")
        
        source = info.get('source', {})
        print(f"📂 Source Branch: {source.get('branch', 'N/A')}")
        print(f"📁 Source Path: {source.get('path', 'N/A')}")
        
        if info.get('custom_domain'):
            print(f"🌐 Custom Domain: {info['custom_domain']}")
            
        print("\n🔧 Recommended Configuration for MkDocs:")
        print("- Source should be: gh-pages branch, / (root)")
        print("- Build type should be: legacy (for gh-pages branch)")
        print("- OR use GitHub Actions as source")
        
    elif info['status'] == 'not_configured':
        print("❌ GitHub Pages is NOT CONFIGURED")
        print("\n🔧 To fix this:")
        print("1. Go to your repository Settings")
        print("2. Scroll down to 'Pages' section")
        print("3. Set Source to 'Deploy from a branch'")
        print("4. Select 'gh-pages' branch and '/ (root)' folder")
        print("5. OR set Source to 'GitHub Actions'")
        
    else:
        print(f"❌ Error checking Pages configuration: {info.get('message', 'Unknown error')}")

def main():
    """Main function to check GitHub Pages settings."""
    
    # Repository information
    owner = "jingminzhang"
    repo = "cauldron"
    
    # Get token from environment (optional)
    token = os.getenv('GITHUB_TOKEN')
    
    print(f"Checking GitHub Pages settings for {owner}/{repo}...")
    
    info = check_github_pages_settings(owner, repo, token)
    print_pages_info(info)
    
    print("\n" + "=" * 50)
    print("💡 Next Steps:")
    print("1. If Pages is not configured, enable it in repository settings")
    print("2. If source is wrong, change it to 'gh-pages' branch or 'GitHub Actions'")
    print("3. Run the GitHub Actions workflow to deploy docs")
    print("4. Check the Actions tab for deployment status")

if __name__ == "__main__":
    main()
