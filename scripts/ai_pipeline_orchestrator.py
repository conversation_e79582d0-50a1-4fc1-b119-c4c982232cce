#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI流水线编排器 - Augment Agent创建
智能调度Claude Code和Rovodev，实现高效的AI工具协作
"""

import subprocess
import json
import time
import asyncio
from datetime import datetime
from enum import Enum
from dataclasses import dataclass
from typing import List, Dict, Optional, Callable
import threading
import queue

class TaskType(Enum):
    """任务类型枚举"""
    QUICK_FIX = "quick_fix"          # 快速修复 -> Claude Code
    CODE_SNIPPET = "code_snippet"    # 代码片段 -> Claude Code
    API_TEST = "api_test"            # API测试 -> Claude Code
    DEBUG = "debug"                  # 调试 -> Claude Code

    ARCHITECTURE = "architecture"    # 架构设计 -> Rovodev
    ANALYSIS = "analysis"           # 深度分析 -> Rovodev
    COMPLEX_SCRIPT = "complex_script" # 复杂脚本 -> Rovodev
    INTEGRATION = "integration"      # 系统集成 -> Rovodev

class TaskPriority(Enum):
    """任务优先级"""
    URGENT = 1      # 紧急
    HIGH = 2        # 高
    NORMAL = 3      # 普通
    LOW = 4         # 低

@dataclass
class Task:
    """任务数据结构"""
    id: str
    type: TaskType
    priority: TaskPriority
    description: str
    prompt: str
    dependencies: List[str] = None
    timeout: int = 300  # 超时时间(秒)
    retry_count: int = 0
    max_retries: int = 2
    created_at: datetime = None
    assigned_to: str = None
    status: str = "pending"  # pending, running, completed, failed
    result: str = None

    def __post_init__(self):
        if self.created_at is None:
            self.created_at = datetime.now()
        if self.dependencies is None:
            self.dependencies = []

class AIWorker:
    """AI工具包装器"""
    def __init__(self, name: str, command: str, capabilities: List[TaskType]):
        self.name = name
        self.command = command
        self.capabilities = capabilities
        self.is_busy = False
        self.current_task = None
        self.process = None

    def can_handle(self, task_type: TaskType) -> bool:
        """检查是否能处理指定类型的任务"""
        return task_type in self.capabilities

    def is_available(self) -> bool:
        """检查是否可用"""
        return not self.is_busy

class AIPipelineOrchestrator:
    """AI流水线编排器"""

    def __init__(self):
        self.task_queue = queue.PriorityQueue()
        self.completed_tasks = {}
        self.failed_tasks = {}
        self.running = False

        # 初始化AI工具
        self.workers = {
            "claude": AIWorker(
                name="Claude Code",
                command="claude",
                capabilities=[
                    TaskType.QUICK_FIX,
                    TaskType.CODE_SNIPPET,
                    TaskType.API_TEST,
                    TaskType.DEBUG
                ]
            ),
            "rovodev": AIWorker(
                name="Rovodev",
                command="acli rovodev run --shadow",  # 使用shadow模式避免权限提示
                capabilities=[
                    TaskType.ARCHITECTURE,
                    TaskType.ANALYSIS,
                    TaskType.COMPLEX_SCRIPT,
                    TaskType.INTEGRATION
                ]
            )
        }

        # 任务路由规则
        self.routing_rules = {
            TaskType.QUICK_FIX: "claude",
            TaskType.CODE_SNIPPET: "claude",
            TaskType.API_TEST: "claude",
            TaskType.DEBUG: "claude",
            TaskType.ARCHITECTURE: "rovodev",
            TaskType.ANALYSIS: "rovodev",
            TaskType.COMPLEX_SCRIPT: "rovodev",
            TaskType.INTEGRATION: "rovodev"
        }

    def add_task(self, task: Task) -> str:
        """添加任务到队列"""
        # 优先级队列：数字越小优先级越高
        priority = task.priority.value
        self.task_queue.put((priority, task.created_at, task))
        print(f"📋 任务已添加: {task.id} ({task.type.value}) - 优先级: {task.priority.name}")
        return task.id

    def get_assigned_worker(self, task: Task) -> Optional[str]:
        """获取任务分配的工具"""
        return self.routing_rules.get(task.type)

    def execute_task(self, task: Task, worker_name: str) -> bool:
        """执行单个任务"""
        worker = self.workers[worker_name]

        if not worker.is_available():
            return False

        worker.is_busy = True
        worker.current_task = task
        task.assigned_to = worker_name
        task.status = "running"

        print(f"🚀 开始执行任务: {task.id} -> {worker.name}")

        try:
            # 构建命令
            if worker_name == "claude":
                cmd = f'{worker.command} --print "{task.prompt}"'
            else:  # rovodev
                # Rovodev需要交互式输入
                cmd = worker.command

            # 执行命令
            if worker_name == "claude":
                result = subprocess.run(
                    cmd,
                    shell=True,
                    capture_output=True,
                    text=True,
                    timeout=task.timeout,
                    cwd="/Users/<USER>/cauldron"
                )

                if result.returncode == 0:
                    task.result = result.stdout
                    task.status = "completed"
                    self.completed_tasks[task.id] = task
                    print(f"✅ 任务完成: {task.id}")
                    return True
                else:
                    task.result = result.stderr
                    task.status = "failed"
                    print(f"❌ 任务失败: {task.id} - {result.stderr}")
                    return False
            else:
                # Rovodev需要特殊处理（交互式）
                return self._execute_rovodev_task(task, worker)

        except subprocess.TimeoutExpired:
            task.status = "failed"
            task.result = "任务超时"
            print(f"⏰ 任务超时: {task.id}")
            return False
        except Exception as e:
            task.status = "failed"
            task.result = str(e)
            print(f"💥 任务异常: {task.id} - {e}")
            return False
        finally:
            worker.is_busy = False
            worker.current_task = None

    def _execute_rovodev_task(self, task: Task, worker: AIWorker) -> bool:
        """执行Rovodev任务（使用自动化包装器）"""
        try:
            # 导入包装器
            from rovodev_automation_wrapper import run_rovodev_task

            print(f"🤖 使用自动化包装器执行Rovodev任务: {task.id}")

            # 使用包装器执行任务
            success, result = run_rovodev_task(task.prompt, task.timeout)

            if success:
                task.result = result
                task.status = "completed"
                self.completed_tasks[task.id] = task
                print(f"✅ Rovodev任务完成: {task.id}")
                return True
            else:
                task.result = result
                task.status = "failed"
                print(f"❌ Rovodev任务失败: {task.id} - {result}")
                return False

        except ImportError:
            # 如果包装器不可用，回退到原始方法
            print(f"⚠️ 包装器不可用，使用原始方法执行: {task.id}")
            return self._execute_rovodev_fallback(task, worker)
        except Exception as e:
            task.status = "failed"
            task.result = str(e)
            print(f"💥 Rovodev任务异常: {task.id} - {e}")
            return False

    def _execute_rovodev_fallback(self, task: Task, worker: AIWorker) -> bool:
        """Rovodev回退执行方法"""
        try:
            # 使用shadow模式和非交互式执行
            cmd = f"echo '{task.prompt}' | {worker.command} --shadow"

            result = subprocess.run(
                cmd,
                shell=True,
                capture_output=True,
                text=True,
                timeout=task.timeout,
                cwd="/Users/<USER>/cauldron"
            )

            if result.returncode == 0:
                task.result = result.stdout
                task.status = "completed"
                self.completed_tasks[task.id] = task
                print(f"✅ Rovodev回退任务完成: {task.id}")
                return True
            else:
                task.result = result.stderr
                task.status = "failed"
                print(f"❌ Rovodev回退任务失败: {task.id}")
                return False

        except Exception as e:
            task.status = "failed"
            task.result = str(e)
            print(f"💥 Rovodev回退任务异常: {task.id} - {e}")
            return False

    def process_queue(self):
        """处理任务队列"""
        while self.running:
            try:
                if not self.task_queue.empty():
                    priority, created_at, task = self.task_queue.get(timeout=1)

                    # 检查依赖
                    if not self._check_dependencies(task):
                        # 重新放回队列
                        self.task_queue.put((priority, created_at, task))
                        time.sleep(1)
                        continue

                    # 分配工具
                    worker_name = self.get_assigned_worker(task)
                    if not worker_name:
                        print(f"⚠️ 无法分配工具给任务: {task.id}")
                        self.failed_tasks[task.id] = task
                        continue

                    # 执行任务
                    success = self.execute_task(task, worker_name)

                    # 重试机制
                    if not success and task.retry_count < task.max_retries:
                        task.retry_count += 1
                        task.status = "pending"
                        print(f"🔄 重试任务: {task.id} (第{task.retry_count}次)")
                        self.task_queue.put((priority, created_at, task))
                    elif not success:
                        self.failed_tasks[task.id] = task
                        print(f"💀 任务最终失败: {task.id}")

                time.sleep(0.5)  # 避免CPU占用过高

            except queue.Empty:
                continue
            except Exception as e:
                print(f"💥 队列处理异常: {e}")

    def _check_dependencies(self, task: Task) -> bool:
        """检查任务依赖是否满足"""
        for dep_id in task.dependencies:
            if dep_id not in self.completed_tasks:
                return False
        return True

    def start(self):
        """启动流水线"""
        self.running = True
        self.worker_thread = threading.Thread(target=self.process_queue)
        self.worker_thread.daemon = True
        self.worker_thread.start()
        print("🎮 AI流水线已启动")

    def stop(self):
        """停止流水线"""
        self.running = False
        if hasattr(self, 'worker_thread'):
            self.worker_thread.join()
        print("🛑 AI流水线已停止")

    def get_status(self) -> Dict:
        """获取流水线状态"""
        return {
            "running": self.running,
            "queue_size": self.task_queue.qsize(),
            "completed_tasks": len(self.completed_tasks),
            "failed_tasks": len(self.failed_tasks),
            "workers": {
                name: {
                    "busy": worker.is_busy,
                    "current_task": worker.current_task.id if worker.current_task else None
                }
                for name, worker in self.workers.items()
            }
        }

    def create_cauldron_tasks(self) -> List[str]:
        """创建Cauldron项目的示例任务流"""
        tasks = [
            Task(
                id="analyze_n8n_structure",
                type=TaskType.ANALYSIS,
                priority=TaskPriority.HIGH,
                description="分析N8N工作流结构",
                prompt="请深度分析当前N8N配置，提出优化建议和集成方案"
            ),
            Task(
                id="test_webhook_endpoints",
                type=TaskType.API_TEST,
                priority=TaskPriority.URGENT,
                description="测试N8N webhook端点",
                prompt="测试N8N的webhook端点连接性和响应状态",
                dependencies=["analyze_n8n_structure"]
            ),
            Task(
                id="create_ai_agent_prototype",
                type=TaskType.CODE_SNIPPET,
                priority=TaskPriority.NORMAL,
                description="创建AI智能体原型",
                prompt="为三清八仙中的一个角色创建基础的AI智能体代码框架"
            ),
            Task(
                id="design_vector_db_integration",
                type=TaskType.ARCHITECTURE,
                priority=TaskPriority.HIGH,
                description="设计向量数据库集成方案",
                prompt="设计Zilliz/Milvus向量数据库与AutoGen的集成架构"
            )
        ]

        task_ids = []
        for task in tasks:
            task_id = self.add_task(task)
            task_ids.append(task_id)

        return task_ids

# 使用示例
def main():
    """主函数 - 演示AI流水线使用"""
    orchestrator = AIPipelineOrchestrator()

    print("🎭 启动Cauldron AI流水线...")
    orchestrator.start()

    # 创建示例任务
    task_ids = orchestrator.create_cauldron_tasks()
    print(f"📋 已创建 {len(task_ids)} 个任务")

    # 监控执行
    try:
        while True:
            status = orchestrator.get_status()
            print(f"\n📊 流水线状态: {json.dumps(status, indent=2, ensure_ascii=False)}")

            if status["queue_size"] == 0 and not any(w["busy"] for w in status["workers"].values()):
                print("🎉 所有任务已完成!")
                break

            time.sleep(5)

    except KeyboardInterrupt:
        print("\n👋 用户中断")
    finally:
        orchestrator.stop()

if __name__ == "__main__":
    main()