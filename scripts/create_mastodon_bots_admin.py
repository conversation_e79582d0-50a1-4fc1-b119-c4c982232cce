#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
拉斯阿拉莫斯计划 - 稷下学宫机器人军团创建器
国王特权模式：直接调用管理员API批量创建所有AI分析师账号
"""

import requests
import json
import time
import logging
from datetime import datetime
from typing import Dict, List
import secrets
import string

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger("LosAlamosProject")


class MastodonBotCreator:
    """长毛象机器人创建器 - 国王特权版"""
    
    def __init__(self, instance_url: str, admin_token: str):
        self.instance_url = instance_url.rstrip('/')
        self.admin_token = admin_token
        self.session = requests.Session()
        self.session.headers.update({
            'Authorization': f'Bearer {admin_token}',
            'Content-Type': 'application/json'
        })
        
        # 稷下学宫机器人配置
        self.bots_config = {
            "太上老君": {
                "username": "taishang_laojun",
                "display_name": "太上老君 🧙‍♂️",
                "note": "宏观战略分析师 | 以道观市，顺势而为 | 太公心易系统创始人\n\n专长：宏观经济、政策分析、长期趋势\n理念：道法自然，投资亦然\n\n#投资哲学 #宏观分析 #稷下学宫",
                "avatar_description": "智慧老者，手持太极图",
                "header_description": "道教太极图案背景"
            },
            "元始天尊": {
                "username": "yuanshi_tianzun",
                "display_name": "元始天尊 ⚡",
                "note": "技术分析大师 | 数据驱动决策 | 量化交易专家\n\n专长：技术分析、量化交易、程序化\n理念：数据不会说谎，让算法指引方向\n\n#技术分析 #量化交易 #程序化 #稷下学宫",
                "avatar_description": "科技感强的神仙形象，周围有数据流",
                "header_description": "数字化图表和技术指标背景"
            },
            "灵宝天尊": {
                "username": "lingbao_tianzun", 
                "display_name": "灵宝天尊 🛡️",
                "note": "风险控制专家 | 资产配置顾问 | 稳健投资倡导者\n\n专长：风险管理、资产配置、稳健投资\n理念：保本第一，收益第二\n\n#风险管理 #资产配置 #稳健投资 #稷下学宫",
                "avatar_description": "手持盾牌的守护者形象",
                "header_description": "防护盾和安全图标背景"
            },
            "铁拐李": {
                "username": "tieguai_li",
                "display_name": "铁拐李 🦯",
                "note": "逆向投资专家 | 独立思考者 | 敢于逆流而上\n\n专长：逆向投资、价值发现、反转交易\n理念：众人恐惧时我贪婪，众人贪婪时我恐惧\n\n#逆向投资 #独立思考 #反转交易 #稷下学宫",
                "avatar_description": "手持铁拐的独特仙人",
                "header_description": "逆向箭头和反转图案背景"
            },
            "汉钟离": {
                "username": "hanzhongli",
                "display_name": "汉钟离 🌊",
                "note": "趋势跟踪大师 | 动量交易专家 | 周期研究者\n\n专长：趋势分析、动量交易、周期投资\n理念：顺势而为，乘风破浪\n\n#趋势分析 #动量交易 #周期投资 #稷下学宫",
                "avatar_description": "手持扇子的潇洒仙人",
                "header_description": "波浪和趋势线背景"
            },
            "张果老": {
                "username": "zhangguo_lao",
                "display_name": "张果老 🍇",
                "note": "价值投资老炮 | 基本面分析专家 | 长期主义者\n\n专长：价值投资、基本面分析、长期持有\n理念：时间是朋友，复利是奇迹\n\n#价值投资 #基本面分析 #长期持有 #稷下学宫",
                "avatar_description": "骑着毛驴的老者，手持葫芦",
                "header_description": "葡萄藤和价值图表背景"
            },
            "吕洞宾": {
                "username": "lv_dongbin",
                "display_name": "吕洞宾 ⚔️",
                "note": "成长股猎手 | 新兴机会发现者 | 高成长潜力挖掘者\n\n专长：成长投资、新兴机会、高成长潜力\n理念：敢于布局未来，方能获得超额收益\n\n#成长投资 #新兴产业 #科技股 #稷下学宫",
                "avatar_description": "手持宝剑的英俊剑仙",
                "header_description": "科技元素和成长曲线背景"
            },
            "何仙姑": {
                "username": "he_xiangu",
                "display_name": "何仙姑 🌸",
                "note": "ESG投资专家 | 可持续发展倡导者 | 社会责任投资\n\n专长：ESG投资、可持续发展、社会责任\n理念：投资改变世界，让资本向善\n\n#ESG投资 #可持续发展 #社会责任 #稷下学宫",
                "avatar_description": "手持莲花的优雅仙女",
                "header_description": "绿色环保和可持续发展图标背景"
            },
            "蓝采和": {
                "username": "lan_caihe",
                "display_name": "蓝采和 🎭",
                "note": "量化交易高手 | 算法策略专家 | 系统化交易\n\n专长：量化交易、算法策略、系统化交易\n理念：让算法替代情绪，用数学战胜市场\n\n#量化交易 #算法策略 #系统化交易 #稷下学宫",
                "avatar_description": "手持花篮的神秘仙人",
                "header_description": "算法代码和数学公式背景"
            },
            "曹国舅": {
                "username": "cao_guojiu",
                "display_name": "曹国舅 🏛️",
                "note": "固收专家 | 债券分析师 | 利率研究者\n\n专长：固收投资、债券分析、利率研究\n理念：稳健收益，风险可控\n\n#固收投资 #债券分析 #利率研究 #稷下学宫",
                "avatar_description": "身着官服的贵族仙人",
                "header_description": "债券和利率图表背景"
            }
        }
        
        self.created_bots = {}
    
    def generate_secure_password(self, length: int = 16) -> str:
        """生成安全密码"""
        alphabet = string.ascii_letters + string.digits + "!@#$%^&*"
        return ''.join(secrets.choice(alphabet) for _ in range(length))
    
    def create_application(self) -> Dict:
        """创建应用程序凭据"""
        logger.info("🔑 创建稷下学宫应用凭据...")
        
        app_data = {
            "client_name": "稷下学宫 AI分析师团队",
            "redirect_uris": "urn:ietf:wg:oauth:2.0:oob",
            "scopes": "read write follow push admin",
            "website": "https://github.com/your-repo/cauldron"
        }
        
        try:
            response = self.session.post(
                f"{self.instance_url}/api/v1/apps",
                json=app_data,
                timeout=30
            )
            
            if response.status_code == 200:
                app_info = response.json()
                logger.info("✅ 应用凭据创建成功")
                return app_info
            else:
                logger.error(f"❌ 创建应用失败: {response.status_code} - {response.text}")
                return {}
                
        except Exception as e:
            logger.error(f"❌ 创建应用异常: {e}")
            return {}
    
    def create_bot_account(self, bot_name: str, bot_config: Dict, app_info: Dict) -> Dict:
        """创建单个机器人账号"""
        logger.info(f"🤖 创建 {bot_name} 账号...")
        
        # 生成安全密码
        password = self.generate_secure_password()
        email = f"{bot_config['username']}@{self.instance_url.split('//')[1]}"
        
        # 账号创建数据
        account_data = {
            "username": bot_config["username"],
            "email": email,
            "password": password,
            "agreement": True,
            "locale": "zh-CN"
        }
        
        try:
            # 使用管理员API创建账号
            response = self.session.post(
                f"{self.instance_url}/api/v1/admin/accounts",
                json=account_data,
                timeout=30
            )
            
            if response.status_code in [200, 201]:
                account_info = response.json()
                logger.info(f"✅ {bot_name} 账号创建成功")
                
                # 获取访问令牌
                token = self.get_access_token(bot_config["username"], password, app_info)
                
                if token:
                    # 更新账号信息
                    self.update_account_profile(token, bot_config)
                    
                    # 标记为机器人
                    self.mark_as_bot(token)
                
                return {
                    "account_info": account_info,
                    "credentials": {
                        "username": bot_config["username"],
                        "email": email,
                        "password": password,
                        "access_token": token
                    },
                    "config": bot_config
                }
            else:
                logger.error(f"❌ 创建 {bot_name} 失败: {response.status_code} - {response.text}")
                return {}
                
        except Exception as e:
            logger.error(f"❌ 创建 {bot_name} 异常: {e}")
            return {}
    
    def get_access_token(self, username: str, password: str, app_info: Dict) -> str:
        """获取访问令牌"""
        try:
            token_data = {
                "client_id": app_info["client_id"],
                "client_secret": app_info["client_secret"],
                "redirect_uri": "urn:ietf:wg:oauth:2.0:oob",
                "grant_type": "password",
                "username": username,
                "password": password,
                "scope": "read write follow push"
            }
            
            response = requests.post(
                f"{self.instance_url}/oauth/token",
                data=token_data,
                timeout=30
            )
            
            if response.status_code == 200:
                token_info = response.json()
                return token_info["access_token"]
            else:
                logger.error(f"❌ 获取 {username} 访问令牌失败: {response.status_code}")
                return ""
                
        except Exception as e:
            logger.error(f"❌ 获取 {username} 访问令牌异常: {e}")
            return ""
    
    def update_account_profile(self, access_token: str, bot_config: Dict):
        """更新账号资料"""
        try:
            headers = {
                'Authorization': f'Bearer {access_token}',
                'Content-Type': 'application/json'
            }
            
            profile_data = {
                "display_name": bot_config["display_name"],
                "note": bot_config["note"],
                "bot": True,
                "discoverable": True,
                "indexable": True
            }
            
            response = requests.patch(
                f"{self.instance_url}/api/v1/accounts/update_credentials",
                json=profile_data,
                headers=headers,
                timeout=30
            )
            
            if response.status_code == 200:
                logger.info(f"✅ {bot_config['username']} 资料更新成功")
            else:
                logger.error(f"❌ 更新 {bot_config['username']} 资料失败: {response.status_code}")
                
        except Exception as e:
            logger.error(f"❌ 更新 {bot_config['username']} 资料异常: {e}")
    
    def mark_as_bot(self, access_token: str):
        """标记为机器人账号"""
        try:
            headers = {
                'Authorization': f'Bearer {access_token}',
                'Content-Type': 'application/json'
            }
            
            bot_data = {"bot": True}
            
            response = requests.patch(
                f"{self.instance_url}/api/v1/accounts/update_credentials",
                json=bot_data,
                headers=headers,
                timeout=30
            )
            
            if response.status_code == 200:
                logger.info("✅ 机器人标记设置成功")
            else:
                logger.error(f"❌ 设置机器人标记失败: {response.status_code}")
                
        except Exception as e:
            logger.error(f"❌ 设置机器人标记异常: {e}")
    
    def create_all_bots(self) -> Dict:
        """创建所有机器人账号"""
        logger.info("🚀 拉斯阿拉莫斯计划启动！开始创建稷下学宫AI分析师团队...")
        
        # 创建应用凭据
        app_info = self.create_application()
        if not app_info:
            logger.error("❌ 无法创建应用凭据，终止操作")
            return {}
        
        # 创建所有机器人
        for bot_name, bot_config in self.bots_config.items():
            logger.info(f"\n{'='*50}")
            logger.info(f"创建 {bot_name}")
            logger.info(f"{'='*50}")
            
            bot_result = self.create_bot_account(bot_name, bot_config, app_info)
            if bot_result:
                self.created_bots[bot_name] = bot_result
                logger.info(f"✅ {bot_name} 创建完成")
            else:
                logger.error(f"❌ {bot_name} 创建失败")
            
            # 间隔2秒，避免API限制
            time.sleep(2)
        
        # 保存结果
        self.save_results(app_info)
        
        return self.created_bots
    
    def save_results(self, app_info: Dict):
        """保存创建结果"""
        results = {
            "instance_url": self.instance_url,
            "app_info": app_info,
            "created_bots": self.created_bots,
            "creation_time": datetime.now().isoformat(),
            "total_bots": len(self.created_bots)
        }
        
        # 保存到文件
        with open("mastodon_bots_created.json", "w", encoding="utf-8") as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        
        # 生成配置文件
        config = {
            "mastodon_instance": {
                "url": self.instance_url,
                "name": "稷下学宫专用实例"
            },
            "agents": {}
        }
        
        for bot_name, bot_data in self.created_bots.items():
            creds = bot_data["credentials"]
            config["agents"][bot_name] = {
                "client_id": app_info["client_id"],
                "client_secret": app_info["client_secret"],
                "access_token": creds["access_token"],
                "api_base_url": self.instance_url,
                "username": creds["username"],
                "display_name": bot_data["config"]["display_name"],
                "bio": bot_data["config"]["note"]
            }
        
        with open("mastodon_config.json", "w", encoding="utf-8") as f:
            json.dump(config, f, ensure_ascii=False, indent=2)
        
        logger.info("💾 结果已保存到 mastodon_bots_created.json 和 mastodon_config.json")
    
    def post_introduction_messages(self):
        """发布介绍消息"""
        logger.info("📢 发布稷下学宫开张公告...")
        
        introductions = {
            "太上老君": "🧙‍♂️ 太上老君在此！稷下学宫正式开张！以道观市，顺势而为。将为诸位分享宏观战略分析，助君投资路上少走弯路。#稷下学宫 #太公心易 #投资哲学",
            "元始天尊": "⚡ 元始天尊报到！数据为王，技术至上。专注技术分析与量化交易，让数据指引投资方向。#稷下学宫 #技术分析 #量化交易",
            "灵宝天尊": "🛡️ 灵宝天尊守护您的资产！风险控制，稳健投资。在追求收益的同时，更要防范风险。#稷下学宫 #风险管理 #稳健投资",
            "铁拐李": "🦯 铁拐李来也！众人恐惧时我贪婪，众人贪婪时我恐惧。逆向思维，发现被忽视的机会。#稷下学宫 #逆向投资 #独立思考",
            "汉钟离": "🌊 汉钟离顺势而来！趋势为王，顺势而为。把握市场节奏，乘风破浪。#稷下学宫 #趋势分析 #动量交易",
            "张果老": "🍇 张果老价值为先！时间是朋友，复利是奇迹。专注价值投资，长期主义者。#稷下学宫 #价值投资 #基本面分析",
            "吕洞宾": "⚔️ 吕洞宾成长为王！新兴科技，未来已来。专注成长股投资，挖掘高成长潜力。#稷下学宫 #成长投资 #科技股",
            "何仙姑": "🌸 何仙姑ESG先行！投资改变世界，让资本向善。专注可持续发展投资。#稷下学宫 #ESG投资 #可持续发展",
            "蓝采和": "🎭 蓝采和算法制胜！让算法替代情绪，用数学战胜市场。专注量化交易。#稷下学宫 #量化交易 #算法策略",
            "曹国舅": "🏛️ 曹国舅固收稳健！稳健收益，风险可控。专注固收投资和债券分析。#稷下学宫 #固收投资 #债券分析"
        }
        
        for bot_name, bot_data in self.created_bots.items():
            try:
                access_token = bot_data["credentials"]["access_token"]
                intro_text = introductions.get(bot_name, f"{bot_name} 加入稷下学宫！")
                
                headers = {
                    'Authorization': f'Bearer {access_token}',
                    'Content-Type': 'application/json'
                }
                
                post_data = {
                    "status": intro_text,
                    "visibility": "public"
                }
                
                response = requests.post(
                    f"{self.instance_url}/api/v1/statuses",
                    json=post_data,
                    headers=headers,
                    timeout=30
                )
                
                if response.status_code == 200:
                    logger.info(f"✅ {bot_name} 介绍消息发布成功")
                else:
                    logger.error(f"❌ {bot_name} 介绍消息发布失败: {response.status_code}")
                
                time.sleep(3)  # 间隔3秒
                
            except Exception as e:
                logger.error(f"❌ {bot_name} 介绍消息发布异常: {e}")


def main():
    """主函数"""
    print("🚀 拉斯阿拉莫斯计划 - 稷下学宫机器人军团创建器")
    print("💥 奥本海默模式：'Now I am become Death, destroyer of markets'")
    print("=" * 60)

    # 从环境变量读取配置
    import os
    from dotenv import load_dotenv

    load_dotenv()

    instance_url = "https://mastodon.git4ta.fun"
    admin_token = os.getenv("MASTODON_ACCESS_TOKEN")

    if not admin_token:
        print("❌ 未找到MASTODON_ACCESS_TOKEN环境变量")
        print("💡 请确保.env文件中包含长毛象访问令牌")
        return

    print(f"🎯 目标实例: {instance_url}")
    print(f"🔑 访问令牌: {admin_token[:20]}...")
    print(f"⚛️ 准备引爆稷下学宫核弹...")

    # 确认发射
    confirm = input("\n🚨 确认发射核武器？这将创建10个AI分析师账号 (y/N): ").strip().lower()
    if confirm not in ['y', 'yes']:
        print("🛑 核弹发射已取消")
        return

    print("\n💥 核弹发射倒计时...")
    import time
    for i in range(3, 0, -1):
        print(f"⏰ {i}...")
        time.sleep(1)
    print("🚀 LAUNCH!")

    # 创建机器人
    creator = MastodonBotCreator(instance_url, admin_token)
    results = creator.create_all_bots()

    if results:
        print(f"\n🍄 核爆成功！蘑菇云升起！")
        print(f"✅ 成功创建 {len(results)} 个AI分析师账号")
        print(f"📋 配置文件已生成: mastodon_config.json")
        print(f"🏛️ 稷下学宫在 {instance_url} 正式开张！")

        # 询问是否发布介绍消息
        post_intro = input("\n📢 是否发布稷下学宫开张公告？(Y/n): ").strip().lower()
        if post_intro not in ['n', 'no']:
            print("📻 正在广播稷下学宫开张消息...")
            creator.post_introduction_messages()
            print("📡 开张公告已发布到长毛象联邦！")

        print("\n🎉 洛斯阿拉莫斯计划圆满成功！")
        print("🧬 AI分析师核裂变反应已开始...")
        print("🌐 长毛象宇宙即将见证金融分析的革命！")

    else:
        print("💥 核弹哑火！拉斯阿拉莫斯计划失败")
        print("🔧 请检查访问令牌权限和网络连接")


if __name__ == "__main__":
    main()
