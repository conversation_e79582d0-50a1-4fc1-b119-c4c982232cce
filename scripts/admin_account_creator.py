#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
管理员账号创建器 - 修复版
使用正确的管理员API端点
"""

import os
import json
import time
import logging
import requests
from datetime import datetime
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger("AdminAccountCreator")


class AdminAccountCreator:
    """管理员账号创建器"""
    
    def __init__(self):
        self.instance_url = "https://mastodon.git4ta.fun"
        self.access_token = os.getenv("MASTODON_ACCESS_TOKEN")
        
        if not self.access_token:
            raise ValueError("❌ 缺少MASTODON_ACCESS_TOKEN！")
        
        self.session = requests.Session()
        self.session.headers.update({
            'Authorization': f'Bearer {self.access_token}',
            'Content-Type': 'application/json'
        })
        
        # AI分析师配置
        self.analysts = {
            "太上老君": {
                "username": "taishang_laojun",
                "email": "<EMAIL>",
                "display_name": "太上老君 🧙‍♂️",
                "note": "宏观战略分析师 | 以道观市，顺势而为 | 太公心易系统创始人\n\n专长：宏观经济、政策分析、长期趋势\n理念：道法自然，投资亦然\n\n#投资哲学 #宏观分析 #稷下学宫"
            },
            "元始天尊": {
                "username": "yuanshi_tianzun",
                "email": "<EMAIL>", 
                "display_name": "元始天尊 ⚡",
                "note": "技术分析大师 | 数据驱动决策 | 量化交易专家\n\n专长：技术分析、量化交易、程序化\n理念：数据不会说谎，让算法指引方向\n\n#技术分析 #量化交易 #程序化 #稷下学宫"
            },
            "铁拐李": {
                "username": "tieguai_li",
                "email": "<EMAIL>",
                "display_name": "铁拐李 🦯",
                "note": "逆向投资专家 | 独立思考者 | 敢于逆流而上\n\n专长：逆向投资、价值发现、反转交易\n理念：众人恐惧时我贪婪，众人贪婪时我恐惧\n\n#逆向投资 #独立思考 #反转交易 #稷下学宫"
            }
        }
    
    def test_admin_permissions(self):
        """测试管理员权限"""
        try:
            # 测试获取管理员账号列表
            response = self.session.get(
                f"{self.instance_url}/api/v1/admin/accounts",
                params={"limit": 1},
                timeout=30
            )
            
            logger.info(f"管理员权限测试: {response.status_code}")
            if response.status_code == 200:
                logger.info("✅ 管理员权限确认")
                return True
            else:
                logger.error(f"❌ 管理员权限测试失败: {response.text}")
                return False
                
        except Exception as e:
            logger.error(f"❌ 权限测试异常: {e}")
            return False
    
    def create_account_direct(self, analyst_name: str, config: dict):
        """尝试不同的账号创建方法"""
        logger.info(f"🎯 创建 {analyst_name} ({config['username']})")
        
        # 方法1: 尝试管理员创建账号API
        success = self.try_admin_create(config)
        if success:
            return True
        
        # 方法2: 尝试邀请API
        success = self.try_invite_create(config)
        if success:
            return True
        
        # 方法3: 检查账号是否已存在
        if self.check_account_exists(config['username']):
            logger.info(f"✅ {analyst_name} 账号已存在，跳过创建")
            return True
        
        logger.error(f"❌ {analyst_name} 创建失败")
        return False
    
    def try_admin_create(self, config: dict) -> bool:
        """尝试管理员创建账号"""
        try:
            # 使用不同的API端点
            endpoints = [
                "/api/v1/admin/accounts",
                "/api/v2/admin/accounts", 
                "/admin/accounts"
            ]
            
            account_data = {
                "username": config["username"],
                "email": config["email"],
                "password": "TempPassword123!@#",
                "confirmed": True,
                "approved": True,
                "locale": "zh-CN"
            }
            
            for endpoint in endpoints:
                try:
                    response = self.session.post(
                        f"{self.instance_url}{endpoint}",
                        json=account_data,
                        timeout=30
                    )
                    
                    logger.info(f"尝试端点 {endpoint}: {response.status_code}")
                    
                    if response.status_code in [200, 201]:
                        logger.info(f"✅ 通过 {endpoint} 创建成功")
                        return True
                    elif response.status_code == 422:
                        # 可能是账号已存在
                        logger.info(f"⚠️ 账号可能已存在: {response.text}")
                        return True
                    else:
                        logger.warning(f"⚠️ {endpoint} 失败: {response.status_code} - {response.text}")
                        
                except Exception as e:
                    logger.warning(f"⚠️ {endpoint} 异常: {e}")
                    continue
            
            return False
            
        except Exception as e:
            logger.error(f"❌ 管理员创建异常: {e}")
            return False
    
    def try_invite_create(self, config: dict) -> bool:
        """尝试邀请创建"""
        try:
            invite_data = {
                "max_uses": 1,
                "expires_in": 86400,  # 24小时
                "autofollow": False
            }
            
            # 创建邀请链接
            response = self.session.post(
                f"{self.instance_url}/api/v1/invites",
                json=invite_data,
                timeout=30
            )
            
            if response.status_code == 200:
                invite_info = response.json()
                logger.info(f"✅ 为 {config['username']} 创建邀请链接")
                logger.info(f"🔗 邀请链接: {invite_info.get('url', 'N/A')}")
                return True
            else:
                logger.warning(f"⚠️ 邀请创建失败: {response.status_code}")
                return False
                
        except Exception as e:
            logger.error(f"❌ 邀请创建异常: {e}")
            return False
    
    def check_account_exists(self, username: str) -> bool:
        """检查账号是否已存在"""
        try:
            # 搜索账号
            response = self.session.get(
                f"{self.instance_url}/api/v2/search",
                params={"q": f"@{username}", "type": "accounts", "limit": 1},
                timeout=30
            )
            
            if response.status_code == 200:
                results = response.json()
                accounts = results.get("accounts", [])
                
                for account in accounts:
                    if account.get("username") == username:
                        logger.info(f"✅ 找到现有账号: @{username}")
                        return True
                
                return False
            else:
                logger.warning(f"⚠️ 搜索账号失败: {response.status_code}")
                return False
                
        except Exception as e:
            logger.error(f"❌ 搜索账号异常: {e}")
            return False
    
    def get_account_info(self, username: str) -> dict:
        """获取账号信息"""
        try:
            response = self.session.get(
                f"{self.instance_url}/api/v1/accounts/lookup",
                params={"acct": username},
                timeout=30
            )
            
            if response.status_code == 200:
                return response.json()
            else:
                logger.warning(f"⚠️ 获取 {username} 信息失败: {response.status_code}")
                return {}
                
        except Exception as e:
            logger.error(f"❌ 获取 {username} 信息异常: {e}")
            return {}
    
    def run_creation_process(self):
        """运行创建流程"""
        logger.info("🚀 开始管理员账号创建流程...")
        
        # 测试权限
        if not self.test_admin_permissions():
            logger.error("❌ 管理员权限验证失败")
            return False
        
        success_count = 0
        results = {}
        
        for analyst_name, config in self.analysts.items():
            logger.info(f"\n{'='*50}")
            logger.info(f"处理 {analyst_name}")
            logger.info(f"{'='*50}")
            
            if self.create_account_direct(analyst_name, config):
                success_count += 1
                
                # 获取账号信息
                account_info = self.get_account_info(config['username'])
                results[analyst_name] = {
                    "username": config['username'],
                    "account_info": account_info,
                    "config": config
                }
                
                logger.info(f"✅ {analyst_name} 处理完成")
            else:
                logger.error(f"❌ {analyst_name} 处理失败")
            
            time.sleep(2)
        
        # 保存结果
        if results:
            with open("created_accounts.json", "w", encoding="utf-8") as f:
                json.dump(results, f, ensure_ascii=False, indent=2)
            logger.info("💾 结果已保存到 created_accounts.json")
        
        logger.info(f"\n🎉 创建流程完成！成功处理 {success_count}/{len(self.analysts)} 个账号")
        return success_count > 0


def main():
    """主函数"""
    print("🔧 管理员账号创建器 - 修复版")
    print("=" * 50)
    
    try:
        creator = AdminAccountCreator()
        success = creator.run_creation_process()
        
        if success:
            print("\n🎉 任务完成！请检查结果")
        else:
            print("\n💥 任务失败！可能需要手动创建账号")
            
    except Exception as e:
        print(f"❌ 程序异常: {e}")


if __name__ == "__main__":
    main()
