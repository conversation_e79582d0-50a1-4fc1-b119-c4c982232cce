#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI流水线命令行界面 - Augment Agent创建
简单易用的AI工具协调命令行工具
"""

import argparse
import json
import sys
import time
from pathlib import Path

# 导入我们的编排器
from ai_pipeline_orchestrator import AIPipelineOrchestrator, Task, TaskType, TaskPriority

class PipelineCLI:
    """流水线命令行界面"""
    
    def __init__(self):
        self.orchestrator = AIPipelineOrchestrator()
        self.config_path = Path("config/ai_pipeline_tasks.json")
    
    def load_task_config(self, pipeline_name: str = "cauldron_development_pipeline"):
        """从配置文件加载任务"""
        if not self.config_path.exists():
            print(f"❌ 配置文件不存在: {self.config_path}")
            return []
        
        with open(self.config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        if pipeline_name not in config:
            print(f"❌ 流水线配置不存在: {pipeline_name}")
            return []
        
        tasks = []
        for task_config in config[pipeline_name]["tasks"]:
            task = Task(
                id=task_config["id"],
                type=TaskType(task_config["type"]),
                priority=TaskPriority[task_config["priority"].upper()],
                description=task_config["description"],
                prompt=task_config["prompt"],
                dependencies=task_config.get("dependencies", []),
                timeout=task_config.get("timeout", 300)
            )
            tasks.append(task)
        
        return tasks
    
    def run_pipeline(self, pipeline_name: str):
        """运行指定的流水线"""
        print(f"🚀 启动流水线: {pipeline_name}")
        
        # 加载任务
        tasks = self.load_task_config(pipeline_name)
        if not tasks:
            return
        
        # 启动编排器
        self.orchestrator.start()
        
        # 添加任务
        task_ids = []
        for task in tasks:
            task_id = self.orchestrator.add_task(task)
            task_ids.append(task_id)
        
        print(f"📋 已添加 {len(task_ids)} 个任务到队列")
        
        # 监控执行
        try:
            self._monitor_execution()
        except KeyboardInterrupt:
            print("\n👋 用户中断执行")
        finally:
            self.orchestrator.stop()
    
    def add_single_task(self, task_type: str, prompt: str, priority: str = "normal"):
        """添加单个任务"""
        task = Task(
            id=f"manual_{int(time.time())}",
            type=TaskType(task_type),
            priority=TaskPriority[priority.upper()],
            description=f"手动任务: {task_type}",
            prompt=prompt
        )
        
        self.orchestrator.start()
        task_id = self.orchestrator.add_task(task)
        print(f"✅ 任务已添加: {task_id}")
        
        try:
            self._monitor_execution()
        except KeyboardInterrupt:
            print("\n👋 用户中断执行")
        finally:
            self.orchestrator.stop()
    
    def _monitor_execution(self):
        """监控执行过程"""
        print("\n📊 开始监控执行...")
        
        while True:
            status = self.orchestrator.get_status()
            
            # 显示状态
            print(f"\r🔄 队列: {status['queue_size']} | "
                  f"完成: {status['completed_tasks']} | "
                  f"失败: {status['failed_tasks']} | "
                  f"Claude: {'🔥' if status['workers']['claude']['busy'] else '💤'} | "
                  f"Rovodev: {'🔥' if status['workers']['rovodev']['busy'] else '💤'}", 
                  end='', flush=True)
            
            # 检查是否完成
            if (status["queue_size"] == 0 and 
                not any(w["busy"] for w in status["workers"].values())):
                print(f"\n🎉 所有任务已完成! 成功: {status['completed_tasks']}, 失败: {status['failed_tasks']}")
                break
            
            time.sleep(2)
    
    def list_pipelines(self):
        """列出可用的流水线"""
        if not self.config_path.exists():
            print("❌ 配置文件不存在")
            return
        
        with open(self.config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        print("📋 可用的流水线:")
        for name, pipeline in config.items():
            task_count = len(pipeline["tasks"])
            print(f"  • {name}: {pipeline['description']} ({task_count} 个任务)")
    
    def show_status(self):
        """显示当前状态"""
        if not self.orchestrator.running:
            print("💤 流水线未运行")
            return
        
        status = self.orchestrator.get_status()
        print("📊 流水线状态:")
        print(json.dumps(status, indent=2, ensure_ascii=False))

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="AI流水线命令行工具")
    subparsers = parser.add_subparsers(dest='command', help='可用命令')
    
    # 运行流水线
    run_parser = subparsers.add_parser('run', help='运行流水线')
    run_parser.add_argument('pipeline', help='流水线名称')
    
    # 添加单个任务
    task_parser = subparsers.add_parser('task', help='添加单个任务')
    task_parser.add_argument('type', choices=[t.value for t in TaskType], help='任务类型')
    task_parser.add_argument('prompt', help='任务提示')
    task_parser.add_argument('--priority', choices=['urgent', 'high', 'normal', 'low'], 
                           default='normal', help='任务优先级')
    
    # 列出流水线
    subparsers.add_parser('list', help='列出可用流水线')
    
    # 显示状态
    subparsers.add_parser('status', help='显示流水线状态')
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return
    
    cli = PipelineCLI()
    
    if args.command == 'run':
        cli.run_pipeline(args.pipeline)
    elif args.command == 'task':
        cli.add_single_task(args.type, args.prompt, args.priority)
    elif args.command == 'list':
        cli.list_pipelines()
    elif args.command == 'status':
        cli.show_status()

if __name__ == "__main__":
    main()
