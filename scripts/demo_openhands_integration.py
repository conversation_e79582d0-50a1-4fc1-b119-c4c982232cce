#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
OpenHands集成演示脚本
展示灵宝道君如何使用OpenHands进行Web验证

作者：太公心易BI系统
版本：v1.0
"""

import asyncio
import json
import logging
import os
import sys
from datetime import datetime

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from src.core.openhands_integration import LingbaoOpenHandsVerifier
from src.core.lingbao_field_verification import LingbaoFieldVerifier, BaxianDebateResult

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def demo_openhands_standalone():
    """演示OpenHands独立验证功能"""
    print("🔮 演示1：OpenHands独立验证")
    print("=" * 60)
    
    # 从环境变量获取API key
    openhands_api_key = "hA04ZDQbdKUbBCqmN5ZPFkcdK0xsKLwX"  # 用户提供的API key
    
    if not openhands_api_key:
        print("❌ 请设置OPENHANDS_API_KEY环境变量")
        return
    
    # 创建验证器
    verifier = LingbaoOpenHandsVerifier(api_key=openhands_api_key)
    
    # 模拟辩论结果
    debate_result = {
        "debate_id": "demo_20250113_001",
        "topic": "苹果公司Q1财报对股价影响分析",
        "conclusions": {
            "price_prediction": "上涨12%",
            "risk_level": "中等",
            "time_horizon": "2个月",
            "target_price": "$200"
        },
        "key_claims": [
            "苹果Q1 iPhone销量将超预期15%",
            "服务业务收入增长强劲",
            "中国市场需求复苏明显",
            "AI功能推动产品升级周期"
        ],
        "confidence_score": 0.78
    }
    
    print(f"📊 辩论主题: {debate_result['topic']}")
    print(f"🎯 价格预测: {debate_result['conclusions']['price_prediction']}")
    print(f"📈 原始置信度: {debate_result['confidence_score']}")
    print("\n🔍 关键论断:")
    for i, claim in enumerate(debate_result['key_claims'], 1):
        print(f"  {i}. {claim}")
    
    print("\n🌐 启动OpenHands Web验证...")
    
    try:
        # 执行验证
        verification_report = await verifier.verify_debate_conclusions(debate_result)
        
        print("\n✅ OpenHands验证完成!")
        print(f"📊 验证任务数: {verification_report['verification_summary']['total_tasks']}")
        print(f"✅ 成功率: {verification_report['verification_summary']['success_rate']:.2%}")
        print(f"🎯 平均置信度: {verification_report['verification_summary']['average_confidence']:.2f}")
        print(f"📋 最终建议: {verification_report['final_recommendation']}")
        
        print("\n📝 详细验证结果:")
        for i, result in enumerate(verification_report['detailed_results'], 1):
            print(f"  任务{i}: {result['task_id'][:20]}...")
            print(f"    成功: {'✅' if result['success'] else '❌'}")
            print(f"    置信度: {result['confidence']:.2f}")
            print(f"    证据数量: {result['evidence_count']}")
            if result.get('key_findings'):
                print(f"    关键发现: {result['key_findings']}")
        
    except Exception as e:
        print(f"❌ OpenHands验证失败: {e}")
        logger.exception("OpenHands验证异常")

async def demo_integrated_verification():
    """演示集成验证功能"""
    print("\n🔮 演示2：灵宝道君集成验证")
    print("=" * 60)
    
    # API配置
    openmanus_url = "https://mock-openmanus.example.com"  # 模拟URL
    openmanus_api_key = "mock-api-key"
    openhands_api_key = "hA04ZDQbdKUbBCqmN5ZPFkcdK0xsKLwX"
    
    # 创建集成验证器
    verifier = LingbaoFieldVerifier(
        openmanus_url=openmanus_url,
        api_key=openmanus_api_key,
        openhands_api_key=openhands_api_key
    )
    
    # 模拟八仙辩论结果
    debate_result = BaxianDebateResult(
        debate_id="integrated_demo_20250113_001",
        topic="特斯拉FSD技术突破对股价影响",
        participants=["吕洞宾", "何仙姑", "铁拐李", "蓝采和", "张果老", "韩湘子", "曹国舅", "汉钟离"],
        conclusions={
            "price_prediction": "上涨25%",
            "risk_level": "高",
            "time_horizon": "3个月",
            "key_catalyst": "FSD技术商业化"
        },
        confidence_score=0.82,
        timestamp=datetime.now(),
        key_claims=[
            "特斯拉FSD V13版本将实现L4级自动驾驶",
            "监管部门将加速自动驾驶审批",
            "竞争对手技术差距扩大",
            "自动驾驶出租车业务将带来新收入流"
        ]
    )
    
    print(f"🎭 参与辩论: {', '.join(debate_result.participants)}")
    print(f"📊 辩论主题: {debate_result.topic}")
    print(f"🎯 价格预测: {debate_result.conclusions['price_prediction']}")
    print(f"⚠️ 风险等级: {debate_result.conclusions['risk_level']}")
    print(f"📈 八仙置信度: {debate_result.confidence_score}")
    
    print("\n🔍 关键论断:")
    for i, claim in enumerate(debate_result.key_claims, 1):
        print(f"  {i}. {claim}")
    
    print("\n🔮 灵宝道君启动田野调查...")
    print("  📡 OpenManus传统验证...")
    print("  🌐 OpenHands Web验证...")
    
    try:
        # 执行集成验证
        tianzun_report = await verifier.verify_debate_result(debate_result)
        
        print("\n✅ 田野调查完成!")
        print(f"📊 原始置信度: {tianzun_report['verification_analysis']['original_confidence']:.2f}")
        print(f"🎯 最终置信度: {tianzun_report['verification_analysis']['final_confidence']:.2f}")
        print(f"📋 灵宝建议: {tianzun_report['recommendation']}")
        
        # Web验证结果
        web_verification = tianzun_report['verification_analysis'].get('web_verification', {})
        if web_verification.get('web_verification_enabled'):
            print(f"\n🌐 Web验证结果:")
            print(f"  成功率: {web_verification.get('web_success_rate', 0):.2%}")
            print(f"  Web置信度: {web_verification.get('web_confidence', 0):.2f}")
            print(f"  Web建议: {web_verification.get('web_recommendation', 'UNKNOWN')}")
            print(f"  证据数量: {web_verification.get('web_evidence_count', 0)}")
        else:
            print("\n🌐 Web验证: 未启用")
        
        print(f"\n📋 给元始天尊的报告:")
        print(f"  验证状态: {tianzun_report['verification_status']}")
        print(f"  置信度变化: {tianzun_report['verification_analysis']['final_confidence'] - tianzun_report['verification_analysis']['original_confidence']:+.2f}")
        print(f"  报告时间: {tianzun_report['timestamp']}")
        
    except Exception as e:
        print(f"❌ 集成验证失败: {e}")
        logger.exception("集成验证异常")

async def demo_comparison():
    """演示验证方法对比"""
    print("\n🔮 演示3：验证方法对比分析")
    print("=" * 60)
    
    print("📊 传统验证 vs OpenHands Web验证")
    print("\n🏛️ 传统OpenManus验证:")
    print("  ✅ 优势: 结构化数据处理、稳定可靠")
    print("  ❌ 局限: 数据源有限、更新滞后")
    
    print("\n🌐 OpenHands Web验证:")
    print("  ✅ 优势: 实时web数据、多源验证、AI分析")
    print("  ❌ 局限: 网络依赖、结果不确定性")
    
    print("\n🔮 灵宝道君集成方案:")
    print("  🎯 双重验证: 传统+Web验证互补")
    print("  📊 智能权重: 根据验证质量动态调整")
    print("  🛡️ 风险控制: 多层验证降低误判")
    print("  ⚡ 实时性: Web验证提供最新信息")

async def main():
    """主演示函数"""
    print("🔮 灵宝道君 × OpenHands 集成演示")
    print("=" * 80)
    print("太公心易BI系统 - 稷下学宫田野调查验证")
    print("=" * 80)
    
    try:
        # 演示1：OpenHands独立验证
        await demo_openhands_standalone()
        
        # 演示2：集成验证
        await demo_integrated_verification()
        
        # 演示3：对比分析
        await demo_comparison()
        
        print("\n🎉 演示完成!")
        print("💡 OpenHands为灵宝道君提供了强大的Web验证能力")
        print("🚀 这将显著提升稷下学宫辩论系统的验证准确性")
        
    except Exception as e:
        print(f"❌ 演示过程出错: {e}")
        logger.exception("演示异常")

if __name__ == "__main__":
    asyncio.run(main())
