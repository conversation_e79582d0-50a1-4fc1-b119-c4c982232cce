#!/usr/bin/env python3
"""
N8N诊断工具 - 专门解决webhook 404错误
分析N8N配置结构并提供详细的连接性测试
"""

import requests
import json
import time
from datetime import datetime
from typing import Dict, List, Any, Optional
import urllib.parse

class N8NDiagnosticTool:
    """N8N诊断和故障排除工具"""
    
    def __init__(self):
        self.base_url = "https://houzhongxu-n8n-free.hf.space"
        self.workflow_id = "5Ibi4vJZjSB0ZaTt"
        self.webhook_id = "ce40f698-832e-475a-a3c7-0895c9e2e90b"
        
        # 可能的webhook端点格式
        self.webhook_variants = [
            f"{self.base_url}/webhook/{self.webhook_id}",
            f"{self.base_url}/webhook-test/{self.webhook_id}",
            f"{self.base_url}/webhook/test/{self.webhook_id}",
            f"{self.base_url}/api/webhook/{self.webhook_id}",
            f"{self.base_url}/hooks/{self.webhook_id}",
            f"{self.base_url}/trigger/{self.webhook_id}",
            f"{self.base_url}/workflow/{self.workflow_id}/webhook/{self.webhook_id}",
            f"{self.base_url}/workflow/{self.workflow_id}/execute",
        ]
        
        print("🔍 N8N诊断工具启动")
        print(f"🌐 N8N实例: {self.base_url}")
        print(f"📊 工作流ID: {self.workflow_id}")
        print(f"🔗 Webhook ID: {self.webhook_id}")
    
    def diagnose_404_error(self) -> Dict[str, Any]:
        """诊断404错误的具体原因"""
        
        print("\n🚨 诊断Webhook 404错误...")
        
        diagnosis = {
            "error_analysis": {},
            "possible_causes": [],
            "solutions": [],
            "test_results": {}
        }
        
        # 1. 测试基础连接
        print("\n1️⃣ 测试N8N实例基础连接...")
        base_test = self._test_base_connection()
        diagnosis["test_results"]["base_connection"] = base_test
        
        if not base_test["accessible"]:
            diagnosis["possible_causes"].append("N8N实例不可访问或已停止")
            diagnosis["solutions"].append("检查Hugging Face Spaces状态")
            return diagnosis
        
        # 2. 测试工作流访问
        print("\n2️⃣ 测试工作流访问...")
        workflow_test = self._test_workflow_access()
        diagnosis["test_results"]["workflow_access"] = workflow_test
        
        # 3. 测试所有可能的webhook端点
        print("\n3️⃣ 测试所有可能的Webhook端点...")
        webhook_tests = self._test_all_webhook_variants()
        diagnosis["test_results"]["webhook_variants"] = webhook_tests
        
        # 4. 分析N8N API端点
        print("\n4️⃣ 探测N8N API端点...")
        api_tests = self._probe_n8n_api_endpoints()
        diagnosis["test_results"]["api_endpoints"] = api_tests
        
        # 5. 分析错误原因
        diagnosis["error_analysis"] = self._analyze_404_causes(diagnosis["test_results"])
        
        # 6. 生成解决方案
        diagnosis["possible_causes"], diagnosis["solutions"] = self._generate_solutions(
            diagnosis["test_results"], diagnosis["error_analysis"]
        )
        
        return diagnosis
    
    def _test_base_connection(self) -> Dict[str, Any]:
        """测试N8N实例基础连接"""
        
        try:
            response = requests.get(self.base_url, timeout=10)
            
            return {
                "accessible": True,
                "status_code": response.status_code,
                "headers": dict(response.headers),
                "response_size": len(response.content),
                "response_time": response.elapsed.total_seconds()
            }
        except Exception as e:
            return {
                "accessible": False,
                "error": str(e)
            }
    
    def _test_workflow_access(self) -> Dict[str, Any]:
        """测试工作流访问"""
        
        workflow_url = f"{self.base_url}/workflow/{self.workflow_id}"
        
        try:
            response = requests.get(workflow_url, timeout=10)
            
            result = {
                "accessible": response.status_code == 200,
                "status_code": response.status_code,
                "url": workflow_url
            }
            
            if response.status_code == 200:
                # 尝试解析响应内容
                try:
                    content = response.text
                    if "webhook" in content.lower():
                        result["contains_webhook_config"] = True
                    if "active" in content.lower():
                        result["workflow_status_mentioned"] = True
                except:
                    pass
            
            return result
            
        except Exception as e:
            return {
                "accessible": False,
                "error": str(e),
                "url": workflow_url
            }
    
    def _test_all_webhook_variants(self) -> Dict[str, Any]:
        """测试所有可能的webhook端点变体"""
        
        results = {}
        test_payload = {
            "test": True,
            "timestamp": datetime.now().isoformat(),
            "diagnostic_tool": "N8N诊断工具"
        }
        
        for i, webhook_url in enumerate(self.webhook_variants, 1):
            print(f"   📤 测试变体 {i}/{len(self.webhook_variants)}: {webhook_url}")
            
            try:
                # 尝试GET请求
                get_response = requests.get(webhook_url, timeout=5)
                get_result = {
                    "method": "GET",
                    "status_code": get_response.status_code,
                    "accessible": get_response.status_code != 404
                }
                
                # 尝试POST请求
                post_response = requests.post(
                    webhook_url, 
                    json=test_payload, 
                    timeout=5,
                    headers={'Content-Type': 'application/json'}
                )
                post_result = {
                    "method": "POST",
                    "status_code": post_response.status_code,
                    "accessible": post_response.status_code != 404,
                    "response": post_response.text[:200] if post_response.text else ""
                }
                
                results[webhook_url] = {
                    "get": get_result,
                    "post": post_result,
                    "best_status": min(get_response.status_code, post_response.status_code)
                }
                
                # 如果找到可用的端点，标记它
                if post_response.status_code == 200:
                    results[webhook_url]["working"] = True
                    print(f"   ✅ 找到可用端点: {webhook_url}")
                elif post_response.status_code != 404:
                    print(f"   ⚠️ 端点响应: {post_response.status_code}")
                
            except Exception as e:
                results[webhook_url] = {
                    "error": str(e),
                    "accessible": False
                }
        
        return results
    
    def _probe_n8n_api_endpoints(self) -> Dict[str, Any]:
        """探测N8N的API端点"""
        
        api_endpoints = [
            f"{self.base_url}/api/v1/workflows",
            f"{self.base_url}/api/workflows",
            f"{self.base_url}/api/v1/executions",
            f"{self.base_url}/api/executions",
            f"{self.base_url}/api/v1/workflows/{self.workflow_id}",
            f"{self.base_url}/api/workflows/{self.workflow_id}",
            f"{self.base_url}/api/v1/workflows/{self.workflow_id}/execute",
            f"{self.base_url}/api/workflows/{self.workflow_id}/execute",
            f"{self.base_url}/rest/workflows",
            f"{self.base_url}/rest/executions",
        ]
        
        results = {}
        
        for endpoint in api_endpoints:
            try:
                response = requests.get(endpoint, timeout=5)
                results[endpoint] = {
                    "status_code": response.status_code,
                    "accessible": response.status_code != 404,
                    "requires_auth": response.status_code == 401,
                    "response_size": len(response.content)
                }
                
                if response.status_code == 200:
                    print(f"   ✅ API端点可用: {endpoint}")
                elif response.status_code == 401:
                    print(f"   🔐 需要认证: {endpoint}")
                    
            except Exception as e:
                results[endpoint] = {
                    "error": str(e),
                    "accessible": False
                }
        
        return results
    
    def _analyze_404_causes(self, test_results: Dict[str, Any]) -> Dict[str, Any]:
        """分析404错误的具体原因"""
        
        analysis = {
            "primary_cause": "unknown",
            "confidence": 0.0,
            "evidence": [],
            "recommendations": []
        }
        
        # 检查基础连接
        if not test_results["base_connection"]["accessible"]:
            analysis["primary_cause"] = "n8n_instance_down"
            analysis["confidence"] = 0.9
            analysis["evidence"].append("N8N实例不可访问")
            return analysis
        
        # 检查工作流访问
        workflow_access = test_results["workflow_access"]
        if not workflow_access["accessible"]:
            analysis["primary_cause"] = "workflow_not_found"
            analysis["confidence"] = 0.8
            analysis["evidence"].append(f"工作流ID {self.workflow_id} 不存在或不可访问")
        
        # 检查webhook变体测试结果
        webhook_tests = test_results["webhook_variants"]
        working_webhooks = [
            url for url, result in webhook_tests.items() 
            if result.get("working", False)
        ]
        
        if working_webhooks:
            analysis["primary_cause"] = "wrong_webhook_url"
            analysis["confidence"] = 0.9
            analysis["evidence"].append(f"找到可用的webhook: {working_webhooks[0]}")
            analysis["recommendations"].append(f"使用正确的webhook URL: {working_webhooks[0]}")
        else:
            # 检查是否有非404响应
            non_404_responses = [
                (url, result) for url, result in webhook_tests.items()
                if result.get("post", {}).get("status_code") != 404
            ]
            
            if non_404_responses:
                analysis["primary_cause"] = "webhook_not_activated"
                analysis["confidence"] = 0.7
                analysis["evidence"].append("Webhook存在但未激活")
                analysis["recommendations"].append("在N8N界面中激活工作流")
            else:
                analysis["primary_cause"] = "webhook_not_configured"
                analysis["confidence"] = 0.8
                analysis["evidence"].append("所有webhook端点都返回404")
                analysis["recommendations"].append("在N8N工作流中添加Webhook触发器节点")
        
        return analysis
    
    def _generate_solutions(self, test_results: Dict, error_analysis: Dict) -> tuple:
        """生成可能的原因和解决方案"""
        
        possible_causes = []
        solutions = []
        
        primary_cause = error_analysis["primary_cause"]
        
        if primary_cause == "n8n_instance_down":
            possible_causes.extend([
                "Hugging Face Spaces实例已停止",
                "网络连接问题",
                "N8N服务崩溃"
            ])
            solutions.extend([
                "检查Hugging Face Spaces状态页面",
                "重启N8N实例",
                "检查本地网络连接"
            ])
        
        elif primary_cause == "workflow_not_found":
            possible_causes.extend([
                f"工作流ID {self.workflow_id} 不存在",
                "工作流已被删除",
                "工作流访问权限问题"
            ])
            solutions.extend([
                "确认正确的工作流ID",
                "检查工作流是否存在于N8N中",
                "验证访问权限"
            ])
        
        elif primary_cause == "wrong_webhook_url":
            possible_causes.append("使用了错误的webhook URL")
            solutions.extend([
                f"使用正确的webhook URL: {error_analysis.get('recommendations', [''])[0]}",
                "更新环境变量中的webhook配置"
            ])
        
        elif primary_cause == "webhook_not_activated":
            possible_causes.extend([
                "工作流未激活",
                "Webhook触发器处于测试模式",
                "需要手动执行一次工作流"
            ])
            solutions.extend([
                "在N8N界面中将工作流设置为'Active'",
                "点击'Execute workflow'按钮激活webhook",
                "检查webhook节点配置"
            ])
        
        elif primary_cause == "webhook_not_configured":
            possible_causes.extend([
                "工作流中没有配置Webhook触发器",
                "Webhook节点配置错误",
                "Webhook路径不匹配"
            ])
            solutions.extend([
                "在N8N工作流中添加Webhook触发器节点",
                "配置正确的webhook路径",
                "确保webhook节点已连接到工作流"
            ])
        
        # 通用解决方案
        solutions.extend([
            "检查N8N工作流的执行历史",
            "查看N8N的错误日志",
            "尝试手动执行工作流进行测试",
            "联系N8N实例管理员"
        ])
        
        return possible_causes, solutions
    
    def test_alternative_methods(self) -> Dict[str, Any]:
        """测试替代的连接方法"""
        
        print("\n🔄 测试替代连接方法...")
        
        alternatives = {}
        
        # 1. 尝试直接执行工作流
        print("1️⃣ 尝试直接执行工作流...")
        execute_result = self._try_direct_execution()
        alternatives["direct_execution"] = execute_result
        
        # 2. 尝试使用不同的HTTP方法
        print("2️⃣ 尝试不同的HTTP方法...")
        http_methods_result = self._try_different_http_methods()
        alternatives["http_methods"] = http_methods_result
        
        # 3. 尝试不同的内容类型
        print("3️⃣ 尝试不同的内容类型...")
        content_types_result = self._try_different_content_types()
        alternatives["content_types"] = content_types_result
        
        return alternatives
    
    def _try_direct_execution(self) -> Dict[str, Any]:
        """尝试直接执行工作流"""
        
        execution_urls = [
            f"{self.base_url}/api/v1/workflows/{self.workflow_id}/execute",
            f"{self.base_url}/api/workflows/{self.workflow_id}/execute",
            f"{self.base_url}/workflow/{self.workflow_id}/execute",
            f"{self.base_url}/execute/{self.workflow_id}",
        ]
        
        test_data = {
            "data": {
                "test": True,
                "timestamp": datetime.now().isoformat(),
                "method": "direct_execution"
            }
        }
        
        results = {}
        
        for url in execution_urls:
            try:
                response = requests.post(
                    url, 
                    json=test_data, 
                    timeout=10,
                    headers={'Content-Type': 'application/json'}
                )
                
                results[url] = {
                    "status_code": response.status_code,
                    "response": response.text[:200],
                    "success": response.status_code in [200, 201, 202]
                }
                
                if response.status_code in [200, 201, 202]:
                    print(f"   ✅ 直接执行成功: {url}")
                
            except Exception as e:
                results[url] = {
                    "error": str(e),
                    "success": False
                }
        
        return results
    
    def _try_different_http_methods(self) -> Dict[str, Any]:
        """尝试不同的HTTP方法"""
        
        webhook_url = f"{self.base_url}/webhook/{self.webhook_id}"
        methods = ["GET", "POST", "PUT", "PATCH"]
        
        test_data = {
            "test": True,
            "timestamp": datetime.now().isoformat(),
            "method_test": True
        }
        
        results = {}
        
        for method in methods:
            try:
                if method == "GET":
                    response = requests.get(webhook_url, timeout=5)
                elif method == "POST":
                    response = requests.post(webhook_url, json=test_data, timeout=5)
                elif method == "PUT":
                    response = requests.put(webhook_url, json=test_data, timeout=5)
                elif method == "PATCH":
                    response = requests.patch(webhook_url, json=test_data, timeout=5)
                
                results[method] = {
                    "status_code": response.status_code,
                    "success": response.status_code not in [404, 405],
                    "response": response.text[:100]
                }
                
            except Exception as e:
                results[method] = {
                    "error": str(e),
                    "success": False
                }
        
        return results
    
    def _try_different_content_types(self) -> Dict[str, Any]:
        """尝试不同的内容类型"""
        
        webhook_url = f"{self.base_url}/webhook/{self.webhook_id}"
        
        test_scenarios = {
            "application/json": {
                "data": {"test": True, "timestamp": datetime.now().isoformat()},
                "headers": {"Content-Type": "application/json"}
            },
            "application/x-www-form-urlencoded": {
                "data": "test=true&timestamp=" + datetime.now().isoformat(),
                "headers": {"Content-Type": "application/x-www-form-urlencoded"}
            },
            "text/plain": {
                "data": "test data",
                "headers": {"Content-Type": "text/plain"}
            }
        }
        
        results = {}
        
        for content_type, scenario in test_scenarios.items():
            try:
                if content_type == "application/json":
                    response = requests.post(
                        webhook_url, 
                        json=scenario["data"], 
                        headers=scenario["headers"],
                        timeout=5
                    )
                else:
                    response = requests.post(
                        webhook_url, 
                        data=scenario["data"], 
                        headers=scenario["headers"],
                        timeout=5
                    )
                
                results[content_type] = {
                    "status_code": response.status_code,
                    "success": response.status_code not in [404, 400],
                    "response": response.text[:100]
                }
                
            except Exception as e:
                results[content_type] = {
                    "error": str(e),
                    "success": False
                }
        
        return results
    
    def generate_diagnostic_report(self) -> Dict[str, Any]:
        """生成完整的诊断报告"""
        
        print("🔍 生成完整诊断报告...")
        
        # 执行所有诊断测试
        diagnosis = self.diagnose_404_error()
        alternatives = self.test_alternative_methods()
        
        # 生成综合报告
        report = {
            "diagnostic_timestamp": datetime.now().isoformat(),
            "n8n_instance": {
                "base_url": self.base_url,
                "workflow_id": self.workflow_id,
                "webhook_id": self.webhook_id
            },
            "error_diagnosis": diagnosis,
            "alternative_methods": alternatives,
            "summary": {
                "primary_issue": diagnosis["error_analysis"]["primary_cause"],
                "confidence": diagnosis["error_analysis"]["confidence"],
                "working_alternatives": [],
                "recommended_actions": []
            }
        }
        
        # 查找可用的替代方法
        for method_type, results in alternatives.items():
            for method, result in results.items():
                if result.get("success", False):
                    report["summary"]["working_alternatives"].append(f"{method_type}: {method}")
        
        # 生成推荐行动
        report["summary"]["recommended_actions"] = diagnosis["solutions"][:3]
        
        return report

def main():
    """主诊断函数"""
    
    print("🚨 N8N Webhook 404错误诊断工具")
    print("=" * 60)
    
    diagnostic_tool = N8NDiagnosticTool()
    
    # 生成完整诊断报告
    report = diagnostic_tool.generate_diagnostic_report()
    
    # 保存报告
    with open("n8n_diagnostic_report.json", "w", encoding="utf-8") as f:
        json.dump(report, f, ensure_ascii=False, indent=2)
    
    # 输出诊断结果
    print("\n" + "=" * 60)
    print("📊 诊断结果总结:")
    print(f"主要问题: {report['summary']['primary_issue']}")
    print(f"置信度: {report['error_diagnosis']['error_analysis']['confidence']:.1%}")
    
    if report["summary"]["working_alternatives"]:
        print(f"\n✅ 找到可用的替代方法:")
        for alt in report["summary"]["working_alternatives"]:
            print(f"  • {alt}")
    
    print(f"\n💡 推荐解决方案:")
    for i, action in enumerate(report["summary"]["recommended_actions"], 1):
        print(f"  {i}. {action}")
    
    print(f"\n📁 详细报告已保存到: n8n_diagnostic_report.json")
    
    # 提供具体的下一步指导
    primary_cause = report["summary"]["primary_issue"]
    
    print(f"\n🎯 针对 '{primary_cause}' 的具体解决步骤:")
    
    if primary_cause == "webhook_not_activated":
        print("1. 访问: https://houzhongxu-n8n-free.hf.space/workflow/5Ibi4vJZjSB0ZaTt")
        print("2. 点击 'Execute workflow' 按钮")
        print("3. 将工作流状态设置为 'Active'")
        print("4. 重新测试webhook连接")
    
    elif primary_cause == "webhook_not_configured":
        print("1. 在N8N工作流中添加 'Webhook' 触发器节点")
        print("2. 配置webhook路径为: taigong-xinyi")
        print("3. 设置HTTP方法为: POST")
        print("4. 保存并激活工作流")
    
    elif primary_cause == "wrong_webhook_url":
        working_webhooks = [
            alt.split(": ")[1] for alt in report["summary"]["working_alternatives"]
            if "webhook" in alt.lower()
        ]
        if working_webhooks:
            print(f"1. 使用正确的webhook URL: {working_webhooks[0]}")
            print("2. 更新环境变量配置")
            print("3. 重新测试连接")
    
    return report

if __name__ == "__main__":
    diagnostic_report = main()