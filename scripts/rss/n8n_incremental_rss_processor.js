// N8N增量RSS处理器 - 只处理新文章，避免重复存储
// 适用于MongoDB免费层512MB限制

const processedItems = [];
const items = $input.all();

// 文本清理和标准化函数
function cleanAndValidateText(text) {
    if (!text || typeof text !== 'string') {
        return null;
    }
    
    const cleaned = text
        .trim()
        .replace(/[\r\n\t]+/g, ' ')
        .replace(/\s+/g, ' ')
        .replace(/[^\u4e00-\u9fa5\u0030-\u0039\u0041-\u005a\u0061-\u007a\s\.,!?;:()（），。！？；：\-]/g, '')
        .substring(0, 800);
    
    return cleaned.length >= 3 ? cleaned : null;
}

// 生成稳定的文章ID（基于内容哈希，确保相同文章ID一致）
function generateStableArticleId(title, url, publishedTime) {
    const content = `${title}_${url}_${publishedTime}`;
    const hash = content.split('').reduce((a, b) => {
        a = ((a << 5) - a) + b.charCodeAt(0);
        return a & a;
    }, 0);
    return `rss_${Math.abs(hash)}`;
}

// 处理时间格式
function processDateTime(timeStr) {
    try {
        if (!timeStr) return new Date().toISOString();
        
        const date = new Date(timeStr);
        if (isNaN(date.getTime())) {
            return new Date().toISOString();
        }
        return date.toISOString();
    } catch (error) {
        return new Date().toISOString();
    }
}

// 检查文章是否已存在（需要在N8N中配置MongoDB查询节点）
async function checkArticleExists(articleId) {
    // 这个函数需要在N8N流程中通过MongoDB查询节点实现
    // 返回true如果文章已存在，false如果是新文章
    return false; // 占位符，实际实现在N8N流程中
}

console.log(`🚀 开始增量处理RSS数据: ${items.length} 个items`);

// 获取上次处理时间戳（从N8N全局变量或数据库）
const lastProcessTime = $workflow.lastProcessTime || new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString();
console.log(`📅 上次处理时间: ${lastProcessTime}`);

let newArticlesCount = 0;
let duplicateCount = 0;
let errorCount = 0;

// 处理每个RSS项目
for (const item of items) {
    try {
        if (!item || !item.json) {
            console.log("⚠️ 跳过无效item");
            errorCount++;
            continue;
        }
        
        const data = item.json;
        
        // 提取和清理标题
        const rawTitle = data.title || data.content || data.description || "";
        const cleanTitle = cleanAndValidateText(rawTitle);
        
        if (!cleanTitle) {
            console.log(`⚠️ 跳过无效标题: ${rawTitle}`);
            errorCount++;
            continue;
        }
        
        // 处理发布时间
        const publishedTime = processDateTime(
            data.published_time || data.pubDate || data.isoDate || data.date
        );
        
        // 检查文章是否太旧（超过7天不处理，节省空间）
        const articleDate = new Date(publishedTime);
        const sevenDaysAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
        
        if (articleDate < sevenDaysAgo) {
            console.log(`⏰ 跳过过期文章: ${cleanTitle.substring(0, 30)}...`);
            continue;
        }
        
        // 检查是否是新文章（发布时间晚于上次处理时间）
        if (new Date(publishedTime) <= new Date(lastProcessTime)) {
            console.log(`🔄 跳过旧文章: ${cleanTitle.substring(0, 30)}...`);
            duplicateCount++;
            continue;
        }
        
        // 生成稳定的文章ID
        const articleId = generateStableArticleId(
            cleanTitle, 
            data.link || data.url || "", 
            publishedTime
        );
        
        // 提取内容（用于向量化）
        const content = cleanAndValidateText(
            data.content || data.description || data.summary || cleanTitle
        ) || cleanTitle;
        
        // 创建符合MongoDB Vector Store要求的文档格式
        const document = {
            pageContent: content,
            metadata: {
                title: cleanTitle,
                published_date: publishedTime,
                article_id: articleId,
                source: data.source || data.feed || "rss_feed",
                url: data.link || data.url || "",
                category: data.category || "general",
                processed_at: new Date().toISOString(),
                is_new: true,
                content_hash: articleId // 用于去重
            }
        };
        
        // 验证必需字段
        if (!document.metadata.title || document.metadata.title === "undefined") {
            document.metadata.title = `RSS文章_${Date.now()}`;
        }
        
        processedItems.push(document);
        newArticlesCount++;
        console.log(`✅ 新文章: ${document.metadata.title.substring(0, 40)}...`);
        
    } catch (error) {
        console.log(`❌ 处理错误: ${error.message}`);
        errorCount++;
        continue;
    }
}

// 处理结果统计
console.log(`📊 处理统计:`);
console.log(`  - 新文章: ${newArticlesCount}`);
console.log(`  - 重复文章: ${duplicateCount}`);
console.log(`  - 错误: ${errorCount}`);
console.log(`  - 总输入: ${items.length}`);

// 如果没有新文章，返回空数组（避免无意义的数据库操作）
if (processedItems.length === 0) {
    console.log("✨ 没有新文章需要处理");
    return [];
}

// 最终验证和清理
const validatedItems = [];
for (let i = 0; i < processedItems.length; i++) {
    const doc = processedItems[i];
    
    // 确保所有必需字段存在
    if (!doc.metadata) doc.metadata = {};
    if (!doc.metadata.title) doc.metadata.title = `修复标题_${i}_${Date.now()}`;
    if (!doc.metadata.article_id) doc.metadata.article_id = `fixed_id_${i}_${Date.now()}`;
    if (!doc.metadata.published_date) doc.metadata.published_date = new Date().toISOString();
    if (!doc.pageContent) doc.pageContent = doc.metadata.title;
    
    // 确保所有字段都是字符串
    Object.keys(doc.metadata).forEach(key => {
        doc.metadata[key] = String(doc.metadata[key]);
    });
    doc.pageContent = String(doc.pageContent);
    
    validatedItems.push(doc);
}

// 更新全局处理时间戳
$workflow.lastProcessTime = new Date().toISOString();

console.log(`🎉 增量处理完成: ${validatedItems.length} 个新文档准备向量化`);
console.log(`💾 预计存储空间: ${(validatedItems.length * 5.4).toFixed(2)}KB`);

return validatedItems;
