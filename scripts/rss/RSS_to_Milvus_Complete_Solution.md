# RSS到Milvus完整解决方案

## 方案1: N8N Code节点代码（推荐）

将以下代码复制到你的N8N "Code test"节点，替换现有代码：

```javascript
// RSS到Milvus完整解决方案
// 适用于N8N工作流中的Code节点

const processedItems = [];
const items = $input.all();

// ⏰ 时间过滤配置 - 只处理过去30分钟的文章
const FILTER_MINUTES = 30; // 可调整：15, 30, 60分钟
const cutoffTime = new Date(Date.now() - FILTER_MINUTES * 60 * 1000);

console.log(`⏰ 时间过滤: 只处理 ${cutoffTime.toISOString()} 之后的文章`);

// 文本清理和标准化函数
function cleanAndValidateText(text) {
    if (!text || typeof text !== 'string') {
        return null;
    }

    const cleaned = text
        .trim()
        .replace(/[\r\n\t]+/g, ' ')
        .replace(/\s+/g, ' ')
        .replace(/[^\u4e00-\u9fa5\u0030-\u0039\u0041-\u005a\u0061-\u007a\s\.,!?;:()（），。！？；：\-]/g, '')
        .substring(0, 800);

    return cleaned.length >= 3 ? cleaned : null;
}

// 生成唯一ID
function generateUniqueId(title, timestamp) {
    const hash = title.split('').reduce((a, b) => {
        a = ((a << 5) - a) + b.charCodeAt(0);
        return a & a;
    }, 0);
    return `rss_${Math.abs(hash)}_${timestamp}`;
}

// 处理时间格式
function processDateTime(timeStr) {
    try {
        if (!timeStr) return new Date().toISOString();
        
        const date = new Date(timeStr);
        if (isNaN(date.getTime())) {
            return new Date().toISOString();
        }
        return date.toISOString();
    } catch (error) {
        return new Date().toISOString();
    }
}

console.log(`🚀 开始处理RSS数据: ${items.length} 个items`);

// 处理每个RSS项目
for (const item of items) {
    try {
        if (!item || !item.json) {
            console.log("⚠️ 跳过无效item");
            continue;
        }
        
        const data = item.json;
        
        // 提取和清理标题
        const rawTitle = data.title || data.content || data.description || "";
        const cleanTitle = cleanAndValidateText(rawTitle);
        
        if (!cleanTitle) {
            console.log(`⚠️ 跳过无效标题: ${rawTitle}`);
            continue;
        }
        
        // 处理发布时间
        const publishedTime = processDateTime(
            data.published_time || data.pubDate || data.isoDate || data.date
        );
        
        // 生成文章ID
        const articleId = data.article_id || generateUniqueId(cleanTitle, Date.now());
        
        // 提取内容（用于向量化）
        const content = cleanAndValidateText(
            data.content || data.description || data.summary || cleanTitle
        ) || cleanTitle;
        
        // 创建符合Milvus要求的文档格式
        const document = {
            pageContent: content,
            metadata: {
                title: cleanTitle,
                published_date: publishedTime,
                article_id: articleId,
                source: data.source || data.feed || "rss_feed",
                url: data.link || data.url || "",
                category: data.category || "general",
                processed: "false"
            }
        };
        
        // 验证必需字段
        if (!document.metadata.title || document.metadata.title === "undefined") {
            document.metadata.title = `RSS文章_${Date.now()}`;
        }
        
        processedItems.push(document);
        console.log(`✅ 成功处理: ${document.metadata.title.substring(0, 40)}...`);
        
    } catch (error) {
        console.log(`❌ 处理错误: ${error.message}`);
        continue;
    }
}

// 确保有数据返回
if (processedItems.length === 0) {
    console.log("⚠️ 没有有效数据，创建默认文档");
    const defaultDoc = {
        pageContent: "RSS数据处理测试文档",
        metadata: {
            title: "RSS测试文档",
            published_date: new Date().toISOString(),
            article_id: `default_rss_${Date.now()}`,
            source: "default",
            url: "",
            category: "test",
            processed: "false"
        }
    };
    processedItems.push(defaultDoc);
}

// 最终验证和清理
const validatedItems = [];
for (let i = 0; i < processedItems.length; i++) {
    const doc = processedItems[i];
    
    // 确保所有必需字段存在
    if (!doc.metadata) doc.metadata = {};
    if (!doc.metadata.title) doc.metadata.title = `修复标题_${i}_${Date.now()}`;
    if (!doc.metadata.article_id) doc.metadata.article_id = `fixed_id_${i}_${Date.now()}`;
    if (!doc.metadata.published_date) doc.metadata.published_date = new Date().toISOString();
    if (!doc.pageContent) doc.pageContent = doc.metadata.title;
    
    // 确保所有字段都是字符串
    Object.keys(doc.metadata).forEach(key => {
        doc.metadata[key] = String(doc.metadata[key]);
    });
    doc.pageContent = String(doc.pageContent);
    
    validatedItems.push(doc);
    console.log(`✅ 验证文档 ${i}: title="${doc.metadata.title.substring(0, 30)}..."`);
}

console.log(`🎉 RSS处理完成: ${validatedItems.length} 个有效文档准备向量化`);
return validatedItems;
```

## 方案2: 独立Python脚本（备用方案）

如果N8N方案不行，可以使用这个独立脚本：

```python
#!/usr/bin/env python3
# RSS到Milvus独立脚本

import feedparser
import requests
from datetime import datetime
from pymilvus import MilvusClient
import hashlib
import re
import json

class RSSToMilvusProcessor:
    def __init__(self, milvus_uri, milvus_token, collection_name):
        self.client = MilvusClient(uri=milvus_uri, token=milvus_token)
        self.collection_name = collection_name
        
    def clean_text(self, text):
        if not text:
            return None
        
        # 清理HTML标签
        text = re.sub(r'<[^>]+>', '', text)
        # 清理特殊字符
        text = re.sub(r'[^\u4e00-\u9fa5\u0030-\u0039\u0041-\u005a\u0061-\u007a\s\.,!?;:()（），。！？；：\-]', '', text)
        # 标准化空白字符
        text = re.sub(r'\s+', ' ', text).strip()
        
        return text[:800] if len(text) >= 3 else None
    
    def process_rss_feed(self, rss_url):
        try:
            feed = feedparser.parse(rss_url)
            documents = []
            
            for entry in feed.entries:
                title = self.clean_text(entry.get('title', ''))
                if not title:
                    continue
                
                content = self.clean_text(
                    entry.get('description', '') or 
                    entry.get('summary', '') or 
                    title
                )
                
                # 生成唯一ID
                article_id = hashlib.md5(
                    (title + str(entry.get('published', ''))).encode()
                ).hexdigest()[:16]
                
                # 处理时间
                try:
                    pub_date = datetime(*entry.published_parsed[:6]).isoformat()
                except:
                    pub_date = datetime.now().isoformat()
                
                document = {
                    "id": article_id,
                    "vector": [0.0] * 1024,  # 占位符，实际需要嵌入向量
                    "title": title,
                    "content": content,
                    "published_date": pub_date,
                    "source": rss_url,
                    "url": entry.get('link', ''),
                    "category": entry.get('category', 'general')
                }
                
                documents.append(document)
            
            return documents
            
        except Exception as e:
            print(f"处理RSS失败: {e}")
            return []
    
    def insert_to_milvus(self, documents):
        try:
            if documents:
                result = self.client.insert(
                    collection_name=self.collection_name,
                    data=documents
                )
                print(f"成功插入 {len(documents)} 条记录到Milvus")
                return result
        except Exception as e:
            print(f"插入Milvus失败: {e}")
            return None

# 使用示例
if __name__ == "__main__":
    processor = RSSToMilvusProcessor(
        milvus_uri="your-milvus-uri",
        milvus_token="your-token", 
        collection_name="ifuleyou"
    )
    
    rss_urls = [
        "https://feeds.feedburner.com/anyfeeder/ijwq2dvucg5",  # 华尔街见闻
        "https://feeds.feedburner.com/anyfeeder/brrprypyfmj",  # 雪球
    ]
    
    for url in rss_urls:
        documents = processor.process_rss_feed(url)
        processor.insert_to_milvus(documents)
```

## 使用建议

### 优先使用方案1（N8N代码）
1. 复制方案1的JavaScript代码
2. 替换你N8N中"Code test"节点的代码
3. 确保连接: Code test → Default Data Loader → Embeddings Cohere → Milvus

### 如果方案1不行，使用方案2
1. 保存Python脚本
2. 安装依赖: `pip install feedparser pymilvus requests`
3. 修改Milvus连接参数
4. 运行脚本

## 关键改进点
- ✅ 完整的文本清理和验证
- ✅ 多重安全检查确保数据完整性
- ✅ 详细的日志输出便于调试
- ✅ 符合Milvus和Langchain标准的数据格式
- ✅ 错误处理和默认值设置

试试方案1，应该能解决你的问题！