// 🎯 RSS数据灌入Milvus的完整解决方案
// 适用于N8N Code节点，解决"i服了you"的问题

console.log("🚀 RSS to Milvus 数据处理开始...");

const items = $input.all();
console.log(`📊 接收到 ${items.length} 条RSS数据`);

const processedDocuments = [];

for (let i = 0; i < items.length; i++) {
    const item = items[i];
    console.log(`\n🔍 处理第 ${i+1} 条数据...`);
    
    // 多层数据提取 - 兼容不同的RSS格式
    let data = item;
    if (item.json) data = item.json;
    if (item.data) data = item.data;
    
    console.log("原始数据键:", Object.keys(data));
    
    // 提取标题 - 多种字段名兼容
    let title = "";
    if (data.title && typeof data.title === 'string' && data.title.trim()) {
        title = data.title.trim();
    } else if (data.name) {
        title = String(data.name).trim();
    } else if (data.subject) {
        title = String(data.subject).trim();
    } else if (data.headline) {
        title = String(data.headline).trim();
    } else {
        title = `RSS文章_${Date.now()}_${i}`;
        console.log("⚠️ 未找到标题，使用默认标题");
    }
    
    // 提取内容
    let content = "";
    if (data.content) {
        content = String(data.content);
    } else if (data.description) {
        content = String(data.description);
    } else if (data.summary) {
        content = String(data.summary);
    } else if (data.contentSnippet) {
        content = String(data.contentSnippet);
    } else {
        content = title; // 如果没有内容，用标题作为内容
    }
    
    // 处理发布时间
    let publishedDate = new Date();
    if (data.published_time) {
        publishedDate = new Date(data.published_time);
    } else if (data.pubDate) {
        publishedDate = new Date(data.pubDate);
    } else if (data.isoDate) {
        publishedDate = new Date(data.isoDate);
    } else if (data.date) {
        publishedDate = new Date(data.date);
    }
    
    // 生成文章ID
    let articleId = "";
    if (data.article_id) {
        articleId = String(data.article_id);
    } else if (data.id) {
        articleId = String(data.id);
    } else if (data.guid) {
        articleId = String(data.guid);
    } else if (data.link) {
        // 从链接生成ID
        articleId = `rss_${data.link.split('/').pop()}_${Date.now()}`;
    } else {
        articleId = `rss_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
    
    // 提取来源
    let source = "RSS";
    if (data.source) {
        source = String(data.source);
    } else if (data.feed) {
        source = String(data.feed);
    } else if (data.creator) {
        source = String(data.creator);
    }
    
    // 构建严格的metadata - 确保所有字段都存在且为字符串
    const metadata = {
        title: String(title),                           // 🎯 关键字段！
        published_date: publishedDate.toISOString(),    // ISO格式时间
        article_id: String(articleId),                  // 唯一标识
        source: String(source),                         // 数据源
        url: String(data.link || data.url || ""),       // 原文链接
        category: String(data.category || data.categories || "未分类"),
        processed_at: new Date().toISOString()          // 处理时间
    };
    
    // 构建LangChain Document格式
    const document = {
        pageContent: String(content),
        metadata: metadata
    };
    
    console.log(`✅ 处理完成: ${metadata.title.substring(0, 50)}...`);
    console.log(`📝 文章ID: ${metadata.article_id}`);
    console.log(`📅 发布时间: ${metadata.published_date}`);
    
    // 最终验证
    if (!document.metadata.title) {
        console.error(`❌ 文档 ${i+1} 缺少title字段!`);
        document.metadata.title = `紧急修复_${i}`;
    }
    
    processedDocuments.push(document);
}

// 兜底处理 - 确保至少有一个文档
if (processedDocuments.length === 0) {
    console.log("🆘 没有有效数据，创建默认文档");
    const defaultDocument = {
        pageContent: "默认RSS内容 - 数据处理测试",
        metadata: {
            title: "默认RSS标题",
            published_date: new Date().toISOString(),
            article_id: `default_rss_${Date.now()}`,
            source: "系统默认",
            url: "",
            category: "测试",
            processed_at: new Date().toISOString()
        }
    };
    processedDocuments.push(defaultDocument);
}

// 最终验证所有文档
console.log("\n🔍 最终验证...");
for (let i = 0; i < processedDocuments.length; i++) {
    const doc = processedDocuments[i];
    if (!doc.metadata || !doc.metadata.title) {
        console.error(`❌ 文档 ${i} 验证失败!`);
        // 紧急修复
        if (!doc.metadata) doc.metadata = {};
        doc.metadata.title = `紧急修复标题_${i}`;
    } else {
        console.log(`✅ 文档 ${i+1} 验证通过: ${doc.metadata.title}`);
    }
}

console.log(`\n🎉 RSS处理完成，共生成 ${processedDocuments.length} 个文档`);
console.log("📤 返回LangChain Document格式数据");

// 🚨 关键：直接返回文档数组，不要包装！
return processedDocuments;
