// 🎯 反标题党RSS处理器 - 标题+内容组合
const items = $input.all();
const processedItems = [];

console.log(`📊 处理 ${items.length} 个RSS项目`);

for (const item of items) {
    try {
        if (!item || !item.json) continue;

        const data = item.json;

        // 提取标题
        let title = data.title || "";
        title = title.trim();

        // 提取内容 - 对抗标题党！
        let content = data.content || data.description || data.summary || "";
        content = content.trim();

        // 简单清洗内容
        if (content) {
            content = content
                .replace(/<[^>]*>/g, '')  // 去HTML标签
                .replace(/\s+/g, ' ')     // 合并空白
                .substring(0, 500);      // 限制长度
        }

        if (!title || title.length < 2) {
            console.log("⚠️ 跳过无效标题");
            continue;
        }

        // 提取发布时间
        let publishedDate;
        try {
            const timeStr = data.published_time || data.pubDate || data.isoDate || data.date;
            publishedDate = timeStr ? new Date(timeStr).toISOString() : new Date().toISOString();
        } catch {
            publishedDate = new Date().toISOString();
        }

        // 组合标题+内容用于向量化
        const pageContent = content ? `${title}\n\n${content}` : title;

        const document = {
            pageContent: pageContent,  // 标题+内容组合
            metadata: {
                title: title,
                published_date: publishedDate,
                content: content || ""  // 保留原始内容
            }
        };

        processedItems.push(document);
        console.log(`✅ ${title.substring(0, 30)}... | 内容:${content ? '有' : '无'}`);

    } catch (error) {
        console.log(`❌ 处理错误: ${error.message}`);
    }
}

console.log(`🎉 完成处理: ${processedItems.length} 个文档`);
return processedItems;
