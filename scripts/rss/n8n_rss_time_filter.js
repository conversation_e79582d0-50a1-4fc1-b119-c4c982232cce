// N8N RSS时间过滤器 - 只处理过去30分钟的新文章
// 放在RSS Feed节点之后，数据处理之前

const items = $input.all();
const filteredItems = [];

// ⏰ 配置：只处理过去N分钟的文章
const FILTER_MINUTES = 30; // 可以调整为15, 30, 60分钟
const cutoffTime = new Date(Date.now() - FILTER_MINUTES * 60 * 1000);

console.log(`🕐 RSS时间过滤器启动`);
console.log(`📅 截止时间: ${cutoffTime.toISOString()}`);
console.log(`📊 输入文章数: ${items.length}`);

// 时间解析函数 - 处理各种RSS时间格式
function parseRSSTime(timeStr) {
    if (!timeStr) return null;
    
    try {
        // 常见的RSS时间格式
        const formats = [
            timeStr, // 直接解析
            timeStr.replace(/\s+\+\d{4}$/, ''), // 移除时区
            timeStr.replace(/\s+[A-Z]{3}$/, ''), // 移除时区缩写
        ];
        
        for (const format of formats) {
            const date = new Date(format);
            if (!isNaN(date.getTime())) {
                return date;
            }
        }
        
        // 如果都失败了，尝试手动解析
        const match = timeStr.match(/(\d{4})-(\d{2})-(\d{2})[T\s](\d{2}):(\d{2}):(\d{2})/);
        if (match) {
            const [, year, month, day, hour, minute, second] = match;
            return new Date(year, month - 1, day, hour, minute, second);
        }
        
        return null;
    } catch (error) {
        console.log(`⚠️ 时间解析失败: ${timeStr} - ${error.message}`);
        return null;
    }
}

// 统计变量
let newArticles = 0;
let oldArticles = 0;
let invalidTime = 0;

// 过滤每个RSS项目
for (const item of items) {
    try {
        if (!item || !item.json) {
            console.log("⚠️ 跳过无效item");
            continue;
        }
        
        const data = item.json;
        
        // 提取发布时间 - 尝试多个字段
        const timeFields = [
            'pubDate',           // 标准RSS
            'published',         // Atom
            'isoDate',          // 某些解析器
            'date',             // 通用
            'published_time',   // 自定义
            'lastBuildDate',    // RSS频道
            'updated'           // Atom更新时间
        ];
        
        let publishedTime = null;
        let timeSource = null;
        
        for (const field of timeFields) {
            if (data[field]) {
                publishedTime = parseRSSTime(data[field]);
                if (publishedTime) {
                    timeSource = field;
                    break;
                }
            }
        }
        
        // 如果没有找到有效时间，跳过
        if (!publishedTime) {
            console.log(`⚠️ 无效时间: ${data.title || 'No Title'}`);
            invalidTime++;
            continue;
        }
        
        // 检查文章是否在时间窗口内
        if (publishedTime >= cutoffTime) {
            // 文章在时间窗口内，保留
            filteredItems.push({
                json: {
                    ...data,
                    // 标准化时间字段
                    standardized_time: publishedTime.toISOString(),
                    time_source: timeSource,
                    filter_passed: true,
                    minutes_ago: Math.round((Date.now() - publishedTime.getTime()) / (1000 * 60))
                }
            });
            
            newArticles++;
            console.log(`✅ 新文章 (${Math.round((Date.now() - publishedTime.getTime()) / (1000 * 60))}分钟前): ${(data.title || 'No Title').substring(0, 50)}...`);
        } else {
            // 文章太旧，过滤掉
            const minutesAgo = Math.round((Date.now() - publishedTime.getTime()) / (1000 * 60));
            console.log(`⏰ 过滤旧文章 (${minutesAgo}分钟前): ${(data.title || 'No Title').substring(0, 50)}...`);
            oldArticles++;
        }
        
    } catch (error) {
        console.log(`❌ 处理错误: ${error.message}`);
        continue;
    }
}

// 输出统计信息
console.log(`📊 过滤结果:`);
console.log(`  ✅ 新文章 (${FILTER_MINUTES}分钟内): ${newArticles}`);
console.log(`  ⏰ 旧文章 (已过滤): ${oldArticles}`);
console.log(`  ⚠️ 无效时间: ${invalidTime}`);
console.log(`  📈 过滤率: ${((oldArticles / items.length) * 100).toFixed(1)}%`);

// 如果没有新文章，返回空数组停止后续处理
if (filteredItems.length === 0) {
    console.log(`🛑 没有新文章，停止后续处理`);
    return [];
}

console.log(`🎉 ${filteredItems.length} 篇新文章将继续处理`);

// 返回过滤后的文章
return filteredItems;
