// N8N RSS增量处理 - 保持原有数据结构，只加时间过滤
const items = $input.all();
const processedItems = [];

// ⏰ 时间过滤配置 - 只处理过去30分钟的文章
const FILTER_MINUTES = 30; // 可调整：15, 30, 60分钟
const cutoffTime = new Date(Date.now() - FILTER_MINUTES * 60 * 1000);

console.log(`⏰ 时间过滤: 只处理 ${cutoffTime.toISOString()} 之后的文章`);
console.log(`📊 输入文章数: ${items.length}`);

// 简单哈希函数
function simpleHash(str) {
  let hash = 0;
  for (let i = 0; i < str.length; i++) {
    const char = str.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash;
  }
  return Math.abs(hash).toString(16);
}

// 时间解析函数
function parseRSSTime(timeStr) {
  if (!timeStr) return null;
  
  try {
    const date = new Date(timeStr);
    if (!isNaN(date.getTime())) {
      return date;
    }
    return null;
  } catch (error) {
    console.log(`⚠️ 时间解析失败: ${timeStr}`);
    return null;
  }
}

// 统计变量
let newArticles = 0;
let oldArticles = 0;
let invalidTime = 0;

for (const item of items) {
  const data = item.json;
  
  // 跳过无效数据
  if (!data.title) {
    console.log("⚠️ 跳过无标题文章");
    continue;
  }
  
  // 🕐 时间过滤 - 核心逻辑
  const publishedTime = parseRSSTime(data.isoDate || data.pubDate);
  
  if (!publishedTime) {
    console.log(`⚠️ 无效时间，跳过: ${data.title.substring(0, 30)}...`);
    invalidTime++;
    continue;
  }
  
  // 检查文章是否在30分钟窗口内
  if (publishedTime < cutoffTime) {
    const minutesAgo = Math.round((Date.now() - publishedTime.getTime()) / (1000 * 60));
    console.log(`⏰ 过滤旧文章 (${minutesAgo}分钟前): ${data.title.substring(0, 30)}...`);
    oldArticles++;
    continue; // 🚫 跳过旧文章
  }
  
  // ✅ 文章在时间窗口内，处理它
  const minutesAgo = Math.round((Date.now() - publishedTime.getTime()) / (1000 * 60));
  console.log(`✅ 新文章 (${minutesAgo}分钟前): ${data.title.substring(0, 30)}...`);
  
  // 🔧 保持你原来的数据结构！
  const processedItem = {
    article_id: simpleHash(data.title) + '_' + Date.now().toString(36),
    title: data.title,                    // 直接用！
    content: $input.first().json['content:encodedSnippet'] || data.content || data.description || data.title,
    published_time: data.isoDate ||       // 直接用标准时间！
                   data.pubDate || 
                   new Date().toISOString(),
    processed: false
  };
  
  processedItems.push({ json: processedItem });
  newArticles++;
}

// 简单去重 (保留你的逻辑)
const uniqueItems = [];
const seenTitles = new Set();

for (const item of processedItems) {
  if (!seenTitles.has(item.json.title)) {
    seenTitles.add(item.json.title);
    uniqueItems.push(item);
  }
}

// 📊 输出统计信息
console.log(`📊 处理统计:`);
console.log(`  ✅ 新文章 (${FILTER_MINUTES}分钟内): ${newArticles}`);
console.log(`  ⏰ 旧文章 (已过滤): ${oldArticles}`);
console.log(`  ⚠️ 无效时间: ${invalidTime}`);
console.log(`  🔄 去重后: ${uniqueItems.length}`);
console.log(`  📈 总过滤率: ${((oldArticles + invalidTime) / items.length * 100).toFixed(1)}%`);

// 如果没有新文章，返回空数组停止后续处理
if (uniqueItems.length === 0) {
  console.log(`🛑 没有新文章，停止后续处理`);
  return [];
}

console.log(`🎉 ${uniqueItems.length} 篇新文章将继续处理`);

// 🎯 返回你原来的数据结构，下一个节点可以正常接收！
return uniqueItems;
