# N8N增量RSS工作流配置指南

## 🎯 目标
只抓取过去30分钟的RSS文章，避免重复处理，节省MongoDB空间。

## 📋 工作流结构

```
1. ⏰ <PERSON>ron Trigger (每30分钟)
   ↓
2. 📡 RSS Feed Reader
   ↓
3. 🔍 时间过滤器 (JavaScript)
   ↓
4. 📝 数据清理器 (JavaScript)
   ↓
5. 🧠 向量化处理
   ↓
6. 💾 MongoDB Vector Store
```

## ⚙️ 详细配置

### 1. <PERSON>ron Trigger 节点
```json
{
  "rule": {
    "interval": [
      {
        "field": "minute",
        "expression": "*/30"
      }
    ]
  }
}
```
**说明**: 每30分钟触发一次，与时间过滤器的30分钟窗口匹配。

### 2. RSS Feed Reader 节点
```json
{
  "url": "你的RSS源URL",
  "options": {
    "ignoreSSL": false,
    "timeout": 10000
  }
}
```
**重要**: 不要在这里设置时间过滤，让它获取所有文章，我们在下一步过滤。

### 3. 时间过滤器 (JavaScript Code 节点)

**节点名称**: `RSS时间过滤器`
**代码**: 使用 `n8n_rss_time_filter.js` 的内容

**关键配置**:
```javascript
const FILTER_MINUTES = 30; // 可调整为15, 30, 60分钟
```

### 4. 数据清理器 (JavaScript Code 节点)

**节点名称**: `数据清理和标准化`
**代码**: 
```javascript
const processedItems = [];
const items = $input.all();

// 如果时间过滤器返回空数组，直接停止
if (!items || items.length === 0) {
    console.log("🛑 没有新文章需要处理");
    return [];
}

console.log(`📝 开始清理 ${items.length} 篇新文章`);

// 文本清理函数
function cleanAndValidateText(text) {
    if (!text || typeof text !== 'string') {
        return null;
    }
    
    const cleaned = text
        .trim()
        .replace(/[\r\n\t]+/g, ' ')
        .replace(/\s+/g, ' ')
        .replace(/[^\u4e00-\u9fa5\u0030-\u0039\u0041-\u005a\u0061-\u007a\s\.,!?;:()（），。！？；：\-]/g, '')
        .substring(0, 800);
    
    return cleaned.length >= 3 ? cleaned : null;
}

// 生成内容哈希
function generateContentHash(title, url, time) {
    const content = `${title}_${url}_${time}`;
    return content.split('').reduce((a, b) => {
        a = ((a << 5) - a) + b.charCodeAt(0);
        return a & a;
    }, 0);
}

for (const item of items) {
    try {
        const data = item.json;
        
        // 清理标题
        const cleanTitle = cleanAndValidateText(data.title || data.content || data.description || "");
        if (!cleanTitle) {
            console.log("⚠️ 跳过无效标题");
            continue;
        }
        
        // 清理内容
        const content = cleanAndValidateText(
            data.content || data.description || data.summary || cleanTitle
        ) || cleanTitle;
        
        // 生成唯一ID
        const contentHash = Math.abs(generateContentHash(
            cleanTitle,
            data.link || data.url || "",
            data.standardized_time
        ));
        
        // 创建标准化文档
        const document = {
            pageContent: content,
            metadata: {
                title: cleanTitle,
                published_date: data.standardized_time,
                article_id: `rss_${contentHash}`,
                content_hash: `hash_${contentHash}`,
                source: data.source || "rss_feed",
                url: data.link || data.url || "",
                category: data.category || "general",
                processed_at: new Date().toISOString(),
                minutes_ago: data.minutes_ago || 0,
                time_source: data.time_source || "unknown",
                is_incremental: true
            }
        };
        
        processedItems.push(document);
        console.log(`✅ 处理完成: ${cleanTitle.substring(0, 40)}...`);
        
    } catch (error) {
        console.log(`❌ 处理错误: ${error.message}`);
        continue;
    }
}

console.log(`🎉 清理完成: ${processedItems.length} 篇文章准备向量化`);
return processedItems;
```

### 5. MongoDB Vector Store 节点

**配置**:
```json
{
  "operation": "insertMany",
  "collection": "articles",
  "database": "cauldron_rss",
  "options": {
    "upsert": false,
    "ordered": false
  }
}
```

**重要**: 设置 `ordered: false` 以提高性能，即使某个文档插入失败也继续处理其他文档。

## 🔧 高级配置选项

### 时间窗口调整
根据RSS源的更新频率调整：
- **高频源** (如新闻): 15分钟
- **中频源** (如博客): 30分钟  
- **低频源** (如周报): 60分钟

### 错误处理
在每个JavaScript节点添加错误处理：
```javascript
try {
    // 主要逻辑
} catch (error) {
    console.log(`❌ 节点错误: ${error.message}`);
    // 返回空数组或错误信息
    return [];
}
```

### 监控和日志
添加Webhook节点发送处理统计：
```javascript
// 在最后添加统计节点
const stats = {
    timestamp: new Date().toISOString(),
    processed_articles: $input.all().length,
    workflow: "incremental_rss",
    status: "success"
};

// 发送到监控系统或日志
return [{ json: stats }];
```

## 📊 预期效果

### 空间节省
- **之前**: 每次处理所有RSS文章 (可能重复)
- **现在**: 每30分钟只处理新文章
- **节省**: 减少90%+的重复数据

### 性能提升
- **处理时间**: 从几分钟降到几秒
- **网络流量**: 大幅减少
- **MongoDB写入**: 只写入真正的新数据

### 数据质量
- **去重**: 基于内容哈希自动去重
- **时效性**: 只保留最新30分钟的数据
- **一致性**: 标准化的时间和格式

## 🚨 注意事项

1. **时间同步**: 确保N8N服务器时间准确
2. **RSS源时区**: 不同RSS源可能使用不同时区
3. **网络延迟**: 考虑RSS源的更新延迟
4. **错误恢复**: 如果某次执行失败，下次会自动补齐

## 🔍 调试技巧

### 查看过滤日志
在N8N执行历史中查看JavaScript节点的console.log输出。

### 测试时间过滤
临时修改 `FILTER_MINUTES` 为更大值 (如120) 来测试过滤逻辑。

### 验证数据质量
定期运行MongoDB空间管理器检查重复数据。
