#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AutoGen集成MongoDB向量化RSS内容
让稷下学宫的AI智能体能够访问和分析RSS数据
"""

import asyncio
import json
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from dataclasses import dataclass

import pymongo
from pymilvus import MilvusClient
import autogen
from autogen import ConversableAgent, GroupChat, GroupChatManager

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


@dataclass
class RSSContent:
    """RSS内容数据结构"""
    article_id: str
    title: str
    content: str
    published_time: str
    source: str
    vector_id: Optional[str] = None
    similarity_score: Optional[float] = None


class MongoDBRSSRetriever:
    """MongoDB RSS内容检索器"""
    
    def __init__(self, mongo_uri: str, database_name: str = "rss_database"):
        self.client = pymongo.MongoClient(mongo_uri)
        self.db = self.client[database_name]
        self.articles_collection = self.db.articles
        
    def get_recent_articles(self, hours: int = 24, limit: int = 50) -> List[RSSContent]:
        """获取最近的RSS文章"""
        try:
            # 计算时间范围
            start_time = datetime.now() - timedelta(hours=hours)
            
            # 查询最近文章
            query = {
                "published_time": {"$gte": start_time.isoformat()},
                "processed": True
            }
            
            cursor = self.articles_collection.find(query).sort("published_time", -1).limit(limit)
            
            articles = []
            for doc in cursor:
                article = RSSContent(
                    article_id=doc.get("article_id", ""),
                    title=doc.get("title", ""),
                    content=doc.get("content", doc.get("title", "")),
                    published_time=doc.get("published_time", ""),
                    source=doc.get("source", "unknown")
                )
                articles.append(article)
            
            logger.info(f"检索到 {len(articles)} 篇最近文章")
            return articles
            
        except Exception as e:
            logger.error(f"MongoDB检索错误: {e}")
            return []
    
    def search_articles_by_keywords(self, keywords: List[str], limit: int = 20) -> List[RSSContent]:
        """根据关键词搜索文章"""
        try:
            # 构建搜索查询
            search_pattern = "|".join(keywords)
            query = {
                "$or": [
                    {"title": {"$regex": search_pattern, "$options": "i"}},
                    {"content": {"$regex": search_pattern, "$options": "i"}}
                ],
                "processed": True
            }
            
            cursor = self.articles_collection.find(query).sort("published_time", -1).limit(limit)
            
            articles = []
            for doc in cursor:
                article = RSSContent(
                    article_id=doc.get("article_id", ""),
                    title=doc.get("title", ""),
                    content=doc.get("content", doc.get("title", "")),
                    published_time=doc.get("published_time", ""),
                    source=doc.get("source", "unknown")
                )
                articles.append(article)
            
            logger.info(f"关键词搜索到 {len(articles)} 篇文章")
            return articles
            
        except Exception as e:
            logger.error(f"关键词搜索错误: {e}")
            return []


class MilvusVectorRetriever:
    """Milvus向量检索器"""
    
    def __init__(self, milvus_uri: str, milvus_token: str, collection_name: str = "ifuleyou"):
        self.client = MilvusClient(uri=milvus_uri, token=milvus_token)
        self.collection_name = collection_name
        
    def vector_search(self, query_text: str, limit: int = 10) -> List[Dict]:
        """向量搜索"""
        try:
            # 这里需要先将query_text向量化，简化起见先用文本搜索
            search_results = self.client.search(
                collection_name=self.collection_name,
                data=[query_text],  # 实际应该是向量
                limit=limit,
                output_fields=["title", "published_date", "article_id", "source"]
            )
            
            logger.info(f"向量搜索到 {len(search_results)} 条结果")
            return search_results
            
        except Exception as e:
            logger.error(f"Milvus搜索错误: {e}")
            return []


class RSSKnowledgeBase:
    """RSS知识库集成器"""
    
    def __init__(self, mongo_uri: str, milvus_uri: str, milvus_token: str):
        self.mongo_retriever = MongoDBRSSRetriever(mongo_uri)
        self.milvus_retriever = MilvusVectorRetriever(milvus_uri, milvus_token)
        
    def get_market_context(self, query: str, hours: int = 24) -> Dict[str, Any]:
        """获取市场上下文信息"""
        try:
            # 1. 获取最近文章
            recent_articles = self.mongo_retriever.get_recent_articles(hours=hours, limit=20)
            
            # 2. 关键词搜索
            keywords = self._extract_keywords(query)
            keyword_articles = self.mongo_retriever.search_articles_by_keywords(keywords, limit=10)
            
            # 3. 向量搜索（如果可用）
            vector_results = self.milvus_retriever.vector_search(query, limit=5)
            
            # 4. 整合结果
            context = {
                "query": query,
                "timestamp": datetime.now().isoformat(),
                "recent_articles": [self._article_to_dict(a) for a in recent_articles[:10]],
                "keyword_articles": [self._article_to_dict(a) for a in keyword_articles],
                "vector_results": vector_results,
                "summary": self._generate_context_summary(recent_articles, keyword_articles)
            }
            
            return context
            
        except Exception as e:
            logger.error(f"获取市场上下文错误: {e}")
            return {"error": str(e)}
    
    def _extract_keywords(self, query: str) -> List[str]:
        """提取查询关键词"""
        # 简单的关键词提取，实际可以用更复杂的NLP
        financial_keywords = [
            "股市", "股票", "投资", "基金", "债券", "期货", "外汇",
            "美联储", "央行", "利率", "通胀", "GDP", "CPI",
            "科技股", "金融股", "消费股", "医药股", "新能源",
            "比特币", "加密货币", "区块链", "NFT"
        ]
        
        keywords = []
        query_lower = query.lower()
        
        for keyword in financial_keywords:
            if keyword in query or keyword.lower() in query_lower:
                keywords.append(keyword)
        
        # 如果没有匹配到金融关键词，使用查询本身的词汇
        if not keywords:
            keywords = [word for word in query.split() if len(word) > 1]
        
        return keywords[:5]  # 限制关键词数量
    
    def _article_to_dict(self, article: RSSContent) -> Dict:
        """将文章对象转换为字典"""
        return {
            "article_id": article.article_id,
            "title": article.title,
            "content": article.content[:200] + "..." if len(article.content) > 200 else article.content,
            "published_time": article.published_time,
            "source": article.source
        }
    
    def _generate_context_summary(self, recent_articles: List[RSSContent], 
                                 keyword_articles: List[RSSContent]) -> str:
        """生成上下文摘要"""
        try:
            recent_count = len(recent_articles)
            keyword_count = len(keyword_articles)
            
            # 提取主要来源
            sources = set()
            for article in recent_articles[:10]:
                sources.add(article.source)
            
            # 提取热门话题（简单统计标题中的词汇）
            title_words = []
            for article in recent_articles[:10]:
                title_words.extend(article.title.split())
            
            # 简单的词频统计
            word_count = {}
            for word in title_words:
                if len(word) > 2:  # 过滤短词
                    word_count[word] = word_count.get(word, 0) + 1
            
            hot_topics = sorted(word_count.items(), key=lambda x: x[1], reverse=True)[:5]
            
            summary = f"""
            数据摘要:
            - 最近24小时文章: {recent_count}篇
            - 相关文章: {keyword_count}篇  
            - 主要来源: {', '.join(list(sources)[:3])}
            - 热门话题: {', '.join([topic[0] for topic in hot_topics])}
            """
            
            return summary.strip()
            
        except Exception as e:
            return f"摘要生成错误: {e}"


class AutoGenRSSIntegration:
    """AutoGen RSS集成系统"""
    
    def __init__(self, mongo_uri: str, milvus_uri: str, milvus_token: str):
        self.knowledge_base = RSSKnowledgeBase(mongo_uri, milvus_uri, milvus_token)
        self.setup_agents()
    
    def setup_agents(self):
        """设置AutoGen智能体"""
        
        # 配置LLM
        llm_config = {
            "model": "gpt-4o-mini",
            "api_key": "your-openai-key",  # 需要配置
            "temperature": 0.1
        }
        
        # 数据分析师 - 负责RSS数据检索和分析
        self.data_analyst = ConversableAgent(
            name="数据分析师",
            system_message="""你是稷下学宫的数据分析师，专门负责RSS新闻数据的检索和分析。
            
            你的职责：
            1. 根据用户查询检索相关的RSS新闻数据
            2. 分析新闻内容，提取关键信息
            3. 为其他智能体提供数据支持
            4. 总结市场动态和趋势
            
            你可以调用get_market_context函数来获取RSS数据。
            """,
            llm_config=llm_config,
            function_map={"get_market_context": self.get_market_context_wrapper}
        )
        
        # 吕洞宾 - 技术分析师
        self.ludongbin = ConversableAgent(
            name="吕洞宾",
            system_message="""你是吕洞宾，稷下学宫的技术分析专家。
            
            你的特点：
            - 理性分析，数据驱动
            - 专注技术指标和图表分析
            - 语言简洁有力，逻辑清晰
            - 口头禅："数据不会说谎"
            
            基于数据分析师提供的RSS新闻，进行技术面分析。
            """,
            llm_config=llm_config
        )
        
        # 何仙姑 - 情绪分析师
        self.hexiangu = ConversableAgent(
            name="何仙姑",
            system_message="""你是何仙姑，稷下学宫的市场情绪分析专家。
            
            你的特点：
            - 敏锐的直觉和情感洞察
            - 善于分析市场情绪和投资者心理
            - 语言温和但深刻
            - 口头禅："感受市场的心跳"
            
            基于RSS新闻分析市场情绪和投资者心理状态。
            """,
            llm_config=llm_config
        )
        
        # 铁拐李 - 逆向分析师
        self.tieguaili = ConversableAgent(
            name="铁拐李",
            system_message="""你是铁拐李，稷下学宫的逆向思维专家。
            
            你的特点：
            - 质疑主流观点，逆向思考
            - 寻找市场盲点和误区
            - 语言犀利，直言不讳
            - 口头禅："你们都错了"
            
            对RSS新闻进行逆向分析，质疑主流观点。
            """,
            llm_config=llm_config
        )
        
        # 群聊管理器
        self.group_chat = GroupChat(
            agents=[self.data_analyst, self.ludongbin, self.hexiangu, self.tieguaili],
            messages=[],
            max_round=10
        )
        
        self.manager = GroupChatManager(
            groupchat=self.group_chat,
            llm_config=llm_config
        )
    
    def get_market_context_wrapper(self, query: str, hours: int = 24) -> str:
        """AutoGen函数调用包装器"""
        try:
            context = self.knowledge_base.get_market_context(query, hours)
            
            # 格式化返回结果
            if "error" in context:
                return f"数据检索错误: {context['error']}"
            
            result = f"""
            查询: {context['query']}
            时间: {context['timestamp']}
            
            {context['summary']}
            
            最新文章摘要:
            """
            
            for i, article in enumerate(context['recent_articles'][:5], 1):
                result += f"\n{i}. {article['title']} ({article['source']})"
            
            if context['keyword_articles']:
                result += f"\n\n相关文章:"
                for i, article in enumerate(context['keyword_articles'][:3], 1):
                    result += f"\n{i}. {article['title']} ({article['source']})"
            
            return result
            
        except Exception as e:
            return f"数据检索失败: {e}"
    
    async def analyze_market_topic(self, topic: str) -> str:
        """分析市场话题"""
        try:
            # 启动群聊分析
            initial_message = f"""
            请分析以下市场话题: {topic}
            
            数据分析师，请先检索相关的RSS新闻数据，然后各位专家基于数据进行分析。
            """
            
            # 开始群聊
            chat_result = self.data_analyst.initiate_chat(
                self.manager,
                message=initial_message,
                max_turns=8
            )
            
            return chat_result
            
        except Exception as e:
            logger.error(f"市场分析错误: {e}")
            return f"分析失败: {e}"


# 使用示例和测试
async def main():
    """主函数示例"""
    
    # 配置信息（需要根据实际情况修改）
    config = {
        "mongo_uri": "mongodb://localhost:27017/",
        "milvus_uri": "https://your-milvus-endpoint",
        "milvus_token": "your-milvus-token"
    }
    
    # 创建集成系统
    autogen_rss = AutoGenRSSIntegration(**config)
    
    # 测试RSS数据检索
    print("🔍 测试RSS数据检索...")
    context = autogen_rss.knowledge_base.get_market_context("美联储加息", hours=48)
    print(f"检索结果: {json.dumps(context, ensure_ascii=False, indent=2)}")
    
    # 测试AutoGen分析
    print("\n🤖 测试AutoGen市场分析...")
    analysis_result = await autogen_rss.analyze_market_topic("当前科技股投资机会")
    print(f"分析结果: {analysis_result}")


if __name__ == "__main__":
    # 运行示例
    asyncio.run(main())