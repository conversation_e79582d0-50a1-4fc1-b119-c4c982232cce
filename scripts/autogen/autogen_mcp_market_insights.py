#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AutoGen MCP市场洞察分析
通过MCP直接分析向量数据库内容，发现市场奇闻异事和全息性模式
"""

import asyncio
import json
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
import numpy as np

import pymongo
from pymongo import MongoClient
import autogen
from autogen import ConversableAgent

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class MarketInsightsMCP:
    """市场洞察MCP工具"""
    
    def __init__(self, mongo_uri: str, database_name: str = "rss_database"):
        self.client = MongoClient(mongo_uri)
        self.db = self.client[database_name]
        self.collection = self.db.rss_vectors
        
    def analyze_market_anomalies(self, hours: int = 6) -> List[Dict]:
        """分析市场异常事件和奇闻异事"""
        try:
            # 获取指定时间范围内的所有向量化内容
            start_time = datetime.now() - timedelta(hours=hours)
            
            query = {
                "timestamp": {"$gte": start_time},
                "vector": {"$exists": True, "$ne": None}
            }
            
            cursor = self.collection.find(query).sort("timestamp", -1)
            
            market_events = []
            for doc in cursor:
                # 分析每条新闻的异常性
                event = {
                    "title": doc.get("title", ""),
                    "content": doc.get("content", ""),
                    "source": doc.get("source", ""),
                    "timestamp": doc.get("timestamp", ""),
                    "anomaly_score": self._calculate_anomaly_score(doc),
                    "event_type": self._classify_event_type(doc),
                    "market_impact": self._assess_market_impact(doc),
                    "holistic_connections": self._find_holistic_connections(doc, market_events)
                }
                market_events.append(event)
            
            # 按异常分数排序，找出最奇特的事件
            market_events.sort(key=lambda x: x["anomaly_score"], reverse=True)
            
            return market_events[:20]  # 返回前20个最异常的事件
            
        except Exception as e:
            logger.error(f"市场异常分析错误: {e}")
            return []
    
    def discover_holistic_patterns(self, events: List[Dict]) -> Dict[str, Any]:
        """发现全息性模式"""
        try:
            patterns = {
                "temporal_clusters": self._find_temporal_clusters(events),
                "thematic_resonance": self._find_thematic_resonance(events),
                "cross_market_synchronicity": self._find_cross_market_sync(events),
                "sentiment_waves": self._analyze_sentiment_waves(events),
                "fractal_patterns": self._detect_fractal_patterns(events),
                "butterfly_effects": self._identify_butterfly_effects(events)
            }
            
            return patterns
            
        except Exception as e:
            logger.error(f"全息模式发现错误: {e}")
            return {}
    
    def _calculate_anomaly_score(self, doc: Dict) -> float:
        """计算异常分数"""
        score = 0.0
        title = doc.get("title", "").lower()
        content = doc.get("content", "").lower()
        
        # 异常关键词检测
        anomaly_keywords = {
            "突发": 3.0, "暴涨": 2.5, "暴跌": 2.5, "崩盘": 3.0, "黑天鹅": 3.0,
            "史上最": 2.0, "创纪录": 2.0, "前所未有": 2.5, "罕见": 2.0,
            "紧急": 2.5, "停牌": 2.0, "熔断": 3.0, "闪崩": 3.0,
            "神秘": 1.5, "意外": 1.5, "震惊": 1.5, "诡异": 2.0
        }
        
        for keyword, weight in anomaly_keywords.items():
            if keyword in title:
                score += weight * 2  # 标题权重更高
            if keyword in content:
                score += weight
        
        # 数字异常检测（大幅波动）
        import re
        percentage_pattern = r'(\d+(?:\.\d+)?)%'
        percentages = re.findall(percentage_pattern, title + content)
        
        for pct in percentages:
            pct_val = float(pct)
            if pct_val > 10:  # 超过10%的变动
                score += min(pct_val / 10, 3.0)
        
        return score
    
    def _classify_event_type(self, doc: Dict) -> str:
        """分类事件类型"""
        title = doc.get("title", "").lower()
        content = doc.get("content", "").lower()
        text = title + " " + content
        
        if any(word in text for word in ["央行", "美联储", "货币政策", "利率"]):
            return "货币政策异动"
        elif any(word in text for word in ["地缘", "战争", "制裁", "冲突"]):
            return "地缘政治事件"
        elif any(word in text for word in ["科技", "AI", "芯片", "半导体"]):
            return "科技突破/危机"
        elif any(word in text for word in ["能源", "石油", "天然气", "电力"]):
            return "能源市场异动"
        elif any(word in text for word in ["加密", "比特币", "数字货币"]):
            return "加密货币事件"
        elif any(word in text for word in ["银行", "金融", "信贷", "债务"]):
            return "金融系统事件"
        else:
            return "其他市场异动"
    
    def _assess_market_impact(self, doc: Dict) -> str:
        """评估市场影响"""
        title = doc.get("title", "").lower()
        content = doc.get("content", "").lower()
        text = title + " " + content
        
        high_impact_words = ["全球", "系统性", "危机", "崩盘", "救市"]
        medium_impact_words = ["市场", "股市", "指数", "板块"]
        
        if any(word in text for word in high_impact_words):
            return "系统性影响"
        elif any(word in text for word in medium_impact_words):
            return "市场级影响"
        else:
            return "局部影响"
    
    def _find_holistic_connections(self, current_doc: Dict, previous_events: List[Dict]) -> List[str]:
        """寻找与之前事件的全息连接"""
        connections = []
        current_title = current_doc.get("title", "").lower()
        
        # 寻找主题共振
        for event in previous_events[-5:]:  # 只看最近5个事件
            if event.get("event_type") == self._classify_event_type(current_doc):
                connections.append(f"主题共振: {event.get('event_type')}")
            
            # 寻找因果链
            if any(word in current_title for word in ["因", "由于", "受", "影响"]):
                connections.append(f"因果链条: 可能与{event.get('title', '')[:20]}相关")
        
        return connections
    
    def _find_temporal_clusters(self, events: List[Dict]) -> List[Dict]:
        """寻找时间聚集模式"""
        clusters = []
        
        # 按小时分组
        hourly_groups = {}
        for event in events:
            try:
                timestamp = event.get("timestamp", "")
                if isinstance(timestamp, str):
                    hour = timestamp[:13]  # YYYY-MM-DDTHH
                    if hour not in hourly_groups:
                        hourly_groups[hour] = []
                    hourly_groups[hour].append(event)
            except:
                continue
        
        # 找出事件密集的时间段
        for hour, hour_events in hourly_groups.items():
            if len(hour_events) >= 3:  # 一小时内3个以上异常事件
                clusters.append({
                    "time_window": hour,
                    "event_count": len(hour_events),
                    "cluster_type": "时间密集型异常",
                    "events": [e["title"] for e in hour_events[:3]]
                })
        
        return clusters
    
    def _find_thematic_resonance(self, events: List[Dict]) -> List[Dict]:
        """寻找主题共振"""
        theme_groups = {}
        
        for event in events:
            theme = event.get("event_type", "未知")
            if theme not in theme_groups:
                theme_groups[theme] = []
            theme_groups[theme].append(event)
        
        resonance = []
        for theme, theme_events in theme_groups.items():
            if len(theme_events) >= 2:
                resonance.append({
                    "theme": theme,
                    "resonance_strength": len(theme_events),
                    "pattern": "主题共振",
                    "events": [e["title"] for e in theme_events[:3]]
                })
        
        return resonance
    
    def _find_cross_market_sync(self, events: List[Dict]) -> List[Dict]:
        """寻找跨市场同步性"""
        market_regions = {
            "美股": ["纳斯达克", "道琼斯", "标普", "美股"],
            "A股": ["上证", "深证", "创业板", "A股"],
            "港股": ["恒生", "港股", "香港"],
            "欧股": ["欧洲", "德国", "英国", "法国"],
            "加密": ["比特币", "以太坊", "加密", "数字货币"]
        }
        
        market_events = {market: [] for market in market_regions.keys()}
        
        for event in events:
            title_content = event.get("title", "") + " " + event.get("content", "")
            for market, keywords in market_regions.items():
                if any(keyword in title_content for keyword in keywords):
                    market_events[market].append(event)
                    break
        
        sync_patterns = []
        active_markets = [m for m, evts in market_events.items() if len(evts) > 0]
        
        if len(active_markets) >= 3:
            sync_patterns.append({
                "pattern": "全球市场同步异动",
                "markets": active_markets,
                "sync_strength": len(active_markets),
                "description": f"{len(active_markets)}个市场同时出现异常"
            })
        
        return sync_patterns
    
    def _analyze_sentiment_waves(self, events: List[Dict]) -> Dict[str, Any]:
        """分析情绪波动"""
        positive_words = ["涨", "升", "好", "利好", "突破", "创新高"]
        negative_words = ["跌", "降", "坏", "利空", "破位", "创新低"]
        
        sentiment_timeline = []
        
        for event in events:
            text = event.get("title", "") + " " + event.get("content", "")
            pos_count = sum(1 for word in positive_words if word in text)
            neg_count = sum(1 for word in negative_words if word in text)
            
            sentiment_score = pos_count - neg_count
            sentiment_timeline.append({
                "timestamp": event.get("timestamp", ""),
                "sentiment": sentiment_score,
                "title": event.get("title", "")
            })
        
        # 计算情绪波动幅度
        sentiments = [s["sentiment"] for s in sentiment_timeline]
        if sentiments:
            volatility = np.std(sentiments) if len(sentiments) > 1 else 0
            return {
                "sentiment_volatility": volatility,
                "dominant_sentiment": "正面" if np.mean(sentiments) > 0 else "负面",
                "sentiment_swings": len([s for s in sentiments if abs(s) > 2])
            }
        
        return {}
    
    def _detect_fractal_patterns(self, events: List[Dict]) -> List[Dict]:
        """检测分形模式"""
        patterns = []
        
        # 寻找重复的模式结构
        event_types = [e.get("event_type", "") for e in events]
        
        # 检测周期性模式
        for i in range(len(event_types) - 2):
            pattern = event_types[i:i+3]
            if pattern.count(pattern[0]) == 3:  # 三个相同类型事件
                patterns.append({
                    "pattern_type": "三重共振",
                    "theme": pattern[0],
                    "fractal_level": "微观分形",
                    "description": f"{pattern[0]}事件的三重出现"
                })
        
        return patterns
    
    def _identify_butterfly_effects(self, events: List[Dict]) -> List[Dict]:
        """识别蝴蝶效应"""
        butterfly_effects = []
        
        # 寻找小事件可能引发大影响的模式
        for i, event in enumerate(events):
            if event.get("market_impact") == "局部影响" and event.get("anomaly_score", 0) > 2:
                # 检查后续是否有系统性影响事件
                for j in range(i+1, min(i+5, len(events))):
                    if events[j].get("market_impact") == "系统性影响":
                        butterfly_effects.append({
                            "trigger_event": event.get("title", ""),
                            "cascade_event": events[j].get("title", ""),
                            "effect_type": "潜在蝴蝶效应",
                            "time_gap": j - i
                        })
                        break
        
        return butterfly_effects


class AutoGenMarketAnalyst:
    """AutoGen市场分析师"""
    
    def __init__(self, mongo_uri: str):
        self.mcp_tool = MarketInsightsMCP(mongo_uri)
        self.setup_agents()
    
    def setup_agents(self):
        """设置AutoGen智能体"""
        
        llm_config = {
            "model": "gpt-4o-mini",
            "api_key": "your-openai-key",
            "temperature": 0.3
        }
        
        # 市场异象探测师
        self.anomaly_detective = ConversableAgent(
            name="市场异象探测师",
            system_message="""你是市场异象探测师，专门发现和分析市场中的奇闻异事。

你的能力：
1. 通过MCP工具分析向量数据库中的市场事件
2. 识别异常模式和奇特现象
3. 发现事件之间的隐藏联系
4. 评估异常事件的市场影响

你的特点：
- 敏锐的洞察力，善于发现异常
- 逻辑思维严密，不放过任何细节
- 善于从混乱中找出规律
- 语言生动，善于描述奇异现象

可用MCP工具：
- analyze_market_anomalies: 分析市场异常事件
- discover_holistic_patterns: 发现全息性模式
""",
            llm_config=llm_config,
            function_map={
                "analyze_market_anomalies": self.analyze_market_anomalies_wrapper,
                "discover_holistic_patterns": self.discover_holistic_patterns_wrapper
            }
        )
    
    def analyze_market_anomalies_wrapper(self, hours: int = 6) -> str:
        """MCP工具包装器：分析市场异常"""
        try:
            anomalies = self.mcp_tool.analyze_market_anomalies(hours)
            
            if not anomalies:
                return f"过去{hours}小时内未发现显著的市场异常事件"
            
            # 格式化输出
            output = f"🔍 过去{hours}小时市场异象分析:\n\n"
            
            # 显示前5个最异常的事件
            for i, event in enumerate(anomalies[:5], 1):
                output += f"🚨 异象 {i} (异常分数: {event['anomaly_score']:.1f}):\n"
                output += f"📰 {event['title']}\n"
                output += f"🏷️ 类型: {event['event_type']}\n"
                output += f"📊 影响: {event['market_impact']}\n"
                output += f"🕐 时间: {event['timestamp']}\n"
                
                if event['holistic_connections']:
                    output += f"🔗 全息连接: {', '.join(event['holistic_connections'])}\n"
                
                output += f"📝 内容: {event['content'][:100]}...\n\n"
            
            return output
            
        except Exception as e:
            return f"市场异象分析失败: {e}"
    
    def discover_holistic_patterns_wrapper(self, hours: int = 6) -> str:
        """MCP工具包装器：发现全息模式"""
        try:
            # 先获取异常事件
            anomalies = self.mcp_tool.analyze_market_anomalies(hours)
            
            if not anomalies:
                return "无足够数据进行全息模式分析"
            
            # 发现全息模式
            patterns = self.mcp_tool.discover_holistic_patterns(anomalies)
            
            output = f"🌀 过去{hours}小时全息性模式分析:\n\n"
            
            # 时间聚集模式
            if patterns.get("temporal_clusters"):
                output += "⏰ 时间聚集模式:\n"
                for cluster in patterns["temporal_clusters"]:
                    output += f"  • {cluster['time_window']}: {cluster['event_count']}个异常事件聚集\n"
                output += "\n"
            
            # 主题共振
            if patterns.get("thematic_resonance"):
                output += "🎵 主题共振模式:\n"
                for resonance in patterns["thematic_resonance"]:
                    output += f"  • {resonance['theme']}: {resonance['resonance_strength']}次共振\n"
                output += "\n"
            
            # 跨市场同步
            if patterns.get("cross_market_synchronicity"):
                output += "🌐 跨市场同步模式:\n"
                for sync in patterns["cross_market_synchronicity"]:
                    output += f"  • {sync['pattern']}: {sync['description']}\n"
                output += "\n"
            
            # 情绪波动
            if patterns.get("sentiment_waves"):
                waves = patterns["sentiment_waves"]
                output += f"😊 情绪波动分析:\n"
                output += f"  • 波动强度: {waves.get('sentiment_volatility', 0):.2f}\n"
                output += f"  • 主导情绪: {waves.get('dominant_sentiment', '中性')}\n"
                output += f"  • 剧烈波动次数: {waves.get('sentiment_swings', 0)}\n\n"
            
            # 蝴蝶效应
            if patterns.get("butterfly_effects"):
                output += "🦋 潜在蝴蝶效应:\n"
                for effect in patterns["butterfly_effects"]:
                    output += f"  • 触发: {effect['trigger_event'][:30]}...\n"
                    output += f"    → 级联: {effect['cascade_event'][:30]}...\n"
                output += "\n"
            
            return output
            
        except Exception as e:
            return f"全息模式分析失败: {e}"
    
    async def analyze_market_mysteries(self, hours: int = 6) -> str:
        """分析市场奇闻异事"""
        try:
            print(f"🔮 开始分析过去{hours}小时的市场奇闻异事...\n")
            
            # 让市场异象探测师开始工作
            analysis_prompt = f"""
            请使用MCP工具分析过去{hours}小时内的市场异象和奇闻异事。
            
            具体任务：
            1. 首先调用analyze_market_anomalies获取异常事件
            2. 然后调用discover_holistic_patterns发现全息性模式
            3. 最后综合分析这些异象的深层含义
            
            请重点关注：
            - 最奇特和异常的市场事件
            - 事件之间的神秘联系
            - 可能的全息性和分形模式
            - 潜在的蝴蝶效应和连锁反应
            """
            
            # 生成分析报告
            response = self.anomaly_detective.generate_reply(
                messages=[{"role": "user", "content": analysis_prompt}]
            )
            
            return response
            
        except Exception as e:
            return f"市场奇闻异事分析失败: {e}"


# 演示函数
async def demonstrate_market_insights():
    """演示市场洞察分析"""
    
    print("🚀 AutoGen MCP市场洞察分析演示")
    print("=" * 60)
    
    # 模拟MongoDB连接
    mongo_uri = "mongodb+srv://demo:****@cluster.mongodb.net/rss_database"
    
    # 创建分析师
    analyst = AutoGenMarketAnalyst(mongo_uri)
    
    # 模拟分析过程
    print("🔍 正在分析过去6小时的市场异象...")
    
    # 直接调用MCP工具进行演示
    anomalies_result = analyst.analyze_market_anomalies_wrapper(hours=6)
    print(anomalies_result)
    
    print("\n" + "=" * 60)
    print("🌀 正在发现全息性模式...")
    
    patterns_result = analyst.discover_holistic_patterns_wrapper(hours=6)
    print(patterns_result)
    
    print("\n" + "=" * 60)
    print("🎭 市场异象探测师的综合分析:")
    
    # 模拟智能体的综合分析
    comprehensive_analysis = """
🔮 市场异象探测师报告:

基于对向量数据库的深度分析，我发现了以下奇闻异事：

🚨 重大异象发现:
1. 【时间聚集异象】在14:00-15:00时间窗口内，出现了3个高异常分数事件的聚集，这种密集度在统计学上极为罕见。

2. 【跨市场共振】美股、A股、加密货币市场同时出现异常波动，显示出强烈的全息性特征，仿佛整个金融宇宙在同一频率上震动。

3. 【情绪分形】发现了三重共振模式：货币政策事件连续三次出现，形成了完美的分形结构，暗示着更深层的市场规律。

🦋 蝴蝶效应追踪:
一个看似微不足道的"某小型科技公司技术突破"事件，可能触发了后续的"全球科技股集体上涨"，展现了典型的蝴蝶效应特征。

🌀 全息性洞察:
市场数据显示出强烈的全息性质——每个局部事件都包含着整体市场的信息。这种现象表明，市场可能正处于一个高度敏感的临界状态。

⚡ 预警信号:
基于异象模式分析，建议密切关注接下来2-4小时内的市场动向，可能会有更大规模的系统性事件发生。
    """
    
    print(comprehensive_analysis)
    
    return "✅ 市场洞察分析演示完成"


if __name__ == "__main__":
    asyncio.run(demonstrate_market_insights())