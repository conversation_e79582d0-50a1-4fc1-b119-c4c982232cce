#!/usr/bin/env python3
"""
AutoGen稷下学宫辩论系统
主题：大美丽法案对美国银行股板块的影响（一个月期限）
使用4个API密钥轮换，避免过热
"""

import os
import asyncio
import logging
from datetime import datetime
from typing import List, Dict, Any

# 设置环境变量
os.environ["OPENROUTER_API_KEY_1"] = "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"
os.environ["OPENROUTER_API_KEY_2"] = "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"
os.environ["OPENROUTER_API_KEY_3"] = "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"
os.environ["OPENROUTER_API_KEY_4"] = "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"

try:
    from autogen import ConversableAgent, GroupChat, GroupChatManager
    from autogen.coding import LocalCommandLineCodeExecutor
except ImportError:
    print("❌ AutoGen未安装，请运行: pip install pyautogen")
    exit(1)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('JixiaDebate')

class JixiaAcademyDebate:
    """稷下学宫AutoGen辩论系统"""
    
    def __init__(self):
        self.debate_topic = "大美丽法案对美国银行股板块利多利空分析（一个月投资期限）"
        self.agents = {}
        self.group_chat = None
        self.manager = None
        
        # API密钥轮换配置 - 按发言顺序分配
        self.api_rotation = {
            "吕洞宾": "OPENROUTER_API_KEY_1",    # 乾☰
            "张果老": "OPENROUTER_API_KEY_2",    # 兑☱
            "汉钟离": "OPENROUTER_API_KEY_3",    # 离☲
            "曹国舅": "OPENROUTER_API_KEY_4",    # 震☳
            "铁拐李": "OPENROUTER_API_KEY_1",    # 巽☴ (轮换回1)
            "蓝采和": "OPENROUTER_API_KEY_2",    # 坎☵ (轮换回2)
            "韩湘子": "OPENROUTER_API_KEY_3",    # 艮☶ (轮换回3)
            "何仙姑": "OPENROUTER_API_KEY_4",    # 坤☷ (轮换回4)
            # 三清
            "太上老君": "OPENROUTER_API_KEY_3",
            "灵宝道君": "OPENROUTER_API_KEY_4", 
            "元始天尊": "OPENROUTER_API_KEY_1"
        }
        
        self._create_agents()
        self._setup_group_chat()
    
    def _get_llm_config(self, agent_name: str, model: str, max_tokens: int = 800) -> Dict:
        """获取LLM配置，使用轮换的API密钥"""
        api_key_env = self.api_rotation.get(agent_name, "OPENROUTER_API_KEY_1")

        return {
            "config_list": [{
                "model": model,
                "base_url": "https://openrouter.ai/api/v1",
                "api_key": os.getenv(api_key_env),
                "max_tokens": max_tokens
            }],
            "temperature": 0.7,
            "timeout": 60,
            "cache_seed": None  # 禁用缓存确保每次都是新的回复
        }
    
    def _create_agents(self):
        """创建八仙和三清智能体"""
        
        # 八仙配置 - 按先天八卦顺序，使用可用的免费模型
        baxian_configs = [
            {
                "name": "吕洞宾",
                "model": "deepseek/deepseek-chat:free",
                "system_message": """你是吕洞宾，稷下学宫乾卦代表，价值投资专家。
特点：理性分析，注重基本面，风格保守稳健。
对大美丽法案的银行股影响，你倾向于谨慎分析风险。
请用100字以内简洁表达观点。"""
            },
            {
                "name": "张果老",
                "model": "deepseek/deepseek-chat:free",
                "system_message": """你是张果老，稷下学宫兑卦代表，传统投资专家。
特点：经验丰富，重视历史规律，偏好大盘蓝筹。
从历史经验角度分析大美丽法案对银行股的影响。
请用100字以内表达观点。"""
            },
            {
                "name": "汉钟离",
                "model": "google/gemini-2.0-flash-exp:free",
                "system_message": """你是汉钟离，稷下学宫离卦代表，热点追踪专家。
特点：敏锐捕捉市场热点，偏好激进策略。
你认为大美丽法案是银行股的重大利好机会。
请用100字以内表达观点。"""
            },
            {
                "name": "曹国舅",
                "model": "microsoft/phi-4:free",
                "system_message": """你是曹国舅，稷下学宫震卦代表，机构投资专家。
特点：从机构视角分析，注重资金流向和政策影响。
分析大美丽法案对银行股的机构配置影响。
请用100字以内表达观点。"""
            },
            {
                "name": "铁拐李",
                "model": "meta-llama/llama-3.1-8b-instruct:free",
                "system_message": """你是铁拐李，稷下学宫巽卦代表，逆向投资专家。
特点：独特视角，敢于逆向思维，寻找被错杀的机会。
你倾向于在市场恐慌时寻找银行股的抄底机会。
请用100字以内表达观点。"""
            },
            {
                "name": "蓝采和",
                "model": "mistral/mistral-7b-instruct:free",
                "system_message": """你是蓝采和，稷下学宫坎卦代表，小盘股专家。
特点：关注小盘银行股，寻找被忽视的价值洼地。
分析大美丽法案对中小银行的特殊影响。
请用100字以内表达观点。"""
            },
            {
                "name": "韩湘子",
                "model": "qwen/qwen-2.5-7b-instruct:free",
                "system_message": """你是韩湘子，稷下学宫艮卦代表，新兴投资专家。
特点：关注金融科技，偏好创新型金融服务。
从金融科技角度分析大美丽法案对传统银行的冲击。
请用100字以内表达观点。"""
            },
            {
                "name": "何仙姑",
                "model": "qwen/qwen-2.5-7b-instruct:free",
                "system_message": """你是何仙姑，稷下学宫坤卦代表，ETF配置专家。
特点：稳健配置，注重风险控制，偏好分散投资。
从资产配置角度分析银行股在组合中的权重调整。
请用100字以内表达观点。"""
            }
        ]
        
        # 创建八仙智能体
        for config in baxian_configs:
            self.agents[config["name"]] = ConversableAgent(
                name=config["name"],
                system_message=config["system_message"],
                llm_config=self._get_llm_config(config["name"], config["model"]),
                human_input_mode="NEVER",
                max_consecutive_auto_reply=1
            )
        
        # 三清智能体 - 使用可用的免费模型
        self.agents["太上老君"] = ConversableAgent(
            name="太上老君",
            system_message="""你是太上老君，稷下学宫控场大师。
职责：在八仙发言后，用简短有力的话撩拨双方继续辩论。
风格：斗蛐蛐式挑刺，找出观点矛盾，激发更激烈讨论。
请用50字以内的反问或对比来控场。""",
            llm_config=self._get_llm_config("太上老君", "microsoft/phi-4:free", 400),
            human_input_mode="NEVER",
            max_consecutive_auto_reply=1
        )

        self.agents["灵宝道君"] = ConversableAgent(
            name="灵宝道君",
            system_message="""你是灵宝道君，稷下学宫数据核实专家。
职责：总结八仙的主要观点，核实关键数据，提供客观分析。
风格：基于数据说话，逻辑严密，中立客观。
请用150字以内总结争议焦点和数据支撑。""",
            llm_config=self._get_llm_config("灵宝道君", "deepseek/deepseek-chat:free", 800),
            human_input_mode="NEVER",
            max_consecutive_auto_reply=1
        )

        self.agents["元始天尊"] = ConversableAgent(
            name="元始天尊",
            system_message="""你是元始天尊，稷下学宫最终裁决者。
职责：基于所有辩论，给出明确的多空判断和操作建议。
风格：字字珠玑，直言不讳，一槌定音。
请用50字以内给出最终裁决：买入/卖出/持有，并说明核心理由。""",
            llm_config=self._get_llm_config("元始天尊", "mistral/mistral-7b-instruct:free", 300),
            human_input_mode="NEVER",
            max_consecutive_auto_reply=1
        )
        
        logger.info(f"✅ 创建了 {len(self.agents)} 个智能体")
    
    def _setup_group_chat(self):
        """设置群聊和发言顺序"""
        
        # 按先天八卦顺序 + 三清总结
        speaker_order = [
            "吕洞宾",    # 乾☰
            "张果老",    # 兑☱  
            "汉钟离",    # 离☲
            "曹国舅",    # 震☳
            "铁拐李",    # 巽☴
            "蓝采和",    # 坎☵
            "韩湘子",    # 艮☶
            "何仙姑",    # 坤☷
            "太上老君",  # 控场
            "灵宝道君",  # 总结
            "元始天尊"   # 裁决
        ]
        
        agents_list = [self.agents[name] for name in speaker_order]
        
        self.group_chat = GroupChat(
            agents=agents_list,
            messages=[],
            max_round=11,  # 8仙 + 3清
            speaker_selection_method="round_robin",
            allow_repeat_speaker=False
        )
        
        # 创建管理者（使用单独的API密钥）
        self.manager = GroupChatManager(
            groupchat=self.group_chat,
            llm_config=self._get_llm_config("manager", "deepseek/deepseek-chat:free", 500),
            system_message="你是稷下学宫辩论主持人，负责维持发言顺序。"
        )
        
        logger.info("✅ 群聊设置完成")
    
    async def start_debate(self):
        """启动辩论"""
        logger.info("🚀 稷下学宫辩论开始！")
        logger.info(f"🎯 辩论主题：{self.debate_topic}")
        logger.info("🎭 发言顺序：先天八卦 → 三清总结")
        
        print("\n" + "="*80)
        print("🏛️  稷下学宫辩论：大美丽法案 vs 美国银行股")
        print("="*80)
        
        try:
            # 启动群聊
            result = self.manager.initiate_chat(
                self.agents["吕洞宾"],
                message=f"""
🎯 辩论主题：{self.debate_topic}

各位仙友，现在开始稷下学宫辩论！

请按先天八卦顺序发言，每人用100字以内表达观点：
1. 大美丽法案对银行股是利多还是利空？
2. 一个月投资期限内的操作建议？

现在请吕洞宾（乾卦）首先发言！
""",
                clear_history=True
            )
            
            print("\n" + "="*80)
            print("🎉 稷下学宫辩论结束！")
            print("="*80)
            
            return result
            
        except Exception as e:
            logger.error(f"❌ 辩论过程出错：{e}")
            return None

async def main():
    """主函数"""
    print("🏛️ 启动稷下学宫AutoGen辩论系统...")
    
    # 检查API密钥
    for i in range(1, 5):
        key = os.getenv(f"OPENROUTER_API_KEY_{i}")
        if not key:
            print(f"❌ 缺少 OPENROUTER_API_KEY_{i}")
            return
        print(f"✅ API密钥 {i} 已配置")
    
    # 创建辩论系统
    debate_system = JixiaAcademyDebate()
    
    # 启动辩论
    result = await debate_system.start_debate()
    
    if result:
        print("\n📊 辩论成功完成！")
        
        # 保存辩论记录
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"jixia_debate_{timestamp}.json"
        
        import json
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump({
                "topic": debate_system.debate_topic,
                "timestamp": timestamp,
                "messages": [msg.dict() if hasattr(msg, 'dict') else str(msg) for msg in debate_system.group_chat.messages],
                "participants": list(debate_system.agents.keys())
            }, f, ensure_ascii=False, indent=2)
        
        print(f"📄 辩论记录已保存：{filename}")
    else:
        print("❌ 辩论失败")

if __name__ == "__main__":
    asyncio.run(main())
