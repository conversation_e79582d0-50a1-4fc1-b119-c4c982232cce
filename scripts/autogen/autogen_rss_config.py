#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AutoGen RSS集成配置文件
"""

import os
from typing import Dict, Any

# 数据库配置
DATABASE_CONFIG = {
    "mongodb": {
        "uri": os.getenv("MONGODB_URI", "mongodb://localhost:27017/"),
        "database": "rss_database",
        "collection": "articles"
    },
    
    "milvus": {
        "uri": os.getenv("MILVUS_URI", "https://in03-2a809e6ceec742f.serverless.ali-cn-hangzhou.cloud.zilliz.com.cn:443"),
        "token": os.getenv("MILVUS_TOKEN", "your-milvus-token"),
        "collection": "ifuleyou"
    }
}

# AutoGen配置
AUTOGEN_CONFIG = {
    "llm_config": {
        "model": "gpt-4o-mini",
        "api_key": os.getenv("OPENAI_API_KEY"),
        "temperature": 0.1,
        "timeout": 30
    },
    
    "agents": {
        "data_analyst": {
            "name": "数据分析师",
            "role": "RSS数据检索和分析",
            "functions": ["get_market_context", "search_articles"]
        },
        
        "ludongbin": {
            "name": "吕洞宾",
            "role": "技术分析专家",
            "personality": "理性分析，数据驱动",
            "catchphrase": "数据不会说谎"
        },
        
        "hexiangu": {
            "name": "何仙姑", 
            "role": "情绪分析专家",
            "personality": "敏锐直觉，情感洞察",
            "catchphrase": "感受市场的心跳"
        },
        
        "tieguaili": {
            "name": "铁拐李",
            "role": "逆向分析专家", 
            "personality": "质疑主流，逆向思考",
            "catchphrase": "你们都错了"
        }
    }
}

# RSS检索配置
RSS_CONFIG = {
    "search": {
        "default_hours": 24,
        "max_articles": 50,
        "keywords_limit": 5
    },
    
    "financial_keywords": [
        "股市", "股票", "投资", "基金", "债券", "期货", "外汇",
        "美联储", "央行", "利率", "通胀", "GDP", "CPI", 
        "科技股", "金融股", "消费股", "医药股", "新能源",
        "比特币", "加密货币", "区块链", "NFT",
        "A股", "港股", "美股", "纳斯达克", "道琼斯"
    ]
}

# 日志配置
LOGGING_CONFIG = {
    "level": "INFO",
    "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    "handlers": ["console", "file"]
}