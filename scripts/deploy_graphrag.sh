#!/bin/bash
# GraphRAG部署脚本

set -e

echo "🚀 开始部署稷下学宫 GraphRAG 系统..."
echo "=" * 60

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查依赖
check_dependencies() {
    log_info "检查系统依赖..."
    
    if ! command -v docker &> /dev/null; then
        log_error "Docker未安装，请先安装Docker"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose未安装，请先安装Docker Compose"
        exit 1
    fi
    
    log_success "系统依赖检查通过"
}

# 检查环境变量
check_environment() {
    log_info "检查环境变量..."
    
    if [ ! -f .env ]; then
        log_warning ".env文件不存在，创建示例文件..."
        cat > .env << EOF
# OpenAI API配置
OPENAI_API_KEY=your_openai_api_key_here

# OpenRouter API配置 (备用)
OPENROUTER_API_KEY_1=your_openrouter_api_key_here

# MongoDB配置
MONGODB_URI=*********************************************************************

# N8N配置
N8N_USER=admin
N8N_PASSWORD=jixia123

# Webhook配置
WEBHOOK_URL=http://localhost:5678

# GraphRAG配置
GRAPHRAG_API_URL=http://graphrag-server:8080
PROCESSING_INTERVAL=300
EOF
        log_warning "请编辑.env文件，填入正确的API密钥"
        read -p "按Enter继续..."
    fi
    
    source .env
    
    if [ -z "$OPENAI_API_KEY" ] || [ "$OPENAI_API_KEY" = "your_openai_api_key_here" ]; then
        log_warning "请在.env文件中设置正确的OPENAI_API_KEY"
    fi
    
    log_success "环境变量检查完成"
}

# 创建目录结构
create_directories() {
    log_info "创建目录结构..."
    
    directories=(
        "graphrag_data"
        "graphrag_config" 
        "graphrag_logs"
        "neo4j_data"
        "neo4j_logs"
        "neo4j_import"
        "redis_data"
        "etl_logs"
    )
    
    for dir in "${directories[@]}"; do
        mkdir -p "$dir"
        log_info "创建目录: $dir"
    done
    
    log_success "目录结构创建完成"
}

# 构建自定义镜像
build_images() {
    log_info "构建自定义Docker镜像..."
    
    if [ -f "Dockerfile.graphrag-etl" ]; then
        log_info "构建GraphRAG ETL镜像..."
        docker build -f Dockerfile.graphrag-etl -t jixia-graphrag-etl:latest .
        log_success "GraphRAG ETL镜像构建完成"
    fi
    
    log_success "镜像构建完成"
}

# 启动服务
start_services() {
    log_info "启动OPEA GraphRAG服务..."

    # 停止现有服务
    docker-compose -f docker-compose.graphrag.yml down

    # 拉取最新镜像
    log_info "拉取OPEA GraphRAG镜像..."
    docker-compose -f docker-compose.graphrag.yml pull

    # 启动服务
    docker-compose -f docker-compose.graphrag.yml up -d

    log_success "OPEA GraphRAG服务启动完成"
}

# 等待服务就绪
wait_for_services() {
    log_info "等待服务启动..."
    
    services=(
        "http://localhost:7474:Neo4j Web UI"
        "http://localhost:6379:Redis"
        "http://localhost:8080:OPEA GraphRAG API"
        "http://localhost:8888:OPEA GraphRAG UI"
        "http://localhost:8889:GraphRAG Gateway"
    )
    
    for service_info in "${services[@]}"; do
        IFS=':' read -r url name <<< "$service_info"
        
        log_info "等待 $name 启动..."
        
        max_attempts=30
        attempt=1
        
        while [ $attempt -le $max_attempts ]; do
            if curl -f -s "$url" > /dev/null 2>&1; then
                log_success "$name 已启动"
                break
            fi
            
            if [ $attempt -eq $max_attempts ]; then
                log_warning "$name 启动超时，请检查日志"
                break
            fi
            
            sleep 2
            ((attempt++))
        done
    done
}

# 健康检查
health_check() {
    log_info "执行健康检查..."
    
    # 检查GraphRAG服务
    if curl -f -s http://localhost:8080/health > /dev/null; then
        log_success "GraphRAG服务健康检查通过"
    else
        log_warning "GraphRAG服务健康检查失败"
    fi
    
    # 检查Neo4j
    if curl -f -s http://localhost:7474 > /dev/null; then
        log_success "Neo4j服务健康检查通过"
    else
        log_warning "Neo4j服务健康检查失败"
    fi
    
    # 检查Redis
    if redis-cli -h localhost -p 6379 ping > /dev/null 2>&1; then
        log_success "Redis服务健康检查通过"
    else
        log_warning "Redis服务健康检查失败"
    fi
}

# 显示访问信息
show_access_info() {
    log_success "🎉 OPEA GraphRAG系统部署完成！"
    echo ""
    echo "📊 服务访问地址："
    echo "  🔗 OPEA GraphRAG API:  http://localhost:8080"
    echo "  🔗 OPEA GraphRAG UI:   http://localhost:8888"
    echo "  🔗 GraphRAG网关:       http://localhost:8889"
    echo "  🔗 Neo4j Web UI:       http://localhost:7474"
    echo "  🔗 健康检查:           http://localhost:8080/health"
    echo ""
    echo "🔑 默认登录信息："
    echo "  Neo4j: neo4j / jixia123"
    echo ""
    echo "📚 API使用示例："
    echo "  # 查询GraphRAG"
    echo "  curl -X POST http://localhost:8080/query \\"
    echo "    -H 'Content-Type: application/json' \\"
    echo "    -d '{\"question\": \"当前市场趋势如何？\", \"method\": \"global\"}'"
    echo ""
    echo "  # 构建索引"
    echo "  curl -X POST http://localhost:8080/index \\"
    echo "    -H 'Content-Type: application/json' \\"
    echo "    -d '{\"documents\": [\"测试文档内容\"], \"source\": \"test\"}'"
    echo ""
    echo "📋 下一步："
    echo "  1. 在N8N中配置GraphRAG节点"
    echo "  2. 测试RSS到GraphRAG的数据流"
    echo "  3. 集成到稷下学宫辩论系统"
    echo ""
}

# 主函数
main() {
    check_dependencies
    check_environment
    create_directories
    build_images
    start_services
    wait_for_services
    health_check
    show_access_info
}

# 错误处理
trap 'log_error "部署过程中发生错误，请检查日志"; exit 1' ERR

# 执行主函数
main

log_success "GraphRAG部署脚本执行完成！"
