#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Heroku MCP工具注册脚本
将炼妖壶的API端点注册为Heroku MCP Agent可调用的工具
"""

import json
import requests
import os
import sys
from datetime import datetime
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("MCPRegistration")

# 配置
HEROKU_APP_NAME = os.getenv("HEROKU_APP_NAME", "cauldron")
HEROKU_API_TOKEN = os.getenv("HEROKU_API_TOKEN", "")
CAULDRON_API_URL = os.getenv("CAULDRON_API_URL", "http://localhost:8000")

def create_mcp_tool_config():
    """创建MCP工具配置"""
    return {
        "name": "cauldron-financial-tools",
        "version": "1.0.0",
        "description": "炼妖壶金融分析工具集",
        "server": {
            "command": "python",
            "args": ["-m", "src.mcp_server"],
            "env": {
                "CAULDRON_API_URL": CAULDRON_API_URL,
                "CAULDRON_API_KEY": "${CAULDRON_API_KEY}"
            }
        },
        "tools": [
            {
                "name": "market_sentiment",
                "description": "分析当前市场情绪和趋势",
                "endpoint": f"{CAULDRON_API_URL}/api/mcp/tools/market_sentiment/call",
                "method": "POST",
                "schema": {
                    "type": "object",
                    "properties": {
                        "source": {
                            "type": "string",
                            "enum": ["all", "rss", "social", "news"],
                            "default": "all"
                        },
                        "timeframe": {
                            "type": "string",
                            "enum": ["1h", "4h", "1d", "1w"],
                            "default": "1d"
                        },
                        "symbols": {
                            "type": "array",
                            "items": {"type": "string"}
                        }
                    }
                }
            },
            {
                "name": "trading_signal",
                "description": "生成交易信号和建议",
                "endpoint": f"{CAULDRON_API_URL}/api/mcp/tools/trading_signal/call",
                "method": "POST",
                "schema": {
                    "type": "object",
                    "properties": {
                        "symbol": {"type": "string"},
                        "strategy": {
                            "type": "string",
                            "enum": ["momentum", "mean_reversion", "breakout", "default"],
                            "default": "default"
                        },
                        "timeframe": {
                            "type": "string",
                            "enum": ["5m", "15m", "1h", "4h", "1d"],
                            "default": "1d"
                        },
                        "risk_level": {
                            "type": "integer",
                            "minimum": 1,
                            "maximum": 5,
                            "default": 3
                        }
                    },
                    "required": ["symbol"]
                }
            },
            {
                "name": "portfolio_analysis",
                "description": "分析投资组合风险和收益",
                "endpoint": f"{CAULDRON_API_URL}/api/mcp/tools/portfolio_analysis/call",
                "method": "POST",
                "schema": {
                    "type": "object",
                    "properties": {
                        "holdings": {
                            "type": "array",
                            "items": {
                                "type": "object",
                                "properties": {
                                    "symbol": {"type": "string"},
                                    "weight": {"type": "number"}
                                }
                            }
                        },
                        "benchmark": {
                            "type": "string",
                            "default": "000300.SH"
                        }
                    },
                    "required": ["holdings"]
                }
            },
            {
                "name": "risk_assessment",
                "description": "评估投资风险",
                "endpoint": f"{CAULDRON_API_URL}/api/mcp/tools/risk_assessment/call",
                "method": "POST",
                "schema": {
                    "type": "object",
                    "properties": {
                        "symbol": {"type": "string"},
                        "position_size": {
                            "type": "number",
                            "default": 0.1
                        },
                        "time_horizon": {
                            "type": "string",
                            "enum": ["short", "medium", "long"],
                            "default": "medium"
                        }
                    },
                    "required": ["symbol"]
                }
            },
            {
                "name": "news_impact",
                "description": "分析新闻对市场的影响",
                "endpoint": f"{CAULDRON_API_URL}/api/mcp/tools/news_impact/call",
                "method": "POST",
                "schema": {
                    "type": "object",
                    "properties": {
                        "news_text": {"type": "string"},
                        "symbols": {
                            "type": "array",
                            "items": {"type": "string"}
                        }
                    },
                    "required": ["news_text"]
                }
            }
        ]
    }

def register_with_heroku_mcp():
    """向Heroku MCP注册工具"""
    try:
        config = create_mcp_tool_config()

        # 这里应该是Heroku MCP的注册API端点
        # 目前Heroku MCP可能还没有公开的注册API
        # 所以我们先保存配置文件

        with open("heroku_mcp_config.json", "w", encoding="utf-8") as f:
            json.dump(config, f, ensure_ascii=False, indent=2)

        logger.info("✅ MCP工具配置已生成: heroku_mcp_config.json")

        if not HEROKU_API_TOKEN:
            logger.warning("⚠️ 缺少HEROKU_API_TOKEN环境变量，跳过自动注册")
            logger.info("📝 请手动将此配置添加到Heroku MCP设置中")
        else:
            logger.info("📝 请手动将此配置添加到Heroku MCP设置中")

        return True

    except Exception as e:
        logger.error(f"注册MCP工具失败: {e}")
        return False

def test_api_endpoints():
    """测试API端点是否可用"""
    try:
        # 测试主API
        response = requests.get(f"{CAULDRON_API_URL}/api/mcp/tools", timeout=10)
        if response.status_code == 200:
            logger.info("✅ MCP工具API可用")
            tools = response.json().get("tools", [])
            logger.info(f"📊 发现 {len(tools)} 个可用工具")
        else:
            logger.error(f"❌ MCP工具API不可用: {response.status_code}")
            return False
        
        # 测试健康检查
        response = requests.get(f"{CAULDRON_API_URL}/health", timeout=10)
        if response.status_code == 200:
            logger.info("✅ 健康检查API可用")
        else:
            logger.warning(f"⚠️ 健康检查API异常: {response.status_code}")
        
        return True
        
    except Exception as e:
        logger.error(f"API端点测试失败: {e}")
        return False

def generate_mcp_client_config():
    """生成MCP客户端配置"""
    config = {
        "mcpServers": {
            "cauldron": {
                "command": "python",
                "args": ["-m", "src.mcp_server"],
                "env": {
                    "CAULDRON_API_URL": CAULDRON_API_URL,
                    "CAULDRON_API_KEY": "${CAULDRON_API_KEY}"
                }
            }
        }
    }
    
    with open("mcp_client_config.json", "w", encoding="utf-8") as f:
        json.dump(config, f, ensure_ascii=False, indent=2)
    
    logger.info("✅ MCP客户端配置已生成: mcp_client_config.json")
    logger.info("📝 可用于Claude Desktop、Cursor等MCP客户端")

def main():
    """主函数"""
    logger.info("🚀 开始注册炼妖壶MCP工具...")
    
    # 测试API端点
    if not test_api_endpoints():
        logger.error("❌ API端点测试失败，请检查服务状态")
        sys.exit(1)
    
    # 注册MCP工具
    if register_with_heroku_mcp():
        logger.info("✅ MCP工具注册完成")
    else:
        logger.error("❌ MCP工具注册失败")
        sys.exit(1)
    
    # 生成客户端配置
    generate_mcp_client_config()
    
    logger.info("🎉 所有配置已完成！")
    logger.info("📋 下一步:")
    logger.info("1. 将 heroku_mcp_config.json 配置添加到Heroku MCP")
    logger.info("2. 将 mcp_client_config.json 配置添加到MCP客户端")
    logger.info("3. 设置 CAULDRON_API_KEY 环境变量")

if __name__ == "__main__":
    main()
