#!/usr/bin/env python3
"""
统一启动脚本 - 替代Django的manage.py
提供项目的各种启动选项
"""

import sys
import os
import argparse
import subprocess
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.append(str(project_root))
sys.path.append(str(project_root / "src"))

def start_api_server(host="0.0.0.0", port=8001, reload=True):
    """启动FastAPI服务器"""
    print(f"🚀 启动API服务器 http://{host}:{port}")
    import uvicorn
    uvicorn.run(
        "api_server:app",
        host=host,
        port=port,
        reload=reload,
        log_level="info"
    )

def start_streamlit_app(port=8501):
    """启动Streamlit应用"""
    print(f"🎨 启动Streamlit应用 http://localhost:{port}")
    subprocess.run([
        sys.executable, "-m", "streamlit", "run", 
        "streamlit_app.py", 
        "--server.port", str(port),
        "--server.address", "0.0.0.0"
    ])

def start_jixia_academy(port=8000):
    """启动稷下学宫AI辩论系统（已整合到Streamlit主应用）"""
    print("🏛️ 稷下学宫已整合到主Streamlit应用中")
    print("请使用 'python start.py streamlit' 启动主应用，然后访问稷下学宫页面")
    print("或者使用 'python start.py all' 启动所有服务")
    return

def start_all_services():
    """启动所有服务"""
    print("🌟 启动所有服务...")
    import threading
    import time
    
    # 启动API服务器
    api_thread = threading.Thread(
        target=start_api_server, 
        kwargs={"reload": False}
    )
    api_thread.daemon = True
    api_thread.start()
    
    time.sleep(2)  # 等待API服务器启动
    
    print("✅ API服务器已启动 (http://localhost:8001)")
    print("🎨 启动Streamlit主应用 (包含稷下学宫功能)...")
    
    # 启动Streamlit（主线程）
    start_streamlit_app()

def run_scanner():
    """运行股票扫描器"""
    print("📊 启动股票扫描器...")
    try:
        from src.strategy.scanning.MainRunner import MainRunner
        runner = MainRunner()
        runner.start_process()
    except ImportError as e:
        print(f"❌ 无法导入MainRunner: {e}")
        print("请确保所有依赖已正确安装")

def install_dependencies():
    """安装项目依赖"""
    print("📦 安装项目依赖...")
    subprocess.run([sys.executable, "-m", "pip", "install", "-e", "."])

def run_tests():
    """运行测试"""
    print("🧪 运行测试...")
    subprocess.run([sys.executable, "-m", "pytest"])

def show_status():
    """显示项目状态"""
    print("📋 项目状态:")
    print(f"  项目根目录: {project_root}")
    print(f"  Python版本: {sys.version}")
    
    # 检查关键文件
    key_files = [
        "api_server.py",
        "streamlit_app.py", 
        "jixia_academy/chainlit_debate_app.py",
        "pyproject.toml"
    ]
    
    print("  关键文件:")
    for file in key_files:
        file_path = project_root / file
        status = "✅" if file_path.exists() else "❌"
        print(f"    {status} {file}")

def main():
    parser = argparse.ArgumentParser(
        description="Penny Scanner 项目管理工具",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例:
  python start.py api          # 启动API服务器
  python start.py streamlit    # 启动Streamlit应用
  python start.py jixia        # 启动稷下学宫
  python start.py all          # 启动所有服务
  python start.py scanner      # 运行股票扫描器
  python start.py install      # 安装依赖
  python start.py test         # 运行测试
  python start.py status       # 显示状态
        """
    )
    
    parser.add_argument(
        "command",
        choices=[
            "api", "streamlit", "jixia", "all", 
            "scanner", "install", "test", "status"
        ],
        help="要执行的命令"
    )
    
    parser.add_argument(
        "--port", 
        type=int, 
        help="指定端口号"
    )
    
    parser.add_argument(
        "--host", 
        default="0.0.0.0", 
        help="指定主机地址 (默认: 0.0.0.0)"
    )
    
    parser.add_argument(
        "--no-reload", 
        action="store_true", 
        help="禁用自动重载 (仅API服务器)"
    )
    
    args = parser.parse_args()
    
    try:
        if args.command == "api":
            port = args.port or 8001
            start_api_server(
                host=args.host, 
                port=port, 
                reload=not args.no_reload
            )
        elif args.command == "streamlit":
            port = args.port or 8501
            start_streamlit_app(port)
        elif args.command == "jixia":
            port = args.port or 8000
            start_jixia_academy(port)
        elif args.command == "all":
            start_all_services()
        elif args.command == "scanner":
            run_scanner()
        elif args.command == "install":
            install_dependencies()
        elif args.command == "test":
            run_tests()
        elif args.command == "status":
            show_status()
            
    except KeyboardInterrupt:
        print("\n👋 服务已停止")
    except Exception as e:
        print(f"❌ 错误: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()