#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
太公心易BI系统 - Heroku部署主程序
Streamlit Web应用入口文件
"""

import os
import sys
from pathlib import Path
import logging

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 设置环境变量，避免Streamlit警告
os.environ['STREAMLIT_SERVER_HEADLESS'] = 'true'
os.environ['STREAMLIT_SERVER_ENABLE_CORS'] = 'false'
os.environ['STREAMLIT_SERVER_ENABLE_XSRF_PROTECTION'] = 'false'

# Heroku PostgreSQL数据库配置
if 'DATABASE_URL' in os.environ:
    # Heroku提供的PostgreSQL连接字符串
    database_url = os.environ['DATABASE_URL']
    # 如果是postgres://开头，需要改为postgresql://
    if database_url.startswith('postgres://'):
        database_url = database_url.replace('postgres://', 'postgresql://', 1)
    os.environ['DATABASE_URL'] = database_url
    logger.info(f"✅ 检测到Heroku PostgreSQL数据库")
else:
    # 本地开发环境
    logger.info("🔧 使用本地开发环境配置")

# 导入Streamlit和UI模块
import streamlit as st
from src.ui.morning_briefing_ui import MorningBriefingUI
from src.utils.monitoring import monitor_performance, HealthChecker, performance_monitor

@monitor_performance
def main():
    """
    Streamlit应用主函数
    """
    # 设置页面配置（必须在其他Streamlit命令之前）
    st.set_page_config(
        page_title="太公心易BI系统 - 每日晨会",
        page_icon="📊",
        layout="wide",
        initial_sidebar_state="expanded"
    )
    
    # 添加健康检查和监控信息到侧边栏
    with st.sidebar:
        if st.button("🔍 系统健康检查"):
            health_status = HealthChecker.check_system_health()
            
            if health_status['status'] == 'healthy':
                st.success("✅ 系统运行正常")
            elif health_status['status'] == 'warning':
                st.warning("⚠️ 系统有警告")
                for warning in health_status.get('warnings', []):
                    st.warning(warning)
            else:
                st.error("❌ 系统异常")
                st.error(health_status.get('error', '未知错误'))
            
            # 显示系统指标
            if 'cpu_percent' in health_status:
                st.metric("CPU使用率", f"{health_status['cpu_percent']:.1f}%")
            if 'memory' in health_status:
                st.metric("内存使用率", f"{health_status['memory']['percent']:.1f}%")
        
        # 显示性能指标
        if st.button("📊 性能指标"):
            metrics = performance_monitor.get_metrics()
            st.metric("总请求数", metrics['total_requests'])
            st.metric("平均响应时间", f"{metrics['avg_response_time']:.2f}s")
            st.metric("错误数量", metrics['error_count'])
    
    try:
        # 创建UI实例（不在__init__中设置页面配置）
        ui = MorningBriefingUI()
        
        # 运行界面
        ui.run()
        
    except Exception as e:
        st.error(f"系统启动失败: {e}")
        st.exception(e)
        logger.error(f"应用启动失败: {e}", exc_info=True)

if __name__ == "__main__":
    main()