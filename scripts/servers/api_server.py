#!/usr/bin/env python3
"""
FastAPI服务器 - 替代Django的webhook功能
提供n8n webhook接收端点
"""

import json
import sys
import os
from pathlib import Path
from fastapi import FastAPI, Request, HTTPException
from fastapi.responses import JSONResponse
import uvicorn

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.append(str(project_root))
sys.path.append(str(project_root / "src"))

app = FastAPI(
    title="Cauldron API",
    description="炼妖壶 API 服务器，用于接收 n8n webhook 和其他 API 调用",
    version="1.0.0"
)

@app.get("/")
async def root():
    """健康检查端点"""
    return {"message": "Cauldron API Server is running"}

@app.get("/health")
async def health_check():
    """健康检查端点"""
    return {"status": "healthy", "service": "cauldron_api"}

@app.post("/api/webhook/n8n/")
async def n8n_webhook(request: Request):
    """接收来自n8n的webhook推送"""
    try:
        # 获取请求体数据
        data = await request.json()
        
        # 导入MainRunner并处理webhook
        try:
            from src.strategy.scanning.MainRunner import MainRunner
            runner = MainRunner()
            runner.receive_n8n_webhook(data)
            
            return JSONResponse(
                content={"status": "success", "message": "Webhook processed successfully"},
                status_code=200
            )
        except ImportError as e:
            # 如果MainRunner导入失败，记录错误但不中断服务
            print(f"Warning: Could not import MainRunner: {e}")
            return JSONResponse(
                content={"status": "warning", "message": "Webhook received but MainRunner unavailable"},
                status_code=200
            )
            
    except json.JSONDecodeError:
        raise HTTPException(status_code=400, detail="Invalid JSON in request body")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

@app.get("/api/status")
async def get_status():
    """获取系统状态"""
    return {
        "status": "running",
        "components": {
            "api_server": "active",
            "streamlit_app": "check /app.py",
            "jixia_academy": "check /jixia_academy"
        }
    }

if __name__ == "__main__":
    # 开发环境运行
    uvicorn.run(
        "api_server:app",
        host="0.0.0.0",
        port=8001,
        reload=True,
        log_level="info"
    )