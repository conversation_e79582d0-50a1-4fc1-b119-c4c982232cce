#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
太公心易BI系统 - 主启动文件
晨会简报Web界面启动器
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 导入UI模块
from src.ui.morning_briefing_ui import MorningBriefingUI

def main():
    """
    主函数 - 启动晨会简报Web界面
    """
    print("🏛️  太公心易BI系统启动中...")
    print("📊 炼股葫芦 × 🎭 八仙论道")
    print("=" * 50)
    
    try:
        # 创建并运行UI
        ui = MorningBriefingUI()
        ui.run()
    except KeyboardInterrupt:
        print("\n👋 系统已停止")
    except Exception as e:
        print(f"❌ 系统启动失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()