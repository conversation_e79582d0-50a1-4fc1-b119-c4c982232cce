#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
八仙论道系统启动脚本
整合到主项目中的辩论系统启动器
"""

import os
import sys
import subprocess
from pathlib import Path
# 稷下学宫已迁移到jixia_academy_clean，使用新的配置系统
# from config_manager import ConfigManager

def main():
    """主启动函数"""
    print("🎭 启动八仙论道系统...")
    
    # 获取项目根目录
    project_root = Path(__file__).parent.parent
    jixia_dir = project_root / "jixia_academy_clean"

    # 检查jixia_academy_clean目录是否存在
    if not jixia_dir.exists():
        print("❌ 错误: jixia_academy_clean目录不存在")
        print("ℹ️ 稷下学宫已迁移到jixia_academy_clean目录")
        sys.exit(1)
    
    # 使用默认配置
    print("ℹ️ 稷下学宫已迁移到新架构，使用默认配置")

    # 设置默认环境变量
    host = os.getenv("JIXIA_HOST", "0.0.0.0")
    port = int(os.getenv("JIXIA_PORT", "8000"))

    print(f"🏛️ 稷下学宫配置: {host}:{port}")

    # 检查是否启用
    if os.getenv("JIXIA_ENABLED", "true").lower() != "true":
        print("⚠️ 稷下学宫系统已禁用")
        sys.exit(0)
    
    # 切换到jixia_academy目录
    os.chdir(jixia_dir)
    
    # 启动新架构的稷下学宫
    try:
        print(f"🚀 启动稷下学宫服务器: {host}:{port}")
        print("ℹ️ 稷下学宫已迁移到新架构，请使用以下方式启动：")
        print("   1. 直接运行: python -m jixia_academy_clean.core.debate_system")
        print("   2. 或使用Streamlit: streamlit run app/streamlit_app.py")
        print("   3. 或使用MCP服务: python -m jixia_academy_clean.core.autogen_mcp_server")

        # 尝试启动新的辩论系统
        debate_app = jixia_dir / "core" / "debate_system.py"
        if debate_app.exists():
            print("🎭 启动稷下学宫辩论系统...")
            subprocess.run([
                sys.executable, str(debate_app)
            ], cwd=jixia_dir, check=True)
        else:
            print("⚠️ 新架构辩论系统文件不存在，请检查jixia_academy_clean目录")

    except subprocess.CalledProcessError as e:
        print(f"❌ 启动失败: {e}")
        sys.exit(1)
    except KeyboardInterrupt:
        print("\n👋 稷下学宫系统已停止")

if __name__ == "__main__":
    main()