#!/bin/bash
# 部署对韭当割代码到实际项目目录
# 使用方法: bash deploy_to_cauldron.sh

echo "🚀 开始部署对韭当割代码到 /home/<USER>/gitlab/cauldron/"

# 设置源目录和目标目录
SOURCE_DIR="/mnt/persist/workspace"
TARGET_DIR="/home/<USER>/gitlab/cauldron"

# 检查目标目录是否存在
if [ ! -d "$TARGET_DIR" ]; then
    echo "❌ 错误: 目标目录 $TARGET_DIR 不存在"
    echo "请确认项目路径是否正确"
    exit 1
fi

# 创建必要的目录结构
echo "📁 创建目录结构..."
mkdir -p "$TARGET_DIR/src/core"
mkdir -p "$TARGET_DIR/src/ui"

# 复制核心系统文件
echo "📦 复制核心系统文件..."
if [ -f "$SOURCE_DIR/src/core/rss_trigger_system.py" ]; then
    cp "$SOURCE_DIR/src/core/rss_trigger_system.py" "$TARGET_DIR/src/core/"
    echo "✅ 已复制: src/core/rss_trigger_system.py"
else
    echo "❌ 未找到: src/core/rss_trigger_system.py"
fi

if [ -f "$SOURCE_DIR/src/ui/rss_theater_ui.py" ]; then
    cp "$SOURCE_DIR/src/ui/rss_theater_ui.py" "$TARGET_DIR/src/ui/"
    echo "✅ 已复制: src/ui/rss_theater_ui.py"
else
    echo "❌ 未找到: src/ui/rss_theater_ui.py"
fi

# 复制测试和演示文件
echo "🧪 复制测试和演示文件..."
test_files=(
    "standalone_rss_test.py"
    "auto_demo.py"
    "terminal_demo.py"
    "test_rss_system.py"
    "test_theater.py"
    "simple_demo.py"
    "start_theater.py"
    "install_theater.py"
)

for file in "${test_files[@]}"; do
    if [ -f "$SOURCE_DIR/$file" ]; then
        cp "$SOURCE_DIR/$file" "$TARGET_DIR/"
        echo "✅ 已复制: $file"
    else
        echo "⚠️ 未找到: $file"
    fi
done

# 复制文档文件
echo "📚 复制文档文件..."
doc_files=(
    "PROJECT_STRUCTURE.md"
    "API_INTEGRATION.md"
    "CODE_INVENTORY.md"
    "FINAL_CODE_PACKAGE.md"
    "README_THEATER.md"
)

for file in "${doc_files[@]}"; do
    if [ -f "$SOURCE_DIR/$file" ]; then
        cp "$SOURCE_DIR/$file" "$TARGET_DIR/"
        echo "✅ 已复制: $file"
    else
        echo "⚠️ 未找到: $file"
    fi
done

# 检查streamlit_app.py是否需要更新
echo "🔧 检查主应用文件..."
if [ -f "$TARGET_DIR/streamlit_app.py" ]; then
    echo "ℹ️ streamlit_app.py 已存在，请手动检查是否需要集成RSS韭菜小剧场功能"
    echo "   需要添加的代码片段在 API_INTEGRATION.md 文档中"
else
    echo "⚠️ streamlit_app.py 不存在"
fi

# 创建依赖安装脚本
echo "📦 创建依赖安装脚本..."
cat > "$TARGET_DIR/install_dependencies.sh" << 'EOF'
#!/bin/bash
echo "📦 安装对韭当割依赖包..."
pip install streamlit aiohttp feedparser plotly pandas
echo "✅ 依赖安装完成"
EOF

chmod +x "$TARGET_DIR/install_dependencies.sh"
echo "✅ 已创建: install_dependencies.sh"

# 创建快速启动脚本
echo "🚀 创建快速启动脚本..."
cat > "$TARGET_DIR/start_rss_theater.sh" << 'EOF'
#!/bin/bash
echo "🔪 启动对韭当割 - RSS触发韭菜小剧场"
echo "访问地址: http://localhost:8505"
echo "选择 '🔪 对韭当割' 标签页"
echo ""
streamlit run streamlit_app.py --server.port 8505
EOF

chmod +x "$TARGET_DIR/start_rss_theater.sh"
echo "✅ 已创建: start_rss_theater.sh"

# 显示部署总结
echo ""
echo "🎉 部署完成！"
echo "=" * 50
echo "📁 已部署到: $TARGET_DIR"
echo ""
echo "📦 核心文件:"
echo "  ✅ src/core/rss_trigger_system.py - RSS触发系统核心"
echo "  ✅ src/ui/rss_theater_ui.py - Streamlit UI界面"
echo ""
echo "🧪 测试文件:"
echo "  ✅ standalone_rss_test.py - 独立测试"
echo "  ✅ auto_demo.py - 自动演示"
echo ""
echo "📚 文档文件:"
echo "  ✅ PROJECT_STRUCTURE.md - 项目结构"
echo "  ✅ API_INTEGRATION.md - API集成文档"
echo ""
echo "🚀 快速开始:"
echo "  1. 安装依赖: bash install_dependencies.sh"
echo "  2. 启动应用: bash start_rss_theater.sh"
echo "  3. 访问: http://localhost:8505"
echo "  4. 选择 '🔪 对韭当割' 标签页"
echo ""
echo "⚙️ 配置OpenRouter API Key:"
echo "  export OPENROUTER_API_KEY='your_api_key_here'"
echo ""
echo "📖 详细说明请查看 PROJECT_STRUCTURE.md"
echo "=" * 50
