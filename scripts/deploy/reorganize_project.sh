#!/bin/bash

# 🚀 炼妖壶项目重新整理脚本
# 将散落在根目录的文件整理到合适的目录中

echo "🎯 开始重新整理炼妖壶项目结构..."

# 创建必要的目录
mkdir -p examples/alibaba
mkdir -p examples/strategies
mkdir -p examples/research
mkdir -p tools/scripts
mkdir -p tools/analysis
mkdir -p archive/legacy
mkdir -p archive/logs

# 移动阿里巴巴相关文件到examples/alibaba
echo "📁 整理阿里巴巴示例文件..."
if [ -f "baba_fundamental_analysis.py" ]; then
    mv baba_fundamental_analysis.py examples/alibaba/
fi
if [ -f "baba_fundamentals_20250629_120800.xml" ]; then
    mv baba_fundamentals_20250629_120800.xml examples/alibaba/
fi
if [ -f "get_baba_fundamentals.py" ]; then
    mv get_baba_fundamentals.py examples/alibaba/
fi

# 移动策略相关文件到examples/strategies
echo "📈 整理策略文件..."
if [ -f "non_gaussian_strategy_framework.py" ]; then
    mv non_gaussian_strategy_framework.py examples/strategies/
fi
if [ -f "rock_mechanics_analyzer.py" ]; then
    mv rock_mechanics_analyzer.py examples/strategies/
fi
if [ -f "rock_mechanics_financial_framework.py" ]; then
    mv rock_mechanics_financial_framework.py examples/strategies/
fi

# 移动研究文档到examples/research
echo "📚 整理研究文档..."
if [ -f "rock_mechanics_financial_theory.md" ]; then
    mv rock_mechanics_financial_theory.md examples/research/
fi
if [ -f "supreme_member_non_gaussian_research.md" ]; then
    mv supreme_member_non_gaussian_research.md examples/research/
fi

# 移动工具脚本到tools/scripts
echo "🔧 整理工具脚本..."
if [ -f "batch_apply_changes.sh" ]; then
    mv batch_apply_changes.sh tools/scripts/
fi
if [ -f "install_theater.py" ]; then
    mv install_theater.py tools/scripts/
fi

# 移动测试文件到tests目录
echo "🧪 整理测试文件..."
if [ -f "test_streamlit.py" ]; then
    mv test_streamlit.py tests/
fi

# 移动日志文件到archive/logs
echo "📋 整理日志文件..."
if [ -f "final_output.log" ]; then
    mv final_output.log archive/logs/
fi

# 移动旧的README到archive
echo "📄 整理文档文件..."
if [ -f "README_THEATER.md" ]; then
    mv README_THEATER.md archive/legacy/
fi

# 创建examples目录的README
cat > examples/README.md << 'EOF'
# 📚 炼妖壶示例集合

本目录包含炼妖壶项目的各种示例和演示代码。

## 📁 目录结构

- **alibaba/** - 阿里巴巴基本面分析示例
- **strategies/** - 量化策略示例
- **research/** - 研究文档和理论

## 🚀 快速开始

每个子目录都包含独立的示例，可以单独运行和学习。
EOF

# 创建tools目录的README
cat > tools/README.md << 'EOF'
# 🔧 炼妖壶工具集

本目录包含项目开发和维护所需的各种工具。

## 📁 目录结构

- **scripts/** - 自动化脚本
- **analysis/** - 分析工具

## 使用说明

所有脚本都应该从项目根目录运行。
EOF

# 创建archive目录的README
cat > archive/README.md << 'EOF'
# 📦 炼妖壶归档

本目录包含项目的历史文件和日志。

## 📁 目录结构

- **legacy/** - 旧版本文件
- **logs/** - 历史日志

## 说明

这些文件保留用于参考，但不再活跃维护。
EOF

echo "✅ 项目重新整理完成！"
echo "📊 新的项目结构更加清晰和专业"
echo "🎯 根目录现在只保留核心配置和主要模块"