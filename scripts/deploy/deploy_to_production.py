#!/usr/bin/env python3
"""
生产部署脚本
"""
import subprocess
import sys
import os
from datetime import datetime

def run_command(command, description):
    """运行命令并处理结果"""
    print(f"🔄 {description}...")
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ {description}完成")
            return True
        else:
            print(f"❌ {description}失败: {result.stderr}")
            return False
    except Exception as e:
        print(f"❌ {description}异常: {e}")
        return False

def deploy_to_production():
    """执行生产部署"""
    
    print("🚀 开始生产部署...")
    print(f"⏰ 部署时间: {datetime.now().isoformat()}")
    
    deployment_steps = [
        ("python3 update_env_config.py", "更新环境配置"),
        ("python3 test_n8n_integration.py", "测试N8N集成"),
        ("git add .", "添加更改到Git"),
        ("git commit -m 'Deploy N8N production integration'", "提交更改"),
    ]
    
    success_count = 0
    
    for command, description in deployment_steps:
        if run_command(command, description):
            success_count += 1
        else:
            print(f"⚠️ 部署在'{description}'步骤失败")
            break
    
    if success_count == len(deployment_steps):
        print("\n🎉 生产部署成功完成！")
        print("\n📋 后续步骤:")
        print("1. 监控N8N数据推送")
        print("2. 检查日志输出")
        print("3. 验证数据流正常")
        return True
    else:
        print(f"\n⚠️ 部署未完全成功 ({success_count}/{len(deployment_steps)})")
        return False

if __name__ == "__main__":
    success = deploy_to_production()
    exit(0 if success else 1)
