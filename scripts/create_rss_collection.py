#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建RSS交易情报专用Collection
为N8N RSS工作流创建完美匹配的Zilliz Collection

🎯 目标：创建 rss_trading_intelligence Collection
📊 字段：完全匹配N8N数据结构
🔧 维度：BGE-M3 1024维向量
"""

import os
import sys
from typing import Dict, Any
from dotenv import load_dotenv
from pymilvus import (
    connections, 
    utility, 
    FieldSchema, 
    CollectionSchema, 
    DataType, 
    Collection,
    Index
)

# 加载环境变量
load_dotenv()

class RSSCollectionCreator:
    """RSS Collection创建器"""
    
    def __init__(self):
        self.zilliz_endpoint = os.getenv('ZILLIZ_ENDPOINT')
        self.zilliz_token = os.getenv('ZILLIZ_TOKEN')
        self.collection_name = "rss_trading_intelligence"
        self.vector_dimension = 1024  # BGE-M3模型维度
        
        print("🚀 RSS Collection创建器初始化")
        print(f"📊 Collection名称: {self.collection_name}")
        print(f"🔢 向量维度: {self.vector_dimension}")
    
    def connect_zilliz(self) -> bool:
        """连接Zilliz Cloud"""
        try:
            if not self.zilliz_endpoint or not self.zilliz_token:
                print("❌ Zilliz连接信息不完整，请检查.env文件")
                return False
            
            connections.connect(
                alias="default",
                uri=self.zilliz_endpoint,
                token=self.zilliz_token
            )
            
            print("✅ Zilliz Cloud连接成功")
            return True
            
        except Exception as e:
            print(f"❌ Zilliz连接失败: {e}")
            return False
    
    def check_existing_collection(self) -> bool:
        """检查Collection是否已存在，如果存在则自动删除"""
        try:
            exists = utility.has_collection(self.collection_name)
            if exists:
                print(f"⚠️  Collection '{self.collection_name}' 已存在")

                # 显示现有Collection信息
                collection = Collection(self.collection_name)
                print(f"📊 现有Collection统计:")
                print(f"   - 文档数量: {collection.num_entities}")
                print(f"   - 字段信息: {[field.name for field in collection.schema.fields]}")

                # 自动删除现有Collection
                print("🗑️  自动删除现有Collection并重新创建...")
                utility.drop_collection(self.collection_name)
                print("✅ Collection删除成功")

                return False  # 返回False表示需要创建新Collection
            else:
                print(f"🆕 Collection '{self.collection_name}' 不存在，将创建新Collection")
                return False

        except Exception as e:
            print(f"❌ 检查Collection失败: {e}")
            return False
    
    def create_collection_schema(self) -> CollectionSchema:
        """创建Collection Schema"""
        print("🏗️  设计Collection Schema...")
        
        # 定义字段 - 简化版本，只保留必要字段
        fields = [
            # 主键ID
            FieldSchema(
                name="id",
                dtype=DataType.VARCHAR,
                max_length=64,
                is_primary=True,
                description="文章唯一标识符"
            ),

            # 文章标题
            FieldSchema(
                name="title",
                dtype=DataType.VARCHAR,
                max_length=512,
                description="RSS文章标题"
            ),

            # 发布日期 (字符串格式，如 "2025-01-07")
            FieldSchema(
                name="published_date",
                dtype=DataType.VARCHAR,
                max_length=32,
                description="发布日期"
            ),

            # 模型等待标志
            FieldSchema(
                name="wait_for_model",
                dtype=DataType.BOOL,
                description="HuggingFace模型等待标志"
            ),

            # BGE-M3向量 (1024维)
            FieldSchema(
                name="vector",
                dtype=DataType.FLOAT_VECTOR,
                dim=self.vector_dimension,
                description="BGE-M3语义向量"
            )
        ]
        
        # 创建Schema
        schema = CollectionSchema(
            fields=fields,
            description="RSS交易情报向量存储 - 太公心易系统专用",
            enable_dynamic_field=True  # 允许动态字段
        )
        
        print("✅ Collection Schema创建完成")
        print("📋 字段列表:")
        for field in fields:
            print(f"   - {field.name}: {field.dtype} ({field.description})")
        
        return schema
    
    def create_collection(self) -> bool:
        """创建Collection"""
        try:
            # 创建Schema
            schema = self.create_collection_schema()
            
            # 创建Collection
            print(f"🔨 创建Collection: {self.collection_name}")
            collection = Collection(
                name=self.collection_name,
                schema=schema,
                using='default'
            )
            
            print("✅ Collection创建成功")
            return True
            
        except Exception as e:
            print(f"❌ Collection创建失败: {e}")
            return False
    
    def create_index(self) -> bool:
        """创建向量索引"""
        try:
            collection = Collection(self.collection_name)
            
            print("🔍 创建向量索引...")
            
            # 为向量字段创建IVF_FLAT索引
            index_params = {
                "metric_type": "COSINE",  # 余弦相似度
                "index_type": "IVF_FLAT",
                "params": {"nlist": 1024}
            }
            
            collection.create_index(
                field_name="vector",
                index_params=index_params
            )
            
            print("✅ 向量索引创建成功")
            
            # 加载Collection到内存
            collection.load()
            print("✅ Collection已加载到内存")
            
            return True
            
        except Exception as e:
            print(f"❌ 索引创建失败: {e}")
            return False
    
    def verify_collection(self) -> bool:
        """验证Collection创建结果"""
        try:
            collection = Collection(self.collection_name)
            
            print("🔍 验证Collection...")
            print(f"   - Collection名称: {collection.name}")
            print(f"   - 字段数量: {len(collection.schema.fields)}")
            print(f"   - 向量维度: {self.vector_dimension}")
            print(f"   - 文档数量: {collection.num_entities}")
            print(f"   - 索引状态: {collection.indexes}")
            
            print("✅ Collection验证通过")
            return True
            
        except Exception as e:
            print(f"❌ Collection验证失败: {e}")
            return False
    
    def run(self) -> bool:
        """执行完整的Collection创建流程"""
        print("🎯 开始创建RSS交易情报Collection")
        print("=" * 50)
        
        # 1. 连接Zilliz
        if not self.connect_zilliz():
            return False
        
        # 2. 检查现有Collection
        if self.check_existing_collection():
            user_input = input("是否删除现有Collection并重新创建? (y/N): ")
            if user_input.lower() == 'y':
                try:
                    utility.drop_collection(self.collection_name)
                    print(f"🗑️  已删除现有Collection: {self.collection_name}")
                except Exception as e:
                    print(f"❌ 删除Collection失败: {e}")
                    return False
            else:
                print("🚫 取消创建，使用现有Collection")
                return True
        
        # 3. 创建Collection
        if not self.create_collection():
            return False
        
        # 4. 创建索引
        if not self.create_index():
            return False
        
        # 5. 验证结果
        if not self.verify_collection():
            return False
        
        print("=" * 50)
        print("🎉 RSS交易情报Collection创建完成!")
        print(f"📊 Collection名称: {self.collection_name}")
        print("🔗 现在可以在N8N中使用此Collection了")
        
        return True

def main():
    """主函数"""
    creator = RSSCollectionCreator()
    success = creator.run()
    
    if success:
        print("\n🚀 下一步:")
        print("1. 在N8N中将Collection名称改为: rss_trading_intelligence")
        print("2. 确保字段映射正确")
        print("3. 测试RSS数据插入")
        sys.exit(0)
    else:
        print("\n❌ Collection创建失败，请检查错误信息")
        sys.exit(1)

if __name__ == "__main__":
    main()
