#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
通过N8N API在RSS工作流中插入时间过滤器
在RSS Feed节点和数据处理节点之间插入30分钟时间过滤器
"""

import requests
import json
import os
from typing import Dict, Any, List, Optional
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

class N8NTimeFilterInserter:
    """N8N时间过滤器插入器"""
    
    def __init__(self):
        # 从环境变量获取N8N配置
        self.workflow_id = "JDwsHwp7VM9lWic7"  # 你的RSS工作流ID
        self.base_url = "https://n8n.git4ta.fun"
        self.api_token = os.getenv('n8n_token', 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJhNzYwZjUxMy0zMWMzLTQwYzMtOTQ0Zi0xZDkyNGQ4ZjM3Y2QiLCJpc3MiOiJuOG4iLCJhdWQiOiJwdWJsaWMtYXBpIiwiaWF0IjoxNzUxNTU0NjY4fQ.YO11K1xJQQ9lmo-VYCdg8Vf0jyvQ5ufoLF-sWJLqE08')
        
        self.headers = {
            "X-N8N-API-KEY": self.api_token,
            "Content-Type": "application/json"
        }
        
        print("🔧 N8N时间过滤器插入器启动")
        print(f"🎯 目标工作流: {self.workflow_id}")
        print(f"🌐 N8N地址: {self.base_url}")
    
    def get_workflow(self) -> Optional[Dict[str, Any]]:
        """获取当前工作流"""
        try:
            url = f"{self.base_url}/api/v1/workflows/{self.workflow_id}"
            response = requests.get(url, headers=self.headers, timeout=15)
            
            if response.status_code == 200:
                workflow = response.json()
                print(f"✅ 获取工作流成功: {workflow.get('name', 'Unknown')}")
                return workflow
            else:
                print(f"❌ 获取工作流失败: {response.status_code}")
                print(f"响应: {response.text}")
                return None
                
        except Exception as e:
            print(f"❌ 获取工作流异常: {e}")
            return None
    
    def analyze_workflow_structure(self, workflow: Dict[str, Any]) -> Dict[str, Any]:
        """分析工作流结构，找到插入点"""
        nodes = workflow.get('nodes', [])
        connections = workflow.get('connections', {})
        
        print(f"\n🔍 分析工作流结构 ({len(nodes)} 个节点)")
        
        # 查找RSS相关节点和数据处理节点
        rss_nodes = []
        processing_nodes = []
        
        for node in nodes:
            node_type = node.get('type', '')
            node_name = node.get('name', '').lower()
            
            print(f"  📋 {node.get('name')}: {node_type}")
            
            # 识别RSS节点
            if ('rss' in node_type.lower() or 'rss' in node_name or 
                'feed' in node_name or 'http' in node_type.lower()):
                rss_nodes.append(node)
                print(f"    🎯 RSS节点: {node.get('name')}")
            
            # 识别数据处理节点
            if ('code' in node_type.lower() or 'function' in node_type.lower() or
                '处理' in node_name or 'process' in node_name or 'clean' in node_name):
                processing_nodes.append(node)
                print(f"    💻 处理节点: {node.get('name')}")
        
        return {
            'rss_nodes': rss_nodes,
            'processing_nodes': processing_nodes,
            'connections': connections,
            'total_nodes': len(nodes)
        }
    
    def create_time_filter_node(self) -> Dict[str, Any]:
        """创建时间过滤器节点"""
        filter_code = '''// ⏰ RSS时间过滤器 - 只处理过去30分钟的文章
const items = $input.all();
const filteredItems = [];

// 配置：只处理过去N分钟的文章
const FILTER_MINUTES = 30; // 可调整为15, 30, 60分钟
const cutoffTime = new Date(Date.now() - FILTER_MINUTES * 60 * 1000);

console.log(`🕐 RSS时间过滤器启动`);
console.log(`📅 截止时间: ${cutoffTime.toISOString()}`);
console.log(`📊 输入文章数: ${items.length}`);

// 时间解析函数
function parseRSSTime(timeStr) {
    if (!timeStr) return null;
    
    try {
        const formats = [
            timeStr,
            timeStr.replace(/\\s+\\+\\d{4}$/, ''),
            timeStr.replace(/\\s+[A-Z]{3}$/, ''),
        ];
        
        for (const format of formats) {
            const date = new Date(format);
            if (!isNaN(date.getTime())) {
                return date;
            }
        }
        return null;
    } catch (error) {
        console.log(`⚠️ 时间解析失败: ${timeStr}`);
        return null;
    }
}

// 统计变量
let newArticles = 0;
let oldArticles = 0;
let invalidTime = 0;

// 过滤每个RSS项目
for (const item of items) {
    try {
        if (!item || !item.json) continue;
        
        const data = item.json;
        
        // 提取发布时间
        const timeFields = ['pubDate', 'published', 'isoDate', 'date', 'published_time'];
        let publishedTime = null;
        let timeSource = null;
        
        for (const field of timeFields) {
            if (data[field]) {
                publishedTime = parseRSSTime(data[field]);
                if (publishedTime) {
                    timeSource = field;
                    break;
                }
            }
        }
        
        if (!publishedTime) {
            invalidTime++;
            continue;
        }
        
        // 检查文章是否在时间窗口内
        if (publishedTime >= cutoffTime) {
            filteredItems.push({
                json: {
                    ...data,
                    standardized_time: publishedTime.toISOString(),
                    time_source: timeSource,
                    filter_passed: true,
                    minutes_ago: Math.round((Date.now() - publishedTime.getTime()) / (1000 * 60))
                }
            });
            
            newArticles++;
            console.log(`✅ 新文章 (${Math.round((Date.now() - publishedTime.getTime()) / (1000 * 60))}分钟前): ${(data.title || 'No Title').substring(0, 50)}...`);
        } else {
            const minutesAgo = Math.round((Date.now() - publishedTime.getTime()) / (1000 * 60));
            console.log(`⏰ 过滤旧文章 (${minutesAgo}分钟前): ${(data.title || 'No Title').substring(0, 50)}...`);
            oldArticles++;
        }
        
    } catch (error) {
        console.log(`❌ 处理错误: ${error.message}`);
        continue;
    }
}

// 输出统计信息
console.log(`📊 过滤结果:`);
console.log(`  ✅ 新文章 (${FILTER_MINUTES}分钟内): ${newArticles}`);
console.log(`  ⏰ 旧文章 (已过滤): ${oldArticles}`);
console.log(`  ⚠️ 无效时间: ${invalidTime}`);
console.log(`  📈 过滤率: ${((oldArticles / items.length) * 100).toFixed(1)}%`);

// 如果没有新文章，返回空数组停止后续处理
if (filteredItems.length === 0) {
    console.log(`🛑 没有新文章，停止后续处理`);
    return [];
}

console.log(`🎉 ${filteredItems.length} 篇新文章将继续处理`);
return filteredItems;'''
        
        return {
            "id": "rss-time-filter",
            "name": "RSS时间过滤器",
            "type": "n8n-nodes-base.code",
            "typeVersion": 2,
            "position": [600, 300],  # 位置会根据实际情况调整
            "parameters": {
                "jsCode": filter_code,
                "mode": "runOnceForAllItems"
            },
            "notes": "⏰ 只处理过去30分钟的RSS文章，避免重复处理"
        }
    
    def insert_filter_node(self, workflow: Dict[str, Any], analysis: Dict[str, Any]) -> Dict[str, Any]:
        """在工作流中插入时间过滤器节点"""
        print("\n🔧 开始插入时间过滤器...")
        
        nodes = workflow.get('nodes', [])
        connections = workflow.get('connections', {})
        
        # 创建时间过滤器节点
        filter_node = self.create_time_filter_node()
        
        # 找到合适的插入位置
        rss_nodes = analysis['rss_nodes']
        processing_nodes = analysis['processing_nodes']
        
        if not rss_nodes:
            print("❌ 未找到RSS节点，无法插入过滤器")
            return workflow
        
        if not processing_nodes:
            print("❌ 未找到处理节点，无法插入过滤器")
            return workflow
        
        # 选择第一个RSS节点和第一个处理节点
        rss_node = rss_nodes[0]
        processing_node = processing_nodes[0]
        
        print(f"📍 插入位置: {rss_node.get('name')} → 时间过滤器 → {processing_node.get('name')}")
        
        # 调整过滤器节点位置
        rss_pos = rss_node.get('position', [400, 300])
        processing_pos = processing_node.get('position', [800, 300])
        
        filter_node['position'] = [
            (rss_pos[0] + processing_pos[0]) // 2,  # X坐标在中间
            rss_pos[1]  # Y坐标与RSS节点对齐
        ]
        
        # 添加过滤器节点到节点列表
        nodes.append(filter_node)
        
        # 修改连接关系
        rss_node_name = rss_node.get('name')
        processing_node_name = processing_node.get('name')
        filter_node_name = filter_node.get('name')
        
        # 找到RSS节点的原始连接
        if rss_node_name in connections:
            original_connections = connections[rss_node_name]
            
            # 将RSS节点连接到过滤器
            connections[rss_node_name] = {
                "main": [[{
                    "node": filter_node_name,
                    "type": "main",
                    "index": 0
                }]]
            }
            
            # 将过滤器连接到原来的目标节点
            connections[filter_node_name] = original_connections
        
        print(f"✅ 时间过滤器插入成功")
        print(f"📊 节点总数: {analysis['total_nodes']} → {len(nodes)}")
        
        return workflow
    
    def update_workflow(self, workflow: Dict[str, Any]) -> bool:
        """更新工作流"""
        try:
            url = f"{self.base_url}/api/v1/workflows/{self.workflow_id}"

            # 只发送最基本的可修改字段
            clean_workflow = {
                "name": workflow.get("name"),
                "nodes": workflow.get("nodes", []),
                "connections": workflow.get("connections", {})
            }

            # 如果settings存在且不为空，添加它
            if workflow.get("settings"):
                clean_workflow["settings"] = workflow.get("settings")

            # 如果staticData存在，添加它
            if workflow.get("staticData") is not None:
                clean_workflow["staticData"] = workflow.get("staticData")

            print(f"\n📡 更新工作流...")
            response = requests.put(url, headers=self.headers, json=clean_workflow, timeout=30)

            if response.status_code == 200:
                print("✅ 工作流更新成功！")
                print("🎉 时间过滤器已成功插入RSS工作流")
                return True
            else:
                print(f"❌ 工作流更新失败: {response.status_code}")
                print(f"响应: {response.text}")

                # 尝试更详细的错误信息
                try:
                    error_data = response.json()
                    print(f"错误详情: {error_data}")
                except:
                    pass
                return False

        except Exception as e:
            print(f"❌ 工作流更新异常: {e}")
            return False
    
    def run(self) -> bool:
        """执行插入操作"""
        print("🚀 开始插入时间过滤器到N8N工作流")
        print("=" * 50)
        
        # 1. 获取当前工作流
        workflow = self.get_workflow()
        if not workflow:
            return False
        
        # 2. 分析工作流结构
        analysis = self.analyze_workflow_structure(workflow)
        
        # 3. 检查是否已经有时间过滤器
        existing_filter = any(
            'time' in node.get('name', '').lower() and 'filter' in node.get('name', '').lower()
            for node in workflow.get('nodes', [])
        )
        
        if existing_filter:
            print("⚠️ 工作流中已存在时间过滤器，跳过插入")
            return True
        
        # 4. 插入时间过滤器
        modified_workflow = self.insert_filter_node(workflow, analysis)
        
        # 5. 更新工作流
        success = self.update_workflow(modified_workflow)
        
        if success:
            print("\n🎉 时间过滤器插入完成！")
            print("📋 功能说明:")
            print("  ⏰ 只处理过去30分钟的RSS文章")
            print("  🔄 避免重复处理旧文章")
            print("  💾 节省MongoDB存储空间")
            print("  📊 提供详细的过滤统计信息")
        
        return success


def main():
    """主函数"""
    inserter = N8NTimeFilterInserter()
    success = inserter.run()
    
    if success:
        print("\n✅ 操作完成！你的RSS工作流现在只会处理最新的文章了。")
    else:
        print("\n❌ 操作失败，请检查N8N连接和权限。")


if __name__ == "__main__":
    main()
