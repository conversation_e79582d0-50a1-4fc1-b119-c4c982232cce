// N8N 时间过滤节点 - 专门负责30分钟时间窗口过滤
// 放在 Merge 节点之后，数据处理节点之前

const items = $input.all();

// ⏰ 时间过滤配置
const FILTER_MINUTES = 30; // 可调整：15, 30, 60分钟
const cutoffTime = new Date(Date.now() - FILTER_MINUTES * 60 * 1000);

console.log(`🕐 时间过滤器启动`);
console.log(`📅 过滤时间: ${cutoffTime.toISOString()}`);
console.log(`📊 输入文章数: ${items.length}`);
console.log(`⏰ 只保留过去 ${FILTER_MINUTES} 分钟内的文章`);

// 时间解析函数
function parseRSSTime(timeStr) {
  if (!timeStr) return null;
  
  try {
    const date = new Date(timeStr);
    if (!isNaN(date.getTime())) {
      return date;
    }
    return null;
  } catch (error) {
    return null;
  }
}

// 统计变量
let newArticles = 0;
let oldArticles = 0;
let invalidTime = 0;
const filteredItems = [];

// 处理每篇文章
for (const item of items) {
  const data = item.json;
  
  // 跳过无标题文章
  if (!data.title) {
    console.log("⚠️ 跳过无标题文章");
    continue;
  }
  
  // 解析发布时间
  const publishedTime = parseRSSTime(data.isoDate || data.pubDate);
  
  if (!publishedTime) {
    console.log(`⚠️ 无效时间，跳过: ${data.title.substring(0, 30)}...`);
    invalidTime++;
    continue;
  }
  
  // 时间过滤核心逻辑
  if (publishedTime < cutoffTime) {
    const minutesAgo = Math.round((Date.now() - publishedTime.getTime()) / (1000 * 60));
    console.log(`⏰ 过滤旧文章 (${minutesAgo}分钟前): ${data.title.substring(0, 30)}...`);
    oldArticles++;
    continue; // 跳过旧文章
  }
  
  // 文章在时间窗口内，保留
  const minutesAgo = Math.round((Date.now() - publishedTime.getTime()) / (1000 * 60));
  console.log(`✅ 新文章 (${minutesAgo}分钟前): ${data.title.substring(0, 30)}...`);
  
  // 保持原始数据结构不变，直接传递
  filteredItems.push(item);
  newArticles++;
}

// 输出统计信息
console.log(`\n📊 时间过滤统计:`);
console.log(`  ✅ 新文章 (${FILTER_MINUTES}分钟内): ${newArticles}`);
console.log(`  ⏰ 旧文章 (已过滤): ${oldArticles}`);
console.log(`  ⚠️ 无效时间: ${invalidTime}`);
console.log(`  📈 过滤率: ${((oldArticles + invalidTime) / items.length * 100).toFixed(1)}%`);

// 如果没有新文章，停止后续处理
if (filteredItems.length === 0) {
  console.log(`\n🛑 没有新文章，停止后续处理`);
  console.log(`💡 建议: 可以调整 FILTER_MINUTES 参数或检查RSS源更新频率`);
  return [];
}

console.log(`\n🎉 ${filteredItems.length} 篇新文章通过时间过滤，继续处理`);
console.log(`🔄 数据结构保持不变，传递给下一个节点`);

// 返回过滤后的文章，保持原始数据结构
return filteredItems;
