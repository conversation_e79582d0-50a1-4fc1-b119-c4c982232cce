import asyncio
import os
import xml.etree.ElementTree as ET
import math
from ib_insync import IB, Stock
from dotenv import load_dotenv

# 加载 .env 文件，并强制覆盖现有环境变量
load_dotenv(override=True)

# IB 连接配置
IB_HOST = os.getenv("IB_HOST", "127.0.0.1")
IB_PORT = int(os.getenv("IB_PORT", 4002))
IB_CLIENT_ID = int(os.getenv("IB_CLIENT_ID", 99)) # 使用一个唯一的客户端ID

def parse_fundamental_data(xml_data: str) -> dict:
    """解析IB返回的XML基本面数据"""
    data = {
        'morningstar_rating': 0,
        'shares_short': 0,
        'analyst_target_price': 0.0
    }
    print("\n--- 开始解析XML数据 ---")
    if not xml_data:
        print("错误：收到的XML数据为空。")
        return data
        
    try:
        root = ET.fromstring(xml_data)
        print("XML数据成功解析为ElementTree。")

        # 遍历所有 'Ratio' 节点来查找我们需要的数据
        for ratio in root.findall(".//Ratio"):
            field_name = ratio.get('FieldName')
            if field_name == 'Rating' and ratio.text and ratio.text.strip():
                print(f"找到晨星评级: {ratio.text.strip()}")
                data['morningstar_rating'] = int(float(ratio.text.strip()))
            elif field_name == 'SharesShort' and ratio.text and ratio.text.strip():
                print(f"找到做空股数: {ratio.text.strip()}")
                data['shares_short'] = int(float(ratio.text.strip()))
            elif field_name == 'TargetPrice' and ratio.text and ratio.text.strip():
                print(f"找到分析师目标价: {ratio.text.strip()}")
                data['analyst_target_price'] = float(ratio.text.strip())

        if data['morningstar_rating'] == 0: print("最终未找到晨星评级。")
        if data['shares_short'] == 0: print("最终未找到做空股数。")
        if data['analyst_target_price'] == 0.0: print("最终未找到分析师目标价。")
            
    except Exception as e:
        print(f"解析XML时出错: {e}")
        print("--- XML内容 ---")
        print(xml_data[:1000] + "...") # 打印部分XML内容以供调试
        print("--- XML内容结束 ---")
    
    print("--- XML解析完成 ---")
    return data

async def main():
    """主调试函数"""
    ib = IB()
    symbol = "AAPL"
    
    print(f"--- 开始IB连接测试 ---")
    print(f"主机: {IB_HOST}, 端口: {IB_PORT}, 客户端ID: {IB_CLIENT_ID}")
    
    try:
        await ib.connectAsync(IB_HOST, IB_PORT, clientId=IB_CLIENT_ID, timeout=15)
        print("✅ IB连接成功！")
        
        contract = Stock(symbol, 'SMART', 'USD')
        await ib.qualifyContractsAsync(contract)
        print(f"✅ 合约限定成功: {contract.symbol}")
        
        print("\n--- 请求市场数据 (价格) ---")
        ticker = ib.reqMktData(contract, '', False, False)
        await asyncio.sleep(3) # 等待数据返回
        
        if ticker and ticker.last and not math.isnan(ticker.last):
            print(f"✅ 成功获取价格: ${ticker.last}")
        else:
            print(f"⚠️ 未能获取 {symbol} 的实时价格。")
            print(f"Ticker详情: {ticker}")

        print("\n--- 请求基本面数据 (XML) ---")
        # 'ReportSnapshot' 包含更广泛的摘要数据，包括评级和目标价
        xml_data = await ib.reqFundamentalDataAsync(contract, 'ReportSnapshot')
        
        if xml_data:
            print(f"✅ 成功获取 {symbol} 的基本面XML数据。")
            # 将完整的XML数据写入文件以供分析
            output_path = "examples/alibaba/baba_fundamentals_20250629_120800.xml"
            with open(output_path, "w", encoding="utf-8") as f:
                f.write(xml_data)
            print(f"✅ XML数据已保存到: {output_path}")
            
            # 仍然尝试解析，以供即时反馈
            parsed_data = parse_fundamental_data(xml_data)
            print("\n--- 解析结果 ---")
            print(parsed_data)
        else:
            print(f"❌ 未能获取 {symbol} 的基本面数据。")

    except Exception as e:
        print(f"❌ 在调试过程中发生错误: {e}")
    finally:
        if ib.isConnected():
            print("\n--- 断开IB连接 ---")
            ib.disconnect()
            print("✅ IB连接已断开。")

if __name__ == "__main__":
    # 在Windows上，可能需要这个策略来避免事件循环错误
    if os.name == 'nt':
        asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())
    
    try:
        asyncio.run(main())
    except (KeyboardInterrupt, SystemExit):
        print("\n程序被用户中断。")