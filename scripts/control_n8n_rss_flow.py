#!/usr/bin/env python3
"""
直接控制N8N RSS工作流
使用具体的工作流ID来修复问题
"""

import requests
import json
import re
from typing import Dict, Any, Optional

class N8NRSSFlowController:
    """N8N RSS工作流控制器"""
    
    def __init__(self):
        # 从用户提供的信息中提取
        self.workflow_url = "https://n8n.git4ta.fun/workflow/JDwsHwp7VM9lWic7"
        self.api_token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJhNzYwZjUxMy0zMWMzLTQwYzMtOTQ0Zi0xZDkyNGQ4ZjM3Y2QiLCJpc3MiOiJuOG4iLCJhdWQiOiJwdWJsaWMtYXBpIiwiaWF0IjoxNzUxNTU0NjY4fQ.YO11K1xJQQ9lmo-VYCdg8Vf0jyvQ5ufoLF-sWJLqE08"
        
        # 提取工作流ID
        self.workflow_id = "JDwsHwp7VM9lWic7"
        self.base_url = "https://n8n.git4ta.fun"
        
        self.headers = {
            "X-N8N-API-KEY": self.api_token,
            "Content-Type": "application/json"
        }
        
        print("🎯 N8N RSS工作流控制器启动")
        print(f"📊 工作流ID: {self.workflow_id}")
        print(f"🌐 N8N地址: {self.base_url}")
        print("🎭 准备解决'i服了you'的问题...")
    
    def get_workflow(self) -> Optional[Dict[str, Any]]:
        """获取具体的工作流"""
        try:
            url = f"{self.base_url}/api/v1/workflows/{self.workflow_id}"
            response = requests.get(url, headers=self.headers, timeout=15)
            
            print(f"📡 API请求: {url}")
            print(f"📈 响应状态: {response.status_code}")
            
            if response.status_code == 200:
                workflow = response.json()
                print(f"✅ 成功获取工作流: {workflow.get('name', 'Unknown')}")
                return workflow
            else:
                print(f"❌ 获取工作流失败: {response.status_code}")
                print(f"📝 响应内容: {response.text}")
                return None
                
        except Exception as e:
            print(f"❌ 获取工作流异常: {e}")
            return None
    
    def analyze_workflow(self, workflow: Dict[str, Any]) -> None:
        """分析工作流结构"""
        print("\n🔍 分析工作流结构...")
        
        nodes = workflow.get('nodes', [])
        print(f"📊 节点总数: {len(nodes)}")
        
        for i, node in enumerate(nodes):
            node_type = node.get('type', 'unknown')
            node_name = node.get('name', 'unnamed')
            print(f"  {i+1}. {node_name} ({node_type})")
            
            # 特别关注Milvus和Code节点
            if 'milvus' in node_type.lower() or 'vector' in node_name.lower():
                print(f"    🎯 发现Milvus节点!")
                params = node.get('parameters', {})
                collection = params.get('collectionName', 'Not Set')
                print(f"    📝 Collection: {collection}")
            
            if 'code' in node_type.lower():
                print(f"    💻 发现Code节点!")
    
    def fix_workflow(self, workflow: Dict[str, Any]) -> Dict[str, Any]:
        """修复工作流"""
        print("\n🔧 开始修复工作流...")
        
        nodes = workflow.get('nodes', [])
        fixed_count = 0
        
        for node in nodes:
            node_type = node.get('type', '')
            node_name = node.get('name', '')
            
            # 修复Milvus Vector Store节点
            if 'milvus' in node_type.lower() or 'vector' in node_name.lower():
                print(f"🎯 修复Milvus节点: {node_name}")
                
                if 'parameters' not in node:
                    node['parameters'] = {}
                
                # 设置正确的collection名称
                old_collection = node['parameters'].get('collectionName', 'Not Set')
                node['parameters']['collectionName'] = 'ifuleyou'
                
                print(f"  📝 Collection: {old_collection} → ifuleyou")
                fixed_count += 1
            
            # 修复Code节点
            if 'code' in node_type.lower() and any(keyword in node_name.lower() for keyword in ['format', '格式', 'process', '处理', 'transform']):
                print(f"💻 修复Code节点: {node_name}")
                
                if 'parameters' not in node:
                    node['parameters'] = {}
                
                # 设置修复后的代码
                node['parameters']['jsCode'] = self._get_fixed_code()
                print(f"  ✅ 已更新JavaScript代码")
                fixed_count += 1
        
        print(f"🎉 修复完成，共修复 {fixed_count} 个节点")
        return workflow
    
    def _get_fixed_code(self) -> str:
        """获取修复后的JavaScript代码"""
        return '''// 🎭 修复"i服了you"问题的代码
const processedItems = [];
const items = $input.all();

console.log("🔍 输入数据数量:", items.length);

for (const item of items) {
    if (item && item.json) {
        // 确保title存在 - 这是关键！
        const title = item.json.title && item.json.title.trim() !== '' 
            ? String(item.json.title) 
            : "无标题文章";
            
        const publishedDate = item.json.published_time 
            ? new Date(item.json.published_time) 
            : new Date();
        const simpleTime = publishedDate.toISOString();

        // 构建安全的metadata - 确保title字段存在
        const safeMetadata = {
            title: title,  // 🎯 这个字段是必需的！
            published_date: String(simpleTime),
            article_id: String(item.json.article_id || `article_${Date.now()}_${Math.random()}`)
        };

        // LangChain Document格式
        const formattedItem = {
            pageContent: String(item.json.content || item.json.title || "空内容"),
            metadata: safeMetadata
        };
        
        console.log(`📝 处理文档: ${safeMetadata.title}`);
        
        // 🚨 关键：直接推送，不要包装在json中！
        processedItems.push(formattedItem);
    }
}

// 兜底处理
if (processedItems.length === 0) {
    console.log("⚠️ 没有有效数据，返回默认文档");
    return [{
        pageContent: "默认测试内容",
        metadata: {
            title: "默认标题",
            published_date: new Date().toISOString(),
            article_id: "default_article"
        }
    }];
}

console.log(`✅ 返回 ${processedItems.length} 个文档`);
return processedItems;'''
    
    def update_workflow(self, workflow: Dict[str, Any]) -> bool:
        """更新工作流"""
        try:
            url = f"{self.base_url}/api/v1/workflows/{self.workflow_id}"
            
            print(f"\n📡 更新工作流: {url}")
            response = requests.put(url, headers=self.headers, json=workflow, timeout=30)
            
            print(f"📈 响应状态: {response.status_code}")
            
            if response.status_code == 200:
                print("✅ 工作流更新成功！")
                return True
            else:
                print(f"❌ 工作流更新失败: {response.status_code}")
                print(f"📝 响应内容: {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ 工作流更新异常: {e}")
            return False
    
    def execute_workflow(self) -> bool:
        """执行工作流测试"""
        try:
            url = f"{self.base_url}/api/v1/workflows/{self.workflow_id}/execute"
            
            test_data = {
                "title": "测试文章 - i服了you",
                "content": "这是一篇测试文章，用于验证修复后的工作流是否正常。",
                "published_time": "2025-01-09T10:00:00Z",
                "article_id": "test_ifuleyou_123",
                "source": "测试源"
            }
            
            print(f"\n🧪 执行工作流测试...")
            response = requests.post(url, headers=self.headers, json=test_data, timeout=30)
            
            print(f"📈 响应状态: {response.status_code}")
            
            if response.status_code == 200:
                print("✅ 工作流执行成功！")
                return True
            else:
                print(f"❌ 工作流执行失败: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ 工作流执行异常: {e}")
            return False
    
    def run_complete_fix(self) -> bool:
        """执行完整的修复流程"""
        print("🚀 开始完整修复流程...")
        print("=" * 60)
        
        # 1. 获取工作流
        workflow = self.get_workflow()
        if not workflow:
            return False
        
        # 2. 分析工作流
        self.analyze_workflow(workflow)
        
        # 3. 修复工作流
        fixed_workflow = self.fix_workflow(workflow)
        
        # 4. 更新工作流
        update_success = self.update_workflow(fixed_workflow)
        if not update_success:
            return False
        
        # 5. 测试工作流
        test_success = self.execute_workflow()
        
        print("=" * 60)
        if update_success and test_success:
            print("🎉 完整修复成功！")
            print("🎭 'i服了you'的问题彻底解决了！")
            print("💡 现在可以正常写入Zilliz数据库了")
        else:
            print("⚠️ 修复部分成功，可能需要手动检查")
        
        return update_success

def main():
    """主函数"""
    controller = N8NRSSFlowController()
    success = controller.run_complete_fix()
    
    if success:
        print("\n✅ 修复完成，工作流应该正常了")
    else:
        print("\n❌ 修复失败，请检查API权限或手动修复")

if __name__ == "__main__":
    main()
