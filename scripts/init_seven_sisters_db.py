#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
初始化七姐妹基本面数据库
创建表结构并进行首次数据更新
"""

import sys
import os
import asyncio
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.data.seven_sisters_db_manager import SevenSistersDBManager
from ib_insync import util

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

async def main():
    """主函数"""
    setup_logging()
    logger = logging.getLogger(__name__)
    
    logger.info("🚀 开始初始化七姐妹基本面数据库")
    
    try:
        manager = SevenSistersDBManager()
        
        # 初始化数据库结构
        logger.info("🔧 创建数据库表结构...")
        manager.init_database()
        logger.info("✅ 数据库表结构创建完成")
        
        # 进行首次数据更新
        logger.info("📊 开始首次数据更新...")
        results = await manager.update_all_fundamentals()
        
        # 输出结果
        success_count = len(results['success'])
        error_count = len(results['errors'])
        
        logger.info(f"✅ 首次数据更新完成!")
        logger.info(f"   成功: {success_count} 只股票")
        logger.info(f"   失败: {error_count} 只股票")
        
        if results['success']:
            logger.info(f"   成功更新: {', '.join(results['success'])}")
        
        if results['errors']:
            logger.warning(f"   失败详情: {results['errors']}")
        
        # 验证数据
        logger.info("🔍 验证初始化结果...")
        latest_df = manager.get_latest_fundamentals()
        
        if not latest_df.empty:
            logger.info(f"   数据库中共有 {len(latest_df)} 条记录")
            logger.info("   七姐妹基本面数据:")
            for _, row in latest_df.iterrows():
                price = f"${row['close_price']:.2f}" if pd.notna(row['close_price']) else "N/A"
                eps = f"${row['eps_ttm']:.2f}" if pd.notna(row['eps_ttm']) else "N/A"
                pe = f"{row['pe_ratio']:.1f}" if pd.notna(row['pe_ratio']) else "N/A"
                logger.info(f"   {row['emoji']} {row['symbol']}: 价格={price}, EPS={eps}, PE={pe}")
        else:
            logger.warning("   ⚠️ 数据库中没有找到数据")
        
        # 测试排名功能
        logger.info("🏆 测试排名功能...")
        ranking_df = manager.get_eps_ranking()
        
        if not ranking_df.empty:
            logger.info("   EPS排名前三:")
            for i, (_, row) in enumerate(ranking_df.head(3).iterrows()):
                logger.info(f"   {i+1}. {row['emoji']} {row['symbol']} (综合评分: {row['composite_score']:.2f})")
        
        logger.info("🎉 七姐妹基本面数据库初始化完成!")
        logger.info("💡 现在可以在Streamlit中查看七仙女雷达的EPS横向比较功能了")
        
        return 0 if error_count == 0 else 1
            
    except Exception as e:
        logger.error(f"❌ 初始化失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return 2

if __name__ == "__main__":
    # 设置IB事件循环
    util.patchAsyncio()
    
    # 需要pandas用于数据验证
    import pandas as pd
    
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except (KeyboardInterrupt, SystemExit):
        print("\n🛑 初始化被用户中断")
        sys.exit(130)