# N8N Webhook激活和集成指南

## 🎯 问题分析

根据测试结果，您的N8N webhook处于**测试模式**，需要手动激活。

### 错误信息解读：
```json
{
  "code": 404,
  "message": "The requested webhook \"ce40f698-832e-475a-a3c7-0895c9e2e90b\" is not registered.",
  "hint": "Click the 'Execute workflow' button on the canvas, then try again. (In test mode, the webhook only works for one call after you click this button)"
}
```

## 🔧 解决方案

### 步骤1: 激活N8N Webhook（必须先做）

1. **访问您的N8N工作流**:
   ```
   https://houzhongxu-n8n-free.hf.space/workflow/5Ibi4vJZjSB0ZaTt
   ```

2. **在N8N界面中**:
   - 找到并点击 **"Execute workflow"** 按钮
   - 或者点击 **"Test workflow"** 按钮
   - 这会激活webhook端点，使其可以接收一次请求

3. **立即测试**:
   - 激活后，webhook只能接收**一次**请求
   - 需要立即发送测试数据

### 步骤2: 将Webhook设置为生产模式

为了让webhook持续可用，需要：

1. **激活工作流**:
   - 在N8N界面中，将工作流状态设置为 **"Active"**
   - 这样webhook就会持续运行，不需要每次手动激活

2. **使用生产webhook URL**:
   ```
   https://houzhongxu-n8n-free.hf.space/webhook/ce40f698-832e-475a-a3c7-0895c9e2e90b
   ```
   注意：去掉了 `-test` 部分

## 🚀 立即可执行的测试方案

### 方案A: 手动激活测试（推荐）

1. **现在就去N8N界面点击"Execute workflow"**
2. **立即运行以下命令**:
   ```bash
   python3 tmp_rovodev_webhook_test.py
   ```

### 方案B: 使用生产webhook

更新webhook URL为生产模式：
```bash
# 将测试URL
https://houzhongxu-n8n-free.hf.space/webhook-test/ce40f698-832e-475a-a3c7-0895c9e2e90b

# 改为生产URL
https://houzhongxu-n8n-free.hf.space/webhook/ce40f698-832e-475a-a3c7-0895c9e2e90b
```

## 📝 更新配置

### 更新环境变量
```bash
# 测试模式webhook（需要手动激活）
N8N_WEBHOOK_TEST=https://houzhongxu-n8n-free.hf.space/webhook-test/ce40f698-832e-475a-a3c7-0895c9e2e90b

# 生产模式webhook（持续可用）
N8N_WEBHOOK_PROD=https://houzhongxu-n8n-free.hf.space/webhook/ce40f698-832e-475a-a3c7-0895c9e2e90b
```

### 智能重试机制

创建一个智能的webhook客户端，可以处理两种模式：

```python
class SmartN8NWebhookClient:
    def __init__(self):
        self.test_url = "https://houzhongxu-n8n-free.hf.space/webhook-test/ce40f698-832e-475a-a3c7-0895c9e2e90b"
        self.prod_url = "https://houzhongxu-n8n-free.hf.space/webhook/ce40f698-832e-475a-a3c7-0895c9e2e90b"
    
    def send_data(self, data):
        # 先尝试生产模式
        try:
            response = requests.post(self.prod_url, json=data, timeout=10)
            if response.status_code == 200:
                return {"success": True, "mode": "production"}
        except:
            pass
        
        # 如果生产模式失败，尝试测试模式
        try:
            response = requests.post(self.test_url, json=data, timeout=10)
            if response.status_code == 200:
                return {"success": True, "mode": "test"}
            elif response.status_code == 404:
                return {
                    "success": False, 
                    "mode": "test",
                    "message": "需要在N8N中点击'Execute workflow'激活webhook"
                }
        except Exception as e:
            return {"success": False, "error": str(e)}
```

## 🎯 推荐的工作流程

### 开发阶段
1. 使用测试webhook进行开发和调试
2. 每次测试前在N8N中点击"Execute workflow"
3. 立即发送测试数据

### 生产阶段
1. 将N8N工作流设置为"Active"状态
2. 使用生产webhook URL
3. 实现自动重试和错误处理

## 📊 完整的集成代码

```python
#!/usr/bin/env python3
"""
太公心易 → N8N 智能集成客户端
支持测试模式和生产模式的自动切换
"""

import requests
import json
import time
from datetime import datetime
from typing import Dict, Any, Optional

class TaiGongXinYiN8NClient:
    def __init__(self):
        self.test_webhook = "https://houzhongxu-n8n-free.hf.space/webhook-test/ce40f698-832e-475a-a3c7-0895c9e2e90b"
        self.prod_webhook = "https://houzhongxu-n8n-free.hf.space/webhook/ce40f698-832e-475a-a3c7-0895c9e2e90b"
        
    def send_rss_analysis(self, rss_data: Dict[str, Any]) -> Dict[str, Any]:
        """发送RSS分析数据到N8N"""
        
        payload = {
            "timestamp": datetime.now().isoformat(),
            "source": "太公心易RSS分析",
            "type": "rss_analysis",
            "data": rss_data
        }
        
        # 尝试生产模式
        result = self._try_webhook(self.prod_webhook, payload, "production")
        if result["success"]:
            return result
        
        # 尝试测试模式
        result = self._try_webhook(self.test_webhook, payload, "test")
        return result
    
    def send_market_data(self, market_data: Dict[str, Any]) -> Dict[str, Any]:
        """发送市场数据到N8N"""
        
        payload = {
            "timestamp": datetime.now().isoformat(),
            "source": "太公心易市场分析",
            "type": "market_analysis",
            "data": market_data
        }
        
        # 智能重试逻辑
        return self._smart_send(payload)
    
    def _try_webhook(self, url: str, data: Dict[str, Any], mode: str) -> Dict[str, Any]:
        """尝试发送到指定webhook"""
        try:
            response = requests.post(
                url, 
                json=data, 
                timeout=30,
                headers={'Content-Type': 'application/json'}
            )
            
            if response.status_code == 200:
                return {
                    "success": True,
                    "mode": mode,
                    "response": response.json() if response.content else None
                }
            elif response.status_code == 404 and mode == "test":
                return {
                    "success": False,
                    "mode": mode,
                    "message": "测试webhook未激活，请在N8N中点击'Execute workflow'"
                }
            else:
                return {
                    "success": False,
                    "mode": mode,
                    "status_code": response.status_code,
                    "error": response.text
                }
                
        except Exception as e:
            return {
                "success": False,
                "mode": mode,
                "error": str(e)
            }
    
    def _smart_send(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """智能发送，自动选择最佳webhook"""
        
        # 1. 尝试生产模式
        result = self._try_webhook(self.prod_webhook, data, "production")
        if result["success"]:
            print("✅ 生产模式发送成功")
            return result
        
        # 2. 尝试测试模式
        result = self._try_webhook(self.test_webhook, data, "test")
        if result["success"]:
            print("✅ 测试模式发送成功")
            return result
        
        # 3. 都失败了
        print("❌ 所有webhook都不可用")
        return result

# 使用示例
if __name__ == "__main__":
    client = TaiGongXinYiN8NClient()
    
    # 测试RSS数据
    test_data = {
        "title": "太公心易测试 - 科技股分析",
        "content": "今日科技股表现强劲，建议关注。",
        "sentiment": "positive",
        "confidence": 0.85
    }
    
    result = client.send_rss_analysis(test_data)
    print("发送结果:", json.dumps(result, indent=2, ensure_ascii=False))
```

## 🎯 立即行动建议

**现在就可以做的事情**:

1. **立即去N8N界面**: `https://houzhongxu-n8n-free.hf.space/workflow/5Ibi4vJZjSB0ZaTt`
2. **点击"Execute workflow"按钮**
3. **立即运行测试**: `python3 tmp_rovodev_webhook_test.py`
4. **如果成功，将工作流设置为"Active"状态**

这样您就可以实现太公心易系统与N8N的完美集成了！🚀