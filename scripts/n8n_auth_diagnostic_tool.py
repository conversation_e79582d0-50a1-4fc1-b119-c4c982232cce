#!/usr/bin/env python3
"""
N8N API认证诊断工具
解决token失效和认证问题
"""

import requests
import json
import base64
from datetime import datetime
from typing import Dict, List, Any, Optional

class N8NAuthDiagnostic:
    """N8N认证诊断和修复工具"""
    
    def __init__(self):
        self.base_url = "https://houzhongxu-n8n-free.hf.space"
        self.workflow_id = "5Ibi4vJZjSB0ZaTt"
        self.webhook_id = "ce40f698-832e-475a-a3c7-0895c9e2e90b"
        
        # 可能的API端点
        self.api_endpoints = {
            "workflows": f"{self.base_url}/api/v1/workflows",
            "executions": f"{self.base_url}/api/v1/executions", 
            "workflow_detail": f"{self.base_url}/api/v1/workflows/{self.workflow_id}",
            "workflow_execute": f"{self.base_url}/api/v1/workflows/{self.workflow_id}/execute",
            "health": f"{self.base_url}/healthz",
            "webhook_prod": f"{self.base_url}/webhook/{self.webhook_id}",
            "webhook_test": f"{self.base_url}/webhook-test/{self.webhook_id}"
        }
        
        print("🔐 N8N API认证诊断工具启动")
        print(f"🌐 N8N实例: {self.base_url}")
        print(f"📊 工作流ID: {self.workflow_id}")
    
    def diagnose_auth_issues(self) -> Dict[str, Any]:
        """诊断认证问题"""
        
        print("\n🔍 开始认证问题诊断...")
        
        diagnosis = {
            "timestamp": datetime.now().isoformat(),
            "auth_methods_tested": {},
            "endpoint_access": {},
            "webhook_status": {},
            "recommendations": []
        }
        
        # 1. 测试无认证访问
        print("\n1️⃣ 测试无认证访问...")
        no_auth_results = self._test_no_auth_access()
        diagnosis["auth_methods_tested"]["no_auth"] = no_auth_results
        
        # 2. 测试可能的认证方法
        print("\n2️⃣ 测试可能的认证方法...")
        auth_methods_results = self._test_auth_methods()
        diagnosis["auth_methods_tested"].update(auth_methods_results)
        
        # 3. 测试所有端点的访问性
        print("\n3️⃣ 测试端点访问性...")
        endpoint_results = self._test_endpoint_access()
        diagnosis["endpoint_access"] = endpoint_results
        
        # 4. 专门测试webhook状态
        print("\n4️⃣ 测试webhook状态...")
        webhook_results = self._test_webhook_status()
        diagnosis["webhook_status"] = webhook_results
        
        # 5. 生成修复建议
        diagnosis["recommendations"] = self._generate_auth_recommendations(diagnosis)
        
        return diagnosis
    
    def _test_no_auth_access(self) -> Dict[str, Any]:
        """测试无认证访问"""
        
        results = {}
        
        for endpoint_name, url in self.api_endpoints.items():
            try:
                response = requests.get(url, timeout=10)
                
                results[endpoint_name] = {
                    "status_code": response.status_code,
                    "accessible": response.status_code not in [401, 403],
                    "requires_auth": response.status_code in [401, 403],
                    "response_size": len(response.content),
                    "headers": dict(response.headers)
                }
                
                if response.status_code == 200:
                    print(f"   ✅ {endpoint_name}: 无需认证")
                elif response.status_code in [401, 403]:
                    print(f"   🔐 {endpoint_name}: 需要认证")
                else:
                    print(f"   ⚠️ {endpoint_name}: {response.status_code}")
                    
            except Exception as e:
                results[endpoint_name] = {
                    "error": str(e),
                    "accessible": False
                }
                print(f"   ❌ {endpoint_name}: {e}")
        
        return results
    
    def _test_auth_methods(self) -> Dict[str, Any]:
        """测试各种认证方法"""
        
        auth_methods = {
            "basic_auth_admin": self._test_basic_auth("admin", "admin"),
            "basic_auth_n8n": self._test_basic_auth("n8n", "n8n"),
            "basic_auth_user": self._test_basic_auth("user", "password"),
            "api_key_header": self._test_api_key_auth(),
            "bearer_token": self._test_bearer_token(),
            "session_auth": self._test_session_auth()
        }
        
        return auth_methods
    
    def _test_basic_auth(self, username: str, password: str) -> Dict[str, Any]:
        """测试基础认证"""
        
        try:
            auth = (username, password)
            response = requests.get(
                self.api_endpoints["workflows"], 
                auth=auth, 
                timeout=5
            )
            
            result = {
                "method": "basic_auth",
                "username": username,
                "status_code": response.status_code,
                "success": response.status_code == 200,
                "requires_auth": response.status_code in [401, 403]
            }
            
            if response.status_code == 200:
                print(f"   ✅ Basic Auth ({username}): 成功")
            elif response.status_code in [401, 403]:
                print(f"   ❌ Basic Auth ({username}): 认证失败")
            else:
                print(f"   ⚠️ Basic Auth ({username}): {response.status_code}")
            
            return result
            
        except Exception as e:
            return {
                "method": "basic_auth",
                "username": username,
                "error": str(e),
                "success": False
            }
    
    def _test_api_key_auth(self) -> Dict[str, Any]:
        """测试API Key认证"""
        
        # 常见的API Key头部名称
        api_key_headers = [
            "X-N8N-API-KEY",
            "Authorization",
            "X-API-KEY",
            "API-KEY"
        ]
        
        # 尝试从环境变量或常见位置获取可能的API Key
        possible_keys = [
            "n8n_api_key_placeholder",
            "test_api_key",
            "demo_key"
        ]
        
        results = {}
        
        for header_name in api_key_headers:
            for api_key in possible_keys:
                try:
                    headers = {header_name: api_key}
                    response = requests.get(
                        self.api_endpoints["workflows"],
                        headers=headers,
                        timeout=5
                    )
                    
                    key_name = f"{header_name}_{api_key}"
                    results[key_name] = {
                        "header": header_name,
                        "key": api_key,
                        "status_code": response.status_code,
                        "success": response.status_code == 200
                    }
                    
                    if response.status_code == 200:
                        print(f"   ✅ API Key ({header_name}): 成功")
                        break
                        
                except Exception as e:
                    continue
        
        return results
    
    def _test_bearer_token(self) -> Dict[str, Any]:
        """测试Bearer Token认证"""
        
        # 尝试一些可能的token格式
        possible_tokens = [
            "bearer_token_placeholder",
            "n8n_token",
            "demo_token"
        ]
        
        results = {}
        
        for token in possible_tokens:
            try:
                headers = {"Authorization": f"Bearer {token}"}
                response = requests.get(
                    self.api_endpoints["workflows"],
                    headers=headers,
                    timeout=5
                )
                
                results[token] = {
                    "token": token,
                    "status_code": response.status_code,
                    "success": response.status_code == 200
                }
                
                if response.status_code == 200:
                    print(f"   ✅ Bearer Token: 成功")
                    break
                    
            except Exception as e:
                continue
        
        return results
    
    def _test_session_auth(self) -> Dict[str, Any]:
        """测试会话认证"""
        
        try:
            # 尝试获取登录页面
            login_response = requests.get(f"{self.base_url}/", timeout=5)
            
            return {
                "method": "session_auth",
                "login_page_accessible": login_response.status_code == 200,
                "status_code": login_response.status_code,
                "note": "需要通过Web界面登录获取会话"
            }
            
        except Exception as e:
            return {
                "method": "session_auth",
                "error": str(e),
                "success": False
            }
    
    def _test_endpoint_access(self) -> Dict[str, Any]:
        """测试端点访问性"""
        
        results = {}
        
        for endpoint_name, url in self.api_endpoints.items():
            print(f"   🔍 测试 {endpoint_name}...")
            
            try:
                # 测试GET请求
                get_response = requests.get(url, timeout=5)
                
                # 测试POST请求（如果适用）
                post_response = None
                if "execute" in endpoint_name or "webhook" in endpoint_name:
                    try:
                        post_response = requests.post(
                            url, 
                            json={"test": True}, 
                            timeout=5
                        )
                    except:
                        pass
                
                results[endpoint_name] = {
                    "url": url,
                    "get_status": get_response.status_code,
                    "get_accessible": get_response.status_code not in [404, 500],
                    "post_status": post_response.status_code if post_response else None,
                    "requires_auth": get_response.status_code in [401, 403],
                    "response_headers": dict(get_response.headers)
                }
                
                if get_response.status_code == 200:
                    print(f"     ✅ GET: 成功")
                elif get_response.status_code in [401, 403]:
                    print(f"     🔐 GET: 需要认证")
                elif get_response.status_code == 404:
                    print(f"     ❌ GET: 不存在")
                else:
                    print(f"     ⚠️ GET: {get_response.status_code}")
                
                if post_response:
                    if post_response.status_code == 200:
                        print(f"     ✅ POST: 成功")
                    else:
                        print(f"     ⚠️ POST: {post_response.status_code}")
                
            except Exception as e:
                results[endpoint_name] = {
                    "url": url,
                    "error": str(e),
                    "accessible": False
                }
                print(f"     ❌ 错误: {e}")
        
        return results
    
    def _test_webhook_status(self) -> Dict[str, Any]:
        """专门测试webhook状态"""
        
        webhook_tests = {}
        
        test_data = {
            "auth_test": True,
            "timestamp": datetime.now().isoformat(),
            "source": "认证诊断工具"
        }
        
        # 测试生产webhook
        print("   🔗 测试生产webhook...")
        try:
            prod_response = requests.post(
                self.api_endpoints["webhook_prod"],
                json=test_data,
                timeout=10
            )
            
            webhook_tests["production"] = {
                "url": self.api_endpoints["webhook_prod"],
                "status_code": prod_response.status_code,
                "success": prod_response.status_code == 200,
                "response": prod_response.text[:200],
                "requires_auth": prod_response.status_code in [401, 403]
            }
            
            if prod_response.status_code == 200:
                print("     ✅ 生产webhook: 正常")
            elif prod_response.status_code in [401, 403]:
                print("     🔐 生产webhook: 需要认证")
            else:
                print(f"     ⚠️ 生产webhook: {prod_response.status_code}")
                
        except Exception as e:
            webhook_tests["production"] = {
                "url": self.api_endpoints["webhook_prod"],
                "error": str(e),
                "success": False
            }
        
        # 测试测试webhook
        print("   🧪 测试测试webhook...")
        try:
            test_response = requests.post(
                self.api_endpoints["webhook_test"],
                json=test_data,
                timeout=10
            )
            
            webhook_tests["test_mode"] = {
                "url": self.api_endpoints["webhook_test"],
                "status_code": test_response.status_code,
                "success": test_response.status_code == 200,
                "response": test_response.text[:200],
                "requires_auth": test_response.status_code in [401, 403],
                "needs_activation": test_response.status_code == 404
            }
            
            if test_response.status_code == 200:
                print("     ✅ 测试webhook: 正常")
            elif test_response.status_code == 404:
                print("     🔧 测试webhook: 需要激活")
            elif test_response.status_code in [401, 403]:
                print("     🔐 测试webhook: 需要认证")
            else:
                print(f"     ⚠️ 测试webhook: {test_response.status_code}")
                
        except Exception as e:
            webhook_tests["test_mode"] = {
                "url": self.api_endpoints["webhook_test"],
                "error": str(e),
                "success": False
            }
        
        return webhook_tests
    
    def _generate_auth_recommendations(self, diagnosis: Dict) -> List[str]:
        """生成认证修复建议"""
        
        recommendations = []
        
        # 分析webhook状态
        webhook_status = diagnosis.get("webhook_status", {})
        prod_webhook = webhook_status.get("production", {})
        test_webhook = webhook_status.get("test_mode", {})
        
        # 基于生产webhook状态
        if prod_webhook.get("success"):
            recommendations.extend([
                "✅ 生产webhook无需认证，可以直接使用",
                "🚀 建议继续使用生产webhook进行集成",
                "📊 可以开始配置自动化数据推送"
            ])
        elif prod_webhook.get("requires_auth"):
            recommendations.extend([
                "🔐 生产webhook需要认证，需要获取有效的API token",
                "🔑 建议联系N8N管理员获取API密钥",
                "⚙️ 或在N8N界面中生成新的API token"
            ])
        
        # 基于测试webhook状态
        if test_webhook.get("needs_activation"):
            recommendations.append("🔧 测试webhook需要在N8N界面中手动激活")
        elif test_webhook.get("requires_auth"):
            recommendations.append("🔐 测试webhook也需要认证")
        
        # 基于API端点访问
        endpoint_access = diagnosis.get("endpoint_access", {})
        auth_required_count = sum(
            1 for result in endpoint_access.values() 
            if result.get("requires_auth", False)
        )
        
        if auth_required_count > 0:
            recommendations.extend([
                f"🔐 {auth_required_count} 个API端点需要认证",
                "📋 建议获取N8N API访问权限",
                "🌐 可以通过N8N Web界面生成API token"
            ])
        
        # 通用建议
        recommendations.extend([
            "📖 建议查看N8N文档了解认证配置",
            "🔄 如果是Hugging Face Spaces，可能需要重启实例",
            "⚡ 考虑将工作流设置为公开访问（如果安全允许）"
        ])
        
        return recommendations
    
    def generate_auth_fix_guide(self, diagnosis: Dict) -> str:
        """生成认证修复指南"""
        
        guide = f"""
# N8N认证问题修复指南

## 🔍 诊断结果总结

**诊断时间**: {diagnosis['timestamp']}

### Webhook状态
"""
        
        webhook_status = diagnosis.get("webhook_status", {})
        
        if webhook_status.get("production", {}).get("success"):
            guide += "- ✅ **生产webhook**: 正常工作，无需认证\n"
        elif webhook_status.get("production", {}).get("requires_auth"):
            guide += "- 🔐 **生产webhook**: 需要认证\n"
        else:
            guide += "- ❌ **生产webhook**: 异常\n"
        
        if webhook_status.get("test_mode", {}).get("needs_activation"):
            guide += "- 🔧 **测试webhook**: 需要手动激活\n"
        elif webhook_status.get("test_mode", {}).get("requires_auth"):
            guide += "- 🔐 **测试webhook**: 需要认证\n"
        else:
            guide += "- ❌ **测试webhook**: 异常\n"
        
        guide += f"""
## 🚀 推荐解决方案

### 方案1: 使用生产webhook（推荐）
如果生产webhook正常工作，直接使用：
```bash
curl -X POST {self.api_endpoints['webhook_prod']} \\
  -H "Content-Type: application/json" \\
  -d '{{"test": true, "timestamp": "$(date -Iseconds)"}}'
```

### 方案2: 获取API认证
1. 访问N8N界面: {self.base_url}
2. 进入设置 → API Keys
3. 生成新的API密钥
4. 在请求中添加认证头:
```bash
curl -X POST {self.api_endpoints['workflows']} \\
  -H "X-N8N-API-KEY: YOUR_API_KEY" \\
  -H "Content-Type: application/json"
```

### 方案3: 激活测试模式
1. 访问工作流: {self.base_url}/workflow/{self.workflow_id}
2. 点击 "Execute workflow" 按钮
3. 立即测试webhook

## 💡 最佳实践建议
"""
        
        for rec in diagnosis.get("recommendations", [])[:5]:
            guide += f"- {rec}\n"
        
        return guide

def main():
    """主诊断函数"""
    
    print("🔐 N8N API认证诊断工具")
    print("=" * 60)
    
    diagnostic = N8NAuthDiagnostic()
    
    # 执行完整诊断
    diagnosis_results = diagnostic.diagnose_auth_issues()
    
    # 生成修复指南
    fix_guide = diagnostic.generate_auth_fix_guide(diagnosis_results)
    
    # 保存结果
    with open("n8n_auth_diagnosis.json", "w", encoding="utf-8") as f:
        json.dump(diagnosis_results, f, ensure_ascii=False, indent=2)
    
    with open("n8n_auth_fix_guide.md", "w", encoding="utf-8") as f:
        f.write(fix_guide)
    
    # 输出总结
    print("\n" + "=" * 60)
    print("📊 认证诊断总结:")
    
    webhook_status = diagnosis_results.get("webhook_status", {})
    prod_status = "✅ 正常" if webhook_status.get("production", {}).get("success") else "❌ 需要修复"
    test_status = "✅ 正常" if webhook_status.get("test_mode", {}).get("success") else "🔧 需要激活/认证"
    
    print(f"生产webhook: {prod_status}")
    print(f"测试webhook: {test_status}")
    
    print(f"\n💡 主要建议:")
    for i, rec in enumerate(diagnosis_results.get("recommendations", [])[:3], 1):
        print(f"  {i}. {rec}")
    
    print(f"\n📁 文件已生成:")
    print(f"• n8n_auth_diagnosis.json - 详细诊断结果")
    print(f"• n8n_auth_fix_guide.md - 修复指南")
    
    # 给出明确的下一步指导
    if webhook_status.get("production", {}).get("success"):
        print(f"\n🎯 建议: 生产webhook正常，可以继续使用！")
    else:
        print(f"\n🔧 建议: 需要解决认证问题或激活webhook")
    
    return diagnosis_results

if __name__ == "__main__":
    results = main()