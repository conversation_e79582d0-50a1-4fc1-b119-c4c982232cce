# Hugging Face Spaces N8N集成指南

## 🎯 测试结果分析

### ✅ 成功项目
- **基础连接**: N8N实例在Hugging Face Spaces上正常运行
- **工作流访问**: 工作流ID `5Ibi4vJZjSB0ZaTt` 可以访问
- **服务状态**: `https://houzhongxu-n8n-free.hf.space` 响应正常

### ⚠️ 需要配置的项目
- **Webhook端点**: 当前没有配置可用的webhook触发器
- **数据接收**: 需要在N8N工作流中添加Webhook节点

## 🔧 完整集成方案

### 步骤1: 在N8N中配置Webhook触发器

1. **访问您的N8N实例**:
   ```
   https://houzhongxu-n8n-free.hf.space/workflow/5Ibi4vJZjSB0ZaTt
   ```

2. **添加Webhook节点**:
   - 在工作流开始处添加 "Webhook" 节点
   - 配置参数:
     ```json
     {
       "httpMethod": "POST",
       "path": "taigong-xinyi-rss",
       "responseMode": "responseNode",
       "options": {}
     }
     ```

3. **获取Webhook URL**:
   配置完成后，webhook URL应该是:
   ```
   https://houzhongxu-n8n-free.hf.space/webhook/taigong-xinyi-rss
   ```

### 步骤2: 更新项目配置

更新 `.env` 文件:
```bash
# N8N RSS工作流配置 (Hugging Face Spaces)
N8N_RSS_FLOW=https://houzhongxu-n8n-free.hf.space/workflow/5Ibi4vJZjSB0ZaTt
N8N_BASE_URL=https://houzhongxu-n8n-free.hf.space
N8N_WORKFLOW_ID=5Ibi4vJZjSB0ZaTt
N8N_WEBHOOK_URL=https://houzhongxu-n8n-free.hf.space/webhook/taigong-xinyi-rss
```

### 步骤3: 推荐的N8N工作流结构

```mermaid
flowchart TD
    A[Webhook触发器<br/>taigong-xinyi-rss] --> B[数据验证<br/>Function节点]
    B --> C[RSS内容分析<br/>Function节点]
    C --> D[市场情绪分析<br/>AI节点]
    D --> E[数据存储<br/>HTTP Request]
    E --> F[结果响应<br/>Respond to Webhook]
```

### 步骤4: N8N节点配置代码

#### A. 数据验证节点 (Function)
```javascript
// 验证和清理输入数据
const inputData = $json;

// 验证必需字段
if (!inputData.title || !inputData.content) {
    return {
        error: "缺少必需字段: title 或 content",
        timestamp: new Date().toISOString()
    };
}

// 清理和标准化数据
const cleanData = {
    title: inputData.title.trim(),
    content: inputData.content.trim(),
    timestamp: inputData.timestamp || new Date().toISOString(),
    source: inputData.source || "太公心易",
    market_data: inputData.market_data || {},
    analysis_type: "rss_market_analysis"
};

return cleanData;
```

#### B. RSS内容分析节点 (Function)
```javascript
// RSS内容智能分析
const data = $json;

// 关键词提取
function extractKeywords(text) {
    const keywords = text.toLowerCase()
        .match(/\b(股票|市场|涨跌|投资|财报|业绩|收益|亏损|增长|下跌|上涨|买入|卖出|持有)\b/g);
    return [...new Set(keywords || [])];
}

// 情绪分析 (简单版本)
function analyzeSentiment(text) {
    const positiveWords = ['上涨', '增长', '盈利', '买入', '看好', '利好'];
    const negativeWords = ['下跌', '亏损', '卖出', '看空', '利空', '风险'];
    
    let score = 0;
    positiveWords.forEach(word => {
        if (text.includes(word)) score += 1;
    });
    negativeWords.forEach(word => {
        if (text.includes(word)) score -= 1;
    });
    
    return {
        score: score,
        sentiment: score > 0 ? 'positive' : score < 0 ? 'negative' : 'neutral'
    };
}

// 执行分析
const analysis = {
    ...data,
    keywords: extractKeywords(data.content),
    sentiment: analyzeSentiment(data.content),
    analysis_timestamp: new Date().toISOString(),
    processed_by: "太公心易N8N分析引擎"
};

return analysis;
```

#### C. 结果存储节点 (HTTP Request)
```json
{
  "method": "POST",
  "url": "https://your-backend-api.com/api/rss-analysis",
  "headers": {
    "Content-Type": "application/json",
    "Authorization": "Bearer YOUR_API_TOKEN"
  },
  "body": "={{ JSON.stringify($json) }}"
}
```

### 步骤5: 测试集成

创建测试脚本 `test_hf_n8n_integration.py`:
```python
import requests
import json
from datetime import datetime

def test_n8n_integration():
    webhook_url = "https://houzhongxu-n8n-free.hf.space/webhook/taigong-xinyi-rss"
    
    test_data = {
        "title": "太公心易市场分析 - 科技股表现强劲",
        "content": "今日科技股普遍上涨，苹果公司股价上涨3.2%，特斯拉增长2.8%。市场情绪乐观，投资者看好科技板块前景。",
        "timestamp": datetime.now().isoformat(),
        "source": "太公心易RSS分析",
        "market_data": {
            "top_performer": "AAPL",
            "sentiment": "positive",
            "confidence": 0.85
        }
    }
    
    try:
        response = requests.post(webhook_url, json=test_data, timeout=30)
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.text}")
        return response.status_code == 200
    except Exception as e:
        print(f"错误: {e}")
        return False

if __name__ == "__main__":
    success = test_n8n_integration()
    print("✅ 集成测试成功" if success else "❌ 集成测试失败")
```

## 🚀 自动化集成

### 与现有系统集成

修改现有的RSS处理脚本，添加N8N推送功能:

```python
# 在 scripts/control_n8n_rss_flow.py 中添加
class HuggingFaceN8NConnector:
    def __init__(self):
        self.webhook_url = os.getenv('N8N_WEBHOOK_URL')
        self.base_url = os.getenv('N8N_BASE_URL')
    
    def send_rss_analysis(self, analysis_data):
        """发送RSS分析数据到Hugging Face N8N"""
        try:
            response = requests.post(
                self.webhook_url, 
                json=analysis_data, 
                timeout=30
            )
            return response.status_code == 200
        except Exception as e:
            logger.error(f"N8N推送失败: {e}")
            return False
```

## 📊 监控和日志

### 在N8N中添加监控节点
```javascript
// 监控和日志节点
const result = $json;

const logData = {
    workflow_id: "5Ibi4vJZjSB0ZaTt",
    execution_time: new Date().toISOString(),
    status: result.error ? "failed" : "success",
    processed_items: Array.isArray(result) ? result.length : 1,
    source: "太公心易",
    analysis_type: result.analysis_type || "unknown"
};

// 发送到监控系统
console.log("📊 执行统计:", JSON.stringify(logData, null, 2));

return logData;
```

## 🎯 总结

### 当前状态
- ✅ Hugging Face N8N实例运行正常
- ✅ 工作流可访问
- ⚠️ 需要配置Webhook触发器

### 下一步行动
1. 在N8N工作流中添加Webhook节点
2. 配置数据处理和分析节点
3. 测试端到端数据流
4. 集成到现有的太公心易系统

### 预期效果
- 🚀 实时RSS数据分析
- 📊 智能市场情绪分析
- 🔄 自动化数据流处理
- 📈 增强的投资决策支持

完成这些配置后，您的太公心易系统将能够通过Hugging Face Spaces上的N8N实例进行强大的RSS数据分析！