#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Rovodev非交互式执行器
通过预设输入和expect模式解决权限提示问题
"""

import subprocess
import tempfile
import os
import time

def run_rovodev_noninteractive(prompt: str, timeout: int = 300) -> tuple[bool, str]:
    """
    非交互式运行Rovodev
    
    Args:
        prompt: 要执行的提示
        timeout: 超时时间(秒)
    
    Returns:
        (success, result): 成功标志和结果
    """
    
    # 创建临时脚本文件
    with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
        f.write(prompt)
        temp_file = f.name
    
    try:
        # 方法1: 使用echo管道 + shadow模式
        cmd = f'echo "{prompt}" | acli rovodev run --shadow'
        
        print(f"🤖 执行Rovodev命令: {cmd}")
        
        result = subprocess.run(
            cmd,
            shell=True,
            capture_output=True,
            text=True,
            timeout=timeout,
            cwd="/Users/<USER>/cauldron",
            env={**os.environ, 'ROVODEV_AUTO_APPROVE': 'true'}  # 设置环境变量
        )
        
        if result.returncode == 0:
            return True, result.stdout
        else:
            # 如果shadow模式失败，尝试方法2
            return _try_expect_method(prompt, timeout)
            
    except subprocess.TimeoutExpired:
        return False, "执行超时"
    except Exception as e:
        return False, f"执行异常: {e}"
    finally:
        # 清理临时文件
        try:
            os.unlink(temp_file)
        except:
            pass

def _try_expect_method(prompt: str, timeout: int) -> tuple[bool, str]:
    """使用expect方法处理交互式提示"""
    
    # 创建expect脚本
    expect_script = f'''#!/usr/bin/expect -f
set timeout {timeout}

# 启动rovodev
spawn acli rovodev run

# 等待提示并发送命令
expect {{
    ">" {{
        send "{prompt}\\n"
    }}
    timeout {{
        puts "启动超时"
        exit 1
    }}
}}

# 处理可能的权限提示
expect {{
    "*Do you want to proceed*" {{
        send "y\\n"
        exp_continue
    }}
    "*Continue*" {{
        send "y\\n"
        exp_continue
    }}
    "*Allow this operation*" {{
        send "y\\n"
        exp_continue
    }}
    "*Confirm*" {{
        send "y\\n"
        exp_continue
    }}
    "*Are you sure*" {{
        send "y\\n"
        exp_continue
    }}
    "*Execute this command*" {{
        send "y\\n"
        exp_continue
    }}
    "*Apply changes*" {{
        send "y\\n"
        exp_continue
    }}
    "*Save file*" {{
        send "y\\n"
        exp_continue
    }}
    "*Create file*" {{
        send "y\\n"
        exp_continue
    }}
    "*Delete*" {{
        send "n\\n"
        exp_continue
    }}
    "*Remove*" {{
        send "n\\n"
        exp_continue
    }}
    eof {{
        puts "任务完成"
    }}
    timeout {{
        puts "执行超时"
        exit 1
    }}
}}

# 等待进程结束
expect eof
'''
    
    # 写入临时expect脚本
    with tempfile.NamedTemporaryFile(mode='w', suffix='.exp', delete=False) as f:
        f.write(expect_script)
        expect_file = f.name
    
    try:
        # 使expect脚本可执行
        os.chmod(expect_file, 0o755)
        
        # 执行expect脚本
        result = subprocess.run(
            [expect_file],
            capture_output=True,
            text=True,
            timeout=timeout,
            cwd="/Users/<USER>/cauldron"
        )
        
        if result.returncode == 0:
            return True, result.stdout
        else:
            return False, result.stderr
            
    except FileNotFoundError:
        # expect命令不存在，使用最简单的方法
        return _try_simple_method(prompt, timeout)
    except Exception as e:
        return False, f"Expect方法失败: {e}"
    finally:
        try:
            os.unlink(expect_file)
        except:
            pass

def _try_simple_method(prompt: str, timeout: int) -> tuple[bool, str]:
    """最简单的方法 - 直接传递参数"""
    try:
        # 直接将prompt作为参数传递
        cmd = ["acli", "rovodev", "run", "--shadow", prompt]
        
        result = subprocess.run(
            cmd,
            capture_output=True,
            text=True,
            timeout=timeout,
            cwd="/Users/<USER>/cauldron"
        )
        
        if result.returncode == 0:
            return True, result.stdout
        else:
            return False, result.stderr
            
    except Exception as e:
        return False, f"简单方法失败: {e}"

# 测试函数
def test_noninteractive():
    """测试非交互式执行"""
    print("🧪 测试Rovodev非交互式执行...")
    
    success, result = run_rovodev_noninteractive(
        "列出当前目录下的Python文件",
        timeout=60
    )
    
    if success:
        print("✅ 测试成功!")
        print(f"结果: {result[:300]}...")
    else:
        print("❌ 测试失败!")
        print(f"错误: {result}")

if __name__ == "__main__":
    test_noninteractive()
