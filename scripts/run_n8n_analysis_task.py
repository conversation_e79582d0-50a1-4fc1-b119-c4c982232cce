#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
立即执行N8N分析任务
给AI流水线一个实际的工作任务
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from ai_pipeline_orchestrator import AIPipelineOrchestrator, Task, TaskType, TaskPriority
import time
import json

def create_n8n_analysis_tasks():
    """创建N8N分析任务"""
    
    # 任务1: Claude Code快速测试webhook
    webhook_test_task = Task(
        id="webhook_connectivity_test",
        type=TaskType.API_TEST,
        priority=TaskPriority.URGENT,
        description="N8N Webhook连接性测试",
        prompt="""立即测试N8N webhook端点的连接状态：
        
URL: houzhongxu-n8n-free.hf.space/webhook-test/ce40f698-832e-475a-a3c7-0895c9e2e90b

请执行以下测试：
1. 发送POST请求到webhook端点
2. 分析返回的错误信息
3. 检查N8N实例的健康状态
4. 生成连接诊断报告

使用curl或Python requests库进行测试，并提供详细的响应分析。""",
        timeout=120
    )
    
    # 任务2: Rovodev深度分析N8N架构
    architecture_analysis_task = Task(
        id="n8n_architecture_analysis", 
        type=TaskType.ANALYSIS,
        priority=TaskPriority.HIGH,
        description="N8N架构深度分析",
        prompt="""基于当前N8N配置进行深度架构分析：

当前配置：
- N8N实例: houzhongxu-n8n-free.hf.space
- Webhook ID: ce40f698-832e-475a-a3c7-0895c9e2e90b
- 工作流ID: 5Ibi4vJZjSB0ZaTt

分析要点：
1. 为什么webhook返回404错误？
2. N8N工作流激活机制分析
3. HuggingFace Spaces部署的N8N限制
4. 如何优化N8N与Cauldron项目的集成
5. 提出完整的修复和优化方案

请提供详细的技术分析和实施建议。""",
        dependencies=["webhook_connectivity_test"],
        timeout=300
    )
    
    # 任务3: Claude Code创建修复脚本
    fix_script_task = Task(
        id="create_n8n_fix_script",
        type=TaskType.CODE_SNIPPET,
        priority=TaskPriority.NORMAL,
        description="创建N8N修复脚本",
        prompt="""基于分析结果，创建一个N8N修复和优化脚本：

要求：
1. 自动检测N8N工作流状态
2. 提供webhook激活功能
3. 实现健康检查和监控
4. 包含错误处理和重试机制
5. 生成状态报告

脚本应该是Python格式，可以直接运行。""",
        dependencies=["n8n_architecture_analysis"],
        timeout=180
    )
    
    return [webhook_test_task, architecture_analysis_task, fix_script_task]

def main():
    """主函数 - 执行N8N分析任务"""
    print("🚀 启动N8N分析任务流水线...")
    
    # 创建编排器
    orchestrator = AIPipelineOrchestrator()
    orchestrator.start()
    
    # 创建任务
    tasks = create_n8n_analysis_tasks()
    
    print(f"📋 创建了 {len(tasks)} 个任务:")
    for task in tasks:
        task_id = orchestrator.add_task(task)
        print(f"  • {task.id}: {task.description} (分配给: {orchestrator.get_assigned_worker(task)})")
    
    print("\n🎭 AI工具分工:")
    print("  • Claude Code: 快速webhook测试 + 修复脚本编写")
    print("  • Rovodev: 深度架构分析")
    
    print("\n📊 开始监控执行...")
    
    # 监控执行
    start_time = time.time()
    try:
        while True:
            status = orchestrator.get_status()
            elapsed = int(time.time() - start_time)
            
            # 显示实时状态
            print(f"\r⏱️  {elapsed:03d}s | "
                  f"队列: {status['queue_size']} | "
                  f"完成: {status['completed_tasks']} | "
                  f"失败: {status['failed_tasks']} | "
                  f"Claude: {'🔥' if status['workers']['claude']['busy'] else '💤'} | "
                  f"Rovodev: {'🔥' if status['workers']['rovodev']['busy'] else '💤'}", 
                  end='', flush=True)
            
            # 检查是否完成
            if (status["queue_size"] == 0 and 
                not any(w["busy"] for w in status["workers"].values())):
                print(f"\n\n🎉 所有任务已完成!")
                print(f"📊 最终统计: 成功 {status['completed_tasks']} | 失败 {status['failed_tasks']}")
                
                # 显示完成的任务结果
                if orchestrator.completed_tasks:
                    print("\n✅ 完成的任务:")
                    for task_id, task in orchestrator.completed_tasks.items():
                        print(f"\n📝 {task.description}:")
                        result_preview = task.result[:200] if task.result else "无结果"
                        print(f"   {result_preview}...")
                
                if orchestrator.failed_tasks:
                    print("\n❌ 失败的任务:")
                    for task_id, task in orchestrator.failed_tasks.items():
                        print(f"   • {task.description}: {task.result}")
                
                break
            
            time.sleep(2)
            
    except KeyboardInterrupt:
        print(f"\n\n👋 用户中断执行 (运行了 {elapsed} 秒)")
    finally:
        orchestrator.stop()
        print("🛑 流水线已停止")

if __name__ == "__main__":
    main()
