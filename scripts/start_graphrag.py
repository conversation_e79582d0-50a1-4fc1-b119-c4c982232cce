#!/usr/bin/env python3
"""
GraphRAG启动脚本
"""

import os
from simple_graphrag import SimpleGraphRAG

# 配置
API_KEY = "sk-mvigrmsjcpqnofnuaybjjlvemegcngztgxbgebgphbhyqneu"

def main():
    print("=== 简单GraphRAG系统 ===")
    print("使用SiliconFlow API + BAAI/bge-m3向量模型")
    print()
    
    # 初始化GraphRAG
    graphrag = SimpleGraphRAG(API_KEY)
    
    while True:
        print("\n请选择操作：")
        print("1. 添加文档")
        print("2. 查询问题")
        print("3. 退出")
        
        choice = input("\n请输入选择 (1-3): ").strip()
        
        if choice == "1":
            print("\n=== 添加文档 ===")
            title = input("请输入文档标题: ").strip()
            if not title:
                print("标题不能为空")
                continue
            
            print("请输入文档内容（输入'END'结束）:")
            content_lines = []
            while True:
                line = input()
                if line.strip() == "END":
                    break
                content_lines.append(line)
            
            content = "\n".join(content_lines)
            if not content.strip():
                print("内容不能为空")
                continue
            
            try:
                doc_id = graphrag.add_document(title, content)
                print(f"✅ 文档添加成功！ID: {doc_id}")
            except Exception as e:
                print(f"❌ 添加文档失败: {e}")
        
        elif choice == "2":
            print("\n=== 查询问题 ===")
            question = input("请输入您的问题: ").strip()
            if not question:
                print("问题不能为空")
                continue
            
            try:
                print("\n🤔 正在思考...")
                answer = graphrag.query(question)
                print(f"\n💡 回答:\n{answer}")
            except Exception as e:
                print(f"❌ 查询失败: {e}")
        
        elif choice == "3":
            print("再见！")
            break
        
        else:
            print("无效选择，请重新输入")

if __name__ == "__main__":
    main()
