#!/usr/bin/env python3
"""
详细诊断 Supabase 连接问题
"""

import os
import sys
from dotenv import load_dotenv
import urllib.parse

# 加载环境变量
load_dotenv()

def parse_connection_string(conn_str):
    """解析连接字符串"""
    try:
        parsed = urllib.parse.urlparse(conn_str)
        return {
            'scheme': parsed.scheme,
            'username': parsed.username,
            'password': parsed.password,
            'hostname': parsed.hostname,
            'port': parsed.port,
            'database': parsed.path.lstrip('/')
        }
    except Exception as e:
        return {'error': str(e)}

def test_basic_connectivity():
    """测试基本网络连接"""
    import socket
    
    hosts_to_test = [
        ('aws-0-us-east-2.pooler.supabase.com', 5432),
        ('aws-0-us-east-2.pooler.supabase.com', 6543),
        ('db.dopcxawpqiyoakoachob.supabase.co', 5432)
    ]
    
    print("🌐 测试网络连接:")
    for host, port in hosts_to_test:
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(5)
            result = sock.connect_ex((host, port))
            sock.close()
            
            if result == 0:
                print(f"   ✅ {host}:{port} - 可达")
            else:
                print(f"   ❌ {host}:{port} - 不可达 (错误码: {result})")
        except Exception as e:
            print(f"   ❌ {host}:{port} - 连接错误: {e}")

def test_postgresql_detailed():
    """详细测试 PostgreSQL 连接"""
    try:
        import psycopg2
        
        # 获取连接字符串
        pooler_url = os.getenv('SUPABASE_DB_URL_POOLER')
        session_url = os.getenv('SUPABASE_DB_URL_SESSION')
        
        print("\n🔍 详细 PostgreSQL 连接测试:")
        
        # 解析连接字符串
        print("\n📋 连接字符串解析:")
        for name, url in [('Transaction Pooler', pooler_url), ('Session Pooler', session_url)]:
            if url:
                parsed = parse_connection_string(url)
                print(f"   {name}:")
                print(f"     主机: {parsed.get('hostname')}")
                print(f"     端口: {parsed.get('port')}")
                print(f"     用户: {parsed.get('username')}")
                print(f"     数据库: {parsed.get('database')}")
                print(f"     密码长度: {len(parsed.get('password', ''))}")
        
        # 测试连接
        for name, url in [('Session Pooler', session_url), ('Transaction Pooler', pooler_url)]:
            if not url:
                continue
                
            print(f"\n🔄 测试 {name}:")
            try:
                # 尝试连接
                conn = psycopg2.connect(url, connect_timeout=10)
                print(f"   ✅ 连接建立成功")
                
                # 测试基本查询
                cursor = conn.cursor()
                cursor.execute("SELECT current_user, current_database(), version();")
                user, db, version = cursor.fetchone()
                
                print(f"   📊 连接信息:")
                print(f"     当前用户: {user}")
                print(f"     当前数据库: {db}")
                print(f"     PostgreSQL版本: {version[:50]}...")
                
                # 测试权限
                cursor.execute("SELECT schemaname FROM pg_tables LIMIT 5;")
                tables = cursor.fetchall()
                print(f"     可访问的表模式: {[t[0] for t in tables]}")
                
                cursor.close()
                conn.close()
                print(f"   ✅ {name} 测试完全成功")
                return True
                
            except psycopg2.OperationalError as e:
                print(f"   ❌ {name} 操作错误: {e}")
            except psycopg2.Error as e:
                print(f"   ❌ {name} PostgreSQL错误: {e}")
            except Exception as e:
                print(f"   ❌ {name} 未知错误: {e}")
        
        return False
        
    except ImportError:
        print("❌ psycopg2 未安装")
        return False

def test_supabase_client_detailed():
    """详细测试 Supabase 客户端"""
    try:
        from supabase import create_client, Client
        
        url = os.getenv('SUPABASE_URL')
        key = os.getenv('SUPABASE_ANON_KEY')
        
        print("\n🔍 详细 Supabase 客户端测试:")
        
        if not url or not key:
            print("❌ 缺少必要的环境变量")
            return False
        
        supabase: Client = create_client(url, key)
        print("   ✅ 客户端创建成功")
        
        # 测试简单的 RPC 调用
        try:
            # 尝试获取数据库时间
            response = supabase.rpc('now').execute()
            print(f"   ✅ RPC调用成功: {response.data}")
            return True
        except Exception as e:
            print(f"   ⚠️  RPC调用失败，尝试其他方法: {e}")
        
        # 尝试列出表
        try:
            # 这个查询应该总是有效的
            response = supabase.table('information_schema.tables').select('table_name').limit(1).execute()
            print(f"   ✅ 表查询成功")
            return True
        except Exception as e:
            print(f"   ❌ 表查询失败: {e}")
        
        return False
        
    except ImportError:
        print("❌ supabase 库未安装")
        return False
    except Exception as e:
        print(f"❌ Supabase 客户端错误: {e}")
        return False

def main():
    """主函数"""
    print("🔬 Supabase 详细诊断")
    print("=" * 60)
    
    # 基本网络连接测试
    test_basic_connectivity()
    
    # PostgreSQL 详细测试
    pg_success = test_postgresql_detailed()
    
    # Supabase 客户端详细测试
    client_success = test_supabase_client_detailed()
    
    # 总结
    print("\n" + "=" * 60)
    print("📊 诊断总结:")
    print(f"   PostgreSQL 连接: {'✅ 成功' if pg_success else '❌ 失败'}")
    print(f"   Supabase 客户端: {'✅ 成功' if client_success else '❌ 失败'}")
    
    if not pg_success:
        print("\n💡 PostgreSQL 连接建议:")
        print("   1. 检查 Supabase 项目是否暂停")
        print("   2. 验证数据库密码是否正确")
        print("   3. 确认网络防火墙设置")
        print("   4. 检查 Supabase 项目的网络访问限制")

if __name__ == "__main__":
    main()
