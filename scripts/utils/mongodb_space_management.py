#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MongoDB免费层空间管理器
自动清理旧数据，确保512MB限制内运行
"""

import os
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
import pymongo
from pymongo import MongoClient
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("MongoDBSpaceManager")


class MongoDBSpaceManager:
    """MongoDB免费层空间管理器"""
    
    def __init__(self):
        self.client = None
        self.db = None
        self.collection = None
        
        # 空间管理配置
        self.MAX_SIZE_MB = 400  # 保留100MB缓冲空间
        self.MAX_ARTICLES = 70000  # 预估最大文章数
        self.RETENTION_DAYS = 30  # 保留30天数据
        self.CLEANUP_BATCH_SIZE = 1000  # 批量删除大小
        
        self.connect()
    
    def connect(self):
        """连接到MongoDB Atlas"""
        try:
            mongo_uri = os.getenv('mongodb_string')
            if not mongo_uri:
                raise ValueError("未找到MongoDB连接字符串")
            
            logger.info("🔗 连接MongoDB Atlas...")
            
            self.client = MongoClient(
                mongo_uri,
                tls=True,
                tlsAllowInvalidCertificates=True,
                serverSelectionTimeoutMS=5000,
                connectTimeoutMS=5000,
                socketTimeoutMS=5000
            )
            
            # 测试连接
            self.client.admin.command('ping')
            logger.info("✅ MongoDB连接成功")
            
            # 连接到RSS数据库
            self.db = self.client['cauldron_rss']
            self.collection = self.db['articles']
            
        except Exception as e:
            logger.error(f"❌ MongoDB连接失败: {e}")
            raise
    
    def get_database_stats(self) -> Dict[str, Any]:
        """获取数据库统计信息"""
        try:
            stats = self.db.command("dbStats")
            collection_stats = self.db.command("collStats", "articles")
            
            return {
                "database_size_mb": stats.get("dataSize", 0) / (1024 * 1024),
                "storage_size_mb": stats.get("storageSize", 0) / (1024 * 1024),
                "collection_size_mb": collection_stats.get("size", 0) / (1024 * 1024),
                "document_count": collection_stats.get("count", 0),
                "avg_doc_size_kb": collection_stats.get("avgObjSize", 0) / 1024,
                "indexes_size_mb": collection_stats.get("totalIndexSize", 0) / (1024 * 1024)
            }
        except Exception as e:
            logger.error(f"❌ 获取统计信息失败: {e}")
            return {}
    
    def check_space_usage(self) -> Dict[str, Any]:
        """检查空间使用情况"""
        stats = self.get_database_stats()
        
        if not stats:
            return {"status": "error", "message": "无法获取统计信息"}
        
        usage_percent = (stats["database_size_mb"] / 512) * 100
        
        return {
            "current_size_mb": stats["database_size_mb"],
            "max_size_mb": 512,
            "usage_percent": usage_percent,
            "available_mb": 512 - stats["database_size_mb"],
            "document_count": stats["document_count"],
            "needs_cleanup": usage_percent > 78,  # 超过78%需要清理
            "critical": usage_percent > 90,
            "stats": stats
        }
    
    def find_old_articles(self, days: int = None) -> List[Dict]:
        """查找需要清理的旧文章"""
        if days is None:
            days = self.RETENTION_DAYS
        
        cutoff_date = datetime.now() - timedelta(days=days)
        
        try:
            # 查找旧文章
            query = {
                "$or": [
                    {"metadata.published_date": {"$lt": cutoff_date.isoformat()}},
                    {"metadata.processed_at": {"$lt": cutoff_date.isoformat()}}
                ]
            }
            
            old_articles = list(self.collection.find(
                query,
                {"_id": 1, "metadata.title": 1, "metadata.published_date": 1}
            ).limit(self.CLEANUP_BATCH_SIZE))
            
            logger.info(f"🔍 找到 {len(old_articles)} 篇超过 {days} 天的文章")
            return old_articles
            
        except Exception as e:
            logger.error(f"❌ 查找旧文章失败: {e}")
            return []
    
    def remove_duplicates(self) -> int:
        """移除重复文章"""
        try:
            logger.info("🔍 查找重复文章...")
            
            # 使用聚合管道查找重复的content_hash
            pipeline = [
                {
                    "$group": {
                        "_id": "$metadata.content_hash",
                        "count": {"$sum": 1},
                        "docs": {"$push": "$_id"}
                    }
                },
                {
                    "$match": {
                        "count": {"$gt": 1}
                    }
                }
            ]
            
            duplicates = list(self.collection.aggregate(pipeline))
            removed_count = 0
            
            for dup in duplicates:
                # 保留第一个，删除其余的
                docs_to_remove = dup["docs"][1:]
                if docs_to_remove:
                    result = self.collection.delete_many({"_id": {"$in": docs_to_remove}})
                    removed_count += result.deleted_count
            
            logger.info(f"🧹 移除了 {removed_count} 个重复文档")
            return removed_count
            
        except Exception as e:
            logger.error(f"❌ 移除重复文章失败: {e}")
            return 0
    
    def cleanup_old_articles(self, days: int = None) -> int:
        """清理旧文章"""
        old_articles = self.find_old_articles(days)
        
        if not old_articles:
            logger.info("✨ 没有需要清理的旧文章")
            return 0
        
        try:
            # 批量删除
            article_ids = [article["_id"] for article in old_articles]
            result = self.collection.delete_many({"_id": {"$in": article_ids}})
            
            logger.info(f"🗑️ 删除了 {result.deleted_count} 篇旧文章")
            return result.deleted_count
            
        except Exception as e:
            logger.error(f"❌ 清理旧文章失败: {e}")
            return 0
    
    def optimize_indexes(self):
        """优化索引"""
        try:
            logger.info("🔧 优化索引...")
            
            # 创建有用的索引
            indexes_to_create = [
                ("metadata.published_date", 1),
                ("metadata.content_hash", 1),
                ("metadata.article_id", 1),
                ("metadata.processed_at", 1)
            ]
            
            for index_spec in indexes_to_create:
                try:
                    self.collection.create_index([index_spec], background=True)
                    logger.info(f"✅ 创建索引: {index_spec}")
                except Exception as e:
                    if "already exists" not in str(e):
                        logger.warning(f"⚠️ 索引创建失败: {e}")
            
        except Exception as e:
            logger.error(f"❌ 索引优化失败: {e}")
    
    def auto_cleanup(self) -> Dict[str, Any]:
        """自动清理策略"""
        logger.info("🚀 开始自动空间管理...")
        
        # 检查当前空间使用
        space_info = self.check_space_usage()
        
        if space_info.get("status") == "error":
            return space_info
        
        cleanup_actions = []
        
        # 1. 总是移除重复项
        removed_duplicates = self.remove_duplicates()
        if removed_duplicates > 0:
            cleanup_actions.append(f"移除重复文档: {removed_duplicates}")
        
        # 2. 如果空间使用超过阈值，清理旧文章
        if space_info["needs_cleanup"]:
            logger.warning(f"⚠️ 空间使用率 {space_info['usage_percent']:.1f}%，开始清理...")
            
            # 根据使用率调整清理策略
            if space_info["critical"]:
                # 危急情况：保留15天
                removed_old = self.cleanup_old_articles(15)
                cleanup_actions.append(f"紧急清理15天前文章: {removed_old}")
            elif space_info["usage_percent"] > 85:
                # 严重情况：保留20天
                removed_old = self.cleanup_old_articles(20)
                cleanup_actions.append(f"清理20天前文章: {removed_old}")
            else:
                # 一般情况：保留30天
                removed_old = self.cleanup_old_articles(30)
                cleanup_actions.append(f"清理30天前文章: {removed_old}")
        
        # 3. 优化索引
        self.optimize_indexes()
        cleanup_actions.append("优化索引")
        
        # 4. 获取清理后的统计信息
        final_stats = self.check_space_usage()
        
        return {
            "status": "success",
            "actions_taken": cleanup_actions,
            "before": space_info,
            "after": final_stats,
            "space_saved_mb": space_info["current_size_mb"] - final_stats["current_size_mb"]
        }
    
    def close(self):
        """关闭连接"""
        if self.client:
            self.client.close()
            logger.info("🔒 MongoDB连接已关闭")


def main():
    """主函数"""
    manager = MongoDBSpaceManager()
    
    try:
        # 执行自动清理
        result = manager.auto_cleanup()
        
        logger.info("📊 清理结果:")
        for key, value in result.items():
            logger.info(f"  {key}: {value}")
        
    except Exception as e:
        logger.error(f"❌ 自动清理失败: {e}")
    
    finally:
        manager.close()


if __name__ == "__main__":
    main()
