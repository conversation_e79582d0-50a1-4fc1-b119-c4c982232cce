#!/usr/bin/env python3
"""
获取当前公网 IP 地址
"""

import requests
import json

def get_public_ip():
    """获取公网 IP 地址"""
    services = [
        'https://api.ipify.org?format=json',
        'https://httpbin.org/ip',
        'https://api.myip.com',
        'https://ipinfo.io/json'
    ]
    
    for service in services:
        try:
            response = requests.get(service, timeout=5)
            data = response.json()
            
            # 不同服务的响应格式不同
            if 'ip' in data:
                return data['ip']
            elif 'origin' in data:
                return data['origin']
            
        except Exception as e:
            print(f"尝试 {service} 失败: {e}")
            continue
    
    return None

def main():
    print("🌐 获取当前公网 IP 地址...")
    
    ip = get_public_ip()
    if ip:
        print(f"✅ 你的公网 IP 地址是: {ip}")
        print(f"\n📋 需要在 Supabase 控制台添加的 IP:")
        print(f"   {ip}/32")
        print(f"\n🔗 Supabase 项目设置链接:")
        print(f"   https://supabase.com/dashboard/project/dopcxawpqiyoakoachob/settings/database")
        print(f"\n💡 操作步骤:")
        print(f"   1. 打开上面的链接")
        print(f"   2. 找到 'Network Restrictions' 部分")
        print(f"   3. 添加 IP 地址: {ip}")
        print(f"   4. 保存设置")
    else:
        print("❌ 无法获取公网 IP 地址")

if __name__ == "__main__":
    main()
