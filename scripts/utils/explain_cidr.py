#!/usr/bin/env python3
"""
解释 CIDR 网络配置
"""

import ipaddress

def explain_cidr(cidr_notation):
    """解释 CIDR 表示法"""
    try:
        network = ipaddress.IPv4Network(cidr_notation, strict=False)
        return {
            'network': str(network),
            'network_address': str(network.network_address),
            'broadcast_address': str(network.broadcast_address),
            'num_addresses': network.num_addresses,
            'description': get_description(cidr_notation)
        }
    except Exception as e:
        return {'error': str(e)}

def get_description(cidr):
    """获取 CIDR 的描述"""
    if cidr == "0.0.0.0/0":
        return "允许所有 IPv4 地址访问（全开放）"
    elif cidr == "0.0.0.0/32":
        return "只允许 0.0.0.0 这个地址（无效配置，0.0.0.0 不是有效的公网 IP）"
    elif cidr.endswith("/32"):
        ip = cidr.split("/")[0]
        return f"只允许单个 IP 地址 {ip} 访问"
    elif cidr.endswith("/24"):
        return "允许一个 C 类网段（256个地址）访问"
    else:
        return "自定义网段"

def main():
    """主函数"""
    print("🌐 CIDR 网络配置解释器")
    print("=" * 50)
    
    # 常见的配置示例
    examples = [
        "0.0.0.0/32",  # 你之前的错误配置
        "0.0.0.0/0",   # 允许所有
        "**************/32",  # 你的具体 IP
        "************/24",    # 你的网段
    ]
    
    for cidr in examples:
        print(f"\n📋 配置: {cidr}")
        result = explain_cidr(cidr)
        
        if 'error' in result:
            print(f"   ❌ 错误: {result['error']}")
        else:
            print(f"   📝 描述: {result['description']}")
            print(f"   🌐 网络: {result['network']}")
            print(f"   📊 地址数量: {result['num_addresses']:,}")
            
            if cidr == "0.0.0.0/32":
                print("   ⚠️  警告: 这个配置无效！0.0.0.0 不是有效的公网 IP")
            elif cidr == "0.0.0.0/0":
                print("   ⚠️  警告: 这个配置允许任何人访问，存在安全风险")
            elif cidr == "**************/32":
                print("   ✅ 推荐: 这是最安全的配置，只允许你的 IP 访问")
    
    print(f"\n" + "=" * 50)
    print("💡 建议:")
    print("   1. 将 Supabase 中的 '0.0.0.0/32' 删除")
    print("   2. 添加 '**************/32' （最安全）")
    print("   3. 或者临时使用 '0.0.0.0/0' （测试用，不安全）")

if __name__ == "__main__":
    main()
