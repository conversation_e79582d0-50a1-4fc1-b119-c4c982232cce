#!/usr/bin/env python3
"""
检查各个数据库的实际可访问情况
"""

import os
import psycopg2
from dotenv import load_dotenv

load_dotenv()

def check_database_access(name, connection_string):
    """检查数据库访问权限和内容"""
    if not connection_string:
        print(f"❌ {name}: 连接字符串未配置")
        return
    
    try:
        print(f"\n🔍 检查 {name}:")
        conn = psycopg2.connect(connection_string, connect_timeout=10)
        cursor = conn.cursor()
        
        # 基本信息
        cursor.execute("SELECT current_database(), current_user, version();")
        db_name, user, version = cursor.fetchone()
        print(f"   📊 数据库: {db_name}")
        print(f"   👤 用户: {user}")
        print(f"   🔧 版本: {version.split(',')[0]}")
        
        # 检查可访问的数据库列表
        cursor.execute("""
            SELECT datname FROM pg_database 
            WHERE datallowconn = true 
            AND datname NOT IN ('template0', 'template1')
            ORDER BY datname;
        """)
        databases = cursor.fetchall()
        print(f"   🗄️  可见数据库数量: {len(databases)}")
        
        if len(databases) <= 10:
            print(f"   📋 数据库列表: {[db[0] for db in databases]}")
        else:
            print(f"   📋 前10个数据库: {[db[0] for db in databases[:10]]}")
            print(f"   ⚠️  数据库太多，只显示前10个")
        
        # 检查当前数据库的表
        cursor.execute("""
            SELECT schemaname, tablename 
            FROM pg_tables 
            WHERE schemaname NOT IN ('information_schema', 'pg_catalog')
            ORDER BY schemaname, tablename
            LIMIT 20;
        """)
        tables = cursor.fetchall()
        print(f"   📋 用户表数量: {len(tables)}")
        if tables:
            print(f"   🏷️  示例表: {tables[:5]}")
        
        # 检查权限
        cursor.execute("""
            SELECT 
                has_database_privilege(current_user, current_database(), 'CREATE') as can_create,
                has_database_privilege(current_user, current_database(), 'CONNECT') as can_connect,
                has_database_privilege(current_user, current_database(), 'TEMPORARY') as can_temp;
        """)
        perms = cursor.fetchone()
        print(f"   🔐 权限: CREATE={perms[0]}, CONNECT={perms[1]}, TEMP={perms[2]}")
        
        cursor.close()
        conn.close()
        print(f"   ✅ {name} 连接正常")
        
    except Exception as e:
        print(f"   ❌ {name} 连接失败: {str(e)[:100]}...")

def main():
    """主函数"""
    print("🗄️  数据库访问权限检查")
    print("=" * 60)
    
    # 检查所有配置的数据库
    databases = {
        "Heroku PostgreSQL": os.getenv('DATABASE_URL'),
        "Supabase (Session Pooler)": os.getenv('SUPABASE_DB_URL_SESSION'),
        "Supabase (Transaction Pooler)": os.getenv('SUPABASE_DB_URL_POOLER'),
        "Supabase (Direct)": os.getenv('SUPABASE_DB_URL_DIRECT'),
        "Neon": os.getenv('NEON_DATABASE_URL')
    }
    
    for name, conn_str in databases.items():
        check_database_access(name, conn_str)
    
    print(f"\n" + "=" * 60)
    print("💡 建议:")
    print("   1. 如果 Supabase 和 Neon 都正常工作，可以考虑取消 Heroku PostgreSQL")
    print("   2. Navicat 中看到很多数据库是正常的，你只能访问自己的")
    print("   3. 建议选择一个主要数据库，避免数据分散")
    print("   4. Supabase 提供 REST API，更适合现代应用开发")

if __name__ == "__main__":
    main()
