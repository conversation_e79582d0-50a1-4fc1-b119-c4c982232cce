#!/usr/bin/env python3
"""
MongoDB Atlas集成的GraphRAG系统
支持从MongoDB Atlas读取文章数据并构建知识图谱
"""

import os
import sqlite3
import re
import argparse
import time
from datetime import datetime
from typing import List, Dict, Any, Optional
import pymongo
from pymongo import MongoClient
from bson import ObjectId
import json

class MongoDBGraphRAG:
    def __init__(self, mongo_uri: str = None, db_name: str = "cauldron_rss", collection_name: str = "articles"):
        """
        初始化MongoDB GraphRAG系统
        
        Args:
            mongo_uri: MongoDB连接字符串
            db_name: 数据库名称
            collection_name: 集合名称
        """
        self.db_path = "mongodb_graphrag.db"
        self.mongo_uri = mongo_uri
        self.db_name = db_name
        self.collection_name = collection_name
        
        # 初始化本地SQLite数据库
        self.init_local_db()
        
        # 连接MongoDB Atlas
        if mongo_uri:
            self.connect_mongodb()
        else:
            print("警告: 未提供MongoDB连接字符串，仅使用本地功能")
            self.mongo_client = None
            self.mongo_db = None
            self.mongo_collection = None
    
    def connect_mongodb(self):
        """连接到MongoDB Atlas"""
        try:
            self.mongo_client = MongoClient(self.mongo_uri)
            self.mongo_db = self.mongo_client[self.db_name]
            self.mongo_collection = self.mongo_db[self.collection_name]
            
            # 测试连接
            self.mongo_client.admin.command('ping')
            print(f"✅ 成功连接到MongoDB Atlas: {self.db_name}.{self.collection_name}")
            
            # 获取集合统计信息
            count = self.mongo_collection.count_documents({})
            print(f"📊 MongoDB集合中共有 {count:,} 篇文章")
            
        except Exception as e:
            print(f"❌ MongoDB连接失败: {e}")
            self.mongo_client = None
            self.mongo_db = None
            self.mongo_collection = None
    
    def init_local_db(self):
        """初始化本地SQLite数据库"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # 创建文档表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS documents (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                mongo_id TEXT UNIQUE,
                article_id TEXT,
                title TEXT,
                content TEXT,
                published_time TEXT,
                processed BOOLEAN DEFAULT FALSE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # 创建实体表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS entities (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT UNIQUE,
                type TEXT,
                description TEXT,
                confidence REAL DEFAULT 0.8,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # 创建关系表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS relations (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                source TEXT,
                target TEXT,
                type TEXT,
                description TEXT,
                confidence REAL DEFAULT 0.8,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # 创建同步状态表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS sync_status (
                id INTEGER PRIMARY KEY,
                last_sync_time TIMESTAMP,
                total_synced INTEGER DEFAULT 0,
                last_mongo_id TEXT
            )
        ''')
        
        # 创建索引
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_documents_mongo_id ON documents(mongo_id)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_documents_article_id ON documents(article_id)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_entities_name ON entities(name)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_relations_source ON relations(source)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_relations_target ON relations(target)')
        
        conn.commit()
        conn.close()
        print("✅ 本地SQLite数据库初始化完成")
    
    def extract_entities_and_relations(self, text: str) -> tuple:
        """
        从文本中提取实体和关系（简化版本）
        在实际应用中，这里应该使用更高级的NLP模型
        """
        # 简单的实体提取（基于关键词和模式）
        entities = []
        relations = []
        
        # 金融相关实体模式
        patterns = {
            'COMPANY': r'([A-Z][a-zA-Z\u4e00-\u9fff]{2,20}(?:公司|集团|股份|有限|科技|金融|银行|保险|基金|证券))',
            'PERSON': r'([A-Z][a-z]+\s+[A-Z][a-z]+|[\u4e00-\u9fff]{2,4}(?:先生|女士|总裁|董事长|CEO|CFO))',
            'MONEY': r'(\d+(?:\.\d+)?(?:万|亿|千万|百万)?(?:元|美元|港元|欧元))',
            'PERCENT': r'(\d+(?:\.\d+)?%)',
            'DATE': r'(\d{4}年\d{1,2}月\d{1,2}日|\d{4}-\d{1,2}-\d{1,2})',
            'CONCEPT': r'(人工智能|机器学习|深度学习|区块链|数字货币|股票|债券|基金|投资|融资|IPO|并购)'
        }
        
        entity_id = 0
        for entity_type, pattern in patterns.items():
            matches = re.findall(pattern, text)
            for match in matches:
                if isinstance(match, tuple):
                    match = match[0]
                if len(match.strip()) > 1:
                    entities.append({
                        'name': match.strip(),
                        'type': entity_type,
                        'description': f'{entity_type}实体: {match.strip()}',
                        'confidence': 0.7
                    })
                    entity_id += 1
        
        # 简单的关系提取
        if len(entities) >= 2:
            for i in range(min(3, len(entities)-1)):
                relations.append({
                    'source': entities[i]['name'],
                    'target': entities[i+1]['name'],
                    'type': 'RELATED_TO',
                    'description': f'{entities[i]["name"]} 与 {entities[i+1]["name"]} 相关',
                    'confidence': 0.6
                })
        
        return entities, relations
    
    def add_document_from_mongo(self, mongo_doc: Dict[str, Any]) -> int:
        """从MongoDB文档添加到本地GraphRAG"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        try:
            # 检查文档是否已存在
            cursor.execute("SELECT id FROM documents WHERE mongo_id = ?", (str(mongo_doc['_id']),))
            existing = cursor.fetchone()
            
            if existing:
                print(f"文档已存在，跳过: {mongo_doc.get('title', 'Unknown')[:50]}")
                return existing[0]
            
            # 插入文档
            cursor.execute('''
                INSERT INTO documents (mongo_id, article_id, title, content, published_time, processed)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', (
                str(mongo_doc['_id']),
                mongo_doc.get('article_id', ''),
                mongo_doc.get('title', ''),
                mongo_doc.get('content', ''),
                mongo_doc.get('published_time', ''),
                False
            ))
            
            doc_id = cursor.lastrowid
            
            # 提取实体和关系
            content = mongo_doc.get('content', '') + ' ' + mongo_doc.get('title', '')
            entities, relations = self.extract_entities_and_relations(content)
            
            # 插入实体
            for entity in entities:
                cursor.execute('''
                    INSERT OR IGNORE INTO entities (name, type, description, confidence)
                    VALUES (?, ?, ?, ?)
                ''', (entity['name'], entity['type'], entity['description'], entity['confidence']))
            
            # 插入关系
            for relation in relations:
                cursor.execute('''
                    INSERT INTO relations (source, target, type, description, confidence)
                    VALUES (?, ?, ?, ?, ?)
                ''', (relation['source'], relation['target'], relation['type'], 
                     relation['description'], relation['confidence']))
            
            # 标记文档为已处理
            cursor.execute("UPDATE documents SET processed = TRUE WHERE id = ?", (doc_id,))
            
            conn.commit()
            return doc_id
            
        except Exception as e:
            print(f"处理文档时出错: {e}")
            conn.rollback()
            return None
        finally:
            conn.close()
    
    def sync_from_mongodb(self, limit: int = 100, skip: int = 0) -> Dict[str, int]:
        """从MongoDB同步文章到本地GraphRAG"""
        if not self.mongo_collection:
            print("❌ MongoDB未连接，无法同步")
            return {'synced': 0, 'errors': 0}
        
        print(f"🔄 开始同步MongoDB数据 (limit={limit}, skip={skip})")
        
        synced_count = 0
        error_count = 0
        start_time = time.time()
        
        try:
            # 获取文档
            cursor = self.mongo_collection.find({}).skip(skip).limit(limit)
            
            for i, doc in enumerate(cursor):
                try:
                    doc_id = self.add_document_from_mongo(doc)
                    if doc_id:
                        synced_count += 1
                        if synced_count % 10 == 0:
                            elapsed = time.time() - start_time
                            rate = synced_count / elapsed if elapsed > 0 else 0
                            print(f"📈 已同步 {synced_count} 篇文章 (速度: {rate:.1f} 篇/秒)")
                    else:
                        error_count += 1
                        
                except Exception as e:
                    print(f"处理文档 {i+1} 时出错: {e}")
                    error_count += 1
                    continue
            
            # 更新同步状态
            self.update_sync_status(synced_count)
            
            elapsed = time.time() - start_time
            print(f"✅ 同步完成: {synced_count} 篇成功, {error_count} 篇失败, 耗时 {elapsed:.1f} 秒")
            
            return {'synced': synced_count, 'errors': error_count, 'elapsed': elapsed}
            
        except Exception as e:
            print(f"❌ 同步过程中出错: {e}")
            return {'synced': synced_count, 'errors': error_count + 1}
    
    def update_sync_status(self, synced_count: int):
        """更新同步状态"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute('''
            INSERT OR REPLACE INTO sync_status (id, last_sync_time, total_synced)
            VALUES (1, ?, COALESCE((SELECT total_synced FROM sync_status WHERE id = 1), 0) + ?)
        ''', (datetime.now().isoformat(), synced_count))

        conn.commit()
        conn.close()

    def search_documents(self, query: str, top_k: int = 5) -> List[Dict]:
        """搜索相关文档"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        # 简单的关键词搜索
        search_terms = query.lower().split()
        conditions = []
        params = []

        for term in search_terms:
            conditions.append("(LOWER(title) LIKE ? OR LOWER(content) LIKE ?)")
            params.extend([f'%{term}%', f'%{term}%'])

        where_clause = " AND ".join(conditions) if conditions else "1=1"

        cursor.execute(f'''
            SELECT id, title, content, published_time
            FROM documents
            WHERE {where_clause}
            ORDER BY id DESC
            LIMIT ?
        ''', params + [top_k])

        results = []
        for row in cursor.fetchall():
            results.append({
                'id': row[0],
                'title': row[1],
                'content': row[2][:500] + '...' if len(row[2]) > 500 else row[2],
                'published_time': row[3]
            })

        conn.close()
        return results

    def search_entities(self, query: str, top_k: int = 5) -> List[Dict]:
        """搜索相关实体"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute('''
            SELECT name, type, description, confidence
            FROM entities
            WHERE LOWER(name) LIKE ? OR LOWER(description) LIKE ?
            ORDER BY confidence DESC
            LIMIT ?
        ''', (f'%{query.lower()}%', f'%{query.lower()}%', top_k))

        results = []
        for row in cursor.fetchall():
            results.append({
                'name': row[0],
                'type': row[1],
                'description': row[2],
                'confidence': row[3]
            })

        conn.close()
        return results

    def get_entity_relations(self, entity_name: str) -> List[Dict]:
        """获取实体的关系"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute('''
            SELECT source, target, type, description, confidence
            FROM relations
            WHERE source = ? OR target = ?
            ORDER BY confidence DESC
            LIMIT 10
        ''', (entity_name, entity_name))

        results = []
        for row in cursor.fetchall():
            results.append({
                'source': row[0],
                'target': row[1],
                'type': row[2],
                'description': row[3],
                'confidence': row[4]
            })

        conn.close()
        return results

    def query(self, question: str) -> str:
        """回答问题"""
        print(f"🤔 正在处理问题: {question}")

        # 搜索相关文档
        relevant_docs = self.search_documents(question, top_k=3)

        # 搜索相关实体
        relevant_entities = self.search_entities(question, top_k=3)

        # 构建上下文
        context_parts = []

        # 添加文档上下文
        if relevant_docs:
            context_parts.append("📄 相关文档：")
            for doc in relevant_docs:
                context_parts.append(f"- {doc['title']}: {doc['content'][:200]}...")

        # 添加实体上下文
        if relevant_entities:
            context_parts.append("\n🏷️ 相关实体：")
            for entity in relevant_entities:
                context_parts.append(f"- {entity['name']} ({entity['type']}): {entity['description']}")

                # 添加实体关系
                relations = self.get_entity_relations(entity['name'])
                if relations:
                    for rel in relations[:2]:  # 只显示前2个关系
                        context_parts.append(f"  🔗 关系: {rel['source']} -> {rel['type']} -> {rel['target']}")

        context = "\n".join(context_parts)

        # 生成回答
        if not context_parts:
            return "抱歉，我在知识库中没有找到与您问题相关的信息。请尝试使用不同的关键词或先同步更多数据。"

        response = f"基于知识图谱中的信息，我找到了以下相关内容：\n\n{context}\n\n这些信息可以帮助回答您的问题。如需更详细的信息，请提供更具体的问题。"

        return response

    def get_stats(self) -> Dict[str, Any]:
        """获取系统统计信息"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        # 获取基本统计
        cursor.execute("SELECT COUNT(*) FROM documents")
        doc_count = cursor.fetchone()[0]

        cursor.execute("SELECT COUNT(*) FROM entities")
        entity_count = cursor.fetchone()[0]

        cursor.execute("SELECT COUNT(*) FROM relations")
        relation_count = cursor.fetchone()[0]

        # 获取同步状态
        cursor.execute("SELECT last_sync_time, total_synced FROM sync_status WHERE id = 1")
        sync_info = cursor.fetchone()

        # 获取MongoDB统计
        mongo_count = 0
        if self.mongo_collection:
            try:
                mongo_count = self.mongo_collection.count_documents({})
            except:
                mongo_count = 0

        conn.close()

        stats = {
            'local_documents': doc_count,
            'entities': entity_count,
            'relations': relation_count,
            'mongo_documents': mongo_count,
            'last_sync': sync_info[0] if sync_info else None,
            'total_synced': sync_info[1] if sync_info else 0,
            'sync_progress': f"{doc_count}/{mongo_count}" if mongo_count > 0 else "N/A"
        }

        return stats

    def show_stats(self):
        """显示统计信息"""
        stats = self.get_stats()

        print(f"\n=== 📊 MongoDB GraphRAG 统计信息 ===")
        print(f"本地文档数量: {stats['local_documents']:,}")
        print(f"实体数量: {stats['entities']:,}")
        print(f"关系数量: {stats['relations']:,}")
        print(f"MongoDB文档数量: {stats['mongo_documents']:,}")
        print(f"同步进度: {stats['sync_progress']}")
        print(f"最后同步时间: {stats['last_sync'] or '未同步'}")
        print(f"累计同步数量: {stats['total_synced']:,}")


def main():
    parser = argparse.ArgumentParser(description="MongoDB Atlas集成的GraphRAG系统")
    parser.add_argument("--action", choices=["sync", "query", "stats"], required=True, help="操作类型")
    parser.add_argument("--mongo-uri", help="MongoDB连接字符串")
    parser.add_argument("--limit", type=int, default=100, help="同步文档数量限制")
    parser.add_argument("--skip", type=int, default=0, help="跳过的文档数量")
    parser.add_argument("--question", help="问题（query模式）")

    args = parser.parse_args()

    # 初始化GraphRAG
    graphrag = MongoDBGraphRAG(mongo_uri=args.mongo_uri)

    if args.action == "sync":
        if not args.mongo_uri:
            print("❌ 同步操作需要提供 --mongo-uri")
            return

        result = graphrag.sync_from_mongodb(limit=args.limit, skip=args.skip)
        print(f"\n📊 同步结果: {result}")

    elif args.action == "query":
        if not args.question:
            print("❌ 查询需要提供 --question")
            return

        answer = graphrag.query(args.question)
        print(f"\n❓ 问题: {args.question}")
        print(f"💡 回答: {answer}")

    elif args.action == "stats":
        graphrag.show_stats()


if __name__ == "__main__":
    main()
