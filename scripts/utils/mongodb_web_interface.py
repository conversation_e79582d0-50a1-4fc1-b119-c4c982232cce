#!/usr/bin/env python3
"""
MongoDB GraphRAG Web界面
支持MongoDB Atlas数据同步和知识图谱查询
"""

from flask import Flask, render_template, request, jsonify
import os
import sys
import threading
import time
from mongodb_graphrag import MongoDBGraphRAG

app = Flask(__name__)

# 全局GraphRAG实例
graphrag = None
sync_status = {
    'is_syncing': False,
    'progress': 0,
    'total': 0,
    'current_batch': 0,
    'message': ''
}

def init_graphrag(mongo_uri=None):
    """初始化GraphRAG实例"""
    global graphrag
    graphrag = MongoDBGraphRAG(mongo_uri=mongo_uri)
    return graphrag

@app.route('/')
def index():
    return render_template('mongodb_index.html')

@app.route('/api/init', methods=['POST'])
def init_connection():
    """初始化MongoDB连接"""
    try:
        data = request.json
        mongo_uri = data.get('mongo_uri', '')
        
        if not mongo_uri:
            return jsonify({'error': 'MongoDB连接字符串不能为空'}), 400
        
        global graphrag
        graphrag = MongoDBGraphRAG(mongo_uri=mongo_uri)
        
        if graphrag.mongo_collection:
            return jsonify({'success': True, 'message': 'MongoDB连接成功'})
        else:
            return jsonify({'error': 'MongoDB连接失败'}), 500
            
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/sync', methods=['POST'])
def start_sync():
    """开始同步MongoDB数据"""
    global sync_status
    
    if sync_status['is_syncing']:
        return jsonify({'error': '同步正在进行中'}), 400
    
    if not graphrag or not graphrag.mongo_collection:
        return jsonify({'error': '请先连接MongoDB'}), 400
    
    try:
        data = request.json
        batch_size = data.get('batch_size', 100)
        max_documents = data.get('max_documents', 1000)
        
        # 启动后台同步任务
        sync_thread = threading.Thread(
            target=background_sync,
            args=(batch_size, max_documents)
        )
        sync_thread.daemon = True
        sync_thread.start()
        
        return jsonify({'success': True, 'message': '同步任务已启动'})
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

def background_sync(batch_size: int, max_documents: int):
    """后台同步任务"""
    global sync_status
    
    sync_status['is_syncing'] = True
    sync_status['progress'] = 0
    sync_status['total'] = max_documents
    sync_status['current_batch'] = 0
    sync_status['message'] = '开始同步...'
    
    try:
        total_synced = 0
        skip = 0
        
        while total_synced < max_documents:
            remaining = max_documents - total_synced
            current_batch_size = min(batch_size, remaining)
            
            sync_status['current_batch'] += 1
            sync_status['message'] = f'正在同步第 {sync_status["current_batch"]} 批数据...'
            
            # 执行同步
            result = graphrag.sync_from_mongodb(limit=current_batch_size, skip=skip)
            
            synced_count = result.get('synced', 0)
            total_synced += synced_count
            skip += current_batch_size
            
            # 更新进度
            sync_status['progress'] = total_synced
            sync_status['message'] = f'已同步 {total_synced} / {max_documents} 篇文章'
            
            # 如果这批没有同步到任何文档，可能已经到达末尾
            if synced_count == 0:
                break
            
            # 短暂休息，避免过度占用资源
            time.sleep(0.1)
        
        sync_status['message'] = f'同步完成！共同步 {total_synced} 篇文章'
        
    except Exception as e:
        sync_status['message'] = f'同步出错: {str(e)}'
    
    finally:
        sync_status['is_syncing'] = False

@app.route('/api/sync_status')
def get_sync_status():
    """获取同步状态"""
    return jsonify(sync_status)

@app.route('/api/query', methods=['POST'])
def query():
    """查询知识图谱"""
    try:
        if not graphrag:
            return jsonify({'error': '请先初始化系统'}), 400
        
        data = request.json
        question = data.get('question', '')
        
        if not question:
            return jsonify({'error': '问题不能为空'}), 400
        
        answer = graphrag.query(question)
        return jsonify({'success': True, 'answer': answer})
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/stats')
def stats():
    """获取统计信息"""
    try:
        if not graphrag:
            return jsonify({'error': '请先初始化系统'}), 400
        
        stats = graphrag.get_stats()
        return jsonify(stats)
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/search_documents', methods=['POST'])
def search_documents():
    """搜索文档"""
    try:
        if not graphrag:
            return jsonify({'error': '请先初始化系统'}), 400
        
        data = request.json
        query = data.get('query', '')
        limit = data.get('limit', 10)
        
        if not query:
            return jsonify({'error': '搜索关键词不能为空'}), 400
        
        results = graphrag.search_documents(query, top_k=limit)
        return jsonify({'success': True, 'results': results})
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/search_entities', methods=['POST'])
def search_entities():
    """搜索实体"""
    try:
        if not graphrag:
            return jsonify({'error': '请先初始化系统'}), 400
        
        data = request.json
        query = data.get('query', '')
        limit = data.get('limit', 10)
        
        if not query:
            return jsonify({'error': '搜索关键词不能为空'}), 400
        
        results = graphrag.search_entities(query, top_k=limit)
        return jsonify({'success': True, 'results': results})
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

if __name__ == '__main__':
    # 创建templates目录
    os.makedirs('templates', exist_ok=True)
    
    # 创建HTML模板
    html_template = '''
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MongoDB GraphRAG 系统</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f7fa;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            margin-bottom: 25px;
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
            font-size: 2.5em;
        }
        h2 {
            color: #34495e;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
            color: #555;
        }
        input[type="text"], input[type="number"], textarea {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 14px;
            box-sizing: border-box;
            transition: border-color 0.3s;
        }
        input[type="text"]:focus, input[type="number"]:focus, textarea:focus {
            border-color: #3498db;
            outline: none;
        }
        button {
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin-right: 10px;
            transition: all 0.3s;
        }
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(52, 152, 219, 0.3);
        }
        button:disabled {
            background: #bdc3c7;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .stat-item {
            text-align: center;
            padding: 25px;
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            border-radius: 12px;
            border: 1px solid #dee2e6;
        }
        .stat-number {
            font-size: 2.5em;
            font-weight: bold;
            color: #3498db;
            margin-bottom: 5px;
        }
        .stat-label {
            color: #666;
            font-size: 0.9em;
        }
        .progress-container {
            background: #ecf0f1;
            border-radius: 10px;
            padding: 3px;
            margin: 15px 0;
        }
        .progress-bar {
            background: linear-gradient(90deg, #3498db, #2ecc71);
            height: 20px;
            border-radius: 8px;
            transition: width 0.3s ease;
            position: relative;
        }
        .progress-text {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: white;
            font-size: 12px;
            font-weight: bold;
        }
        .result {
            margin-top: 20px;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #3498db;
            background-color: #f8f9fa;
        }
        .error {
            border-left-color: #e74c3c;
            background-color: #fdf2f2;
            color: #c0392b;
        }
        .success {
            border-left-color: #27ae60;
            background-color: #f0f9f4;
            color: #1e8449;
        }
        .loading {
            display: none;
            text-align: center;
            color: #3498db;
            font-style: italic;
        }
        .tabs {
            display: flex;
            margin-bottom: 20px;
            border-bottom: 2px solid #ecf0f1;
        }
        .tab {
            padding: 12px 24px;
            cursor: pointer;
            border-bottom: 3px solid transparent;
            transition: all 0.3s;
        }
        .tab.active {
            border-bottom-color: #3498db;
            color: #3498db;
            font-weight: bold;
        }
        .tab-content {
            display: none;
        }
        .tab-content.active {
            display: block;
        }
        .search-results {
            max-height: 400px;
            overflow-y: auto;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            margin-top: 15px;
        }
        .search-item {
            padding: 10px;
            border-bottom: 1px solid #eee;
            margin-bottom: 10px;
        }
        .search-item:last-child {
            border-bottom: none;
        }
        .search-title {
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 5px;
        }
        .search-content {
            color: #666;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <h1>🧠 MongoDB GraphRAG 智能知识图谱系统</h1>
    
    <!-- MongoDB连接 -->
    <div class="container">
        <h2>🔗 MongoDB Atlas 连接</h2>
        <div class="form-group">
            <label for="mongo-uri">MongoDB连接字符串：</label>
            <input type="text" id="mongo-uri" placeholder="mongodb+srv://username:<EMAIL>/database">
        </div>
        <button onclick="connectMongoDB()">连接MongoDB</button>
        <div id="connection-result"></div>
    </div>

    <!-- 系统统计 -->
    <div class="container">
        <h2>📊 系统统计</h2>
        <div class="stats" id="stats">
            <div class="stat-item">
                <div class="stat-number" id="local-docs">-</div>
                <div class="stat-label">本地文档</div>
            </div>
            <div class="stat-item">
                <div class="stat-number" id="mongo-docs">-</div>
                <div class="stat-label">MongoDB文档</div>
            </div>
            <div class="stat-item">
                <div class="stat-number" id="entities">-</div>
                <div class="stat-label">实体数量</div>
            </div>
            <div class="stat-item">
                <div class="stat-number" id="relations">-</div>
                <div class="stat-label">关系数量</div>
            </div>
            <div class="stat-item">
                <div class="stat-number" id="sync-progress">-</div>
                <div class="stat-label">同步进度</div>
            </div>
        </div>
        <button onclick="loadStats()">刷新统计</button>
    </div>

    <!-- 数据同步 -->
    <div class="container">
        <h2>🔄 数据同步</h2>
        <div class="form-group">
            <label for="batch-size">批次大小：</label>
            <input type="number" id="batch-size" value="100" min="10" max="1000">
        </div>
        <div class="form-group">
            <label for="max-docs">最大文档数：</label>
            <input type="number" id="max-docs" value="1000" min="100" max="50000">
        </div>
        <button onclick="startSync()" id="sync-btn">开始同步</button>
        
        <div class="progress-container" id="progress-container" style="display: none;">
            <div class="progress-bar" id="progress-bar" style="width: 0%;">
                <div class="progress-text" id="progress-text">0%</div>
            </div>
        </div>
        <div id="sync-result"></div>
    </div>

    <!-- 功能选项卡 -->
    <div class="container">
        <div class="tabs">
            <div class="tab active" onclick="switchTab('query')">💬 智能问答</div>
            <div class="tab" onclick="switchTab('search-docs')">📄 文档搜索</div>
            <div class="tab" onclick="switchTab('search-entities')">🏷️ 实体搜索</div>
        </div>

        <!-- 智能问答 -->
        <div class="tab-content active" id="query-content">
            <h2>💬 智能问答</h2>
            <div class="form-group">
                <label for="question">问题：</label>
                <input type="text" id="question" placeholder="请输入您的问题，例如：什么是人工智能？">
            </div>
            <button onclick="askQuestion()">提问</button>
            <div class="loading" id="query-loading">🤔 正在思考中...</div>
            <div id="query-result"></div>
        </div>

        <!-- 文档搜索 -->
        <div class="tab-content" id="search-docs-content">
            <h2>📄 文档搜索</h2>
            <div class="form-group">
                <label for="doc-query">搜索关键词：</label>
                <input type="text" id="doc-query" placeholder="请输入搜索关键词">
            </div>
            <button onclick="searchDocuments()">搜索文档</button>
            <div class="loading" id="doc-loading">🔍 正在搜索...</div>
            <div id="doc-results"></div>
        </div>

        <!-- 实体搜索 -->
        <div class="tab-content" id="search-entities-content">
            <h2>🏷️ 实体搜索</h2>
            <div class="form-group">
                <label for="entity-query">搜索关键词：</label>
                <input type="text" id="entity-query" placeholder="请输入实体名称或关键词">
            </div>
            <button onclick="searchEntities()">搜索实体</button>
            <div class="loading" id="entity-loading">🔍 正在搜索...</div>
            <div id="entity-results"></div>
        </div>
    </div>

    <script>
        let syncInterval = null;

        // 切换选项卡
        function switchTab(tabName) {
            // 隐藏所有选项卡内容
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.remove('active');
            });
            document.querySelectorAll('.tab').forEach(tab => {
                tab.classList.remove('active');
            });

            // 显示选中的选项卡
            document.getElementById(tabName + '-content').classList.add('active');
            event.target.classList.add('active');
        }

        // 连接MongoDB
        function connectMongoDB() {
            const mongoUri = document.getElementById('mongo-uri').value;
            
            if (!mongoUri) {
                showResult('connection-result', '请输入MongoDB连接字符串', 'error');
                return;
            }

            fetch('/api/init', {
                method: 'POST',
                headers: {'Content-Type': 'application/json'},
                body: JSON.stringify({mongo_uri: mongoUri})
            })
            .then(response => response.json())
            .then(data => {
                if (data.error) {
                    showResult('connection-result', '连接失败: ' + data.error, 'error');
                } else {
                    showResult('connection-result', '✅ ' + data.message, 'success');
                    loadStats();
                }
            })
            .catch(error => {
                showResult('connection-result', '网络错误: ' + error, 'error');
            });
        }

        // 加载统计信息
        function loadStats() {
            fetch('/api/stats')
                .then(response => response.json())
                .then(data => {
                    if (data.error) {
                        console.error('Error:', data.error);
                        return;
                    }
                    document.getElementById('local-docs').textContent = data.local_documents?.toLocaleString() || '-';
                    document.getElementById('mongo-docs').textContent = data.mongo_documents?.toLocaleString() || '-';
                    document.getElementById('entities').textContent = data.entities?.toLocaleString() || '-';
                    document.getElementById('relations').textContent = data.relations?.toLocaleString() || '-';
                    document.getElementById('sync-progress').textContent = data.sync_progress || '-';
                })
                .catch(error => {
                    console.error('Error:', error);
                });
        }

        // 开始同步
        function startSync() {
            const batchSize = parseInt(document.getElementById('batch-size').value);
            const maxDocs = parseInt(document.getElementById('max-docs').value);
            
            fetch('/api/sync', {
                method: 'POST',
                headers: {'Content-Type': 'application/json'},
                body: JSON.stringify({
                    batch_size: batchSize,
                    max_documents: maxDocs
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.error) {
                    showResult('sync-result', '同步失败: ' + data.error, 'error');
                } else {
                    showResult('sync-result', '✅ ' + data.message, 'success');
                    document.getElementById('progress-container').style.display = 'block';
                    document.getElementById('sync-btn').disabled = true;
                    
                    // 开始监控同步进度
                    syncInterval = setInterval(checkSyncStatus, 1000);
                }
            })
            .catch(error => {
                showResult('sync-result', '网络错误: ' + error, 'error');
            });
        }

        // 检查同步状态
        function checkSyncStatus() {
            fetch('/api/sync_status')
                .then(response => response.json())
                .then(data => {
                    const progress = data.total > 0 ? (data.progress / data.total * 100) : 0;
                    document.getElementById('progress-bar').style.width = progress + '%';
                    document.getElementById('progress-text').textContent = Math.round(progress) + '%';
                    
                    showResult('sync-result', data.message, data.is_syncing ? 'result' : 'success');
                    
                    if (!data.is_syncing) {
                        clearInterval(syncInterval);
                        document.getElementById('sync-btn').disabled = false;
                        loadStats();
                    }
                })
                .catch(error => {
                    console.error('Error checking sync status:', error);
                });
        }

        // 提问
        function askQuestion() {
            const question = document.getElementById('question').value;
            
            if (!question) {
                showResult('query-result', '请输入问题', 'error');
                return;
            }

            document.getElementById('query-loading').style.display = 'block';
            document.getElementById('query-result').innerHTML = '';

            fetch('/api/query', {
                method: 'POST',
                headers: {'Content-Type': 'application/json'},
                body: JSON.stringify({question: question})
            })
            .then(response => response.json())
            .then(data => {
                document.getElementById('query-loading').style.display = 'none';
                
                if (data.error) {
                    showResult('query-result', '错误: ' + data.error, 'error');
                } else {
                    showResult('query-result', '<strong>💡 回答:</strong><br><br>' + data.answer.replace(/\\n/g, '<br>'), 'result');
                }
            })
            .catch(error => {
                document.getElementById('query-loading').style.display = 'none';
                showResult('query-result', '网络错误: ' + error, 'error');
            });
        }

        // 搜索文档
        function searchDocuments() {
            const query = document.getElementById('doc-query').value;
            
            if (!query) {
                showResult('doc-results', '请输入搜索关键词', 'error');
                return;
            }

            document.getElementById('doc-loading').style.display = 'block';
            document.getElementById('doc-results').innerHTML = '';

            fetch('/api/search_documents', {
                method: 'POST',
                headers: {'Content-Type': 'application/json'},
                body: JSON.stringify({query: query, limit: 10})
            })
            .then(response => response.json())
            .then(data => {
                document.getElementById('doc-loading').style.display = 'none';
                
                if (data.error) {
                    showResult('doc-results', '错误: ' + data.error, 'error');
                } else {
                    displaySearchResults('doc-results', data.results, 'document');
                }
            })
            .catch(error => {
                document.getElementById('doc-loading').style.display = 'none';
                showResult('doc-results', '网络错误: ' + error, 'error');
            });
        }

        // 搜索实体
        function searchEntities() {
            const query = document.getElementById('entity-query').value;
            
            if (!query) {
                showResult('entity-results', '请输入搜索关键词', 'error');
                return;
            }

            document.getElementById('entity-loading').style.display = 'block';
            document.getElementById('entity-results').innerHTML = '';

            fetch('/api/search_entities', {
                method: 'POST',
                headers: {'Content-Type': 'application/json'},
                body: JSON.stringify({query: query, limit: 10})
            })
            .then(response => response.json())
            .then(data => {
                document.getElementById('entity-loading').style.display = 'none';
                
                if (data.error) {
                    showResult('entity-results', '错误: ' + data.error, 'error');
                } else {
                    displaySearchResults('entity-results', data.results, 'entity');
                }
            })
            .catch(error => {
                document.getElementById('entity-loading').style.display = 'none';
                showResult('entity-results', '网络错误: ' + error, 'error');
            });
        }

        // 显示搜索结果
        function displaySearchResults(elementId, results, type) {
            const element = document.getElementById(elementId);
            
            if (results.length === 0) {
                element.innerHTML = '<div class="result">没有找到相关结果</div>';
                return;
            }

            let html = '<div class="search-results">';
            
            results.forEach(item => {
                if (type === 'document') {
                    html += `
                        <div class="search-item">
                            <div class="search-title">📄 ${item.title}</div>
                            <div class="search-content">${item.content}</div>
                            <small style="color: #999;">发布时间: ${item.published_time || '未知'}</small>
                        </div>
                    `;
                } else if (type === 'entity') {
                    html += `
                        <div class="search-item">
                            <div class="search-title">🏷️ ${item.name} <span style="color: #3498db;">(${item.type})</span></div>
                            <div class="search-content">${item.description}</div>
                            <small style="color: #999;">置信度: ${(item.confidence * 100).toFixed(1)}%</small>
                        </div>
                    `;
                }
            });
            
            html += '</div>';
            element.innerHTML = html;
        }

        // 显示结果
        function showResult(elementId, message, type) {
            const element = document.getElementById(elementId);
            element.innerHTML = message;
            element.className = 'result ' + type;
        }

        // 页面加载时初始化
        window.onload = function() {
            // 可以在这里添加初始化代码
        };
    </script>
</body>
</html>
    '''
    
    with open('templates/mongodb_index.html', 'w', encoding='utf-8') as f:
        f.write(html_template)
    
    print("🚀 MongoDB GraphRAG Web界面已创建，启动服务器...")
    app.run(host='0.0.0.0', port=5001, debug=True)
