#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
七姐妹基本面数据更新脚本
用于定时任务，每日更新基本面数据
"""

import sys
import os
import asyncio
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.data.seven_sisters_db_manager import SevenSistersDBManager
from ib_insync import util

def setup_logging():
    """设置日志"""
    log_dir = project_root / "logs"
    log_dir.mkdir(exist_ok=True)
    
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_dir / "seven_sisters_update.log"),
            logging.StreamHandler()
        ]
    )

async def main():
    """主函数"""
    setup_logging()
    logger = logging.getLogger(__name__)
    
    logger.info("🚀 开始七姐妹基本面数据更新任务")
    
    try:
        manager = SevenSistersDBManager()
        
        # 初始化数据库（如果需要）
        logger.info("🔧 检查数据库结构...")
        manager.init_database()
        
        # 更新数据
        logger.info("📊 开始更新基本面数据...")
        results = await manager.update_all_fundamentals()
        
        # 输出结果
        success_count = len(results['success'])
        error_count = len(results['errors'])
        
        logger.info(f"✅ 数据更新完成!")
        logger.info(f"   成功: {success_count} 只股票")
        logger.info(f"   失败: {error_count} 只股票")
        
        if results['success']:
            logger.info(f"   成功更新: {', '.join(results['success'])}")
        
        if results['errors']:
            logger.warning(f"   失败详情: {results['errors']}")
        
        # 验证数据
        logger.info("🔍 验证更新结果...")
        latest_df = manager.get_latest_fundamentals()
        
        if not latest_df.empty:
            logger.info(f"   数据库中共有 {len(latest_df)} 条最新记录")
            for _, row in latest_df.iterrows():
                logger.info(f"   {row['symbol']}: 价格=${row['close_price']:.2f}, EPS=${row['eps_ttm']:.2f}")
        else:
            logger.warning("   ⚠️ 数据库中没有找到最新数据")
        
        # 返回状态码
        if error_count == 0:
            logger.info("🎉 所有数据更新成功!")
            return 0
        elif success_count > 0:
            logger.warning(f"⚠️ 部分数据更新成功 ({success_count}/{success_count + error_count})")
            return 1
        else:
            logger.error("❌ 所有数据更新失败!")
            return 2
            
    except Exception as e:
        logger.error(f"❌ 更新任务执行失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return 3

if __name__ == "__main__":
    # 设置IB事件循环
    util.patchAsyncio()
    
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except (KeyboardInterrupt, SystemExit):
        print("\n🛑 更新任务被用户中断")
        sys.exit(130)