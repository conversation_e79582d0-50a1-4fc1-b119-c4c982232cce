#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
n8n工作流优化器
基于当前配置提出科学的工作流重构方案
"""

import requests
import json
import os
from dotenv import load_dotenv

load_dotenv()

class N8nWorkflowOptimizer:
    """n8n工作流优化器"""
    
    def __init__(self):
        self.api_key = os.getenv('n8n_token')
        self.base_url = 'https://n8n.git4ta.fun/api/v1'
        self.headers = {
            'X-N8N-API-KEY': self.api_key,
            'Content-Type': 'application/json'
        }
    
    def create_optimized_data_workflow(self):
        """创建优化的数据处理工作流"""
        
        # 新的数据处理工作流配置
        optimized_workflow = {
            "name": "太公心易-数据处理管道",
            "active": True,
            "nodes": [
                {
                    "id": "webhook-trigger",
                    "name": "数据接收器",
                    "type": "n8n-nodes-base.webhook",
                    "position": [100, 100],
                    "parameters": {
                        "path": "taigong-data-pipeline",
                        "httpMethod": "POST",
                        "responseMode": "onReceived",
                        "options": {}
                    }
                },
                {
                    "id": "function-processor",
                    "name": "数据预处理",
                    "type": "n8n-nodes-base.function",
                    "position": [300, 100],
                    "parameters": {
                        "functionCode": """
// 处理太公心易七姐妹数据
const inputData = $json;

// 提取关键信息
const summary = inputData.market_summary || {};
const detailedData = inputData.detailed_data || [];

// 格式化为分析报告
const analysisReport = {
  timestamp: new Date().toISOString(),
  source: '太公心易七仙女雷达',
  market_analysis: {
    top_performer: summary.top_performer,
    total_stocks: detailedData.length,
    key_metrics: detailedData.map(stock => ({
      symbol: stock.symbol,
      name: stock.chinese_name,
      emoji: stock.emoji,
      price: stock.close_price,
      eps: stock.eps_ttm,
      rank: stock.eps_rank,
      score: stock.composite_score
    }))
  },
  analysis_request: inputData.ai_instructions || {}
};

return analysisReport;
"""
                    }
                },
                {
                    "id": "serp-search",
                    "name": "市场信息搜索",
                    "type": "n8n-nodes-base.serpApi",
                    "position": [500, 100],
                    "parameters": {
                        "engine": "google",
                        "query": "={{$json.market_analysis.top_performer.symbol}} stock analysis latest news",
                        "location": "United States",
                        "hl": "en",
                        "gl": "us"
                    }
                },
                {
                    "id": "ai-analyzer",
                    "name": "AI智能分析",
                    "type": "@n8n/n8n-nodes-langchain.agent",
                    "position": [700, 100],
                    "parameters": {
                        "agent": "conversationalRetrievalQAAgent",
                        "prompt": """
你是太公心易的金融分析专家。请基于以下数据进行深度分析：

1. 七姐妹基本面数据：{{$json.market_analysis}}
2. 最新市场信息：{{$('市场信息搜索').item.json}}

请提供：
- 顶级表现者的投资价值分析
- 市场趋势判断
- 风险评估
- 投资建议

分析要专业、客观、有数据支撑。
""",
                        "options": {
                            "systemMessage": "你是专业的金融分析师，擅长基本面分析和市场趋势判断。"
                        }
                    }
                },
                {
                    "id": "mongodb-storage",
                    "name": "数据存储",
                    "type": "n8n-nodes-base.mongoDb",
                    "position": [500, 300],
                    "parameters": {
                        "operation": "insert",
                        "collection": "market_analysis",
                        "fields": "timestamp,source,market_analysis,ai_analysis"
                    }
                },
                {
                    "id": "notion-report",
                    "name": "生成报告",
                    "type": "n8n-nodes-base.notion",
                    "position": [700, 300],
                    "parameters": {
                        "operation": "create",
                        "resource": "page",
                        "databaseId": "your-database-id",
                        "title": "太公心易市场分析 - {{$json.timestamp}}",
                        "properties": {}
                    }
                }
            ],
            "connections": {
                "webhook-trigger": {
                    "main": [
                        [
                            {
                                "node": "function-processor",
                                "type": "main",
                                "index": 0
                            }
                        ]
                    ]
                },
                "function-processor": {
                    "main": [
                        [
                            {
                                "node": "serp-search",
                                "type": "main",
                                "index": 0
                            },
                            {
                                "node": "mongodb-storage",
                                "type": "main",
                                "index": 0
                            }
                        ]
                    ]
                },
                "serp-search": {
                    "main": [
                        [
                            {
                                "node": "ai-analyzer",
                                "type": "main",
                                "index": 0
                            }
                        ]
                    ]
                },
                "ai-analyzer": {
                    "main": [
                        [
                            {
                                "node": "notion-report",
                                "type": "main",
                                "index": 0
                            }
                        ]
                    ]
                }
            }
        }
        
        return optimized_workflow
    
    def create_chat_workflow(self):
        """创建独立的聊天工作流"""
        
        chat_workflow = {
            "name": "太公心易-智能对话",
            "active": True,
            "nodes": [
                {
                    "id": "chat-trigger",
                    "name": "聊天触发器",
                    "type": "n8n-nodes-base.chatTrigger",
                    "position": [100, 100],
                    "parameters": {}
                },
                {
                    "id": "redis-memory",
                    "name": "聊天记忆",
                    "type": "n8n-nodes-langchain.memoryRedisChat",
                    "position": [300, 100],
                    "parameters": {
                        "sessionKey": "taigong_chat_{{$workflow.id}}_{{$execution.id}}",
                        "operation": "get"
                    }
                },
                {
                    "id": "langchain-agent",
                    "name": "AI助手",
                    "type": "@n8n/n8n-nodes-langchain.agent",
                    "position": [500, 100],
                    "parameters": {
                        "agent": "conversationalRetrievalQAAgent",
                        "prompt": """
你是太公心易的AI助手，专门帮助用户分析金融市场和投资决策。

你可以：
1. 解答金融投资问题
2. 分析市场趋势
3. 提供投资建议
4. 解释财务指标

请保持专业、客观、有帮助的态度。
""",
                        "options": {
                            "systemMessage": "你是专业的金融AI助手。"
                        }
                    }
                }
            ],
            "connections": {
                "chat-trigger": {
                    "main": [
                        [
                            {
                                "node": "redis-memory",
                                "type": "main",
                                "index": 0
                            }
                        ]
                    ]
                },
                "redis-memory": {
                    "main": [
                        [
                            {
                                "node": "langchain-agent",
                                "type": "main",
                                "index": 0
                            }
                        ]
                    ]
                }
            }
        }
        
        return chat_workflow
    
    def create_workflow(self, workflow_config):
        """创建新的工作流"""
        try:
            url = f"{self.base_url}/workflows"
            response = requests.post(url, headers=self.headers, json=workflow_config, timeout=30)
            
            if response.status_code in [200, 201]:
                workflow_data = response.json()
                print(f"✅ 工作流创建成功: {workflow_data.get('name')} (ID: {workflow_data.get('id')})")
                return workflow_data
            else:
                print(f"❌ 工作流创建失败: {response.status_code}")
                print(f"错误信息: {response.text}")
                return None
                
        except Exception as e:
            print(f"❌ 创建工作流异常: {e}")
            return None
    
    def optimize_workflows(self):
        """执行工作流优化"""
        print("🚀 开始优化n8n工作流...")
        
        # 1. 创建优化的数据处理工作流
        print("\n📊 创建数据处理管道...")
        data_workflow = self.create_optimized_data_workflow()
        data_result = self.create_workflow(data_workflow)
        
        # 2. 创建独立的聊天工作流
        print("\n💬 创建智能对话工作流...")
        chat_workflow = self.create_chat_workflow()
        chat_result = self.create_workflow(chat_workflow)
        
        # 3. 输出结果
        print("\n🎯 优化结果:")
        if data_result:
            print(f"✅ 数据处理管道: {data_result.get('name')}")
            print(f"   🔗 Webhook: https://n8n.git4ta.fun/webhook/taigong-data-pipeline")
        
        if chat_result:
            print(f"✅ 智能对话系统: {chat_result.get('name')}")
        
        print("\n💡 优化建议:")
        print("1. 使用新的数据处理管道接收太公心易数据")
        print("2. 聊天功能独立运行，不会干扰数据处理")
        print("3. 每个工作流职责单一，更易维护")
        print("4. 可以独立扩展和优化各个功能")
        
        return data_result, chat_result

def main():
    """主函数"""
    optimizer = N8nWorkflowOptimizer()
    
    print("🔧 n8n工作流优化器")
    print("=" * 50)
    
    # 执行优化
    data_workflow, chat_workflow = optimizer.optimize_workflows()
    
    if data_workflow or chat_workflow:
        print("\n🎉 工作流优化完成!")
        print("💡 建议更新.env中的webhook URL为新的数据处理管道")
    else:
        print("\n❌ 工作流优化失败")

if __name__ == "__main__":
    main()