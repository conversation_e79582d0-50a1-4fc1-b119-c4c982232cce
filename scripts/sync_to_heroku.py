#!/usr/bin/env python3
"""
数据库同步脚本 - 将本地SQLite数据同步到Heroku PostgreSQL

使用方法:
1. 设置环境变量 DATABASE_URL 为Heroku PostgreSQL连接字符串
2. 运行: python scripts/sync_to_heroku.py

注意:
- 此脚本会将本地SQLite的所有数据迁移到PostgreSQL
- 如果PostgreSQL中已有数据，会进行合并（避免重复）
- 建议在迁移前备份数据
"""

import os
import sys
import sqlite3
from pathlib import Path
from datetime import datetime

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.core.member_system import MemberSystem, MembershipLevel

def sync_sqlite_to_postgres():
    """同步SQLite数据到PostgreSQL"""
    
    # 检查环境变量
    database_url = os.getenv('DATABASE_URL')
    if not database_url:
        print("❌ 错误: 未设置 DATABASE_URL 环境变量")
        print("请设置Heroku PostgreSQL连接字符串，例如:")
        print("export DATABASE_URL='postgresql://user:password@host:port/database'")
        return False
    
    # 检查本地SQLite文件
    sqlite_path = project_root / "src" / "core" / "data" / "members.db"
    if not sqlite_path.exists():
        print(f"❌ 错误: 本地SQLite文件不存在: {sqlite_path}")
        print("请先在本地运行系统以创建SQLite数据库")
        return False
    
    print("🔄 开始数据库同步...")
    print(f"📁 本地SQLite: {sqlite_path}")
    print(f"🐘 目标PostgreSQL: {database_url[:50]}...")
    
    try:
        # 创建PostgreSQL会员系统实例
        postgres_system = MemberSystem()
        if not postgres_system.use_postgres:
            print("❌ 错误: 无法连接到PostgreSQL，请检查DATABASE_URL")
            return False
        
        # 连接本地SQLite
        sqlite_conn = sqlite3.connect(sqlite_path)
        sqlite_conn.row_factory = sqlite3.Row
        sqlite_cursor = sqlite_conn.cursor()
        
        # 同步会员数据
        print("\n👥 同步会员数据...")
        sqlite_cursor.execute("""
            SELECT username, email, password_hash, membership_level, 
                   created_at, last_login, is_active, subscription_expires
            FROM members
        """)
        
        members_synced = 0
        members_skipped = 0
        
        for row in sqlite_cursor.fetchall():
            # 检查PostgreSQL中是否已存在该用户
            with postgres_system._get_connection() as pg_conn:
                pg_cursor = pg_conn.cursor()
                pg_cursor.execute(
                    "SELECT id FROM members WHERE username = %s OR email = %s",
                    (row['username'], row['email'])
                )
                
                if pg_cursor.fetchone():
                    print(f"⏭️  跳过已存在用户: {row['username']}")
                    members_skipped += 1
                    continue
                
                # 插入新用户
                try:
                    pg_cursor.execute("""
                        INSERT INTO members (username, email, password_hash, membership_level,
                                           created_at, last_login, is_active, subscription_expires)
                        VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
                    """, (
                        row['username'],
                        row['email'], 
                        row['password_hash'],
                        row['membership_level'],
                        datetime.fromisoformat(row['created_at']) if row['created_at'] else None,
                        datetime.fromisoformat(row['last_login']) if row['last_login'] else None,
                        bool(row['is_active']),
                        datetime.fromisoformat(row['subscription_expires']) if row['subscription_expires'] else None
                    ))
                    pg_conn.commit()
                    print(f"✅ 同步用户: {row['username']}")
                    members_synced += 1
                    
                except Exception as e:
                    print(f"❌ 同步用户失败 {row['username']}: {e}")
        
        # 同步报告访问记录
        print("\n📊 同步报告访问记录...")
        sqlite_cursor.execute("""
            SELECT member_id, report_date, access_time, report_type
            FROM report_access
        """)
        
        access_synced = 0
        for row in sqlite_cursor.fetchall():
            with postgres_system._get_connection() as pg_conn:
                pg_cursor = pg_conn.cursor()
                try:
                    # 需要根据用户名找到PostgreSQL中的member_id
                    sqlite_cursor.execute("SELECT username FROM members WHERE id = ?", (row['member_id'],))
                    username_row = sqlite_cursor.fetchone()
                    if not username_row:
                        continue
                    
                    pg_cursor.execute("SELECT id FROM members WHERE username = %s", (username_row['username'],))
                    pg_member = pg_cursor.fetchone()
                    if not pg_member:
                        continue
                    
                    pg_cursor.execute("""
                        INSERT INTO report_access (member_id, report_date, access_time, report_type)
                        VALUES (%s, %s, %s, %s)
                    """, (
                        pg_member['id'],
                        row['report_date'],
                        datetime.fromisoformat(row['access_time']) if row['access_time'] else datetime.now(),
                        row['report_type']
                    ))
                    pg_conn.commit()
                    access_synced += 1
                    
                except Exception as e:
                    print(f"⚠️  同步访问记录失败: {e}")
        
        # 同步日报数据
        print("\n📰 同步日报数据...")
        sqlite_cursor.execute("""
            SELECT report_date, report_data, created_at
            FROM daily_reports
        """)
        
        reports_synced = 0
        for row in sqlite_cursor.fetchall():
            with postgres_system._get_connection() as pg_conn:
                pg_cursor = pg_conn.cursor()
                try:
                    # 检查是否已存在
                    pg_cursor.execute("SELECT id FROM daily_reports WHERE report_date = %s", (row['report_date'],))
                    if pg_cursor.fetchone():
                        continue
                    
                    pg_cursor.execute("""
                        INSERT INTO daily_reports (report_date, report_data, created_at)
                        VALUES (%s, %s, %s)
                    """, (
                        row['report_date'],
                        row['report_data'],
                        datetime.fromisoformat(row['created_at']) if row['created_at'] else datetime.now()
                    ))
                    pg_conn.commit()
                    reports_synced += 1
                    
                except Exception as e:
                    print(f"⚠️  同步日报失败: {e}")
        
        sqlite_conn.close()
        
        # 输出同步结果
        print("\n🎉 数据库同步完成!")
        print(f"👥 会员数据: {members_synced} 个新增, {members_skipped} 个跳过")
        print(f"📊 访问记录: {access_synced} 条同步")
        print(f"📰 日报数据: {reports_synced} 条同步")
        
        return True
        
    except Exception as e:
        print(f"❌ 同步过程中发生错误: {e}")
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("🔄 Penny Scanner 数据库同步工具")
    print("=" * 60)
    
    # 确认操作
    response = input("\n⚠️  此操作将同步本地SQLite数据到Heroku PostgreSQL，是否继续? (y/N): ")
    if response.lower() != 'y':
        print("❌ 操作已取消")
        return
    
    success = sync_sqlite_to_postgres()
    
    if success:
        print("\n✅ 同步成功! 现在可以在Heroku上使用PostgreSQL数据库了")
        print("\n💡 提示:")
        print("1. 确保Heroku应用已设置DATABASE_URL环境变量")
        print("2. 部署应用后，系统会自动使用PostgreSQL")
        print("3. 默认guest用户 (guest/guest) 已包含在同步中")
    else:
        print("\n❌ 同步失败，请检查错误信息并重试")

if __name__ == "__main__":
    main()