#!/bin/bash

# MCP管理器快速启动脚本
# 解决stdio/SSE/HTTP混合管理的痛点

echo "🧙‍♂️ 炼妖壶MCP管理器 - 快速启动"
echo "=================================="

# 检查Python环境
if ! command -v python3 &> /dev/null; then
    echo "❌ Python3未安装，请先安装Python3"
    exit 1
fi

# 检查依赖
echo "📦 检查依赖..."
pip3 install -q fastapi uvicorn pyyaml httpx

# 创建必要目录
mkdir -p templates logs

# 检查配置文件
if [ ! -f "mcp_services.yml" ]; then
    echo "📝 创建默认配置文件..."
    python3 -c "
from mcp_manager import MCPManager
manager = MCPManager()
print('✅ 默认配置已创建')
"
fi

# 启动选项
echo ""
echo "🚀 启动选项:"
echo "1. 启动管理器 (默认)"
echo "2. 启动管理器 + 所有服务"
echo "3. 启动管理器 + 金融服务组"
echo "4. 启动管理器 + 工作流服务组"
echo "5. 仅启动核心服务"

read -p "请选择 (1-5, 默认1): " choice
choice=${choice:-1}

case $choice in
    1)
        echo "🌐 启动MCP管理器..."
        python3 start_mcp_manager.py
        ;;
    2)
        echo "🚀 启动管理器并启动所有服务..."
        python3 start_mcp_manager.py --start-all
        ;;
    3)
        echo "💰 启动管理器并启动金融服务组..."
        python3 start_mcp_manager.py --group financial
        ;;
    4)
        echo "⚙️ 启动管理器并启动工作流服务组..."
        python3 start_mcp_manager.py --group workflow
        ;;
    5)
        echo "🎯 启动管理器并启动核心服务..."
        python3 start_mcp_manager.py --group core
        ;;
    *)
        echo "❌ 无效选择"
        exit 1
        ;;
esac