#!/bin/bash
# 八仙论道+三清验证系统快速启动脚本

set -e

echo "🌟 八仙论道+三清验证系统快速启动"
echo "=================================="

# 检查Python版本
python_version=$(python3 --version 2>&1 | cut -d' ' -f2 | cut -d'.' -f1,2)
required_version="3.8"

if [ "$(printf '%s\n' "$required_version" "$python_version" | sort -V | head -n1)" != "$required_version" ]; then
    echo "❌ 需要Python 3.8或更高版本，当前版本: $python_version"
    exit 1
fi

echo "✅ Python版本检查通过: $python_version"

# 创建必要的目录
echo "📁 创建必要目录..."
mkdir -p logs
mkdir -p reports
mkdir -p config

# 检查环境变量文件
if [ ! -f ".env.baxian_sanqing" ]; then
    echo "⚠️  环境变量文件不存在，从示例文件复制..."
    cp .env.baxian_sanqing.example .env.baxian_sanqing
    echo "📝 请编辑 .env.baxian_sanqing 文件，填入正确的配置信息"
    echo "🔧 必需配置项："
    echo "   - OPENMANUS_URL"
    echo "   - OPENMANUS_API_KEY" 
    echo "   - ZILLIZ_HOST"
    echo "   - ZILLIZ_USERNAME"
    echo "   - ZILLIZ_PASSWORD"
    echo "   - OPENAI_API_KEY"
    
    read -p "是否现在编辑配置文件? (y/n): " edit_config
    if [ "$edit_config" = "y" ] || [ "$edit_config" = "Y" ]; then
        ${EDITOR:-nano} .env.baxian_sanqing
    else
        echo "⚠️  请手动编辑 .env.baxian_sanqing 文件后重新运行此脚本"
        exit 1
    fi
fi

# 加载环境变量
echo "🔧 加载环境变量..."
export $(cat .env.baxian_sanqing | grep -v '^#' | xargs)

# 检查必需的环境变量
required_vars=("OPENMANUS_URL" "OPENMANUS_API_KEY" "ZILLIZ_HOST" "ZILLIZ_USERNAME" "ZILLIZ_PASSWORD")
missing_vars=()

for var in "${required_vars[@]}"; do
    if [ -z "${!var}" ]; then
        missing_vars+=("$var")
    fi
done

if [ ${#missing_vars[@]} -ne 0 ]; then
    echo "❌ 缺少必需的环境变量:"
    printf '   - %s\n' "${missing_vars[@]}"
    echo "请编辑 .env.baxian_sanqing 文件"
    exit 1
fi

echo "✅ 环境变量检查通过"

# 安装依赖
echo "📦 检查并安装依赖..."
if [ ! -f "requirements.txt" ]; then
    echo "⚠️  requirements.txt 不存在，创建基本依赖文件..."
    cat > requirements.txt << EOF
autogen-agentchat>=0.2.0
pydantic>=2.0.0
aiohttp>=3.8.0
PyYAML>=6.0
asyncio-mqtt>=0.11.0
python-dotenv>=1.0.0
fastapi>=0.100.0
uvicorn>=0.23.0
pymilvus>=2.3.0
playwright>=1.40.0
openai>=1.0.0
anthropic>=0.7.0
EOF
fi

pip install -r requirements.txt

# 安装Playwright浏览器
echo "🌐 安装Playwright浏览器..."
playwright install chromium

# 检查配置文件
if [ ! -f "config/baxian_sanqing_config.yaml" ]; then
    echo "❌ 配置文件不存在: config/baxian_sanqing_config.yaml"
    echo "请确保配置文件已正确创建"
    exit 1
fi

echo "✅ 配置文件检查通过"

# 测试连接
echo "🔍 测试系统连接..."

# 测试OpenManus连接
echo "  测试OpenManus连接..."
if curl -s --connect-timeout 10 "$OPENMANUS_URL/health" > /dev/null; then
    echo "  ✅ OpenManus连接正常"
else
    echo "  ⚠️  OpenManus连接失败，请检查URL和网络"
fi

# 测试Zilliz连接
echo "  测试Zilliz连接..."
python3 -c "
import sys
try:
    from pymilvus import connections
    connections.connect(
        alias='default',
        host='$ZILLIZ_HOST',
        port='19530',
        user='$ZILLIZ_USERNAME',
        password='$ZILLIZ_PASSWORD'
    )
    print('  ✅ Zilliz连接正常')
except Exception as e:
    print(f'  ⚠️  Zilliz连接失败: {e}')
"

echo "🚀 系统准备完成！"
echo ""
echo "启动选项："
echo "1. 交互模式: python3 scripts/start_baxian_sanqing_system.py --interactive"
echo "2. 命令行模式: python3 scripts/start_baxian_sanqing_system.py --topic '您的主题'"
echo "3. 查看帮助: python3 scripts/start_baxian_sanqing_system.py --help"
echo ""

# 询问是否立即启动
read -p "是否立即启动交互模式? (y/n): " start_now
if [ "$start_now" = "y" ] || [ "$start_now" = "Y" ]; then
    echo "🎭 启动八仙论道+三清验证系统..."
    python3 scripts/start_baxian_sanqing_system.py --interactive
else
    echo "👋 准备完成，您可以随时使用上述命令启动系统"
fi