# n8n兜率宫集成指南

## 🎯 目标
将太公心易的七姐妹基本面数据自动推送到n8n兜率宫，实现数据流自动化。

## 📋 当前问题分析

### ❌ 问题：
- URL: `https://n8n.git4ta.fun/workflow/1nD7GgjcaEERPWHV`
- 错误: `Cannot POST /workflow/1nD7GgjcaEERPWHV`
- 状态码: 404

### 💡 原因：
n8n的workflow URL不是webhook endpoint。需要使用专门的webhook URL。

## 🔧 解决方案

### 步骤1: 在n8n中创建Webhook工作流

1. **登录n8n**: `https://n8n.git4ta.fun`

2. **创建新工作流**:
   - 点击 "New Workflow"
   - 添加 "Webhook" 节点作为触发器

3. **配置Webhook节点**:
   ```json
   {
     "httpMethod": "POST",
     "path": "taigong-seven-sisters",
     "responseMode": "responseNode",
     "options": {}
   }
   ```

4. **获取Webhook URL**:
   - 应该类似: `https://n8n.git4ta.fun/webhook/taigong-seven-sisters`
   - 或者: `https://n8n.git4ta.fun/webhook-test/taigong-seven-sisters`

### 步骤2: 配置数据处理节点

添加以下节点来处理七姐妹数据：

1. **Function节点** - 数据预处理:
   ```javascript
   // 解析七姐妹数据
   const sistersData = $json.data;
   const summary = $json.summary;
   
   // 格式化为兜率宫需要的格式
   const formattedData = {
     timestamp: $json.timestamp,
     source: "太公心易七仙女雷达",
     market_analysis: {
       top_performer: summary.top_performer,
       total_stocks: summary.total_stocks,
       data: sistersData.map(stock => ({
         symbol: stock.symbol,
         name: stock.chinese_name,
         emoji: stock.emoji,
         price: stock.close_price,
         eps: stock.eps_ttm,
         pe_ratio: stock.pe_ratio,
         rank: stock.rank,
         score: stock.composite_score
       }))
     }
   };
   
   return formattedData;
   ```

2. **HTTP Request节点** - 转发到其他系统:
   ```json
   {
     "method": "POST",
     "url": "https://your-other-system.com/api/market-data",
     "headers": {
       "Content-Type": "application/json",
       "Authorization": "Bearer YOUR_TOKEN"
     }
   }
   ```

3. **Slack/Discord节点** - 发送通知:
   ```
   📊 太公心易七姐妹更新
   
   🥇 第一名: {{$json.market_analysis.top_performer.emoji}} {{$json.market_analysis.top_performer.symbol}}
   💰 价格: ${{$json.market_analysis.top_performer.close_price}}
   📈 EPS: ${{$json.market_analysis.top_performer.eps_ttm}}
   
   🕐 更新时间: {{$json.timestamp}}
   ```

### 步骤3: 更新环境变量

在 `.env` 文件中更新：
```bash
# 正确的webhook URL
n8n_url=https://n8n.git4ta.fun/webhook/taigong-seven-sisters

# 如果需要认证token
n8n_token=your_webhook_token_if_needed
```

## 🚀 自动化推送实现

### 方案1: 数据更新时自动推送
修改 `scripts/update_seven_sisters_data.py`:

```python
async def main():
    # ... 现有的数据更新逻辑 ...
    
    # 数据更新成功后，推送到n8n
    if success_count > 0:
        try:
            from scripts.test_n8n_connectivity import N8nConnector
            
            connector = N8nConnector()
            latest_df = manager.get_latest_fundamentals()
            
            if not latest_df.empty:
                # 转换为推送格式
                sisters_data = []
                for _, row in latest_df.iterrows():
                    sisters_data.append({
                        "symbol": row['symbol'],
                        "chinese_name": row['chinese_name'],
                        "emoji": row['emoji'],
                        "close_price": float(row['close_price']) if pd.notna(row['close_price']) else None,
                        "eps_ttm": float(row['eps_ttm']) if pd.notna(row['eps_ttm']) else None,
                        "pe_ratio": float(row['pe_ratio']) if pd.notna(row['pe_ratio']) else None,
                        "rank": int(row.name + 1)  # 排名
                    })
                
                # 推送到n8n兜率宫
                success = connector.send_seven_sisters_data(sisters_data)
                if success:
                    logger.info("🎉 数据已成功推送到兜率宫!")
                else:
                    logger.warning("⚠️ 推送到兜率宫失败")
                    
        except Exception as e:
            logger.error(f"❌ 推送到兜率宫异常: {e}")
```

### 方案2: 定时推送
创建 `scripts/push_to_n8n.py`:

```python
#!/usr/bin/env python3
"""
定时推送七姐妹数据到n8n兜率宫
"""

import sys
sys.path.append('src')

from data.seven_sisters_db_manager import SevenSistersDBManager
from scripts.test_n8n_connectivity import N8nConnector

def push_latest_data():
    manager = SevenSistersDBManager()
    connector = N8nConnector()
    
    # 获取最新数据
    latest_df = manager.get_latest_fundamentals()
    ranking_df = manager.get_eps_ranking()
    
    if not latest_df.empty:
        # 构造推送数据
        sisters_data = ranking_df.to_dict('records')
        
        # 推送到兜率宫
        success = connector.send_seven_sisters_data(sisters_data)
        return success
    
    return False

if __name__ == "__main__":
    success = push_latest_data()
    print("✅ 推送成功" if success else "❌ 推送失败")
```

## 🎯 总结

### 回答你的问题：

**"能不能在这里写，然后推送到N8n还是说，必须人来摆渡这些代码？"**

✅ **可以完全自动化推送，无需人工摆渡！**

1. **自动化程度**: 100%自动化
2. **触发方式**: 数据更新后自动推送
3. **数据流**: 太公心易 → PostgreSQL → n8n兜率宫 → 其他系统
4. **人工干预**: 仅需一次性配置webhook URL

### 优势：
- 🚀 实时数据流
- 🔄 自动化推送
- 📊 结构化数据
- 🎯 精准投递
- 🔗 系统集成

### 下一步：
1. 在n8n中创建正确的webhook工作流
2. 获取正确的webhook URL
3. 更新环境变量
4. 测试连通性
5. 启用自动推送