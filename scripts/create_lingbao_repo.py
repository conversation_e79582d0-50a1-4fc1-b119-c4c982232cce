#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
为灵宝道君创建专用GitHub仓库
让OpenHands基于完整上下文进行深度思考

作者：太公心易BI系统
版本：v1.0 - 深度思考版
"""

import json
import urllib.request
import urllib.parse
import urllib.error
import time
from datetime import datetime

class LingbaoRepoManager:
    """灵宝道君仓库管理器"""
    
    def __init__(self, openhands_api_key: str):
        self.openhands_api_key = openhands_api_key
        self.base_url = "https://app.all-hands.dev/api"
        
    def create_context_aware_conversation(self, repository: str, initial_message: str):
        """创建基于仓库上下文的对话"""
        
        print("🔮 创建灵宝道君专用对话通道")
        print("=" * 60)
        print(f"📁 目标仓库: {repository}")
        print(f"📝 初始消息: {initial_message[:100]}...")
        print()
        
        headers = {
            "Authorization": f"Bearer {self.openhands_api_key}",
            "Content-Type": "application/json",
            "User-Agent": "Lingbao-Daojun-Deep-Analysis/1.0"
        }
        
        # 构建深度分析请求
        data = {
            "initial_user_msg": initial_message,
            "repository": repository
        }
        
        try:
            url = f"{self.base_url}/conversations"
            json_data = json.dumps(data).encode('utf-8')
            
            req = urllib.request.Request(url, data=json_data)
            for key, value in headers.items():
                req.add_header(key, value)
            
            print("🚀 发送深度分析请求...")
            
            with urllib.request.urlopen(req, timeout=30) as response:
                response_data = response.read().decode('utf-8')
                status_code = response.getcode()
                
                if status_code == 200:
                    result = json.loads(response_data)
                    conversation_id = result.get('conversation_id')
                    
                    print("✅ 灵宝道君专用通道创建成功!")
                    print(f"🆔 对话ID: {conversation_id}")
                    print(f"🔗 查看链接: https://app.all-hands.dev/conversations/{conversation_id}")
                    
                    # 保存对话ID供后续使用
                    self._save_conversation_id(conversation_id, repository)
                    
                    return conversation_id, None
                else:
                    error_msg = f"HTTP {status_code}: {response_data}"
                    print(f"❌ 创建失败: {error_msg}")
                    return None, error_msg
                    
        except Exception as e:
            error_msg = f"请求异常: {str(e)}"
            print(f"❌ 异常: {error_msg}")
            return None, error_msg
    
    def _save_conversation_id(self, conversation_id: str, repository: str):
        """保存对话ID供后续使用"""
        
        config = {
            "lingbao_conversation_id": conversation_id,
            "repository": repository,
            "created_at": datetime.now().isoformat(),
            "purpose": "灵宝道君深度验证专用通道"
        }
        
        try:
            with open("lingbao_conversation.json", "w", encoding="utf-8") as f:
                json.dump(config, f, indent=2, ensure_ascii=False)
            print(f"💾 对话配置已保存到 lingbao_conversation.json")
        except Exception as e:
            print(f"⚠️ 保存配置失败: {e}")
    
    def load_conversation_id(self):
        """加载已保存的对话ID"""
        try:
            with open("lingbao_conversation.json", "r", encoding="utf-8") as f:
                config = json.load(f)
            return config.get("lingbao_conversation_id"), config.get("repository")
        except:
            return None, None

def create_repo_context_message():
    """创建基于仓库上下文的深度分析消息"""
    
    message = """
你好！我是稷下学宫的灵宝道君，负责八仙论道后的田野调查验证工作。

请仔细阅读这个仓库的代码和文档，理解我们的项目架构：

🏛️ 项目背景：
- 这是炼妖壶(Cauldron)项目，一个AI驱动的投资分析系统
- 采用中华古典哲学与现代AI技术融合的架构
- 核心是稷下学宫的三清论道+八仙过海辩论系统

🔮 我的角色：
- 灵宝道君：三清之一，负责辩论主持和结果验证
- 田野调查：通过你(OpenHands)进行实时Web验证
- 技术统计：分析数据完整性和验证结果置信度

🎯 当前任务：
请基于对项目的理解，分析以下投资论断的可信度：

1. 特斯拉Q4财报将超预期20%
2. AI芯片市场需求在2025年将翻倍
3. 英伟达数据中心业务将保持高增长
4. 电动车行业竞争将进一步加剧

📊 验证要求：
- 搜索最新的财报数据和市场研究
- 分析每个论断的支持证据和反驳证据
- 给出0-1的置信度评分
- 识别关键风险因素
- 提供投资建议的合理性评估

🔍 深度分析：
请不要简单回答是/否，而是：
1. 深入分析每个论断的逻辑基础
2. 查找权威数据源进行验证
3. 考虑宏观经济和行业趋势
4. 评估时间窗口的合理性
5. 识别可能的黑天鹅事件

这是稷下学宫八仙辩论后的关键验证环节，请发挥你的深度分析能力，为元始天尊的最终决策提供可靠依据。

开始你的田野调查吧！
"""
    
    return message.strip()

def test_deep_analysis():
    """测试深度分析功能"""
    
    print("🧠 测试OpenHands深度分析能力")
    print("=" * 60)
    
    api_key = "hA04ZDQbdKUbBCqmN5ZPFkcdK0xsKLwX"
    repo_manager = LingbaoRepoManager(api_key)
    
    # 目标仓库（你需要创建一个包含项目上下文的仓库）
    target_repository = "jingminzhang/cauldron-lingbao"  # 示例仓库名
    
    # 创建深度分析消息
    deep_message = create_repo_context_message()
    
    # 创建基于上下文的对话
    conversation_id, error = repo_manager.create_context_aware_conversation(
        target_repository, deep_message
    )
    
    if conversation_id:
        print(f"\n🎉 成功创建灵宝道君专用分析通道!")
        print(f"🆔 对话ID: {conversation_id}")
        print(f"📁 关联仓库: {target_repository}")
        print(f"🔗 查看分析: https://app.all-hands.dev/conversations/{conversation_id}")
        
        print(f"\n💡 使用建议:")
        print("1. 🔄 后续可以继续在这个对话中发送验证任务")
        print("2. 📊 OpenHands会基于项目上下文进行深度分析")
        print("3. 🧠 每次分析都会考虑稷下学宫的整体架构")
        print("4. 💾 对话ID已保存，可以重复使用")
        
        return conversation_id
    else:
        print(f"❌ 创建失败: {error}")
        return None

def demonstrate_repo_strategy():
    """演示基于仓库的策略"""
    
    print(f"\n📚 基于仓库的深度分析策略")
    print("=" * 60)
    
    print("🎯 为什么需要仓库上下文？")
    print("1. 🧠 理解项目架构 - OpenHands知道稷下学宫的设计理念")
    print("2. 🔮 角色认知 - 明确灵宝道君的职责和验证标准")
    print("3. 📊 数据标准 - 了解置信度计算和风险评估方法")
    print("4. 🎭 文化背景 - 理解三清八仙的哲学框架")
    print()
    
    print("📁 建议的仓库结构:")
    print("cauldron-lingbao/")
    print("├── README.md              # 项目概述和灵宝道君介绍")
    print("├── docs/")
    print("│   ├── architecture.md    # 稷下学宫架构设计")
    print("│   ├── verification.md    # 验证方法论")
    print("│   └── examples.md        # 验证案例")
    print("├── src/")
    print("│   ├── lingbao_core.py    # 灵宝道君核心逻辑")
    print("│   └── verification_framework.py")
    print("└── config/")
    print("    └── verification_standards.json")
    print()
    
    print("🚀 深度分析的优势:")
    print("✅ 上下文理解 - 基于完整项目背景")
    print("✅ 角色一致性 - 保持灵宝道君的人设")
    print("✅ 标准统一 - 使用一致的验证标准")
    print("✅ 持续对话 - 建立长期的分析关系")
    print("✅ 学习能力 - 随着交互不断优化")

def create_repo_template():
    """创建仓库模板文件"""
    
    print(f"\n📝 创建仓库模板文件")
    print("=" * 60)
    
    # README.md
    readme_content = """# 🔮 灵宝道君田野调查系统

## 项目概述

灵宝道君是稷下学宫三清论道系统的核心组件，负责八仙过海辩论后的田野调查验证工作。

## 角色定位

- **身份**: 三清之一，技术统计专家
- **职责**: 辩论结果验证、数据完整性分析
- **能力**: 实时Web验证、置信度计算、风险评估

## 验证方法论

### 置信度计算
- 传统验证权重: 60%
- Web验证权重: 40%
- 综合置信度 = 加权平均

### 验证标准
1. 数据来源权威性
2. 时效性和准确性
3. 逻辑一致性
4. 风险因素识别

## 使用说明

灵宝道君通过OpenHands进行田野调查，请基于项目上下文进行深度分析。
"""
    
    try:
        with open("README_template.md", "w", encoding="utf-8") as f:
            f.write(readme_content)
        print("✅ README模板已创建: README_template.md")
    except Exception as e:
        print(f"❌ 创建README失败: {e}")

def main():
    """主函数"""
    
    print("🔮 灵宝道君深度分析系统初始化")
    print("=" * 80)
    print("让OpenHands基于完整项目上下文进行深度思考")
    print("=" * 80)
    
    # 演示策略
    demonstrate_repo_strategy()
    
    # 创建模板文件
    create_repo_template()
    
    # 测试深度分析
    conversation_id = test_deep_analysis()
    
    print(f"\n🎯 下一步行动计划:")
    print("=" * 60)
    print("1. 📁 创建GitHub仓库 'cauldron-lingbao'")
    print("2. 📝 上传项目文档和代码")
    print("3. 🔮 使用仓库URL创建OpenHands对话")
    print("4. 💾 保存对话ID供灵宝道君使用")
    print("5. 🧠 开始基于上下文的深度验证")
    
    if conversation_id:
        print(f"\n🎉 灵宝道君已获得真正的'手'!")
        print(f"🆔 专用对话ID: {conversation_id}")
        print("🚀 现在可以进行真正的深度田野调查了!")

if __name__ == "__main__":
    main()
