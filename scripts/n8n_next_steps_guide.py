#!/usr/bin/env python3
"""
N8N健康状态确认后的下一步行动指南
基于健康的N8N实例，提供完整的集成和优化方案
"""

import requests
import json
from datetime import datetime
from typing import Dict, List, Any

class N8NNextStepsPlanner:
    """N8N下一步行动规划器"""
    
    def __init__(self):
        self.webhook_url = "https://houzhongxu-n8n-free.hf.space/webhook/ce40f698-832e-475a-a3c7-0895c9e2e90b"
        self.base_url = "https://houzhongxu-n8n-free.hf.space"
        self.workflow_id = "5Ibi4vJZjSB0ZaTt"
        
        print("🚀 N8N下一步行动规划器启动")
        print("✅ N8N实例健康状态已确认")
    
    def create_production_integration_plan(self) -> Dict[str, Any]:
        """创建生产环境集成计划"""
        
        plan = {
            "immediate_actions": {
                "priority": "high",
                "timeframe": "今天完成",
                "tasks": [
                    {
                        "task": "更新环境配置",
                        "description": "将正确的webhook URL添加到所有配置文件",
                        "code_example": """
# 更新 .env 文件
N8N_WEBHOOK_PROD=https://houzhongxu-n8n-free.hf.space/webhook/ce40f698-832e-475a-a3c7-0895c9e2e90b
N8N_BASE_URL=https://houzhongxu-n8n-free.hf.space
N8N_WORKFLOW_ID=5Ibi4vJZjSB0ZaTt
"""
                    },
                    {
                        "task": "集成到RSS处理脚本",
                        "description": "在现有RSS处理流程中添加N8N推送",
                        "code_example": """
from scripts.taigong_n8n_production_integration import TaiGongXinYiN8NProduction

# 在RSS处理完成后添加
n8n = TaiGongXinYiN8NProduction()
result = n8n.send_rss_analysis(processed_rss_data)
"""
                    },
                    {
                        "task": "设置七姐妹数据自动推送",
                        "description": "在数据更新时自动推送到N8N",
                        "code_example": """
# 在 scripts/update_seven_sisters_data.py 中添加
if success_count > 0:
    n8n_result = n8n.send_seven_sisters_update(latest_data)
    if n8n_result["success"]:
        logger.info("✅ 数据已推送到N8N")
"""
                    }
                ]
            },
            "short_term_goals": {
                "priority": "medium",
                "timeframe": "本周完成",
                "tasks": [
                    {
                        "task": "配置N8N工作流增强",
                        "description": "在N8N中添加数据处理和分析节点",
                        "components": [
                            "AI分析节点 (OpenAI/Gemini)",
                            "数据存储节点 (MongoDB/PostgreSQL)",
                            "通知节点 (Email/Slack)",
                            "错误处理节点"
                        ]
                    },
                    {
                        "task": "实现监控和日志",
                        "description": "添加完整的监控和日志系统",
                        "features": [
                            "实时健康检查",
                            "性能监控",
                            "错误追踪",
                            "数据质量监控"
                        ]
                    },
                    {
                        "task": "创建定时任务",
                        "description": "设置自动化的数据推送任务",
                        "schedule": [
                            "每小时推送七姐妹数据",
                            "每30分钟推送RSS分析",
                            "每日生成综合报告"
                        ]
                    }
                ]
            },
            "long_term_vision": {
                "priority": "strategic",
                "timeframe": "未来1-3个月",
                "goals": [
                    "构建完整的智能投资决策系统",
                    "实现多数据源的智能融合",
                    "开发预测分析和风险评估功能",
                    "建立用户个性化推荐系统"
                ]
            }
        }
        
        return plan
    
    def test_production_readiness(self) -> Dict[str, Any]:
        """测试生产环境就绪状态"""
        
        print("\n🧪 测试生产环境就绪状态...")
        
        readiness_tests = {
            "webhook_reliability": self._test_webhook_reliability(),
            "data_format_support": self._test_data_formats(),
            "performance_metrics": self._test_performance(),
            "error_handling": self._test_error_scenarios()
        }
        
        # 计算总体就绪分数
        scores = []
        for test_name, result in readiness_tests.items():
            if isinstance(result, dict) and "score" in result:
                scores.append(result["score"])
        
        overall_score = sum(scores) / len(scores) if scores else 0
        
        readiness_assessment = {
            "overall_score": overall_score,
            "readiness_level": self._get_readiness_level(overall_score),
            "test_results": readiness_tests,
            "recommendations": self._get_readiness_recommendations(overall_score)
        }
        
        return readiness_assessment
    
    def _test_webhook_reliability(self) -> Dict[str, Any]:
        """测试webhook可靠性"""
        
        print("   📡 测试webhook可靠性...")
        
        success_count = 0
        total_tests = 5
        response_times = []
        
        for i in range(total_tests):
            try:
                start_time = datetime.now()
                response = requests.post(
                    self.webhook_url,
                    json={
                        "reliability_test": True,
                        "test_number": i + 1,
                        "timestamp": datetime.now().isoformat()
                    },
                    timeout=10
                )
                end_time = datetime.now()
                
                if response.status_code == 200:
                    success_count += 1
                    response_times.append((end_time - start_time).total_seconds())
                
            except Exception as e:
                print(f"   ⚠️ 测试 {i+1} 失败: {e}")
        
        success_rate = success_count / total_tests
        avg_response_time = sum(response_times) / len(response_times) if response_times else 0
        
        return {
            "success_rate": success_rate,
            "average_response_time": avg_response_time,
            "total_tests": total_tests,
            "successful_tests": success_count,
            "score": success_rate * 100,
            "status": "excellent" if success_rate >= 0.9 else "good" if success_rate >= 0.7 else "needs_improvement"
        }
    
    def _test_data_formats(self) -> Dict[str, Any]:
        """测试数据格式支持"""
        
        print("   📊 测试数据格式支持...")
        
        test_formats = {
            "simple_json": {"test": "simple", "value": 123},
            "nested_object": {
                "data": {
                    "nested": {
                        "value": "test",
                        "array": [1, 2, 3]
                    }
                }
            },
            "rss_analysis": {
                "type": "rss_analysis",
                "title": "测试文章",
                "content": "测试内容",
                "sentiment": "positive",
                "keywords": ["测试", "N8N"]
            },
            "market_data": {
                "type": "market_data",
                "symbols": ["AAPL", "MSFT"],
                "analysis": {
                    "trend": "up",
                    "confidence": 0.85
                }
            }
        }
        
        supported_formats = 0
        total_formats = len(test_formats)
        
        for format_name, test_data in test_formats.items():
            try:
                response = requests.post(
                    self.webhook_url,
                    json=test_data,
                    timeout=5
                )
                
                if response.status_code == 200:
                    supported_formats += 1
                    print(f"   ✅ {format_name}: 支持")
                else:
                    print(f"   ⚠️ {format_name}: 不支持 ({response.status_code})")
                    
            except Exception as e:
                print(f"   ❌ {format_name}: 错误 ({e})")
        
        support_rate = supported_formats / total_formats
        
        return {
            "supported_formats": supported_formats,
            "total_formats": total_formats,
            "support_rate": support_rate,
            "score": support_rate * 100,
            "status": "excellent" if support_rate >= 0.9 else "good" if support_rate >= 0.7 else "needs_improvement"
        }
    
    def _test_performance(self) -> Dict[str, Any]:
        """测试性能指标"""
        
        print("   ⚡ 测试性能指标...")
        
        # 测试不同大小的数据包
        test_sizes = {
            "small": {"data": "x" * 100},
            "medium": {"data": "x" * 1000},
            "large": {"data": "x" * 10000}
        }
        
        performance_results = {}
        
        for size_name, test_data in test_sizes.items():
            try:
                start_time = datetime.now()
                response = requests.post(
                    self.webhook_url,
                    json=test_data,
                    timeout=30
                )
                end_time = datetime.now()
                
                response_time = (end_time - start_time).total_seconds()
                
                performance_results[size_name] = {
                    "response_time": response_time,
                    "success": response.status_code == 200,
                    "data_size": len(json.dumps(test_data))
                }
                
            except Exception as e:
                performance_results[size_name] = {
                    "error": str(e),
                    "success": False
                }
        
        # 计算平均性能分数
        successful_tests = [r for r in performance_results.values() if r.get("success")]
        avg_response_time = sum(r["response_time"] for r in successful_tests) / len(successful_tests) if successful_tests else 0
        
        # 性能评分 (响应时间越短分数越高)
        performance_score = max(0, 100 - (avg_response_time * 50))
        
        return {
            "test_results": performance_results,
            "average_response_time": avg_response_time,
            "score": performance_score,
            "status": "excellent" if avg_response_time < 1 else "good" if avg_response_time < 3 else "needs_improvement"
        }
    
    def _test_error_scenarios(self) -> Dict[str, Any]:
        """测试错误处理场景"""
        
        print("   🛡️ 测试错误处理...")
        
        error_tests = {
            "invalid_json": "invalid json string",
            "empty_payload": {},
            "null_values": {"data": None, "value": None},
            "special_characters": {"data": "测试🚀特殊字符@#$%^&*()"},
        }
        
        handled_errors = 0
        total_tests = len(error_tests)
        
        for test_name, test_data in error_tests.items():
            try:
                if test_name == "invalid_json":
                    # 发送无效JSON
                    response = requests.post(
                        self.webhook_url,
                        data=test_data,
                        headers={'Content-Type': 'application/json'},
                        timeout=5
                    )
                else:
                    response = requests.post(
                        self.webhook_url,
                        json=test_data,
                        timeout=5
                    )
                
                # 检查是否优雅处理错误 (不应该返回5xx错误)
                if response.status_code < 500:
                    handled_errors += 1
                    print(f"   ✅ {test_name}: 优雅处理 ({response.status_code})")
                else:
                    print(f"   ⚠️ {test_name}: 服务器错误 ({response.status_code})")
                    
            except Exception as e:
                print(f"   ❌ {test_name}: 异常 ({e})")
        
        error_handling_rate = handled_errors / total_tests
        
        return {
            "handled_errors": handled_errors,
            "total_tests": total_tests,
            "error_handling_rate": error_handling_rate,
            "score": error_handling_rate * 100,
            "status": "excellent" if error_handling_rate >= 0.8 else "good" if error_handling_rate >= 0.6 else "needs_improvement"
        }
    
    def _get_readiness_level(self, score: float) -> str:
        """获取就绪等级"""
        if score >= 90:
            return "production_ready"
        elif score >= 75:
            return "mostly_ready"
        elif score >= 60:
            return "needs_optimization"
        else:
            return "needs_significant_work"
    
    def _get_readiness_recommendations(self, score: float) -> List[str]:
        """获取就绪建议"""
        if score >= 90:
            return [
                "✅ 可以立即部署到生产环境",
                "🚀 开始集成到现有系统",
                "📊 设置监控和日志",
                "🔄 实现自动化流程"
            ]
        elif score >= 75:
            return [
                "🔧 进行少量优化后可部署",
                "📈 改进性能较差的部分",
                "🛡️ 加强错误处理",
                "🧪 增加更多测试"
            ]
        elif score >= 60:
            return [
                "⚠️ 需要显著优化才能生产使用",
                "🔍 深入分析性能问题",
                "🛠️ 修复可靠性问题",
                "📋 制定详细的改进计划"
            ]
        else:
            return [
                "🚨 不建议用于生产环境",
                "🔄 重新配置N8N工作流",
                "🧪 进行全面的测试和调试",
                "📞 寻求技术支持"
            ]
    
    def generate_implementation_roadmap(self, readiness_assessment: Dict) -> Dict[str, Any]:
        """生成实施路线图"""
        
        readiness_level = readiness_assessment["readiness_level"]
        
        roadmap = {
            "current_status": {
                "readiness_level": readiness_level,
                "overall_score": readiness_assessment["overall_score"],
                "timestamp": datetime.now().isoformat()
            },
            "implementation_phases": {}
        }
        
        if readiness_level == "production_ready":
            roadmap["implementation_phases"] = {
                "phase_1_immediate": {
                    "duration": "1-2 天",
                    "tasks": [
                        "更新所有配置文件",
                        "集成到RSS处理流程",
                        "设置基础监控"
                    ]
                },
                "phase_2_integration": {
                    "duration": "1 周",
                    "tasks": [
                        "完整的系统集成",
                        "自动化定时任务",
                        "用户界面集成"
                    ]
                },
                "phase_3_optimization": {
                    "duration": "2-4 周",
                    "tasks": [
                        "性能优化",
                        "高级功能开发",
                        "用户反馈收集和改进"
                    ]
                }
            }
        else:
            roadmap["implementation_phases"] = {
                "phase_1_optimization": {
                    "duration": "1-2 周",
                    "tasks": [
                        "解决识别的问题",
                        "提高可靠性和性能",
                        "增强错误处理"
                    ]
                },
                "phase_2_testing": {
                    "duration": "1 周",
                    "tasks": [
                        "全面测试优化结果",
                        "验证生产就绪状态",
                        "制定部署计划"
                    ]
                },
                "phase_3_deployment": {
                    "duration": "1-2 周",
                    "tasks": [
                        "谨慎的生产部署",
                        "密切监控",
                        "逐步扩展使用"
                    ]
                }
            }
        
        return roadmap

def main():
    """主规划函数"""
    
    print("🎯 N8N健康状态确认 - 下一步行动规划")
    print("=" * 60)
    
    planner = N8NNextStepsPlanner()
    
    # 1. 创建集成计划
    print("\n📋 创建生产环境集成计划...")
    integration_plan = planner.create_production_integration_plan()
    
    # 2. 测试生产就绪状态
    print("\n🧪 评估生产环境就绪状态...")
    readiness_assessment = planner.test_production_readiness()
    
    # 3. 生成实施路线图
    print("\n🗺️ 生成实施路线图...")
    implementation_roadmap = planner.generate_implementation_roadmap(readiness_assessment)
    
    # 4. 汇总完整报告
    complete_plan = {
        "planning_timestamp": datetime.now().isoformat(),
        "n8n_status": "healthy",
        "integration_plan": integration_plan,
        "readiness_assessment": readiness_assessment,
        "implementation_roadmap": implementation_roadmap,
        "next_actions": {
            "immediate": integration_plan["immediate_actions"]["tasks"][:2],
            "this_week": integration_plan["short_term_goals"]["tasks"][:1],
            "this_month": integration_plan["long_term_vision"]["goals"][:2]
        }
    }
    
    # 5. 保存计划
    with open("n8n_next_steps_plan.json", "w", encoding="utf-8") as f:
        json.dump(complete_plan, f, ensure_ascii=False, indent=2)
    
    # 6. 输出总结
    print("\n" + "=" * 60)
    print("📊 规划总结:")
    print(f"生产就绪评分: {readiness_assessment['overall_score']:.1f}/100")
    print(f"就绪等级: {readiness_assessment['readiness_level']}")
    
    print(f"\n🎯 立即行动 (今天):")
    for i, task in enumerate(complete_plan["next_actions"]["immediate"], 1):
        print(f"  {i}. {task['task']}")
    
    print(f"\n📅 本周目标:")
    for i, task in enumerate(complete_plan["next_actions"]["this_week"], 1):
        print(f"  {i}. {task['task']}")
    
    print(f"\n🚀 本月愿景:")
    for i, goal in enumerate(complete_plan["next_actions"]["this_month"], 1):
        print(f"  {i}. {goal}")
    
    print(f"\n📁 详细计划已保存到: n8n_next_steps_plan.json")
    
    return complete_plan

if __name__ == "__main__":
    plan = main()