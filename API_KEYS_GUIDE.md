# API 密钥管理指南

## 概述

本项目使用多个 API 密钥来访问不同的服务。为了安全起见，我们建议使用 Doppler 来管理这些密钥，而不是直接在代码中硬编码或仅依赖 `.env` 文件。

## API 密钥列表

本项目需要以下 API 密钥：

### 三清炼丹系统

- `TAISHANG_API_KEY` - 太上老君 API 密钥（魔搭平台）
- `BAXIAN_API_KEY` - 八仙炼丹炉 API 密钥（OpenRouter）

### 稷下学宫辩论系统

- `OPENROUTER_API_KEY_MODERATOR` - 灵宝道君（主持人）
- `OPENROUTER_API_KEY_PRO_1` - 吕洞宾（正方一辩）
- `OPENROUTER_API_KEY_CON_1` - 何仙姑（反方一辩）
- `OPENROUTER_API_KEY_PRO_2` - 张果老（正方二辩）
- `OPENROUTER_API_KEY_CON_2` - 韩湘子（反方二辩）
- `OPENROUTER_API_KEY_PRO_3` - 汉钟离（正方三辩）
- `OPENROUTER_API_KEY_CON_3` - 蓝采和（反方三辩）
- `OPENROUTER_API_KEY_PRO_4` - 曹国舅（正方四辩）
- `OPENROUTER_API_KEY_CON_4` - 铁拐李（反方四辩）

### 兼容性密钥（旧版本）

- `OPENROUTER_API_KEY` - 通用 OpenRouter 密钥
- `OPENROUTER_API_KEY_1` 到 `OPENROUTER_API_KEY_4` - 旧版 OpenRouter 密钥

## 使用 Doppler 管理密钥

[Doppler](https://www.doppler.com/) 是一个安全的环境变量和密钥管理服务，可以帮助您在不同环境中管理密钥。

### 安装 Doppler CLI

```bash
# macOS
brew install dopplerhq/cli/doppler

# Ubuntu/Debian
sudo apt-get update && sudo apt-get install -y apt-transport-https ca-certificates curl gnupg
curl -sLf --retry 3 --tlsv1.2 --proto "=https" 'https://packages.doppler.com/public/cli/gpg.DE2A7741A397C129.key' | sudo apt-key add -
echo "deb https://packages.doppler.com/public/cli/deb/debian any-version main" | sudo tee /etc/apt/sources.list.d/doppler-cli.list
sudo apt-get update && sudo apt-get install -y doppler

# Windows (使用 scoop)
scoop bucket add doppler https://github.com/DopplerHQ/scoop-doppler.git
scoop install doppler
```

### 配置 Doppler 项目

1. 注册并登录 [Doppler](https://dashboard.doppler.com/)
2. 创建一个新项目，例如 `cauldron`
3. 创建不同的环境，如 `dev`、`test` 和 `prod`
4. 在每个环境中添加所需的密钥

### 在本地使用 Doppler

```bash
# 登录 Doppler
doppler login

# 设置项目和环境
doppler setup --project cauldron --config dev

# 运行带有 Doppler 环境变量的命令
doppler run -- python test_alchemy_scene.py
```

### 在 CI/CD 中使用 Doppler

在 GitHub Actions 中使用 Doppler：

```yaml
name: Run Tests

on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.10'
      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install -r requirements.txt
      - name: Install Doppler CLI
        run: |
          curl -sLf --retry 3 --tlsv1.2 --proto "=https" 'https://packages.doppler.com/public/cli/gpg.DE2A7741A397C129.key' | sudo apt-key add -
          echo "deb https://packages.doppler.com/public/cli/deb/debian any-version main" | sudo tee /etc/apt/sources.list.d/doppler-cli.list
          sudo apt-get update && sudo apt-get install -y doppler
      - name: Run tests with Doppler
        run: doppler run --token=${{ secrets.DOPPLER_TOKEN }} -- pytest
```

## 验证 API 密钥

我们提供了一个验证脚本，可以检查您的 API 密钥是否正确设置：

```bash
# 使用 .env 文件验证
python verify_api_keys.py

# 使用 Doppler 验证
doppler run -- python verify_api_keys.py
```

## 从硬编码密钥迁移

如果您之前在代码中硬编码了 API 密钥，请按照以下步骤迁移：

1. 将所有密钥添加到 Doppler 或 `.env` 文件中
2. 确保代码使用 `os.getenv()` 来获取密钥
3. 运行验证脚本确保所有密钥都可以正确获取
4. 删除代码中的硬编码密钥

## 安全最佳实践

- **永远不要**在代码中硬编码 API 密钥
- **永远不要**将 API 密钥提交到版本控制系统
- 使用 `.gitignore` 确保 `.env` 文件不会被提交
- 定期轮换 API 密钥
- 为不同环境使用不同的 API 密钥
- 使用最小权限原则，只授予必要的访问权限

## 问题排查

如果您在使用 API 密钥时遇到问题，请检查：

1. 密钥是否正确设置在 Doppler 或 `.env` 文件中
2. 代码是否正确使用 `os.getenv()` 获取密钥
3. 运行 `verify_api_keys.py` 脚本验证密钥是否可以被正确获取
4. 检查 API 服务提供商的状态页面，确认服务是否正常运行