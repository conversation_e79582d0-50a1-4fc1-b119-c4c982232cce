# -*- coding: utf-8 -*-
"""
OpenHands集成配置
管理OpenHands云服务的配置和验证策略

作者：太公心易BI系统
版本：v1.0
"""

import os
from typing import Dict, List, Any
from dataclasses import dataclass
from enum import Enum

class VerificationStrategy(Enum):
    """验证策略"""
    OPENMANUS_ONLY = "openmanus_only"           # 仅使用传统验证
    OPENHANDS_ONLY = "openhands_only"           # 仅使用Web验证
    HYBRID_BALANCED = "hybrid_balanced"         # 平衡双重验证
    HYBRID_WEB_PRIORITY = "hybrid_web_priority" # Web验证优先
    ADAPTIVE = "adaptive"                       # 自适应策略

@dataclass
class OpenHandsConfig:
    """OpenHands配置"""
    api_key: str
    base_url: str = "https://app.all-hands.dev"
    timeout: int = 300  # 5分钟超时
    max_retries: int = 3
    enable_caching: bool = True
    cache_ttl: int = 3600  # 1小时缓存
    
    # 验证任务配置
    max_concurrent_tasks: int = 3
    task_priority_weights: Dict[str, float] = None
    
    def __post_init__(self):
        if self.task_priority_weights is None:
            self.task_priority_weights = {
                "web_search": 1.0,
                "data_analysis": 0.8,
                "fact_check": 0.9
            }

@dataclass
class LingbaoVerificationConfig:
    """灵宝道君验证配置"""
    strategy: VerificationStrategy = VerificationStrategy.HYBRID_BALANCED
    
    # 置信度权重
    openmanus_weight: float = 0.6
    openhands_weight: float = 0.4
    
    # 阈值设置
    min_confidence_threshold: float = 0.5
    high_confidence_threshold: float = 0.8
    
    # 验证质量要求
    min_evidence_count: int = 2
    min_success_rate: float = 0.7
    
    # 自适应策略参数
    adaptive_learning_rate: float = 0.1
    historical_performance_weight: float = 0.3

class OpenHandsIntegrationManager:
    """OpenHands集成管理器"""
    
    def __init__(self, config: OpenHandsConfig, verification_config: LingbaoVerificationConfig):
        self.config = config
        self.verification_config = verification_config
        self.performance_history = []
        
    def get_verification_strategy(self, debate_context: Dict[str, Any]) -> VerificationStrategy:
        """根据辩论上下文确定验证策略"""
        
        if self.verification_config.strategy == VerificationStrategy.ADAPTIVE:
            return self._adaptive_strategy_selection(debate_context)
        else:
            return self.verification_config.strategy
    
    def _adaptive_strategy_selection(self, debate_context: Dict[str, Any]) -> VerificationStrategy:
        """自适应策略选择"""
        
        # 基于历史表现选择策略
        if not self.performance_history:
            return VerificationStrategy.HYBRID_BALANCED
        
        # 分析最近的验证表现
        recent_performance = self.performance_history[-10:]  # 最近10次
        
        openmanus_avg = sum(p.get('openmanus_score', 0) for p in recent_performance) / len(recent_performance)
        openhands_avg = sum(p.get('openhands_score', 0) for p in recent_performance) / len(recent_performance)
        
        # 根据表现选择策略
        if openhands_avg > openmanus_avg + 0.2:
            return VerificationStrategy.HYBRID_WEB_PRIORITY
        elif openmanus_avg > openhands_avg + 0.2:
            return VerificationStrategy.OPENMANUS_ONLY
        else:
            return VerificationStrategy.HYBRID_BALANCED
    
    def calculate_final_confidence(self, 
                                 openmanus_result: Dict[str, Any], 
                                 openhands_result: Dict[str, Any],
                                 strategy: VerificationStrategy) -> float:
        """计算最终置信度"""
        
        openmanus_confidence = openmanus_result.get('confidence', 0)
        openhands_confidence = openhands_result.get('verification_summary', {}).get('average_confidence', 0) if openhands_result else 0
        
        if strategy == VerificationStrategy.OPENMANUS_ONLY:
            return openmanus_confidence
        elif strategy == VerificationStrategy.OPENHANDS_ONLY:
            return openhands_confidence
        elif strategy == VerificationStrategy.HYBRID_WEB_PRIORITY:
            return openhands_confidence * 0.7 + openmanus_confidence * 0.3
        else:  # HYBRID_BALANCED or ADAPTIVE
            return (openmanus_confidence * self.verification_config.openmanus_weight + 
                   openhands_confidence * self.verification_config.openhands_weight)
    
    def evaluate_verification_quality(self, verification_result: Dict[str, Any]) -> Dict[str, float]:
        """评估验证质量"""
        
        quality_metrics = {}
        
        # 证据数量评分
        evidence_count = verification_result.get('web_verification', {}).get('web_evidence_count', 0)
        quality_metrics['evidence_score'] = min(evidence_count / self.verification_config.min_evidence_count, 1.0)
        
        # 成功率评分
        success_rate = verification_result.get('web_verification', {}).get('web_success_rate', 0)
        quality_metrics['success_score'] = success_rate
        
        # 置信度评分
        confidence = verification_result.get('final_confidence', 0)
        if confidence >= self.verification_config.high_confidence_threshold:
            quality_metrics['confidence_score'] = 1.0
        elif confidence >= self.verification_config.min_confidence_threshold:
            quality_metrics['confidence_score'] = 0.7
        else:
            quality_metrics['confidence_score'] = 0.3
        
        # 综合质量分数
        quality_metrics['overall_score'] = (
            quality_metrics['evidence_score'] * 0.3 +
            quality_metrics['success_score'] * 0.4 +
            quality_metrics['confidence_score'] * 0.3
        )
        
        return quality_metrics
    
    def update_performance_history(self, verification_result: Dict[str, Any]):
        """更新性能历史"""
        
        quality_metrics = self.evaluate_verification_quality(verification_result)
        
        performance_record = {
            'timestamp': verification_result.get('timestamp'),
            'openmanus_score': verification_result.get('original_confidence', 0),
            'openhands_score': verification_result.get('web_verification', {}).get('web_confidence', 0),
            'overall_quality': quality_metrics['overall_score'],
            'final_confidence': verification_result.get('final_confidence', 0)
        }
        
        self.performance_history.append(performance_record)
        
        # 保持历史记录在合理范围内
        if len(self.performance_history) > 100:
            self.performance_history = self.performance_history[-50:]
    
    def get_verification_recommendations(self, debate_context: Dict[str, Any]) -> Dict[str, Any]:
        """获取验证建议"""
        
        strategy = self.get_verification_strategy(debate_context)
        
        recommendations = {
            'strategy': strategy.value,
            'priority_tasks': [],
            'expected_duration': 0,
            'confidence_expectations': {}
        }
        
        # 基于策略生成任务优先级
        if strategy in [VerificationStrategy.OPENHANDS_ONLY, VerificationStrategy.HYBRID_WEB_PRIORITY]:
            recommendations['priority_tasks'] = ['web_search', 'fact_check', 'data_analysis']
            recommendations['expected_duration'] = 180  # 3分钟
        else:
            recommendations['priority_tasks'] = ['data_analysis', 'web_search', 'fact_check']
            recommendations['expected_duration'] = 240  # 4分钟
        
        # 置信度预期
        if self.performance_history:
            recent_avg = sum(p['final_confidence'] for p in self.performance_history[-5:]) / min(5, len(self.performance_history))
            recommendations['confidence_expectations'] = {
                'expected_range': [max(0, recent_avg - 0.1), min(1.0, recent_avg + 0.1)],
                'historical_average': recent_avg
            }
        
        return recommendations

# 默认配置
def get_default_openhands_config() -> OpenHandsConfig:
    """获取默认OpenHands配置"""
    return OpenHandsConfig(
        api_key=os.getenv('OPENHANDS_API_KEY', 'hA04ZDQbdKUbBCqmN5ZPFkcdK0xsKLwX'),
        base_url=os.getenv('OPENHANDS_BASE_URL', 'https://app.all-hands.dev'),
        timeout=int(os.getenv('OPENHANDS_TIMEOUT', '300')),
        max_retries=int(os.getenv('OPENHANDS_MAX_RETRIES', '3'))
    )

def get_default_verification_config() -> LingbaoVerificationConfig:
    """获取默认验证配置"""
    strategy_name = os.getenv('LINGBAO_VERIFICATION_STRATEGY', 'hybrid_balanced')
    strategy = VerificationStrategy(strategy_name)
    
    return LingbaoVerificationConfig(
        strategy=strategy,
        openmanus_weight=float(os.getenv('OPENMANUS_WEIGHT', '0.6')),
        openhands_weight=float(os.getenv('OPENHANDS_WEIGHT', '0.4')),
        min_confidence_threshold=float(os.getenv('MIN_CONFIDENCE_THRESHOLD', '0.5')),
        high_confidence_threshold=float(os.getenv('HIGH_CONFIDENCE_THRESHOLD', '0.8'))
    )

# 配置实例
DEFAULT_OPENHANDS_CONFIG = get_default_openhands_config()
DEFAULT_VERIFICATION_CONFIG = get_default_verification_config()
DEFAULT_INTEGRATION_MANAGER = OpenHandsIntegrationManager(
    DEFAULT_OPENHANDS_CONFIG, 
    DEFAULT_VERIFICATION_CONFIG
)
