services:
  my-ibga:
    image: heshiming/ibga
    restart: unless-stopped
    environment:
      - TERM=xterm
      - IB_USERNAME=binfan846
      - IB_PASSWORD=shared11
      - IB_REGION=America
      - IB_TIMEZONE=America/New York
      - IB_LOGINTAB=IB API
      - IB_LOGINTYPE=Paper Trading
      - IB_LOGOFF=11:55 PM
      - IB_APILOG=data
      - IB_LOGLEVEL=Error
    volumes:
      - ./run/program:/home/<USER>
      - ./run/settings:/home/<USER>
    ports:
      - "15800:5800"
      - "4000:9000"
  nginx-proxy:
    image: nginx:latest
    container_name: nginx_ib_proxy # Give your Nginx container a name
    restart: unless-stopped
    ports:
      - "9999:9999" # Map host's 9999 to container's 9999 for Nginx
    volumes:
      - ./nginx-conf/nginx.conf:/etc/nginx/nginx.conf:ro # Mount Nginx config read-only
      # If you need SSL, mount your certificate files here too:
      # - ./nginx-conf/certs:/etc/nginx/certs:ro
    depends_on:
      - my-ibga # Ensures my-ibga starts before nginx-proxy