# <<<<<<<<<<<<<<< 注意这里：events 块必须在最外层，与 stream 块平级 >>>>>>>>>>>>>>>
events {
    worker_connections 1024; # 通常保持默认或根据服务器性能调整
}
# <<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<

stream {
    # 定义上游服务器组，用于TCP转发
    upstream ib_gateway_backend_tcp {
        # 'my-ibga' 是 docker-compose.yml 中 IB Gateway 服务的名称
        # '4012' 是 IB Gateway 容器内部监听的 API 端口
        server my-ibga:4000;
    }

    server {
        # 监听宿主机的 9999 端口
        listen 9999;

        # 将所有到达 9999 端口的TCP连接转发到 upstream 定义的后端
        proxy_pass ib_gateway_backend_tcp;

        # 可选：设置TCP连接超时
        proxy_timeout 60s;
        proxy_connect_timeout 60s;
    }
}

# 如果你不需要 Nginx 处理 HTTP 流量（例如健康检查页面），可以删除整个 http { ... } 块。
# 如果需要，则 http 块也应该和 events、stream 块平级。
# 示例：
# http {
#     # ... (你的HTTP配置)
# }