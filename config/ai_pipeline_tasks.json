{"cauldron_development_pipeline": {"description": "Cauldron项目开发流水线任务配置", "tasks": [{"id": "health_check", "type": "api_test", "priority": "urgent", "description": "系统健康检查", "prompt": "检查N8N、数据库和关键服务的运行状态", "timeout": 60}, {"id": "n8n_analysis", "type": "analysis", "priority": "high", "description": "N8N工作流深度分析", "prompt": "分析当前N8N配置，识别瓶颈和优化机会，提出改进方案", "dependencies": ["health_check"], "timeout": 300}, {"id": "api_integration_test", "type": "api_test", "priority": "high", "description": "API集成测试", "prompt": "测试RapidAPI、HuggingFace等外部API的连接性和响应时间", "timeout": 120}, {"id": "sanqing_agent_design", "type": "architecture", "priority": "normal", "description": "三清智能体架构设计", "prompt": "设计三清(元始天尊、灵宝天尊、道德天尊)智能体的架构和交互模式", "dependencies": ["n8n_analysis"], "timeout": 600}, {"id": "baxian_prototype", "type": "code_snippet", "priority": "normal", "description": "八仙角色原型实现", "prompt": "为八仙中的一个角色(如铁拐李)创建基础的AI智能体代码", "dependencies": ["sanqing_agent_design"], "timeout": 180}, {"id": "vector_db_integration", "type": "integration", "priority": "high", "description": "向量数据库集成", "prompt": "实现<PERSON><PERSON><PERSON>/<PERSON><PERSON><PERSON><PERSON>与AutoGen的完整集成方案，包括数据流和查询优化", "dependencies": ["n8n_analysis"], "timeout": 900}, {"id": "mastodon_connector", "type": "code_snippet", "priority": "normal", "description": "Mastodon连接器", "prompt": "创建Mastodon API连接器，实现智能体的社交媒体发布功能", "timeout": 240}, {"id": "streamlit_dashboard", "type": "quick_fix", "priority": "low", "description": "Streamlit仪表板优化", "prompt": "优化Streamlit健康检查模块，添加实时监控功能", "dependencies": ["health_check"], "timeout": 120}]}, "emergency_tasks": {"description": "紧急任务模板", "tasks": [{"id": "system_debug", "type": "debug", "priority": "urgent", "description": "系统调试", "prompt": "快速诊断和修复系统故障", "timeout": 300}, {"id": "hotfix", "type": "quick_fix", "priority": "urgent", "description": "热修复", "prompt": "紧急修复关键功能问题", "timeout": 180}]}, "routine_maintenance": {"description": "日常维护任务", "tasks": [{"id": "daily_health_check", "type": "api_test", "priority": "normal", "description": "每日健康检查", "prompt": "执行系统日常健康检查，生成状态报告", "timeout": 120}, {"id": "performance_analysis", "type": "analysis", "priority": "low", "description": "性能分析", "prompt": "分析系统性能指标，识别优化机会", "timeout": 600}]}}