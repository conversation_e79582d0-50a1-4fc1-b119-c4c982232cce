{"name": "RSS到三脑架构完整工作流", "nodes": [{"parameters": {"rule": {"interval": [{"field": "minutes", "minutesInterval": 30}]}}, "id": "rss-cron-trigger", "name": "RSS定时采集触发器", "type": "n8n-nodes-base.scheduleTrigger", "typeVersion": 1.1, "position": [240, 300], "notes": "每30分钟触发RSS采集"}, {"parameters": {"url": "https://feeds.finance.yahoo.com/rss/2.0/headline", "options": {}}, "id": "yahoo-finance-rss", "name": "Yahoo财经RSS", "type": "n8n-nodes-base.rssFeedRead", "typeVersion": 1, "position": [460, 200]}, {"parameters": {"url": "https://techcrunch.com/feed/", "options": {}}, "id": "techcrunch-rss", "name": "TechCrunch RSS", "type": "n8n-nodes-base.rssFeedRead", "typeVersion": 1, "position": [460, 300]}, {"parameters": {"url": "https://cointelegraph.com/rss", "options": {}}, "id": "cointelegraph-rss", "name": "CoinTelegraph RSS", "type": "n8n-nodes-base.rssFeedRead", "typeVersion": 1, "position": [460, 400]}, {"parameters": {"jsCode": "// RSS数据清洗和标准化\nconst items = $input.all();\nconst processedItems = [];\n\nfor (const item of items) {\n  const data = item.json;\n  \n  // 生成唯一ID\n  const crypto = require('crypto');\n  const articleId = crypto.createHash('md5')\n    .update(data.title + data.link)\n    .digest('hex');\n  \n  // 确定分类\n  let category = 'general';\n  if (data.link.includes('finance.yahoo.com')) category = 'finance';\n  if (data.link.includes('techcrunch.com')) category = 'technology';\n  if (data.link.includes('cointelegraph.com')) category = 'cryptocurrency';\n  \n  // 清洗内容\n  const cleanContent = data.contentSnippet || data.content || '';\n  \n  const processedItem = {\n    article_id: articleId,\n    title: data.title,\n    content: cleanContent,\n    link: data.link,\n    category: category,\n    source_url: data.link.split('/')[2], // 提取域名\n    published_at: new Date(data.pubDate || data.isoDate),\n    collected_at: new Date(),\n    raw_data: data,\n    processed: false,\n    processing_status: 'collected'\n  };\n  \n  processedItems.push({ json: processedItem });\n}\n\nreturn processedItems;"}, "id": "data-processing", "name": "数据处理和标准化", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [680, 300], "notes": "清洗RSS数据并标准化格式"}, {"parameters": {"operation": "insertMany", "collection": "rss_raw_articles", "options": {"upsert": true}}, "id": "mongodb-insert", "name": "MongoDB存储原始数据", "type": "n8n-nodes-base.mongoDb", "typeVersion": 1, "position": [900, 300], "credentials": {"mongoDb": {"id": "mongodb-credentials", "name": "MongoDB Atlas"}}, "notes": "存储到MongoDB情报脑"}, {"parameters": {"httpMethod": "POST", "path": "trigger-vectorization", "options": {}}, "id": "vectorization-webhook", "name": "触发向量化Webhook", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [1120, 300], "notes": "通知向量化工作流"}, {"parameters": {"rule": {"interval": [{"field": "hours", "hoursInterval": 1}]}}, "id": "vectorization-cron", "name": "向量化定时触发器", "type": "n8n-nodes-base.scheduleTrigger", "typeVersion": 1.1, "position": [240, 600], "notes": "每小时检查未处理数据"}, {"parameters": {"httpMethod": "POST", "path": "trigger-vectorization", "options": {}}, "id": "vectorization-webhook-trigger", "name": "向量化Webhook触发器", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 700], "notes": "接收向量化触发信号"}, {"parameters": {"operation": "find", "collection": "rss_raw_articles", "query": "{ \"processed\": false, \"processing_status\": \"collected\" }", "limit": 50}, "id": "mongodb-query-unprocessed", "name": "查询未处理数据", "type": "n8n-nodes-base.mongoDb", "typeVersion": 1, "position": [460, 650], "credentials": {"mongoDb": {"id": "mongodb-credentials", "name": "MongoDB Atlas"}}, "notes": "从MongoDB查询未处理的文章"}, {"parameters": {"conditions": {"number": [{"value1": "={{ $json.length }}", "operation": "larger", "value2": 0}]}}, "id": "check-has-data", "name": "检查是否有数据", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [680, 650], "notes": "检查是否有未处理的数据"}, {"parameters": {"jsCode": "// 文本处理和分析\nconst items = $input.all();\nconst processedItems = [];\n\nfor (const item of items) {\n  const data = item.json;\n  \n  // 文本预处理\n  const fullText = `${data.title} ${data.content}`;\n  const cleanText = fullText.replace(/<[^>]*>/g, '').trim(); // 移除HTML标签\n  \n  // 简单情感分析 (实际应用中可调用专业API)\n  const positiveWords = ['good', 'great', 'excellent', 'positive', 'up', 'rise', 'gain'];\n  const negativeWords = ['bad', 'terrible', 'negative', 'down', 'fall', 'loss', 'crash'];\n  \n  let sentiment = 0;\n  const words = cleanText.toLowerCase().split(/\\s+/);\n  \n  for (const word of words) {\n    if (positiveWords.includes(word)) sentiment += 0.1;\n    if (negativeWords.includes(word)) sentiment -= 0.1;\n  }\n  \n  sentiment = Math.max(-1, Math.min(1, sentiment)); // 限制在-1到1之间\n  \n  // 关键词提取 (简化版)\n  const keywords = [];\n  const keywordPatterns = {\n    'bitcoin': /bitcoin|btc/gi,\n    'ethereum': /ethereum|eth/gi,\n    'stock': /stock|share|equity/gi,\n    'market': /market|trading/gi,\n    'ai': /artificial intelligence|ai|machine learning/gi\n  };\n  \n  for (const [keyword, pattern] of Object.entries(keywordPatterns)) {\n    if (pattern.test(cleanText)) {\n      keywords.push(keyword);\n    }\n  }\n  \n  // 重要性评分\n  let importanceScore = 0.5; // 基础分数\n  if (keywords.length > 0) importanceScore += 0.2;\n  if (Math.abs(sentiment) > 0.3) importanceScore += 0.2;\n  if (cleanText.length > 500) importanceScore += 0.1;\n  \n  const processedItem = {\n    ...data,\n    processed_text: cleanText.substring(0, 1000), // 限制长度\n    sentiment: sentiment,\n    keywords: keywords.join(','),\n    importance_score: Math.min(1, importanceScore),\n    processing_status: 'analyzed',\n    analyzed_at: new Date()\n  };\n  \n  processedItems.push({ json: processedItem });\n}\n\nreturn processedItems;"}, "id": "text-analysis", "name": "文本分析处理", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [900, 650], "notes": "执行情感分析和关键词提取"}, {"parameters": {"url": "http://localhost:8000/api/vectorize", "sendBody": true, "bodyContentType": "json", "jsonBody": "={{ { \"text\": $json.processed_text, \"metadata\": { \"article_id\": $json.article_id, \"category\": $json.category, \"sentiment\": $json.sentiment } } }}", "options": {}}, "id": "vectorization-api", "name": "调用向量化API", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [1120, 650], "notes": "调用本地向量化服务"}, {"parameters": {"jsCode": "// 准备Zilliz插入数据\nconst items = $input.all();\nconst zillizData = [];\n\nfor (let i = 0; i < items.length; i++) {\n  const originalData = items[i].json;\n  const vectorData = $('调用向量化API').item(i).json;\n  \n  const zillizItem = {\n    id: originalData.article_id,\n    document: originalData.processed_text,\n    embedding: vectorData.embedding, // 从向量化API获取\n    sentiment: originalData.sentiment,\n    topics: originalData.keywords,\n    importance_score: originalData.importance_score,\n    published_ts: Math.floor(new Date(originalData.published_at).getTime() / 1000),\n    source_category: originalData.category,\n    mongo_ref: originalData._id\n  };\n  \n  zillizData.push({ json: zillizItem });\n}\n\nreturn zillizData;"}, "id": "prepare-zilliz-data", "name": "准备Zilliz数据", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1340, 650], "notes": "准备向量数据格式"}, {"parameters": {"url": "{{ $env.ZILLIZ_ENDPOINT }}/v1/vector/insert", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Authorization", "value": "Bearer {{ $env.ZILLIZ_TOKEN }}"}, {"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "bodyContentType": "json", "jsonBody": "={{ { \"collectionName\": \"rss_semantic_events_v1\", \"data\": [$json] } }}", "options": {}}, "id": "zilliz-insert", "name": "Zilliz向量存储", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [1560, 650], "notes": "存储到Zilliz神经脑"}, {"parameters": {"operation": "updateMany", "collection": "rss_raw_articles", "updateKey": "article_id", "fieldsUi": {"fieldPairs": [{"name": "processed", "value": true}, {"name": "processing_status", "value": "vectorized"}, {"name": "vectorized_at", "value": "={{ new Date() }}"}]}}, "id": "mongodb-update-status", "name": "更新处理状态", "type": "n8n-nodes-base.mongoDb", "typeVersion": 1, "position": [1780, 650], "credentials": {"mongoDb": {"id": "mongodb-credentials", "name": "MongoDB Atlas"}}, "notes": "标记为已处理"}], "connections": {"RSS定时采集触发器": {"main": [[{"node": "Yahoo财经RSS", "type": "main", "index": 0}, {"node": "TechCrunch RSS", "type": "main", "index": 0}, {"node": "CoinTelegraph RSS", "type": "main", "index": 0}]]}, "Yahoo财经RSS": {"main": [[{"node": "数据处理和标准化", "type": "main", "index": 0}]]}, "TechCrunch RSS": {"main": [[{"node": "数据处理和标准化", "type": "main", "index": 0}]]}, "CoinTelegraph RSS": {"main": [[{"node": "数据处理和标准化", "type": "main", "index": 0}]]}, "数据处理和标准化": {"main": [[{"node": "MongoDB存储原始数据", "type": "main", "index": 0}]]}, "MongoDB存储原始数据": {"main": [[{"node": "触发向量化Webhook", "type": "main", "index": 0}]]}, "向量化定时触发器": {"main": [[{"node": "查询未处理数据", "type": "main", "index": 0}]]}, "向量化Webhook触发器": {"main": [[{"node": "查询未处理数据", "type": "main", "index": 0}]]}, "查询未处理数据": {"main": [[{"node": "检查是否有数据", "type": "main", "index": 0}]]}, "检查是否有数据": {"main": [[{"node": "文本分析处理", "type": "main", "index": 0}]]}, "文本分析处理": {"main": [[{"node": "调用向量化API", "type": "main", "index": 0}]]}, "调用向量化API": {"main": [[{"node": "准备Zilliz数据", "type": "main", "index": 0}]]}, "准备Zilliz数据": {"main": [[{"node": "Zilliz向量存储", "type": "main", "index": 0}]]}, "Zilliz向量存储": {"main": [[{"node": "更新处理状态", "type": "main", "index": 0}]]}}, "settings": {"executionOrder": "v1"}, "staticData": null, "tags": [], "triggerCount": 2, "updatedAt": "2024-01-15T10:00:00.000Z", "versionId": "1"}