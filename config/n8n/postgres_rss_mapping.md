# N8N PostgreSQL RSS文章映射配置

## 当前数据库表结构 - 四字段简化设计

根据炼妖壶项目的四字段架构，`rss_articles`表结构如下：

```sql
Table "public.rss_articles"
     Column     |           Type           | Collation | Nullable | Default
----------------+--------------------------+-----------+----------+---------
 article_id     | character varying(100)   |           | not null |
 title          | text                     |           | not null |
 published_time | timestamp with time zone |           |          |
 processed      | boolean                  |           |          | false
```

**设计理念：**
- 严格按照四字段设计，避免冗余
- 专注核心功能：标识、标题、时间、状态
- URL等信息可以从article_id中推导或在向量表中存储

## N8N字段映射配置 - 四字段设计

### 核心字段映射（仅四个字段）

1. **article_id** (主要标识符)
   - 类型: VARCHAR(100)
   - 必需: 是
   - 唯一: 是
   - 建议值: `{{ $json.guid || $json.id || ($json.link + '_' + new Date($json.pubDate).getTime()) }}`

2. **title** (文章标题)
   - 类型: TEXT
   - 必需: 是
   - 建议值: `{{ $json.title }}`

3. **published_time** (发布时间)
   - 类型: TIMESTAMP WITH TIME ZONE
   - 必需: 否
   - 建议值: `{{ $json.pubDate }}`

4. **processed** (处理状态)
   - 类型: BOOLEAN
   - 默认值: false
   - 建议值: `false`

### 注意事项

- **URL字段已移除**：URL信息可以从article_id中推导，或在需要时存储在向量表中
- **简化设计**：专注核心功能，避免数据冗余
- **向量存储**：详细的元数据存储在`rss_article_vectors`表中

## N8N节点配置示例

### PostgreSQL Insert节点配置 - 四字段版本

```json
{
  "operation": "insert",
  "schema": "public",
  "table": "rss_articles",
  "columns": "article_id,title,published_time,processed",
  "additionalFields": {
    "mode": "upsert",
    "upsertKeyColumns": "article_id"
  },
  "values": {
    "article_id": "{{ $json.guid || $json.id || ($json.link + '_' + new Date($json.pubDate).getTime()) }}",
    "title": "{{ $json.title }}",
    "published_time": "{{ $json.pubDate }}",
    "processed": false
  }
}
```

### 数据预处理建议 - 四字段版本

在插入PostgreSQL之前，建议添加一个Function节点进行数据清理：

```javascript
// 数据清理和标准化 - 四字段设计
const items = $input.all();

return items.map(item => {
  const data = item.json;

  // 生成唯一的article_id（可包含URL信息用于后续推导）
  const articleId = data.guid || data.id ||
    `${data.link}_${new Date(data.pubDate).getTime()}`;

  // 清理标题
  const title = (data.title || '').trim().substring(0, 500);

  // 标准化时间格式
  const publishedTime = data.pubDate ?
    new Date(data.pubDate).toISOString() : null;

  return {
    json: {
      article_id: articleId,
      title: title,
      published_time: publishedTime,
      processed: false
    }
  };
});
```

## 错误处理

### 常见问题及解决方案

1. **重复article_id错误**
   - 使用UPSERT模式: `ON CONFLICT (article_id) DO UPDATE SET ...`
   - 或使用`ON CONFLICT (article_id) DO NOTHING`

2. **时间格式错误**
   - 确保时间格式为ISO 8601: `YYYY-MM-DDTHH:mm:ss.sssZ`
   - 使用JavaScript的`new Date().toISOString()`

3. **标题过长**
   - 限制标题长度: `title.substring(0, 500)`

## 测试数据示例 - 四字段版本

```json
{
  "article_id": "sample_rss_001",
  "title": "测试RSS文章标题",
  "published_time": "2025-07-06T10:00:00Z",
  "processed": false
}
```

## 四字段设计的优势

1. **简洁性**：只保留核心必需字段，避免数据冗余
2. **性能**：更少的字段意味着更快的查询和更小的存储空间
3. **专注性**：专注于RSS文章的核心属性
4. **扩展性**：详细信息可以存储在向量表或其他专门的表中
5. **一致性**：与炼妖壶项目的整体架构保持一致
