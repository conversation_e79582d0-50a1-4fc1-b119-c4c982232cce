{"timestamp": "2025-07-12T14:15:19.433210", "auth_methods_tested": {"no_auth": {"workflows": {"status_code": 401, "accessible": false, "requires_auth": true, "response_size": 45, "headers": {"Date": "Sat, 12 Jul 2025 06:15:21 GMT", "Content-Type": "application/json; charset=utf-8", "Content-Length": "45", "Connection": "keep-alive", "etag": "W/\"2d-iq+yOyJ5MwqLFni8cg0qdshcAFs\"", "vary": "Accept-Encoding, origin, access-control-request-method, access-control-request-headers", "x-proxied-host": "http://*************", "x-proxied-replica": "erixc2b0-d7n4e", "x-proxied-path": "/api/v1/workflows", "link": "<https://huggingface.co/spaces/houzhongxu/n8n-free>;rel=\"canonical\"", "x-request-id": "u4GN40", "access-control-allow-credentials": "true"}}, "executions": {"status_code": 401, "accessible": false, "requires_auth": true, "response_size": 45, "headers": {"Date": "Sat, 12 Jul 2025 06:15:22 GMT", "Content-Type": "application/json; charset=utf-8", "Content-Length": "45", "Connection": "keep-alive", "etag": "W/\"2d-iq+yOyJ5MwqLFni8cg0qdshcAFs\"", "vary": "Accept-Encoding, origin, access-control-request-method, access-control-request-headers", "x-proxied-host": "http://*************", "x-proxied-replica": "erixc2b0-d7n4e", "x-proxied-path": "/api/v1/executions", "link": "<https://huggingface.co/spaces/houzhongxu/n8n-free>;rel=\"canonical\"", "x-request-id": "MIzj8l", "access-control-allow-credentials": "true"}}, "workflow_detail": {"status_code": 401, "accessible": false, "requires_auth": true, "response_size": 45, "headers": {"Date": "Sat, 12 Jul 2025 06:15:22 GMT", "Content-Type": "application/json; charset=utf-8", "Content-Length": "45", "Connection": "keep-alive", "etag": "W/\"2d-iq+yOyJ5MwqLFni8cg0qdshcAFs\"", "vary": "Accept-Encoding, origin, access-control-request-method, access-control-request-headers", "x-proxied-host": "http://*************", "x-proxied-replica": "erixc2b0-d7n4e", "x-proxied-path": "/api/v1/workflows/5Ibi4vJZjSB0ZaTt", "link": "<https://huggingface.co/spaces/houzhongxu/n8n-free>;rel=\"canonical\"", "x-request-id": "Oedi3T", "access-control-allow-credentials": "true"}}, "workflow_execute": {"status_code": 404, "accessible": true, "requires_auth": false, "response_size": 23, "headers": {"Date": "Sat, 12 Jul 2025 06:15:23 GMT", "Content-Type": "application/json; charset=utf-8", "Content-Length": "23", "Connection": "keep-alive", "etag": "W/\"17-m4r1LcV4pvVDhRunAabPoDm1PiI\"", "vary": "Accept-Encoding, origin, access-control-request-method, access-control-request-headers", "x-proxied-host": "http://*************", "x-proxied-replica": "erixc2b0-d7n4e", "x-proxied-path": "/api/v1/workflows/5Ibi4vJZjSB0ZaTt/execute", "link": "<https://huggingface.co/spaces/houzhongxu/n8n-free>;rel=\"canonical\"", "x-request-id": "ZEr-9m", "access-control-allow-credentials": "true"}}, "health": {"status_code": 200, "accessible": true, "requires_auth": false, "response_size": 15, "headers": {"Date": "Sat, 12 Jul 2025 06:15:24 GMT", "Content-Type": "application/json; charset=utf-8", "Content-Length": "15", "Connection": "keep-alive", "etag": "W/\"f-VaSQ4oDUiZblZNAEkkN+sX+q3Sg\"", "x-proxied-host": "http://*************", "x-proxied-replica": "erixc2b0-d7n4e", "x-proxied-path": "/healthz", "link": "<https://huggingface.co/spaces/houzhongxu/n8n-free>;rel=\"canonical\"", "x-request-id": "AHjT0G", "vary": "origin, access-control-request-method, access-control-request-headers", "access-control-allow-credentials": "true"}}, "webhook_prod": {"status_code": 404, "accessible": true, "requires_auth": false, "response_size": 722, "headers": {"Date": "Sat, 12 Jul 2025 06:15:26 GMT", "Content-Type": "application/json; charset=utf-8", "Content-Length": "722", "Connection": "keep-alive", "etag": "W/\"2d2-v5YpexeUvG2HyZezlf8wrdTF/tc\"", "vary": "Accept-Encoding, origin, access-control-request-method, access-control-request-headers", "x-proxied-host": "http://*************", "x-proxied-replica": "erixc2b0-d7n4e", "x-proxied-path": "/webhook/ce40f698-832e-475a-a3c7-0895c9e2e90b", "link": "<https://huggingface.co/spaces/houzhongxu/n8n-free>;rel=\"canonical\"", "x-request-id": "sfa4xk", "access-control-allow-credentials": "true"}}, "webhook_test": {"status_code": 404, "accessible": true, "requires_auth": false, "response_size": 876, "headers": {"Date": "Sat, 12 Jul 2025 06:15:27 GMT", "Content-Type": "application/json; charset=utf-8", "Content-Length": "876", "Connection": "keep-alive", "etag": "W/\"36c-nZUzEpsYiRVLGSodFlfRo08hDas\"", "vary": "Accept-Encoding, origin, access-control-request-method, access-control-request-headers", "x-proxied-host": "http://*************", "x-proxied-replica": "erixc2b0-d7n4e", "x-proxied-path": "/webhook-test/ce40f698-832e-475a-a3c7-0895c9e2e90b", "link": "<https://huggingface.co/spaces/houzhongxu/n8n-free>;rel=\"canonical\"", "x-request-id": "8cplh4", "access-control-allow-credentials": "true"}}}, "basic_auth_admin": {"method": "basic_auth", "username": "admin", "status_code": 401, "success": false, "requires_auth": true}, "basic_auth_n8n": {"method": "basic_auth", "username": "n8n", "status_code": 401, "success": false, "requires_auth": true}, "basic_auth_user": {"method": "basic_auth", "username": "user", "status_code": 401, "success": false, "requires_auth": true}, "api_key_header": {"X-N8N-API-KEY_n8n_api_key_placeholder": {"header": "X-N8N-API-KEY", "key": "n8n_api_key_placeholder", "status_code": 401, "success": false}, "X-N8N-API-KEY_test_api_key": {"header": "X-N8N-API-KEY", "key": "test_api_key", "status_code": 401, "success": false}, "X-N8N-API-KEY_demo_key": {"header": "X-N8N-API-KEY", "key": "demo_key", "status_code": 401, "success": false}, "Authorization_n8n_api_key_placeholder": {"header": "Authorization", "key": "n8n_api_key_placeholder", "status_code": 401, "success": false}, "Authorization_test_api_key": {"header": "Authorization", "key": "test_api_key", "status_code": 401, "success": false}, "Authorization_demo_key": {"header": "Authorization", "key": "demo_key", "status_code": 401, "success": false}, "X-API-KEY_n8n_api_key_placeholder": {"header": "X-API-KEY", "key": "n8n_api_key_placeholder", "status_code": 401, "success": false}, "X-API-KEY_test_api_key": {"header": "X-API-KEY", "key": "test_api_key", "status_code": 401, "success": false}, "X-API-KEY_demo_key": {"header": "X-API-KEY", "key": "demo_key", "status_code": 401, "success": false}, "API-KEY_n8n_api_key_placeholder": {"header": "API-KEY", "key": "n8n_api_key_placeholder", "status_code": 401, "success": false}, "API-KEY_test_api_key": {"header": "API-KEY", "key": "test_api_key", "status_code": 401, "success": false}, "API-KEY_demo_key": {"header": "API-KEY", "key": "demo_key", "status_code": 401, "success": false}}, "bearer_token": {"bearer_token_placeholder": {"token": "bearer_token_placeholder", "status_code": 401, "success": false}, "n8n_token": {"token": "n8n_token", "status_code": 401, "success": false}, "demo_token": {"token": "demo_token", "status_code": 401, "success": false}}, "session_auth": {"method": "session_auth", "login_page_accessible": true, "status_code": 200, "note": "需要通过Web界面登录获取会话"}}, "endpoint_access": {"workflows": {"url": "https://houzhongxu-n8n-free.hf.space/api/v1/workflows", "get_status": 401, "get_accessible": true, "post_status": null, "requires_auth": true, "response_headers": {"Date": "Sat, 12 Jul 2025 06:15:46 GMT", "Content-Type": "application/json; charset=utf-8", "Content-Length": "45", "Connection": "keep-alive", "etag": "W/\"2d-iq+yOyJ5MwqLFni8cg0qdshcAFs\"", "vary": "Accept-Encoding, origin, access-control-request-method, access-control-request-headers", "x-proxied-host": "http://*************", "x-proxied-replica": "erixc2b0-d7n4e", "x-proxied-path": "/api/v1/workflows", "link": "<https://huggingface.co/spaces/houzhongxu/n8n-free>;rel=\"canonical\"", "x-request-id": "HdF0v8", "access-control-allow-credentials": "true"}}, "executions": {"url": "https://houzhongxu-n8n-free.hf.space/api/v1/executions", "get_status": 401, "get_accessible": true, "post_status": null, "requires_auth": true, "response_headers": {"Date": "Sat, 12 Jul 2025 06:15:47 GMT", "Content-Type": "application/json; charset=utf-8", "Content-Length": "45", "Connection": "keep-alive", "etag": "W/\"2d-iq+yOyJ5MwqLFni8cg0qdshcAFs\"", "vary": "Accept-Encoding, origin, access-control-request-method, access-control-request-headers", "x-proxied-host": "http://*************", "x-proxied-replica": "erixc2b0-d7n4e", "x-proxied-path": "/api/v1/executions", "link": "<https://huggingface.co/spaces/houzhongxu/n8n-free>;rel=\"canonical\"", "x-request-id": "3RABJ-", "access-control-allow-credentials": "true"}}, "workflow_detail": {"url": "https://houzhongxu-n8n-free.hf.space/api/v1/workflows/5Ibi4vJZjSB0ZaTt", "get_status": 401, "get_accessible": true, "post_status": null, "requires_auth": true, "response_headers": {"Date": "Sat, 12 Jul 2025 06:15:48 GMT", "Content-Type": "application/json; charset=utf-8", "Content-Length": "45", "Connection": "keep-alive", "etag": "W/\"2d-iq+yOyJ5MwqLFni8cg0qdshcAFs\"", "vary": "Accept-Encoding, origin, access-control-request-method, access-control-request-headers", "x-proxied-host": "http://*************", "x-proxied-replica": "erixc2b0-d7n4e", "x-proxied-path": "/api/v1/workflows/5Ibi4vJZjSB0ZaTt", "link": "<https://huggingface.co/spaces/houzhongxu/n8n-free>;rel=\"canonical\"", "x-request-id": "7Tiv6j", "access-control-allow-credentials": "true"}}, "workflow_execute": {"url": "https://houzhongxu-n8n-free.hf.space/api/v1/workflows/5Ibi4vJZjSB0ZaTt/execute", "get_status": 404, "get_accessible": false, "post_status": null, "requires_auth": false, "response_headers": {"Date": "Sat, 12 Jul 2025 06:15:49 GMT", "Content-Type": "application/json; charset=utf-8", "Content-Length": "23", "Connection": "keep-alive", "etag": "W/\"17-m4r1LcV4pvVDhRunAabPoDm1PiI\"", "vary": "Accept-Encoding, origin, access-control-request-method, access-control-request-headers", "x-proxied-host": "http://*************", "x-proxied-replica": "erixc2b0-d7n4e", "x-proxied-path": "/api/v1/workflows/5Ibi4vJZjSB0ZaTt/execute", "link": "<https://huggingface.co/spaces/houzhongxu/n8n-free>;rel=\"canonical\"", "x-request-id": "KnMrgz", "access-control-allow-credentials": "true"}}, "health": {"url": "https://houzhongxu-n8n-free.hf.space/healthz", "get_status": 200, "get_accessible": true, "post_status": null, "requires_auth": false, "response_headers": {"Date": "Sat, 12 Jul 2025 06:15:51 GMT", "Content-Type": "application/json; charset=utf-8", "Content-Length": "15", "Connection": "keep-alive", "etag": "W/\"f-VaSQ4oDUiZblZNAEkkN+sX+q3Sg\"", "x-proxied-host": "http://*************", "x-proxied-replica": "erixc2b0-d7n4e", "x-proxied-path": "/healthz", "link": "<https://huggingface.co/spaces/houzhongxu/n8n-free>;rel=\"canonical\"", "x-request-id": "osGiKP", "vary": "origin, access-control-request-method, access-control-request-headers", "access-control-allow-credentials": "true"}}, "webhook_prod": {"url": "https://houzhongxu-n8n-free.hf.space/webhook/ce40f698-832e-475a-a3c7-0895c9e2e90b", "get_status": 404, "get_accessible": false, "post_status": 200, "requires_auth": false, "response_headers": {"Date": "Sat, 12 Jul 2025 06:15:52 GMT", "Content-Type": "application/json; charset=utf-8", "Content-Length": "722", "Connection": "keep-alive", "etag": "W/\"2d2-v5YpexeUvG2HyZezlf8wrdTF/tc\"", "vary": "Accept-Encoding, origin, access-control-request-method, access-control-request-headers", "x-proxied-host": "http://*************", "x-proxied-replica": "erixc2b0-d7n4e", "x-proxied-path": "/webhook/ce40f698-832e-475a-a3c7-0895c9e2e90b", "link": "<https://huggingface.co/spaces/houzhongxu/n8n-free>;rel=\"canonical\"", "x-request-id": "GyZhfi", "access-control-allow-credentials": "true"}}, "webhook_test": {"url": "https://houzhongxu-n8n-free.hf.space/webhook-test/ce40f698-832e-475a-a3c7-0895c9e2e90b", "get_status": 404, "get_accessible": false, "post_status": null, "requires_auth": false, "response_headers": {"Date": "Sat, 12 Jul 2025 06:15:54 GMT", "Content-Type": "application/json; charset=utf-8", "Content-Length": "876", "Connection": "keep-alive", "etag": "W/\"36c-nZUzEpsYiRVLGSodFlfRo08hDas\"", "vary": "Accept-Encoding, origin, access-control-request-method, access-control-request-headers", "x-proxied-host": "http://*************", "x-proxied-replica": "erixc2b0-d7n4e", "x-proxied-path": "/webhook-test/ce40f698-832e-475a-a3c7-0895c9e2e90b", "link": "<https://huggingface.co/spaces/houzhongxu/n8n-free>;rel=\"canonical\"", "x-request-id": "8i0Q_p", "access-control-allow-credentials": "true"}}}, "webhook_status": {"production": {"url": "https://houzhongxu-n8n-free.hf.space/webhook/ce40f698-832e-475a-a3c7-0895c9e2e90b", "status_code": 200, "success": true, "response": "{\"message\":\"Workflow was started\"}", "requires_auth": false}, "test_mode": {"url": "https://houzhongxu-n8n-free.hf.space/webhook-test/ce40f698-832e-475a-a3c7-0895c9e2e90b", "status_code": 404, "success": false, "response": "{\"code\":404,\"message\":\"The requested webhook \\\"ce40f698-832e-475a-a3c7-0895c9e2e90b\\\" is not registered.\",\"hint\":\"Click the 'Execute workflow' button on the canvas, then try again. (In test mode, the ", "requires_auth": false, "needs_activation": true}}, "recommendations": ["✅ 生产webhook无需认证，可以直接使用", "🚀 建议继续使用生产webhook进行集成", "📊 可以开始配置自动化数据推送", "🔧 测试webhook需要在N8N界面中手动激活", "🔐 3 个API端点需要认证", "📋 建议获取N8N API访问权限", "🌐 可以通过N8N Web界面生成API token", "📖 建议查看N8N文档了解认证配置", "🔄 如果是Hugging Face Spaces，可能需要重启实例", "⚡ 考虑将工作流设置为公开访问（如果安全允许）"]}