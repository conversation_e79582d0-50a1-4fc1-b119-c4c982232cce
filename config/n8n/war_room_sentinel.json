{"name": "War Room Sentinel", "nodes": [{"parameters": {"rule": {"interval": [{"field": "minutes", "minutesInterval": 30}]}}, "id": "5df1533a-be56-4248-a40f-3edff015783b", "name": "Crawl every 30 minutes", "type": "n8n-nodes-base.scheduleTrigger", "typeVersion": 1.1, "position": [-200, 0]}, {"parameters": {"jsCode": "const feeds = [\n  { name: 'Reuters', feedUrl: 'https://www.reuters.com/pf/api/v2/content/articles/channel?channel=us' },\n  { name: 'Associated Press', feedUrl: 'https://apnews.com/hub/ap-top-news/rss' },\n  { name: '<PERSON><PERSON><PERSON>', feedUrl: 'http://www.xinhuanet.com/english/rss/worldrss.xml' },\n  { name: 'PRNewswire', feedUrl: 'https://www.prnewswire.com/rss/news-releases-list.rss' },\n  { name: 'Wall Street Journal', feedUrl: 'https://feeds.a.dj.com/rss/RSSWorldNews.xml' },\n  { name: 'Financial Times', feedUrl: 'https://www.ft.com/rss/home' },\n  { name: 'Bloomberg', feedUrl: 'https://feeds.bloomberg.com/politics/news.rss' },\n  { name: 'ZeroHedge', feedUrl: 'https://www.zerohedge.com/feed' },\n  { name: 'TechCrunch', feedUrl: 'https://techcrunch.com/feed/' },\n  { name: 'The Verge', feedUrl: 'https://www.theverge.com/rss/index.xml' },\n  { name: 'Ars Technica', feedUrl: 'https://arstechnica.com/feed/' }\n];\nreturn feeds.map(feed => ({ json: feed }));"}, "id": "a1b2c3d4-e5f6-7890-1234-567890abcdef", "name": "RSS Feed Source List", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [0, 0]}, {"parameters": {}, "id": "b2c3d4e5-f6a7-8901-2345-67890abcdef1", "name": "Split Feeds", "type": "n8n-nodes-base.splitOut", "typeVersion": 1, "position": [200, 0]}, {"parameters": {"url": "={{ $json.feedUrl }}"}, "id": "c3d4e5f6-a7b8-9012-3456-7890abcdef12", "name": "Read RSS Feed", "type": "n8n-nodes-base.rssFeedRead", "typeVersion": 1.1, "position": [400, 0]}, {"parameters": {"mode": "combine", "combinationMode": "multiplex"}, "id": "d4e5f6a7-b8c9-0123-4567-890abcdef123", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.merge", "typeVersion": 2.1, "position": [600, 0]}, {"parameters": {"jsCode": "const uniqueArticles = new Map();\nfor (const item of items) {\n  const link = item.json.link || item.json.guid;\n  if (!link || uniqueArticles.has(link)) continue;\n  const articleDate = new Date(item.json.pubDate || item.json.isoDate);\n  const thirtyFiveMinutesAgo = new Date(Date.now() - 35 * 60 * 1000);\n  if (articleDate >= thirtyFiveMinutesAgo) {\n    uniqueArticles.set(link, {\n      title: item.json.title,\n      link: link,\n      snippet: (item.json.contentSnippet || item.json.content || '').substring(0, 500),\n      source: item.json.feed.title || 'Unknown',\n      publishDate: articleDate.toISOString()\n    });\n  }\n}\nconst results = Array.from(uniqueArticles.values());\nif (results.length === 0) return;\nreturn results.map(json => ({ json }));"}, "id": "e5f6a7b8-c9d0-1234-5678-90abcdef1234", "name": "Deduplicate & Filter Recent", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [800, 0]}, {"parameters": {"text": "={{ JSON.stringify($json) }}", "options": {"systemMessage": "As a China market specialist, analyze this Chinese financial news with focus on:\n1. sentimentScore (-1 to 1) considering:\n   - A股市场敏感性\n   - 政策影响权重\n   - 行业特殊性\n2. importanceScore (1-10) evaluating:\n   - 对沪深300成分股影响\n   - 产业链联动效应\n   - 散户关注度\n3. uniqueFactors:\n   - 北向资金敏感度\n   - 龙虎榜关联性\n\nRespond with JSON:\n{\n  \"sentimentScore\": 0.5,\n  \"importanceScore\": 7,\n  \"aShareImpact\": [\"光伏\", \"半导体\"],\n  \"policyLink\": \"十四五规划\"\n}\n\n新闻原文:\n标题: {{ $json.title }}\n来源: {{ $json.source }}\n内容: {{ $json.snippet }}"}, "promptType": "define"}, "id": "f6g7h8i9-j0k1-2345-6789-lmnopqrstuvw", "name": "News Analyzer", "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 2, "position": [1000, 0]}, {"parameters": {"jsCode": "if (!$input.first().json.baseline) {\n  $input.first().json.baseline = {\n    \"sentimentScores\": [],\n    \"importanceScores\": [],\n    \"lastUpdated\": new Date().toISOString()\n  };\n}\nconst currentScores = $input.all().map(item => ({\n  \"sentiment\": item.json.sentimentScore,\n  \"importance\": item.json.importanceScore\n}));\nfunction calculateRSI(scores, baseline, windowSize = 24) {\n  const allScores = [...baseline.sentimentScores, ...scores.map(s => s.sentiment)];\n  const allImportance = [...baseline.importanceScores, ...scores.map(s => s.importance)];\n  const weightedScores = allScores.map((s, i) => s * (allImportance[i]/10));\n  const recentScores = weightedScores.slice(-windowSize);\n  if (recentScores.length < 2) return 50;\n  let gains = 0;\n  let losses = 0;\n  for (let i = 1; i < recentScores.length; i++) {\n    const diff = recentScores[i] - recentScores[i-1];\n    if (diff > 0) gains += diff;\n    else losses += Math.abs(diff);\n  }\n  const avgGain = gains / (windowSize - 1);\n  const avgLoss = losses / (windowSize - 1);\n  if (avgLoss === 0) return 100;\n  if (avgGain === 0) return 0;\n  const rs = avgGain / avgLoss;\n  const rsi = 100 - (100 / (1 + rs));\n  return Math.round(rsi * 10) / 10;\n}\nconst excitementIndex = calculateRSI(currentScores, $input.first().json.baseline);\n$input.first().json.baseline.sentimentScores = \n  [...$input.first().json.baseline.sentimentScores, ...currentScores.map(s => s.sentiment)]\n    .slice(-100);\n$input.first().json.baseline.importanceScores = \n  [...$input.first().json.baseline.importanceScores, ...currentScores.map(s => s.importance)]\n    .slice(-100);\n$input.first().json.baseline.lastUpdated = new Date().toISOString();\nreturn [{\n  \"json\": {\n    \"excitementIndex\": excitementIndex,\n    \"analyzedArticles\": $input.all().length,\n    \"baseline\": $input.first().json.baseline,\n    \"timestamp\": new Date().toISOString()\n  }\n}];"}, "id": "g7h8i9j0-k1l2-3456-7890-mnopqrstuvwx", "name": "Calculate Excitement Index", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1200, 0]}, {"parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "excitement-threshold", "operator": {"type": "number", "operation": "gte"}, "leftValue": "={{ $json.excitementIndex }}", "rightValue": 80}]}}, "id": "h8i9j0k1-l2m3-4567-8901-nopqrstuvwxy", "name": "<PERSON>gger Debate", "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [1400, 0]}, {"parameters": {"text": "={{ JSON.stringify($input.all().map(i => i.json)) }}", "options": {"systemMessage": "You are moderating a war room debate with 3 experts:\n1. <PERSON><PERSON> (optimistic)\n2. <PERSON><PERSON> (pessimistic)\n3. <PERSON>eu<PERSON> Analyst (balanced)\n\nDebate these news articles and produce key insights:\n{{ $json.analyzedArticles }}\n\nFormat output as:\n{\n  \"debateTranscript\": \"...\",\n  \"keyConclusions\": [\"...\", \"...\"]\n}"}, "promptType": "define"}, "id": "i9j0k1l2-m3n4-5678-9012-opqrstuvwxyz", "name": "War Room Debate", "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 2, "position": [1600, 0]}, {"parameters": {"text": "={{ $json.debateTranscript }}", "options": {"systemMessage": "Summarize this debate transcript into a concise 3-bullet executive summary:\n1. <PERSON> bullish points\n2. <PERSON> bearish points\n3. Neutral consensus\n\nKeep under 150 words total."}, "promptType": "define"}, "id": "j0k1l2m3-n4o5-6789-0123-pqrstuvwxyza", "name": "Create Summary", "type": "@n8n/n8n-langchain.agent", "typeVersion": 2, "position": [1800, 0]}, {"parameters": {"channel": "general", "text": "={{ $json.keyConclusions.join('\\n') }}", "options": {}}, "id": "k1l2m3n4-o5p6-7890-1234-qrstuvwxyzab", "name": "Send to Slack", "type": "n8n-nodes-base.slack", "typeVersion": 1, "position": [2000, 0]}], "connections": {"Crawl every 30 minutes": {"main": [[{"node": "RSS Feed Source List", "type": "main", "index": 0}]]}, "RSS Feed Source List": {"main": [[{"node": "Split Feeds", "type": "main", "index": 0}]]}, "Split Feeds": {"main": [[{"node": "Read RSS Feed", "type": "main", "index": 0}]]}, "Read RSS Feed": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 0}]]}, "Merge All Articles": {"main": [[{"node": "Deduplicate & Filter Recent", "type": "main", "index": 0}]]}, "Deduplicate & Filter Recent": {"main": [[{"node": "News Analyzer", "type": "main", "index": 0}]]}, "News Analyzer": {"main": [[{"node": "Calculate Excitement Index", "type": "main", "index": 0}]]}, "Calculate Excitement Index": {"main": [[{"node": "<PERSON>gger Debate", "type": "main", "index": 0}]]}, "Trigger Debate": {"main": [[{"node": "War Room Debate", "type": "main", "index": 0}]]}, "War Room Debate": {"main": [[{"node": "Create Summary", "type": "main", "index": 0}]]}, "Create Summary": {"main": [[{"node": "Send to Slack", "type": "main", "index": 0}]]}}, "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}}