{"diagnostic_timestamp": "2025-07-12T14:04:45.463240", "n8n_instance": {"base_url": "https://houzhongxu-n8n-free.hf.space", "workflow_id": "5Ibi4vJZjSB0ZaTt", "webhook_id": "ce40f698-832e-475a-a3c7-0895c9e2e90b"}, "error_diagnosis": {"error_analysis": {"primary_cause": "wrong_webhook_url", "confidence": 0.9, "evidence": ["找到可用的webhook: https://houzhongxu-n8n-free.hf.space/webhook/ce40f698-832e-475a-a3c7-0895c9e2e90b"], "recommendations": ["使用正确的webhook URL: https://houzhongxu-n8n-free.hf.space/webhook/ce40f698-832e-475a-a3c7-0895c9e2e90b"]}, "possible_causes": ["使用了错误的webhook URL"], "solutions": ["使用正确的webhook URL: 使用正确的webhook URL: https://houzhongxu-n8n-free.hf.space/webhook/ce40f698-832e-475a-a3c7-0895c9e2e90b", "更新环境变量中的webhook配置", "检查N8N工作流的执行历史", "查看N8N的错误日志", "尝试手动执行工作流进行测试", "联系N8N实例管理员"], "test_results": {"base_connection": {"accessible": true, "status_code": 200, "headers": {"Date": "Sat, 12 Jul 2025 06:04:07 GMT", "Content-Type": "text/html; charset=utf-8", "Transfer-Encoding": "chunked", "Connection": "keep-alive", "cache-control": "no-cache, no-store, must-revalidate, proxy-revalidate", "cross-origin-opener-policy": "same-origin", "cross-origin-resource-policy": "same-origin", "origin-agent-cluster": "?1", "referrer-policy": "no-referrer", "x-content-type-options": "nosniff", "x-permitted-cross-domain-policies": "none", "x-xss-protection": "0", "accept-ranges": "bytes", "etag": "W/\"7b7-197fc41d20d\"", "vary": "Accept-Encoding, origin, access-control-request-method, access-control-request-headers", "content-encoding": "gzip", "x-proxied-host": "http://*************", "x-proxied-replica": "erixc2b0-d7n4e", "x-proxied-path": "/", "link": "<https://huggingface.co/spaces/houzhongxu/n8n-free>;rel=\"canonical\"", "x-request-id": "b-Qg2a", "access-control-allow-credentials": "true"}, "response_size": 1975, "response_time": 1.236773}, "workflow_access": {"accessible": true, "status_code": 200, "url": "https://houzhongxu-n8n-free.hf.space/workflow/5Ibi4vJZjSB0ZaTt"}, "webhook_variants": {"https://houzhongxu-n8n-free.hf.space/webhook/ce40f698-832e-475a-a3c7-0895c9e2e90b": {"get": {"method": "GET", "status_code": 404, "accessible": false}, "post": {"method": "POST", "status_code": 200, "accessible": true, "response": "{\"message\":\"Workflow was started\"}"}, "best_status": 200, "working": true}, "https://houzhongxu-n8n-free.hf.space/webhook-test/ce40f698-832e-475a-a3c7-0895c9e2e90b": {"get": {"method": "GET", "status_code": 404, "accessible": false}, "post": {"method": "POST", "status_code": 404, "accessible": false, "response": "{\"code\":404,\"message\":\"The requested webhook \\\"ce40f698-832e-475a-a3c7-0895c9e2e90b\\\" is not registered.\",\"hint\":\"Click the 'Execute workflow' button on the canvas, then try again. (In test mode, the "}, "best_status": 404}, "https://houzhongxu-n8n-free.hf.space/webhook/test/ce40f698-832e-475a-a3c7-0895c9e2e90b": {"get": {"method": "GET", "status_code": 404, "accessible": false}, "post": {"method": "POST", "status_code": 404, "accessible": false, "response": "{\"code\":404,\"message\":\"The requested webhook \\\"POST test/ce40f698-832e-475a-a3c7-0895c9e2e90b\\\" is not registered.\",\"hint\":\"The workflow must be active for a production URL to run successfully. You ca"}, "best_status": 404}, "https://houzhongxu-n8n-free.hf.space/api/webhook/ce40f698-832e-475a-a3c7-0895c9e2e90b": {"get": {"method": "GET", "status_code": 200, "accessible": true}, "post": {"method": "POST", "status_code": 404, "accessible": false, "response": "<!DOCTYPE html>\n<html lang=\"en\">\n<head>\n<meta charset=\"utf-8\">\n<title>Error</title>\n</head>\n<body>\n<pre>Cannot POST /api/webhook/ce40f698-832e-475a-a3c7-0895c9e2e90b</pre>\n</body>\n</html>\n"}, "best_status": 200}, "https://houzhongxu-n8n-free.hf.space/hooks/ce40f698-832e-475a-a3c7-0895c9e2e90b": {"get": {"method": "GET", "status_code": 200, "accessible": true}, "post": {"method": "POST", "status_code": 404, "accessible": false, "response": "<!DOCTYPE html>\n<html lang=\"en\">\n<head>\n<meta charset=\"utf-8\">\n<title>Error</title>\n</head>\n<body>\n<pre>Cannot POST /hooks/ce40f698-832e-475a-a3c7-0895c9e2e90b</pre>\n</body>\n</html>\n"}, "best_status": 200}, "https://houzhongxu-n8n-free.hf.space/trigger/ce40f698-832e-475a-a3c7-0895c9e2e90b": {"get": {"method": "GET", "status_code": 200, "accessible": true}, "post": {"method": "POST", "status_code": 404, "accessible": false, "response": "<!DOCTYPE html>\n<html lang=\"en\">\n<head>\n<meta charset=\"utf-8\">\n<title>Error</title>\n</head>\n<body>\n<pre>Cannot POST /trigger/ce40f698-832e-475a-a3c7-0895c9e2e90b</pre>\n</body>\n</html>\n"}, "best_status": 200}, "https://houzhongxu-n8n-free.hf.space/workflow/5Ibi4vJZjSB0ZaTt/webhook/ce40f698-832e-475a-a3c7-0895c9e2e90b": {"get": {"method": "GET", "status_code": 200, "accessible": true}, "post": {"method": "POST", "status_code": 404, "accessible": false, "response": "<!DOCTYPE html>\n<html lang=\"en\">\n<head>\n<meta charset=\"utf-8\">\n<title>Error</title>\n</head>\n<body>\n<pre>Cannot POST /workflow/5Ibi4vJZjSB0ZaTt/webhook/ce40f698-832e-475a-a3c7-0895c9e2e90b</pre>\n</body"}, "best_status": 200}, "https://houzhongxu-n8n-free.hf.space/workflow/5Ibi4vJZjSB0ZaTt/execute": {"get": {"method": "GET", "status_code": 200, "accessible": true}, "post": {"method": "POST", "status_code": 404, "accessible": false, "response": "<!DOCTYPE html>\n<html lang=\"en\">\n<head>\n<meta charset=\"utf-8\">\n<title>Error</title>\n</head>\n<body>\n<pre>Cannot POST /workflow/5Ibi4vJZjSB0ZaTt/execute</pre>\n</body>\n</html>\n"}, "best_status": 200}}, "api_endpoints": {"https://houzhongxu-n8n-free.hf.space/api/v1/workflows": {"status_code": 401, "accessible": true, "requires_auth": true, "response_size": 45}, "https://houzhongxu-n8n-free.hf.space/api/workflows": {"status_code": 200, "accessible": true, "requires_auth": false, "response_size": 1975}, "https://houzhongxu-n8n-free.hf.space/api/v1/executions": {"status_code": 401, "accessible": true, "requires_auth": true, "response_size": 45}, "https://houzhongxu-n8n-free.hf.space/api/executions": {"status_code": 200, "accessible": true, "requires_auth": false, "response_size": 1975}, "https://houzhongxu-n8n-free.hf.space/api/v1/workflows/5Ibi4vJZjSB0ZaTt": {"status_code": 401, "accessible": true, "requires_auth": true, "response_size": 45}, "https://houzhongxu-n8n-free.hf.space/api/workflows/5Ibi4vJZjSB0ZaTt": {"status_code": 200, "accessible": true, "requires_auth": false, "response_size": 1975}, "https://houzhongxu-n8n-free.hf.space/api/v1/workflows/5Ibi4vJZjSB0ZaTt/execute": {"status_code": 404, "accessible": false, "requires_auth": false, "response_size": 23}, "https://houzhongxu-n8n-free.hf.space/api/workflows/5Ibi4vJZjSB0ZaTt/execute": {"status_code": 200, "accessible": true, "requires_auth": false, "response_size": 1975}, "https://houzhongxu-n8n-free.hf.space/rest/workflows": {"status_code": 401, "accessible": true, "requires_auth": true, "response_size": 43}, "https://houzhongxu-n8n-free.hf.space/rest/executions": {"status_code": 401, "accessible": true, "requires_auth": true, "response_size": 43}}}}, "alternative_methods": {"direct_execution": {"https://houzhongxu-n8n-free.hf.space/api/v1/workflows/5Ibi4vJZjSB0ZaTt/execute": {"status_code": 404, "response": "{\"message\":\"not found\"}", "success": false}, "https://houzhongxu-n8n-free.hf.space/api/workflows/5Ibi4vJZjSB0ZaTt/execute": {"status_code": 404, "response": "<!DOCTYPE html>\n<html lang=\"en\">\n<head>\n<meta charset=\"utf-8\">\n<title>Error</title>\n</head>\n<body>\n<pre>Cannot POST /api/workflows/5Ibi4vJZjSB0ZaTt/execute</pre>\n</body>\n</html>\n", "success": false}, "https://houzhongxu-n8n-free.hf.space/workflow/5Ibi4vJZjSB0ZaTt/execute": {"status_code": 404, "response": "<!DOCTYPE html>\n<html lang=\"en\">\n<head>\n<meta charset=\"utf-8\">\n<title>Error</title>\n</head>\n<body>\n<pre>Cannot POST /workflow/5Ibi4vJZjSB0ZaTt/execute</pre>\n</body>\n</html>\n", "success": false}, "https://houzhongxu-n8n-free.hf.space/execute/5Ibi4vJZjSB0ZaTt": {"status_code": 404, "response": "<!DOCTYPE html>\n<html lang=\"en\">\n<head>\n<meta charset=\"utf-8\">\n<title>Error</title>\n</head>\n<body>\n<pre>Cannot POST /execute/5Ibi4vJZjSB0ZaTt</pre>\n</body>\n</html>\n", "success": false}}, "http_methods": {"GET": {"status_code": 404, "success": false, "response": "{\"code\":404,\"message\":\"This webhook is not registered for GET requests. Did you mean to make a POST "}, "POST": {"status_code": 200, "success": true, "response": "{\"message\":\"Workflow was started\"}"}, "PUT": {"status_code": 404, "success": false, "response": "{\"code\":404,\"message\":\"This webhook is not registered for PUT requests. Did you mean to make a POST "}, "PATCH": {"status_code": 404, "success": false, "response": "{\"code\":404,\"message\":\"This webhook is not registered for PATCH requests. Did you mean to make a POS"}}, "content_types": {"application/json": {"status_code": 200, "success": true, "response": "{\"message\":\"Workflow was started\"}"}, "application/x-www-form-urlencoded": {"status_code": 200, "success": true, "response": "{\"message\":\"Workflow was started\"}"}, "text/plain": {"status_code": 200, "success": true, "response": "{\"message\":\"Workflow was started\"}"}}}, "summary": {"primary_issue": "wrong_webhook_url", "confidence": 0.9, "working_alternatives": ["http_methods: POST", "content_types: application/json", "content_types: application/x-www-form-urlencoded", "content_types: text/plain"], "recommended_actions": ["使用正确的webhook URL: 使用正确的webhook URL: https://houzhongxu-n8n-free.hf.space/webhook/ce40f698-832e-475a-a3c7-0895c9e2e90b", "更新环境变量中的webhook配置", "检查N8N工作流的执行历史"]}}