# 零停机配置文件
# 炼妖壶交响乐团指挥模式配置

# 健康检查配置
health_check_interval: 30  # 健康检查间隔（秒）
failover_threshold: 3      # 故障转移阈值（连续失败次数）
response_timeout: 10       # 响应超时（秒）
circuit_breaker_timeout: 300  # 熔断器超时（秒）

# 蓝绿部署配置
deployments:
  blue:
    url: "https://cauldron-blue.herokuapp.com"
    version: "v1.0.0"
    description: "主要生产环境"
    
  green:
    url: "https://cauldron-green.herokuapp.com"
    version: "v1.0.0"
    description: "备用/新版本环境"

# 本地指挥配置
local_conductor:
  enabled: true
  model_name: "llama3.2:3b"  # Ollama模型
  fallback_models:
    - "llama3.2:1b"  # 更轻量的备用模型
    - "qwen2.5:3b"   # 其他备用模型
  
  # RAG配置
  rag:
    data_dir: "./data/rag"
    max_context_length: 2000
    similarity_threshold: 0.7
    max_results: 10

# 本地降级配置
local_fallback:
  enabled: true
  confidence_threshold: 0.6  # 本地分析置信度阈值
  max_processing_time: 30    # 最大处理时间（秒）

# 成本优化配置
cost_optimization:
  local_first: true          # 优先使用本地处理
  cloud_threshold_score: 80  # 云端处理阈值分数
  
  # 成本估算（美元）
  costs:
    openrouter_call: 0.002
    full_debate: 0.05
    simple_analysis: 0.01
    local_processing: 0.0

# 智能路由配置
routing:
  # 事件类型路由规则
  event_routing:
    high_impact:      # 高影响事件（>= 90分）
      strategy: "cloud_priority"
      fallback: "local_enhanced"
      
    medium_impact:    # 中等影响事件（60-89分）
      strategy: "hybrid"
      fallback: "local_standard"
      
    low_impact:       # 低影响事件（< 60分）
      strategy: "local_first"
      fallback: "local_basic"
  
  # 负载均衡策略
  load_balancing:
    algorithm: "weighted_round_robin"  # 加权轮询
    weights:
      blue: 70   # 主环境权重
      green: 30  # 备用环境权重

# 监控和告警配置
monitoring:
  metrics_retention_days: 30
  
  # SLA目标
  sla_targets:
    uptime_percentage: 99.9
    max_response_time_ms: 5000
    max_error_rate_percentage: 1.0
  
  # 告警阈值
  alerts:
    consecutive_failures: 5
    response_time_ms: 10000
    error_rate_percentage: 5.0
    uptime_below_percentage: 99.0

# 自动恢复配置
auto_recovery:
  enabled: true
  max_retry_attempts: 3
  retry_delay_seconds: 5
  exponential_backoff: true
  
  # 自动重启条件
  restart_conditions:
    memory_usage_percentage: 90
    cpu_usage_percentage: 95
    consecutive_errors: 10

# 部署策略配置
deployment_strategy:
  type: "blue_green"  # 蓝绿部署
  
  # 部署流程
  deployment_flow:
    1: "health_check_current"     # 检查当前环境健康状态
    2: "deploy_to_standby"        # 部署到备用环境
    3: "health_check_standby"     # 检查备用环境健康状态
    4: "gradual_traffic_shift"    # 逐步切换流量
    5: "monitor_metrics"          # 监控关键指标
    6: "complete_switch"          # 完成切换
    7: "cleanup_old_version"      # 清理旧版本
  
  # 流量切换策略
  traffic_shift:
    strategy: "gradual"
    steps:
      - percentage: 10
        duration_minutes: 5
      - percentage: 25
        duration_minutes: 5
      - percentage: 50
        duration_minutes: 10
      - percentage: 75
        duration_minutes: 10
      - percentage: 100
        duration_minutes: 0
  
  # 回滚策略
  rollback:
    auto_rollback_enabled: true
    rollback_triggers:
      - error_rate_increase_percentage: 200
      - response_time_increase_percentage: 300
      - health_check_failures: 3
    rollback_timeout_minutes: 5

# 四梁八柱架构配置
architecture:
  # 四梁（主要承重结构）
  four_beams:
    beam_1:
      name: "主力承重梁"
      components: ["primary_deployment", "main_conductor"]
      responsibility: "核心业务处理"
      
    beam_2:
      name: "备用承重梁"
      components: ["backup_deployment", "backup_conductor"]
      responsibility: "故障转移和负载分担"
      
    beam_3:
      name: "监控承重梁"
      components: ["health_monitor", "metrics_collector"]
      responsibility: "系统监控和状态管理"
      
    beam_4:
      name: "应急承重梁"
      components: ["emergency_fallback", "local_processing"]
      responsibility: "紧急情况处理和本地兜底"
  
  # 八柱（分布式支撑）
  eight_pillars:
    pillar_1:
      name: "乾柱 - 吕洞宾"
      function: "主动分析"
      deployment_zone: "blue"
      
    pillar_2:
      name: "兑柱 - 何仙姑"
      function: "情绪分析"
      deployment_zone: "blue"
      
    pillar_3:
      name: "离柱 - 李铁拐"
      function: "技术分析"
      deployment_zone: "green"
      
    pillar_4:
      name: "震柱 - 韩湘子"
      function: "新闻分析"
      deployment_zone: "green"
      
    pillar_5:
      name: "坤柱 - 曹国舅"
      function: "风险评估"
      deployment_zone: "blue"
      
    pillar_6:
      name: "艮柱 - 蓝采和"
      function: "基本面分析"
      deployment_zone: "blue"
      
    pillar_7:
      name: "坎柱 - 张果老"
      function: "流动性分析"
      deployment_zone: "green"
      
    pillar_8:
      name: "巽柱 - 铁拐李"
      function: "宏观分析"
      deployment_zone: "green"

# 学习和优化配置
learning:
  enabled: true
  
  # 自动学习
  auto_learning:
    feedback_collection: true
    pattern_recognition: true
    strategy_optimization: true
  
  # 知识库更新
  knowledge_base:
    auto_update_interval_hours: 24
    max_entries_per_category: 1000
    cleanup_old_entries_days: 90
  
  # 模型优化
  model_optimization:
    performance_tracking: true
    accuracy_monitoring: true
    cost_efficiency_analysis: true

# 安全配置
security:
  # API安全
  api_security:
    rate_limiting:
      requests_per_minute: 100
      burst_limit: 20
    
    authentication:
      required: false  # 内部系统，暂不需要认证
      
  # 数据安全
  data_security:
    encryption_at_rest: false
    encryption_in_transit: true
    data_retention_days: 90
    
  # 网络安全
  network_security:
    allowed_origins: ["*"]  # 开发阶段允许所有来源
    cors_enabled: true

# 日志配置
logging:
  level: "INFO"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  
  # 日志文件
  files:
    main: "logs/cauldron.log"
    conductor: "logs/conductor.log"
    orchestrator: "logs/orchestrator.log"
    health_check: "logs/health_check.log"
  
  # 日志轮转
  rotation:
    max_size_mb: 100
    backup_count: 5
    
  # 结构化日志
  structured_logging:
    enabled: true
    include_trace_id: true
    include_user_context: false
