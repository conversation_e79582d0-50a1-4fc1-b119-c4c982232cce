{"rapidapi_key": "**************************************************", "working_apis": [{"name": "Alpha Vantage (股票数据)", "host": "alpha-vantage.p.rapidapi.com", "category": "股票/金融数据", "working_endpoints": 5, "total_endpoints": 8}, {"name": "Yahoo Finance (财经数据)", "host": "yahoo-finance15.p.rapidapi.com", "category": "股票/金融数据", "working_endpoints": 5, "total_endpoints": 6}, {"name": "Seeking Alpha (投资分析)", "host": "seeking-alpha.p.rapidapi.com", "category": "投资分析/新闻", "working_endpoints": 1, "total_endpoints": 5}, {"name": "Webull (股票数据)", "host": "webull.p.rapidapi.com", "category": "股票/金融数据", "working_endpoints": 1, "total_endpoints": 3}], "detailed_results": {"alpha-vantage.p.rapidapi.com": [{"endpoint": "/query", "params": {"function": "GLOBAL_QUOTE", "symbol": "AAPL"}, "description": "实时股票报价", "status_code": 200, "success": true, "data_preview": "{\n  \"Global Quote\": {\n    \"01. symbol\": \"AAPL\",\n    \"02. open\": \"210.5050\",\n    \"03. high\": \"213.4800\",\n    \"04. low\": \"210.0300\",\n    \"05. price\": \"212.4100\",\n    \"06. volume\": \"44443635\",\n    \"07. latest trading day\": \"2025-07-10\",\n    \"08. previous close\": \"211.1400\",\n    \"09. change\": \"1.2700\",\n    \"10. change percent\": \"0.6015%\"\n  }\n}", "response_size": 385, "content_type": "application/json", "rate_limit_info": {"X-RateLimit-Requests-Limit": "500", "X-RateLimit-Requests-Remaining": "491", "X-RateLimit-Requests-Reset": "63598", "X-RateLimit-rapid-free-plans-hard-limit-Limit": "500000", "X-RateLimit-rapid-free-plans-hard-limit-Remaining": "499991", "X-RateLimit-rapid-free-plans-hard-limit-Reset": "2655598"}}, {"endpoint": "/query", "params": {"function": "OVERVIEW", "symbol": "AAPL"}, "description": "公司概览", "status_code": 200, "success": true, "data_preview": "{\n  \"Symbol\": \"AAPL\",\n  \"AssetType\": \"Common Stock\",\n  \"Name\": \"Apple Inc\",\n  \"Description\": \"Apple Inc. is an American multinational technology company that specializes in consumer electronics, computer software, and online services. Apple is the world's largest technology company by revenue (total...", "response_size": 2278, "content_type": "application/json", "rate_limit_info": {"X-RateLimit-Requests-Limit": "500", "X-RateLimit-Requests-Remaining": "490", "X-RateLimit-Requests-Reset": "63596", "X-RateLimit-rapid-free-plans-hard-limit-Limit": "500000", "X-RateLimit-rapid-free-plans-hard-limit-Remaining": "499990", "X-RateLimit-rapid-free-plans-hard-limit-Reset": "2655596"}}, {"endpoint": "/query", "params": {"function": "INCOME_STATEMENT", "symbol": "AAPL"}, "description": "损益表", "status_code": 200, "success": true, "data_preview": "{\n  \"symbol\": \"AAPL\",\n  \"annualReports\": [\n    {\n      \"fiscalDateEnding\": \"2024-09-30\",\n      \"reportedCurrency\": \"USD\",\n      \"grossProfit\": \"180683000000\",\n      \"totalRevenue\": \"391035000000\",\n      \"costOfRevenue\": \"210352000000\",\n      \"costofGoodsAndServicesSold\": \"210352000000\",\n      \"opera...", "response_size": 122265, "content_type": "application/json", "rate_limit_info": {"X-RateLimit-Requests-Limit": "500", "X-RateLimit-Requests-Remaining": "489", "X-RateLimit-Requests-Reset": "63595", "X-RateLimit-rapid-free-plans-hard-limit-Limit": "500000", "X-RateLimit-rapid-free-plans-hard-limit-Remaining": "499989", "X-RateLimit-rapid-free-plans-hard-limit-Reset": "2655595"}}, {"endpoint": "/query", "params": {"function": "BALANCE_SHEET", "symbol": "AAPL"}, "description": "资产负债表", "status_code": 200, "success": true, "data_preview": "{\n  \"symbol\": \"AAPL\",\n  \"annualReports\": [\n    {\n      \"fiscalDateEnding\": \"2024-09-30\",\n      \"reportedCurrency\": \"USD\",\n      \"totalAssets\": \"364980000000\",\n      \"totalCurrentAssets\": \"152987000000\",\n      \"cashAndCashEquivalentsAtCarryingValue\": \"29943000000\",\n      \"cashAndShortTermInvestments\"...", "response_size": 187200, "content_type": "application/json", "rate_limit_info": {"X-RateLimit-Requests-Limit": "500", "X-RateLimit-Requests-Remaining": "488", "X-RateLimit-Requests-Reset": "63593", "X-RateLimit-rapid-free-plans-hard-limit-Limit": "500000", "X-RateLimit-rapid-free-plans-hard-limit-Remaining": "499988", "X-RateLimit-rapid-free-plans-hard-limit-Reset": "2655593"}}, {"endpoint": "/query", "params": {"function": "CASH_FLOW", "symbol": "AAPL"}, "description": "现金流量表", "status_code": 200, "success": true, "data_preview": "{\n  \"symbol\": \"AAPL\",\n  \"annualReports\": [\n    {\n      \"fiscalDateEnding\": \"2024-09-30\",\n      \"reportedCurrency\": \"USD\",\n      \"operatingCashflow\": \"118254000000\",\n      \"paymentsForOperatingActivities\": \"None\",\n      \"proceedsFromOperatingActivities\": \"None\",\n      \"changeInOperatingLiabilities\": ...", "response_size": 155929, "content_type": "application/json", "rate_limit_info": {"X-RateLimit-Requests-Limit": "500", "X-RateLimit-Requests-Remaining": "487", "X-RateLimit-Requests-Reset": "63591", "X-RateLimit-rapid-free-plans-hard-limit-Limit": "500000", "X-RateLimit-rapid-free-plans-hard-limit-Remaining": "499987", "X-RateLimit-rapid-free-plans-hard-limit-Reset": "2655591"}}, {"endpoint": "/query", "params": {"function": "EARNINGS", "symbol": "AAPL"}, "description": "财报数据", "status_code": 429, "success": false, "data_preview": "{\n  \"message\": \"You have exceeded the rate limit per minute for your plan, BASIC, by the API provider\"\n}", "response_size": 99, "content_type": "application/json", "rate_limit_info": {}}, {"endpoint": "/query", "params": {"function": "TIME_SERIES_DAILY", "symbol": "AAPL"}, "description": "日线数据", "status_code": 429, "success": false, "data_preview": "{\n  \"message\": \"You have exceeded the rate limit per minute for your plan, BASIC, by the API provider\"\n}", "response_size": 99, "content_type": "application/json", "rate_limit_info": {}}, {"endpoint": "/query", "params": {"function": "NEWS_SENTIMENT", "tickers": "AAPL"}, "description": "新闻情绪", "status_code": 429, "success": false, "data_preview": "{\n  \"message\": \"You have exceeded the rate limit per minute for your plan, BASIC, by the API provider\"\n}", "response_size": 99, "content_type": "application/json", "rate_limit_info": {}}], "yahoo-finance15.p.rapidapi.com": [{"endpoint": "/api/yahoo/qu/quote/AAPL", "params": {}, "description": "股票报价", "status_code": 200, "success": true, "data_preview": "{\n  \"meta\": {\n    \"version\": \"v1.0\",\n    \"status\": 200,\n    \"copywrite\": \"https://apicalls.io\",\n    \"symbol\": \"AAPL\",\n    \"processedTime\": \"2025-07-11T10:29:41.945007Z\"\n  },\n  \"body\": [\n    {\n      \"language\": \"en-US\",\n      \"region\": \"US\",\n      \"quoteType\": \"EQUITY\",\n      \"typeDisp\": \"Equity\",\n  ...", "response_size": 2691, "content_type": "application/json", "rate_limit_info": {"X-RateLimit-Requests-Limit": "500", "X-RateLimit-Requests-Remaining": "495", "X-RateLimit-Requests-Reset": "2655511", "X-RateLimit-Old-Requests-Limit": "500", "X-RateLimit-Old-Requests-Remaining": "495", "X-RateLimit-Old-Requests-Reset": "2655511", "X-RateLimit-rapid-free-plans-hard-limit-Limit": "500000", "X-RateLimit-rapid-free-plans-hard-limit-Remaining": "499995", "X-RateLimit-rapid-free-plans-hard-limit-Reset": "2655511"}}, {"endpoint": "/api/yahoo/co/collections/day_gainers", "params": {}, "description": "当日涨幅榜", "status_code": 200, "success": true, "data_preview": "{\n  \"meta\": {\n    \"description\": \"day_gainers\",\n    \"processedTime\": \"2025-07-11T10:29:44.161815Z\",\n    \"offset\": 0,\n    \"count\": 25,\n    \"total\": 124,\n    \"version\": \"v1.0\",\n    \"status\": 200,\n    \"copywrite\": \"https://apicalls.io\"\n  },\n  \"body\": [\n    {\n      \"language\": \"en-US\",\n      \"region\": \"...", "response_size": 61469, "content_type": "application/json", "rate_limit_info": {"X-RateLimit-Requests-Limit": "500", "X-RateLimit-Requests-Remaining": "494", "X-RateLimit-Requests-Reset": "2655508", "X-RateLimit-Old-Requests-Limit": "500", "X-RateLimit-Old-Requests-Remaining": "494", "X-RateLimit-Old-Requests-Reset": "2655508", "X-RateLimit-rapid-free-plans-hard-limit-Limit": "500000", "X-RateLimit-rapid-free-plans-hard-limit-Remaining": "499994", "X-RateLimit-rapid-free-plans-hard-limit-Reset": "2655508"}}, {"endpoint": "/api/yahoo/co/collections/day_losers", "params": {}, "description": "当日跌幅榜", "status_code": 200, "success": true, "data_preview": "{\n  \"meta\": {\n    \"description\": \"day_losers\",\n    \"processedTime\": \"2025-07-11T10:29:46.633312Z\",\n    \"offset\": 0,\n    \"count\": 25,\n    \"total\": 117,\n    \"version\": \"v1.0\",\n    \"status\": 200,\n    \"copywrite\": \"https://apicalls.io\"\n  },\n  \"body\": [\n    {\n      \"language\": \"en-US\",\n      \"region\": \"U...", "response_size": 60801, "content_type": "application/json", "rate_limit_info": {"X-RateLimit-Requests-Limit": "500", "X-RateLimit-Requests-Remaining": "493", "X-RateLimit-Requests-Reset": "2655506", "X-RateLimit-Old-Requests-Limit": "500", "X-RateLimit-Old-Requests-Remaining": "493", "X-RateLimit-Old-Requests-Reset": "2655506", "X-RateLimit-rapid-free-plans-hard-limit-Limit": "500000", "X-RateLimit-rapid-free-plans-hard-limit-Remaining": "499993", "X-RateLimit-rapid-free-plans-hard-limit-Reset": "2655506"}}, {"endpoint": "/api/yahoo/co/collections/most_actives", "params": {}, "description": "最活跃股票", "status_code": 200, "success": true, "data_preview": "{\n  \"meta\": {\n    \"description\": \"most_actives\",\n    \"processedTime\": \"2025-07-11T10:29:49.138947Z\",\n    \"offset\": 0,\n    \"count\": 25,\n    \"total\": 302,\n    \"version\": \"v1.0\",\n    \"status\": 200,\n    \"copywrite\": \"https://apicalls.io\"\n  },\n  \"body\": [\n    {\n      \"language\": \"en-US\",\n      \"region\": ...", "response_size": 62016, "content_type": "application/json", "rate_limit_info": {"X-RateLimit-Requests-Limit": "500", "X-RateLimit-Requests-Remaining": "492", "X-RateLimit-Requests-Reset": "2655503", "X-RateLimit-Old-Requests-Limit": "500", "X-RateLimit-Old-Requests-Remaining": "492", "X-RateLimit-Old-Requests-Reset": "2655503", "X-RateLimit-rapid-free-plans-hard-limit-Limit": "500000", "X-RateLimit-rapid-free-plans-hard-limit-Remaining": "499992", "X-RateLimit-rapid-free-plans-hard-limit-Reset": "2655503"}}, {"endpoint": "/api/yahoo/ne/news", "params": {"symbols": "AAPL"}, "description": "股票新闻", "status_code": 200, "success": true, "data_preview": "{\n  \"meta\": {\n    \"ticker\": null,\n    \"processedTime\": \"2025-07-11T10:29:52.021289Z\",\n    \"version\": \"v1.0\",\n    \"status\": 200,\n    \"copywrite\": \"https://apicalls.io\"\n  },\n  \"body\": [\n    {\n      \"title\": \"Citi Custom Cash Card review: Earn up to 5% cash back, plus 0% intro APR\",\n      \"link\": \"http...", "response_size": 15236, "content_type": "application/json", "rate_limit_info": {"X-RateLimit-Requests-Limit": "500", "X-RateLimit-Requests-Remaining": "491", "X-RateLimit-Requests-Reset": "2655500", "X-RateLimit-Old-Requests-Limit": "500", "X-RateLimit-Old-Requests-Remaining": "491", "X-RateLimit-Old-Requests-Reset": "2655500", "X-RateLimit-rapid-free-plans-hard-limit-Limit": "500000", "X-RateLimit-rapid-free-plans-hard-limit-Remaining": "499991", "X-RateLimit-rapid-free-plans-hard-limit-Reset": "2655500"}}, {"endpoint": "/api/yahoo/hi/history/AAPL", "params": {"period1": "1640995200", "period2": "1672531200"}, "description": "历史数据", "status_code": 404, "success": false, "data_preview": "{\n  \"message\": \"Endpoint '/api/yahoo/hi/history/AAPL' does not exist\"\n}", "response_size": 71, "content_type": "application/json", "rate_limit_info": {}}], "seeking-alpha.p.rapidapi.com": [{"endpoint": "/symbols/get-profile", "params": {"symbols": "AAPL"}, "description": "公司档案", "status_code": 200, "success": true, "data_preview": "{\n  \"data\": [\n    {\n      \"id\": \"AAPL\",\n      \"tickerId\": 146,\n      \"attributes\": {\n        \"longDesc\": \"Apple Inc. designs, manufactures, and markets smartphones, personal computers, tablets, wearables, and accessories worldwide. The company offers iPhone, a line of smartphones; Mac, a line of per...", "response_size": 3005, "content_type": "application/json", "rate_limit_info": {"X-RateLimit-Requests-Limit": "500", "X-RateLimit-Requests-Remaining": "498", "X-RateLimit-Requests-Reset": "2655580", "X-RateLimit-rapid-free-plans-hard-limit-Limit": "500000", "X-RateLimit-rapid-free-plans-hard-limit-Remaining": "499998", "X-RateLimit-rapid-free-plans-hard-limit-Reset": "2655580"}}, {"endpoint": "/symbols/get-earnings", "params": {"symbols": "AAPL"}, "description": "财报数据", "status_code": 500, "success": false, "data_preview": "500 - Server Error", "response_size": 18, "content_type": "text/html;charset=iso-8859-1", "rate_limit_info": {"X-RateLimit-Requests-Limit": "500", "X-RateLimit-Requests-Remaining": "498", "X-RateLimit-Requests-Reset": "2655579", "X-RateLimit-rapid-free-plans-hard-limit-Limit": "500000", "X-RateLimit-rapid-free-plans-hard-limit-Remaining": "499998", "X-RateLimit-rapid-free-plans-hard-limit-Reset": "2655579"}}, {"endpoint": "/symbols/get-dividends", "params": {"symbols": "AAPL"}, "description": "股息信息", "status_code": 404, "success": false, "data_preview": "{\n  \"message\": \"Endpoint '/symbols/get-dividends' does not exist\"\n}", "response_size": 64, "content_type": "application/json", "rate_limit_info": {}}, {"endpoint": "/news/list", "params": {"category": "market-news"}, "description": "市场新闻", "status_code": 204, "success": false, "data_preview": "", "response_size": 0, "content_type": "", "rate_limit_info": {"X-RateLimit-Requests-Limit": "500", "X-RateLimit-Requests-Remaining": "497", "X-RateLimit-Requests-Reset": "2655576", "X-RateLimit-rapid-free-plans-hard-limit-Limit": "500000", "X-RateLimit-rapid-free-plans-hard-limit-Remaining": "499997", "X-RateLimit-rapid-free-plans-hard-limit-Reset": "2655576"}}, {"endpoint": "/symbols/get-ratings", "params": {"symbols": "AAPL"}, "description": "分析师评级", "status_code": 204, "success": false, "data_preview": "", "response_size": 0, "content_type": "", "rate_limit_info": {"X-RateLimit-Requests-Limit": "500", "X-RateLimit-Requests-Remaining": "496", "X-RateLimit-Requests-Reset": "2655574", "X-RateLimit-rapid-free-plans-hard-limit-Limit": "500000", "X-RateLimit-rapid-free-plans-hard-limit-Remaining": "499996", "X-RateLimit-rapid-free-plans-hard-limit-Reset": "2655574"}}], "twelve-data1.p.rapidapi.com": [{"endpoint": "/quote", "params": {"symbol": "AAPL"}, "description": "实时报价", "status_code": 403, "success": false, "data_preview": "{\n  \"message\": \"You are not subscribed to this API.\"\n}", "response_size": 49, "content_type": "application/json", "rate_limit_info": {}}, {"endpoint": "/time_series", "params": {"symbol": "AAPL", "interval": "1day"}, "description": "时间序列", "status_code": 429, "success": false, "data_preview": "{\n  \"message\": \"Too many requests\"\n}", "response_size": 31, "content_type": "application/json", "rate_limit_info": {}}, {"endpoint": "/earnings", "params": {"symbol": "AAPL"}, "description": "财报数据", "status_code": 403, "success": false, "data_preview": "{\n  \"message\": \"You are not subscribed to this API.\"\n}", "response_size": 49, "content_type": "application/json", "rate_limit_info": {}}, {"endpoint": "/profile", "params": {"symbol": "AAPL"}, "description": "公司档案", "status_code": 429, "success": false, "data_preview": "{\n  \"message\": \"Too many requests\"\n}", "response_size": 31, "content_type": "application/json", "rate_limit_info": {}}, {"endpoint": "/statistics", "params": {"symbol": "AAPL"}, "description": "统计数据", "status_code": 403, "success": false, "data_preview": "{\n  \"message\": \"You are not subscribed to this API.\"\n}", "response_size": 49, "content_type": "application/json", "rate_limit_info": {}}], "polygon-io.p.rapidapi.com": [{"endpoint": "/v2/aggs/ticker/AAPL/prev", "params": {}, "description": "前一交易日数据", "status_code": 403, "success": false, "data_preview": "{\n  \"message\": \"You are not subscribed to this API.\"\n}", "response_size": 49, "content_type": "application/json", "rate_limit_info": {}}, {"endpoint": "/v1/meta/symbols/AAPL/company", "params": {}, "description": "公司信息", "status_code": 429, "success": false, "data_preview": "{\n  \"message\": \"Too many requests\"\n}", "response_size": 31, "content_type": "application/json", "rate_limit_info": {}}, {"endpoint": "/v2/snapshot/locale/us/markets/stocks/tickers", "params": {}, "description": "市场快照", "status_code": 403, "success": false, "data_preview": "{\n  \"message\": \"You are not subscribed to this API.\"\n}", "response_size": 49, "content_type": "application/json", "rate_limit_info": {}}, {"endpoint": "/v1/meta/symbols/AAPL/news", "params": {}, "description": "公司新闻", "status_code": 429, "success": false, "data_preview": "{\n  \"message\": \"Too many requests\"\n}", "response_size": 31, "content_type": "application/json", "rate_limit_info": {}}], "webull.p.rapidapi.com": [{"endpoint": "/stock/search", "params": {"keyword": "AAPL"}, "description": "股票搜索", "status_code": 200, "success": true, "data_preview": "{\n  \"stocks\": {\n    \"hasMore\": false,\n    \"busiModel\": 10000,\n    \"pageSize\": 20,\n    \"pageIndex\": 1,\n    \"totals\": 7,\n    \"datas\": [\n      {\n        \"highlight\": [\n          \"AAPL\"\n        ],\n        \"modelType\": 10001,\n        \"ticker\": {\n          \"tickerId\": 913256135,\n          \"exchangeId\": 96...", "response_size": 4955, "content_type": "application/json", "rate_limit_info": {"X-RateLimit-rapid-free-plans-hard-limit-Limit": "500000", "X-RateLimit-rapid-free-plans-hard-limit-Remaining": "499999", "X-RateLimit-rapid-free-plans-hard-limit-Reset": "2655709", "X-RateLimit-Requests-Limit": "500", "X-RateLimit-Requests-Remaining": "499", "X-RateLimit-Requests-Reset": "2655709"}}, {"endpoint": "/stock/get-quote", "params": {"tickerId": "913256135"}, "description": "股票报价", "status_code": 404, "success": false, "data_preview": "{\n  \"message\": \"Endpoint '/stock/get-quote' does not exist\"\n}", "response_size": 58, "content_type": "application/json", "rate_limit_info": {}}, {"endpoint": "/stock/get-analysis", "params": {"tickerId": "913256135"}, "description": "技术分析", "status_code": 404, "success": false, "data_preview": "{\n  \"message\": \"Endpoint '/stock/get-analysis' does not exist\"\n}", "response_size": 61, "content_type": "application/json", "rate_limit_info": {}}], "sec-filings.p.rapidapi.com": [{"endpoint": "/search", "params": {"query": "AAPL"}, "description": "SEC文件搜索", "status_code": 404, "success": false, "data_preview": "{\n  \"message\": \"API doesn't exists\"\n}", "response_size": 32, "content_type": "application/json", "rate_limit_info": {}}, {"endpoint": "/company/AAPL", "params": {}, "description": "公司SEC信息", "status_code": 429, "success": false, "data_preview": "{\n  \"message\": \"Too many requests\"\n}", "response_size": 31, "content_type": "application/json", "rate_limit_info": {}}, {"endpoint": "/filings/10-K", "params": {"symbol": "AAPL"}, "description": "10-<PERSON><PERSON>报", "status_code": 404, "success": false, "data_preview": "{\n  \"message\": \"API doesn't exists\"\n}", "response_size": 32, "content_type": "application/json", "rate_limit_info": {}}, {"endpoint": "/filings/10-Q", "params": {"symbol": "AAPL"}, "description": "10-Q季报", "status_code": 429, "success": false, "data_preview": "{\n  \"message\": \"Too many requests\"\n}", "response_size": 31, "content_type": "application/json", "rate_limit_info": {}}], "coinranking1.p.rapidapi.com": [{"endpoint": "/coins", "params": {}, "description": "加密货币列表", "status_code": 403, "success": false, "data_preview": "{\n  \"message\": \"You are not subscribed to this API.\"\n}", "response_size": 49, "content_type": "application/json", "rate_limit_info": {}}, {"endpoint": "/coin/Qwsogvtv82FCd/price", "params": {}, "description": "Bitcoin价格", "status_code": 429, "success": false, "data_preview": "{\n  \"message\": \"Too many requests\"\n}", "response_size": 31, "content_type": "application/json", "rate_limit_info": {}}, {"endpoint": "/coin/razxDUgYGNAdQ/price", "params": {}, "description": "Ethereum价格", "status_code": 403, "success": false, "data_preview": "{\n  \"message\": \"You are not subscribed to this API.\"\n}", "response_size": 49, "content_type": "application/json", "rate_limit_info": {}}, {"endpoint": "/stats", "params": {}, "description": "市场统计", "status_code": 429, "success": false, "data_preview": "{\n  \"message\": \"Too many requests\"\n}", "response_size": 31, "content_type": "application/json", "rate_limit_info": {}}], "news-api14.p.rapidapi.com": [{"endpoint": "/everything", "params": {"q": "tesla", "pageSize": 5}, "description": "全部新闻", "status_code": 403, "success": false, "data_preview": "{\n  \"message\": \"You are not subscribed to this API.\"\n}", "response_size": 49, "content_type": "application/json", "rate_limit_info": {}}, {"endpoint": "/top-headlines", "params": {"country": "us", "pageSize": 5}, "description": "头条新闻", "status_code": 429, "success": false, "data_preview": "{\n  \"message\": \"Too many requests\"\n}", "response_size": 31, "content_type": "application/json", "rate_limit_info": {}}, {"endpoint": "/sources", "params": {}, "description": "新闻源", "status_code": 403, "success": false, "data_preview": "{\n  \"message\": \"You are not subscribed to this API.\"\n}", "response_size": 49, "content_type": "application/json", "rate_limit_info": {}}]}}