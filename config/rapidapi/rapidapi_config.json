{"rapidapi_key": "**************************************************", "subscribed_apis": {"alpha-vantage": {"host": "alpha-vantage.p.rapidapi.com", "base_url": "https://alpha-vantage.p.rapidapi.com", "endpoints": [{"path": "/query", "method": "GET", "params": {"function": "OVERVIEW", "symbol": "AAPL"}, "rate_limit": {"X-RateLimit-Requests-Limit": "500", "X-RateLimit-Requests-Remaining": "493", "X-RateLimit-Requests-Reset": "63704", "X-RateLimit-rapid-free-plans-hard-limit-Limit": "500000", "X-RateLimit-rapid-free-plans-hard-limit-Remaining": "499993", "X-RateLimit-rapid-free-plans-hard-limit-Reset": "2655704"}}, {"path": "/query", "method": "GET", "params": {"function": "GLOBAL_QUOTE", "symbol": "AAPL"}, "rate_limit": {"X-RateLimit-Requests-Limit": "500", "X-RateLimit-Requests-Remaining": "493", "X-RateLimit-Requests-Reset": "63704", "X-RateLimit-rapid-free-plans-hard-limit-Limit": "500000", "X-RateLimit-rapid-free-plans-hard-limit-Remaining": "499993", "X-RateLimit-rapid-free-plans-hard-limit-Reset": "2655704"}}]}, "yahoo-finance15": {"host": "yahoo-finance15.p.rapidapi.com", "base_url": "https://yahoo-finance15.p.rapidapi.com", "endpoints": [{"path": "/api/yahoo/qu/quote/AAPL", "method": "GET", "params": {}, "rate_limit": {"X-RateLimit-Requests-Limit": "500", "X-RateLimit-Requests-Remaining": "497", "X-RateLimit-Requests-Reset": "2655629", "X-RateLimit-Old-Requests-Limit": "500", "X-RateLimit-Old-Requests-Remaining": "497", "X-RateLimit-Old-Requests-Reset": "2655629", "X-RateLimit-rapid-free-plans-hard-limit-Limit": "500000", "X-RateLimit-rapid-free-plans-hard-limit-Remaining": "499997", "X-RateLimit-rapid-free-plans-hard-limit-Reset": "2655629"}}, {"path": "/api/yahoo/co/collections/day_gainers", "method": "GET", "params": {}, "rate_limit": {"X-RateLimit-Requests-Limit": "500", "X-RateLimit-Requests-Remaining": "496", "X-RateLimit-Requests-Reset": "2655628", "X-RateLimit-Old-Requests-Limit": "500", "X-RateLimit-Old-Requests-Remaining": "496", "X-RateLimit-Old-Requests-Reset": "2655628", "X-RateLimit-rapid-free-plans-hard-limit-Limit": "500000", "X-RateLimit-rapid-free-plans-hard-limit-Remaining": "499996", "X-RateLimit-rapid-free-plans-hard-limit-Reset": "2655628"}}]}, "finnhub.io": {"host": "finnhub.io", "base_url": "https://finnhub.io", "endpoints": [{"path": "/", "method": "GET", "params": {}, "rate_limit": {}}]}, "seeking-alpha": {"host": "seeking-alpha.p.rapidapi.com", "base_url": "https://seeking-alpha.p.rapidapi.com", "endpoints": [{"path": "/symbols/get-profile", "method": "GET", "params": {"symbols": "AAPL"}, "rate_limit": {"X-RateLimit-rapid-free-plans-hard-limit-Limit": "500000", "X-RateLimit-rapid-free-plans-hard-limit-Remaining": "499999", "X-RateLimit-rapid-free-plans-hard-limit-Reset": "2655707", "X-RateLimit-Requests-Limit": "500", "X-RateLimit-Requests-Remaining": "499", "X-RateLimit-Requests-Reset": "2655707"}}]}, "financialmodelingprep.com": {"host": "financialmodelingprep.com", "base_url": "https://financialmodelingprep.com", "endpoints": [{"path": "/", "method": "GET", "params": {}, "rate_limit": {}}]}}}