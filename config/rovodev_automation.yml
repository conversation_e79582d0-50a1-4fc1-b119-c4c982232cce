version: 1

agent:
  # 禁用额外的系统提示确认
  additionalSystemPrompt: null
  # 启用流式响应
  streaming: true
  # 降低温度以获得更一致的输出
  temperature: 0.1

experimental:
  # 启用shadow模式 - 在临时克隆中运行，减少权限提示
  enableShadowMode: true
  # 自动批准安全操作
  autoApproveFileOperations: true
  # 减少交互式确认
  minimizeInteractivePrompts: true

console:
  # 显示工具结果但不要求确认
  showToolResults: true
  # 禁用详细输出以减少干扰
  verbose: false
  # 自动确认安全操作
  autoConfirmSafeOperations: true

sessions:
  # 不自动恢复会话，每次都是新的开始
  autoRestore: false
  # 自动保存会话
  autoSave: true

tools:
  # 允许的工具操作，减少权限检查
  allowedOperations:
    - "file_read"
    - "file_write" 
    - "directory_list"
    - "code_analysis"
    - "script_execution"
  
  # 自动批准的文件类型
  autoApproveFileTypes:
    - ".py"
    - ".js" 
    - ".json"
    - ".yml"
    - ".yaml"
    - ".md"
    - ".txt"
  
  # 禁止的危险操作
  blockedOperations:
    - "system_shutdown"
    - "user_deletion"
    - "network_config_change"

# 安全设置
security:
  # 在沙盒环境中运行
  sandboxMode: true
  # 限制文件系统访问范围
  restrictedPaths:
    - "/Users/<USER>/cauldron"
    - "/tmp"
  # 自动批准安全范围内的操作
  autoApproveSafeOperations: true
