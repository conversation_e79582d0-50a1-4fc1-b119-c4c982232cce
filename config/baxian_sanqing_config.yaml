# 八仙论道+三清验证系统配置
system:
  name: "八仙论道三清验证系统"
  version: "1.0.0"
  description: "基于AutoGen的八仙辩论系统与OpenManus田野调查验证"

# OpenManus配置
openmanus:
  base_url: "${OPENMANUS_URL}"
  api_key: "${OPENMANUS_API_KEY}"
  timeout: 600
  max_concurrent_tasks: 5
  playwright_config:
    headless: true
    viewport:
      width: 1280
      height: 720
    wait_timeout: 30000
    navigation_timeout: 60000

# Zilliz知识库配置
zilliz:
  host: "${ZILLIZ_HOST}"
  port: 19530
  collection_name: "jixia_knowledge_base"
  username: "${ZILLIZ_USERNAME}"
  password: "${ZILLIZ_PASSWORD}"
  search_params:
    metric_type: "COSINE"
    top_k: 10
    nprobe: 16

# 验证系统配置
verification:
  confidence_threshold: 0.6
  max_verification_tasks: 10
  verification_timeout: 600
  retry_attempts: 3
  
  # 三清权重配置
  sanqing_weights:
    original_debate: 0.3    # 八仙原始辩论权重
    taiqing_logic: 0.3      # 太清逻辑分析权重
    shangqing_field: 0.4    # 上清田野调查权重

# 八仙代理配置
baxian_agents:
  吕洞宾:
    role: "剑仙投资顾问"
    gua_position: "乾☰"
    personality: "犀利直接，善于识破迷雾"
    specialization: "高风险高收益策略"
    model_config:
      model: "gpt-4"
      temperature: 0.7
      max_tokens: 1000
    
  何仙姑:
    role: "慈悲风控专家"
    gua_position: "坤☷" 
    personality: "温和坚定，关注风险控制"
    specialization: "稳健保守策略"
    model_config:
      model: "gpt-4"
      temperature: 0.5
      max_tokens: 1000
      
  铁拐李:
    role: "逆向思维大师"
    gua_position: "震☳"
    personality: "不拘一格，挑战主流"
    specialization: "逆向投资机会"
    model_config:
      model: "gpt-4"
      temperature: 0.8
      max_tokens: 1000
      
  蓝采和:
    role: "情绪分析师"
    gua_position: "巽☴"
    personality: "敏锐活泼，感知情绪"
    specialization: "市场情绪分析"
    model_config:
      model: "gpt-4"
      temperature: 0.6
      max_tokens: 1000
      
  张果老:
    role: "技术分析仙"
    gua_position: "坎☵"
    personality: "深沉睿智，精通图表"
    specialization: "技术面分析"
    model_config:
      model: "gpt-4"
      temperature: 0.4
      max_tokens: 1000
      
  韩湘子:
    role: "基本面研究员"
    gua_position: "艮☶"
    personality: "严谨细致，注重基本面"
    specialization: "财务数据分析"
    model_config:
      model: "gpt-4"
      temperature: 0.3
      max_tokens: 1000
      
  曹国舅:
    role: "宏观经济学家"
    gua_position: "兑☱"
    personality: "宏观视野，政策敏感"
    specialization: "宏观经济分析"
    model_config:
      model: "gpt-4"
      temperature: 0.5
      max_tokens: 1000
      
  钟汉离:
    role: "量化交易专家"
    gua_position: "离☲"
    personality: "理性冷静，数据驱动"
    specialization: "量化策略"
    model_config:
      model: "gpt-4"
      temperature: 0.2
      max_tokens: 1000

# 辩论配置
debate:
  max_rounds: 8
  speaker_selection: "round_robin"  # 或 "auto"
  termination_conditions:
    max_consecutive_auto_reply: 3
    human_input_mode: "NEVER"
  
  # 辩论阶段配置
  phases:
    opening:
      duration: 2  # 轮次
      focus: "观点陈述"
    discussion:
      duration: 4
      focus: "深入辩论"
    conclusion:
      duration: 2
      focus: "总结共识"

# 田野调查任务配置
field_investigation:
  task_types:
    web_scraping:
      enabled: true
      default_timeout: 300
      retry_count: 3
      
    data_validation:
      enabled: true
      sources: ["yahoo_finance", "reuters", "bloomberg"]
      
    market_monitoring:
      enabled: true
      indicators: ["price", "volume", "volatility"]
      
    news_verification:
      enabled: true
      sources: ["reuters", "bloomberg", "cnbc"]
      
    social_sentiment:
      enabled: true
      platforms: ["twitter", "reddit", "stocktwits"]

# 报告配置
reporting:
  formats: ["json", "markdown", "pdf"]
  include_charts: true
  include_raw_data: false
  
  # 报告模板
  templates:
    executive_summary: true
    detailed_analysis: true
    risk_assessment: true
    implementation_plan: true
    monitoring_plan: true

# 监控和告警
monitoring:
  enabled: true
  metrics:
    - "debate_duration"
    - "verification_success_rate" 
    - "confidence_score_distribution"
    - "field_task_completion_rate"
  
  alerts:
    low_confidence_threshold: 0.3
    verification_failure_threshold: 0.5
    system_error_notification: true

# 日志配置
logging:
  level: "INFO"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  handlers:
    console:
      enabled: true
      level: "INFO"
    file:
      enabled: true
      level: "DEBUG"
      filename: "logs/baxian_sanqing.log"
      max_bytes: 10485760  # 10MB
      backup_count: 5

# 安全配置
security:
  api_rate_limiting:
    enabled: true
    requests_per_minute: 60
  
  data_encryption:
    enabled: true
    algorithm: "AES-256"
  
  access_control:
    require_authentication: true
    session_timeout: 3600  # 1小时

# 性能配置
performance:
  async_processing: true
  max_concurrent_debates: 3
  max_concurrent_verifications: 5
  cache_enabled: true
  cache_ttl: 3600  # 1小时

# 开发和调试
development:
  debug_mode: false
  mock_openmanus: false
  mock_zilliz: false
  save_intermediate_results: true
  
# 环境变量映射
environment_variables:
  required:
    - "OPENMANUS_URL"
    - "OPENMANUS_API_KEY"
    - "ZILLIZ_HOST"
    - "ZILLIZ_USERNAME"
    - "ZILLIZ_PASSWORD"
  optional:
    - "OPENAI_API_KEY"
    - "ANTHROPIC_API_KEY"
    - "DEBUG_MODE"