# Cauldron (炼妖壶) 环境配置文件示例
# 复制此文件为 .env 并填入实际值

# ===========================================
# 核心应用配置
# ===========================================
DEBUG=True
LOG_LEVEL=INFO

# ===========================================
# 主数据库 (PostgreSQL)
# ===========================================
DB_HOST=localhost
DB_PORT=5432
DB_NAME=cauldron_db
DB_USER=postgres
DB_PASSWORD=


# ===========================================
# 第三方服务API密钥
# ===========================================

# Tushare (财经数据)
tushare=

# Blockchair (区块链数据)
blockchair=

# Deepnote configuration removed - module moved outside project

# MongoDB
mongopublickkey=
mongoprivatekey=

# ===========================================
# N8N 工作流自动化
# ===========================================
N8N_WEBHOOK_URL=
N8N_API_KEY=

# ===========================================
# 八仙论道 (Jixia Academy) 系统配置
# ===========================================
JIXIA_ACADEMY_ENABLED=True
JIXIA_ACADEMY_PORT=8000
JIXIA_ACADEMY_HOST=localhost

# Jixia Academy 数据库 (PostgreSQL)
JIXIA_DB_HOST=localhost
JIXIA_DB_PORT=5432
JIXIA_DB_NAME=jixia_academy
JIXIA_DB_USER=postgres
JIXIA_DB_PASSWORD=

# Jixia 辩论配置
DEBATE_MAX_ROUNDS=8
DEBATE_TIME_LIMIT=300
DEBATE_AUTO_START=False

# OpenRouter API 密钥 (用于 Jixia 辩论模型)
# 稷下学宫八仙辩论系统所需的API密钥
OPENROUTER_API_KEY_MODERATOR=  # 灵宝道君 - 主持人
OPENROUTER_API_KEY_PRO_1=      # 吕洞宾 - 正方一辩
OPENROUTER_API_KEY_CON_1=      # 何仙姑 - 反方一辩
OPENROUTER_API_KEY_PRO_2=      # 张果老 - 正方二辩
OPENROUTER_API_KEY_CON_2=      # 韩湘子 - 反方二辩
OPENROUTER_API_KEY_PRO_3=      # 汉钟离 - 正方三辩
OPENROUTER_API_KEY_CON_3=      # 蓝采和 - 反方三辩
OPENROUTER_API_KEY_PRO_4=      # 曹国舅 - 正方四辩
OPENROUTER_API_KEY_CON_4=      # 铁拐李 - 反方四辩

# LiteLLM 配置 (用于 Jixia 裁判和统计员模型)
LITELLM_BASE_URL=
LITELLM_API_KEY=

# ===========================================
# 三清炼丹系统配置
# ===========================================
# 太上老君API密钥（魔搭）
TAISHANG_API_KEY=

# 八仙炼丹炉API密钥（OpenRouter）
BAXIAN_API_KEY=

# ===========================================
# 其他可选配置
# ===========================================

# Heroku PostgreSQL 连接（如果使用）
# DATABASE_URL=

# OpenAI API (如果直接使用)
# OPENAI_API_KEY=

# Alpha Vantage API
# ALPHA_VANTAGE_API_KEY=

# Polygon.io API
# POLYGON_API_KEY=