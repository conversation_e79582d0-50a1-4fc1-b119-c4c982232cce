# 炼妖壶金融AI系统需求文档

## 当前版本：v2.0.0

## 最新更新摘要
- 新增七层认知架构模型（L1-L7）
- 引入六壬察心系统作为市场情绪识别模块
- 完善FSRP协议栈定义
- 优化AI角色成长体系与决策流程

## 需求清单

| 编号 | 标题                       | 类型       | 优先级 | 状态 | 执行者             | 相关模块         |
|------|----------------------------|------------|--------|------|--------------------|------------------|
| R001 | 创建FSRP协议栈              | Feature    | High   | Todo | 协议设计组         | 系统核心         |
| R002 | 实现六壬察心模块            | Enhancement| Medium | Todo | AI情绪团队         | 自然语言处理     |
| R003 | 构建L7决策输出格式规范      | Feature    | High   | Todo | 决策引擎组         | 交易接口         |
| R004 | 设计七层架构可视化面板      | Feature    | Medium | Todo | UI团队             | Streamlit展示    |
| R005 | 实现卦象识别器与FSRP报文封装 | Feature    | High   | Todo | AI通信组           | Agent交互        |
| R006 | 构建太上老君的语义汇总逻辑   | Feature    | High   | Todo | 汇总逻辑组         | 信息聚合         |
| R007 | 实现灵宝道君验证循环机制     | Feature    | High   | Todo | 验证逻辑组         | 决策闭环         |
| R008 | 支持用户分级服务与功能限制   | Feature    | Medium | Todo | 权限控制组         | 用户体验         |
| R009 | 建立AI角色记忆系统          | Enhancement| Medium | Todo | 记忆系统组         | 持续学习         |
| R010 | 优化N8N工作流集成           | Enhancement| Medium | Todo | 自动化流程组       | 工作流引擎       |

## 子系统划分

### 数据采集子系统
- 负责Agent列表：RSS采集器、新闻源解析器
- 核心功能描述：多源新闻聚合、影响力评分计算、LSA式主张生成
- 关联需求编号：R001, R002, R005

### AI分析子系统
- 负责Agent列表：八仙辩论组、三清验证组、元始天尊
- 核心功能描述：多智能体协作、共识机制构建、最终决策输出
- 关联需求编号：R005, R006, R007, R009

### 可视化与交互子系统
- 负责Agent列表：Streamlit前端、Plotly图表引擎
- 核心功能描述：市场应力图谱、每日一卦模块、AI辩论摘要展示
- 关联需求编号：R004, R008

### 系统安全与扩展子系统
- 负责Agent列表：FastAPI网关、身份认证服务
- 核心功能描述：统一登录接口、权限控制、API密钥管理
- 关联需求编号：R003, R008

## 需求变更记录

| 日期       | 变更内容                   | 操作人     | 影响范围               |
|------------|----------------------------|------------|------------------------|
| 2025-07-11 | 添加七层架构模型需求         | Lingma     | 系统核心/需求治理      |
| 2025-07-11 | 新增六壬察心模块需求         | Lingma     | 自然语言处理/AI情绪识别 |
| 2025-07-11 | 补充L7决策输出格式规范       | Lingma     | 交易接口/决策闭环      |
| 2025-07-11 | 增加用户分级服务支持         | ProductMgr | 用户体验/权限控制      |