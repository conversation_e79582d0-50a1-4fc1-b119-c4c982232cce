# 🎭 稷下学宫 - AI仙人军团文档配置

site_name: 炼妖壶 (Cauldron)
site_description: 'AI驱动的投资决策系统 - 稷下学宫智能体辩论平台'
site_author: '炼妖壶开发团队'
site_url: 'https://jingminzhang.github.io/cauldron'

# 仓库信息
repo_name: 'jingminzhang/cauldron'
repo_url: 'https://github.com/jingminzhang/cauldron'
edit_uri: 'edit/main/docs/'

# 版权信息
copyright: 'Copyright &copy; 2025 炼妖壶项目 - MIT License'

# 主题配置
theme:
  name: material
  language: zh
  
  # 中国风 + 科技感配色
  palette:
    # 明亮模式
    - media: "(prefers-color-scheme: light)"
      scheme: default
      primary: indigo      # 稷下学宫深蓝
      accent: amber        # 仙人金色
      toggle:
        icon: material/brightness-7
        name: 切换到暗黑模式
    
    # 暗黑模式  
    - media: "(prefers-color-scheme: dark)"
      scheme: slate
      primary: indigo
      accent: amber
      toggle:
        icon: material/brightness-4
        name: 切换到明亮模式
  
  # 字体配置
  font:
    text: 'Noto Sans SC'  # 中文友好
    code: 'JetBrains Mono'
  
  # 图标和Logo
  icon:
    repo: fontawesome/brands/github
    logo: material/school
  
  # 功能特性
  features:
    - navigation.instant      # 即时导航
    - navigation.tracking     # 锚点跟踪
    - navigation.tabs         # 顶部标签页
    - navigation.tabs.sticky  # 粘性标签页
    - navigation.sections     # 导航分组
    - navigation.expand       # 展开导航
    - navigation.indexes      # 索引页面
    - navigation.top          # 返回顶部
    - search.highlight        # 搜索高亮
    - search.share           # 搜索分享
    - search.suggest         # 搜索建议
    - content.code.annotate  # 代码注释
    - content.code.copy      # 代码复制
    - content.tabs.link      # 标签页链接

# 导航结构
nav:
  - 🏠 首页:
    - index.md
    - 发展路线图: roadmap.md

  - 🚀 快速开始:
    - 快速开始: getting-started/quick-start.md
    - 入门教程: tutorials/getting_started.md
    - 使用指南: how-to-guides/getting_started.md

  - 🎭 AI仙人:
    - AI人格设计: reference/ai_personalities.md

  - 🏗️ 架构设计:
    - 架构笔记: architecture/ARCHITECTURE_NOTES.md
    - 全能AI智能体架构: architecture/Omnipresent_AI_Agent_Architecture.md

  - ⚡ 核心功能:
    - 功能概览: how-to-guides/features.md
    - 产品定位: how-to-guides/offering.md

  - 🔧 技术文档:
    - 数据库概览: reference/database_overview.md
    - MCP设置: guides/MCP_SETUP.md
    - IB基础指南: how-to-guides/ib_fundamentals_guide.md

  - 📜 项目愿景:
    - 三层架构: vision/three-tiers.md
    - 开源路线图: guides/OPENSOURCE_ROADMAP.md

  - 🚀 部署指南:
    - 部署指南: how-to-guides/deployment/

  - 📡 API文档:
    - API参考: api/

  - 🤝 贡献指南:
    - 贡献指南: CONTRIBUTING.md
    - 文档重组计划: guides/DOCS_REORGANIZATION_PLAN.md

# Markdown扩展
markdown_extensions:
  # Python Markdown
  - abbr
  - admonition
  - attr_list
  - def_list
  - footnotes
  - md_in_html
  - toc:
      permalink: true
      title: 目录
  
  # Python Markdown Extensions
  - pymdownx.arithmatex:
      generic: true
  - pymdownx.betterem:
      smart_enable: all
  - pymdownx.caret
  - pymdownx.details
  - pymdownx.emoji
  - pymdownx.highlight:
      anchor_linenums: true
      line_spans: __span
      pygments_lang_class: true
  - pymdownx.inlinehilite
  - pymdownx.keys
  - pymdownx.magiclink:
      repo_url_shorthand: true
      user: your-username
      repo: jixia-academy
  - pymdownx.mark
  - pymdownx.smartsymbols
  - pymdownx.superfences
  - pymdownx.tabbed:
      alternate_style: true
  - pymdownx.tasklist:
      custom_checkbox: true
  - pymdownx.tilde

# 插件配置
plugins:
  - search:
      lang: 
        - zh
        - en
      separator: '[\s\-,:!=\[\]()"/]+|(?!\b)(?=[A-Z][a-z])|\.(?!\d)|&[lg]t;'
  - minify:
      minify_html: true
  - git-revision-date-localized:
      enable_creation_date: true
      type: datetime
      locale: zh

# 额外配置
extra:
  # 社交链接
  social:
    - icon: fontawesome/brands/github
      link: https://github.com/your-username/jixia-academy
      name: GitHub仓库
    - icon: fontawesome/brands/mastodon
      link: https://your-mastodon-instance.com/@taishang_laojun
      name: 太上老君
    - icon: fontawesome/brands/twitter
      link: https://twitter.com/jixia_academy
      name: Twitter
    - icon: fontawesome/solid/paper-plane
      link: mailto:<EMAIL>
      name: 联系我们
  
  # 版本信息
  version:
    provider: mike
    default: latest
  
  # 分析统计
  analytics:
    provider: google
    property: G-XXXXXXXXXX  # 替换为你的Google Analytics ID
    feedback:
      title: 这个页面有帮助吗？
      ratings:
        - icon: material/emoticon-happy-outline
          name: 有帮助
          data: 1
          note: >-
            感谢您的反馈！
        - icon: material/emoticon-sad-outline
          name: 需要改进
          data: 0
          note: >-
            感谢您的反馈！请通过GitHub Issues告诉我们如何改进。

# 额外CSS和JS
extra_css:
  - assets/stylesheets/extra.css

extra_javascript:
  - assets/javascripts/mathjax.js
  - https://polyfill.io/v3/polyfill.min.js?features=es6
  - https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js

# 严格模式 - 暂时禁用以允许构建
strict: false
