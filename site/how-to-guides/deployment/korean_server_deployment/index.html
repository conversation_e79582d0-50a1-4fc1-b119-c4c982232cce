<!doctype html><html lang=zh class=no-js> <head><meta charset=utf-8><meta name=viewport content="width=device-width,initial-scale=1"><meta name=description content="AI驱动的投资决策系统 - 稷下学宫智能体辩论平台"><meta name=author content=炼妖壶开发团队><link href=https://jingminzhang.github.io/cauldron/how-to-guides/deployment/korean_server_deployment/ rel=canonical><link rel=icon href=../../../assets/images/favicon.png><meta name=generator content="mkdocs-1.6.1, mkdocs-material-9.6.15"><title>韩国服务器MCP生态系统部署指南 - 炼妖壶 (Cauldron)</title><link rel=stylesheet href=../../../assets/stylesheets/main.342714a4.min.css><link rel=stylesheet href=../../../assets/stylesheets/palette.06af60db.min.css><link rel=preconnect href=https://fonts.gstatic.com crossorigin><link rel=stylesheet href="https://fonts.googleapis.com/css?family=Noto+Sans+SC:300,300i,400,400i,700,700i%7CJetBrains+Mono:400,400i,700,700i&display=fallback"><style>:root{--md-text-font:"Noto Sans SC";--md-code-font:"JetBrains Mono"}</style><link rel=stylesheet href=../../../assets/stylesheets/extra.css><script>__md_scope=new URL("../../..",location),__md_hash=e=>[...e].reduce(((e,_)=>(e<<5)-e+_.charCodeAt(0)),0),__md_get=(e,_=localStorage,t=__md_scope)=>JSON.parse(_.getItem(t.pathname+"."+e)),__md_set=(e,_,t=localStorage,a=__md_scope)=>{try{t.setItem(a.pathname+"."+e,JSON.stringify(_))}catch(e){}}</script><script id=__analytics>function __md_analytics(){function e(){dataLayer.push(arguments)}window.dataLayer=window.dataLayer||[],e("js",new Date),e("config","G-XXXXXXXXXX"),document.addEventListener("DOMContentLoaded",(function(){document.forms.search&&document.forms.search.query.addEventListener("blur",(function(){this.value&&e("event","search",{search_term:this.value})}));document$.subscribe((function(){var t=document.forms.feedback;if(void 0!==t)for(var a of t.querySelectorAll("[type=submit]"))a.addEventListener("click",(function(a){a.preventDefault();var n=document.location.pathname,d=this.getAttribute("data-md-value");e("event","feedback",{page:n,data:d}),t.firstElementChild.disabled=!0;var r=t.querySelector(".md-feedback__note [data-md-value='"+d+"']");r&&(r.hidden=!1)})),t.hidden=!1})),location$.subscribe((function(t){e("config","G-XXXXXXXXXX",{page_path:t.pathname})}))}));var t=document.createElement("script");t.async=!0,t.src="https://www.googletagmanager.com/gtag/js?id=G-XXXXXXXXXX",document.getElementById("__analytics").insertAdjacentElement("afterEnd",t)}</script><script>"undefined"!=typeof __md_analytics&&__md_analytics()</script></head> <body dir=ltr data-md-color-scheme=default data-md-color-primary=indigo data-md-color-accent=amber> <input class=md-toggle data-md-toggle=drawer type=checkbox id=__drawer autocomplete=off> <input class=md-toggle data-md-toggle=search type=checkbox id=__search autocomplete=off> <label class=md-overlay for=__drawer></label> <div data-md-component=skip> <a href=#mcp class=md-skip> 跳转至 </a> </div> <div data-md-component=announce> </div> <div data-md-color-scheme=default data-md-component=outdated hidden> </div> <header class="md-header md-header--shadow md-header--lifted" data-md-component=header> <nav class="md-header__inner md-grid" aria-label=页眉> <a href=../../.. title="炼妖壶 (Cauldron)" class="md-header__button md-logo" aria-label="炼妖壶 (Cauldron)" data-md-component=logo> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M12 3 1 9l11 6 9-4.91V17h2V9M5 13.18v4L12 21l7-3.82v-4L12 17z"/></svg> </a> <label class="md-header__button md-icon" for=__drawer> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M3 6h18v2H3zm0 5h18v2H3zm0 5h18v2H3z"/></svg> </label> <div class=md-header__title data-md-component=header-title> <div class=md-header__ellipsis> <div class=md-header__topic> <span class=md-ellipsis> 炼妖壶 (Cauldron) </span> </div> <div class=md-header__topic data-md-component=header-topic> <span class=md-ellipsis> 韩国服务器MCP生态系统部署指南 </span> </div> </div> </div> <form class=md-header__option data-md-component=palette> <input class=md-option data-md-color-media="(prefers-color-scheme: light)" data-md-color-scheme=default data-md-color-primary=indigo data-md-color-accent=amber aria-label=切换到暗黑模式 type=radio name=__palette id=__palette_0> <label class="md-header__button md-icon" title=切换到暗黑模式 for=__palette_1 hidden> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M12 8a4 4 0 0 0-4 4 4 4 0 0 0 4 4 4 4 0 0 0 4-4 4 4 0 0 0-4-4m0 10a6 6 0 0 1-6-6 6 6 0 0 1 6-6 6 6 0 0 1 6 6 6 6 0 0 1-6 6m8-9.31V4h-4.69L12 .69 8.69 4H4v4.69L.69 12 4 15.31V20h4.69L12 23.31 15.31 20H20v-4.69L23.31 12z"/></svg> </label> <input class=md-option data-md-color-media="(prefers-color-scheme: dark)" data-md-color-scheme=slate data-md-color-primary=indigo data-md-color-accent=amber aria-label=切换到明亮模式 type=radio name=__palette id=__palette_1> <label class="md-header__button md-icon" title=切换到明亮模式 for=__palette_0 hidden> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M12 18c-.89 0-1.74-.2-2.5-.55C11.56 16.5 13 14.42 13 12s-1.44-4.5-3.5-5.45C10.26 6.2 11.11 6 12 6a6 6 0 0 1 6 6 6 6 0 0 1-6 6m8-9.31V4h-4.69L12 .69 8.69 4H4v4.69L.69 12 4 15.31V20h4.69L12 23.31 15.31 20H20v-4.69L23.31 12z"/></svg> </label> </form> <script>var palette=__md_get("__palette");if(palette&&palette.color){if("(prefers-color-scheme)"===palette.color.media){var media=matchMedia("(prefers-color-scheme: light)"),input=document.querySelector(media.matches?"[data-md-color-media='(prefers-color-scheme: light)']":"[data-md-color-media='(prefers-color-scheme: dark)']");palette.color.media=input.getAttribute("data-md-color-media"),palette.color.scheme=input.getAttribute("data-md-color-scheme"),palette.color.primary=input.getAttribute("data-md-color-primary"),palette.color.accent=input.getAttribute("data-md-color-accent")}for(var[key,value]of Object.entries(palette.color))document.body.setAttribute("data-md-color-"+key,value)}</script> <label class="md-header__button md-icon" for=__search> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M9.5 3A6.5 6.5 0 0 1 16 9.5c0 1.61-.59 3.09-1.56 4.23l.27.27h.79l5 5-1.5 1.5-5-5v-.79l-.27-.27A6.52 6.52 0 0 1 9.5 16 6.5 6.5 0 0 1 3 9.5 6.5 6.5 0 0 1 9.5 3m0 2C7 5 5 7 5 9.5S7 14 9.5 14 14 12 14 9.5 12 5 9.5 5"/></svg> </label> <div class=md-search data-md-component=search role=dialog> <label class=md-search__overlay for=__search></label> <div class=md-search__inner role=search> <form class=md-search__form name=search> <input type=text class=md-search__input name=query aria-label=搜索 placeholder=搜索 autocapitalize=off autocorrect=off autocomplete=off spellcheck=false data-md-component=search-query required> <label class="md-search__icon md-icon" for=__search> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M9.5 3A6.5 6.5 0 0 1 16 9.5c0 1.61-.59 3.09-1.56 4.23l.27.27h.79l5 5-1.5 1.5-5-5v-.79l-.27-.27A6.52 6.52 0 0 1 9.5 16 6.5 6.5 0 0 1 3 9.5 6.5 6.5 0 0 1 9.5 3m0 2C7 5 5 7 5 9.5S7 14 9.5 14 14 12 14 9.5 12 5 9.5 5"/></svg> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M20 11v2H8l5.5 5.5-1.42 1.42L4.16 12l7.92-7.92L13.5 5.5 8 11z"/></svg> </label> <nav class=md-search__options aria-label=查找> <a href=javascript:void(0) class="md-search__icon md-icon" title=分享 aria-label=分享 data-clipboard data-clipboard-text data-md-component=search-share tabindex=-1> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M18 16.08c-.76 0-1.44.3-1.96.77L8.91 12.7c.05-.23.09-.46.09-.7s-.04-.47-.09-.7l7.05-4.11c.54.5 1.25.81 2.04.81a3 3 0 0 0 3-3 3 3 0 0 0-3-3 3 3 0 0 0-3 3c0 .***********.7L8.04 9.81C7.5 9.31 6.79 9 6 9a3 3 0 0 0-3 3 3 3 0 0 0 3 3c.79 0 1.5-.31 2.04-.81l7.12 4.15c-.05.21-.08.43-.08.66 0 1.61 1.31 2.91 2.92 2.91s2.92-1.3 2.92-2.91A2.92 2.92 0 0 0 18 16.08"/></svg> </a> <button type=reset class="md-search__icon md-icon" title=清空当前内容 aria-label=清空当前内容 tabindex=-1> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M19 6.41 17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/></svg> </button> </nav> <div class=md-search__suggest data-md-component=search-suggest></div> </form> <div class=md-search__output> <div class=md-search__scrollwrap tabindex=0 data-md-scrollfix> <div class=md-search-result data-md-component=search-result> <div class=md-search-result__meta> 正在初始化搜索引擎 </div> <ol class=md-search-result__list role=presentation></ol> </div> </div> </div> </div> </div> <div class=md-header__source> <a href=https://github.com/jingminzhang/cauldron title=前往仓库 class=md-source data-md-component=source> <div class="md-source__icon md-icon"> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 496 512"><!-- Font Awesome Free 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license/free (Icons: CC BY 4.0, Fonts: SIL OFL 1.1, Code: MIT License) Copyright 2024 Fonticons, Inc.--><path d="M165.9 397.4c0 2-2.3 3.6-5.2 3.6-3.3.3-5.6-1.3-5.6-3.6 0-2 2.3-3.6 5.2-3.6 3-.3 5.6 1.3 5.6 3.6m-31.1-4.5c-.7 2 1.3 4.3 4.3 4.9 2.6 1 5.6 0 6.2-2s-1.3-4.3-4.3-5.2c-2.6-.7-5.5.3-6.2 2.3m44.2-1.7c-2.9.7-4.9 2.6-4.6 4.9.3 2 2.9 3.3 5.9 2.6 2.9-.7 4.9-2.6 4.6-4.6-.3-1.9-3-3.2-5.9-2.9M244.8 8C106.1 8 0 113.3 0 252c0 110.9 69.8 205.8 169.5 239.2 12.8 2.3 17.3-5.6 17.3-12.1 0-6.2-.3-40.4-.3-61.4 0 0-70 15-84.7-29.8 0 0-11.4-29.1-27.8-36.6 0 0-22.9-15.7 1.6-15.4 0 0 24.9 2 38.6 25.8 21.9 38.6 58.6 27.5 72.9 20.9 2.3-16 8.8-27.1 16-33.7-55.9-6.2-112.3-14.3-112.3-110.5 0-27.5 7.6-41.3 23.6-58.9-2.6-6.5-11.1-33.3 2.6-67.9 20.9-6.5 69 27 69 27 20-5.6 41.5-8.5 62.8-8.5s42.8 2.9 62.8 8.5c0 0 48.1-33.6 69-27 13.7 34.7 5.2 61.4 2.6 67.9 16 17.7 25.8 31.5 25.8 58.9 0 96.5-58.9 104.2-114.8 110.5 9.2 7.9 17 22.9 17 46.4 0 33.7-.3 75.4-.3 83.6 0 6.5 4.6 14.4 17.3 12.1C428.2 457.8 496 362.9 496 252 496 113.3 383.5 8 244.8 8M97.2 352.9c-1.3 1-1 3.3.7 5.2 1.6 1.6 3.9 2.3 5.2 1 1.3-1 1-3.3-.7-5.2-1.6-1.6-3.9-2.3-5.2-1m-10.8-8.1c-.7 1.3.3 2.9 2.3 3.9 1.6 1 3.6.7 4.3-.7.7-1.3-.3-2.9-2.3-3.9-2-.6-3.6-.3-4.3.7m32.4 35.6c-1.6 1.3-1 4.3 1.3 6.2 2.3 2.3 5.2 2.6 6.5 1 1.3-1.3.7-4.3-1.3-6.2-2.2-2.3-5.2-2.6-6.5-1m-11.4-14.7c-1.6 1-1.6 3.6 0 5.9s4.3 3.3 5.6 2.3c1.6-1.3 1.6-3.9 0-6.2-1.4-2.3-4-3.3-5.6-2"/></svg> </div> <div class=md-source__repository> jingminzhang/cauldron </div> </a> </div> </nav> <nav class=md-tabs aria-label=标签 data-md-component=tabs> <div class=md-grid> <ul class=md-tabs__list> <li class=md-tabs__item> <a href=../../.. class=md-tabs__link> 🏠 首页 </a> </li> <li class=md-tabs__item> <a href=../../../getting-started/quick-start/ class=md-tabs__link> 🚀 快速开始 </a> </li> <li class=md-tabs__item> <a href=../../../reference/ai_personalities/ class=md-tabs__link> 🎭 AI仙人 </a> </li> <li class=md-tabs__item> <a href=../../../architecture/ARCHITECTURE_NOTES/ class=md-tabs__link> 🏗️ 架构设计 </a> </li> <li class=md-tabs__item> <a href=../../features/ class=md-tabs__link> ⚡ 核心功能 </a> </li> <li class=md-tabs__item> <a href=../../../reference/database_overview/ class=md-tabs__link> 🔧 技术文档 </a> </li> <li class=md-tabs__item> <a href=../../../vision/three-tiers/ class=md-tabs__link> 📜 项目愿景 </a> </li> <li class=md-tabs__item> <a href=../ class=md-tabs__link> 🚀 部署指南 </a> </li> <li class=md-tabs__item> <a href=../../../api/ class=md-tabs__link> 📡 API文档 </a> </li> <li class=md-tabs__item> <a href=../../../CONTRIBUTING/ class=md-tabs__link> 🤝 贡献指南 </a> </li> </ul> </div> </nav> </header> <div class=md-container data-md-component=container> <main class=md-main data-md-component=main> <div class="md-main__inner md-grid"> <div class="md-sidebar md-sidebar--primary" data-md-component=sidebar data-md-type=navigation> <div class=md-sidebar__scrollwrap> <div class=md-sidebar__inner> <nav class="md-nav md-nav--primary md-nav--lifted" aria-label=导航栏 data-md-level=0> <label class=md-nav__title for=__drawer> <a href=../../.. title="炼妖壶 (Cauldron)" class="md-nav__button md-logo" aria-label="炼妖壶 (Cauldron)" data-md-component=logo> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M12 3 1 9l11 6 9-4.91V17h2V9M5 13.18v4L12 21l7-3.82v-4L12 17z"/></svg> </a> 炼妖壶 (Cauldron) </label> <div class=md-nav__source> <a href=https://github.com/jingminzhang/cauldron title=前往仓库 class=md-source data-md-component=source> <div class="md-source__icon md-icon"> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 496 512"><!-- Font Awesome Free 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license/free (Icons: CC BY 4.0, Fonts: SIL OFL 1.1, Code: MIT License) Copyright 2024 Fonticons, Inc.--><path d="M165.9 397.4c0 2-2.3 3.6-5.2 3.6-3.3.3-5.6-1.3-5.6-3.6 0-2 2.3-3.6 5.2-3.6 3-.3 5.6 1.3 5.6 3.6m-31.1-4.5c-.7 2 1.3 4.3 4.3 4.9 2.6 1 5.6 0 6.2-2s-1.3-4.3-4.3-5.2c-2.6-.7-5.5.3-6.2 2.3m44.2-1.7c-2.9.7-4.9 2.6-4.6 4.9.3 2 2.9 3.3 5.9 2.6 2.9-.7 4.9-2.6 4.6-4.6-.3-1.9-3-3.2-5.9-2.9M244.8 8C106.1 8 0 113.3 0 252c0 110.9 69.8 205.8 169.5 239.2 12.8 2.3 17.3-5.6 17.3-12.1 0-6.2-.3-40.4-.3-61.4 0 0-70 15-84.7-29.8 0 0-11.4-29.1-27.8-36.6 0 0-22.9-15.7 1.6-15.4 0 0 24.9 2 38.6 25.8 21.9 38.6 58.6 27.5 72.9 20.9 2.3-16 8.8-27.1 16-33.7-55.9-6.2-112.3-14.3-112.3-110.5 0-27.5 7.6-41.3 23.6-58.9-2.6-6.5-11.1-33.3 2.6-67.9 20.9-6.5 69 27 69 27 20-5.6 41.5-8.5 62.8-8.5s42.8 2.9 62.8 8.5c0 0 48.1-33.6 69-27 13.7 34.7 5.2 61.4 2.6 67.9 16 17.7 25.8 31.5 25.8 58.9 0 96.5-58.9 104.2-114.8 110.5 9.2 7.9 17 22.9 17 46.4 0 33.7-.3 75.4-.3 83.6 0 6.5 4.6 14.4 17.3 12.1C428.2 457.8 496 362.9 496 252 496 113.3 383.5 8 244.8 8M97.2 352.9c-1.3 1-1 3.3.7 5.2 1.6 1.6 3.9 2.3 5.2 1 1.3-1 1-3.3-.7-5.2-1.6-1.6-3.9-2.3-5.2-1m-10.8-8.1c-.7 1.3.3 2.9 2.3 3.9 1.6 1 3.6.7 4.3-.7.7-1.3-.3-2.9-2.3-3.9-2-.6-3.6-.3-4.3.7m32.4 35.6c-1.6 1.3-1 4.3 1.3 6.2 2.3 2.3 5.2 2.6 6.5 1 1.3-1.3.7-4.3-1.3-6.2-2.2-2.3-5.2-2.6-6.5-1m-11.4-14.7c-1.6 1-1.6 3.6 0 5.9s4.3 3.3 5.6 2.3c1.6-1.3 1.6-3.9 0-6.2-1.4-2.3-4-3.3-5.6-2"/></svg> </div> <div class=md-source__repository> jingminzhang/cauldron </div> </a> </div> <ul class=md-nav__list data-md-scrollfix> <li class="md-nav__item md-nav__item--nested"> <input class="md-nav__toggle md-toggle md-toggle--indeterminate" type=checkbox id=__nav_1> <div class="md-nav__link md-nav__container"> <a href=../../.. class="md-nav__link "> <span class=md-ellipsis> 🏠 首页 </span> </a> <label class="md-nav__link " for=__nav_1 id=__nav_1_label tabindex=0> <span class="md-nav__icon md-icon"></span> </label> </div> <nav class=md-nav data-md-level=1 aria-labelledby=__nav_1_label aria-expanded=false> <label class=md-nav__title for=__nav_1> <span class="md-nav__icon md-icon"></span> 🏠 首页 </label> <ul class=md-nav__list data-md-scrollfix> <li class=md-nav__item> <a href=../../../roadmap/ class=md-nav__link> <span class=md-ellipsis> 发展路线图 </span> </a> </li> </ul> </nav> </li> <li class="md-nav__item md-nav__item--nested"> <input class="md-nav__toggle md-toggle md-toggle--indeterminate" type=checkbox id=__nav_2> <label class=md-nav__link for=__nav_2 id=__nav_2_label tabindex=0> <span class=md-ellipsis> 🚀 快速开始 </span> <span class="md-nav__icon md-icon"></span> </label> <nav class=md-nav data-md-level=1 aria-labelledby=__nav_2_label aria-expanded=false> <label class=md-nav__title for=__nav_2> <span class="md-nav__icon md-icon"></span> 🚀 快速开始 </label> <ul class=md-nav__list data-md-scrollfix> <li class=md-nav__item> <a href=../../../getting-started/quick-start/ class=md-nav__link> <span class=md-ellipsis> 快速开始 </span> </a> </li> <li class=md-nav__item> <a href=../../../tutorials/getting_started/ class=md-nav__link> <span class=md-ellipsis> 入门教程 </span> </a> </li> <li class=md-nav__item> <a href=../../getting_started/ class=md-nav__link> <span class=md-ellipsis> 使用指南 </span> </a> </li> </ul> </nav> </li> <li class="md-nav__item md-nav__item--nested"> <input class="md-nav__toggle md-toggle md-toggle--indeterminate" type=checkbox id=__nav_3> <label class=md-nav__link for=__nav_3 id=__nav_3_label tabindex=0> <span class=md-ellipsis> 🎭 AI仙人 </span> <span class="md-nav__icon md-icon"></span> </label> <nav class=md-nav data-md-level=1 aria-labelledby=__nav_3_label aria-expanded=false> <label class=md-nav__title for=__nav_3> <span class="md-nav__icon md-icon"></span> 🎭 AI仙人 </label> <ul class=md-nav__list data-md-scrollfix> <li class=md-nav__item> <a href=../../../reference/ai_personalities/ class=md-nav__link> <span class=md-ellipsis> AI人格设计 </span> </a> </li> </ul> </nav> </li> <li class="md-nav__item md-nav__item--nested"> <input class="md-nav__toggle md-toggle md-toggle--indeterminate" type=checkbox id=__nav_4> <label class=md-nav__link for=__nav_4 id=__nav_4_label tabindex=0> <span class=md-ellipsis> 🏗️ 架构设计 </span> <span class="md-nav__icon md-icon"></span> </label> <nav class=md-nav data-md-level=1 aria-labelledby=__nav_4_label aria-expanded=false> <label class=md-nav__title for=__nav_4> <span class="md-nav__icon md-icon"></span> 🏗️ 架构设计 </label> <ul class=md-nav__list data-md-scrollfix> <li class=md-nav__item> <a href=../../../architecture/ARCHITECTURE_NOTES/ class=md-nav__link> <span class=md-ellipsis> 架构笔记 </span> </a> </li> <li class=md-nav__item> <a href=../../../architecture/Omnipresent_AI_Agent_Architecture/ class=md-nav__link> <span class=md-ellipsis> 全能AI智能体架构 </span> </a> </li> </ul> </nav> </li> <li class="md-nav__item md-nav__item--nested"> <input class="md-nav__toggle md-toggle md-toggle--indeterminate" type=checkbox id=__nav_5> <label class=md-nav__link for=__nav_5 id=__nav_5_label tabindex=0> <span class=md-ellipsis> ⚡ 核心功能 </span> <span class="md-nav__icon md-icon"></span> </label> <nav class=md-nav data-md-level=1 aria-labelledby=__nav_5_label aria-expanded=false> <label class=md-nav__title for=__nav_5> <span class="md-nav__icon md-icon"></span> ⚡ 核心功能 </label> <ul class=md-nav__list data-md-scrollfix> <li class=md-nav__item> <a href=../../features/ class=md-nav__link> <span class=md-ellipsis> 功能概览 </span> </a> </li> <li class=md-nav__item> <a href=../../offering/ class=md-nav__link> <span class=md-ellipsis> 产品定位 </span> </a> </li> </ul> </nav> </li> <li class="md-nav__item md-nav__item--nested"> <input class="md-nav__toggle md-toggle md-toggle--indeterminate" type=checkbox id=__nav_6> <label class=md-nav__link for=__nav_6 id=__nav_6_label tabindex=0> <span class=md-ellipsis> 🔧 技术文档 </span> <span class="md-nav__icon md-icon"></span> </label> <nav class=md-nav data-md-level=1 aria-labelledby=__nav_6_label aria-expanded=false> <label class=md-nav__title for=__nav_6> <span class="md-nav__icon md-icon"></span> 🔧 技术文档 </label> <ul class=md-nav__list data-md-scrollfix> <li class=md-nav__item> <a href=../../../reference/database_overview/ class=md-nav__link> <span class=md-ellipsis> 数据库概览 </span> </a> </li> <li class=md-nav__item> <a href=../../../guides/MCP_SETUP/ class=md-nav__link> <span class=md-ellipsis> MCP设置 </span> </a> </li> <li class=md-nav__item> <a href=../../ib_fundamentals_guide/ class=md-nav__link> <span class=md-ellipsis> IB基础指南 </span> </a> </li> </ul> </nav> </li> <li class="md-nav__item md-nav__item--nested"> <input class="md-nav__toggle md-toggle md-toggle--indeterminate" type=checkbox id=__nav_7> <label class=md-nav__link for=__nav_7 id=__nav_7_label tabindex=0> <span class=md-ellipsis> 📜 项目愿景 </span> <span class="md-nav__icon md-icon"></span> </label> <nav class=md-nav data-md-level=1 aria-labelledby=__nav_7_label aria-expanded=false> <label class=md-nav__title for=__nav_7> <span class="md-nav__icon md-icon"></span> 📜 项目愿景 </label> <ul class=md-nav__list data-md-scrollfix> <li class=md-nav__item> <a href=../../../vision/three-tiers/ class=md-nav__link> <span class=md-ellipsis> 三层架构 </span> </a> </li> <li class=md-nav__item> <a href=../../../guides/OPENSOURCE_ROADMAP/ class=md-nav__link> <span class=md-ellipsis> 开源路线图 </span> </a> </li> </ul> </nav> </li> <li class="md-nav__item md-nav__item--nested"> <input class="md-nav__toggle md-toggle md-toggle--indeterminate" type=checkbox id=__nav_8> <label class=md-nav__link for=__nav_8 id=__nav_8_label tabindex=0> <span class=md-ellipsis> 🚀 部署指南 </span> <span class="md-nav__icon md-icon"></span> </label> <nav class=md-nav data-md-level=1 aria-labelledby=__nav_8_label aria-expanded=false> <label class=md-nav__title for=__nav_8> <span class="md-nav__icon md-icon"></span> 🚀 部署指南 </label> <ul class=md-nav__list data-md-scrollfix> <li class=md-nav__item> <a href=../ class=md-nav__link> <span class=md-ellipsis> 部署指南 </span> </a> </li> </ul> </nav> </li> <li class="md-nav__item md-nav__item--nested"> <input class="md-nav__toggle md-toggle md-toggle--indeterminate" type=checkbox id=__nav_9> <label class=md-nav__link for=__nav_9 id=__nav_9_label tabindex=0> <span class=md-ellipsis> 📡 API文档 </span> <span class="md-nav__icon md-icon"></span> </label> <nav class=md-nav data-md-level=1 aria-labelledby=__nav_9_label aria-expanded=false> <label class=md-nav__title for=__nav_9> <span class="md-nav__icon md-icon"></span> 📡 API文档 </label> <ul class=md-nav__list data-md-scrollfix> <li class=md-nav__item> <a href=../../../api/ class=md-nav__link> <span class=md-ellipsis> API参考 </span> </a> </li> </ul> </nav> </li> <li class="md-nav__item md-nav__item--nested"> <input class="md-nav__toggle md-toggle md-toggle--indeterminate" type=checkbox id=__nav_10> <label class=md-nav__link for=__nav_10 id=__nav_10_label tabindex=0> <span class=md-ellipsis> 🤝 贡献指南 </span> <span class="md-nav__icon md-icon"></span> </label> <nav class=md-nav data-md-level=1 aria-labelledby=__nav_10_label aria-expanded=false> <label class=md-nav__title for=__nav_10> <span class="md-nav__icon md-icon"></span> 🤝 贡献指南 </label> <ul class=md-nav__list data-md-scrollfix> <li class=md-nav__item> <a href=../../../CONTRIBUTING/ class=md-nav__link> <span class=md-ellipsis> 贡献指南 </span> </a> </li> <li class=md-nav__item> <a href=../../../guides/DOCS_REORGANIZATION_PLAN/ class=md-nav__link> <span class=md-ellipsis> 文档重组计划 </span> </a> </li> </ul> </nav> </li> </ul> </nav> </div> </div> </div> <div class="md-sidebar md-sidebar--secondary" data-md-component=sidebar data-md-type=toc> <div class=md-sidebar__scrollwrap> <div class=md-sidebar__inner> <nav class="md-nav md-nav--secondary" aria-label=目录> <label class=md-nav__title for=__toc> <span class="md-nav__icon md-icon"></span> 目录 </label> <ul class=md-nav__list data-md-component=toc data-md-scrollfix> <li class=md-nav__item> <a href=#_1 class=md-nav__link> <span class=md-ellipsis> 🏗️ 架构设计 </span> </a> </li> <li class=md-nav__item> <a href=#_2 class=md-nav__link> <span class=md-ellipsis> 📦 部署清单 </span> </a> <nav class=md-nav aria-label="📦 部署清单"> <ul class=md-nav__list> <li class=md-nav__item> <a href=#1 class=md-nav__link> <span class=md-ellipsis> 1. 基础环境准备 </span> </a> </li> <li class=md-nav__item> <a href=#2 class=md-nav__link> <span class=md-ellipsis> 2. 目录结构 </span> </a> </li> <li class=md-nav__item> <a href=#3-docker-compose class=md-nav__link> <span class=md-ellipsis> 3. Docker Compose配置 </span> </a> </li> <li class=md-nav__item> <a href=#4 class=md-nav__link> <span class=md-ellipsis> 4. 环境变量配置 </span> </a> </li> <li class=md-nav__item> <a href=#5-mcp class=md-nav__link> <span class=md-ellipsis> 5. MCP服务器实现 </span> </a> <nav class=md-nav aria-label="5. MCP服务器实现"> <ul class=md-nav__list> <li class=md-nav__item> <a href=#mcp-mcp-serverscryptodockerfile class=md-nav__link> <span class=md-ellipsis> 加密货币MCP服务器 (mcp-servers/crypto/Dockerfile) </span> </a> </li> <li class=md-nav__item> <a href=#mcp-mcp-serverscryptocrypto_mcp_serverpy class=md-nav__link> <span class=md-ellipsis> 加密货币MCP服务器 (mcp-servers/crypto/crypto_mcp_server.py) </span> </a> </li> </ul> </nav> </li> <li class=md-nav__item> <a href=#6-n8n class=md-nav__link> <span class=md-ellipsis> 6. N8N工作流配置 </span> </a> <nav class=md-nav aria-label="6. N8N工作流配置"> <ul class=md-nav__list> <li class=md-nav__item> <a href=#_3 class=md-nav__link> <span class=md-ellipsis> 自动化工作流示例 </span> </a> </li> </ul> </nav> </li> <li class=md-nav__item> <a href=#7-nginx class=md-nav__link> <span class=md-ellipsis> 7. Nginx配置 </span> </a> </li> <li class=md-nav__item> <a href=#8 class=md-nav__link> <span class=md-ellipsis> 8. 部署命令 </span> </a> </li> <li class=md-nav__item> <a href=#9 class=md-nav__link> <span class=md-ellipsis> 9. 监控和维护 </span> </a> </li> <li class=md-nav__item> <a href=#10 class=md-nav__link> <span class=md-ellipsis> 10. 安全配置 </span> </a> </li> </ul> </nav> </li> <li class=md-nav__item> <a href=#_4 class=md-nav__link> <span class=md-ellipsis> 🚀 使用方式 </span> </a> <nav class=md-nav aria-label="🚀 使用方式"> <ul class=md-nav__list> <li class=md-nav__item> <a href=#_5 class=md-nav__link> <span class=md-ellipsis> 本地连接韩国服务器 </span> </a> </li> <li class=md-nav__item> <a href=#n8n class=md-nav__link> <span class=md-ellipsis> N8N工作流触发 </span> </a> </li> </ul> </nav> </li> <li class=md-nav__item> <a href=#_6 class=md-nav__link> <span class=md-ellipsis> 💰 成本估算 </span> </a> </li> </ul> </nav> </div> </div> </div> <div class=md-content data-md-component=content> <article class="md-content__inner md-typeset"> <h1 id=mcp>韩国服务器MCP生态系统部署指南<a class=headerlink href=#mcp title="Permanent link">&para;</a></h1> <h2 id=_1>🏗️ 架构设计<a class=headerlink href=#_1 title="Permanent link">&para;</a></h2> <div class="language-text highlight"><pre><span></span><code><span id=__span-0-1><a id=__codelineno-0-1 name=__codelineno-0-1 href=#__codelineno-0-1></a>韩国服务器 (Korean Server)
</span><span id=__span-0-2><a id=__codelineno-0-2 name=__codelineno-0-2 href=#__codelineno-0-2></a>├── N8N工作流引擎 (端口: 5678)
</span><span id=__span-0-3><a id=__codelineno-0-3 name=__codelineno-0-3 href=#__codelineno-0-3></a>├── MCP服务器集群 (端口: 8000-8010)
</span><span id=__span-0-4><a id=__codelineno-0-4 name=__codelineno-0-4 href=#__codelineno-0-4></a>│   ├── crypto-mcp-server (8001)
</span><span id=__span-0-5><a id=__codelineno-0-5 name=__codelineno-0-5 href=#__codelineno-0-5></a>│   ├── news-mcp-server (8002)
</span><span id=__span-0-6><a id=__codelineno-0-6 name=__codelineno-0-6 href=#__codelineno-0-6></a>│   ├── finance-mcp-server (8003)
</span><span id=__span-0-7><a id=__codelineno-0-7 name=__codelineno-0-7 href=#__codelineno-0-7></a>│   └── taigong-bridge-server (8004)
</span><span id=__span-0-8><a id=__codelineno-0-8 name=__codelineno-0-8 href=#__codelineno-0-8></a>├── Nginx反向代理 (端口: 80/443)
</span><span id=__span-0-9><a id=__codelineno-0-9 name=__codelineno-0-9 href=#__codelineno-0-9></a>└── Redis缓存 (端口: 6379)
</span></code></pre></div> <h2 id=_2>📦 部署清单<a class=headerlink href=#_2 title="Permanent link">&para;</a></h2> <h3 id=1>1. 基础环境准备<a class=headerlink href=#1 title="Permanent link">&para;</a></h3> <div class="language-bash highlight"><pre><span></span><code><span id=__span-1-1><a id=__codelineno-1-1 name=__codelineno-1-1 href=#__codelineno-1-1></a><span class=c1># 更新系统</span>
</span><span id=__span-1-2><a id=__codelineno-1-2 name=__codelineno-1-2 href=#__codelineno-1-2></a>sudo<span class=w> </span>apt<span class=w> </span>update<span class=w> </span><span class=o>&amp;&amp;</span><span class=w> </span>sudo<span class=w> </span>apt<span class=w> </span>upgrade<span class=w> </span>-y
</span><span id=__span-1-3><a id=__codelineno-1-3 name=__codelineno-1-3 href=#__codelineno-1-3></a>
</span><span id=__span-1-4><a id=__codelineno-1-4 name=__codelineno-1-4 href=#__codelineno-1-4></a><span class=c1># 安装Docker和Docker Compose</span>
</span><span id=__span-1-5><a id=__codelineno-1-5 name=__codelineno-1-5 href=#__codelineno-1-5></a>curl<span class=w> </span>-fsSL<span class=w> </span>https://get.docker.com<span class=w> </span>-o<span class=w> </span>get-docker.sh
</span><span id=__span-1-6><a id=__codelineno-1-6 name=__codelineno-1-6 href=#__codelineno-1-6></a>sudo<span class=w> </span>sh<span class=w> </span>get-docker.sh
</span><span id=__span-1-7><a id=__codelineno-1-7 name=__codelineno-1-7 href=#__codelineno-1-7></a>sudo<span class=w> </span>curl<span class=w> </span>-L<span class=w> </span><span class=s2>&quot;https://github.com/docker/compose/releases/download/v2.20.0/docker-compose-</span><span class=k>$(</span>uname<span class=w> </span>-s<span class=k>)</span><span class=s2>-</span><span class=k>$(</span>uname<span class=w> </span>-m<span class=k>)</span><span class=s2>&quot;</span><span class=w> </span>-o<span class=w> </span>/usr/local/bin/docker-compose
</span><span id=__span-1-8><a id=__codelineno-1-8 name=__codelineno-1-8 href=#__codelineno-1-8></a>sudo<span class=w> </span>chmod<span class=w> </span>+x<span class=w> </span>/usr/local/bin/docker-compose
</span><span id=__span-1-9><a id=__codelineno-1-9 name=__codelineno-1-9 href=#__codelineno-1-9></a>
</span><span id=__span-1-10><a id=__codelineno-1-10 name=__codelineno-1-10 href=#__codelineno-1-10></a><span class=c1># 安装Node.js (N8N需要)</span>
</span><span id=__span-1-11><a id=__codelineno-1-11 name=__codelineno-1-11 href=#__codelineno-1-11></a>curl<span class=w> </span>-fsSL<span class=w> </span>https://deb.nodesource.com/setup_18.x<span class=w> </span><span class=p>|</span><span class=w> </span>sudo<span class=w> </span>-E<span class=w> </span>bash<span class=w> </span>-
</span><span id=__span-1-12><a id=__codelineno-1-12 name=__codelineno-1-12 href=#__codelineno-1-12></a>sudo<span class=w> </span>apt-get<span class=w> </span>install<span class=w> </span>-y<span class=w> </span>nodejs
</span><span id=__span-1-13><a id=__codelineno-1-13 name=__codelineno-1-13 href=#__codelineno-1-13></a>
</span><span id=__span-1-14><a id=__codelineno-1-14 name=__codelineno-1-14 href=#__codelineno-1-14></a><span class=c1># 安装Python (MCP服务器需要)</span>
</span><span id=__span-1-15><a id=__codelineno-1-15 name=__codelineno-1-15 href=#__codelineno-1-15></a>sudo<span class=w> </span>apt<span class=w> </span>install<span class=w> </span>python3<span class=w> </span>python3-pip<span class=w> </span>python3-venv<span class=w> </span>-y
</span></code></pre></div> <h3 id=2>2. 目录结构<a class=headerlink href=#2 title="Permanent link">&para;</a></h3> <div class="language-bash highlight"><pre><span></span><code><span id=__span-2-1><a id=__codelineno-2-1 name=__codelineno-2-1 href=#__codelineno-2-1></a>mkdir<span class=w> </span>-p<span class=w> </span>/opt/taigong-mcp-ecosystem
</span><span id=__span-2-2><a id=__codelineno-2-2 name=__codelineno-2-2 href=#__codelineno-2-2></a><span class=nb>cd</span><span class=w> </span>/opt/taigong-mcp-ecosystem
</span><span id=__span-2-3><a id=__codelineno-2-3 name=__codelineno-2-3 href=#__codelineno-2-3></a>
</span><span id=__span-2-4><a id=__codelineno-2-4 name=__codelineno-2-4 href=#__codelineno-2-4></a><span class=c1># 创建目录结构</span>
</span><span id=__span-2-5><a id=__codelineno-2-5 name=__codelineno-2-5 href=#__codelineno-2-5></a>mkdir<span class=w> </span>-p<span class=w> </span><span class=o>{</span>n8n,mcp-servers,nginx,redis,logs<span class=o>}</span>
</span><span id=__span-2-6><a id=__codelineno-2-6 name=__codelineno-2-6 href=#__codelineno-2-6></a>mkdir<span class=w> </span>-p<span class=w> </span>mcp-servers/<span class=o>{</span>crypto,news,finance,bridge<span class=o>}</span>
</span></code></pre></div> <h3 id=3-docker-compose>3. Docker Compose配置<a class=headerlink href=#3-docker-compose title="Permanent link">&para;</a></h3> <p>创建 <code>docker-compose.yml</code>:</p> <div class="language-yaml highlight"><pre><span></span><code><span id=__span-3-1><a id=__codelineno-3-1 name=__codelineno-3-1 href=#__codelineno-3-1></a><span class=nt>version</span><span class=p>:</span><span class=w> </span><span class=s>&#39;3.8&#39;</span>
</span><span id=__span-3-2><a id=__codelineno-3-2 name=__codelineno-3-2 href=#__codelineno-3-2></a>
</span><span id=__span-3-3><a id=__codelineno-3-3 name=__codelineno-3-3 href=#__codelineno-3-3></a><span class=nt>services</span><span class=p>:</span>
</span><span id=__span-3-4><a id=__codelineno-3-4 name=__codelineno-3-4 href=#__codelineno-3-4></a><span class=w>  </span><span class=c1># N8N工作流引擎</span>
</span><span id=__span-3-5><a id=__codelineno-3-5 name=__codelineno-3-5 href=#__codelineno-3-5></a><span class=w>  </span><span class=nt>n8n</span><span class=p>:</span>
</span><span id=__span-3-6><a id=__codelineno-3-6 name=__codelineno-3-6 href=#__codelineno-3-6></a><span class=w>    </span><span class=nt>image</span><span class=p>:</span><span class=w> </span><span class="l l-Scalar l-Scalar-Plain">n8nio/n8n:latest</span>
</span><span id=__span-3-7><a id=__codelineno-3-7 name=__codelineno-3-7 href=#__codelineno-3-7></a><span class=w>    </span><span class=nt>container_name</span><span class=p>:</span><span class=w> </span><span class="l l-Scalar l-Scalar-Plain">taigong-n8n</span>
</span><span id=__span-3-8><a id=__codelineno-3-8 name=__codelineno-3-8 href=#__codelineno-3-8></a><span class=w>    </span><span class=nt>restart</span><span class=p>:</span><span class=w> </span><span class="l l-Scalar l-Scalar-Plain">unless-stopped</span>
</span><span id=__span-3-9><a id=__codelineno-3-9 name=__codelineno-3-9 href=#__codelineno-3-9></a><span class=w>    </span><span class=nt>ports</span><span class=p>:</span>
</span><span id=__span-3-10><a id=__codelineno-3-10 name=__codelineno-3-10 href=#__codelineno-3-10></a><span class=w>      </span><span class="p p-Indicator">-</span><span class=w> </span><span class=s>&quot;5678:5678&quot;</span>
</span><span id=__span-3-11><a id=__codelineno-3-11 name=__codelineno-3-11 href=#__codelineno-3-11></a><span class=w>    </span><span class=nt>environment</span><span class=p>:</span>
</span><span id=__span-3-12><a id=__codelineno-3-12 name=__codelineno-3-12 href=#__codelineno-3-12></a><span class=w>      </span><span class="p p-Indicator">-</span><span class=w> </span><span class="l l-Scalar l-Scalar-Plain">N8N_BASIC_AUTH_ACTIVE=true</span>
</span><span id=__span-3-13><a id=__codelineno-3-13 name=__codelineno-3-13 href=#__codelineno-3-13></a><span class=w>      </span><span class="p p-Indicator">-</span><span class=w> </span><span class="l l-Scalar l-Scalar-Plain">N8N_BASIC_AUTH_USER=admin</span>
</span><span id=__span-3-14><a id=__codelineno-3-14 name=__codelineno-3-14 href=#__codelineno-3-14></a><span class=w>      </span><span class="p p-Indicator">-</span><span class=w> </span><span class="l l-Scalar l-Scalar-Plain">N8N_BASIC_AUTH_PASSWORD=your_secure_password</span>
</span><span id=__span-3-15><a id=__codelineno-3-15 name=__codelineno-3-15 href=#__codelineno-3-15></a><span class=w>      </span><span class="p p-Indicator">-</span><span class=w> </span><span class="l l-Scalar l-Scalar-Plain">N8N_HOST=0.0.0.0</span>
</span><span id=__span-3-16><a id=__codelineno-3-16 name=__codelineno-3-16 href=#__codelineno-3-16></a><span class=w>      </span><span class="p p-Indicator">-</span><span class=w> </span><span class="l l-Scalar l-Scalar-Plain">N8N_PORT=5678</span>
</span><span id=__span-3-17><a id=__codelineno-3-17 name=__codelineno-3-17 href=#__codelineno-3-17></a><span class=w>      </span><span class="p p-Indicator">-</span><span class=w> </span><span class="l l-Scalar l-Scalar-Plain">N8N_PROTOCOL=http</span>
</span><span id=__span-3-18><a id=__codelineno-3-18 name=__codelineno-3-18 href=#__codelineno-3-18></a><span class=w>      </span><span class="p p-Indicator">-</span><span class=w> </span><span class="l l-Scalar l-Scalar-Plain">WEBHOOK_URL=https://your-korean-server.com</span>
</span><span id=__span-3-19><a id=__codelineno-3-19 name=__codelineno-3-19 href=#__codelineno-3-19></a><span class=w>    </span><span class=nt>volumes</span><span class=p>:</span>
</span><span id=__span-3-20><a id=__codelineno-3-20 name=__codelineno-3-20 href=#__codelineno-3-20></a><span class=w>      </span><span class="p p-Indicator">-</span><span class=w> </span><span class="l l-Scalar l-Scalar-Plain">./n8n:/home/<USER>/.n8n</span>
</span><span id=__span-3-21><a id=__codelineno-3-21 name=__codelineno-3-21 href=#__codelineno-3-21></a><span class=w>      </span><span class="p p-Indicator">-</span><span class=w> </span><span class="l l-Scalar l-Scalar-Plain">/var/run/docker.sock:/var/run/docker.sock</span>
</span><span id=__span-3-22><a id=__codelineno-3-22 name=__codelineno-3-22 href=#__codelineno-3-22></a><span class=w>    </span><span class=nt>depends_on</span><span class=p>:</span>
</span><span id=__span-3-23><a id=__codelineno-3-23 name=__codelineno-3-23 href=#__codelineno-3-23></a><span class=w>      </span><span class="p p-Indicator">-</span><span class=w> </span><span class="l l-Scalar l-Scalar-Plain">redis</span>
</span><span id=__span-3-24><a id=__codelineno-3-24 name=__codelineno-3-24 href=#__codelineno-3-24></a>
</span><span id=__span-3-25><a id=__codelineno-3-25 name=__codelineno-3-25 href=#__codelineno-3-25></a><span class=w>  </span><span class=c1># Redis缓存</span>
</span><span id=__span-3-26><a id=__codelineno-3-26 name=__codelineno-3-26 href=#__codelineno-3-26></a><span class=w>  </span><span class=nt>redis</span><span class=p>:</span>
</span><span id=__span-3-27><a id=__codelineno-3-27 name=__codelineno-3-27 href=#__codelineno-3-27></a><span class=w>    </span><span class=nt>image</span><span class=p>:</span><span class=w> </span><span class="l l-Scalar l-Scalar-Plain">redis:7-alpine</span>
</span><span id=__span-3-28><a id=__codelineno-3-28 name=__codelineno-3-28 href=#__codelineno-3-28></a><span class=w>    </span><span class=nt>container_name</span><span class=p>:</span><span class=w> </span><span class="l l-Scalar l-Scalar-Plain">taigong-redis</span>
</span><span id=__span-3-29><a id=__codelineno-3-29 name=__codelineno-3-29 href=#__codelineno-3-29></a><span class=w>    </span><span class=nt>restart</span><span class=p>:</span><span class=w> </span><span class="l l-Scalar l-Scalar-Plain">unless-stopped</span>
</span><span id=__span-3-30><a id=__codelineno-3-30 name=__codelineno-3-30 href=#__codelineno-3-30></a><span class=w>    </span><span class=nt>ports</span><span class=p>:</span>
</span><span id=__span-3-31><a id=__codelineno-3-31 name=__codelineno-3-31 href=#__codelineno-3-31></a><span class=w>      </span><span class="p p-Indicator">-</span><span class=w> </span><span class=s>&quot;6379:6379&quot;</span>
</span><span id=__span-3-32><a id=__codelineno-3-32 name=__codelineno-3-32 href=#__codelineno-3-32></a><span class=w>    </span><span class=nt>volumes</span><span class=p>:</span>
</span><span id=__span-3-33><a id=__codelineno-3-33 name=__codelineno-3-33 href=#__codelineno-3-33></a><span class=w>      </span><span class="p p-Indicator">-</span><span class=w> </span><span class="l l-Scalar l-Scalar-Plain">./redis:/data</span>
</span><span id=__span-3-34><a id=__codelineno-3-34 name=__codelineno-3-34 href=#__codelineno-3-34></a><span class=w>    </span><span class=nt>command</span><span class=p>:</span><span class=w> </span><span class="l l-Scalar l-Scalar-Plain">redis-server --appendonly yes</span>
</span><span id=__span-3-35><a id=__codelineno-3-35 name=__codelineno-3-35 href=#__codelineno-3-35></a>
</span><span id=__span-3-36><a id=__codelineno-3-36 name=__codelineno-3-36 href=#__codelineno-3-36></a><span class=w>  </span><span class=c1># 加密货币MCP服务器</span>
</span><span id=__span-3-37><a id=__codelineno-3-37 name=__codelineno-3-37 href=#__codelineno-3-37></a><span class=w>  </span><span class=nt>crypto-mcp</span><span class=p>:</span>
</span><span id=__span-3-38><a id=__codelineno-3-38 name=__codelineno-3-38 href=#__codelineno-3-38></a><span class=w>    </span><span class=nt>build</span><span class=p>:</span><span class=w> </span><span class="l l-Scalar l-Scalar-Plain">./mcp-servers/crypto</span>
</span><span id=__span-3-39><a id=__codelineno-3-39 name=__codelineno-3-39 href=#__codelineno-3-39></a><span class=w>    </span><span class=nt>container_name</span><span class=p>:</span><span class=w> </span><span class="l l-Scalar l-Scalar-Plain">taigong-crypto-mcp</span>
</span><span id=__span-3-40><a id=__codelineno-3-40 name=__codelineno-3-40 href=#__codelineno-3-40></a><span class=w>    </span><span class=nt>restart</span><span class=p>:</span><span class=w> </span><span class="l l-Scalar l-Scalar-Plain">unless-stopped</span>
</span><span id=__span-3-41><a id=__codelineno-3-41 name=__codelineno-3-41 href=#__codelineno-3-41></a><span class=w>    </span><span class=nt>ports</span><span class=p>:</span>
</span><span id=__span-3-42><a id=__codelineno-3-42 name=__codelineno-3-42 href=#__codelineno-3-42></a><span class=w>      </span><span class="p p-Indicator">-</span><span class=w> </span><span class=s>&quot;8001:8000&quot;</span>
</span><span id=__span-3-43><a id=__codelineno-3-43 name=__codelineno-3-43 href=#__codelineno-3-43></a><span class=w>    </span><span class=nt>environment</span><span class=p>:</span>
</span><span id=__span-3-44><a id=__codelineno-3-44 name=__codelineno-3-44 href=#__codelineno-3-44></a><span class=w>      </span><span class="p p-Indicator">-</span><span class=w> </span><span class="l l-Scalar l-Scalar-Plain">COINGECKO_API_KEY=${COINGECKO_API_KEY}</span>
</span><span id=__span-3-45><a id=__codelineno-3-45 name=__codelineno-3-45 href=#__codelineno-3-45></a><span class=w>      </span><span class="p p-Indicator">-</span><span class=w> </span><span class="l l-Scalar l-Scalar-Plain">REDIS_URL=redis://redis:6379</span>
</span><span id=__span-3-46><a id=__codelineno-3-46 name=__codelineno-3-46 href=#__codelineno-3-46></a><span class=w>    </span><span class=nt>depends_on</span><span class=p>:</span>
</span><span id=__span-3-47><a id=__codelineno-3-47 name=__codelineno-3-47 href=#__codelineno-3-47></a><span class=w>      </span><span class="p p-Indicator">-</span><span class=w> </span><span class="l l-Scalar l-Scalar-Plain">redis</span>
</span><span id=__span-3-48><a id=__codelineno-3-48 name=__codelineno-3-48 href=#__codelineno-3-48></a>
</span><span id=__span-3-49><a id=__codelineno-3-49 name=__codelineno-3-49 href=#__codelineno-3-49></a><span class=w>  </span><span class=c1># 新闻MCP服务器</span>
</span><span id=__span-3-50><a id=__codelineno-3-50 name=__codelineno-3-50 href=#__codelineno-3-50></a><span class=w>  </span><span class=nt>news-mcp</span><span class=p>:</span>
</span><span id=__span-3-51><a id=__codelineno-3-51 name=__codelineno-3-51 href=#__codelineno-3-51></a><span class=w>    </span><span class=nt>build</span><span class=p>:</span><span class=w> </span><span class="l l-Scalar l-Scalar-Plain">./mcp-servers/news</span>
</span><span id=__span-3-52><a id=__codelineno-3-52 name=__codelineno-3-52 href=#__codelineno-3-52></a><span class=w>    </span><span class=nt>container_name</span><span class=p>:</span><span class=w> </span><span class="l l-Scalar l-Scalar-Plain">taigong-news-mcp</span>
</span><span id=__span-3-53><a id=__codelineno-3-53 name=__codelineno-3-53 href=#__codelineno-3-53></a><span class=w>    </span><span class=nt>restart</span><span class=p>:</span><span class=w> </span><span class="l l-Scalar l-Scalar-Plain">unless-stopped</span>
</span><span id=__span-3-54><a id=__codelineno-3-54 name=__codelineno-3-54 href=#__codelineno-3-54></a><span class=w>    </span><span class=nt>ports</span><span class=p>:</span>
</span><span id=__span-3-55><a id=__codelineno-3-55 name=__codelineno-3-55 href=#__codelineno-3-55></a><span class=w>      </span><span class="p p-Indicator">-</span><span class=w> </span><span class=s>&quot;8002:8000&quot;</span>
</span><span id=__span-3-56><a id=__codelineno-3-56 name=__codelineno-3-56 href=#__codelineno-3-56></a><span class=w>    </span><span class=nt>environment</span><span class=p>:</span>
</span><span id=__span-3-57><a id=__codelineno-3-57 name=__codelineno-3-57 href=#__codelineno-3-57></a><span class=w>      </span><span class="p p-Indicator">-</span><span class=w> </span><span class="l l-Scalar l-Scalar-Plain">REDIS_URL=redis://redis:6379</span>
</span><span id=__span-3-58><a id=__codelineno-3-58 name=__codelineno-3-58 href=#__codelineno-3-58></a><span class=w>    </span><span class=nt>depends_on</span><span class=p>:</span>
</span><span id=__span-3-59><a id=__codelineno-3-59 name=__codelineno-3-59 href=#__codelineno-3-59></a><span class=w>      </span><span class="p p-Indicator">-</span><span class=w> </span><span class="l l-Scalar l-Scalar-Plain">redis</span>
</span><span id=__span-3-60><a id=__codelineno-3-60 name=__codelineno-3-60 href=#__codelineno-3-60></a>
</span><span id=__span-3-61><a id=__codelineno-3-61 name=__codelineno-3-61 href=#__codelineno-3-61></a><span class=w>  </span><span class=c1># 金融数据MCP服务器</span>
</span><span id=__span-3-62><a id=__codelineno-3-62 name=__codelineno-3-62 href=#__codelineno-3-62></a><span class=w>  </span><span class=nt>finance-mcp</span><span class=p>:</span>
</span><span id=__span-3-63><a id=__codelineno-3-63 name=__codelineno-3-63 href=#__codelineno-3-63></a><span class=w>    </span><span class=nt>build</span><span class=p>:</span><span class=w> </span><span class="l l-Scalar l-Scalar-Plain">./mcp-servers/finance</span>
</span><span id=__span-3-64><a id=__codelineno-3-64 name=__codelineno-3-64 href=#__codelineno-3-64></a><span class=w>    </span><span class=nt>container_name</span><span class=p>:</span><span class=w> </span><span class="l l-Scalar l-Scalar-Plain">taigong-finance-mcp</span>
</span><span id=__span-3-65><a id=__codelineno-3-65 name=__codelineno-3-65 href=#__codelineno-3-65></a><span class=w>    </span><span class=nt>restart</span><span class=p>:</span><span class=w> </span><span class="l l-Scalar l-Scalar-Plain">unless-stopped</span>
</span><span id=__span-3-66><a id=__codelineno-3-66 name=__codelineno-3-66 href=#__codelineno-3-66></a><span class=w>    </span><span class=nt>ports</span><span class=p>:</span>
</span><span id=__span-3-67><a id=__codelineno-3-67 name=__codelineno-3-67 href=#__codelineno-3-67></a><span class=w>      </span><span class="p p-Indicator">-</span><span class=w> </span><span class=s>&quot;8003:8000&quot;</span>
</span><span id=__span-3-68><a id=__codelineno-3-68 name=__codelineno-3-68 href=#__codelineno-3-68></a><span class=w>    </span><span class=nt>environment</span><span class=p>:</span>
</span><span id=__span-3-69><a id=__codelineno-3-69 name=__codelineno-3-69 href=#__codelineno-3-69></a><span class=w>      </span><span class="p p-Indicator">-</span><span class=w> </span><span class="l l-Scalar l-Scalar-Plain">ALPHA_VANTAGE_API_KEY=${ALPHA_VANTAGE_API_KEY}</span>
</span><span id=__span-3-70><a id=__codelineno-3-70 name=__codelineno-3-70 href=#__codelineno-3-70></a><span class=w>      </span><span class="p p-Indicator">-</span><span class=w> </span><span class="l l-Scalar l-Scalar-Plain">POLYGON_API_KEY=${POLYGON_API_KEY}</span>
</span><span id=__span-3-71><a id=__codelineno-3-71 name=__codelineno-3-71 href=#__codelineno-3-71></a><span class=w>      </span><span class="p p-Indicator">-</span><span class=w> </span><span class="l l-Scalar l-Scalar-Plain">REDIS_URL=redis://redis:6379</span>
</span><span id=__span-3-72><a id=__codelineno-3-72 name=__codelineno-3-72 href=#__codelineno-3-72></a><span class=w>    </span><span class=nt>depends_on</span><span class=p>:</span>
</span><span id=__span-3-73><a id=__codelineno-3-73 name=__codelineno-3-73 href=#__codelineno-3-73></a><span class=w>      </span><span class="p p-Indicator">-</span><span class=w> </span><span class="l l-Scalar l-Scalar-Plain">redis</span>
</span><span id=__span-3-74><a id=__codelineno-3-74 name=__codelineno-3-74 href=#__codelineno-3-74></a>
</span><span id=__span-3-75><a id=__codelineno-3-75 name=__codelineno-3-75 href=#__codelineno-3-75></a><span class=w>  </span><span class=c1># 太公心易桥接服务器</span>
</span><span id=__span-3-76><a id=__codelineno-3-76 name=__codelineno-3-76 href=#__codelineno-3-76></a><span class=w>  </span><span class=nt>taigong-bridge</span><span class=p>:</span>
</span><span id=__span-3-77><a id=__codelineno-3-77 name=__codelineno-3-77 href=#__codelineno-3-77></a><span class=w>    </span><span class=nt>build</span><span class=p>:</span><span class=w> </span><span class="l l-Scalar l-Scalar-Plain">./mcp-servers/bridge</span>
</span><span id=__span-3-78><a id=__codelineno-3-78 name=__codelineno-3-78 href=#__codelineno-3-78></a><span class=w>    </span><span class=nt>container_name</span><span class=p>:</span><span class=w> </span><span class="l l-Scalar l-Scalar-Plain">taigong-bridge-mcp</span>
</span><span id=__span-3-79><a id=__codelineno-3-79 name=__codelineno-3-79 href=#__codelineno-3-79></a><span class=w>    </span><span class=nt>restart</span><span class=p>:</span><span class=w> </span><span class="l l-Scalar l-Scalar-Plain">unless-stopped</span>
</span><span id=__span-3-80><a id=__codelineno-3-80 name=__codelineno-3-80 href=#__codelineno-3-80></a><span class=w>    </span><span class=nt>ports</span><span class=p>:</span>
</span><span id=__span-3-81><a id=__codelineno-3-81 name=__codelineno-3-81 href=#__codelineno-3-81></a><span class=w>      </span><span class="p p-Indicator">-</span><span class=w> </span><span class=s>&quot;8004:8000&quot;</span>
</span><span id=__span-3-82><a id=__codelineno-3-82 name=__codelineno-3-82 href=#__codelineno-3-82></a><span class=w>    </span><span class=nt>environment</span><span class=p>:</span>
</span><span id=__span-3-83><a id=__codelineno-3-83 name=__codelineno-3-83 href=#__codelineno-3-83></a><span class=w>      </span><span class="p p-Indicator">-</span><span class=w> </span><span class="l l-Scalar l-Scalar-Plain">REDIS_URL=redis://redis:6379</span>
</span><span id=__span-3-84><a id=__codelineno-3-84 name=__codelineno-3-84 href=#__codelineno-3-84></a><span class=w>      </span><span class="p p-Indicator">-</span><span class=w> </span><span class="l l-Scalar l-Scalar-Plain">N8N_WEBHOOK_URL=http://n8n:5678/webhook</span>
</span><span id=__span-3-85><a id=__codelineno-3-85 name=__codelineno-3-85 href=#__codelineno-3-85></a><span class=w>    </span><span class=nt>depends_on</span><span class=p>:</span>
</span><span id=__span-3-86><a id=__codelineno-3-86 name=__codelineno-3-86 href=#__codelineno-3-86></a><span class=w>      </span><span class="p p-Indicator">-</span><span class=w> </span><span class="l l-Scalar l-Scalar-Plain">redis</span>
</span><span id=__span-3-87><a id=__codelineno-3-87 name=__codelineno-3-87 href=#__codelineno-3-87></a><span class=w>      </span><span class="p p-Indicator">-</span><span class=w> </span><span class="l l-Scalar l-Scalar-Plain">n8n</span>
</span><span id=__span-3-88><a id=__codelineno-3-88 name=__codelineno-3-88 href=#__codelineno-3-88></a>
</span><span id=__span-3-89><a id=__codelineno-3-89 name=__codelineno-3-89 href=#__codelineno-3-89></a><span class=w>  </span><span class=c1># Nginx反向代理</span>
</span><span id=__span-3-90><a id=__codelineno-3-90 name=__codelineno-3-90 href=#__codelineno-3-90></a><span class=w>  </span><span class=nt>nginx</span><span class=p>:</span>
</span><span id=__span-3-91><a id=__codelineno-3-91 name=__codelineno-3-91 href=#__codelineno-3-91></a><span class=w>    </span><span class=nt>image</span><span class=p>:</span><span class=w> </span><span class="l l-Scalar l-Scalar-Plain">nginx:alpine</span>
</span><span id=__span-3-92><a id=__codelineno-3-92 name=__codelineno-3-92 href=#__codelineno-3-92></a><span class=w>    </span><span class=nt>container_name</span><span class=p>:</span><span class=w> </span><span class="l l-Scalar l-Scalar-Plain">taigong-nginx</span>
</span><span id=__span-3-93><a id=__codelineno-3-93 name=__codelineno-3-93 href=#__codelineno-3-93></a><span class=w>    </span><span class=nt>restart</span><span class=p>:</span><span class=w> </span><span class="l l-Scalar l-Scalar-Plain">unless-stopped</span>
</span><span id=__span-3-94><a id=__codelineno-3-94 name=__codelineno-3-94 href=#__codelineno-3-94></a><span class=w>    </span><span class=nt>ports</span><span class=p>:</span>
</span><span id=__span-3-95><a id=__codelineno-3-95 name=__codelineno-3-95 href=#__codelineno-3-95></a><span class=w>      </span><span class="p p-Indicator">-</span><span class=w> </span><span class=s>&quot;80:80&quot;</span>
</span><span id=__span-3-96><a id=__codelineno-3-96 name=__codelineno-3-96 href=#__codelineno-3-96></a><span class=w>      </span><span class="p p-Indicator">-</span><span class=w> </span><span class=s>&quot;443:443&quot;</span>
</span><span id=__span-3-97><a id=__codelineno-3-97 name=__codelineno-3-97 href=#__codelineno-3-97></a><span class=w>    </span><span class=nt>volumes</span><span class=p>:</span>
</span><span id=__span-3-98><a id=__codelineno-3-98 name=__codelineno-3-98 href=#__codelineno-3-98></a><span class=w>      </span><span class="p p-Indicator">-</span><span class=w> </span><span class="l l-Scalar l-Scalar-Plain">./nginx/nginx.conf:/etc/nginx/nginx.conf</span>
</span><span id=__span-3-99><a id=__codelineno-3-99 name=__codelineno-3-99 href=#__codelineno-3-99></a><span class=w>      </span><span class="p p-Indicator">-</span><span class=w> </span><span class="l l-Scalar l-Scalar-Plain">./nginx/ssl:/etc/nginx/ssl</span>
</span><span id=__span-3-100><a id=__codelineno-3-100 name=__codelineno-3-100 href=#__codelineno-3-100></a><span class=w>    </span><span class=nt>depends_on</span><span class=p>:</span>
</span><span id=__span-3-101><a id=__codelineno-3-101 name=__codelineno-3-101 href=#__codelineno-3-101></a><span class=w>      </span><span class="p p-Indicator">-</span><span class=w> </span><span class="l l-Scalar l-Scalar-Plain">n8n</span>
</span><span id=__span-3-102><a id=__codelineno-3-102 name=__codelineno-3-102 href=#__codelineno-3-102></a><span class=w>      </span><span class="p p-Indicator">-</span><span class=w> </span><span class="l l-Scalar l-Scalar-Plain">crypto-mcp</span>
</span><span id=__span-3-103><a id=__codelineno-3-103 name=__codelineno-3-103 href=#__codelineno-3-103></a><span class=w>      </span><span class="p p-Indicator">-</span><span class=w> </span><span class="l l-Scalar l-Scalar-Plain">news-mcp</span>
</span><span id=__span-3-104><a id=__codelineno-3-104 name=__codelineno-3-104 href=#__codelineno-3-104></a><span class=w>      </span><span class="p p-Indicator">-</span><span class=w> </span><span class="l l-Scalar l-Scalar-Plain">finance-mcp</span>
</span><span id=__span-3-105><a id=__codelineno-3-105 name=__codelineno-3-105 href=#__codelineno-3-105></a><span class=w>      </span><span class="p p-Indicator">-</span><span class=w> </span><span class="l l-Scalar l-Scalar-Plain">taigong-bridge</span>
</span><span id=__span-3-106><a id=__codelineno-3-106 name=__codelineno-3-106 href=#__codelineno-3-106></a>
</span><span id=__span-3-107><a id=__codelineno-3-107 name=__codelineno-3-107 href=#__codelineno-3-107></a><span class=nt>networks</span><span class=p>:</span>
</span><span id=__span-3-108><a id=__codelineno-3-108 name=__codelineno-3-108 href=#__codelineno-3-108></a><span class=w>  </span><span class=nt>default</span><span class=p>:</span>
</span><span id=__span-3-109><a id=__codelineno-3-109 name=__codelineno-3-109 href=#__codelineno-3-109></a><span class=w>    </span><span class=nt>name</span><span class=p>:</span><span class=w> </span><span class="l l-Scalar l-Scalar-Plain">taigong-network</span>
</span></code></pre></div> <h3 id=4>4. 环境变量配置<a class=headerlink href=#4 title="Permanent link">&para;</a></h3> <p>创建 <code>.env</code> 文件:</p> <div class="language-bash highlight"><pre><span></span><code><span id=__span-4-1><a id=__codelineno-4-1 name=__codelineno-4-1 href=#__codelineno-4-1></a><span class=c1># API密钥</span>
</span><span id=__span-4-2><a id=__codelineno-4-2 name=__codelineno-4-2 href=#__codelineno-4-2></a><span class=nv>COINGECKO_API_KEY</span><span class=o>=</span>your_coingecko_pro_key
</span><span id=__span-4-3><a id=__codelineno-4-3 name=__codelineno-4-3 href=#__codelineno-4-3></a><span class=nv>ALPHA_VANTAGE_API_KEY</span><span class=o>=</span>your_alpha_vantage_key
</span><span id=__span-4-4><a id=__codelineno-4-4 name=__codelineno-4-4 href=#__codelineno-4-4></a><span class=nv>POLYGON_API_KEY</span><span class=o>=</span>your_polygon_key
</span><span id=__span-4-5><a id=__codelineno-4-5 name=__codelineno-4-5 href=#__codelineno-4-5></a>
</span><span id=__span-4-6><a id=__codelineno-4-6 name=__codelineno-4-6 href=#__codelineno-4-6></a><span class=c1># 服务器配置</span>
</span><span id=__span-4-7><a id=__codelineno-4-7 name=__codelineno-4-7 href=#__codelineno-4-7></a><span class=nv>KOREAN_SERVER_DOMAIN</span><span class=o>=</span>your-korean-server.com
</span><span id=__span-4-8><a id=__codelineno-4-8 name=__codelineno-4-8 href=#__codelineno-4-8></a><span class=nv>N8N_BASIC_AUTH_PASSWORD</span><span class=o>=</span>your_secure_n8n_password
</span><span id=__span-4-9><a id=__codelineno-4-9 name=__codelineno-4-9 href=#__codelineno-4-9></a>
</span><span id=__span-4-10><a id=__codelineno-4-10 name=__codelineno-4-10 href=#__codelineno-4-10></a><span class=c1># 数据库配置</span>
</span><span id=__span-4-11><a id=__codelineno-4-11 name=__codelineno-4-11 href=#__codelineno-4-11></a><span class=nv>REDIS_PASSWORD</span><span class=o>=</span>your_redis_password
</span><span id=__span-4-12><a id=__codelineno-4-12 name=__codelineno-4-12 href=#__codelineno-4-12></a>
</span><span id=__span-4-13><a id=__codelineno-4-13 name=__codelineno-4-13 href=#__codelineno-4-13></a><span class=c1># 安全配置</span>
</span><span id=__span-4-14><a id=__codelineno-4-14 name=__codelineno-4-14 href=#__codelineno-4-14></a><span class=nv>JWT_SECRET</span><span class=o>=</span>your_jwt_secret_key
</span><span id=__span-4-15><a id=__codelineno-4-15 name=__codelineno-4-15 href=#__codelineno-4-15></a><span class=nv>API_RATE_LIMIT</span><span class=o>=</span><span class=m>1000</span>
</span></code></pre></div> <h3 id=5-mcp>5. MCP服务器实现<a class=headerlink href=#5-mcp title="Permanent link">&para;</a></h3> <h4 id=mcp-mcp-serverscryptodockerfile>加密货币MCP服务器 (<code>mcp-servers/crypto/Dockerfile</code>)<a class=headerlink href=#mcp-mcp-serverscryptodockerfile title="Permanent link">&para;</a></h4> <div class="language-dockerfile highlight"><pre><span></span><code><span id=__span-5-1><a id=__codelineno-5-1 name=__codelineno-5-1 href=#__codelineno-5-1></a><span class=k>FROM</span><span class=w> </span><span class=s>python:3.11-slim</span>
</span><span id=__span-5-2><a id=__codelineno-5-2 name=__codelineno-5-2 href=#__codelineno-5-2></a>
</span><span id=__span-5-3><a id=__codelineno-5-3 name=__codelineno-5-3 href=#__codelineno-5-3></a><span class=k>WORKDIR</span><span class=w> </span><span class=s>/app</span>
</span><span id=__span-5-4><a id=__codelineno-5-4 name=__codelineno-5-4 href=#__codelineno-5-4></a>
</span><span id=__span-5-5><a id=__codelineno-5-5 name=__codelineno-5-5 href=#__codelineno-5-5></a><span class=k>COPY</span><span class=w> </span>requirements.txt<span class=w> </span>.
</span><span id=__span-5-6><a id=__codelineno-5-6 name=__codelineno-5-6 href=#__codelineno-5-6></a><span class=k>RUN</span><span class=w> </span>pip<span class=w> </span>install<span class=w> </span>-r<span class=w> </span>requirements.txt
</span><span id=__span-5-7><a id=__codelineno-5-7 name=__codelineno-5-7 href=#__codelineno-5-7></a>
</span><span id=__span-5-8><a id=__codelineno-5-8 name=__codelineno-5-8 href=#__codelineno-5-8></a><span class=k>COPY</span><span class=w> </span>.<span class=w> </span>.
</span><span id=__span-5-9><a id=__codelineno-5-9 name=__codelineno-5-9 href=#__codelineno-5-9></a>
</span><span id=__span-5-10><a id=__codelineno-5-10 name=__codelineno-5-10 href=#__codelineno-5-10></a><span class=k>EXPOSE</span><span class=w> </span><span class=s>8000</span>
</span><span id=__span-5-11><a id=__codelineno-5-11 name=__codelineno-5-11 href=#__codelineno-5-11></a>
</span><span id=__span-5-12><a id=__codelineno-5-12 name=__codelineno-5-12 href=#__codelineno-5-12></a><span class=k>CMD</span><span class=w> </span><span class=p>[</span><span class=s2>&quot;python&quot;</span><span class=p>,</span><span class=w> </span><span class=s2>&quot;crypto_mcp_server.py&quot;</span><span class=p>]</span>
</span></code></pre></div> <h4 id=mcp-mcp-serverscryptocrypto_mcp_serverpy>加密货币MCP服务器 (<code>mcp-servers/crypto/crypto_mcp_server.py</code>)<a class=headerlink href=#mcp-mcp-serverscryptocrypto_mcp_serverpy title="Permanent link">&para;</a></h4> <div class="language-python highlight"><pre><span></span><code><span id=__span-6-1><a id=__codelineno-6-1 name=__codelineno-6-1 href=#__codelineno-6-1></a><span class=ch>#!/usr/bin/env python3</span>
</span><span id=__span-6-2><a id=__codelineno-6-2 name=__codelineno-6-2 href=#__codelineno-6-2></a><span class=sd>&quot;&quot;&quot;</span>
</span><span id=__span-6-3><a id=__codelineno-6-3 name=__codelineno-6-3 href=#__codelineno-6-3></a><span class=sd>韩国服务器 - 加密货币MCP服务器</span>
</span><span id=__span-6-4><a id=__codelineno-6-4 name=__codelineno-6-4 href=#__codelineno-6-4></a><span class=sd>提供实时加密货币数据和分析服务</span>
</span><span id=__span-6-5><a id=__codelineno-6-5 name=__codelineno-6-5 href=#__codelineno-6-5></a><span class=sd>&quot;&quot;&quot;</span>
</span><span id=__span-6-6><a id=__codelineno-6-6 name=__codelineno-6-6 href=#__codelineno-6-6></a>
</span><span id=__span-6-7><a id=__codelineno-6-7 name=__codelineno-6-7 href=#__codelineno-6-7></a><span class=kn>import</span><span class=w> </span><span class=nn>os</span>
</span><span id=__span-6-8><a id=__codelineno-6-8 name=__codelineno-6-8 href=#__codelineno-6-8></a><span class=kn>import</span><span class=w> </span><span class=nn>asyncio</span>
</span><span id=__span-6-9><a id=__codelineno-6-9 name=__codelineno-6-9 href=#__codelineno-6-9></a><span class=kn>from</span><span class=w> </span><span class=nn>fastapi</span><span class=w> </span><span class=kn>import</span> <span class=n>FastAPI</span><span class=p>,</span> <span class=n>HTTPException</span>
</span><span id=__span-6-10><a id=__codelineno-6-10 name=__codelineno-6-10 href=#__codelineno-6-10></a><span class=kn>from</span><span class=w> </span><span class=nn>fastapi.middleware.cors</span><span class=w> </span><span class=kn>import</span> <span class=n>CORSMiddleware</span>
</span><span id=__span-6-11><a id=__codelineno-6-11 name=__codelineno-6-11 href=#__codelineno-6-11></a><span class=kn>import</span><span class=w> </span><span class=nn>redis.asyncio</span><span class=w> </span><span class=k>as</span><span class=w> </span><span class=nn>redis</span>
</span><span id=__span-6-12><a id=__codelineno-6-12 name=__codelineno-6-12 href=#__codelineno-6-12></a><span class=kn>from</span><span class=w> </span><span class=nn>datetime</span><span class=w> </span><span class=kn>import</span> <span class=n>datetime</span>
</span><span id=__span-6-13><a id=__codelineno-6-13 name=__codelineno-6-13 href=#__codelineno-6-13></a><span class=kn>import</span><span class=w> </span><span class=nn>httpx</span>
</span><span id=__span-6-14><a id=__codelineno-6-14 name=__codelineno-6-14 href=#__codelineno-6-14></a>
</span><span id=__span-6-15><a id=__codelineno-6-15 name=__codelineno-6-15 href=#__codelineno-6-15></a><span class=n>app</span> <span class=o>=</span> <span class=n>FastAPI</span><span class=p>(</span><span class=n>title</span><span class=o>=</span><span class=s2>&quot;Taigong Crypto MCP Server&quot;</span><span class=p>,</span> <span class=n>version</span><span class=o>=</span><span class=s2>&quot;1.0.0&quot;</span><span class=p>)</span>
</span><span id=__span-6-16><a id=__codelineno-6-16 name=__codelineno-6-16 href=#__codelineno-6-16></a>
</span><span id=__span-6-17><a id=__codelineno-6-17 name=__codelineno-6-17 href=#__codelineno-6-17></a><span class=c1># CORS配置</span>
</span><span id=__span-6-18><a id=__codelineno-6-18 name=__codelineno-6-18 href=#__codelineno-6-18></a><span class=n>app</span><span class=o>.</span><span class=n>add_middleware</span><span class=p>(</span>
</span><span id=__span-6-19><a id=__codelineno-6-19 name=__codelineno-6-19 href=#__codelineno-6-19></a>    <span class=n>CORSMiddleware</span><span class=p>,</span>
</span><span id=__span-6-20><a id=__codelineno-6-20 name=__codelineno-6-20 href=#__codelineno-6-20></a>    <span class=n>allow_origins</span><span class=o>=</span><span class=p>[</span><span class=s2>&quot;*&quot;</span><span class=p>],</span>
</span><span id=__span-6-21><a id=__codelineno-6-21 name=__codelineno-6-21 href=#__codelineno-6-21></a>    <span class=n>allow_credentials</span><span class=o>=</span><span class=kc>True</span><span class=p>,</span>
</span><span id=__span-6-22><a id=__codelineno-6-22 name=__codelineno-6-22 href=#__codelineno-6-22></a>    <span class=n>allow_methods</span><span class=o>=</span><span class=p>[</span><span class=s2>&quot;*&quot;</span><span class=p>],</span>
</span><span id=__span-6-23><a id=__codelineno-6-23 name=__codelineno-6-23 href=#__codelineno-6-23></a>    <span class=n>allow_headers</span><span class=o>=</span><span class=p>[</span><span class=s2>&quot;*&quot;</span><span class=p>],</span>
</span><span id=__span-6-24><a id=__codelineno-6-24 name=__codelineno-6-24 href=#__codelineno-6-24></a><span class=p>)</span>
</span><span id=__span-6-25><a id=__codelineno-6-25 name=__codelineno-6-25 href=#__codelineno-6-25></a>
</span><span id=__span-6-26><a id=__codelineno-6-26 name=__codelineno-6-26 href=#__codelineno-6-26></a><span class=c1># Redis连接</span>
</span><span id=__span-6-27><a id=__codelineno-6-27 name=__codelineno-6-27 href=#__codelineno-6-27></a><span class=n>redis_client</span> <span class=o>=</span> <span class=kc>None</span>
</span><span id=__span-6-28><a id=__codelineno-6-28 name=__codelineno-6-28 href=#__codelineno-6-28></a>
</span><span id=__span-6-29><a id=__codelineno-6-29 name=__codelineno-6-29 href=#__codelineno-6-29></a><span class=nd>@app</span><span class=o>.</span><span class=n>on_event</span><span class=p>(</span><span class=s2>&quot;startup&quot;</span><span class=p>)</span>
</span><span id=__span-6-30><a id=__codelineno-6-30 name=__codelineno-6-30 href=#__codelineno-6-30></a><span class=k>async</span> <span class=k>def</span><span class=w> </span><span class=nf>startup_event</span><span class=p>():</span>
</span><span id=__span-6-31><a id=__codelineno-6-31 name=__codelineno-6-31 href=#__codelineno-6-31></a>    <span class=k>global</span> <span class=n>redis_client</span>
</span><span id=__span-6-32><a id=__codelineno-6-32 name=__codelineno-6-32 href=#__codelineno-6-32></a>    <span class=n>redis_url</span> <span class=o>=</span> <span class=n>os</span><span class=o>.</span><span class=n>getenv</span><span class=p>(</span><span class=s2>&quot;REDIS_URL&quot;</span><span class=p>,</span> <span class=s2>&quot;redis://localhost:6379&quot;</span><span class=p>)</span>
</span><span id=__span-6-33><a id=__codelineno-6-33 name=__codelineno-6-33 href=#__codelineno-6-33></a>    <span class=n>redis_client</span> <span class=o>=</span> <span class=n>redis</span><span class=o>.</span><span class=n>from_url</span><span class=p>(</span><span class=n>redis_url</span><span class=p>)</span>
</span><span id=__span-6-34><a id=__codelineno-6-34 name=__codelineno-6-34 href=#__codelineno-6-34></a>
</span><span id=__span-6-35><a id=__codelineno-6-35 name=__codelineno-6-35 href=#__codelineno-6-35></a><span class=nd>@app</span><span class=o>.</span><span class=n>on_event</span><span class=p>(</span><span class=s2>&quot;shutdown&quot;</span><span class=p>)</span>
</span><span id=__span-6-36><a id=__codelineno-6-36 name=__codelineno-6-36 href=#__codelineno-6-36></a><span class=k>async</span> <span class=k>def</span><span class=w> </span><span class=nf>shutdown_event</span><span class=p>():</span>
</span><span id=__span-6-37><a id=__codelineno-6-37 name=__codelineno-6-37 href=#__codelineno-6-37></a>    <span class=k>if</span> <span class=n>redis_client</span><span class=p>:</span>
</span><span id=__span-6-38><a id=__codelineno-6-38 name=__codelineno-6-38 href=#__codelineno-6-38></a>        <span class=k>await</span> <span class=n>redis_client</span><span class=o>.</span><span class=n>close</span><span class=p>()</span>
</span><span id=__span-6-39><a id=__codelineno-6-39 name=__codelineno-6-39 href=#__codelineno-6-39></a>
</span><span id=__span-6-40><a id=__codelineno-6-40 name=__codelineno-6-40 href=#__codelineno-6-40></a><span class=nd>@app</span><span class=o>.</span><span class=n>get</span><span class=p>(</span><span class=s2>&quot;/health&quot;</span><span class=p>)</span>
</span><span id=__span-6-41><a id=__codelineno-6-41 name=__codelineno-6-41 href=#__codelineno-6-41></a><span class=k>async</span> <span class=k>def</span><span class=w> </span><span class=nf>health_check</span><span class=p>():</span>
</span><span id=__span-6-42><a id=__codelineno-6-42 name=__codelineno-6-42 href=#__codelineno-6-42></a>    <span class=k>return</span> <span class=p>{</span><span class=s2>&quot;status&quot;</span><span class=p>:</span> <span class=s2>&quot;healthy&quot;</span><span class=p>,</span> <span class=s2>&quot;service&quot;</span><span class=p>:</span> <span class=s2>&quot;crypto-mcp&quot;</span><span class=p>,</span> <span class=s2>&quot;timestamp&quot;</span><span class=p>:</span> <span class=n>datetime</span><span class=o>.</span><span class=n>now</span><span class=p>()}</span>
</span><span id=__span-6-43><a id=__codelineno-6-43 name=__codelineno-6-43 href=#__codelineno-6-43></a>
</span><span id=__span-6-44><a id=__codelineno-6-44 name=__codelineno-6-44 href=#__codelineno-6-44></a><span class=nd>@app</span><span class=o>.</span><span class=n>get</span><span class=p>(</span><span class=s2>&quot;/api/mcp/crypto-data&quot;</span><span class=p>)</span>
</span><span id=__span-6-45><a id=__codelineno-6-45 name=__codelineno-6-45 href=#__codelineno-6-45></a><span class=k>async</span> <span class=k>def</span><span class=w> </span><span class=nf>get_crypto_data</span><span class=p>(</span><span class=n>symbols</span><span class=p>:</span> <span class=nb>str</span> <span class=o>=</span> <span class=kc>None</span><span class=p>):</span>
</span><span id=__span-6-46><a id=__codelineno-6-46 name=__codelineno-6-46 href=#__codelineno-6-46></a><span class=w>    </span><span class=sd>&quot;&quot;&quot;获取加密货币数据&quot;&quot;&quot;</span>
</span><span id=__span-6-47><a id=__codelineno-6-47 name=__codelineno-6-47 href=#__codelineno-6-47></a>    <span class=k>try</span><span class=p>:</span>
</span><span id=__span-6-48><a id=__codelineno-6-48 name=__codelineno-6-48 href=#__codelineno-6-48></a>        <span class=c1># 实现加密货币数据获取逻辑</span>
</span><span id=__span-6-49><a id=__codelineno-6-49 name=__codelineno-6-49 href=#__codelineno-6-49></a>        <span class=c1># 这里可以集成您之前实现的免费API聚合逻辑</span>
</span><span id=__span-6-50><a id=__codelineno-6-50 name=__codelineno-6-50 href=#__codelineno-6-50></a>
</span><span id=__span-6-51><a id=__codelineno-6-51 name=__codelineno-6-51 href=#__codelineno-6-51></a>        <span class=c1># 缓存检查</span>
</span><span id=__span-6-52><a id=__codelineno-6-52 name=__codelineno-6-52 href=#__codelineno-6-52></a>        <span class=n>cache_key</span> <span class=o>=</span> <span class=sa>f</span><span class=s2>&quot;crypto_data:</span><span class=si>{</span><span class=n>symbols</span><span class=w> </span><span class=ow>or</span><span class=w> </span><span class=s1>&#39;all&#39;</span><span class=si>}</span><span class=s2>&quot;</span>
</span><span id=__span-6-53><a id=__codelineno-6-53 name=__codelineno-6-53 href=#__codelineno-6-53></a>        <span class=n>cached_data</span> <span class=o>=</span> <span class=k>await</span> <span class=n>redis_client</span><span class=o>.</span><span class=n>get</span><span class=p>(</span><span class=n>cache_key</span><span class=p>)</span>
</span><span id=__span-6-54><a id=__codelineno-6-54 name=__codelineno-6-54 href=#__codelineno-6-54></a>
</span><span id=__span-6-55><a id=__codelineno-6-55 name=__codelineno-6-55 href=#__codelineno-6-55></a>        <span class=k>if</span> <span class=n>cached_data</span><span class=p>:</span>
</span><span id=__span-6-56><a id=__codelineno-6-56 name=__codelineno-6-56 href=#__codelineno-6-56></a>            <span class=k>return</span> <span class=n>json</span><span class=o>.</span><span class=n>loads</span><span class=p>(</span><span class=n>cached_data</span><span class=p>)</span>
</span><span id=__span-6-57><a id=__codelineno-6-57 name=__codelineno-6-57 href=#__codelineno-6-57></a>
</span><span id=__span-6-58><a id=__codelineno-6-58 name=__codelineno-6-58 href=#__codelineno-6-58></a>        <span class=c1># 获取新数据</span>
</span><span id=__span-6-59><a id=__codelineno-6-59 name=__codelineno-6-59 href=#__codelineno-6-59></a>        <span class=n>data</span> <span class=o>=</span> <span class=k>await</span> <span class=n>fetch_crypto_data</span><span class=p>(</span><span class=n>symbols</span><span class=p>)</span>
</span><span id=__span-6-60><a id=__codelineno-6-60 name=__codelineno-6-60 href=#__codelineno-6-60></a>
</span><span id=__span-6-61><a id=__codelineno-6-61 name=__codelineno-6-61 href=#__codelineno-6-61></a>        <span class=c1># 缓存5分钟</span>
</span><span id=__span-6-62><a id=__codelineno-6-62 name=__codelineno-6-62 href=#__codelineno-6-62></a>        <span class=k>await</span> <span class=n>redis_client</span><span class=o>.</span><span class=n>setex</span><span class=p>(</span><span class=n>cache_key</span><span class=p>,</span> <span class=mi>300</span><span class=p>,</span> <span class=n>json</span><span class=o>.</span><span class=n>dumps</span><span class=p>(</span><span class=n>data</span><span class=p>))</span>
</span><span id=__span-6-63><a id=__codelineno-6-63 name=__codelineno-6-63 href=#__codelineno-6-63></a>
</span><span id=__span-6-64><a id=__codelineno-6-64 name=__codelineno-6-64 href=#__codelineno-6-64></a>        <span class=k>return</span> <span class=n>data</span>
</span><span id=__span-6-65><a id=__codelineno-6-65 name=__codelineno-6-65 href=#__codelineno-6-65></a>
</span><span id=__span-6-66><a id=__codelineno-6-66 name=__codelineno-6-66 href=#__codelineno-6-66></a>    <span class=k>except</span> <span class=ne>Exception</span> <span class=k>as</span> <span class=n>e</span><span class=p>:</span>
</span><span id=__span-6-67><a id=__codelineno-6-67 name=__codelineno-6-67 href=#__codelineno-6-67></a>        <span class=k>raise</span> <span class=n>HTTPException</span><span class=p>(</span><span class=n>status_code</span><span class=o>=</span><span class=mi>500</span><span class=p>,</span> <span class=n>detail</span><span class=o>=</span><span class=nb>str</span><span class=p>(</span><span class=n>e</span><span class=p>))</span>
</span><span id=__span-6-68><a id=__codelineno-6-68 name=__codelineno-6-68 href=#__codelineno-6-68></a>
</span><span id=__span-6-69><a id=__codelineno-6-69 name=__codelineno-6-69 href=#__codelineno-6-69></a><span class=k>async</span> <span class=k>def</span><span class=w> </span><span class=nf>fetch_crypto_data</span><span class=p>(</span><span class=n>symbols</span><span class=p>):</span>
</span><span id=__span-6-70><a id=__codelineno-6-70 name=__codelineno-6-70 href=#__codelineno-6-70></a><span class=w>    </span><span class=sd>&quot;&quot;&quot;获取加密货币数据的具体实现&quot;&quot;&quot;</span>
</span><span id=__span-6-71><a id=__codelineno-6-71 name=__codelineno-6-71 href=#__codelineno-6-71></a>    <span class=c1># 这里集成您的免费API聚合逻辑</span>
</span><span id=__span-6-72><a id=__codelineno-6-72 name=__codelineno-6-72 href=#__codelineno-6-72></a>    <span class=k>return</span> <span class=p>{</span><span class=s2>&quot;message&quot;</span><span class=p>:</span> <span class=s2>&quot;crypto data implementation&quot;</span><span class=p>}</span>
</span><span id=__span-6-73><a id=__codelineno-6-73 name=__codelineno-6-73 href=#__codelineno-6-73></a>
</span><span id=__span-6-74><a id=__codelineno-6-74 name=__codelineno-6-74 href=#__codelineno-6-74></a><span class=k>if</span> <span class=vm>__name__</span> <span class=o>==</span> <span class=s2>&quot;__main__&quot;</span><span class=p>:</span>
</span><span id=__span-6-75><a id=__codelineno-6-75 name=__codelineno-6-75 href=#__codelineno-6-75></a>    <span class=kn>import</span><span class=w> </span><span class=nn>uvicorn</span>
</span><span id=__span-6-76><a id=__codelineno-6-76 name=__codelineno-6-76 href=#__codelineno-6-76></a>    <span class=n>uvicorn</span><span class=o>.</span><span class=n>run</span><span class=p>(</span><span class=n>app</span><span class=p>,</span> <span class=n>host</span><span class=o>=</span><span class=s2>&quot;0.0.0.0&quot;</span><span class=p>,</span> <span class=n>port</span><span class=o>=</span><span class=mi>8000</span><span class=p>)</span>
</span></code></pre></div> <h3 id=6-n8n>6. N8N工作流配置<a class=headerlink href=#6-n8n title="Permanent link">&para;</a></h3> <h4 id=_3>自动化工作流示例<a class=headerlink href=#_3 title="Permanent link">&para;</a></h4> <ol> <li><strong>定时数据收集工作流</strong>:</li> <li>每30分钟触发</li> <li>调用各MCP服务器收集数据</li> <li> <p>数据异常时发送告警</p> </li> <li> <p><strong>事件驱动分析工作流</strong>:</p> </li> <li>Webhook接收外部事件</li> <li>触发太公心易分析</li> <li> <p>结果推送到指定渠道</p> </li> <li> <p><strong>智能监控工作流</strong>:</p> </li> <li>监控MCP服务器健康状态</li> <li>自动重启故障服务</li> <li>性能指标收集</li> </ol> <h3 id=7-nginx>7. Nginx配置<a class=headerlink href=#7-nginx title="Permanent link">&para;</a></h3> <p>创建 <code>nginx/nginx.conf</code>:</p> <div class="language-nginx highlight"><pre><span></span><code><span id=__span-7-1><a id=__codelineno-7-1 name=__codelineno-7-1 href=#__codelineno-7-1></a><span class=k>events</span><span class=w> </span><span class=p>{</span>
</span><span id=__span-7-2><a id=__codelineno-7-2 name=__codelineno-7-2 href=#__codelineno-7-2></a><span class=w>    </span><span class=kn>worker_connections</span><span class=w> </span><span class=mi>1024</span><span class=p>;</span>
</span><span id=__span-7-3><a id=__codelineno-7-3 name=__codelineno-7-3 href=#__codelineno-7-3></a><span class=p>}</span>
</span><span id=__span-7-4><a id=__codelineno-7-4 name=__codelineno-7-4 href=#__codelineno-7-4></a>
</span><span id=__span-7-5><a id=__codelineno-7-5 name=__codelineno-7-5 href=#__codelineno-7-5></a><span class=k>http</span><span class=w> </span><span class=p>{</span>
</span><span id=__span-7-6><a id=__codelineno-7-6 name=__codelineno-7-6 href=#__codelineno-7-6></a><span class=w>    </span><span class=kn>upstream</span><span class=w> </span><span class=s>n8n_backend</span><span class=w> </span><span class=p>{</span>
</span><span id=__span-7-7><a id=__codelineno-7-7 name=__codelineno-7-7 href=#__codelineno-7-7></a><span class=w>        </span><span class=kn>server</span><span class=w> </span><span class=n>n8n</span><span class=p>:</span><span class=mi>5678</span><span class=p>;</span>
</span><span id=__span-7-8><a id=__codelineno-7-8 name=__codelineno-7-8 href=#__codelineno-7-8></a><span class=w>    </span><span class=p>}</span>
</span><span id=__span-7-9><a id=__codelineno-7-9 name=__codelineno-7-9 href=#__codelineno-7-9></a>
</span><span id=__span-7-10><a id=__codelineno-7-10 name=__codelineno-7-10 href=#__codelineno-7-10></a><span class=w>    </span><span class=kn>upstream</span><span class=w> </span><span class=s>crypto_mcp</span><span class=w> </span><span class=p>{</span>
</span><span id=__span-7-11><a id=__codelineno-7-11 name=__codelineno-7-11 href=#__codelineno-7-11></a><span class=w>        </span><span class=kn>server</span><span class=w> </span><span class=n>crypto-mcp</span><span class=p>:</span><span class=mi>8000</span><span class=p>;</span>
</span><span id=__span-7-12><a id=__codelineno-7-12 name=__codelineno-7-12 href=#__codelineno-7-12></a><span class=w>    </span><span class=p>}</span>
</span><span id=__span-7-13><a id=__codelineno-7-13 name=__codelineno-7-13 href=#__codelineno-7-13></a>
</span><span id=__span-7-14><a id=__codelineno-7-14 name=__codelineno-7-14 href=#__codelineno-7-14></a><span class=w>    </span><span class=kn>upstream</span><span class=w> </span><span class=s>news_mcp</span><span class=w> </span><span class=p>{</span>
</span><span id=__span-7-15><a id=__codelineno-7-15 name=__codelineno-7-15 href=#__codelineno-7-15></a><span class=w>        </span><span class=kn>server</span><span class=w> </span><span class=n>news-mcp</span><span class=p>:</span><span class=mi>8000</span><span class=p>;</span>
</span><span id=__span-7-16><a id=__codelineno-7-16 name=__codelineno-7-16 href=#__codelineno-7-16></a><span class=w>    </span><span class=p>}</span>
</span><span id=__span-7-17><a id=__codelineno-7-17 name=__codelineno-7-17 href=#__codelineno-7-17></a>
</span><span id=__span-7-18><a id=__codelineno-7-18 name=__codelineno-7-18 href=#__codelineno-7-18></a><span class=w>    </span><span class=kn>upstream</span><span class=w> </span><span class=s>finance_mcp</span><span class=w> </span><span class=p>{</span>
</span><span id=__span-7-19><a id=__codelineno-7-19 name=__codelineno-7-19 href=#__codelineno-7-19></a><span class=w>        </span><span class=kn>server</span><span class=w> </span><span class=n>finance-mcp</span><span class=p>:</span><span class=mi>8000</span><span class=p>;</span>
</span><span id=__span-7-20><a id=__codelineno-7-20 name=__codelineno-7-20 href=#__codelineno-7-20></a><span class=w>    </span><span class=p>}</span>
</span><span id=__span-7-21><a id=__codelineno-7-21 name=__codelineno-7-21 href=#__codelineno-7-21></a>
</span><span id=__span-7-22><a id=__codelineno-7-22 name=__codelineno-7-22 href=#__codelineno-7-22></a><span class=w>    </span><span class=kn>upstream</span><span class=w> </span><span class=s>bridge_mcp</span><span class=w> </span><span class=p>{</span>
</span><span id=__span-7-23><a id=__codelineno-7-23 name=__codelineno-7-23 href=#__codelineno-7-23></a><span class=w>        </span><span class=kn>server</span><span class=w> </span><span class=n>taigong-bridge</span><span class=p>:</span><span class=mi>8000</span><span class=p>;</span>
</span><span id=__span-7-24><a id=__codelineno-7-24 name=__codelineno-7-24 href=#__codelineno-7-24></a><span class=w>    </span><span class=p>}</span>
</span><span id=__span-7-25><a id=__codelineno-7-25 name=__codelineno-7-25 href=#__codelineno-7-25></a>
</span><span id=__span-7-26><a id=__codelineno-7-26 name=__codelineno-7-26 href=#__codelineno-7-26></a><span class=w>    </span><span class=kn>server</span><span class=w> </span><span class=p>{</span>
</span><span id=__span-7-27><a id=__codelineno-7-27 name=__codelineno-7-27 href=#__codelineno-7-27></a><span class=w>        </span><span class=kn>listen</span><span class=w> </span><span class=mi>80</span><span class=p>;</span>
</span><span id=__span-7-28><a id=__codelineno-7-28 name=__codelineno-7-28 href=#__codelineno-7-28></a><span class=w>        </span><span class=kn>server_name</span><span class=w> </span><span class=s>your-korean-server.com</span><span class=p>;</span>
</span><span id=__span-7-29><a id=__codelineno-7-29 name=__codelineno-7-29 href=#__codelineno-7-29></a>
</span><span id=__span-7-30><a id=__codelineno-7-30 name=__codelineno-7-30 href=#__codelineno-7-30></a><span class=w>        </span><span class=c1># N8N工作流引擎</span>
</span><span id=__span-7-31><a id=__codelineno-7-31 name=__codelineno-7-31 href=#__codelineno-7-31></a><span class=w>        </span><span class=kn>location</span><span class=w> </span><span class=s>/n8n/</span><span class=w> </span><span class=p>{</span>
</span><span id=__span-7-32><a id=__codelineno-7-32 name=__codelineno-7-32 href=#__codelineno-7-32></a><span class=w>            </span><span class=kn>proxy_pass</span><span class=w> </span><span class=s>http://n8n_backend/</span><span class=p>;</span>
</span><span id=__span-7-33><a id=__codelineno-7-33 name=__codelineno-7-33 href=#__codelineno-7-33></a><span class=w>            </span><span class=kn>proxy_set_header</span><span class=w> </span><span class=s>Host</span><span class=w> </span><span class=nv>$host</span><span class=p>;</span>
</span><span id=__span-7-34><a id=__codelineno-7-34 name=__codelineno-7-34 href=#__codelineno-7-34></a><span class=w>            </span><span class=kn>proxy_set_header</span><span class=w> </span><span class=s>X-Real-IP</span><span class=w> </span><span class=nv>$remote_addr</span><span class=p>;</span>
</span><span id=__span-7-35><a id=__codelineno-7-35 name=__codelineno-7-35 href=#__codelineno-7-35></a><span class=w>            </span><span class=kn>proxy_set_header</span><span class=w> </span><span class=s>X-Forwarded-For</span><span class=w> </span><span class=nv>$proxy_add_x_forwarded_for</span><span class=p>;</span>
</span><span id=__span-7-36><a id=__codelineno-7-36 name=__codelineno-7-36 href=#__codelineno-7-36></a><span class=w>            </span><span class=kn>proxy_set_header</span><span class=w> </span><span class=s>X-Forwarded-Proto</span><span class=w> </span><span class=nv>$scheme</span><span class=p>;</span>
</span><span id=__span-7-37><a id=__codelineno-7-37 name=__codelineno-7-37 href=#__codelineno-7-37></a><span class=w>        </span><span class=p>}</span>
</span><span id=__span-7-38><a id=__codelineno-7-38 name=__codelineno-7-38 href=#__codelineno-7-38></a>
</span><span id=__span-7-39><a id=__codelineno-7-39 name=__codelineno-7-39 href=#__codelineno-7-39></a><span class=w>        </span><span class=c1># MCP API端点</span>
</span><span id=__span-7-40><a id=__codelineno-7-40 name=__codelineno-7-40 href=#__codelineno-7-40></a><span class=w>        </span><span class=kn>location</span><span class=w> </span><span class=s>/api/mcp/crypto/</span><span class=w> </span><span class=p>{</span>
</span><span id=__span-7-41><a id=__codelineno-7-41 name=__codelineno-7-41 href=#__codelineno-7-41></a><span class=w>            </span><span class=kn>proxy_pass</span><span class=w> </span><span class=s>http://crypto_mcp/api/mcp/</span><span class=p>;</span>
</span><span id=__span-7-42><a id=__codelineno-7-42 name=__codelineno-7-42 href=#__codelineno-7-42></a><span class=w>        </span><span class=p>}</span>
</span><span id=__span-7-43><a id=__codelineno-7-43 name=__codelineno-7-43 href=#__codelineno-7-43></a>
</span><span id=__span-7-44><a id=__codelineno-7-44 name=__codelineno-7-44 href=#__codelineno-7-44></a><span class=w>        </span><span class=kn>location</span><span class=w> </span><span class=s>/api/mcp/news/</span><span class=w> </span><span class=p>{</span>
</span><span id=__span-7-45><a id=__codelineno-7-45 name=__codelineno-7-45 href=#__codelineno-7-45></a><span class=w>            </span><span class=kn>proxy_pass</span><span class=w> </span><span class=s>http://news_mcp/api/mcp/</span><span class=p>;</span>
</span><span id=__span-7-46><a id=__codelineno-7-46 name=__codelineno-7-46 href=#__codelineno-7-46></a><span class=w>        </span><span class=p>}</span>
</span><span id=__span-7-47><a id=__codelineno-7-47 name=__codelineno-7-47 href=#__codelineno-7-47></a>
</span><span id=__span-7-48><a id=__codelineno-7-48 name=__codelineno-7-48 href=#__codelineno-7-48></a><span class=w>        </span><span class=kn>location</span><span class=w> </span><span class=s>/api/mcp/finance/</span><span class=w> </span><span class=p>{</span>
</span><span id=__span-7-49><a id=__codelineno-7-49 name=__codelineno-7-49 href=#__codelineno-7-49></a><span class=w>            </span><span class=kn>proxy_pass</span><span class=w> </span><span class=s>http://finance_mcp/api/mcp/</span><span class=p>;</span>
</span><span id=__span-7-50><a id=__codelineno-7-50 name=__codelineno-7-50 href=#__codelineno-7-50></a><span class=w>        </span><span class=p>}</span>
</span><span id=__span-7-51><a id=__codelineno-7-51 name=__codelineno-7-51 href=#__codelineno-7-51></a>
</span><span id=__span-7-52><a id=__codelineno-7-52 name=__codelineno-7-52 href=#__codelineno-7-52></a><span class=w>        </span><span class=kn>location</span><span class=w> </span><span class=s>/api/mcp/bridge/</span><span class=w> </span><span class=p>{</span>
</span><span id=__span-7-53><a id=__codelineno-7-53 name=__codelineno-7-53 href=#__codelineno-7-53></a><span class=w>            </span><span class=kn>proxy_pass</span><span class=w> </span><span class=s>http://bridge_mcp/api/mcp/</span><span class=p>;</span>
</span><span id=__span-7-54><a id=__codelineno-7-54 name=__codelineno-7-54 href=#__codelineno-7-54></a><span class=w>        </span><span class=p>}</span>
</span><span id=__span-7-55><a id=__codelineno-7-55 name=__codelineno-7-55 href=#__codelineno-7-55></a>
</span><span id=__span-7-56><a id=__codelineno-7-56 name=__codelineno-7-56 href=#__codelineno-7-56></a><span class=w>        </span><span class=c1># 健康检查</span>
</span><span id=__span-7-57><a id=__codelineno-7-57 name=__codelineno-7-57 href=#__codelineno-7-57></a><span class=w>        </span><span class=kn>location</span><span class=w> </span><span class=s>/health</span><span class=w> </span><span class=p>{</span>
</span><span id=__span-7-58><a id=__codelineno-7-58 name=__codelineno-7-58 href=#__codelineno-7-58></a><span class=w>            </span><span class=kn>return</span><span class=w> </span><span class=mi>200</span><span class=w> </span><span class=s>&quot;OK&quot;</span><span class=p>;</span>
</span><span id=__span-7-59><a id=__codelineno-7-59 name=__codelineno-7-59 href=#__codelineno-7-59></a><span class=w>        </span><span class=p>}</span>
</span><span id=__span-7-60><a id=__codelineno-7-60 name=__codelineno-7-60 href=#__codelineno-7-60></a><span class=w>    </span><span class=p>}</span>
</span><span id=__span-7-61><a id=__codelineno-7-61 name=__codelineno-7-61 href=#__codelineno-7-61></a><span class=p>}</span>
</span></code></pre></div> <h3 id=8>8. 部署命令<a class=headerlink href=#8 title="Permanent link">&para;</a></h3> <div class="language-bash highlight"><pre><span></span><code><span id=__span-8-1><a id=__codelineno-8-1 name=__codelineno-8-1 href=#__codelineno-8-1></a><span class=c1># 克隆配置到韩国服务器</span>
</span><span id=__span-8-2><a id=__codelineno-8-2 name=__codelineno-8-2 href=#__codelineno-8-2></a>scp<span class=w> </span>-r<span class=w> </span>jixia_academy/korean_server_deployment.md<span class=w> </span>user@korean-server:/opt/
</span><span id=__span-8-3><a id=__codelineno-8-3 name=__codelineno-8-3 href=#__codelineno-8-3></a>
</span><span id=__span-8-4><a id=__codelineno-8-4 name=__codelineno-8-4 href=#__codelineno-8-4></a><span class=c1># 在韩国服务器上执行</span>
</span><span id=__span-8-5><a id=__codelineno-8-5 name=__codelineno-8-5 href=#__codelineno-8-5></a><span class=nb>cd</span><span class=w> </span>/opt/taigong-mcp-ecosystem
</span><span id=__span-8-6><a id=__codelineno-8-6 name=__codelineno-8-6 href=#__codelineno-8-6></a>
</span><span id=__span-8-7><a id=__codelineno-8-7 name=__codelineno-8-7 href=#__codelineno-8-7></a><span class=c1># 构建和启动服务</span>
</span><span id=__span-8-8><a id=__codelineno-8-8 name=__codelineno-8-8 href=#__codelineno-8-8></a>docker-compose<span class=w> </span>up<span class=w> </span>-d
</span><span id=__span-8-9><a id=__codelineno-8-9 name=__codelineno-8-9 href=#__codelineno-8-9></a>
</span><span id=__span-8-10><a id=__codelineno-8-10 name=__codelineno-8-10 href=#__codelineno-8-10></a><span class=c1># 查看服务状态</span>
</span><span id=__span-8-11><a id=__codelineno-8-11 name=__codelineno-8-11 href=#__codelineno-8-11></a>docker-compose<span class=w> </span>ps
</span><span id=__span-8-12><a id=__codelineno-8-12 name=__codelineno-8-12 href=#__codelineno-8-12></a>
</span><span id=__span-8-13><a id=__codelineno-8-13 name=__codelineno-8-13 href=#__codelineno-8-13></a><span class=c1># 查看日志</span>
</span><span id=__span-8-14><a id=__codelineno-8-14 name=__codelineno-8-14 href=#__codelineno-8-14></a>docker-compose<span class=w> </span>logs<span class=w> </span>-f
</span></code></pre></div> <h3 id=9>9. 监控和维护<a class=headerlink href=#9 title="Permanent link">&para;</a></h3> <div class="language-bash highlight"><pre><span></span><code><span id=__span-9-1><a id=__codelineno-9-1 name=__codelineno-9-1 href=#__codelineno-9-1></a><span class=c1># 服务健康检查脚本</span>
</span><span id=__span-9-2><a id=__codelineno-9-2 name=__codelineno-9-2 href=#__codelineno-9-2></a><span class=c1>#!/bin/bash</span>
</span><span id=__span-9-3><a id=__codelineno-9-3 name=__codelineno-9-3 href=#__codelineno-9-3></a><span class=nb>echo</span><span class=w> </span><span class=s2>&quot;检查太公心易MCP生态系统状态...&quot;</span>
</span><span id=__span-9-4><a id=__codelineno-9-4 name=__codelineno-9-4 href=#__codelineno-9-4></a>
</span><span id=__span-9-5><a id=__codelineno-9-5 name=__codelineno-9-5 href=#__codelineno-9-5></a><span class=c1># 检查N8N</span>
</span><span id=__span-9-6><a id=__codelineno-9-6 name=__codelineno-9-6 href=#__codelineno-9-6></a>curl<span class=w> </span>-f<span class=w> </span>http://localhost:5678/healthz<span class=w> </span><span class=o>||</span><span class=w> </span><span class=nb>echo</span><span class=w> </span><span class=s2>&quot;N8N服务异常&quot;</span>
</span><span id=__span-9-7><a id=__codelineno-9-7 name=__codelineno-9-7 href=#__codelineno-9-7></a>
</span><span id=__span-9-8><a id=__codelineno-9-8 name=__codelineno-9-8 href=#__codelineno-9-8></a><span class=c1># 检查MCP服务器</span>
</span><span id=__span-9-9><a id=__codelineno-9-9 name=__codelineno-9-9 href=#__codelineno-9-9></a><span class=k>for</span><span class=w> </span>port<span class=w> </span><span class=k>in</span><span class=w> </span><span class=m>8001</span><span class=w> </span><span class=m>8002</span><span class=w> </span><span class=m>8003</span><span class=w> </span><span class=m>8004</span><span class=p>;</span><span class=w> </span><span class=k>do</span>
</span><span id=__span-9-10><a id=__codelineno-9-10 name=__codelineno-9-10 href=#__codelineno-9-10></a><span class=w>    </span>curl<span class=w> </span>-f<span class=w> </span>http://localhost:<span class=nv>$port</span>/health<span class=w> </span><span class=o>||</span><span class=w> </span><span class=nb>echo</span><span class=w> </span><span class=s2>&quot;MCP服务器 </span><span class=nv>$port</span><span class=s2> 异常&quot;</span>
</span><span id=__span-9-11><a id=__codelineno-9-11 name=__codelineno-9-11 href=#__codelineno-9-11></a><span class=k>done</span>
</span><span id=__span-9-12><a id=__codelineno-9-12 name=__codelineno-9-12 href=#__codelineno-9-12></a>
</span><span id=__span-9-13><a id=__codelineno-9-13 name=__codelineno-9-13 href=#__codelineno-9-13></a><span class=c1># 检查Redis</span>
</span><span id=__span-9-14><a id=__codelineno-9-14 name=__codelineno-9-14 href=#__codelineno-9-14></a>redis-cli<span class=w> </span>ping<span class=w> </span><span class=o>||</span><span class=w> </span><span class=nb>echo</span><span class=w> </span><span class=s2>&quot;Redis服务异常&quot;</span>
</span></code></pre></div> <h3 id=10>10. 安全配置<a class=headerlink href=#10 title="Permanent link">&para;</a></h3> <ol> <li> <p><strong>防火墙设置</strong>: <div class="language-bash highlight"><pre><span></span><code><span id=__span-10-1><a id=__codelineno-10-1 name=__codelineno-10-1 href=#__codelineno-10-1></a>sudo<span class=w> </span>ufw<span class=w> </span>allow<span class=w> </span><span class=m>22</span><span class=w>    </span><span class=c1># SSH</span>
</span><span id=__span-10-2><a id=__codelineno-10-2 name=__codelineno-10-2 href=#__codelineno-10-2></a>sudo<span class=w> </span>ufw<span class=w> </span>allow<span class=w> </span><span class=m>80</span><span class=w>    </span><span class=c1># HTTP</span>
</span><span id=__span-10-3><a id=__codelineno-10-3 name=__codelineno-10-3 href=#__codelineno-10-3></a>sudo<span class=w> </span>ufw<span class=w> </span>allow<span class=w> </span><span class=m>443</span><span class=w>   </span><span class=c1># HTTPS</span>
</span><span id=__span-10-4><a id=__codelineno-10-4 name=__codelineno-10-4 href=#__codelineno-10-4></a>sudo<span class=w> </span>ufw<span class=w> </span><span class=nb>enable</span>
</span></code></pre></div></p> </li> <li> <p><strong>SSL证书</strong>: <div class="language-bash highlight"><pre><span></span><code><span id=__span-11-1><a id=__codelineno-11-1 name=__codelineno-11-1 href=#__codelineno-11-1></a><span class=c1># 使用Let&#39;s Encrypt</span>
</span><span id=__span-11-2><a id=__codelineno-11-2 name=__codelineno-11-2 href=#__codelineno-11-2></a>sudo<span class=w> </span>apt<span class=w> </span>install<span class=w> </span>certbot<span class=w> </span>python3-certbot-nginx
</span><span id=__span-11-3><a id=__codelineno-11-3 name=__codelineno-11-3 href=#__codelineno-11-3></a>sudo<span class=w> </span>certbot<span class=w> </span>--nginx<span class=w> </span>-d<span class=w> </span>your-korean-server.com
</span></code></pre></div></p> </li> <li> <p><strong>API认证</strong>:</p> </li> <li>JWT令牌认证</li> <li>API密钥管理</li> <li>请求频率限制</li> </ol> <h2 id=_4>🚀 使用方式<a class=headerlink href=#_4 title="Permanent link">&para;</a></h2> <h3 id=_5>本地连接韩国服务器<a class=headerlink href=#_5 title="Permanent link">&para;</a></h3> <div class="language-python highlight"><pre><span></span><code><span id=__span-12-1><a id=__codelineno-12-1 name=__codelineno-12-1 href=#__codelineno-12-1></a><span class=c1># 在本地cauldron项目中</span>
</span><span id=__span-12-2><a id=__codelineno-12-2 name=__codelineno-12-2 href=#__codelineno-12-2></a><span class=kn>from</span><span class=w> </span><span class=nn>remote_mcp_client</span><span class=w> </span><span class=kn>import</span> <span class=n>TaigongRemoteIntegration</span>
</span><span id=__span-12-3><a id=__codelineno-12-3 name=__codelineno-12-3 href=#__codelineno-12-3></a>
</span><span id=__span-12-4><a id=__codelineno-12-4 name=__codelineno-12-4 href=#__codelineno-12-4></a><span class=n>integration</span> <span class=o>=</span> <span class=n>TaigongRemoteIntegration</span><span class=p>(</span>
</span><span id=__span-12-5><a id=__codelineno-12-5 name=__codelineno-12-5 href=#__codelineno-12-5></a>    <span class=n>server_url</span><span class=o>=</span><span class=s2>&quot;https://your-korean-server.com&quot;</span><span class=p>,</span>
</span><span id=__span-12-6><a id=__codelineno-12-6 name=__codelineno-12-6 href=#__codelineno-12-6></a>    <span class=n>api_key</span><span class=o>=</span><span class=s2>&quot;your_api_key&quot;</span>
</span><span id=__span-12-7><a id=__codelineno-12-7 name=__codelineno-12-7 href=#__codelineno-12-7></a><span class=p>)</span>
</span><span id=__span-12-8><a id=__codelineno-12-8 name=__codelineno-12-8 href=#__codelineno-12-8></a>
</span><span id=__span-12-9><a id=__codelineno-12-9 name=__codelineno-12-9 href=#__codelineno-12-9></a><span class=c1># 获取增强情报</span>
</span><span id=__span-12-10><a id=__codelineno-12-10 name=__codelineno-12-10 href=#__codelineno-12-10></a><span class=n>intelligence</span> <span class=o>=</span> <span class=k>await</span> <span class=n>integration</span><span class=o>.</span><span class=n>get_enhanced_market_intelligence</span><span class=p>(</span>
</span><span id=__span-12-11><a id=__codelineno-12-11 name=__codelineno-12-11 href=#__codelineno-12-11></a>    <span class=n>query</span><span class=o>=</span><span class=s2>&quot;比特币价格分析&quot;</span>
</span><span id=__span-12-12><a id=__codelineno-12-12 name=__codelineno-12-12 href=#__codelineno-12-12></a><span class=p>)</span>
</span></code></pre></div> <h3 id=n8n>N8N工作流触发<a class=headerlink href=#n8n title="Permanent link">&para;</a></h3> <div class="language-bash highlight"><pre><span></span><code><span id=__span-13-1><a id=__codelineno-13-1 name=__codelineno-13-1 href=#__codelineno-13-1></a><span class=c1># 通过webhook触发分析</span>
</span><span id=__span-13-2><a id=__codelineno-13-2 name=__codelineno-13-2 href=#__codelineno-13-2></a>curl<span class=w> </span>-X<span class=w> </span>POST<span class=w> </span>https://your-korean-server.com/webhook/market-analysis<span class=w> </span><span class=se>\</span>
</span><span id=__span-13-3><a id=__codelineno-13-3 name=__codelineno-13-3 href=#__codelineno-13-3></a><span class=w>  </span>-H<span class=w> </span><span class=s2>&quot;Content-Type: application/json&quot;</span><span class=w> </span><span class=se>\</span>
</span><span id=__span-13-4><a id=__codelineno-13-4 name=__codelineno-13-4 href=#__codelineno-13-4></a><span class=w>  </span>-d<span class=w> </span><span class=s1>&#39;{&quot;symbol&quot;: &quot;BTC&quot;, &quot;event&quot;: &quot;price_surge&quot;, &quot;change&quot;: 15.5}&#39;</span>
</span></code></pre></div> <h2 id=_6>💰 成本估算<a class=headerlink href=#_6 title="Permanent link">&para;</a></h2> <ul> <li><strong>韩国VPS</strong>: $10-20/月</li> <li><strong>域名</strong>: $10/年</li> <li><strong>SSL证书</strong>: 免费 (Let's Encrypt)</li> <li><strong>API调用</strong>: 大部分免费额度</li> <li><strong>总计</strong>: ~$15/月</li> </ul> <p>这样的架构既保持了本地项目的轻量级，又通过韩国服务器提供了强大的MCP生态系统支持！</p> <aside class=md-source-file> <span class=md-source-file__fact> <span class=md-icon title=最后更新> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M21 13.1c-.1 0-.3.1-.4.2l-1 1 2.1 2.1 1-1c.2-.2.2-.6 0-.8l-1.3-1.3c-.1-.1-.2-.2-.4-.2m-1.9 1.8-6.1 6V23h2.1l6.1-6.1zM12.5 7v5.2l4 2.4-1 1L11 13V7zM11 21.9c-5.1-.5-9-4.8-9-9.9C2 6.5 6.5 2 12 2c5.3 0 9.6 4.1 10 9.3-.3-.1-.6-.2-1-.2s-.7.1-1 .2C19.6 7.2 16.2 4 12 4c-4.4 0-8 3.6-8 8 0 4.1 3.1 7.5 7.1 7.9l-.1.2z"/></svg> </span> <span class="git-revision-date-localized-plugin git-revision-date-localized-plugin-datetime" title="2025年7月10日 04:18:21 UTC">2025年7月10日 04:18:21</span> </span> <span class=md-source-file__fact> <span class=md-icon title=创建日期> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M14.47 15.08 11 13V7h1.5v5.25l3.08 1.83c-.41.28-.79.62-1.11 1m-1.39 4.84c-.36.05-.71.08-1.08.08-4.42 0-8-3.58-8-8s3.58-8 8-8 8 3.58 8 8c0 .37-.03.72-.08 1.08.69.1 1.33.32 1.92.64.1-.56.16-1.13.16-1.72 0-5.5-4.5-10-10-10S2 6.5 2 12s4.47 10 10 10c.59 0 1.16-.06 1.72-.16-.32-.59-.54-1.23-.64-1.92M18 15v3h-3v2h3v3h2v-3h3v-2h-3v-3z"/></svg> </span> <span class="git-revision-date-localized-plugin git-revision-date-localized-plugin-datetime" title="2025年7月3日 17:04:56 UTC">2025年7月3日 17:04:56</span> </span> </aside> <form class=md-feedback name=feedback hidden> <fieldset> <legend class=md-feedback__title> 这个页面有帮助吗？ </legend> <div class=md-feedback__inner> <div class=md-feedback__list> <button class="md-feedback__icon md-icon" type=submit title=有帮助 data-md-value=1> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M20 12a8 8 0 0 0-8-8 8 8 0 0 0-8 8 8 8 0 0 0 8 8 8 8 0 0 0 8-8m2 0a10 10 0 0 1-10 10A10 10 0 0 1 2 12 10 10 0 0 1 12 2a10 10 0 0 1 10 10M10 9.5c0 .8-.7 1.5-1.5 1.5S7 10.3 7 9.5 7.7 8 8.5 8s1.5.7 1.5 1.5m7 0c0 .8-.7 1.5-1.5 1.5S14 10.3 14 9.5 14.7 8 15.5 8s1.5.7 1.5 1.5m-5 7.73c-1.75 0-3.29-.73-4.19-1.81L9.23 14c.45.72 1.52 1.23 2.77 1.23s2.32-.51 2.77-1.23l1.42 1.42c-.9 1.08-2.44 1.81-4.19 1.81"/></svg> </button> <button class="md-feedback__icon md-icon" type=submit title=需要改进 data-md-value=0> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M20 12a8 8 0 0 0-8-8 8 8 0 0 0-8 8 8 8 0 0 0 8 8 8 8 0 0 0 8-8m2 0a10 10 0 0 1-10 10A10 10 0 0 1 2 12 10 10 0 0 1 12 2a10 10 0 0 1 10 10m-6.5-4c.8 0 1.5.7 1.5 1.5s-.7 1.5-1.5 1.5-1.5-.7-1.5-1.5.7-1.5 1.5-1.5M10 9.5c0 .8-.7 1.5-1.5 1.5S7 10.3 7 9.5 7.7 8 8.5 8s1.5.7 1.5 1.5m2 4.5c1.75 0 3.29.72 4.19 1.81l-1.42 1.42C14.32 16.5 13.25 16 12 16s-2.32.5-2.77 1.23l-1.42-1.42C8.71 14.72 10.25 14 12 14"/></svg> </button> </div> <div class=md-feedback__note> <div data-md-value=1 hidden> 感谢您的反馈！ </div> <div data-md-value=0 hidden> 感谢您的反馈！请通过GitHub Issues告诉我们如何改进。 </div> </div> </div> </fieldset> </form> </article> </div> <script>var tabs=__md_get("__tabs");if(Array.isArray(tabs))e:for(var set of document.querySelectorAll(".tabbed-set")){var labels=set.querySelector(".tabbed-labels");for(var tab of tabs)for(var label of labels.getElementsByTagName("label"))if(label.innerText.trim()===tab){var input=document.getElementById(label.htmlFor);input.checked=!0;continue e}}</script> <script>var target=document.getElementById(location.hash.slice(1));target&&target.name&&(target.checked=target.name.startsWith("__tabbed_"))</script> </div> <button type=button class="md-top md-icon" data-md-component=top hidden> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M13 20h-2V8l-5.5 5.5-1.42-1.42L12 4.16l7.92 7.92-1.42 1.42L13 8z"/></svg> 回到页面顶部 </button> </main> <footer class=md-footer> <div class="md-footer-meta md-typeset"> <div class="md-footer-meta__inner md-grid"> <div class=md-copyright> <div class=md-copyright__highlight> Copyright &copy; 2025 炼妖壶项目 - MIT License </div> Made with <a href=https://squidfunk.github.io/mkdocs-material/ target=_blank rel=noopener> Material for MkDocs </a> </div> <div class=md-social> <a href=https://github.com/your-username/jixia-academy target=_blank rel=noopener title=GitHub仓库 class=md-social__link> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 496 512"><!-- Font Awesome Free 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license/free (Icons: CC BY 4.0, Fonts: SIL OFL 1.1, Code: MIT License) Copyright 2024 Fonticons, Inc.--><path d="M165.9 397.4c0 2-2.3 3.6-5.2 3.6-3.3.3-5.6-1.3-5.6-3.6 0-2 2.3-3.6 5.2-3.6 3-.3 5.6 1.3 5.6 3.6m-31.1-4.5c-.7 2 1.3 4.3 4.3 4.9 2.6 1 5.6 0 6.2-2s-1.3-4.3-4.3-5.2c-2.6-.7-5.5.3-6.2 2.3m44.2-1.7c-2.9.7-4.9 2.6-4.6 4.9.3 2 2.9 3.3 5.9 2.6 2.9-.7 4.9-2.6 4.6-4.6-.3-1.9-3-3.2-5.9-2.9M244.8 8C106.1 8 0 113.3 0 252c0 110.9 69.8 205.8 169.5 239.2 12.8 2.3 17.3-5.6 17.3-12.1 0-6.2-.3-40.4-.3-61.4 0 0-70 15-84.7-29.8 0 0-11.4-29.1-27.8-36.6 0 0-22.9-15.7 1.6-15.4 0 0 24.9 2 38.6 25.8 21.9 38.6 58.6 27.5 72.9 20.9 2.3-16 8.8-27.1 16-33.7-55.9-6.2-112.3-14.3-112.3-110.5 0-27.5 7.6-41.3 23.6-58.9-2.6-6.5-11.1-33.3 2.6-67.9 20.9-6.5 69 27 69 27 20-5.6 41.5-8.5 62.8-8.5s42.8 2.9 62.8 8.5c0 0 48.1-33.6 69-27 13.7 34.7 5.2 61.4 2.6 67.9 16 17.7 25.8 31.5 25.8 58.9 0 96.5-58.9 104.2-114.8 110.5 9.2 7.9 17 22.9 17 46.4 0 33.7-.3 75.4-.3 83.6 0 6.5 4.6 14.4 17.3 12.1C428.2 457.8 496 362.9 496 252 496 113.3 383.5 8 244.8 8M97.2 352.9c-1.3 1-1 3.3.7 5.2 1.6 1.6 3.9 2.3 5.2 1 1.3-1 1-3.3-.7-5.2-1.6-1.6-3.9-2.3-5.2-1m-10.8-8.1c-.7 1.3.3 2.9 2.3 3.9 1.6 1 3.6.7 4.3-.7.7-1.3-.3-2.9-2.3-3.9-2-.6-3.6-.3-4.3.7m32.4 35.6c-1.6 1.3-1 4.3 1.3 6.2 2.3 2.3 5.2 2.6 6.5 1 1.3-1.3.7-4.3-1.3-6.2-2.2-2.3-5.2-2.6-6.5-1m-11.4-14.7c-1.6 1-1.6 3.6 0 5.9s4.3 3.3 5.6 2.3c1.6-1.3 1.6-3.9 0-6.2-1.4-2.3-4-3.3-5.6-2"/></svg> </a> <a href=https://your-mastodon-instance.com/@taishang_laojun target=_blank rel="noopener me" title=太上老君 class=md-social__link> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 448 512"><!-- Font Awesome Free 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license/free (Icons: CC BY 4.0, Fonts: SIL OFL 1.1, Code: MIT License) Copyright 2024 Fonticons, Inc.--><path d="M433 179.11c0-97.2-63.71-125.7-63.71-125.7-62.52-28.7-228.56-28.4-290.48 0 0 0-63.72 28.5-63.72 125.7 0 115.7-6.6 259.4 105.63 289.1 40.51 10.7 75.32 13 103.33 11.4 50.81-2.8 79.32-18.1 79.32-18.1l-1.7-36.9s-36.31 11.4-77.12 10.1c-40.41-1.4-83-4.4-89.63-54a102.5 102.5 0 0 1-.9-13.9c85.63 20.9 158.65 9.1 178.75 6.7 56.12-6.7 105-41.3 111.23-72.9 9.8-49.8 9-121.5 9-121.5m-75.12 125.2h-46.63v-114.2c0-49.7-64-51.6-64 6.9v62.5h-46.33V197c0-58.5-64-56.6-64-6.9v114.2H90.19c0-122.1-5.2-147.9 18.41-175 25.9-28.9 79.82-30.8 103.83 6.1l11.6 19.5 11.6-19.5c24.11-37.1 78.12-34.8 103.83-6.1 23.71 27.3 18.4 53 18.4 175z"/></svg> </a> <a href=https://twitter.com/jixia_academy target=_blank rel=noopener title=Twitter class=md-social__link> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 512 512"><!-- Font Awesome Free 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license/free (Icons: CC BY 4.0, Fonts: SIL OFL 1.1, Code: MIT License) Copyright 2024 Fonticons, Inc.--><path d="M459.37 151.716c.325 4.548.325 9.097.325 13.645 0 138.72-105.583 298.558-298.558 298.558-59.452 0-114.68-17.219-161.137-47.106 8.447.974 16.568 1.299 25.34 1.299 49.055 0 94.213-16.568 130.274-44.832-46.132-.975-84.792-31.188-98.112-72.772 6.498.974 12.995 1.624 19.818 1.624 9.421 0 18.843-1.3 27.614-3.573-48.081-9.747-84.143-51.98-84.143-102.985v-1.299c13.969 7.797 30.214 12.67 47.431 13.319-28.264-18.843-46.781-51.005-46.781-87.391 0-19.492 5.197-37.36 14.294-52.954 51.655 63.675 129.3 105.258 216.365 109.807-1.624-7.797-2.599-15.918-2.599-24.04 0-57.828 46.782-104.934 104.934-104.934 30.213 0 57.502 12.67 76.67 33.137 23.715-4.548 46.456-13.32 66.599-25.34-7.798 24.366-24.366 44.833-46.132 57.827 21.117-2.273 41.584-8.122 60.426-16.243-14.292 20.791-32.161 39.308-52.628 54.253"/></svg> </a> <a href=mailto:<EMAIL> target=_blank rel=noopener title=联系我们 class=md-social__link> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 512 512"><!-- Font Awesome Free 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license/free (Icons: CC BY 4.0, Fonts: SIL OFL 1.1, Code: MIT License) Copyright 2024 Fonticons, Inc.--><path d="M498.1 5.6c10.1 7 15.4 19.1 13.5 31.2l-64 416c-1.5 9.7-7.4 18.2-16 23s-18.9 5.4-28 1.6L284 427.7l-68.5 74.1c-8.9 9.7-22.9 12.9-35.2 8.1S160 493.2 160 480v-83.6c0-4 1.5-7.8 4.2-10.8l167.6-182.8c5.8-6.3 5.6-16-.4-22s-15.7-6.4-22-.7L106 360.8l-88.3-44.2C7.1 311.3.3 300.7 0 288.9s5.9-22.8 16.1-28.7l448-256c10.7-6.1 23.9-5.5 34 1.4"/></svg> </a> </div> </div> </div> </footer> </div> <div class=md-dialog data-md-component=dialog> <div class="md-dialog__inner md-typeset"></div> </div> <script id=__config type=application/json>{"base": "../../..", "features": ["navigation.instant", "navigation.tracking", "navigation.tabs", "navigation.tabs.sticky", "navigation.sections", "navigation.expand", "navigation.indexes", "navigation.top", "search.highlight", "search.share", "search.suggest", "content.code.annotate", "content.code.copy", "content.tabs.link"], "search": "../../../assets/javascripts/workers/search.d50fe291.min.js", "tags": null, "translations": {"clipboard.copied": "\u5df2\u590d\u5236", "clipboard.copy": "\u590d\u5236", "search.result.more.one": "\u5728\u8be5\u9875\u4e0a\u8fd8\u6709 1 \u4e2a\u7b26\u5408\u6761\u4ef6\u7684\u7ed3\u679c", "search.result.more.other": "\u5728\u8be5\u9875\u4e0a\u8fd8\u6709 # \u4e2a\u7b26\u5408\u6761\u4ef6\u7684\u7ed3\u679c", "search.result.none": "\u6ca1\u6709\u627e\u5230\u7b26\u5408\u6761\u4ef6\u7684\u7ed3\u679c", "search.result.one": "\u627e\u5230 1 \u4e2a\u7b26\u5408\u6761\u4ef6\u7684\u7ed3\u679c", "search.result.other": "# \u4e2a\u7b26\u5408\u6761\u4ef6\u7684\u7ed3\u679c", "search.result.placeholder": "\u952e\u5165\u4ee5\u5f00\u59cb\u641c\u7d22", "search.result.term.missing": "\u7f3a\u5c11", "select.version": "\u9009\u62e9\u5f53\u524d\u7248\u672c"}, "version": {"default": "latest", "provider": "mike"}}</script> <script src=../../../assets/javascripts/bundle.56ea9cef.min.js></script> <script src=../../../assets/javascripts/mathjax.js></script> <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script> <script src=https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js></script> </body> </html>