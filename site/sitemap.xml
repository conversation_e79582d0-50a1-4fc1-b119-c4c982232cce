<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
    <url>
         <loc>https://jingminzhang.github.io/cauldron/</loc>
         <lastmod>2025-07-13</lastmod>
    </url>
    <url>
         <loc>https://jingminzhang.github.io/cauldron/CONTRIBUTING/</loc>
         <lastmod>2025-07-13</lastmod>
    </url>
    <url>
         <loc>https://jingminzhang.github.io/cauldron/roadmap/</loc>
         <lastmod>2025-07-13</lastmod>
    </url>
    <url>
         <loc>https://jingminzhang.github.io/cauldron/architecture/ARCHITECTURE_NOTES/</loc>
         <lastmod>2025-07-13</lastmod>
    </url>
    <url>
         <loc>https://jingminzhang.github.io/cauldron/architecture/Omnipresent_AI_Agent_Architecture/</loc>
         <lastmod>2025-07-13</lastmod>
    </url>
    <url>
         <loc>https://jingminzhang.github.io/cauldron/archive/business/zilliz-demo-presentation/</loc>
         <lastmod>2025-07-13</lastmod>
    </url>
    <url>
         <loc>https://jingminzhang.github.io/cauldron/archive/business/zilliz-partnership-proposal/</loc>
         <lastmod>2025-07-13</lastmod>
    </url>
    <url>
         <loc>https://jingminzhang.github.io/cauldron/archive/legacy/TAIGONG_XINYI_ARCHITECTURE/</loc>
         <lastmod>2025-07-13</lastmod>
    </url>
    <url>
         <loc>https://jingminzhang.github.io/cauldron/archive/philosophy/digital_revenge_manifesto/</loc>
         <lastmod>2025-07-13</lastmod>
    </url>
    <url>
         <loc>https://jingminzhang.github.io/cauldron/archive/philosophy/user_guide_philosophy/</loc>
         <lastmod>2025-07-13</lastmod>
    </url>
    <url>
         <loc>https://jingminzhang.github.io/cauldron/explanation/concepts/ai_immortals/</loc>
         <lastmod>2025-07-13</lastmod>
    </url>
    <url>
         <loc>https://jingminzhang.github.io/cauldron/explanation/concepts/convolution/</loc>
         <lastmod>2025-07-13</lastmod>
    </url>
    <url>
         <loc>https://jingminzhang.github.io/cauldron/explanation/concepts/dadaosheng/</loc>
         <lastmod>2025-07-13</lastmod>
    </url>
    <url>
         <loc>https://jingminzhang.github.io/cauldron/explanation/concepts/gamefi/</loc>
         <lastmod>2025-07-13</lastmod>
    </url>
    <url>
         <loc>https://jingminzhang.github.io/cauldron/explanation/concepts/hero_journey_design/</loc>
         <lastmod>2025-07-13</lastmod>
    </url>
    <url>
         <loc>https://jingminzhang.github.io/cauldron/explanation/concepts/tusita-palace-n8n-workflows/</loc>
         <lastmod>2025-07-13</lastmod>
    </url>
    <url>
         <loc>https://jingminzhang.github.io/cauldron/explanation/concepts/yijing_debate_system/</loc>
         <lastmod>2025-07-13</lastmod>
    </url>
    <url>
         <loc>https://jingminzhang.github.io/cauldron/explanation/mathematical_foundations/</loc>
         <lastmod>2025-07-13</lastmod>
    </url>
    <url>
         <loc>https://jingminzhang.github.io/cauldron/explanation/mathematical_foundations/bagua_mathematical_model/</loc>
         <lastmod>2025-07-13</lastmod>
    </url>
    <url>
         <loc>https://jingminzhang.github.io/cauldron/explanation/mathematical_foundations/core_innovations/</loc>
         <lastmod>2025-07-13</lastmod>
    </url>
    <url>
         <loc>https://jingminzhang.github.io/cauldron/explanation/mathematical_foundations/debate_game_theory/</loc>
         <lastmod>2025-07-13</lastmod>
    </url>
    <url>
         <loc>https://jingminzhang.github.io/cauldron/explanation/mathematical_foundations/non_gaussian_framework/</loc>
         <lastmod>2025-07-13</lastmod>
    </url>
    <url>
         <loc>https://jingminzhang.github.io/cauldron/explanation/mathematical_foundations/rock_mechanics_theory/</loc>
         <lastmod>2025-07-13</lastmod>
    </url>
    <url>
         <loc>https://jingminzhang.github.io/cauldron/explanation/mathematical_foundations/rss_matrix_theory/</loc>
         <lastmod>2025-07-13</lastmod>
    </url>
    <url>
         <loc>https://jingminzhang.github.io/cauldron/explanation/project/ACADEMIC_OFFERING/</loc>
         <lastmod>2025-07-13</lastmod>
    </url>
    <url>
         <loc>https://jingminzhang.github.io/cauldron/explanation/project/about_index/</loc>
         <lastmod>2025-07-13</lastmod>
    </url>
    <url>
         <loc>https://jingminzhang.github.io/cauldron/explanation/project/manifesto/</loc>
         <lastmod>2025-07-13</lastmod>
    </url>
    <url>
         <loc>https://jingminzhang.github.io/cauldron/explanation/project/naming_philosophy/</loc>
         <lastmod>2025-07-13</lastmod>
    </url>
    <url>
         <loc>https://jingminzhang.github.io/cauldron/explanation/project/roadmap/</loc>
         <lastmod>2025-07-13</lastmod>
    </url>
    <url>
         <loc>https://jingminzhang.github.io/cauldron/explanation/project/vision/</loc>
         <lastmod>2025-07-13</lastmod>
    </url>
    <url>
         <loc>https://jingminzhang.github.io/cauldron/explanation/project/why%20anti-gods/</loc>
         <lastmod>2025-07-13</lastmod>
    </url>
    <url>
         <loc>https://jingminzhang.github.io/cauldron/getting-started/quick-start/</loc>
         <lastmod>2025-07-13</lastmod>
    </url>
    <url>
         <loc>https://jingminzhang.github.io/cauldron/guides/DOCS_REORGANIZATION_PLAN/</loc>
         <lastmod>2025-07-13</lastmod>
    </url>
    <url>
         <loc>https://jingminzhang.github.io/cauldron/guides/MCP_SETUP/</loc>
         <lastmod>2025-07-13</lastmod>
    </url>
    <url>
         <loc>https://jingminzhang.github.io/cauldron/guides/OPENSOURCE_ROADMAP/</loc>
         <lastmod>2025-07-13</lastmod>
    </url>
    <url>
         <loc>https://jingminzhang.github.io/cauldron/how-to-guides/features/</loc>
         <lastmod>2025-07-13</lastmod>
    </url>
    <url>
         <loc>https://jingminzhang.github.io/cauldron/how-to-guides/getting_started/</loc>
         <lastmod>2025-07-13</lastmod>
    </url>
    <url>
         <loc>https://jingminzhang.github.io/cauldron/how-to-guides/ib_fundamentals_guide/</loc>
         <lastmod>2025-07-13</lastmod>
    </url>
    <url>
         <loc>https://jingminzhang.github.io/cauldron/how-to-guides/offering/</loc>
         <lastmod>2025-07-13</lastmod>
    </url>
    <url>
         <loc>https://jingminzhang.github.io/cauldron/how-to-guides/deployment/heroku_deployment/</loc>
         <lastmod>2025-07-13</lastmod>
    </url>
    <url>
         <loc>https://jingminzhang.github.io/cauldron/how-to-guides/deployment/korean_server_deployment/</loc>
         <lastmod>2025-07-13</lastmod>
    </url>
    <url>
         <loc>https://jingminzhang.github.io/cauldron/how-to-guides/deployment/migration_guide/</loc>
         <lastmod>2025-07-13</lastmod>
    </url>
    <url>
         <loc>https://jingminzhang.github.io/cauldron/how-to-guides/development/code_cleanup/</loc>
         <lastmod>2025-07-13</lastmod>
    </url>
    <url>
         <loc>https://jingminzhang.github.io/cauldron/how-to-guides/development/doppler_integration/</loc>
         <lastmod>2025-07-13</lastmod>
    </url>
    <url>
         <loc>https://jingminzhang.github.io/cauldron/how-to-guides/development/opensource_readiness/</loc>
         <lastmod>2025-07-13</lastmod>
    </url>
    <url>
         <loc>https://jingminzhang.github.io/cauldron/how-to-guides/setup/OLLAMA_SETUP/</loc>
         <lastmod>2025-07-13</lastmod>
    </url>
    <url>
         <loc>https://jingminzhang.github.io/cauldron/how-to-guides/setup/database_setup/</loc>
         <lastmod>2025-07-13</lastmod>
    </url>
    <url>
         <loc>https://jingminzhang.github.io/cauldron/reference/ai_personalities/</loc>
         <lastmod>2025-07-13</lastmod>
    </url>
    <url>
         <loc>https://jingminzhang.github.io/cauldron/reference/database_overview/</loc>
         <lastmod>2025-07-13</lastmod>
    </url>
    <url>
         <loc>https://jingminzhang.github.io/cauldron/reference/architecture/</loc>
         <lastmod>2025-07-13</lastmod>
    </url>
    <url>
         <loc>https://jingminzhang.github.io/cauldron/reference/architecture/01-system-overview/</loc>
         <lastmod>2025-07-13</lastmod>
    </url>
    <url>
         <loc>https://jingminzhang.github.io/cauldron/reference/architecture/02-three-brain-architecture/</loc>
         <lastmod>2025-07-13</lastmod>
    </url>
    <url>
         <loc>https://jingminzhang.github.io/cauldron/reference/architecture/03-data-flow-architecture/</loc>
         <lastmod>2025-07-13</lastmod>
    </url>
    <url>
         <loc>https://jingminzhang.github.io/cauldron/reference/architecture/CONDUCTOR_ARCHITECTURE/</loc>
         <lastmod>2025-07-13</lastmod>
    </url>
    <url>
         <loc>https://jingminzhang.github.io/cauldron/reference/architecture/ZILLIZ_DEMO_ARCHITECTURE/</loc>
         <lastmod>2025-07-13</lastmod>
    </url>
    <url>
         <loc>https://jingminzhang.github.io/cauldron/reference/architecture/agent_memory_architecture/</loc>
         <lastmod>2025-07-13</lastmod>
    </url>
    <url>
         <loc>https://jingminzhang.github.io/cauldron/reference/architecture/architecture-diagrams/</loc>
         <lastmod>2025-07-13</lastmod>
    </url>
    <url>
         <loc>https://jingminzhang.github.io/cauldron/reference/architecture/n8n_autogen_mcp_zilliz_architecture/</loc>
         <lastmod>2025-07-13</lastmod>
    </url>
    <url>
         <loc>https://jingminzhang.github.io/cauldron/reference/architecture/three_brain_data_flow/</loc>
         <lastmod>2025-07-13</lastmod>
    </url>
    <url>
         <loc>https://jingminzhang.github.io/cauldron/reference/features/</loc>
         <lastmod>2025-07-13</lastmod>
    </url>
    <url>
         <loc>https://jingminzhang.github.io/cauldron/research/Complete_RAG_Knowledge_Graph_Selection_Guide/</loc>
         <lastmod>2025-07-13</lastmod>
    </url>
    <url>
         <loc>https://jingminzhang.github.io/cauldron/research/GraphRAG_Complete_Guide/</loc>
         <lastmod>2025-07-13</lastmod>
    </url>
    <url>
         <loc>https://jingminzhang.github.io/cauldron/research/GraphRAG_vs_KAG_Comparison/</loc>
         <lastmod>2025-07-13</lastmod>
    </url>
    <url>
         <loc>https://jingminzhang.github.io/cauldron/research/KAG_Milvus_Integration_Architecture/</loc>
         <lastmod>2025-07-13</lastmod>
    </url>
    <url>
         <loc>https://jingminzhang.github.io/cauldron/standards/RFC-FSRP-Draft/</loc>
         <lastmod>2025-07-13</lastmod>
    </url>
    <url>
         <loc>https://jingminzhang.github.io/cauldron/systems/GAMEFI_SYSTEM_SUMMARY/</loc>
         <lastmod>2025-07-13</lastmod>
    </url>
    <url>
         <loc>https://jingminzhang.github.io/cauldron/tutorials/getting_started/</loc>
         <lastmod>2025-07-13</lastmod>
    </url>
    <url>
         <loc>https://jingminzhang.github.io/cauldron/vision/three-tiers/</loc>
         <lastmod>2025-07-13</lastmod>
    </url>
</urlset>