<!doctype html><html lang=zh class=no-js> <head><meta charset=utf-8><meta name=viewport content="width=device-width,initial-scale=1"><meta name=description content="AI驱动的投资决策系统 - 稷下学宫智能体辩论平台"><meta name=author content=炼妖壶开发团队><link href=https://jingminzhang.github.io/cauldron/standards/RFC-FSRP-Draft/ rel=canonical><link rel=icon href=../../assets/images/favicon.png><meta name=generator content="mkdocs-1.6.1, mkdocs-material-9.6.15"><title>RFC XXXX: Financial Semantic Routing Protocol (FSRP) - 炼妖壶 (Cauldron)</title><link rel=stylesheet href=../../assets/stylesheets/main.342714a4.min.css><link rel=stylesheet href=../../assets/stylesheets/palette.06af60db.min.css><link rel=preconnect href=https://fonts.gstatic.com crossorigin><link rel=stylesheet href="https://fonts.googleapis.com/css?family=Noto+Sans+SC:300,300i,400,400i,700,700i%7CJetBrains+Mono:400,400i,700,700i&display=fallback"><style>:root{--md-text-font:"Noto Sans SC";--md-code-font:"JetBrains Mono"}</style><link rel=stylesheet href=../../assets/stylesheets/extra.css><script>__md_scope=new URL("../..",location),__md_hash=e=>[...e].reduce(((e,_)=>(e<<5)-e+_.charCodeAt(0)),0),__md_get=(e,_=localStorage,t=__md_scope)=>JSON.parse(_.getItem(t.pathname+"."+e)),__md_set=(e,_,t=localStorage,a=__md_scope)=>{try{t.setItem(a.pathname+"."+e,JSON.stringify(_))}catch(e){}}</script><script id=__analytics>function __md_analytics(){function e(){dataLayer.push(arguments)}window.dataLayer=window.dataLayer||[],e("js",new Date),e("config","G-XXXXXXXXXX"),document.addEventListener("DOMContentLoaded",(function(){document.forms.search&&document.forms.search.query.addEventListener("blur",(function(){this.value&&e("event","search",{search_term:this.value})}));document$.subscribe((function(){var t=document.forms.feedback;if(void 0!==t)for(var a of t.querySelectorAll("[type=submit]"))a.addEventListener("click",(function(a){a.preventDefault();var n=document.location.pathname,d=this.getAttribute("data-md-value");e("event","feedback",{page:n,data:d}),t.firstElementChild.disabled=!0;var r=t.querySelector(".md-feedback__note [data-md-value='"+d+"']");r&&(r.hidden=!1)})),t.hidden=!1})),location$.subscribe((function(t){e("config","G-XXXXXXXXXX",{page_path:t.pathname})}))}));var t=document.createElement("script");t.async=!0,t.src="https://www.googletagmanager.com/gtag/js?id=G-XXXXXXXXXX",document.getElementById("__analytics").insertAdjacentElement("afterEnd",t)}</script><script>"undefined"!=typeof __md_analytics&&__md_analytics()</script></head> <body dir=ltr data-md-color-scheme=default data-md-color-primary=indigo data-md-color-accent=amber> <input class=md-toggle data-md-toggle=drawer type=checkbox id=__drawer autocomplete=off> <input class=md-toggle data-md-toggle=search type=checkbox id=__search autocomplete=off> <label class=md-overlay for=__drawer></label> <div data-md-component=skip> <a href=#rfc-xxxx-financial-semantic-routing-protocol-fsrp class=md-skip> 跳转至 </a> </div> <div data-md-component=announce> </div> <div data-md-color-scheme=default data-md-component=outdated hidden> </div> <header class="md-header md-header--shadow md-header--lifted" data-md-component=header> <nav class="md-header__inner md-grid" aria-label=页眉> <a href=../.. title="炼妖壶 (Cauldron)" class="md-header__button md-logo" aria-label="炼妖壶 (Cauldron)" data-md-component=logo> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M12 3 1 9l11 6 9-4.91V17h2V9M5 13.18v4L12 21l7-3.82v-4L12 17z"/></svg> </a> <label class="md-header__button md-icon" for=__drawer> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M3 6h18v2H3zm0 5h18v2H3zm0 5h18v2H3z"/></svg> </label> <div class=md-header__title data-md-component=header-title> <div class=md-header__ellipsis> <div class=md-header__topic> <span class=md-ellipsis> 炼妖壶 (Cauldron) </span> </div> <div class=md-header__topic data-md-component=header-topic> <span class=md-ellipsis> RFC XXXX: Financial Semantic Routing Protocol (FSRP) </span> </div> </div> </div> <form class=md-header__option data-md-component=palette> <input class=md-option data-md-color-media="(prefers-color-scheme: light)" data-md-color-scheme=default data-md-color-primary=indigo data-md-color-accent=amber aria-label=切换到暗黑模式 type=radio name=__palette id=__palette_0> <label class="md-header__button md-icon" title=切换到暗黑模式 for=__palette_1 hidden> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M12 8a4 4 0 0 0-4 4 4 4 0 0 0 4 4 4 4 0 0 0 4-4 4 4 0 0 0-4-4m0 10a6 6 0 0 1-6-6 6 6 0 0 1 6-6 6 6 0 0 1 6 6 6 6 0 0 1-6 6m8-9.31V4h-4.69L12 .69 8.69 4H4v4.69L.69 12 4 15.31V20h4.69L12 23.31 15.31 20H20v-4.69L23.31 12z"/></svg> </label> <input class=md-option data-md-color-media="(prefers-color-scheme: dark)" data-md-color-scheme=slate data-md-color-primary=indigo data-md-color-accent=amber aria-label=切换到明亮模式 type=radio name=__palette id=__palette_1> <label class="md-header__button md-icon" title=切换到明亮模式 for=__palette_0 hidden> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M12 18c-.89 0-1.74-.2-2.5-.55C11.56 16.5 13 14.42 13 12s-1.44-4.5-3.5-5.45C10.26 6.2 11.11 6 12 6a6 6 0 0 1 6 6 6 6 0 0 1-6 6m8-9.31V4h-4.69L12 .69 8.69 4H4v4.69L.69 12 4 15.31V20h4.69L12 23.31 15.31 20H20v-4.69L23.31 12z"/></svg> </label> </form> <script>var palette=__md_get("__palette");if(palette&&palette.color){if("(prefers-color-scheme)"===palette.color.media){var media=matchMedia("(prefers-color-scheme: light)"),input=document.querySelector(media.matches?"[data-md-color-media='(prefers-color-scheme: light)']":"[data-md-color-media='(prefers-color-scheme: dark)']");palette.color.media=input.getAttribute("data-md-color-media"),palette.color.scheme=input.getAttribute("data-md-color-scheme"),palette.color.primary=input.getAttribute("data-md-color-primary"),palette.color.accent=input.getAttribute("data-md-color-accent")}for(var[key,value]of Object.entries(palette.color))document.body.setAttribute("data-md-color-"+key,value)}</script> <label class="md-header__button md-icon" for=__search> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M9.5 3A6.5 6.5 0 0 1 16 9.5c0 1.61-.59 3.09-1.56 4.23l.27.27h.79l5 5-1.5 1.5-5-5v-.79l-.27-.27A6.52 6.52 0 0 1 9.5 16 6.5 6.5 0 0 1 3 9.5 6.5 6.5 0 0 1 9.5 3m0 2C7 5 5 7 5 9.5S7 14 9.5 14 14 12 14 9.5 12 5 9.5 5"/></svg> </label> <div class=md-search data-md-component=search role=dialog> <label class=md-search__overlay for=__search></label> <div class=md-search__inner role=search> <form class=md-search__form name=search> <input type=text class=md-search__input name=query aria-label=搜索 placeholder=搜索 autocapitalize=off autocorrect=off autocomplete=off spellcheck=false data-md-component=search-query required> <label class="md-search__icon md-icon" for=__search> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M9.5 3A6.5 6.5 0 0 1 16 9.5c0 1.61-.59 3.09-1.56 4.23l.27.27h.79l5 5-1.5 1.5-5-5v-.79l-.27-.27A6.52 6.52 0 0 1 9.5 16 6.5 6.5 0 0 1 3 9.5 6.5 6.5 0 0 1 9.5 3m0 2C7 5 5 7 5 9.5S7 14 9.5 14 14 12 14 9.5 12 5 9.5 5"/></svg> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M20 11v2H8l5.5 5.5-1.42 1.42L4.16 12l7.92-7.92L13.5 5.5 8 11z"/></svg> </label> <nav class=md-search__options aria-label=查找> <a href=javascript:void(0) class="md-search__icon md-icon" title=分享 aria-label=分享 data-clipboard data-clipboard-text data-md-component=search-share tabindex=-1> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M18 16.08c-.76 0-1.44.3-1.96.77L8.91 12.7c.05-.23.09-.46.09-.7s-.04-.47-.09-.7l7.05-4.11c.54.5 1.25.81 2.04.81a3 3 0 0 0 3-3 3 3 0 0 0-3-3 3 3 0 0 0-3 3c0 .***********.7L8.04 9.81C7.5 9.31 6.79 9 6 9a3 3 0 0 0-3 3 3 3 0 0 0 3 3c.79 0 1.5-.31 2.04-.81l7.12 4.15c-.05.21-.08.43-.08.66 0 1.61 1.31 2.91 2.92 2.91s2.92-1.3 2.92-2.91A2.92 2.92 0 0 0 18 16.08"/></svg> </a> <button type=reset class="md-search__icon md-icon" title=清空当前内容 aria-label=清空当前内容 tabindex=-1> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M19 6.41 17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/></svg> </button> </nav> <div class=md-search__suggest data-md-component=search-suggest></div> </form> <div class=md-search__output> <div class=md-search__scrollwrap tabindex=0 data-md-scrollfix> <div class=md-search-result data-md-component=search-result> <div class=md-search-result__meta> 正在初始化搜索引擎 </div> <ol class=md-search-result__list role=presentation></ol> </div> </div> </div> </div> </div> <div class=md-header__source> <a href=https://github.com/jingminzhang/cauldron title=前往仓库 class=md-source data-md-component=source> <div class="md-source__icon md-icon"> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 496 512"><!-- Font Awesome Free 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license/free (Icons: CC BY 4.0, Fonts: SIL OFL 1.1, Code: MIT License) Copyright 2024 Fonticons, Inc.--><path d="M165.9 397.4c0 2-2.3 3.6-5.2 3.6-3.3.3-5.6-1.3-5.6-3.6 0-2 2.3-3.6 5.2-3.6 3-.3 5.6 1.3 5.6 3.6m-31.1-4.5c-.7 2 1.3 4.3 4.3 4.9 2.6 1 5.6 0 6.2-2s-1.3-4.3-4.3-5.2c-2.6-.7-5.5.3-6.2 2.3m44.2-1.7c-2.9.7-4.9 2.6-4.6 4.9.3 2 2.9 3.3 5.9 2.6 2.9-.7 4.9-2.6 4.6-4.6-.3-1.9-3-3.2-5.9-2.9M244.8 8C106.1 8 0 113.3 0 252c0 110.9 69.8 205.8 169.5 239.2 12.8 2.3 17.3-5.6 17.3-12.1 0-6.2-.3-40.4-.3-61.4 0 0-70 15-84.7-29.8 0 0-11.4-29.1-27.8-36.6 0 0-22.9-15.7 1.6-15.4 0 0 24.9 2 38.6 25.8 21.9 38.6 58.6 27.5 72.9 20.9 2.3-16 8.8-27.1 16-33.7-55.9-6.2-112.3-14.3-112.3-110.5 0-27.5 7.6-41.3 23.6-58.9-2.6-6.5-11.1-33.3 2.6-67.9 20.9-6.5 69 27 69 27 20-5.6 41.5-8.5 62.8-8.5s42.8 2.9 62.8 8.5c0 0 48.1-33.6 69-27 13.7 34.7 5.2 61.4 2.6 67.9 16 17.7 25.8 31.5 25.8 58.9 0 96.5-58.9 104.2-114.8 110.5 9.2 7.9 17 22.9 17 46.4 0 33.7-.3 75.4-.3 83.6 0 6.5 4.6 14.4 17.3 12.1C428.2 457.8 496 362.9 496 252 496 113.3 383.5 8 244.8 8M97.2 352.9c-1.3 1-1 3.3.7 5.2 1.6 1.6 3.9 2.3 5.2 1 1.3-1 1-3.3-.7-5.2-1.6-1.6-3.9-2.3-5.2-1m-10.8-8.1c-.7 1.3.3 2.9 2.3 3.9 1.6 1 3.6.7 4.3-.7.7-1.3-.3-2.9-2.3-3.9-2-.6-3.6-.3-4.3.7m32.4 35.6c-1.6 1.3-1 4.3 1.3 6.2 2.3 2.3 5.2 2.6 6.5 1 1.3-1.3.7-4.3-1.3-6.2-2.2-2.3-5.2-2.6-6.5-1m-11.4-14.7c-1.6 1-1.6 3.6 0 5.9s4.3 3.3 5.6 2.3c1.6-1.3 1.6-3.9 0-6.2-1.4-2.3-4-3.3-5.6-2"/></svg> </div> <div class=md-source__repository> jingminzhang/cauldron </div> </a> </div> </nav> <nav class=md-tabs aria-label=标签 data-md-component=tabs> <div class=md-grid> <ul class=md-tabs__list> <li class=md-tabs__item> <a href=../.. class=md-tabs__link> 🏠 首页 </a> </li> <li class=md-tabs__item> <a href=../../getting-started/quick-start/ class=md-tabs__link> 🚀 快速开始 </a> </li> <li class=md-tabs__item> <a href=../../reference/ai_personalities/ class=md-tabs__link> 🎭 AI仙人 </a> </li> <li class=md-tabs__item> <a href=../../architecture/ARCHITECTURE_NOTES/ class=md-tabs__link> 🏗️ 架构设计 </a> </li> <li class=md-tabs__item> <a href=../../how-to-guides/features/ class=md-tabs__link> ⚡ 核心功能 </a> </li> <li class=md-tabs__item> <a href=../../reference/database_overview/ class=md-tabs__link> 🔧 技术文档 </a> </li> <li class=md-tabs__item> <a href=../../vision/three-tiers/ class=md-tabs__link> 📜 项目愿景 </a> </li> <li class=md-tabs__item> <a href=../../how-to-guides/deployment/ class=md-tabs__link> 🚀 部署指南 </a> </li> <li class=md-tabs__item> <a href=../../api/ class=md-tabs__link> 📡 API文档 </a> </li> <li class=md-tabs__item> <a href=../../CONTRIBUTING/ class=md-tabs__link> 🤝 贡献指南 </a> </li> </ul> </div> </nav> </header> <div class=md-container data-md-component=container> <main class=md-main data-md-component=main> <div class="md-main__inner md-grid"> <div class="md-sidebar md-sidebar--primary" data-md-component=sidebar data-md-type=navigation> <div class=md-sidebar__scrollwrap> <div class=md-sidebar__inner> <nav class="md-nav md-nav--primary md-nav--lifted" aria-label=导航栏 data-md-level=0> <label class=md-nav__title for=__drawer> <a href=../.. title="炼妖壶 (Cauldron)" class="md-nav__button md-logo" aria-label="炼妖壶 (Cauldron)" data-md-component=logo> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M12 3 1 9l11 6 9-4.91V17h2V9M5 13.18v4L12 21l7-3.82v-4L12 17z"/></svg> </a> 炼妖壶 (Cauldron) </label> <div class=md-nav__source> <a href=https://github.com/jingminzhang/cauldron title=前往仓库 class=md-source data-md-component=source> <div class="md-source__icon md-icon"> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 496 512"><!-- Font Awesome Free 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license/free (Icons: CC BY 4.0, Fonts: SIL OFL 1.1, Code: MIT License) Copyright 2024 Fonticons, Inc.--><path d="M165.9 397.4c0 2-2.3 3.6-5.2 3.6-3.3.3-5.6-1.3-5.6-3.6 0-2 2.3-3.6 5.2-3.6 3-.3 5.6 1.3 5.6 3.6m-31.1-4.5c-.7 2 1.3 4.3 4.3 4.9 2.6 1 5.6 0 6.2-2s-1.3-4.3-4.3-5.2c-2.6-.7-5.5.3-6.2 2.3m44.2-1.7c-2.9.7-4.9 2.6-4.6 4.9.3 2 2.9 3.3 5.9 2.6 2.9-.7 4.9-2.6 4.6-4.6-.3-1.9-3-3.2-5.9-2.9M244.8 8C106.1 8 0 113.3 0 252c0 110.9 69.8 205.8 169.5 239.2 12.8 2.3 17.3-5.6 17.3-12.1 0-6.2-.3-40.4-.3-61.4 0 0-70 15-84.7-29.8 0 0-11.4-29.1-27.8-36.6 0 0-22.9-15.7 1.6-15.4 0 0 24.9 2 38.6 25.8 21.9 38.6 58.6 27.5 72.9 20.9 2.3-16 8.8-27.1 16-33.7-55.9-6.2-112.3-14.3-112.3-110.5 0-27.5 7.6-41.3 23.6-58.9-2.6-6.5-11.1-33.3 2.6-67.9 20.9-6.5 69 27 69 27 20-5.6 41.5-8.5 62.8-8.5s42.8 2.9 62.8 8.5c0 0 48.1-33.6 69-27 13.7 34.7 5.2 61.4 2.6 67.9 16 17.7 25.8 31.5 25.8 58.9 0 96.5-58.9 104.2-114.8 110.5 9.2 7.9 17 22.9 17 46.4 0 33.7-.3 75.4-.3 83.6 0 6.5 4.6 14.4 17.3 12.1C428.2 457.8 496 362.9 496 252 496 113.3 383.5 8 244.8 8M97.2 352.9c-1.3 1-1 3.3.7 5.2 1.6 1.6 3.9 2.3 5.2 1 1.3-1 1-3.3-.7-5.2-1.6-1.6-3.9-2.3-5.2-1m-10.8-8.1c-.7 1.3.3 2.9 2.3 3.9 1.6 1 3.6.7 4.3-.7.7-1.3-.3-2.9-2.3-3.9-2-.6-3.6-.3-4.3.7m32.4 35.6c-1.6 1.3-1 4.3 1.3 6.2 2.3 2.3 5.2 2.6 6.5 1 1.3-1.3.7-4.3-1.3-6.2-2.2-2.3-5.2-2.6-6.5-1m-11.4-14.7c-1.6 1-1.6 3.6 0 5.9s4.3 3.3 5.6 2.3c1.6-1.3 1.6-3.9 0-6.2-1.4-2.3-4-3.3-5.6-2"/></svg> </div> <div class=md-source__repository> jingminzhang/cauldron </div> </a> </div> <ul class=md-nav__list data-md-scrollfix> <li class="md-nav__item md-nav__item--nested"> <input class="md-nav__toggle md-toggle md-toggle--indeterminate" type=checkbox id=__nav_1> <div class="md-nav__link md-nav__container"> <a href=../.. class="md-nav__link "> <span class=md-ellipsis> 🏠 首页 </span> </a> <label class="md-nav__link " for=__nav_1 id=__nav_1_label tabindex=0> <span class="md-nav__icon md-icon"></span> </label> </div> <nav class=md-nav data-md-level=1 aria-labelledby=__nav_1_label aria-expanded=false> <label class=md-nav__title for=__nav_1> <span class="md-nav__icon md-icon"></span> 🏠 首页 </label> <ul class=md-nav__list data-md-scrollfix> <li class=md-nav__item> <a href=../../roadmap/ class=md-nav__link> <span class=md-ellipsis> 发展路线图 </span> </a> </li> </ul> </nav> </li> <li class="md-nav__item md-nav__item--nested"> <input class="md-nav__toggle md-toggle md-toggle--indeterminate" type=checkbox id=__nav_2> <label class=md-nav__link for=__nav_2 id=__nav_2_label tabindex=0> <span class=md-ellipsis> 🚀 快速开始 </span> <span class="md-nav__icon md-icon"></span> </label> <nav class=md-nav data-md-level=1 aria-labelledby=__nav_2_label aria-expanded=false> <label class=md-nav__title for=__nav_2> <span class="md-nav__icon md-icon"></span> 🚀 快速开始 </label> <ul class=md-nav__list data-md-scrollfix> <li class=md-nav__item> <a href=../../getting-started/quick-start/ class=md-nav__link> <span class=md-ellipsis> 快速开始 </span> </a> </li> <li class=md-nav__item> <a href=../../tutorials/getting_started/ class=md-nav__link> <span class=md-ellipsis> 入门教程 </span> </a> </li> <li class=md-nav__item> <a href=../../how-to-guides/getting_started/ class=md-nav__link> <span class=md-ellipsis> 使用指南 </span> </a> </li> </ul> </nav> </li> <li class="md-nav__item md-nav__item--nested"> <input class="md-nav__toggle md-toggle md-toggle--indeterminate" type=checkbox id=__nav_3> <label class=md-nav__link for=__nav_3 id=__nav_3_label tabindex=0> <span class=md-ellipsis> 🎭 AI仙人 </span> <span class="md-nav__icon md-icon"></span> </label> <nav class=md-nav data-md-level=1 aria-labelledby=__nav_3_label aria-expanded=false> <label class=md-nav__title for=__nav_3> <span class="md-nav__icon md-icon"></span> 🎭 AI仙人 </label> <ul class=md-nav__list data-md-scrollfix> <li class=md-nav__item> <a href=../../reference/ai_personalities/ class=md-nav__link> <span class=md-ellipsis> AI人格设计 </span> </a> </li> </ul> </nav> </li> <li class="md-nav__item md-nav__item--nested"> <input class="md-nav__toggle md-toggle md-toggle--indeterminate" type=checkbox id=__nav_4> <label class=md-nav__link for=__nav_4 id=__nav_4_label tabindex=0> <span class=md-ellipsis> 🏗️ 架构设计 </span> <span class="md-nav__icon md-icon"></span> </label> <nav class=md-nav data-md-level=1 aria-labelledby=__nav_4_label aria-expanded=false> <label class=md-nav__title for=__nav_4> <span class="md-nav__icon md-icon"></span> 🏗️ 架构设计 </label> <ul class=md-nav__list data-md-scrollfix> <li class=md-nav__item> <a href=../../architecture/ARCHITECTURE_NOTES/ class=md-nav__link> <span class=md-ellipsis> 架构笔记 </span> </a> </li> <li class=md-nav__item> <a href=../../architecture/Omnipresent_AI_Agent_Architecture/ class=md-nav__link> <span class=md-ellipsis> 全能AI智能体架构 </span> </a> </li> </ul> </nav> </li> <li class="md-nav__item md-nav__item--nested"> <input class="md-nav__toggle md-toggle md-toggle--indeterminate" type=checkbox id=__nav_5> <label class=md-nav__link for=__nav_5 id=__nav_5_label tabindex=0> <span class=md-ellipsis> ⚡ 核心功能 </span> <span class="md-nav__icon md-icon"></span> </label> <nav class=md-nav data-md-level=1 aria-labelledby=__nav_5_label aria-expanded=false> <label class=md-nav__title for=__nav_5> <span class="md-nav__icon md-icon"></span> ⚡ 核心功能 </label> <ul class=md-nav__list data-md-scrollfix> <li class=md-nav__item> <a href=../../how-to-guides/features/ class=md-nav__link> <span class=md-ellipsis> 功能概览 </span> </a> </li> <li class=md-nav__item> <a href=../../how-to-guides/offering/ class=md-nav__link> <span class=md-ellipsis> 产品定位 </span> </a> </li> </ul> </nav> </li> <li class="md-nav__item md-nav__item--nested"> <input class="md-nav__toggle md-toggle md-toggle--indeterminate" type=checkbox id=__nav_6> <label class=md-nav__link for=__nav_6 id=__nav_6_label tabindex=0> <span class=md-ellipsis> 🔧 技术文档 </span> <span class="md-nav__icon md-icon"></span> </label> <nav class=md-nav data-md-level=1 aria-labelledby=__nav_6_label aria-expanded=false> <label class=md-nav__title for=__nav_6> <span class="md-nav__icon md-icon"></span> 🔧 技术文档 </label> <ul class=md-nav__list data-md-scrollfix> <li class=md-nav__item> <a href=../../reference/database_overview/ class=md-nav__link> <span class=md-ellipsis> 数据库概览 </span> </a> </li> <li class=md-nav__item> <a href=../../guides/MCP_SETUP/ class=md-nav__link> <span class=md-ellipsis> MCP设置 </span> </a> </li> <li class=md-nav__item> <a href=../../how-to-guides/ib_fundamentals_guide/ class=md-nav__link> <span class=md-ellipsis> IB基础指南 </span> </a> </li> </ul> </nav> </li> <li class="md-nav__item md-nav__item--nested"> <input class="md-nav__toggle md-toggle md-toggle--indeterminate" type=checkbox id=__nav_7> <label class=md-nav__link for=__nav_7 id=__nav_7_label tabindex=0> <span class=md-ellipsis> 📜 项目愿景 </span> <span class="md-nav__icon md-icon"></span> </label> <nav class=md-nav data-md-level=1 aria-labelledby=__nav_7_label aria-expanded=false> <label class=md-nav__title for=__nav_7> <span class="md-nav__icon md-icon"></span> 📜 项目愿景 </label> <ul class=md-nav__list data-md-scrollfix> <li class=md-nav__item> <a href=../../vision/three-tiers/ class=md-nav__link> <span class=md-ellipsis> 三层架构 </span> </a> </li> <li class=md-nav__item> <a href=../../guides/OPENSOURCE_ROADMAP/ class=md-nav__link> <span class=md-ellipsis> 开源路线图 </span> </a> </li> </ul> </nav> </li> <li class="md-nav__item md-nav__item--nested"> <input class="md-nav__toggle md-toggle md-toggle--indeterminate" type=checkbox id=__nav_8> <label class=md-nav__link for=__nav_8 id=__nav_8_label tabindex=0> <span class=md-ellipsis> 🚀 部署指南 </span> <span class="md-nav__icon md-icon"></span> </label> <nav class=md-nav data-md-level=1 aria-labelledby=__nav_8_label aria-expanded=false> <label class=md-nav__title for=__nav_8> <span class="md-nav__icon md-icon"></span> 🚀 部署指南 </label> <ul class=md-nav__list data-md-scrollfix> <li class=md-nav__item> <a href=../../how-to-guides/deployment/ class=md-nav__link> <span class=md-ellipsis> 部署指南 </span> </a> </li> </ul> </nav> </li> <li class="md-nav__item md-nav__item--nested"> <input class="md-nav__toggle md-toggle md-toggle--indeterminate" type=checkbox id=__nav_9> <label class=md-nav__link for=__nav_9 id=__nav_9_label tabindex=0> <span class=md-ellipsis> 📡 API文档 </span> <span class="md-nav__icon md-icon"></span> </label> <nav class=md-nav data-md-level=1 aria-labelledby=__nav_9_label aria-expanded=false> <label class=md-nav__title for=__nav_9> <span class="md-nav__icon md-icon"></span> 📡 API文档 </label> <ul class=md-nav__list data-md-scrollfix> <li class=md-nav__item> <a href=../../api/ class=md-nav__link> <span class=md-ellipsis> API参考 </span> </a> </li> </ul> </nav> </li> <li class="md-nav__item md-nav__item--nested"> <input class="md-nav__toggle md-toggle md-toggle--indeterminate" type=checkbox id=__nav_10> <label class=md-nav__link for=__nav_10 id=__nav_10_label tabindex=0> <span class=md-ellipsis> 🤝 贡献指南 </span> <span class="md-nav__icon md-icon"></span> </label> <nav class=md-nav data-md-level=1 aria-labelledby=__nav_10_label aria-expanded=false> <label class=md-nav__title for=__nav_10> <span class="md-nav__icon md-icon"></span> 🤝 贡献指南 </label> <ul class=md-nav__list data-md-scrollfix> <li class=md-nav__item> <a href=../../CONTRIBUTING/ class=md-nav__link> <span class=md-ellipsis> 贡献指南 </span> </a> </li> <li class=md-nav__item> <a href=../../guides/DOCS_REORGANIZATION_PLAN/ class=md-nav__link> <span class=md-ellipsis> 文档重组计划 </span> </a> </li> </ul> </nav> </li> </ul> </nav> </div> </div> </div> <div class="md-sidebar md-sidebar--secondary" data-md-component=sidebar data-md-type=toc> <div class=md-sidebar__scrollwrap> <div class=md-sidebar__inner> <nav class="md-nav md-nav--secondary" aria-label=目录> <label class=md-nav__title for=__toc> <span class="md-nav__icon md-icon"></span> 目录 </label> <ul class=md-nav__list data-md-component=toc data-md-scrollfix> <li class=md-nav__item> <a href=#abstract class=md-nav__link> <span class=md-ellipsis> Abstract </span> </a> </li> <li class=md-nav__item> <a href=#status-of-this-memo class=md-nav__link> <span class=md-ellipsis> Status of This Memo </span> </a> </li> <li class=md-nav__item> <a href=#copyright-notice class=md-nav__link> <span class=md-ellipsis> Copyright Notice </span> </a> </li> <li class=md-nav__item> <a href=#table-of-contents class=md-nav__link> <span class=md-ellipsis> Table of Contents </span> </a> </li> <li class=md-nav__item> <a href=#1-introduction class=md-nav__link> <span class=md-ellipsis> 1. Introduction </span> </a> <nav class=md-nav aria-label="1. Introduction"> <ul class=md-nav__list> <li class=md-nav__item> <a href=#11-problem-statement class=md-nav__link> <span class=md-ellipsis> 1.1 Problem Statement </span> </a> </li> <li class=md-nav__item> <a href=#12-solution-overview class=md-nav__link> <span class=md-ellipsis> 1.2 Solution Overview </span> </a> </li> <li class=md-nav__item> <a href=#13-design-principles class=md-nav__link> <span class=md-ellipsis> 1.3 Design Principles </span> </a> </li> </ul> </nav> </li> <li class=md-nav__item> <a href=#2-conventions-and-definitions class=md-nav__link> <span class=md-ellipsis> 2. Conventions and Definitions </span> </a> <nav class=md-nav aria-label="2. Conventions and Definitions"> <ul class=md-nav__list> <li class=md-nav__item> <a href=#21-requirements-language class=md-nav__link> <span class=md-ellipsis> 2.1 Requirements Language </span> </a> </li> <li class=md-nav__item> <a href=#22-terminology class=md-nav__link> <span class=md-ellipsis> 2.2 Terminology </span> </a> </li> </ul> </nav> </li> <li class=md-nav__item> <a href=#3-protocol-overview class=md-nav__link> <span class=md-ellipsis> 3. Protocol Overview </span> </a> <nav class=md-nav aria-label="3. Protocol Overview"> <ul class=md-nav__list> <li class=md-nav__item> <a href=#31-fsrp-architecture class=md-nav__link> <span class=md-ellipsis> 3.1 FSRP Architecture </span> </a> </li> <li class=md-nav__item> <a href=#32-bagua-state-representation class=md-nav__link> <span class=md-ellipsis> 3.2 Bagua State Representation </span> </a> </li> <li class=md-nav__item> <a href=#33-network-topology class=md-nav__link> <span class=md-ellipsis> 3.3 Network Topology </span> </a> </li> </ul> </nav> </li> <li class=md-nav__item> <a href=#4-message-format class=md-nav__link> <span class=md-ellipsis> 4. Message Format </span> </a> <nav class=md-nav aria-label="4. Message Format"> <ul class=md-nav__list> <li class=md-nav__item> <a href=#41-fsrp-header-format class=md-nav__link> <span class=md-ellipsis> 4.1 FSRP Header Format </span> </a> </li> <li class=md-nav__item> <a href=#42-message-types class=md-nav__link> <span class=md-ellipsis> 4.2 Message Types </span> </a> </li> <li class=md-nav__item> <a href=#43-payload-formats class=md-nav__link> <span class=md-ellipsis> 4.3 Payload Formats </span> </a> <nav class=md-nav aria-label="4.3 Payload Formats"> <ul class=md-nav__list> <li class=md-nav__item> <a href=#431-fsrp_data-payload class=md-nav__link> <span class=md-ellipsis> 4.3.1 FSRP_DATA Payload </span> </a> </li> <li class=md-nav__item> <a href=#432-fsrp_consensus-payload class=md-nav__link> <span class=md-ellipsis> 4.3.2 FSRP_CONSENSUS Payload </span> </a> </li> </ul> </nav> </li> </ul> </nav> </li> <li class=md-nav__item> <a href=#5-routing-algorithm class=md-nav__link> <span class=md-ellipsis> 5. Routing Algorithm </span> </a> <nav class=md-nav aria-label="5. Routing Algorithm"> <ul class=md-nav__list> <li class=md-nav__item> <a href=#51-semantic-distance-calculation class=md-nav__link> <span class=md-ellipsis> 5.1 Semantic Distance Calculation </span> </a> </li> <li class=md-nav__item> <a href=#52-routing-table-structure class=md-nav__link> <span class=md-ellipsis> 5.2 Routing Table Structure </span> </a> </li> <li class=md-nav__item> <a href=#53-route-discovery-protocol class=md-nav__link> <span class=md-ellipsis> 5.3 Route Discovery Protocol </span> </a> </li> </ul> </nav> </li> <li class=md-nav__item> <a href=#6-consensus-mechanism class=md-nav__link> <span class=md-ellipsis> 6. Consensus Mechanism </span> </a> <nav class=md-nav aria-label="6. Consensus Mechanism"> <ul class=md-nav__list> <li class=md-nav__item> <a href=#61-bagua-byzantine-fault-tolerance-bbft class=md-nav__link> <span class=md-ellipsis> 6.1 Bagua Byzantine Fault Tolerance (BBFT) </span> </a> <nav class=md-nav aria-label="6.1 Bagua Byzantine Fault Tolerance (BBFT)"> <ul class=md-nav__list> <li class=md-nav__item> <a href=#611-consensus-phases class=md-nav__link> <span class=md-ellipsis> 6.1.1 Consensus Phases </span> </a> </li> <li class=md-nav__item> <a href=#612-consensus-message-flow class=md-nav__link> <span class=md-ellipsis> 6.1.2 Consensus Message Flow </span> </a> </li> </ul> </nav> </li> <li class=md-nav__item> <a href=#62-fault-tolerance class=md-nav__link> <span class=md-ellipsis> 6.2 Fault Tolerance </span> </a> </li> </ul> </nav> </li> <li class=md-nav__item> <a href=#7-agent-state-management class=md-nav__link> <span class=md-ellipsis> 7. Agent State Management </span> </a> <nav class=md-nav aria-label="7. Agent State Management"> <ul class=md-nav__list> <li class=md-nav__item> <a href=#71-agent-lifecycle class=md-nav__link> <span class=md-ellipsis> 7.1 Agent Lifecycle </span> </a> </li> <li class=md-nav__item> <a href=#72-state-synchronization class=md-nav__link> <span class=md-ellipsis> 7.2 State Synchronization </span> </a> </li> <li class=md-nav__item> <a href=#73-agent-registration class=md-nav__link> <span class=md-ellipsis> 7.3 Agent Registration </span> </a> </li> </ul> </nav> </li> <li class=md-nav__item> <a href=#8-security-considerations class=md-nav__link> <span class=md-ellipsis> 8. Security Considerations </span> </a> <nav class=md-nav aria-label="8. Security Considerations"> <ul class=md-nav__list> <li class=md-nav__item> <a href=#81-authentication class=md-nav__link> <span class=md-ellipsis> 8.1 Authentication </span> </a> </li> <li class=md-nav__item> <a href=#82-authorization class=md-nav__link> <span class=md-ellipsis> 8.2 Authorization </span> </a> </li> <li class=md-nav__item> <a href=#83-privacy class=md-nav__link> <span class=md-ellipsis> 8.3 Privacy </span> </a> </li> <li class=md-nav__item> <a href=#84-threat-model class=md-nav__link> <span class=md-ellipsis> 8.4 Threat Model </span> </a> </li> </ul> </nav> </li> <li class=md-nav__item> <a href=#9-iana-considerations class=md-nav__link> <span class=md-ellipsis> 9. IANA Considerations </span> </a> <nav class=md-nav aria-label="9. IANA Considerations"> <ul class=md-nav__list> <li class=md-nav__item> <a href=#91-port-assignments class=md-nav__link> <span class=md-ellipsis> 9.1 Port Assignments </span> </a> </li> <li class=md-nav__item> <a href=#92-protocol-numbers class=md-nav__link> <span class=md-ellipsis> 9.2 Protocol Numbers </span> </a> </li> <li class=md-nav__item> <a href=#93-message-type-registry class=md-nav__link> <span class=md-ellipsis> 9.3 Message Type Registry </span> </a> </li> </ul> </nav> </li> <li class=md-nav__item> <a href=#10-implementation-guidelines class=md-nav__link> <span class=md-ellipsis> 10. Implementation Guidelines </span> </a> <nav class=md-nav aria-label="10. Implementation Guidelines"> <ul class=md-nav__list> <li class=md-nav__item> <a href=#101-mandatory-features class=md-nav__link> <span class=md-ellipsis> 10.1 Mandatory Features </span> </a> </li> <li class=md-nav__item> <a href=#102-optional-features class=md-nav__link> <span class=md-ellipsis> 10.2 Optional Features </span> </a> </li> <li class=md-nav__item> <a href=#103-interoperability class=md-nav__link> <span class=md-ellipsis> 10.3 Interoperability </span> </a> </li> <li class=md-nav__item> <a href=#104-performance-considerations class=md-nav__link> <span class=md-ellipsis> 10.4 Performance Considerations </span> </a> </li> </ul> </nav> </li> <li class=md-nav__item> <a href=#11-references class=md-nav__link> <span class=md-ellipsis> 11. References </span> </a> <nav class=md-nav aria-label="11. References"> <ul class=md-nav__list> <li class=md-nav__item> <a href=#111-normative-references class=md-nav__link> <span class=md-ellipsis> 11.1 Normative References </span> </a> </li> <li class=md-nav__item> <a href=#112-informative-references class=md-nav__link> <span class=md-ellipsis> 11.2 Informative References </span> </a> </li> </ul> </nav> </li> <li class=md-nav__item> <a href=#12-appendix class=md-nav__link> <span class=md-ellipsis> 12. Appendix </span> </a> <nav class=md-nav aria-label="12. Appendix"> <ul class=md-nav__list> <li class=md-nav__item> <a href=#121-example-message-exchange class=md-nav__link> <span class=md-ellipsis> 12.1 Example Message Exchange </span> </a> </li> <li class=md-nav__item> <a href=#122-bagua-transformation-matrix class=md-nav__link> <span class=md-ellipsis> 12.2 Bagua Transformation Matrix </span> </a> </li> <li class=md-nav__item> <a href=#123-implementation-checklist class=md-nav__link> <span class=md-ellipsis> 12.3 Implementation Checklist </span> </a> </li> </ul> </nav> </li> </ul> </nav> </div> </div> </div> <div class=md-content data-md-component=content> <article class="md-content__inner md-typeset"> <h1 id=rfc-xxxx-financial-semantic-routing-protocol-fsrp>RFC XXXX: Financial Semantic Routing Protocol (FSRP)<a class=headerlink href=#rfc-xxxx-financial-semantic-routing-protocol-fsrp title="Permanent link">&para;</a></h1> <div class="language-text highlight"><pre><span></span><code><span id=__span-0-1><a id=__codelineno-0-1 name=__codelineno-0-1 href=#__codelineno-0-1></a>Network Working Group                                    J. Liao, Ed.
</span><span id=__span-0-2><a id=__codelineno-0-2 name=__codelineno-0-2 href=#__codelineno-0-2></a>Request for Comments: XXXX                          Jixia Academy
</span><span id=__span-0-3><a id=__codelineno-0-3 name=__codelineno-0-3 href=#__codelineno-0-3></a>Category: Standards Track                                July 2025
</span><span id=__span-0-4><a id=__codelineno-0-4 name=__codelineno-0-4 href=#__codelineno-0-4></a>Obsoletes: None                                    ISSN: 2070-1721
</span><span id=__span-0-5><a id=__codelineno-0-5 name=__codelineno-0-5 href=#__codelineno-0-5></a>
</span><span id=__span-0-6><a id=__codelineno-0-6 name=__codelineno-0-6 href=#__codelineno-0-6></a>                Financial Semantic Routing Protocol (FSRP)
</span></code></pre></div> <h2 id=abstract>Abstract<a class=headerlink href=#abstract title="Permanent link">&para;</a></h2> <p>This document defines the Financial Semantic Routing Protocol (FSRP), a novel application-layer protocol for distributed financial decision-making systems. FSRP enables semantic routing of financial information through multi-agent networks using ancient Chinese philosophical frameworks (Bagua) for state representation and consensus algorithms. The protocol addresses the lack of standardized communication mechanisms in modern AI-driven financial analysis systems.</p> <h2 id=status-of-this-memo>Status of This Memo<a class=headerlink href=#status-of-this-memo title="Permanent link">&para;</a></h2> <p>This Internet-Draft is submitted in full conformance with the provisions of BCP 78 and BCP 79.</p> <p>This document is a product of the Jixia Academy Financial Protocol Working Group. Information about the current status of this document, any errata, and how to provide feedback on it may be obtained at <a href=https://github.com/jixia-academy/fsrp-spec>https://github.com/jixia-academy/fsrp-spec</a>.</p> <h2 id=copyright-notice>Copyright Notice<a class=headerlink href=#copyright-notice title="Permanent link">&para;</a></h2> <p>Copyright &copy; 2025 IETF Trust and the persons identified as the document authors. All rights reserved.</p> <hr> <h2 id=table-of-contents>Table of Contents<a class=headerlink href=#table-of-contents title="Permanent link">&para;</a></h2> <ol> <li><a href=#1-introduction>Introduction</a></li> <li><a href=#2-conventions-and-definitions>Conventions and Definitions</a> </li> <li><a href=#3-protocol-overview>Protocol Overview</a></li> <li><a href=#4-message-format>Message Format</a></li> <li><a href=#5-routing-algorithm>Routing Algorithm</a></li> <li><a href=#6-consensus-mechanism>Consensus Mechanism</a></li> <li><a href=#7-agent-state-management>Agent State Management</a></li> <li><a href=#8-security-considerations>Security Considerations</a></li> <li><a href=#9-iana-considerations>IANA Considerations</a></li> <li><a href=#10-implementation-guidelines>Implementation Guidelines</a></li> <li><a href=#11-references>References</a></li> <li><a href=#12-appendix>Appendix</a></li> </ol> <hr> <h2 id=1-introduction>1. Introduction<a class=headerlink href=#1-introduction title="Permanent link">&para;</a></h2> <h3 id=11-problem-statement>1.1 Problem Statement<a class=headerlink href=#11-problem-statement title="Permanent link">&para;</a></h3> <p>Current financial decision-making systems utilizing artificial intelligence and multi-agent architectures suffer from several critical limitations:</p> <ol> <li><strong>Lack of Standardized Communication</strong>: No standardized protocol exists for inter-agent communication in financial analysis networks</li> <li><strong>Semantic Routing Deficiency</strong>: Existing routing protocols do not consider the semantic content of financial information</li> <li><strong>Consensus Mechanism Absence</strong>: No established consensus algorithms for distributed financial decision-making</li> <li><strong>Scalability Limitations</strong>: Current systems cannot efficiently scale across multiple analytical domains</li> </ol> <h3 id=12-solution-overview>1.2 Solution Overview<a class=headerlink href=#12-solution-overview title="Permanent link">&para;</a></h3> <p>FSRP addresses these limitations by providing:</p> <ul> <li><strong>Standardized Message Formats</strong>: Well-defined protocol headers and payload structures for financial semantic data</li> <li><strong>Content-Aware Routing</strong>: Routing algorithms that consider the semantic meaning of financial information</li> <li><strong>Distributed Consensus</strong>: Byzantine fault-tolerant consensus mechanisms adapted for financial decision-making</li> <li><strong>Multi-Domain Support</strong>: Extensible framework supporting multiple analytical domains (technical analysis, fundamental analysis, sentiment analysis, etc.)</li> </ul> <h3 id=13-design-principles>1.3 Design Principles<a class=headerlink href=#13-design-principles title="Permanent link">&para;</a></h3> <p>FSRP is designed according to the following principles:</p> <ul> <li><strong>Semantic Awareness</strong>: Routing decisions based on content semantics rather than just network topology</li> <li><strong>Cultural Integration</strong>: Incorporation of ancient Chinese philosophical frameworks (I-Ching/Bagua) for state representation</li> <li><strong>Fault Tolerance</strong>: Byzantine fault tolerance for consensus in adversarial financial environments</li> <li><strong>Extensibility</strong>: Modular design allowing integration of new analytical domains</li> <li><strong>Efficiency</strong>: Optimized for low-latency financial decision-making scenarios</li> </ul> <hr> <h2 id=2-conventions-and-definitions>2. Conventions and Definitions<a class=headerlink href=#2-conventions-and-definitions title="Permanent link">&para;</a></h2> <h3 id=21-requirements-language>2.1 Requirements Language<a class=headerlink href=#21-requirements-language title="Permanent link">&para;</a></h3> <p>The key words "MUST", "MUST NOT", "REQUIRED", "SHALL", "SHALL NOT", "SHOULD", "SHOULD NOT", "RECOMMENDED", "NOT RECOMMENDED", "MAY", and "OPTIONAL" in this document are to be interpreted as described in BCP 14 [RFC2119] [RFC8174] when, and only when, they appear in all capitals, as shown here.</p> <h3 id=22-terminology>2.2 Terminology<a class=headerlink href=#22-terminology title="Permanent link">&para;</a></h3> <p><strong>Agent</strong>: An autonomous software entity capable of financial analysis and decision-making</p> <p><strong>Bagua</strong>: Eight trigrams from I-Ching representing fundamental states of change and decision</p> <p><strong>Consensus Domain</strong>: A logical grouping of agents participating in a specific consensus process</p> <p><strong>Financial Semantic</strong>: The meaning and context of financial information beyond its literal content</p> <p><strong>Gua State</strong>: A 3-bit representation of an agent's current analytical stance using Bagua encoding</p> <p><strong>Routing Metric</strong>: A numerical value representing the cost or preference for routing to a specific destination</p> <p><strong>Wisdom Layer</strong>: The protocol layer responsible for meta-analysis and reflection on agent decisions</p> <hr> <h2 id=3-protocol-overview>3. Protocol Overview<a class=headerlink href=#3-protocol-overview title="Permanent link">&para;</a></h2> <h3 id=31-fsrp-architecture>3.1 FSRP Architecture<a class=headerlink href=#31-fsrp-architecture title="Permanent link">&para;</a></h3> <p>FSRP operates as a seven-layer protocol stack, mapping conceptually to the OSI model but optimized for financial semantic routing:</p> <div class="language-text highlight"><pre><span></span><code><span id=__span-1-1><a id=__codelineno-1-1 name=__codelineno-1-1 href=#__codelineno-1-1></a>   +-------------------+
</span><span id=__span-1-2><a id=__codelineno-1-2 name=__codelineno-1-2 href=#__codelineno-1-2></a>   |   Decision Layer  |  &lt;- L7: Final investment decisions (Yuanshi)
</span><span id=__span-1-3><a id=__codelineno-1-3 name=__codelineno-1-3 href=#__codelineno-1-3></a>   +-------------------+
</span><span id=__span-1-4><a id=__codelineno-1-4 name=__codelineno-1-4 href=#__codelineno-1-4></a>   |   Wisdom Layer    |  &lt;- L6: Meta-analysis and reflection (Sanqing)
</span><span id=__span-1-5><a id=__codelineno-1-5 name=__codelineno-1-5 href=#__codelineno-1-5></a>   +-------------------+
</span><span id=__span-1-6><a id=__codelineno-1-6 name=__codelineno-1-6 href=#__codelineno-1-6></a>   |   Session Layer   |  &lt;- L5: Agent session management (AutoGen+MCP)
</span><span id=__span-1-7><a id=__codelineno-1-7 name=__codelineno-1-7 href=#__codelineno-1-7></a>   +-------------------+
</span><span id=__span-1-8><a id=__codelineno-1-8 name=__codelineno-1-8 href=#__codelineno-1-8></a>   |   Transport Layer |  &lt;- L4: Data orchestration and flow control (N8N)
</span><span id=__span-1-9><a id=__codelineno-1-9 name=__codelineno-1-9 href=#__codelineno-1-9></a>   +-------------------+
</span><span id=__span-1-10><a id=__codelineno-1-10 name=__codelineno-1-10 href=#__codelineno-1-10></a>   |   Network Layer   |  &lt;- L3: Semantic routing (RSS aggregation)
</span><span id=__span-1-11><a id=__codelineno-1-11 name=__codelineno-1-11 href=#__codelineno-1-11></a>   +-------------------+
</span><span id=__span-1-12><a id=__codelineno-1-12 name=__codelineno-1-12 href=#__codelineno-1-12></a>   |   Data Link Layer |  &lt;- L2: Information framing (News processing)
</span><span id=__span-1-13><a id=__codelineno-1-13 name=__codelineno-1-13 href=#__codelineno-1-13></a>   +-------------------+
</span><span id=__span-1-14><a id=__codelineno-1-14 name=__codelineno-1-14 href=#__codelineno-1-14></a>   |   Physical Layer  |  &lt;- L1: Event capture (World events)
</span><span id=__span-1-15><a id=__codelineno-1-15 name=__codelineno-1-15 href=#__codelineno-1-15></a>   +-------------------+
</span></code></pre></div> <h3 id=32-bagua-state-representation>3.2 Bagua State Representation<a class=headerlink href=#32-bagua-state-representation title="Permanent link">&para;</a></h3> <p>FSRP uses 8-state Bagua encoding for semantic state representation. Each state represents a fundamental analytical stance:</p> <table> <thead> <tr> <th>Bagua Trigram</th> <th>Binary</th> <th>Decimal</th> <th>Semantic Meaning</th> <th>Financial Interpretation</th> </tr> </thead> <tbody> <tr> <td>Qian (乾)</td> <td>111</td> <td>7</td> <td>Creative Force</td> <td>Strong Bull Signal</td> </tr> <tr> <td>Dui (兑)</td> <td>110</td> <td>6</td> <td>Joyful Exchange</td> <td>Moderate Bull Signal</td> </tr> <tr> <td>Li (离)</td> <td>101</td> <td>5</td> <td>Clinging Fire</td> <td>Volatile Bull Signal</td> </tr> <tr> <td>Zhen (震)</td> <td>100</td> <td>4</td> <td>Arousing Thunder</td> <td>Emerging Bull Signal</td> </tr> <tr> <td>Xun (巽)</td> <td>011</td> <td>3</td> <td>Gentle Wind</td> <td>Emerging Bear Signal</td> </tr> <tr> <td>Kan (坎)</td> <td>010</td> <td>2</td> <td>Abysmal Water</td> <td>Volatile Bear Signal</td> </tr> <tr> <td>Gen (艮)</td> <td>001</td> <td>1</td> <td>Keeping Still</td> <td>Moderate Bear Signal</td> </tr> <tr> <td>Kun (坤)</td> <td>000</td> <td>0</td> <td>Receptive Earth</td> <td>Strong Bear Signal</td> </tr> </tbody> </table> <h3 id=33-network-topology>3.3 Network Topology<a class=headerlink href=#33-network-topology title="Permanent link">&para;</a></h3> <p>FSRP supports hierarchical network topologies with the following roles:</p> <ul> <li><strong>Leaf Agents</strong>: Individual analytical agents (e.g., Eight Immortals, Twelve Generals)</li> <li><strong>Border Routers</strong>: Domain aggregation points (e.g., Taishang Laojun)</li> <li><strong>Spine Routers</strong>: Inter-domain routing (e.g., Lingbao Daojun) </li> <li><strong>Root Controller</strong>: Global orchestration (e.g., Yuanshi Tianzun)</li> </ul> <hr> <h2 id=4-message-format>4. Message Format<a class=headerlink href=#4-message-format title="Permanent link">&para;</a></h2> <h3 id=41-fsrp-header-format>4.1 FSRP Header Format<a class=headerlink href=#41-fsrp-header-format title="Permanent link">&para;</a></h3> <p>All FSRP messages begin with a fixed 16-byte header:</p> <div class="language-text highlight"><pre><span></span><code><span id=__span-2-1><a id=__codelineno-2-1 name=__codelineno-2-1 href=#__codelineno-2-1></a>    0                   1                   2                   3
</span><span id=__span-2-2><a id=__codelineno-2-2 name=__codelineno-2-2 href=#__codelineno-2-2></a>    0 1 2 3 4 5 6 7 8 9 0 1 2 3 4 5 6 7 8 9 0 1 2 3 4 5 6 7 8 9 0 1
</span><span id=__span-2-3><a id=__codelineno-2-3 name=__codelineno-2-3 href=#__codelineno-2-3></a>   +-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+
</span><span id=__span-2-4><a id=__codelineno-2-4 name=__codelineno-2-4 href=#__codelineno-2-4></a>   |Version|  Type |   Source Gua  |  Target Gua   |   Confidence  |
</span><span id=__span-2-5><a id=__codelineno-2-5 name=__codelineno-2-5 href=#__codelineno-2-5></a>   +-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+
</span><span id=__span-2-6><a id=__codelineno-2-6 name=__codelineno-2-6 href=#__codelineno-2-6></a>   |                        Sequence Number                        |
</span><span id=__span-2-7><a id=__codelineno-2-7 name=__codelineno-2-7 href=#__codelineno-2-7></a>   +-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+
</span><span id=__span-2-8><a id=__codelineno-2-8 name=__codelineno-2-8 href=#__codelineno-2-8></a>   |                          Timestamp                            |
</span><span id=__span-2-9><a id=__codelineno-2-9 name=__codelineno-2-9 href=#__codelineno-2-9></a>   +-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+
</span><span id=__span-2-10><a id=__codelineno-2-10 name=__codelineno-2-10 href=#__codelineno-2-10></a>   |           Checksum            |            Reserved           |
</span><span id=__span-2-11><a id=__codelineno-2-11 name=__codelineno-2-11 href=#__codelineno-2-11></a>   +-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+
</span></code></pre></div> <p><strong>Field Descriptions:</strong></p> <ul> <li><strong>Version (4 bits)</strong>: FSRP version number (current version: 1)</li> <li><strong>Type (4 bits)</strong>: Message type (see Section 4.2)</li> <li><strong>Source Gua (3 bits)</strong>: Source agent's current Bagua state</li> <li><strong>Target Gua (3 bits)</strong>: Target agent's Bagua state or desired state</li> <li><strong>Confidence (6 bits)</strong>: Confidence level (0-63, where 63 = 100% confidence)</li> <li><strong>Sequence Number (32 bits)</strong>: Monotonically increasing sequence number</li> <li><strong>Timestamp (32 bits)</strong>: Unix timestamp of message creation</li> <li><strong>Checksum (16 bits)</strong>: Internet checksum of header and payload</li> <li><strong>Reserved (16 bits)</strong>: Reserved for future use, MUST be zero</li> </ul> <h3 id=42-message-types>4.2 Message Types<a class=headerlink href=#42-message-types title="Permanent link">&para;</a></h3> <p>FSRP defines the following message types:</p> <table> <thead> <tr> <th>Type</th> <th>Name</th> <th>Description</th> </tr> </thead> <tbody> <tr> <td>0</td> <td>FSRP_DATA</td> <td>Financial semantic data payload</td> </tr> <tr> <td>1</td> <td>FSRP_CONTROL</td> <td>Routing and control information</td> </tr> <tr> <td>2</td> <td>FSRP_CONSENSUS</td> <td>Consensus protocol messages</td> </tr> <tr> <td>3</td> <td>FSRP_HEARTBEAT</td> <td>Agent liveness and state updates</td> </tr> <tr> <td>4-15</td> <td>Reserved</td> <td>Reserved for future use</td> </tr> </tbody> </table> <h3 id=43-payload-formats>4.3 Payload Formats<a class=headerlink href=#43-payload-formats title="Permanent link">&para;</a></h3> <h4 id=431-fsrp_data-payload>4.3.1 FSRP_DATA Payload<a class=headerlink href=#431-fsrp_data-payload title="Permanent link">&para;</a></h4> <div class="language-json highlight"><pre><span></span><code><span id=__span-3-1><a id=__codelineno-3-1 name=__codelineno-3-1 href=#__codelineno-3-1></a><span class=p>{</span>
</span><span id=__span-3-2><a id=__codelineno-3-2 name=__codelineno-3-2 href=#__codelineno-3-2></a><span class=w>  </span><span class=nt>&quot;analysis_type&quot;</span><span class=p>:</span><span class=w> </span><span class=s2>&quot;technical|fundamental|sentiment|risk&quot;</span><span class=p>,</span>
</span><span id=__span-3-3><a id=__codelineno-3-3 name=__codelineno-3-3 href=#__codelineno-3-3></a><span class=w>  </span><span class=nt>&quot;symbol&quot;</span><span class=p>:</span><span class=w> </span><span class=s2>&quot;AAPL&quot;</span><span class=p>,</span>
</span><span id=__span-3-4><a id=__codelineno-3-4 name=__codelineno-3-4 href=#__codelineno-3-4></a><span class=w>  </span><span class=nt>&quot;recommendation&quot;</span><span class=p>:</span><span class=w> </span><span class=p>{</span>
</span><span id=__span-3-5><a id=__codelineno-3-5 name=__codelineno-3-5 href=#__codelineno-3-5></a><span class=w>    </span><span class=nt>&quot;action&quot;</span><span class=p>:</span><span class=w> </span><span class=s2>&quot;buy|sell|hold&quot;</span><span class=p>,</span>
</span><span id=__span-3-6><a id=__codelineno-3-6 name=__codelineno-3-6 href=#__codelineno-3-6></a><span class=w>    </span><span class=nt>&quot;confidence&quot;</span><span class=p>:</span><span class=w> </span><span class=mf>0.85</span><span class=p>,</span>
</span><span id=__span-3-7><a id=__codelineno-3-7 name=__codelineno-3-7 href=#__codelineno-3-7></a><span class=w>    </span><span class=nt>&quot;reasoning&quot;</span><span class=p>:</span><span class=w> </span><span class=s2>&quot;Technical breakout above resistance&quot;</span><span class=p>,</span>
</span><span id=__span-3-8><a id=__codelineno-3-8 name=__codelineno-3-8 href=#__codelineno-3-8></a><span class=w>    </span><span class=nt>&quot;time_horizon&quot;</span><span class=p>:</span><span class=w> </span><span class=s2>&quot;short|medium|long&quot;</span>
</span><span id=__span-3-9><a id=__codelineno-3-9 name=__codelineno-3-9 href=#__codelineno-3-9></a><span class=w>  </span><span class=p>},</span>
</span><span id=__span-3-10><a id=__codelineno-3-10 name=__codelineno-3-10 href=#__codelineno-3-10></a><span class=w>  </span><span class=nt>&quot;supporting_data&quot;</span><span class=p>:</span><span class=w> </span><span class=p>{</span>
</span><span id=__span-3-11><a id=__codelineno-3-11 name=__codelineno-3-11 href=#__codelineno-3-11></a><span class=w>    </span><span class=nt>&quot;price&quot;</span><span class=p>:</span><span class=w> </span><span class=mf>175.43</span><span class=p>,</span>
</span><span id=__span-3-12><a id=__codelineno-3-12 name=__codelineno-3-12 href=#__codelineno-3-12></a><span class=w>    </span><span class=nt>&quot;volume&quot;</span><span class=p>:</span><span class=w> </span><span class=mi>45234567</span><span class=p>,</span>
</span><span id=__span-3-13><a id=__codelineno-3-13 name=__codelineno-3-13 href=#__codelineno-3-13></a><span class=w>    </span><span class=nt>&quot;indicators&quot;</span><span class=p>:</span><span class=w> </span><span class=p>{</span><span class=err>...</span><span class=p>}</span>
</span><span id=__span-3-14><a id=__codelineno-3-14 name=__codelineno-3-14 href=#__codelineno-3-14></a><span class=w>  </span><span class=p>},</span>
</span><span id=__span-3-15><a id=__codelineno-3-15 name=__codelineno-3-15 href=#__codelineno-3-15></a><span class=w>  </span><span class=nt>&quot;metadata&quot;</span><span class=p>:</span><span class=w> </span><span class=p>{</span>
</span><span id=__span-3-16><a id=__codelineno-3-16 name=__codelineno-3-16 href=#__codelineno-3-16></a><span class=w>    </span><span class=nt>&quot;agent_id&quot;</span><span class=p>:</span><span class=w> </span><span class=s2>&quot;ludongbin_001&quot;</span><span class=p>,</span>
</span><span id=__span-3-17><a id=__codelineno-3-17 name=__codelineno-3-17 href=#__codelineno-3-17></a><span class=w>    </span><span class=nt>&quot;domain&quot;</span><span class=p>:</span><span class=w> </span><span class=s2>&quot;jixia_academy&quot;</span><span class=p>,</span>
</span><span id=__span-3-18><a id=__codelineno-3-18 name=__codelineno-3-18 href=#__codelineno-3-18></a><span class=w>    </span><span class=nt>&quot;timestamp&quot;</span><span class=p>:</span><span class=w> </span><span class=mi>1720598400</span>
</span><span id=__span-3-19><a id=__codelineno-3-19 name=__codelineno-3-19 href=#__codelineno-3-19></a><span class=w>  </span><span class=p>}</span>
</span><span id=__span-3-20><a id=__codelineno-3-20 name=__codelineno-3-20 href=#__codelineno-3-20></a><span class=p>}</span>
</span></code></pre></div> <h4 id=432-fsrp_consensus-payload>4.3.2 FSRP_CONSENSUS Payload<a class=headerlink href=#432-fsrp_consensus-payload title="Permanent link">&para;</a></h4> <div class="language-json highlight"><pre><span></span><code><span id=__span-4-1><a id=__codelineno-4-1 name=__codelineno-4-1 href=#__codelineno-4-1></a><span class=p>{</span>
</span><span id=__span-4-2><a id=__codelineno-4-2 name=__codelineno-4-2 href=#__codelineno-4-2></a><span class=w>  </span><span class=nt>&quot;phase&quot;</span><span class=p>:</span><span class=w> </span><span class=s2>&quot;propose|prepare|commit|finalize&quot;</span><span class=p>,</span>
</span><span id=__span-4-3><a id=__codelineno-4-3 name=__codelineno-4-3 href=#__codelineno-4-3></a><span class=w>  </span><span class=nt>&quot;proposal_id&quot;</span><span class=p>:</span><span class=w> </span><span class=s2>&quot;uuid-string&quot;</span><span class=p>,</span>
</span><span id=__span-4-4><a id=__codelineno-4-4 name=__codelineno-4-4 href=#__codelineno-4-4></a><span class=w>  </span><span class=nt>&quot;decision&quot;</span><span class=p>:</span><span class=w> </span><span class=p>{</span>
</span><span id=__span-4-5><a id=__codelineno-4-5 name=__codelineno-4-5 href=#__codelineno-4-5></a><span class=w>    </span><span class=nt>&quot;symbol&quot;</span><span class=p>:</span><span class=w> </span><span class=s2>&quot;AAPL&quot;</span><span class=p>,</span><span class=w> </span>
</span><span id=__span-4-6><a id=__codelineno-4-6 name=__codelineno-4-6 href=#__codelineno-4-6></a><span class=w>    </span><span class=nt>&quot;action&quot;</span><span class=p>:</span><span class=w> </span><span class=s2>&quot;buy|sell&quot;</span><span class=p>,</span>
</span><span id=__span-4-7><a id=__codelineno-4-7 name=__codelineno-4-7 href=#__codelineno-4-7></a><span class=w>    </span><span class=nt>&quot;confidence&quot;</span><span class=p>:</span><span class=w> </span><span class=mf>0.78</span><span class=p>,</span>
</span><span id=__span-4-8><a id=__codelineno-4-8 name=__codelineno-4-8 href=#__codelineno-4-8></a><span class=w>    </span><span class=nt>&quot;rationale&quot;</span><span class=p>:</span><span class=w> </span><span class=s2>&quot;Consensus reached across 6/8 agents&quot;</span>
</span><span id=__span-4-9><a id=__codelineno-4-9 name=__codelineno-4-9 href=#__codelineno-4-9></a><span class=w>  </span><span class=p>},</span>
</span><span id=__span-4-10><a id=__codelineno-4-10 name=__codelineno-4-10 href=#__codelineno-4-10></a><span class=w>  </span><span class=nt>&quot;votes&quot;</span><span class=p>:</span><span class=w> </span><span class=p>[</span>
</span><span id=__span-4-11><a id=__codelineno-4-11 name=__codelineno-4-11 href=#__codelineno-4-11></a><span class=w>    </span><span class=p>{</span>
</span><span id=__span-4-12><a id=__codelineno-4-12 name=__codelineno-4-12 href=#__codelineno-4-12></a><span class=w>      </span><span class=nt>&quot;agent_id&quot;</span><span class=p>:</span><span class=w> </span><span class=s2>&quot;ludongbin_001&quot;</span><span class=p>,</span>
</span><span id=__span-4-13><a id=__codelineno-4-13 name=__codelineno-4-13 href=#__codelineno-4-13></a><span class=w>      </span><span class=nt>&quot;vote&quot;</span><span class=p>:</span><span class=w> </span><span class=s2>&quot;approve|reject&quot;</span><span class=p>,</span>
</span><span id=__span-4-14><a id=__codelineno-4-14 name=__codelineno-4-14 href=#__codelineno-4-14></a><span class=w>      </span><span class=nt>&quot;gua_state&quot;</span><span class=p>:</span><span class=w> </span><span class=mi>7</span><span class=p>,</span>
</span><span id=__span-4-15><a id=__codelineno-4-15 name=__codelineno-4-15 href=#__codelineno-4-15></a><span class=w>      </span><span class=nt>&quot;signature&quot;</span><span class=p>:</span><span class=w> </span><span class=s2>&quot;digital_signature&quot;</span>
</span><span id=__span-4-16><a id=__codelineno-4-16 name=__codelineno-4-16 href=#__codelineno-4-16></a><span class=w>    </span><span class=p>}</span>
</span><span id=__span-4-17><a id=__codelineno-4-17 name=__codelineno-4-17 href=#__codelineno-4-17></a><span class=w>  </span><span class=p>]</span>
</span><span id=__span-4-18><a id=__codelineno-4-18 name=__codelineno-4-18 href=#__codelineno-4-18></a><span class=p>}</span>
</span></code></pre></div> <hr> <h2 id=5-routing-algorithm>5. Routing Algorithm<a class=headerlink href=#5-routing-algorithm title="Permanent link">&para;</a></h2> <h3 id=51-semantic-distance-calculation>5.1 Semantic Distance Calculation<a class=headerlink href=#51-semantic-distance-calculation title="Permanent link">&para;</a></h3> <p>FSRP routing decisions are based on semantic distance between Bagua states, calculated using the following algorithm:</p> <div class="language-python highlight"><pre><span></span><code><span id=__span-5-1><a id=__codelineno-5-1 name=__codelineno-5-1 href=#__codelineno-5-1></a><span class=k>def</span><span class=w> </span><span class=nf>calculate_semantic_distance</span><span class=p>(</span><span class=n>source_gua</span><span class=p>,</span> <span class=n>target_gua</span><span class=p>):</span>
</span><span id=__span-5-2><a id=__codelineno-5-2 name=__codelineno-5-2 href=#__codelineno-5-2></a><span class=w>    </span><span class=sd>&quot;&quot;&quot;</span>
</span><span id=__span-5-3><a id=__codelineno-5-3 name=__codelineno-5-3 href=#__codelineno-5-3></a><span class=sd>    Calculate semantic distance between Bagua states</span>
</span><span id=__span-5-4><a id=__codelineno-5-4 name=__codelineno-5-4 href=#__codelineno-5-4></a><span class=sd>    Based on I-Ching transformation principles</span>
</span><span id=__span-5-5><a id=__codelineno-5-5 name=__codelineno-5-5 href=#__codelineno-5-5></a><span class=sd>    &quot;&quot;&quot;</span>
</span><span id=__span-5-6><a id=__codelineno-5-6 name=__codelineno-5-6 href=#__codelineno-5-6></a>    <span class=c1># XOR operation to find differing bits</span>
</span><span id=__span-5-7><a id=__codelineno-5-7 name=__codelineno-5-7 href=#__codelineno-5-7></a>    <span class=n>diff</span> <span class=o>=</span> <span class=n>source_gua</span> <span class=o>^</span> <span class=n>target_gua</span>
</span><span id=__span-5-8><a id=__codelineno-5-8 name=__codelineno-5-8 href=#__codelineno-5-8></a>
</span><span id=__span-5-9><a id=__codelineno-5-9 name=__codelineno-5-9 href=#__codelineno-5-9></a>    <span class=c1># Count number of different bits (Hamming distance)</span>
</span><span id=__span-5-10><a id=__codelineno-5-10 name=__codelineno-5-10 href=#__codelineno-5-10></a>    <span class=n>hamming_distance</span> <span class=o>=</span> <span class=nb>bin</span><span class=p>(</span><span class=n>diff</span><span class=p>)</span><span class=o>.</span><span class=n>count</span><span class=p>(</span><span class=s1>&#39;1&#39;</span><span class=p>)</span>
</span><span id=__span-5-11><a id=__codelineno-5-11 name=__codelineno-5-11 href=#__codelineno-5-11></a>
</span><span id=__span-5-12><a id=__codelineno-5-12 name=__codelineno-5-12 href=#__codelineno-5-12></a>    <span class=c1># Apply I-Ching transformation weights</span>
</span><span id=__span-5-13><a id=__codelineno-5-13 name=__codelineno-5-13 href=#__codelineno-5-13></a>    <span class=n>transformation_weights</span> <span class=o>=</span> <span class=p>{</span>
</span><span id=__span-5-14><a id=__codelineno-5-14 name=__codelineno-5-14 href=#__codelineno-5-14></a>        <span class=mi>0</span><span class=p>:</span> <span class=mf>0.0</span><span class=p>,</span>  <span class=c1># Same state</span>
</span><span id=__span-5-15><a id=__codelineno-5-15 name=__codelineno-5-15 href=#__codelineno-5-15></a>        <span class=mi>1</span><span class=p>:</span> <span class=mf>1.0</span><span class=p>,</span>  <span class=c1># Single line change</span>
</span><span id=__span-5-16><a id=__codelineno-5-16 name=__codelineno-5-16 href=#__codelineno-5-16></a>        <span class=mi>2</span><span class=p>:</span> <span class=mf>1.5</span><span class=p>,</span>  <span class=c1># Two line change  </span>
</span><span id=__span-5-17><a id=__codelineno-5-17 name=__codelineno-5-17 href=#__codelineno-5-17></a>        <span class=mi>3</span><span class=p>:</span> <span class=mf>2.0</span>   <span class=c1># Complete transformation</span>
</span><span id=__span-5-18><a id=__codelineno-5-18 name=__codelineno-5-18 href=#__codelineno-5-18></a>    <span class=p>}</span>
</span><span id=__span-5-19><a id=__codelineno-5-19 name=__codelineno-5-19 href=#__codelineno-5-19></a>
</span><span id=__span-5-20><a id=__codelineno-5-20 name=__codelineno-5-20 href=#__codelineno-5-20></a>    <span class=k>return</span> <span class=n>transformation_weights</span><span class=o>.</span><span class=n>get</span><span class=p>(</span><span class=n>hamming_distance</span><span class=p>,</span> <span class=mf>3.0</span><span class=p>)</span>
</span></code></pre></div> <h3 id=52-routing-table-structure>5.2 Routing Table Structure<a class=headerlink href=#52-routing-table-structure title="Permanent link">&para;</a></h3> <p>Each FSRP agent maintains a routing table with the following structure:</p> <table> <thead> <tr> <th>Destination Gua</th> <th>Next Hop Agent</th> <th>Metric</th> <th>Interface</th> <th>Age</th> <th>Flags</th> </tr> </thead> <tbody> <tr> <td>000 (Kun)</td> <td>hexiangu_001</td> <td>1.0</td> <td>eth0</td> <td>30s</td> <td>U</td> </tr> <tr> <td>001 (Gen)</td> <td>tieguaili_001</td> <td>1.5</td> <td>eth1</td> <td>45s</td> <td>U</td> </tr> <tr> <td>010 (Kan)</td> <td>ludongbin_001</td> <td>2.0</td> <td>eth0</td> <td>60s</td> <td>U</td> </tr> </tbody> </table> <p><strong>Field Descriptions:</strong> - <strong>Destination Gua</strong>: Target Bagua state - <strong>Next Hop Agent</strong>: Next agent in routing path - <strong>Metric</strong>: Routing cost (lower is better) - <strong>Interface</strong>: Network interface identifier - <strong>Age</strong>: Time since last update - <strong>Flags</strong>: U=Up, D=Down, S=Static</p> <h3 id=53-route-discovery-protocol>5.3 Route Discovery Protocol<a class=headerlink href=#53-route-discovery-protocol title="Permanent link">&para;</a></h3> <p>FSRP uses a proactive routing approach with periodic updates:</p> <ol> <li><strong>Route Advertisement</strong>: Agents periodically broadcast their reachable Gua states</li> <li><strong>Distance Vector</strong>: Each agent maintains distance vectors to all known Gua states</li> <li><strong>Loop Prevention</strong>: Split horizon with poison reverse to prevent routing loops</li> <li><strong>Convergence</strong>: Triggered updates for rapid convergence after topology changes</li> </ol> <hr> <h2 id=6-consensus-mechanism>6. Consensus Mechanism<a class=headerlink href=#6-consensus-mechanism title="Permanent link">&para;</a></h2> <h3 id=61-bagua-byzantine-fault-tolerance-bbft>6.1 Bagua Byzantine Fault Tolerance (BBFT)<a class=headerlink href=#61-bagua-byzantine-fault-tolerance-bbft title="Permanent link">&para;</a></h3> <p>FSRP implements a modified Byzantine Fault Tolerance algorithm adapted for financial decision-making:</p> <h4 id=611-consensus-phases>6.1.1 Consensus Phases<a class=headerlink href=#611-consensus-phases title="Permanent link">&para;</a></h4> <p><strong>Phase 1: Proposal</strong> - Root Controller (Yuanshi) initiates consensus with investment proposal - Proposal includes symbol, action, confidence threshold, and deadline</p> <p><strong>Phase 2: Prepare</strong> - All participating agents analyze proposal using their domain expertise - Agents broadcast PREPARE messages with their Gua state and preliminary vote</p> <p><strong>Phase 3: Commit</strong> - If &gt;&#8532; of agents reach compatible Gua states, proceed to commit phase - Agents broadcast COMMIT messages with final votes and digital signatures</p> <p><strong>Phase 4: Finalize</strong> - Root Controller aggregates votes and announces final decision - Decision is propagated to all agents and external systems</p> <h4 id=612-consensus-message-flow>6.1.2 Consensus Message Flow<a class=headerlink href=#612-consensus-message-flow title="Permanent link">&para;</a></h4> <div class="language-text highlight"><pre><span></span><code><span id=__span-6-1><a id=__codelineno-6-1 name=__codelineno-6-1 href=#__codelineno-6-1></a>Yuanshi (Root)     Sanqing (Processors)     Agents (Participants)
</span><span id=__span-6-2><a id=__codelineno-6-2 name=__codelineno-6-2 href=#__codelineno-6-2></a>     |                     |                        |
</span><span id=__span-6-3><a id=__codelineno-6-3 name=__codelineno-6-3 href=#__codelineno-6-3></a>     |--- PROPOSE --------&gt;|                        |
</span><span id=__span-6-4><a id=__codelineno-6-4 name=__codelineno-6-4 href=#__codelineno-6-4></a>     |                     |--- PREPARE -----------&gt;|
</span><span id=__span-6-5><a id=__codelineno-6-5 name=__codelineno-6-5 href=#__codelineno-6-5></a>     |                     |&lt;-- PREPARE_ACK --------|
</span><span id=__span-6-6><a id=__codelineno-6-6 name=__codelineno-6-6 href=#__codelineno-6-6></a>     |&lt;-- PREPARE_RESULT --|                        |
</span><span id=__span-6-7><a id=__codelineno-6-7 name=__codelineno-6-7 href=#__codelineno-6-7></a>     |--- COMMIT ---------&gt;|                        |
</span><span id=__span-6-8><a id=__codelineno-6-8 name=__codelineno-6-8 href=#__codelineno-6-8></a>     |                     |--- COMMIT ------------&gt;|
</span><span id=__span-6-9><a id=__codelineno-6-9 name=__codelineno-6-9 href=#__codelineno-6-9></a>     |                     |&lt;-- COMMIT_ACK ---------|
</span><span id=__span-6-10><a id=__codelineno-6-10 name=__codelineno-6-10 href=#__codelineno-6-10></a>     |&lt;-- COMMIT_RESULT ---|                        |
</span><span id=__span-6-11><a id=__codelineno-6-11 name=__codelineno-6-11 href=#__codelineno-6-11></a>     |--- FINALIZE -------&gt;|--- FINALIZE ----------&gt;|
</span></code></pre></div> <h3 id=62-fault-tolerance>6.2 Fault Tolerance<a class=headerlink href=#62-fault-tolerance title="Permanent link">&para;</a></h3> <p>FSRP consensus can tolerate up to f Byzantine failures where f &lt; n/3 (n = total agents).</p> <p><strong>Failure Detection:</strong> - Heartbeat messages every 30 seconds - Timeout detection after 90 seconds - Automatic exclusion of failed agents from consensus</p> <p><strong>Recovery Mechanisms:</strong> - View change protocol for leader failures - State synchronization for recovering agents - Checkpoint and rollback for consistency</p> <hr> <h2 id=7-agent-state-management>7. Agent State Management<a class=headerlink href=#7-agent-state-management title="Permanent link">&para;</a></h2> <h3 id=71-agent-lifecycle>7.1 Agent Lifecycle<a class=headerlink href=#71-agent-lifecycle title="Permanent link">&para;</a></h3> <p>FSRP agents follow a defined lifecycle:</p> <ol> <li><strong>Initialization</strong>: Agent starts and announces capabilities</li> <li><strong>Discovery</strong>: Agent discovers network topology and peers</li> <li><strong>Active</strong>: Agent participates in routing and consensus</li> <li><strong>Maintenance</strong>: Periodic state updates and health checks</li> <li><strong>Shutdown</strong>: Graceful departure with state cleanup</li> </ol> <h3 id=72-state-synchronization>7.2 State Synchronization<a class=headerlink href=#72-state-synchronization title="Permanent link">&para;</a></h3> <p>Agents maintain synchronized state through:</p> <ul> <li><strong>Periodic Updates</strong>: Broadcast current Gua state every 60 seconds</li> <li><strong>Triggered Updates</strong>: Immediate broadcast on significant state changes</li> <li><strong>State Queries</strong>: On-demand state requests between agents</li> <li><strong>Conflict Resolution</strong>: Timestamp-based conflict resolution</li> </ul> <h3 id=73-agent-registration>7.3 Agent Registration<a class=headerlink href=#73-agent-registration title="Permanent link">&para;</a></h3> <p>New agents join the network through the following process:</p> <div class="language-json highlight"><pre><span></span><code><span id=__span-7-1><a id=__codelineno-7-1 name=__codelineno-7-1 href=#__codelineno-7-1></a><span class=p>{</span>
</span><span id=__span-7-2><a id=__codelineno-7-2 name=__codelineno-7-2 href=#__codelineno-7-2></a><span class=w>  </span><span class=nt>&quot;message_type&quot;</span><span class=p>:</span><span class=w> </span><span class=s2>&quot;AGENT_REGISTER&quot;</span><span class=p>,</span>
</span><span id=__span-7-3><a id=__codelineno-7-3 name=__codelineno-7-3 href=#__codelineno-7-3></a><span class=w>  </span><span class=nt>&quot;agent_info&quot;</span><span class=p>:</span><span class=w> </span><span class=p>{</span>
</span><span id=__span-7-4><a id=__codelineno-7-4 name=__codelineno-7-4 href=#__codelineno-7-4></a><span class=w>    </span><span class=nt>&quot;agent_id&quot;</span><span class=p>:</span><span class=w> </span><span class=s2>&quot;unique_identifier&quot;</span><span class=p>,</span>
</span><span id=__span-7-5><a id=__codelineno-7-5 name=__codelineno-7-5 href=#__codelineno-7-5></a><span class=w>    </span><span class=nt>&quot;agent_type&quot;</span><span class=p>:</span><span class=w> </span><span class=s2>&quot;technical|fundamental|sentiment|risk&quot;</span><span class=p>,</span>
</span><span id=__span-7-6><a id=__codelineno-7-6 name=__codelineno-7-6 href=#__codelineno-7-6></a><span class=w>    </span><span class=nt>&quot;capabilities&quot;</span><span class=p>:</span><span class=w> </span><span class=p>[</span><span class=s2>&quot;stock_analysis&quot;</span><span class=p>,</span><span class=w> </span><span class=s2>&quot;options_analysis&quot;</span><span class=p>],</span>
</span><span id=__span-7-7><a id=__codelineno-7-7 name=__codelineno-7-7 href=#__codelineno-7-7></a><span class=w>    </span><span class=nt>&quot;domain&quot;</span><span class=p>:</span><span class=w> </span><span class=s2>&quot;jixia_academy&quot;</span><span class=p>,</span>
</span><span id=__span-7-8><a id=__codelineno-7-8 name=__codelineno-7-8 href=#__codelineno-7-8></a><span class=w>    </span><span class=nt>&quot;version&quot;</span><span class=p>:</span><span class=w> </span><span class=s2>&quot;1.0.0&quot;</span>
</span><span id=__span-7-9><a id=__codelineno-7-9 name=__codelineno-7-9 href=#__codelineno-7-9></a><span class=w>  </span><span class=p>},</span>
</span><span id=__span-7-10><a id=__codelineno-7-10 name=__codelineno-7-10 href=#__codelineno-7-10></a><span class=w>  </span><span class=nt>&quot;initial_gua_state&quot;</span><span class=p>:</span><span class=w> </span><span class=mi>4</span><span class=p>,</span>
</span><span id=__span-7-11><a id=__codelineno-7-11 name=__codelineno-7-11 href=#__codelineno-7-11></a><span class=w>  </span><span class=nt>&quot;public_key&quot;</span><span class=p>:</span><span class=w> </span><span class=s2>&quot;agent_public_key&quot;</span>
</span><span id=__span-7-12><a id=__codelineno-7-12 name=__codelineno-7-12 href=#__codelineno-7-12></a><span class=p>}</span>
</span></code></pre></div> <hr> <h2 id=8-security-considerations>8. Security Considerations<a class=headerlink href=#8-security-considerations title="Permanent link">&para;</a></h2> <h3 id=81-authentication>8.1 Authentication<a class=headerlink href=#81-authentication title="Permanent link">&para;</a></h3> <p>FSRP requires strong authentication mechanisms:</p> <ul> <li><strong>Digital Signatures</strong>: All consensus messages MUST be digitally signed</li> <li><strong>Public Key Infrastructure</strong>: Agents MUST have valid certificates</li> <li><strong>Message Integrity</strong>: Checksums MUST be verified for all messages</li> <li><strong>Replay Protection</strong>: Sequence numbers MUST be monotonically increasing</li> </ul> <h3 id=82-authorization>8.2 Authorization<a class=headerlink href=#82-authorization title="Permanent link">&para;</a></h3> <p>Access control is enforced through:</p> <ul> <li><strong>Role-Based Access</strong>: Agents have defined roles (leaf, border, spine, root)</li> <li><strong>Domain Isolation</strong>: Agents can only access their authorized domains</li> <li><strong>Capability Restrictions</strong>: Agents limited to their declared capabilities</li> </ul> <h3 id=83-privacy>8.3 Privacy<a class=headerlink href=#83-privacy title="Permanent link">&para;</a></h3> <p>Financial data privacy is protected through:</p> <ul> <li><strong>Payload Encryption</strong>: Optional AES-256 encryption for sensitive data</li> <li><strong>Agent Anonymization</strong>: Optional anonymization of agent identities</li> <li><strong>Audit Trails</strong>: Comprehensive logging of all financial decisions</li> </ul> <h3 id=84-threat-model>8.4 Threat Model<a class=headerlink href=#84-threat-model title="Permanent link">&para;</a></h3> <p>FSRP is designed to resist:</p> <ul> <li><strong>Byzantine Agents</strong>: Malicious agents providing false information</li> <li><strong>Network Attacks</strong>: Man-in-the-middle, replay, and DoS attacks </li> <li><strong>Data Manipulation</strong>: Unauthorized modification of financial data</li> <li><strong>Consensus Disruption</strong>: Attempts to prevent consensus formation</li> </ul> <hr> <h2 id=9-iana-considerations>9. IANA Considerations<a class=headerlink href=#9-iana-considerations title="Permanent link">&para;</a></h2> <h3 id=91-port-assignments>9.1 Port Assignments<a class=headerlink href=#91-port-assignments title="Permanent link">&para;</a></h3> <p>FSRP requires the following port assignments:</p> <ul> <li><strong>TCP Port 8888</strong>: Reliable message delivery and consensus</li> <li><strong>UDP Port 8889</strong>: Real-time market data and heartbeats</li> <li><strong>Multicast Address **********</strong>: Consensus broadcast messages</li> </ul> <h3 id=92-protocol-numbers>9.2 Protocol Numbers<a class=headerlink href=#92-protocol-numbers title="Permanent link">&para;</a></h3> <p>FSRP requests assignment of: - <strong>IP Protocol Number</strong>: For direct IP encapsulation - <strong>Ethernet Type</strong>: For Layer 2 implementations</p> <h3 id=93-message-type-registry>9.3 Message Type Registry<a class=headerlink href=#93-message-type-registry title="Permanent link">&para;</a></h3> <p>IANA should maintain a registry of FSRP message types with the following initial assignments:</p> <table> <thead> <tr> <th>Type</th> <th>Name</th> <th>Reference</th> </tr> </thead> <tbody> <tr> <td>0</td> <td>FSRP_DATA</td> <td>This document</td> </tr> <tr> <td>1</td> <td>FSRP_CONTROL</td> <td>This document</td> </tr> <tr> <td>2</td> <td>FSRP_CONSENSUS</td> <td>This document</td> </tr> <tr> <td>3</td> <td>FSRP_HEARTBEAT</td> <td>This document</td> </tr> <tr> <td>4-15</td> <td>Reserved</td> <td>This document</td> </tr> </tbody> </table> <hr> <h2 id=10-implementation-guidelines>10. Implementation Guidelines<a class=headerlink href=#10-implementation-guidelines title="Permanent link">&para;</a></h2> <h3 id=101-mandatory-features>10.1 Mandatory Features<a class=headerlink href=#101-mandatory-features title="Permanent link">&para;</a></h3> <p>Implementations MUST support:</p> <ul> <li>All 8 Bagua state representations</li> <li>BBFT consensus algorithm</li> <li>Message authentication and integrity checking</li> <li>Routing table maintenance</li> <li>Agent lifecycle management</li> </ul> <h3 id=102-optional-features>10.2 Optional Features<a class=headerlink href=#102-optional-features title="Permanent link">&para;</a></h3> <p>Implementations MAY support:</p> <ul> <li>Payload encryption for privacy</li> <li>Message compression for efficiency</li> <li>Quality of Service (QoS) mechanisms</li> <li>Load balancing across multiple paths</li> <li>Advanced analytics and monitoring</li> </ul> <h3 id=103-interoperability>10.3 Interoperability<a class=headerlink href=#103-interoperability title="Permanent link">&para;</a></h3> <p>To ensure interoperability:</p> <ul> <li>Implementations MUST follow the exact message formats specified</li> <li>Implementations MUST handle unknown message types gracefully</li> <li>Implementations SHOULD provide configuration options for timeouts</li> <li>Implementations SHOULD support protocol version negotiation</li> </ul> <h3 id=104-performance-considerations>10.4 Performance Considerations<a class=headerlink href=#104-performance-considerations title="Permanent link">&para;</a></h3> <p>For optimal performance:</p> <ul> <li>Routing table updates SHOULD be rate-limited</li> <li>Consensus timeouts SHOULD be configurable</li> <li>Message queuing SHOULD be implemented for high-throughput scenarios</li> <li>Network topology SHOULD be optimized for low latency</li> </ul> <hr> <h2 id=11-references>11. References<a class=headerlink href=#11-references title="Permanent link">&para;</a></h2> <h3 id=111-normative-references>11.1 Normative References<a class=headerlink href=#111-normative-references title="Permanent link">&para;</a></h3> <p><strong>[RFC2119]</strong> Bradner, S., "Key words for use in RFCs to Indicate Requirement Levels", BCP 14, RFC 2119, DOI 10.17487/RFC2119, March 1997.</p> <p><strong>[RFC8174]</strong> Leiba, B., "Ambiguity of Uppercase vs Lowercase in RFC 2119 Key Words", BCP 14, RFC 8174, DOI 10.17487/RFC8174, May 2017.</p> <p><strong>[RFC5234]</strong> Crocker, D., Ed., and P. Overell, "Augmented BNF for Syntax Specifications: ABNF", STD 68, RFC 5234, DOI 10.17487/RFC5234, January 2008.</p> <h3 id=112-informative-references>11.2 Informative References<a class=headerlink href=#112-informative-references title="Permanent link">&para;</a></h3> <p><strong>[YIJING]</strong> "I Ching: Book of Changes", Ancient Chinese text, circa 1000 BCE.</p> <p><strong>[OSPF]</strong> Moy, J., "OSPF Version 2", STD 54, RFC 2328, DOI 10.17487/RFC2328, April 1998.</p> <p><strong>[BGP]</strong> Rekhter, Y., Ed., Li, T., Ed., and S. Hares, Ed., "A Border Gateway Protocol 4 (BGP-4)", RFC 4271, DOI 10.17487/RFC4271, January 2006.</p> <p><strong>[PBFT]</strong> Castro, M. and B. Liskov, "Practical Byzantine Fault Tolerance", OSDI '99, February 1999.</p> <hr> <h2 id=12-appendix>12. Appendix<a class=headerlink href=#12-appendix title="Permanent link">&para;</a></h2> <h3 id=121-example-message-exchange>12.1 Example Message Exchange<a class=headerlink href=#121-example-message-exchange title="Permanent link">&para;</a></h3> <div class="language-text highlight"><pre><span></span><code><span id=__span-8-1><a id=__codelineno-8-1 name=__codelineno-8-1 href=#__codelineno-8-1></a>Agent A (Gua State: 111) -&gt; Agent B (Gua State: 000)
</span><span id=__span-8-2><a id=__codelineno-8-2 name=__codelineno-8-2 href=#__codelineno-8-2></a>
</span><span id=__span-8-3><a id=__codelineno-8-3 name=__codelineno-8-3 href=#__codelineno-8-3></a>FSRP Header:
</span><span id=__span-8-4><a id=__codelineno-8-4 name=__codelineno-8-4 href=#__codelineno-8-4></a>Version: 1, Type: 0 (DATA), Source Gua: 111, Target Gua: 000
</span><span id=__span-8-5><a id=__codelineno-8-5 name=__codelineno-8-5 href=#__codelineno-8-5></a>Confidence: 45, Sequence: 12345, Timestamp: 1720598400
</span><span id=__span-8-6><a id=__codelineno-8-6 name=__codelineno-8-6 href=#__codelineno-8-6></a>Checksum: 0xABCD, Reserved: 0x0000
</span><span id=__span-8-7><a id=__codelineno-8-7 name=__codelineno-8-7 href=#__codelineno-8-7></a>
</span><span id=__span-8-8><a id=__codelineno-8-8 name=__codelineno-8-8 href=#__codelineno-8-8></a>Payload:
</span><span id=__span-8-9><a id=__codelineno-8-9 name=__codelineno-8-9 href=#__codelineno-8-9></a>{
</span><span id=__span-8-10><a id=__codelineno-8-10 name=__codelineno-8-10 href=#__codelineno-8-10></a>  &quot;analysis_type&quot;: &quot;technical&quot;,
</span><span id=__span-8-11><a id=__codelineno-8-11 name=__codelineno-8-11 href=#__codelineno-8-11></a>  &quot;symbol&quot;: &quot;AAPL&quot;,
</span><span id=__span-8-12><a id=__codelineno-8-12 name=__codelineno-8-12 href=#__codelineno-8-12></a>  &quot;recommendation&quot;: {
</span><span id=__span-8-13><a id=__codelineno-8-13 name=__codelineno-8-13 href=#__codelineno-8-13></a>    &quot;action&quot;: &quot;buy&quot;,
</span><span id=__span-8-14><a id=__codelineno-8-14 name=__codelineno-8-14 href=#__codelineno-8-14></a>    &quot;confidence&quot;: 0.85,
</span><span id=__span-8-15><a id=__codelineno-8-15 name=__codelineno-8-15 href=#__codelineno-8-15></a>    &quot;reasoning&quot;: &quot;Bullish breakout pattern confirmed&quot;
</span><span id=__span-8-16><a id=__codelineno-8-16 name=__codelineno-8-16 href=#__codelineno-8-16></a>  }
</span><span id=__span-8-17><a id=__codelineno-8-17 name=__codelineno-8-17 href=#__codelineno-8-17></a>}
</span></code></pre></div> <h3 id=122-bagua-transformation-matrix>12.2 Bagua Transformation Matrix<a class=headerlink href=#122-bagua-transformation-matrix title="Permanent link">&para;</a></h3> <div class="language-text highlight"><pre><span></span><code><span id=__span-9-1><a id=__codelineno-9-1 name=__codelineno-9-1 href=#__codelineno-9-1></a>From\To  000  001  010  011  100  101  110  111
</span><span id=__span-9-2><a id=__codelineno-9-2 name=__codelineno-9-2 href=#__codelineno-9-2></a>000      0.0  1.0  1.5  2.0  1.5  2.0  1.0  3.0
</span><span id=__span-9-3><a id=__codelineno-9-3 name=__codelineno-9-3 href=#__codelineno-9-3></a>001      1.0  0.0  1.0  1.5  2.0  1.5  2.0  1.0
</span><span id=__span-9-4><a id=__codelineno-9-4 name=__codelineno-9-4 href=#__codelineno-9-4></a>010      1.5  1.0  0.0  1.0  1.0  1.5  1.0  2.0
</span><span id=__span-9-5><a id=__codelineno-9-5 name=__codelineno-9-5 href=#__codelineno-9-5></a>011      2.0  1.5  1.0  0.0  1.0  1.0  1.5  1.0
</span><span id=__span-9-6><a id=__codelineno-9-6 name=__codelineno-9-6 href=#__codelineno-9-6></a>100      1.5  2.0  1.0  1.0  0.0  1.0  1.5  1.0
</span><span id=__span-9-7><a id=__codelineno-9-7 name=__codelineno-9-7 href=#__codelineno-9-7></a>101      2.0  1.5  1.5  1.0  1.0  0.0  1.0  1.0
</span><span id=__span-9-8><a id=__codelineno-9-8 name=__codelineno-9-8 href=#__codelineno-9-8></a>110      1.0  2.0  1.0  1.5  1.5  1.0  0.0  1.0
</span><span id=__span-9-9><a id=__codelineno-9-9 name=__codelineno-9-9 href=#__codelineno-9-9></a>111      3.0  1.0  2.0  1.0  1.0  1.0  1.0  0.0
</span></code></pre></div> <h3 id=123-implementation-checklist>12.3 Implementation Checklist<a class=headerlink href=#123-implementation-checklist title="Permanent link">&para;</a></h3> <ul class=task-list> <li class=task-list-item><label class=task-list-control><input type=checkbox disabled><span class=task-list-indicator></span></label> FSRP header parsing and generation</li> <li class=task-list-item><label class=task-list-control><input type=checkbox disabled><span class=task-list-indicator></span></label> Bagua state management</li> <li class=task-list-item><label class=task-list-control><input type=checkbox disabled><span class=task-list-indicator></span></label> Routing table implementation</li> <li class=task-list-item><label class=task-list-control><input type=checkbox disabled><span class=task-list-indicator></span></label> Consensus protocol implementation</li> <li class=task-list-item><label class=task-list-control><input type=checkbox disabled><span class=task-list-indicator></span></label> Security mechanisms (authentication, integrity)</li> <li class=task-list-item><label class=task-list-control><input type=checkbox disabled><span class=task-list-indicator></span></label> Agent lifecycle management</li> <li class=task-list-item><label class=task-list-control><input type=checkbox disabled><span class=task-list-indicator></span></label> Error handling and recovery</li> <li class=task-list-item><label class=task-list-control><input type=checkbox disabled><span class=task-list-indicator></span></label> Performance optimization</li> <li class=task-list-item><label class=task-list-control><input type=checkbox disabled><span class=task-list-indicator></span></label> Interoperability testing</li> <li class=task-list-item><label class=task-list-control><input type=checkbox disabled><span class=task-list-indicator></span></label> Documentation and examples</li> </ul> <hr> <p><strong>Authors' Addresses</strong></p> <p>J. Liao (Editor)<br> Jixia Academy<br> Email: <a href=mailto:<EMAIL>>&#108;&#105;&#97;&#111;&#64;&#106;&#105;&#120;&#105;&#97;&#46;&#97;&#99;&#97;&#100;&#101;&#109;&#121;</a></p> <hr> <p><em>This document expires January 10, 2026</em></p> <aside class=md-source-file> <span class=md-source-file__fact> <span class=md-icon title=最后更新> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M21 13.1c-.1 0-.3.1-.4.2l-1 1 2.1 2.1 1-1c.2-.2.2-.6 0-.8l-1.3-1.3c-.1-.1-.2-.2-.4-.2m-1.9 1.8-6.1 6V23h2.1l6.1-6.1zM12.5 7v5.2l4 2.4-1 1L11 13V7zM11 21.9c-5.1-.5-9-4.8-9-9.9C2 6.5 6.5 2 12 2c5.3 0 9.6 4.1 10 9.3-.3-.1-.6-.2-1-.2s-.7.1-1 .2C19.6 7.2 16.2 4 12 4c-4.4 0-8 3.6-8 8 0 4.1 3.1 7.5 7.1 7.9l-.1.2z"/></svg> </span> <span class="git-revision-date-localized-plugin git-revision-date-localized-plugin-datetime" title="2025年7月13日 03:54:04 UTC">2025年7月13日 03:54:04</span> </span> <span class=md-source-file__fact> <span class=md-icon title=创建日期> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M14.47 15.08 11 13V7h1.5v5.25l3.08 1.83c-.41.28-.79.62-1.11 1m-1.39 4.84c-.36.05-.71.08-1.08.08-4.42 0-8-3.58-8-8s3.58-8 8-8 8 3.58 8 8c0 .37-.03.72-.08 1.08.69.1 1.33.32 1.92.64.1-.56.16-1.13.16-1.72 0-5.5-4.5-10-10-10S2 6.5 2 12s4.47 10 10 10c.59 0 1.16-.06 1.72-.16-.32-.59-.54-1.23-.64-1.92M18 15v3h-3v2h3v3h2v-3h3v-2h-3v-3z"/></svg> </span> <span class="git-revision-date-localized-plugin git-revision-date-localized-plugin-datetime" title="2025年7月13日 03:54:04 UTC">2025年7月13日 03:54:04</span> </span> </aside> <form class=md-feedback name=feedback hidden> <fieldset> <legend class=md-feedback__title> 这个页面有帮助吗？ </legend> <div class=md-feedback__inner> <div class=md-feedback__list> <button class="md-feedback__icon md-icon" type=submit title=有帮助 data-md-value=1> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M20 12a8 8 0 0 0-8-8 8 8 0 0 0-8 8 8 8 0 0 0 8 8 8 8 0 0 0 8-8m2 0a10 10 0 0 1-10 10A10 10 0 0 1 2 12 10 10 0 0 1 12 2a10 10 0 0 1 10 10M10 9.5c0 .8-.7 1.5-1.5 1.5S7 10.3 7 9.5 7.7 8 8.5 8s1.5.7 1.5 1.5m7 0c0 .8-.7 1.5-1.5 1.5S14 10.3 14 9.5 14.7 8 15.5 8s1.5.7 1.5 1.5m-5 7.73c-1.75 0-3.29-.73-4.19-1.81L9.23 14c.45.72 1.52 1.23 2.77 1.23s2.32-.51 2.77-1.23l1.42 1.42c-.9 1.08-2.44 1.81-4.19 1.81"/></svg> </button> <button class="md-feedback__icon md-icon" type=submit title=需要改进 data-md-value=0> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M20 12a8 8 0 0 0-8-8 8 8 0 0 0-8 8 8 8 0 0 0 8 8 8 8 0 0 0 8-8m2 0a10 10 0 0 1-10 10A10 10 0 0 1 2 12 10 10 0 0 1 12 2a10 10 0 0 1 10 10m-6.5-4c.8 0 1.5.7 1.5 1.5s-.7 1.5-1.5 1.5-1.5-.7-1.5-1.5.7-1.5 1.5-1.5M10 9.5c0 .8-.7 1.5-1.5 1.5S7 10.3 7 9.5 7.7 8 8.5 8s1.5.7 1.5 1.5m2 4.5c1.75 0 3.29.72 4.19 1.81l-1.42 1.42C14.32 16.5 13.25 16 12 16s-2.32.5-2.77 1.23l-1.42-1.42C8.71 14.72 10.25 14 12 14"/></svg> </button> </div> <div class=md-feedback__note> <div data-md-value=1 hidden> 感谢您的反馈！ </div> <div data-md-value=0 hidden> 感谢您的反馈！请通过GitHub Issues告诉我们如何改进。 </div> </div> </div> </fieldset> </form> </article> </div> <script>var tabs=__md_get("__tabs");if(Array.isArray(tabs))e:for(var set of document.querySelectorAll(".tabbed-set")){var labels=set.querySelector(".tabbed-labels");for(var tab of tabs)for(var label of labels.getElementsByTagName("label"))if(label.innerText.trim()===tab){var input=document.getElementById(label.htmlFor);input.checked=!0;continue e}}</script> <script>var target=document.getElementById(location.hash.slice(1));target&&target.name&&(target.checked=target.name.startsWith("__tabbed_"))</script> </div> <button type=button class="md-top md-icon" data-md-component=top hidden> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M13 20h-2V8l-5.5 5.5-1.42-1.42L12 4.16l7.92 7.92-1.42 1.42L13 8z"/></svg> 回到页面顶部 </button> </main> <footer class=md-footer> <div class="md-footer-meta md-typeset"> <div class="md-footer-meta__inner md-grid"> <div class=md-copyright> <div class=md-copyright__highlight> Copyright &copy; 2025 炼妖壶项目 - MIT License </div> Made with <a href=https://squidfunk.github.io/mkdocs-material/ target=_blank rel=noopener> Material for MkDocs </a> </div> <div class=md-social> <a href=https://github.com/your-username/jixia-academy target=_blank rel=noopener title=GitHub仓库 class=md-social__link> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 496 512"><!-- Font Awesome Free 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license/free (Icons: CC BY 4.0, Fonts: SIL OFL 1.1, Code: MIT License) Copyright 2024 Fonticons, Inc.--><path d="M165.9 397.4c0 2-2.3 3.6-5.2 3.6-3.3.3-5.6-1.3-5.6-3.6 0-2 2.3-3.6 5.2-3.6 3-.3 5.6 1.3 5.6 3.6m-31.1-4.5c-.7 2 1.3 4.3 4.3 4.9 2.6 1 5.6 0 6.2-2s-1.3-4.3-4.3-5.2c-2.6-.7-5.5.3-6.2 2.3m44.2-1.7c-2.9.7-4.9 2.6-4.6 4.9.3 2 2.9 3.3 5.9 2.6 2.9-.7 4.9-2.6 4.6-4.6-.3-1.9-3-3.2-5.9-2.9M244.8 8C106.1 8 0 113.3 0 252c0 110.9 69.8 205.8 169.5 239.2 12.8 2.3 17.3-5.6 17.3-12.1 0-6.2-.3-40.4-.3-61.4 0 0-70 15-84.7-29.8 0 0-11.4-29.1-27.8-36.6 0 0-22.9-15.7 1.6-15.4 0 0 24.9 2 38.6 25.8 21.9 38.6 58.6 27.5 72.9 20.9 2.3-16 8.8-27.1 16-33.7-55.9-6.2-112.3-14.3-112.3-110.5 0-27.5 7.6-41.3 23.6-58.9-2.6-6.5-11.1-33.3 2.6-67.9 20.9-6.5 69 27 69 27 20-5.6 41.5-8.5 62.8-8.5s42.8 2.9 62.8 8.5c0 0 48.1-33.6 69-27 13.7 34.7 5.2 61.4 2.6 67.9 16 17.7 25.8 31.5 25.8 58.9 0 96.5-58.9 104.2-114.8 110.5 9.2 7.9 17 22.9 17 46.4 0 33.7-.3 75.4-.3 83.6 0 6.5 4.6 14.4 17.3 12.1C428.2 457.8 496 362.9 496 252 496 113.3 383.5 8 244.8 8M97.2 352.9c-1.3 1-1 3.3.7 5.2 1.6 1.6 3.9 2.3 5.2 1 1.3-1 1-3.3-.7-5.2-1.6-1.6-3.9-2.3-5.2-1m-10.8-8.1c-.7 1.3.3 2.9 2.3 3.9 1.6 1 3.6.7 4.3-.7.7-1.3-.3-2.9-2.3-3.9-2-.6-3.6-.3-4.3.7m32.4 35.6c-1.6 1.3-1 4.3 1.3 6.2 2.3 2.3 5.2 2.6 6.5 1 1.3-1.3.7-4.3-1.3-6.2-2.2-2.3-5.2-2.6-6.5-1m-11.4-14.7c-1.6 1-1.6 3.6 0 5.9s4.3 3.3 5.6 2.3c1.6-1.3 1.6-3.9 0-6.2-1.4-2.3-4-3.3-5.6-2"/></svg> </a> <a href=https://your-mastodon-instance.com/@taishang_laojun target=_blank rel="noopener me" title=太上老君 class=md-social__link> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 448 512"><!-- Font Awesome Free 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license/free (Icons: CC BY 4.0, Fonts: SIL OFL 1.1, Code: MIT License) Copyright 2024 Fonticons, Inc.--><path d="M433 179.11c0-97.2-63.71-125.7-63.71-125.7-62.52-28.7-228.56-28.4-290.48 0 0 0-63.72 28.5-63.72 125.7 0 115.7-6.6 259.4 105.63 289.1 40.51 10.7 75.32 13 103.33 11.4 50.81-2.8 79.32-18.1 79.32-18.1l-1.7-36.9s-36.31 11.4-77.12 10.1c-40.41-1.4-83-4.4-89.63-54a102.5 102.5 0 0 1-.9-13.9c85.63 20.9 158.65 9.1 178.75 6.7 56.12-6.7 105-41.3 111.23-72.9 9.8-49.8 9-121.5 9-121.5m-75.12 125.2h-46.63v-114.2c0-49.7-64-51.6-64 6.9v62.5h-46.33V197c0-58.5-64-56.6-64-6.9v114.2H90.19c0-122.1-5.2-147.9 18.41-175 25.9-28.9 79.82-30.8 103.83 6.1l11.6 19.5 11.6-19.5c24.11-37.1 78.12-34.8 103.83-6.1 23.71 27.3 18.4 53 18.4 175z"/></svg> </a> <a href=https://twitter.com/jixia_academy target=_blank rel=noopener title=Twitter class=md-social__link> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 512 512"><!-- Font Awesome Free 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license/free (Icons: CC BY 4.0, Fonts: SIL OFL 1.1, Code: MIT License) Copyright 2024 Fonticons, Inc.--><path d="M459.37 151.716c.325 4.548.325 9.097.325 13.645 0 138.72-105.583 298.558-298.558 298.558-59.452 0-114.68-17.219-161.137-47.106 8.447.974 16.568 1.299 25.34 1.299 49.055 0 94.213-16.568 130.274-44.832-46.132-.975-84.792-31.188-98.112-72.772 6.498.974 12.995 1.624 19.818 1.624 9.421 0 18.843-1.3 27.614-3.573-48.081-9.747-84.143-51.98-84.143-102.985v-1.299c13.969 7.797 30.214 12.67 47.431 13.319-28.264-18.843-46.781-51.005-46.781-87.391 0-19.492 5.197-37.36 14.294-52.954 51.655 63.675 129.3 105.258 216.365 109.807-1.624-7.797-2.599-15.918-2.599-24.04 0-57.828 46.782-104.934 104.934-104.934 30.213 0 57.502 12.67 76.67 33.137 23.715-4.548 46.456-13.32 66.599-25.34-7.798 24.366-24.366 44.833-46.132 57.827 21.117-2.273 41.584-8.122 60.426-16.243-14.292 20.791-32.161 39.308-52.628 54.253"/></svg> </a> <a href=mailto:<EMAIL> target=_blank rel=noopener title=联系我们 class=md-social__link> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 512 512"><!-- Font Awesome Free 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license/free (Icons: CC BY 4.0, Fonts: SIL OFL 1.1, Code: MIT License) Copyright 2024 Fonticons, Inc.--><path d="M498.1 5.6c10.1 7 15.4 19.1 13.5 31.2l-64 416c-1.5 9.7-7.4 18.2-16 23s-18.9 5.4-28 1.6L284 427.7l-68.5 74.1c-8.9 9.7-22.9 12.9-35.2 8.1S160 493.2 160 480v-83.6c0-4 1.5-7.8 4.2-10.8l167.6-182.8c5.8-6.3 5.6-16-.4-22s-15.7-6.4-22-.7L106 360.8l-88.3-44.2C7.1 311.3.3 300.7 0 288.9s5.9-22.8 16.1-28.7l448-256c10.7-6.1 23.9-5.5 34 1.4"/></svg> </a> </div> </div> </div> </footer> </div> <div class=md-dialog data-md-component=dialog> <div class="md-dialog__inner md-typeset"></div> </div> <script id=__config type=application/json>{"base": "../..", "features": ["navigation.instant", "navigation.tracking", "navigation.tabs", "navigation.tabs.sticky", "navigation.sections", "navigation.expand", "navigation.indexes", "navigation.top", "search.highlight", "search.share", "search.suggest", "content.code.annotate", "content.code.copy", "content.tabs.link"], "search": "../../assets/javascripts/workers/search.d50fe291.min.js", "tags": null, "translations": {"clipboard.copied": "\u5df2\u590d\u5236", "clipboard.copy": "\u590d\u5236", "search.result.more.one": "\u5728\u8be5\u9875\u4e0a\u8fd8\u6709 1 \u4e2a\u7b26\u5408\u6761\u4ef6\u7684\u7ed3\u679c", "search.result.more.other": "\u5728\u8be5\u9875\u4e0a\u8fd8\u6709 # \u4e2a\u7b26\u5408\u6761\u4ef6\u7684\u7ed3\u679c", "search.result.none": "\u6ca1\u6709\u627e\u5230\u7b26\u5408\u6761\u4ef6\u7684\u7ed3\u679c", "search.result.one": "\u627e\u5230 1 \u4e2a\u7b26\u5408\u6761\u4ef6\u7684\u7ed3\u679c", "search.result.other": "# \u4e2a\u7b26\u5408\u6761\u4ef6\u7684\u7ed3\u679c", "search.result.placeholder": "\u952e\u5165\u4ee5\u5f00\u59cb\u641c\u7d22", "search.result.term.missing": "\u7f3a\u5c11", "select.version": "\u9009\u62e9\u5f53\u524d\u7248\u672c"}, "version": {"default": "latest", "provider": "mike"}}</script> <script src=../../assets/javascripts/bundle.56ea9cef.min.js></script> <script src=../../assets/javascripts/mathjax.js></script> <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script> <script src=https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js></script> </body> </html>