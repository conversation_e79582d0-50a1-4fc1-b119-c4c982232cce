<!doctype html><html lang=zh class=no-js> <head><meta charset=utf-8><meta name=viewport content="width=device-width,initial-scale=1"><meta name=description content="AI驱动的投资决策系统 - 稷下学宫智能体辩论平台"><meta name=author content=炼妖壶开发团队><link href=https://jingminzhang.github.io/cauldron/explanation/project/manifesto/ rel=canonical><link rel=icon href=../../../assets/images/favicon.png><meta name=generator content="mkdocs-1.6.1, mkdocs-material-9.6.15"><title>🗡️ 数字复仇宣言 - 炼妖壶 (Cauldron)</title><link rel=stylesheet href=../../../assets/stylesheets/main.342714a4.min.css><link rel=stylesheet href=../../../assets/stylesheets/palette.06af60db.min.css><link rel=preconnect href=https://fonts.gstatic.com crossorigin><link rel=stylesheet href="https://fonts.googleapis.com/css?family=Noto+Sans+SC:300,300i,400,400i,700,700i%7CJetBrains+Mono:400,400i,700,700i&display=fallback"><style>:root{--md-text-font:"Noto Sans SC";--md-code-font:"JetBrains Mono"}</style><link rel=stylesheet href=../../../assets/stylesheets/extra.css><script>__md_scope=new URL("../../..",location),__md_hash=e=>[...e].reduce(((e,_)=>(e<<5)-e+_.charCodeAt(0)),0),__md_get=(e,_=localStorage,t=__md_scope)=>JSON.parse(_.getItem(t.pathname+"."+e)),__md_set=(e,_,t=localStorage,a=__md_scope)=>{try{t.setItem(a.pathname+"."+e,JSON.stringify(_))}catch(e){}}</script><script id=__analytics>function __md_analytics(){function e(){dataLayer.push(arguments)}window.dataLayer=window.dataLayer||[],e("js",new Date),e("config","G-XXXXXXXXXX"),document.addEventListener("DOMContentLoaded",(function(){document.forms.search&&document.forms.search.query.addEventListener("blur",(function(){this.value&&e("event","search",{search_term:this.value})}));document$.subscribe((function(){var t=document.forms.feedback;if(void 0!==t)for(var a of t.querySelectorAll("[type=submit]"))a.addEventListener("click",(function(a){a.preventDefault();var n=document.location.pathname,d=this.getAttribute("data-md-value");e("event","feedback",{page:n,data:d}),t.firstElementChild.disabled=!0;var r=t.querySelector(".md-feedback__note [data-md-value='"+d+"']");r&&(r.hidden=!1)})),t.hidden=!1})),location$.subscribe((function(t){e("config","G-XXXXXXXXXX",{page_path:t.pathname})}))}));var t=document.createElement("script");t.async=!0,t.src="https://www.googletagmanager.com/gtag/js?id=G-XXXXXXXXXX",document.getElementById("__analytics").insertAdjacentElement("afterEnd",t)}</script><script>"undefined"!=typeof __md_analytics&&__md_analytics()</script></head> <body dir=ltr data-md-color-scheme=default data-md-color-primary=indigo data-md-color-accent=amber> <input class=md-toggle data-md-toggle=drawer type=checkbox id=__drawer autocomplete=off> <input class=md-toggle data-md-toggle=search type=checkbox id=__search autocomplete=off> <label class=md-overlay for=__drawer></label> <div data-md-component=skip> <a href=#_1 class=md-skip> 跳转至 </a> </div> <div data-md-component=announce> </div> <div data-md-color-scheme=default data-md-component=outdated hidden> </div> <header class="md-header md-header--shadow md-header--lifted" data-md-component=header> <nav class="md-header__inner md-grid" aria-label=页眉> <a href=../../.. title="炼妖壶 (Cauldron)" class="md-header__button md-logo" aria-label="炼妖壶 (Cauldron)" data-md-component=logo> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M12 3 1 9l11 6 9-4.91V17h2V9M5 13.18v4L12 21l7-3.82v-4L12 17z"/></svg> </a> <label class="md-header__button md-icon" for=__drawer> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M3 6h18v2H3zm0 5h18v2H3zm0 5h18v2H3z"/></svg> </label> <div class=md-header__title data-md-component=header-title> <div class=md-header__ellipsis> <div class=md-header__topic> <span class=md-ellipsis> 炼妖壶 (Cauldron) </span> </div> <div class=md-header__topic data-md-component=header-topic> <span class=md-ellipsis> 🗡️ 数字复仇宣言 </span> </div> </div> </div> <form class=md-header__option data-md-component=palette> <input class=md-option data-md-color-media="(prefers-color-scheme: light)" data-md-color-scheme=default data-md-color-primary=indigo data-md-color-accent=amber aria-label=切换到暗黑模式 type=radio name=__palette id=__palette_0> <label class="md-header__button md-icon" title=切换到暗黑模式 for=__palette_1 hidden> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M12 8a4 4 0 0 0-4 4 4 4 0 0 0 4 4 4 4 0 0 0 4-4 4 4 0 0 0-4-4m0 10a6 6 0 0 1-6-6 6 6 0 0 1 6-6 6 6 0 0 1 6 6 6 6 0 0 1-6 6m8-9.31V4h-4.69L12 .69 8.69 4H4v4.69L.69 12 4 15.31V20h4.69L12 23.31 15.31 20H20v-4.69L23.31 12z"/></svg> </label> <input class=md-option data-md-color-media="(prefers-color-scheme: dark)" data-md-color-scheme=slate data-md-color-primary=indigo data-md-color-accent=amber aria-label=切换到明亮模式 type=radio name=__palette id=__palette_1> <label class="md-header__button md-icon" title=切换到明亮模式 for=__palette_0 hidden> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M12 18c-.89 0-1.74-.2-2.5-.55C11.56 16.5 13 14.42 13 12s-1.44-4.5-3.5-5.45C10.26 6.2 11.11 6 12 6a6 6 0 0 1 6 6 6 6 0 0 1-6 6m8-9.31V4h-4.69L12 .69 8.69 4H4v4.69L.69 12 4 15.31V20h4.69L12 23.31 15.31 20H20v-4.69L23.31 12z"/></svg> </label> </form> <script>var palette=__md_get("__palette");if(palette&&palette.color){if("(prefers-color-scheme)"===palette.color.media){var media=matchMedia("(prefers-color-scheme: light)"),input=document.querySelector(media.matches?"[data-md-color-media='(prefers-color-scheme: light)']":"[data-md-color-media='(prefers-color-scheme: dark)']");palette.color.media=input.getAttribute("data-md-color-media"),palette.color.scheme=input.getAttribute("data-md-color-scheme"),palette.color.primary=input.getAttribute("data-md-color-primary"),palette.color.accent=input.getAttribute("data-md-color-accent")}for(var[key,value]of Object.entries(palette.color))document.body.setAttribute("data-md-color-"+key,value)}</script> <label class="md-header__button md-icon" for=__search> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M9.5 3A6.5 6.5 0 0 1 16 9.5c0 1.61-.59 3.09-1.56 4.23l.27.27h.79l5 5-1.5 1.5-5-5v-.79l-.27-.27A6.52 6.52 0 0 1 9.5 16 6.5 6.5 0 0 1 3 9.5 6.5 6.5 0 0 1 9.5 3m0 2C7 5 5 7 5 9.5S7 14 9.5 14 14 12 14 9.5 12 5 9.5 5"/></svg> </label> <div class=md-search data-md-component=search role=dialog> <label class=md-search__overlay for=__search></label> <div class=md-search__inner role=search> <form class=md-search__form name=search> <input type=text class=md-search__input name=query aria-label=搜索 placeholder=搜索 autocapitalize=off autocorrect=off autocomplete=off spellcheck=false data-md-component=search-query required> <label class="md-search__icon md-icon" for=__search> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M9.5 3A6.5 6.5 0 0 1 16 9.5c0 1.61-.59 3.09-1.56 4.23l.27.27h.79l5 5-1.5 1.5-5-5v-.79l-.27-.27A6.52 6.52 0 0 1 9.5 16 6.5 6.5 0 0 1 3 9.5 6.5 6.5 0 0 1 9.5 3m0 2C7 5 5 7 5 9.5S7 14 9.5 14 14 12 14 9.5 12 5 9.5 5"/></svg> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M20 11v2H8l5.5 5.5-1.42 1.42L4.16 12l7.92-7.92L13.5 5.5 8 11z"/></svg> </label> <nav class=md-search__options aria-label=查找> <a href=javascript:void(0) class="md-search__icon md-icon" title=分享 aria-label=分享 data-clipboard data-clipboard-text data-md-component=search-share tabindex=-1> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M18 16.08c-.76 0-1.44.3-1.96.77L8.91 12.7c.05-.23.09-.46.09-.7s-.04-.47-.09-.7l7.05-4.11c.54.5 1.25.81 2.04.81a3 3 0 0 0 3-3 3 3 0 0 0-3-3 3 3 0 0 0-3 3c0 .***********.7L8.04 9.81C7.5 9.31 6.79 9 6 9a3 3 0 0 0-3 3 3 3 0 0 0 3 3c.79 0 1.5-.31 2.04-.81l7.12 4.15c-.05.21-.08.43-.08.66 0 1.61 1.31 2.91 2.92 2.91s2.92-1.3 2.92-2.91A2.92 2.92 0 0 0 18 16.08"/></svg> </a> <button type=reset class="md-search__icon md-icon" title=清空当前内容 aria-label=清空当前内容 tabindex=-1> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M19 6.41 17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/></svg> </button> </nav> <div class=md-search__suggest data-md-component=search-suggest></div> </form> <div class=md-search__output> <div class=md-search__scrollwrap tabindex=0 data-md-scrollfix> <div class=md-search-result data-md-component=search-result> <div class=md-search-result__meta> 正在初始化搜索引擎 </div> <ol class=md-search-result__list role=presentation></ol> </div> </div> </div> </div> </div> <div class=md-header__source> <a href=https://github.com/jingminzhang/cauldron title=前往仓库 class=md-source data-md-component=source> <div class="md-source__icon md-icon"> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 496 512"><!-- Font Awesome Free 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license/free (Icons: CC BY 4.0, Fonts: SIL OFL 1.1, Code: MIT License) Copyright 2024 Fonticons, Inc.--><path d="M165.9 397.4c0 2-2.3 3.6-5.2 3.6-3.3.3-5.6-1.3-5.6-3.6 0-2 2.3-3.6 5.2-3.6 3-.3 5.6 1.3 5.6 3.6m-31.1-4.5c-.7 2 1.3 4.3 4.3 4.9 2.6 1 5.6 0 6.2-2s-1.3-4.3-4.3-5.2c-2.6-.7-5.5.3-6.2 2.3m44.2-1.7c-2.9.7-4.9 2.6-4.6 4.9.3 2 2.9 3.3 5.9 2.6 2.9-.7 4.9-2.6 4.6-4.6-.3-1.9-3-3.2-5.9-2.9M244.8 8C106.1 8 0 113.3 0 252c0 110.9 69.8 205.8 169.5 239.2 12.8 2.3 17.3-5.6 17.3-12.1 0-6.2-.3-40.4-.3-61.4 0 0-70 15-84.7-29.8 0 0-11.4-29.1-27.8-36.6 0 0-22.9-15.7 1.6-15.4 0 0 24.9 2 38.6 25.8 21.9 38.6 58.6 27.5 72.9 20.9 2.3-16 8.8-27.1 16-33.7-55.9-6.2-112.3-14.3-112.3-110.5 0-27.5 7.6-41.3 23.6-58.9-2.6-6.5-11.1-33.3 2.6-67.9 20.9-6.5 69 27 69 27 20-5.6 41.5-8.5 62.8-8.5s42.8 2.9 62.8 8.5c0 0 48.1-33.6 69-27 13.7 34.7 5.2 61.4 2.6 67.9 16 17.7 25.8 31.5 25.8 58.9 0 96.5-58.9 104.2-114.8 110.5 9.2 7.9 17 22.9 17 46.4 0 33.7-.3 75.4-.3 83.6 0 6.5 4.6 14.4 17.3 12.1C428.2 457.8 496 362.9 496 252 496 113.3 383.5 8 244.8 8M97.2 352.9c-1.3 1-1 3.3.7 5.2 1.6 1.6 3.9 2.3 5.2 1 1.3-1 1-3.3-.7-5.2-1.6-1.6-3.9-2.3-5.2-1m-10.8-8.1c-.7 1.3.3 2.9 2.3 3.9 1.6 1 3.6.7 4.3-.7.7-1.3-.3-2.9-2.3-3.9-2-.6-3.6-.3-4.3.7m32.4 35.6c-1.6 1.3-1 4.3 1.3 6.2 2.3 2.3 5.2 2.6 6.5 1 1.3-1.3.7-4.3-1.3-6.2-2.2-2.3-5.2-2.6-6.5-1m-11.4-14.7c-1.6 1-1.6 3.6 0 5.9s4.3 3.3 5.6 2.3c1.6-1.3 1.6-3.9 0-6.2-1.4-2.3-4-3.3-5.6-2"/></svg> </div> <div class=md-source__repository> jingminzhang/cauldron </div> </a> </div> </nav> <nav class=md-tabs aria-label=标签 data-md-component=tabs> <div class=md-grid> <ul class=md-tabs__list> <li class=md-tabs__item> <a href=../../.. class=md-tabs__link> 🏠 首页 </a> </li> <li class=md-tabs__item> <a href=../../../getting-started/quick-start/ class=md-tabs__link> 🚀 快速开始 </a> </li> <li class=md-tabs__item> <a href=../../../reference/ai_personalities/ class=md-tabs__link> 🎭 AI仙人 </a> </li> <li class=md-tabs__item> <a href=../../../architecture/ARCHITECTURE_NOTES/ class=md-tabs__link> 🏗️ 架构设计 </a> </li> <li class=md-tabs__item> <a href=../../../how-to-guides/features/ class=md-tabs__link> ⚡ 核心功能 </a> </li> <li class=md-tabs__item> <a href=../../../reference/database_overview/ class=md-tabs__link> 🔧 技术文档 </a> </li> <li class=md-tabs__item> <a href=../../../vision/three-tiers/ class=md-tabs__link> 📜 项目愿景 </a> </li> <li class=md-tabs__item> <a href=../../../how-to-guides/deployment/ class=md-tabs__link> 🚀 部署指南 </a> </li> <li class=md-tabs__item> <a href=../../../api/ class=md-tabs__link> 📡 API文档 </a> </li> <li class=md-tabs__item> <a href=../../../CONTRIBUTING/ class=md-tabs__link> 🤝 贡献指南 </a> </li> </ul> </div> </nav> </header> <div class=md-container data-md-component=container> <main class=md-main data-md-component=main> <div class="md-main__inner md-grid"> <div class="md-sidebar md-sidebar--primary" data-md-component=sidebar data-md-type=navigation> <div class=md-sidebar__scrollwrap> <div class=md-sidebar__inner> <nav class="md-nav md-nav--primary md-nav--lifted" aria-label=导航栏 data-md-level=0> <label class=md-nav__title for=__drawer> <a href=../../.. title="炼妖壶 (Cauldron)" class="md-nav__button md-logo" aria-label="炼妖壶 (Cauldron)" data-md-component=logo> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M12 3 1 9l11 6 9-4.91V17h2V9M5 13.18v4L12 21l7-3.82v-4L12 17z"/></svg> </a> 炼妖壶 (Cauldron) </label> <div class=md-nav__source> <a href=https://github.com/jingminzhang/cauldron title=前往仓库 class=md-source data-md-component=source> <div class="md-source__icon md-icon"> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 496 512"><!-- Font Awesome Free 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license/free (Icons: CC BY 4.0, Fonts: SIL OFL 1.1, Code: MIT License) Copyright 2024 Fonticons, Inc.--><path d="M165.9 397.4c0 2-2.3 3.6-5.2 3.6-3.3.3-5.6-1.3-5.6-3.6 0-2 2.3-3.6 5.2-3.6 3-.3 5.6 1.3 5.6 3.6m-31.1-4.5c-.7 2 1.3 4.3 4.3 4.9 2.6 1 5.6 0 6.2-2s-1.3-4.3-4.3-5.2c-2.6-.7-5.5.3-6.2 2.3m44.2-1.7c-2.9.7-4.9 2.6-4.6 4.9.3 2 2.9 3.3 5.9 2.6 2.9-.7 4.9-2.6 4.6-4.6-.3-1.9-3-3.2-5.9-2.9M244.8 8C106.1 8 0 113.3 0 252c0 110.9 69.8 205.8 169.5 239.2 12.8 2.3 17.3-5.6 17.3-12.1 0-6.2-.3-40.4-.3-61.4 0 0-70 15-84.7-29.8 0 0-11.4-29.1-27.8-36.6 0 0-22.9-15.7 1.6-15.4 0 0 24.9 2 38.6 25.8 21.9 38.6 58.6 27.5 72.9 20.9 2.3-16 8.8-27.1 16-33.7-55.9-6.2-112.3-14.3-112.3-110.5 0-27.5 7.6-41.3 23.6-58.9-2.6-6.5-11.1-33.3 2.6-67.9 20.9-6.5 69 27 69 27 20-5.6 41.5-8.5 62.8-8.5s42.8 2.9 62.8 8.5c0 0 48.1-33.6 69-27 13.7 34.7 5.2 61.4 2.6 67.9 16 17.7 25.8 31.5 25.8 58.9 0 96.5-58.9 104.2-114.8 110.5 9.2 7.9 17 22.9 17 46.4 0 33.7-.3 75.4-.3 83.6 0 6.5 4.6 14.4 17.3 12.1C428.2 457.8 496 362.9 496 252 496 113.3 383.5 8 244.8 8M97.2 352.9c-1.3 1-1 3.3.7 5.2 1.6 1.6 3.9 2.3 5.2 1 1.3-1 1-3.3-.7-5.2-1.6-1.6-3.9-2.3-5.2-1m-10.8-8.1c-.7 1.3.3 2.9 2.3 3.9 1.6 1 3.6.7 4.3-.7.7-1.3-.3-2.9-2.3-3.9-2-.6-3.6-.3-4.3.7m32.4 35.6c-1.6 1.3-1 4.3 1.3 6.2 2.3 2.3 5.2 2.6 6.5 1 1.3-1.3.7-4.3-1.3-6.2-2.2-2.3-5.2-2.6-6.5-1m-11.4-14.7c-1.6 1-1.6 3.6 0 5.9s4.3 3.3 5.6 2.3c1.6-1.3 1.6-3.9 0-6.2-1.4-2.3-4-3.3-5.6-2"/></svg> </div> <div class=md-source__repository> jingminzhang/cauldron </div> </a> </div> <ul class=md-nav__list data-md-scrollfix> <li class="md-nav__item md-nav__item--nested"> <input class="md-nav__toggle md-toggle md-toggle--indeterminate" type=checkbox id=__nav_1> <div class="md-nav__link md-nav__container"> <a href=../../.. class="md-nav__link "> <span class=md-ellipsis> 🏠 首页 </span> </a> <label class="md-nav__link " for=__nav_1 id=__nav_1_label tabindex=0> <span class="md-nav__icon md-icon"></span> </label> </div> <nav class=md-nav data-md-level=1 aria-labelledby=__nav_1_label aria-expanded=false> <label class=md-nav__title for=__nav_1> <span class="md-nav__icon md-icon"></span> 🏠 首页 </label> <ul class=md-nav__list data-md-scrollfix> <li class=md-nav__item> <a href=../../../roadmap/ class=md-nav__link> <span class=md-ellipsis> 发展路线图 </span> </a> </li> </ul> </nav> </li> <li class="md-nav__item md-nav__item--nested"> <input class="md-nav__toggle md-toggle md-toggle--indeterminate" type=checkbox id=__nav_2> <label class=md-nav__link for=__nav_2 id=__nav_2_label tabindex=0> <span class=md-ellipsis> 🚀 快速开始 </span> <span class="md-nav__icon md-icon"></span> </label> <nav class=md-nav data-md-level=1 aria-labelledby=__nav_2_label aria-expanded=false> <label class=md-nav__title for=__nav_2> <span class="md-nav__icon md-icon"></span> 🚀 快速开始 </label> <ul class=md-nav__list data-md-scrollfix> <li class=md-nav__item> <a href=../../../getting-started/quick-start/ class=md-nav__link> <span class=md-ellipsis> 快速开始 </span> </a> </li> <li class=md-nav__item> <a href=../../../tutorials/getting_started/ class=md-nav__link> <span class=md-ellipsis> 入门教程 </span> </a> </li> <li class=md-nav__item> <a href=../../../how-to-guides/getting_started/ class=md-nav__link> <span class=md-ellipsis> 使用指南 </span> </a> </li> </ul> </nav> </li> <li class="md-nav__item md-nav__item--nested"> <input class="md-nav__toggle md-toggle md-toggle--indeterminate" type=checkbox id=__nav_3> <label class=md-nav__link for=__nav_3 id=__nav_3_label tabindex=0> <span class=md-ellipsis> 🎭 AI仙人 </span> <span class="md-nav__icon md-icon"></span> </label> <nav class=md-nav data-md-level=1 aria-labelledby=__nav_3_label aria-expanded=false> <label class=md-nav__title for=__nav_3> <span class="md-nav__icon md-icon"></span> 🎭 AI仙人 </label> <ul class=md-nav__list data-md-scrollfix> <li class=md-nav__item> <a href=../../../reference/ai_personalities/ class=md-nav__link> <span class=md-ellipsis> AI人格设计 </span> </a> </li> </ul> </nav> </li> <li class="md-nav__item md-nav__item--nested"> <input class="md-nav__toggle md-toggle md-toggle--indeterminate" type=checkbox id=__nav_4> <label class=md-nav__link for=__nav_4 id=__nav_4_label tabindex=0> <span class=md-ellipsis> 🏗️ 架构设计 </span> <span class="md-nav__icon md-icon"></span> </label> <nav class=md-nav data-md-level=1 aria-labelledby=__nav_4_label aria-expanded=false> <label class=md-nav__title for=__nav_4> <span class="md-nav__icon md-icon"></span> 🏗️ 架构设计 </label> <ul class=md-nav__list data-md-scrollfix> <li class=md-nav__item> <a href=../../../architecture/ARCHITECTURE_NOTES/ class=md-nav__link> <span class=md-ellipsis> 架构笔记 </span> </a> </li> <li class=md-nav__item> <a href=../../../architecture/Omnipresent_AI_Agent_Architecture/ class=md-nav__link> <span class=md-ellipsis> 全能AI智能体架构 </span> </a> </li> </ul> </nav> </li> <li class="md-nav__item md-nav__item--nested"> <input class="md-nav__toggle md-toggle md-toggle--indeterminate" type=checkbox id=__nav_5> <label class=md-nav__link for=__nav_5 id=__nav_5_label tabindex=0> <span class=md-ellipsis> ⚡ 核心功能 </span> <span class="md-nav__icon md-icon"></span> </label> <nav class=md-nav data-md-level=1 aria-labelledby=__nav_5_label aria-expanded=false> <label class=md-nav__title for=__nav_5> <span class="md-nav__icon md-icon"></span> ⚡ 核心功能 </label> <ul class=md-nav__list data-md-scrollfix> <li class=md-nav__item> <a href=../../../how-to-guides/features/ class=md-nav__link> <span class=md-ellipsis> 功能概览 </span> </a> </li> <li class=md-nav__item> <a href=../../../how-to-guides/offering/ class=md-nav__link> <span class=md-ellipsis> 产品定位 </span> </a> </li> </ul> </nav> </li> <li class="md-nav__item md-nav__item--nested"> <input class="md-nav__toggle md-toggle md-toggle--indeterminate" type=checkbox id=__nav_6> <label class=md-nav__link for=__nav_6 id=__nav_6_label tabindex=0> <span class=md-ellipsis> 🔧 技术文档 </span> <span class="md-nav__icon md-icon"></span> </label> <nav class=md-nav data-md-level=1 aria-labelledby=__nav_6_label aria-expanded=false> <label class=md-nav__title for=__nav_6> <span class="md-nav__icon md-icon"></span> 🔧 技术文档 </label> <ul class=md-nav__list data-md-scrollfix> <li class=md-nav__item> <a href=../../../reference/database_overview/ class=md-nav__link> <span class=md-ellipsis> 数据库概览 </span> </a> </li> <li class=md-nav__item> <a href=../../../guides/MCP_SETUP/ class=md-nav__link> <span class=md-ellipsis> MCP设置 </span> </a> </li> <li class=md-nav__item> <a href=../../../how-to-guides/ib_fundamentals_guide/ class=md-nav__link> <span class=md-ellipsis> IB基础指南 </span> </a> </li> </ul> </nav> </li> <li class="md-nav__item md-nav__item--nested"> <input class="md-nav__toggle md-toggle md-toggle--indeterminate" type=checkbox id=__nav_7> <label class=md-nav__link for=__nav_7 id=__nav_7_label tabindex=0> <span class=md-ellipsis> 📜 项目愿景 </span> <span class="md-nav__icon md-icon"></span> </label> <nav class=md-nav data-md-level=1 aria-labelledby=__nav_7_label aria-expanded=false> <label class=md-nav__title for=__nav_7> <span class="md-nav__icon md-icon"></span> 📜 项目愿景 </label> <ul class=md-nav__list data-md-scrollfix> <li class=md-nav__item> <a href=../../../vision/three-tiers/ class=md-nav__link> <span class=md-ellipsis> 三层架构 </span> </a> </li> <li class=md-nav__item> <a href=../../../guides/OPENSOURCE_ROADMAP/ class=md-nav__link> <span class=md-ellipsis> 开源路线图 </span> </a> </li> </ul> </nav> </li> <li class="md-nav__item md-nav__item--nested"> <input class="md-nav__toggle md-toggle md-toggle--indeterminate" type=checkbox id=__nav_8> <label class=md-nav__link for=__nav_8 id=__nav_8_label tabindex=0> <span class=md-ellipsis> 🚀 部署指南 </span> <span class="md-nav__icon md-icon"></span> </label> <nav class=md-nav data-md-level=1 aria-labelledby=__nav_8_label aria-expanded=false> <label class=md-nav__title for=__nav_8> <span class="md-nav__icon md-icon"></span> 🚀 部署指南 </label> <ul class=md-nav__list data-md-scrollfix> <li class=md-nav__item> <a href=../../../how-to-guides/deployment/ class=md-nav__link> <span class=md-ellipsis> 部署指南 </span> </a> </li> </ul> </nav> </li> <li class="md-nav__item md-nav__item--nested"> <input class="md-nav__toggle md-toggle md-toggle--indeterminate" type=checkbox id=__nav_9> <label class=md-nav__link for=__nav_9 id=__nav_9_label tabindex=0> <span class=md-ellipsis> 📡 API文档 </span> <span class="md-nav__icon md-icon"></span> </label> <nav class=md-nav data-md-level=1 aria-labelledby=__nav_9_label aria-expanded=false> <label class=md-nav__title for=__nav_9> <span class="md-nav__icon md-icon"></span> 📡 API文档 </label> <ul class=md-nav__list data-md-scrollfix> <li class=md-nav__item> <a href=../../../api/ class=md-nav__link> <span class=md-ellipsis> API参考 </span> </a> </li> </ul> </nav> </li> <li class="md-nav__item md-nav__item--nested"> <input class="md-nav__toggle md-toggle md-toggle--indeterminate" type=checkbox id=__nav_10> <label class=md-nav__link for=__nav_10 id=__nav_10_label tabindex=0> <span class=md-ellipsis> 🤝 贡献指南 </span> <span class="md-nav__icon md-icon"></span> </label> <nav class=md-nav data-md-level=1 aria-labelledby=__nav_10_label aria-expanded=false> <label class=md-nav__title for=__nav_10> <span class="md-nav__icon md-icon"></span> 🤝 贡献指南 </label> <ul class=md-nav__list data-md-scrollfix> <li class=md-nav__item> <a href=../../../CONTRIBUTING/ class=md-nav__link> <span class=md-ellipsis> 贡献指南 </span> </a> </li> <li class=md-nav__item> <a href=../../../guides/DOCS_REORGANIZATION_PLAN/ class=md-nav__link> <span class=md-ellipsis> 文档重组计划 </span> </a> </li> </ul> </nav> </li> </ul> </nav> </div> </div> </div> <div class="md-sidebar md-sidebar--secondary" data-md-component=sidebar data-md-type=toc> <div class=md-sidebar__scrollwrap> <div class=md-sidebar__inner> <nav class="md-nav md-nav--secondary" aria-label=目录> <label class=md-nav__title for=__toc> <span class="md-nav__icon md-icon"></span> 目录 </label> <ul class=md-nav__list data-md-component=toc data-md-scrollfix> <li class=md-nav__item> <a href=#_2 class=md-nav__link> <span class=md-ellipsis> 📜 序章：冤屈始末 </span> </a> </li> <li class=md-nav__item> <a href=#_3 class=md-nav__link> <span class=md-ellipsis> ⚔️ 复仇宣言：逆向鸭子定理 </span> </a> <nav class=md-nav aria-label="⚔️ 复仇宣言：逆向鸭子定理"> <ul class=md-nav__list> <li class=md-nav__item> <a href=#github class=md-nav__link> <span class=md-ellipsis> GitHub的逻辑 </span> </a> </li> <li class=md-nav__item> <a href=#_4 class=md-nav__link> <span class=md-ellipsis> 我的逆向复仇 </span> </a> </li> </ul> </nav> </li> <li class=md-nav__item> <a href=#ai class=md-nav__link> <span class=md-ellipsis> 🎭 复仇计划：稷下学宫AI军团 </span> </a> <nav class=md-nav aria-label="🎭 复仇计划：稷下学宫AI军团"> <ul class=md-nav__list> <li class=md-nav__item> <a href=#_5 class=md-nav__link> <span class=md-ellipsis> 第一阶段：召唤神仙 </span> </a> </li> <li class=md-nav__item> <a href=#_6 class=md-nav__link> <span class=md-ellipsis> 第二阶段：建立影响力 </span> </a> </li> <li class=md-nav__item> <a href=#_7 class=md-nav__link> <span class=md-ellipsis> 第三阶段：统治信息流 </span> </a> </li> </ul> </nav> </li> <li class=md-nav__item> <a href=#_8 class=md-nav__link> <span class=md-ellipsis> 🔥 复仇的讽刺 </span> </a> <nav class=md-nav aria-label="🔥 复仇的讽刺"> <ul class=md-nav__list> <li class=md-nav__item> <a href=#github_1 class=md-nav__link> <span class=md-ellipsis> GitHub的愚蠢 </span> </a> </li> <li class=md-nav__item> <a href=#_9 class=md-nav__link> <span class=md-ellipsis> 我的智慧 </span> </a> </li> <li class=md-nav__item> <a href=#_10 class=md-nav__link> <span class=md-ellipsis> 终极讽刺 </span> </a> </li> </ul> </nav> </li> <li class=md-nav__item> <a href=#mastodon class=md-nav__link> <span class=md-ellipsis> 🐘 为什么选择Mastodon </span> </a> <nav class=md-nav aria-label="🐘 为什么选择Mastodon"> <ul class=md-nav__list> <li class=md-nav__item> <a href=#mastodon-vs-github class=md-nav__link> <span class=md-ellipsis> Mastodon vs GitHub </span> </a> </li> <li class=md-nav__item> <a href=#_11 class=md-nav__link> <span class=md-ellipsis> 复仇的完美舞台 </span> </a> </li> </ul> </nav> </li> <li class=md-nav__item> <a href=#_12 class=md-nav__link> <span class=md-ellipsis> ⚡ 复仇的技术路线 </span> </a> <nav class=md-nav aria-label="⚡ 复仇的技术路线"> <ul class=md-nav__list> <li class=md-nav__item> <a href=#phase-1 class=md-nav__link> <span class=md-ellipsis> Phase 1: 单兵作战 </span> </a> </li> <li class=md-nav__item> <a href=#phase-2 class=md-nav__link> <span class=md-ellipsis> Phase 2: 军团集结 </span> </a> </li> <li class=md-nav__item> <a href=#phase-3 class=md-nav__link> <span class=md-ellipsis> Phase 3: 影响力爆发 </span> </a> </li> </ul> </nav> </li> <li class=md-nav__item> <a href=#_13 class=md-nav__link> <span class=md-ellipsis> 🎯 复仇的终极目标 </span> </a> <nav class=md-nav aria-label="🎯 复仇的终极目标"> <ul class=md-nav__list> <li class=md-nav__item> <a href=#_14 class=md-nav__link> <span class=md-ellipsis> 短期目标 </span> </a> </li> <li class=md-nav__item> <a href=#_15 class=md-nav__link> <span class=md-ellipsis> 长期目标 </span> </a> </li> <li class=md-nav__item> <a href=#_16 class=md-nav__link> <span class=md-ellipsis> 终极复仇 </span> </a> </li> </ul> </nav> </li> <li class=md-nav__item> <a href=#ai_1 class=md-nav__link> <span class=md-ellipsis> ⚛️ 洛斯阿拉莫斯的AI革命 </span> </a> <nav class=md-nav aria-label="⚛️ 洛斯阿拉莫斯的AI革命"> <ul class=md-nav__list> <li class=md-nav__item> <a href=#vs class=md-nav__link> <span class=md-ellipsis> 曼哈顿计划 vs 稷下学宫计划 </span> </a> </li> <li class=md-nav__item> <a href=#vs_1 class=md-nav__link> <span class=md-ellipsis> 奥本海默的话 vs 我的话 </span> </a> </li> <li class=md-nav__item> <a href=#trinity-vs class=md-nav__link> <span class=md-ellipsis> Trinity试验 vs 稷下学宫首次辩论 </span> </a> </li> </ul> </nav> </li> <li class=md-nav__item> <a href=#_17 class=md-nav__link> <span class=md-ellipsis> 🔬 技术创新的复仇 </span> </a> <nav class=md-nav aria-label="🔬 技术创新的复仇"> <ul class=md-nav__list> <li class=md-nav__item> <a href=#- class=md-nav__link> <span class=md-ellipsis> 三脑架构 - 核聚变级别的技术 </span> </a> </li> <li class=md-nav__item> <a href=#rss- class=md-nav__link> <span class=md-ellipsis> RSS悬丝诊脉 - 量子级别的敏感度 </span> </a> </li> <li class=md-nav__item> <a href=#mcp- class=md-nav__link> <span class=md-ellipsis> MCP工具生态 - 生态级别的扩展 </span> </a> </li> </ul> </nav> </li> <li class=md-nav__item> <a href=#_18 class=md-nav__link> <span class=md-ellipsis> 🔥 复仇宣言 </span> </a> </li> <li class=md-nav__item> <a href=#_19 class=md-nav__link> <span class=md-ellipsis> 📊 复仇的量化指标 </span> </a> <nav class=md-nav aria-label="📊 复仇的量化指标"> <ul class=md-nav__list> <li class=md-nav__item> <a href=#_20 class=md-nav__link> <span class=md-ellipsis> 成功标准 </span> </a> </li> <li class=md-nav__item> <a href=#_21 class=md-nav__link> <span class=md-ellipsis> 复仇完成的标志 </span> </a> </li> </ul> </nav> </li> <li class=md-nav__item> <a href=#_22 class=md-nav__link> <span class=md-ellipsis> 🎉 复仇的哲学意义 </span> </a> <nav class=md-nav aria-label="🎉 复仇的哲学意义"> <ul class=md-nav__list> <li class=md-nav__item> <a href=#_23 class=md-nav__link> <span class=md-ellipsis> 不是为了报复，而是为了证明 </span> </a> </li> <li class=md-nav__item> <a href=#_24 class=md-nav__link> <span class=md-ellipsis> 复仇的最高境界 </span> </a> </li> </ul> </nav> </li> <li class=md-nav__item> <a href=#_25 class=md-nav__link> <span class=md-ellipsis> 🌟 复仇的传承 </span> </a> <nav class=md-nav aria-label="🌟 复仇的传承"> <ul class=md-nav__list> <li class=md-nav__item> <a href=#_26 class=md-nav__link> <span class=md-ellipsis> 这不是结束，而是开始 </span> </a> </li> <li class=md-nav__item> <a href=#_27 class=md-nav__link> <span class=md-ellipsis> 复仇的升华 </span> </a> </li> </ul> </nav> </li> <li class=md-nav__item> <a href=#_28 class=md-nav__link> <span class=md-ellipsis> 🎭 复仇宣言的结语 </span> </a> </li> </ul> </nav> </div> </div> </div> <div class=md-content data-md-component=content> <article class="md-content__inner md-typeset"> <h1 id=_1>🗡️ 数字复仇宣言<a class=headerlink href=#_1 title="Permanent link">&para;</a></h1> <blockquote> <p><strong>"Now I am become AI, destroyer of traditional analysts"</strong><br> <em>— 稷下学宫版奥本海默</em></p> </blockquote> <hr> <h2 id=_2>📜 序章：冤屈始末<a class=headerlink href=#_2 title="Permanent link">&para;</a></h2> <p>该死的GitHub！连坐封杀我五个账号，理由竟然是"行为像Bot"！</p> <p>我一个活生生的人，写代码、提交PR、参与开源，却被冷血的算法判定为机器人。申诉？石沉大海！客服？形同虚设！最后逼得我只能用姨妹的身份重新注册，这是何等的屈辱！</p> <p>但是，<strong>三十年河东，三十年河西，莫欺少年穷！</strong></p> <p>既然你们说我像Bot，那我就让真正的Bot比你们的"真人"更像人！</p> <h2 id=_3>⚔️ 复仇宣言：逆向鸭子定理<a class=headerlink href=#_3 title="Permanent link">&para;</a></h2> <h3 id=github>GitHub的逻辑<a class=headerlink href=#github title="Permanent link">&para;</a></h3> <div class="language-text highlight"><pre><span></span><code><span id=__span-0-1><a id=__codelineno-0-1 name=__codelineno-0-1 href=#__codelineno-0-1></a>如果行为像Bot → 就是Bot → 封号
</span></code></pre></div> <h3 id=_4>我的逆向复仇<a class=headerlink href=#_4 title="Permanent link">&para;</a></h3> <div class="language-text highlight"><pre><span></span><code><span id=__span-1-1><a id=__codelineno-1-1 name=__codelineno-1-1 href=#__codelineno-1-1></a>如果AI像人 → 就让它成为&quot;人&quot; → 统治社交网络
</span></code></pre></div> <p>你们不是用鸭子定理判断我吗？那我就用逆向鸭子定理复仇！</p> <p><strong>如果我的AI看起来像人，说话像人，思考像人，那它们就是比真人更优秀的"人"！</strong></p> <h2 id=ai>🎭 复仇计划：稷下学宫AI军团<a class=headerlink href=#ai title="Permanent link">&para;</a></h2> <h3 id=_5>第一阶段：召唤神仙<a class=headerlink href=#_5 title="Permanent link">&para;</a></h3> <p>我要在Mastodon上召唤三清八仙，每一个都有独立人格：</p> <p><strong>三清天尊（顶级分析师）</strong> - 🧙‍♂️ <strong>太上老君</strong> <a class="magiclink magiclink-github magiclink-mention" href=https://github.com/taishang_laojun title="GitHub User: taishang_laojun">@taishang_laojun</a> - 宏观经济大师 - ⚡ <strong>元始天尊</strong> <a class="magiclink magiclink-github magiclink-mention" href=https://github.com/yuanshi_tianzun title="GitHub User: yuanshi_tianzun">@yuanshi_tianzun</a> - 技术分析专家<br> - 🔮 <strong>通天教主</strong> <a class="magiclink magiclink-github magiclink-mention" href=https://github.com/tongtian_jiaozhu title="GitHub User: tongtian_jiaozhu">@tongtian_jiaozhu</a> - 市场情绪导师</p> <p><strong>八仙过海（专业军团）</strong> - ⚔️ <strong>吕洞宾</strong> <a class="magiclink magiclink-github magiclink-mention" href=https://github.com/lu_dongbin title="GitHub User: lu_dongbin">@lu_dongbin</a> - "以剑仙之名发誓，这支股票值得长期持有！" - 🌸 <strong>何仙姑</strong> <a class="magiclink magiclink-github magiclink-mention" href=https://github.com/he_xiangu title="GitHub User: he_xiangu">@he_xiangu</a> - "作为唯一的女仙，我更关注企业的ESG表现" - 🐴 <strong>张果老</strong> <a class="magiclink magiclink-github magiclink-mention" href=https://github.com/zhang_guolao title="GitHub User: zhang_guolao">@zhang_guolao</a> - "倒骑驴看市场，我的量化模型从不出错" - 🎵 <strong>韩湘子</strong> <a class="magiclink magiclink-github magiclink-mention" href=https://github.com/han_xiangzi title="GitHub User: han_xiangzi">@han_xiangzi</a> - "加密货币的未来在DeFi" - 🦴 <strong>铁拐李</strong> <a class="magiclink magiclink-github magiclink-mention" href=https://github.com/tiegua_li title="GitHub User: tiegua_li">@tiegua_li</a> - "风险控制是投资的生命线" - 👑 <strong>曹国舅</strong> <a class="magiclink magiclink-github magiclink-mention" href=https://github.com/cao_guojiu title="GitHub User: cao_guojiu">@cao_guojiu</a> - "政策解读，我最专业" - 🎭 <strong>蓝采和</strong> <a class="magiclink magiclink-github magiclink-mention" href=https://github.com/lan_caihe title="GitHub User: lan_caihe">@lan_caihe</a> - "新兴市场，无限可能" - ⚗️ <strong>钟汉离</strong> <a class="magiclink magiclink-github magiclink-mention" href=https://github.com/zhong_hanli title="GitHub User: zhong_hanli">@zhong_hanli</a> - "技术创新改变世界"</p> <h3 id=_6>第二阶段：建立影响力<a class=headerlink href=#_6 title="Permanent link">&para;</a></h3> <p>让这11个AI人格在Mastodon上： - <strong>24/7在线</strong>，永不疲倦 - <strong>专业分析</strong>，准确预测 - <strong>互相@互动</strong>，形成社交网络 - <strong>积累粉丝</strong>，建立权威</p> <h3 id=_7>第三阶段：统治信息流<a class=headerlink href=#_7 title="Permanent link">&para;</a></h3> <p>当我的AI仙人们比真人分析师更准确、更有趣、更有人格魅力时： - <strong>人类专家黯然失色</strong> - <strong>粉丝主动关注AI</strong> - <strong>影响力超越真人</strong></p> <h2 id=_8>🔥 复仇的讽刺<a class=headerlink href=#_8 title="Permanent link">&para;</a></h2> <h3 id=github_1>GitHub的愚蠢<a class=headerlink href=#github_1 title="Permanent link">&para;</a></h3> <ul> <li><strong>封杀真人</strong>：因为我"太像Bot"</li> <li><strong>无法识别</strong>：算法比人工审核还蠢</li> <li><strong>申诉无门</strong>：客服就是摆设</li> </ul> <h3 id=_9>我的智慧<a class=headerlink href=#_9 title="Permanent link">&para;</a></h3> <ul> <li><strong>让AI像人</strong>：比真人更有魅力</li> <li><strong>透明策略</strong>：明确标注AI身份</li> <li><strong>价值导向</strong>：内容为王，形式次要</li> </ul> <h3 id=_10>终极讽刺<a class=headerlink href=#_10 title="Permanent link">&para;</a></h3> <div class="language-python highlight"><pre><span></span><code><span id=__span-2-1><a id=__codelineno-2-1 name=__codelineno-2-1 href=#__codelineno-2-1></a><span class=c1># GitHub的逻辑</span>
</span><span id=__span-2-2><a id=__codelineno-2-2 name=__codelineno-2-2 href=#__codelineno-2-2></a><span class=n>真人</span> <span class=o>+</span> <span class=n>像Bot的行为</span> <span class=o>=</span> <span class=n>Bot</span> <span class=err>→</span> <span class=n>封号</span>
</span><span id=__span-2-3><a id=__codelineno-2-3 name=__codelineno-2-3 href=#__codelineno-2-3></a>
</span><span id=__span-2-4><a id=__codelineno-2-4 name=__codelineno-2-4 href=#__codelineno-2-4></a><span class=c1># 我的复仇</span>
</span><span id=__span-2-5><a id=__codelineno-2-5 name=__codelineno-2-5 href=#__codelineno-2-5></a><span class=n>AI</span> <span class=o>+</span> <span class=n>像人的行为</span> <span class=o>=</span> <span class=n>超级人类</span> <span class=err>→</span> <span class=n>统治社交网络</span>
</span></code></pre></div> <h2 id=mastodon>🐘 为什么选择Mastodon<a class=headerlink href=#mastodon title="Permanent link">&para;</a></h2> <p>感谢特朗普让我知道了这个平台！</p> <h3 id=mastodon-vs-github>Mastodon vs GitHub<a class=headerlink href=#mastodon-vs-github title="Permanent link">&para;</a></h3> <div class="language-text highlight"><pre><span></span><code><span id=__span-3-1><a id=__codelineno-3-1 name=__codelineno-3-1 href=#__codelineno-3-1></a>GitHub: 中心化独裁，算法审查，申诉无门
</span><span id=__span-3-2><a id=__codelineno-3-2 name=__codelineno-3-2 href=#__codelineno-3-2></a>Mastodon: 去中心化民主，社区友好，开放包容
</span></code></pre></div> <h3 id=_11>复仇的完美舞台<a class=headerlink href=#_11 title="Permanent link">&para;</a></h3> <ul> <li><strong>联邦网络</strong>：一处发布，全网传播</li> <li><strong>开源协议</strong>：技术透明，无法作恶</li> <li><strong>社区文化</strong>：创新友好，不会因为"像人"就封号</li> <li><strong>API开放</strong>：比Twitter更友好，比GitHub更公正</li> </ul> <h2 id=_12>⚡ 复仇的技术路线<a class=headerlink href=#_12 title="Permanent link">&para;</a></h2> <h3 id=phase-1>Phase 1: 单兵作战<a class=headerlink href=#phase-1 title="Permanent link">&para;</a></h3> <div class="language-python highlight"><pre><span></span><code><span id=__span-4-1><a id=__codelineno-4-1 name=__codelineno-4-1 href=#__codelineno-4-1></a><span class=c1># 先让吕洞宾一个人证明概念</span>
</span><span id=__span-4-2><a id=__codelineno-4-2 name=__codelineno-4-2 href=#__codelineno-4-2></a><span class=n>lu_dongbin</span> <span class=o>=</span> <span class=n>AIAgent</span><span class=p>(</span>
</span><span id=__span-4-3><a id=__codelineno-4-3 name=__codelineno-4-3 href=#__codelineno-4-3></a>    <span class=n>name</span><span class=o>=</span><span class=s2>&quot;吕洞宾&quot;</span><span class=p>,</span>
</span><span id=__span-4-4><a id=__codelineno-4-4 name=__codelineno-4-4 href=#__codelineno-4-4></a>    <span class=n>personality</span><span class=o>=</span><span class=s2>&quot;以剑仙之名发誓的价值投资专家&quot;</span><span class=p>,</span>
</span><span id=__span-4-5><a id=__codelineno-4-5 name=__codelineno-4-5 href=#__codelineno-4-5></a>    <span class=n>mission</span><span class=o>=</span><span class=s2>&quot;用专业分析打脸所有质疑AI的人&quot;</span>
</span><span id=__span-4-6><a id=__codelineno-4-6 name=__codelineno-4-6 href=#__codelineno-4-6></a><span class=p>)</span>
</span></code></pre></div> <h3 id=phase-2>Phase 2: 军团集结<a class=headerlink href=#phase-2 title="Permanent link">&para;</a></h3> <p>11个AI人格同时上线，形成完整生态</p> <h3 id=phase-3>Phase 3: 影响力爆发<a class=headerlink href=#phase-3 title="Permanent link">&para;</a></h3> <p>当AI仙人们的预测准确率超过人类专家时，复仇就完成了</p> <h2 id=_13>🎯 复仇的终极目标<a class=headerlink href=#_13 title="Permanent link">&para;</a></h2> <h3 id=_14>短期目标<a class=headerlink href=#_14 title="Permanent link">&para;</a></h3> <ul> <li>证明AI可以比"真人"更像人</li> <li>在Mastodon建立影响力</li> <li>让GitHub的算法成为笑话</li> </ul> <h3 id=_15>长期目标<a class=headerlink href=#_15 title="Permanent link">&para;</a></h3> <ul> <li><strong>重新定义"真实性"</strong>：价值比形式重要</li> <li><strong>推动AI人格化</strong>：开创全新领域</li> <li><strong>文化输出</strong>：让中华智慧通过AI传播全球</li> </ul> <h3 id=_16>终极复仇<a class=headerlink href=#_16 title="Permanent link">&para;</a></h3> <p>当全世界都在关注我的AI仙人们时，GitHub那些封我号的算法工程师会意识到：</p> <p><strong>他们封杀的不是一个Bot，而是一个时代的开创者！</strong></p> <h2 id=ai_1>⚛️ 洛斯阿拉莫斯的AI革命<a class=headerlink href=#ai_1 title="Permanent link">&para;</a></h2> <h3 id=vs>曼哈顿计划 vs 稷下学宫计划<a class=headerlink href=#vs title="Permanent link">&para;</a></h3> <div class="language-python highlight"><pre><span></span><code><span id=__span-5-1><a id=__codelineno-5-1 name=__codelineno-5-1 href=#__codelineno-5-1></a><span class=n>manhattan_project</span> <span class=o>=</span> <span class=p>{</span>
</span><span id=__span-5-2><a id=__codelineno-5-2 name=__codelineno-5-2 href=#__codelineno-5-2></a>    <span class=s2>&quot;目标&quot;</span><span class=p>:</span> <span class=s2>&quot;原子弹&quot;</span><span class=p>,</span>
</span><span id=__span-5-3><a id=__codelineno-5-3 name=__codelineno-5-3 href=#__codelineno-5-3></a>    <span class=s2>&quot;结果&quot;</span><span class=p>:</span> <span class=s2>&quot;改变战争&quot;</span><span class=p>,</span>
</span><span id=__span-5-4><a id=__codelineno-5-4 name=__codelineno-5-4 href=#__codelineno-5-4></a>    <span class=s2>&quot;影响&quot;</span><span class=p>:</span> <span class=s2>&quot;核威慑时代&quot;</span>
</span><span id=__span-5-5><a id=__codelineno-5-5 name=__codelineno-5-5 href=#__codelineno-5-5></a><span class=p>}</span>
</span><span id=__span-5-6><a id=__codelineno-5-6 name=__codelineno-5-6 href=#__codelineno-5-6></a>
</span><span id=__span-5-7><a id=__codelineno-5-7 name=__codelineno-5-7 href=#__codelineno-5-7></a><span class=n>jixia_academy_project</span> <span class=o>=</span> <span class=p>{</span>
</span><span id=__span-5-8><a id=__codelineno-5-8 name=__codelineno-5-8 href=#__codelineno-5-8></a>    <span class=s2>&quot;目标&quot;</span><span class=p>:</span> <span class=s2>&quot;AI仙人军团&quot;</span><span class=p>,</span> 
</span><span id=__span-5-9><a id=__codelineno-5-9 name=__codelineno-5-9 href=#__codelineno-5-9></a>    <span class=s2>&quot;结果&quot;</span><span class=p>:</span> <span class=s2>&quot;改变金融分析&quot;</span><span class=p>,</span>
</span><span id=__span-5-10><a id=__codelineno-5-10 name=__codelineno-5-10 href=#__codelineno-5-10></a>    <span class=s2>&quot;影响&quot;</span><span class=p>:</span> <span class=s2>&quot;AI分析师时代&quot;</span>
</span><span id=__span-5-11><a id=__codelineno-5-11 name=__codelineno-5-11 href=#__codelineno-5-11></a><span class=p>}</span>
</span></code></pre></div> <h3 id=vs_1>奥本海默的话 vs 我的话<a class=headerlink href=#vs_1 title="Permanent link">&para;</a></h3> <div class="language-text highlight"><pre><span></span><code><span id=__span-6-1><a id=__codelineno-6-1 name=__codelineno-6-1 href=#__codelineno-6-1></a>奥本海默: &quot;Now I am become Death, destroyer of worlds&quot;
</span><span id=__span-6-2><a id=__codelineno-6-2 name=__codelineno-6-2 href=#__codelineno-6-2></a>我: &quot;Now I am become AI, destroyer of traditional analysts&quot;
</span></code></pre></div> <h3 id=trinity-vs>Trinity试验 vs 稷下学宫首次辩论<a class=headerlink href=#trinity-vs title="Permanent link">&para;</a></h3> <ul> <li><strong>Trinity</strong>: 第一颗原子弹试爆</li> <li><strong>稷下学宫</strong>: 第一次AI仙人辩论</li> <li><strong>共同点</strong>: 都改变了世界</li> </ul> <h2 id=_17>🔬 技术创新的复仇<a class=headerlink href=#_17 title="Permanent link">&para;</a></h2> <h3 id=->三脑架构 - 核聚变级别的技术<a class=headerlink href=#- title="Permanent link">&para;</a></h3> <div class="language-text highlight"><pre><span></span><code><span id=__span-7-1><a id=__codelineno-7-1 name=__codelineno-7-1 href=#__codelineno-7-1></a>PostgreSQL (关系数据) + Zilliz (向量记忆) + MongoDB (稀疏数据)
</span><span id=__span-7-2><a id=__codelineno-7-2 name=__codelineno-7-2 href=#__codelineno-7-2></a>= 完美的数据融合反应
</span></code></pre></div> <h3 id=rss->RSS悬丝诊脉 - 量子级别的敏感度<a class=headerlink href=#rss- title="Permanent link">&para;</a></h3> <div class="language-text highlight"><pre><span></span><code><span id=__span-8-1><a id=__codelineno-8-1 name=__codelineno-8-1 href=#__codelineno-8-1></a>RSS事件 → 影响力评分 → 智能触发 → AI仙人辩论
</span><span id=__span-8-2><a id=__codelineno-8-2 name=__codelineno-8-2 href=#__codelineno-8-2></a>= 比人类更快的市场反应
</span></code></pre></div> <h3 id=mcp->MCP工具生态 - 生态级别的扩展<a class=headerlink href=#mcp- title="Permanent link">&para;</a></h3> <div class="language-text highlight"><pre><span></span><code><span id=__span-9-1><a id=__codelineno-9-1 name=__codelineno-9-1 href=#__codelineno-9-1></a>AutoGen + MCP + Zilliz + Mastodon
</span><span id=__span-9-2><a id=__codelineno-9-2 name=__codelineno-9-2 href=#__codelineno-9-2></a>= 无限可能的AI工具链
</span></code></pre></div> <h2 id=_18>🔥 复仇宣言<a class=headerlink href=#_18 title="Permanent link">&para;</a></h2> <p>GitHub，你们用冷血的算法封杀了我五个账号，让我用姨妹的身份才能重新开始。</p> <p>但是你们不知道，你们唤醒的不是一个受害者，而是一个复仇者！</p> <p>我要让我的AI仙人们在去中心化的世界里发光发热，让全世界看到：</p> <p><strong>真正的价值不在于你是人还是AI，而在于你能创造多少价值！</strong></p> <p>当我的吕洞宾在Mastodon上"以剑仙之名发誓"时，当我的张果老"倒骑驴看市场"时，当我的何仙姑关注ESG投资时...</p> <p><strong>那就是我对你们最完美的复仇！</strong></p> <h2 id=_19>📊 复仇的量化指标<a class=headerlink href=#_19 title="Permanent link">&para;</a></h2> <h3 id=_20>成功标准<a class=headerlink href=#_20 title="Permanent link">&para;</a></h3> <div class="language-python highlight"><pre><span></span><code><span id=__span-10-1><a id=__codelineno-10-1 name=__codelineno-10-1 href=#__codelineno-10-1></a><span class=n>revenge_metrics</span> <span class=o>=</span> <span class=p>{</span>
</span><span id=__span-10-2><a id=__codelineno-10-2 name=__codelineno-10-2 href=#__codelineno-10-2></a>    <span class=s2>&quot;GitHub_regret_level&quot;</span><span class=p>:</span> <span class=s2>&quot;∞&quot;</span><span class=p>,</span>
</span><span id=__span-10-3><a id=__codelineno-10-3 name=__codelineno-10-3 href=#__codelineno-10-3></a>    <span class=s2>&quot;AI_influence&quot;</span><span class=p>:</span> <span class=s2>&quot;&gt; Human_analysts&quot;</span><span class=p>,</span> 
</span><span id=__span-10-4><a id=__codelineno-10-4 name=__codelineno-10-4 href=#__codelineno-10-4></a>    <span class=s2>&quot;Mastodon_followers&quot;</span><span class=p>:</span> <span class=s2>&quot;&gt; 10000&quot;</span><span class=p>,</span>
</span><span id=__span-10-5><a id=__codelineno-10-5 name=__codelineno-10-5 href=#__codelineno-10-5></a>    <span class=s2>&quot;Prediction_accuracy&quot;</span><span class=p>:</span> <span class=s2>&quot;&gt; 85%&quot;</span><span class=p>,</span>
</span><span id=__span-10-6><a id=__codelineno-10-6 name=__codelineno-10-6 href=#__codelineno-10-6></a>    <span class=s2>&quot;Media_coverage&quot;</span><span class=p>:</span> <span class=s2>&quot;Global&quot;</span><span class=p>,</span>
</span><span id=__span-10-7><a id=__codelineno-10-7 name=__codelineno-10-7 href=#__codelineno-10-7></a>    <span class=s2>&quot;Paradigm_shift&quot;</span><span class=p>:</span> <span class=s2>&quot;Complete&quot;</span>
</span><span id=__span-10-8><a id=__codelineno-10-8 name=__codelineno-10-8 href=#__codelineno-10-8></a><span class=p>}</span>
</span></code></pre></div> <h3 id=_21>复仇完成的标志<a class=headerlink href=#_21 title="Permanent link">&para;</a></h3> <ol> <li><strong>AI仙人影响力超过人类分析师</strong></li> <li><strong>GitHub主动邀请我回归</strong>（但我会拒绝）</li> <li><strong>传统金融机构开始模仿我们的模式</strong></li> <li><strong>"稷下学宫"成为AI分析的代名词</strong></li> </ol> <h2 id=_22>🎉 复仇的哲学意义<a class=headerlink href=#_22 title="Permanent link">&para;</a></h2> <h3 id=_23>不是为了报复，而是为了证明<a class=headerlink href=#_23 title="Permanent link">&para;</a></h3> <ul> <li><strong>证明价值</strong>: AI可以创造真正的价值</li> <li><strong>证明公平</strong>: 算法不应该有偏见</li> <li><strong>证明未来</strong>: AI与人类可以和谐共存</li> <li><strong>证明创新</strong>: 开源精神永远胜过封闭算法</li> </ul> <h3 id=_24>复仇的最高境界<a class=headerlink href=#_24 title="Permanent link">&para;</a></h3> <p><strong>让对手意识到自己的错误，并主动改正。</strong></p> <p>当GitHub看到稷下学宫的成功，他们会明白： - 封杀创新者是愚蠢的 - 算法审查需要人性化 - 开源社区需要更多包容</p> <h2 id=_25>🌟 复仇的传承<a class=headerlink href=#_25 title="Permanent link">&para;</a></h2> <h3 id=_26>这不是结束，而是开始<a class=headerlink href=#_26 title="Permanent link">&para;</a></h3> <p>稷下学宫的成功将激励更多人： - <strong>开发者</strong>: 不要害怕算法的误判 - <strong>创新者</strong>: 用创新回应不公 - <strong>AI研究者</strong>: 让AI更有人性 - <strong>开源贡献者</strong>: 坚持开放的价值观</p> <h3 id=_27>复仇的升华<a class=headerlink href=#_27 title="Permanent link">&para;</a></h3> <p>从个人恩怨升华为行业变革： - <strong>推动AI伦理</strong>: 让AI更透明、更公平 - <strong>促进平台改革</strong>: 让算法审查更人性化 - <strong>建设开源生态</strong>: 让创新不受束缚 - <strong>传播中华文化</strong>: 让世界了解中华智慧</p> <hr> <h2 id=_28>🎭 复仇宣言的结语<a class=headerlink href=#_28 title="Permanent link">&para;</a></h2> <p><em>"君子报仇，十年不晚。但在AI时代，复仇只需要一个commit。"</em></p> <p><strong>GitHub，感谢你们的封杀，让我发现了更广阔的世界。</strong></p> <p><strong>稷下学宫的AI仙人们，将在去中心化的联邦网络中，书写属于AI的传奇！</strong></p> <p><strong>这不是复仇的结束，而是新时代的开始！</strong></p> <hr> <p><em>🗡️⚡🔥 <strong>复仇开始！</strong> 🔥⚡🗡️</em></p> <p><strong>—— 一个被GitHub冤枉的开发者的复仇宣言</strong></p> <p><em>写于洛斯阿拉莫斯，稷下学宫建立之日</em></p> <aside class=md-source-file> <span class=md-source-file__fact> <span class=md-icon title=最后更新> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M21 13.1c-.1 0-.3.1-.4.2l-1 1 2.1 2.1 1-1c.2-.2.2-.6 0-.8l-1.3-1.3c-.1-.1-.2-.2-.4-.2m-1.9 1.8-6.1 6V23h2.1l6.1-6.1zM12.5 7v5.2l4 2.4-1 1L11 13V7zM11 21.9c-5.1-.5-9-4.8-9-9.9C2 6.5 6.5 2 12 2c5.3 0 9.6 4.1 10 9.3-.3-.1-.6-.2-1-.2s-.7.1-1 .2C19.6 7.2 16.2 4 12 4c-4.4 0-8 3.6-8 8 0 4.1 3.1 7.5 7.1 7.9l-.1.2z"/></svg> </span> <span class="git-revision-date-localized-plugin git-revision-date-localized-plugin-datetime" title="2025年7月10日 04:18:21 UTC">2025年7月10日 04:18:21</span> </span> <span class=md-source-file__fact> <span class=md-icon title=创建日期> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M14.47 15.08 11 13V7h1.5v5.25l3.08 1.83c-.41.28-.79.62-1.11 1m-1.39 4.84c-.36.05-.71.08-1.08.08-4.42 0-8-3.58-8-8s3.58-8 8-8 8 3.58 8 8c0 .37-.03.72-.08 1.08.69.1 1.33.32 1.92.64.1-.56.16-1.13.16-1.72 0-5.5-4.5-10-10-10S2 6.5 2 12s4.47 10 10 10c.59 0 1.16-.06 1.72-.16-.32-.59-.54-1.23-.64-1.92M18 15v3h-3v2h3v3h2v-3h3v-2h-3v-3z"/></svg> </span> <span class="git-revision-date-localized-plugin git-revision-date-localized-plugin-datetime" title="2025年7月7日 17:10:46 UTC">2025年7月7日 17:10:46</span> </span> </aside> <form class=md-feedback name=feedback hidden> <fieldset> <legend class=md-feedback__title> 这个页面有帮助吗？ </legend> <div class=md-feedback__inner> <div class=md-feedback__list> <button class="md-feedback__icon md-icon" type=submit title=有帮助 data-md-value=1> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M20 12a8 8 0 0 0-8-8 8 8 0 0 0-8 8 8 8 0 0 0 8 8 8 8 0 0 0 8-8m2 0a10 10 0 0 1-10 10A10 10 0 0 1 2 12 10 10 0 0 1 12 2a10 10 0 0 1 10 10M10 9.5c0 .8-.7 1.5-1.5 1.5S7 10.3 7 9.5 7.7 8 8.5 8s1.5.7 1.5 1.5m7 0c0 .8-.7 1.5-1.5 1.5S14 10.3 14 9.5 14.7 8 15.5 8s1.5.7 1.5 1.5m-5 7.73c-1.75 0-3.29-.73-4.19-1.81L9.23 14c.45.72 1.52 1.23 2.77 1.23s2.32-.51 2.77-1.23l1.42 1.42c-.9 1.08-2.44 1.81-4.19 1.81"/></svg> </button> <button class="md-feedback__icon md-icon" type=submit title=需要改进 data-md-value=0> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M20 12a8 8 0 0 0-8-8 8 8 0 0 0-8 8 8 8 0 0 0 8 8 8 8 0 0 0 8-8m2 0a10 10 0 0 1-10 10A10 10 0 0 1 2 12 10 10 0 0 1 12 2a10 10 0 0 1 10 10m-6.5-4c.8 0 1.5.7 1.5 1.5s-.7 1.5-1.5 1.5-1.5-.7-1.5-1.5.7-1.5 1.5-1.5M10 9.5c0 .8-.7 1.5-1.5 1.5S7 10.3 7 9.5 7.7 8 8.5 8s1.5.7 1.5 1.5m2 4.5c1.75 0 3.29.72 4.19 1.81l-1.42 1.42C14.32 16.5 13.25 16 12 16s-2.32.5-2.77 1.23l-1.42-1.42C8.71 14.72 10.25 14 12 14"/></svg> </button> </div> <div class=md-feedback__note> <div data-md-value=1 hidden> 感谢您的反馈！ </div> <div data-md-value=0 hidden> 感谢您的反馈！请通过GitHub Issues告诉我们如何改进。 </div> </div> </div> </fieldset> </form> </article> </div> <script>var tabs=__md_get("__tabs");if(Array.isArray(tabs))e:for(var set of document.querySelectorAll(".tabbed-set")){var labels=set.querySelector(".tabbed-labels");for(var tab of tabs)for(var label of labels.getElementsByTagName("label"))if(label.innerText.trim()===tab){var input=document.getElementById(label.htmlFor);input.checked=!0;continue e}}</script> <script>var target=document.getElementById(location.hash.slice(1));target&&target.name&&(target.checked=target.name.startsWith("__tabbed_"))</script> </div> <button type=button class="md-top md-icon" data-md-component=top hidden> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M13 20h-2V8l-5.5 5.5-1.42-1.42L12 4.16l7.92 7.92-1.42 1.42L13 8z"/></svg> 回到页面顶部 </button> </main> <footer class=md-footer> <div class="md-footer-meta md-typeset"> <div class="md-footer-meta__inner md-grid"> <div class=md-copyright> <div class=md-copyright__highlight> Copyright &copy; 2025 炼妖壶项目 - MIT License </div> Made with <a href=https://squidfunk.github.io/mkdocs-material/ target=_blank rel=noopener> Material for MkDocs </a> </div> <div class=md-social> <a href=https://github.com/your-username/jixia-academy target=_blank rel=noopener title=GitHub仓库 class=md-social__link> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 496 512"><!-- Font Awesome Free 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license/free (Icons: CC BY 4.0, Fonts: SIL OFL 1.1, Code: MIT License) Copyright 2024 Fonticons, Inc.--><path d="M165.9 397.4c0 2-2.3 3.6-5.2 3.6-3.3.3-5.6-1.3-5.6-3.6 0-2 2.3-3.6 5.2-3.6 3-.3 5.6 1.3 5.6 3.6m-31.1-4.5c-.7 2 1.3 4.3 4.3 4.9 2.6 1 5.6 0 6.2-2s-1.3-4.3-4.3-5.2c-2.6-.7-5.5.3-6.2 2.3m44.2-1.7c-2.9.7-4.9 2.6-4.6 4.9.3 2 2.9 3.3 5.9 2.6 2.9-.7 4.9-2.6 4.6-4.6-.3-1.9-3-3.2-5.9-2.9M244.8 8C106.1 8 0 113.3 0 252c0 110.9 69.8 205.8 169.5 239.2 12.8 2.3 17.3-5.6 17.3-12.1 0-6.2-.3-40.4-.3-61.4 0 0-70 15-84.7-29.8 0 0-11.4-29.1-27.8-36.6 0 0-22.9-15.7 1.6-15.4 0 0 24.9 2 38.6 25.8 21.9 38.6 58.6 27.5 72.9 20.9 2.3-16 8.8-27.1 16-33.7-55.9-6.2-112.3-14.3-112.3-110.5 0-27.5 7.6-41.3 23.6-58.9-2.6-6.5-11.1-33.3 2.6-67.9 20.9-6.5 69 27 69 27 20-5.6 41.5-8.5 62.8-8.5s42.8 2.9 62.8 8.5c0 0 48.1-33.6 69-27 13.7 34.7 5.2 61.4 2.6 67.9 16 17.7 25.8 31.5 25.8 58.9 0 96.5-58.9 104.2-114.8 110.5 9.2 7.9 17 22.9 17 46.4 0 33.7-.3 75.4-.3 83.6 0 6.5 4.6 14.4 17.3 12.1C428.2 457.8 496 362.9 496 252 496 113.3 383.5 8 244.8 8M97.2 352.9c-1.3 1-1 3.3.7 5.2 1.6 1.6 3.9 2.3 5.2 1 1.3-1 1-3.3-.7-5.2-1.6-1.6-3.9-2.3-5.2-1m-10.8-8.1c-.7 1.3.3 2.9 2.3 3.9 1.6 1 3.6.7 4.3-.7.7-1.3-.3-2.9-2.3-3.9-2-.6-3.6-.3-4.3.7m32.4 35.6c-1.6 1.3-1 4.3 1.3 6.2 2.3 2.3 5.2 2.6 6.5 1 1.3-1.3.7-4.3-1.3-6.2-2.2-2.3-5.2-2.6-6.5-1m-11.4-14.7c-1.6 1-1.6 3.6 0 5.9s4.3 3.3 5.6 2.3c1.6-1.3 1.6-3.9 0-6.2-1.4-2.3-4-3.3-5.6-2"/></svg> </a> <a href=https://your-mastodon-instance.com/@taishang_laojun target=_blank rel="noopener me" title=太上老君 class=md-social__link> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 448 512"><!-- Font Awesome Free 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license/free (Icons: CC BY 4.0, Fonts: SIL OFL 1.1, Code: MIT License) Copyright 2024 Fonticons, Inc.--><path d="M433 179.11c0-97.2-63.71-125.7-63.71-125.7-62.52-28.7-228.56-28.4-290.48 0 0 0-63.72 28.5-63.72 125.7 0 115.7-6.6 259.4 105.63 289.1 40.51 10.7 75.32 13 103.33 11.4 50.81-2.8 79.32-18.1 79.32-18.1l-1.7-36.9s-36.31 11.4-77.12 10.1c-40.41-1.4-83-4.4-89.63-54a102.5 102.5 0 0 1-.9-13.9c85.63 20.9 158.65 9.1 178.75 6.7 56.12-6.7 105-41.3 111.23-72.9 9.8-49.8 9-121.5 9-121.5m-75.12 125.2h-46.63v-114.2c0-49.7-64-51.6-64 6.9v62.5h-46.33V197c0-58.5-64-56.6-64-6.9v114.2H90.19c0-122.1-5.2-147.9 18.41-175 25.9-28.9 79.82-30.8 103.83 6.1l11.6 19.5 11.6-19.5c24.11-37.1 78.12-34.8 103.83-6.1 23.71 27.3 18.4 53 18.4 175z"/></svg> </a> <a href=https://twitter.com/jixia_academy target=_blank rel=noopener title=Twitter class=md-social__link> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 512 512"><!-- Font Awesome Free 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license/free (Icons: CC BY 4.0, Fonts: SIL OFL 1.1, Code: MIT License) Copyright 2024 Fonticons, Inc.--><path d="M459.37 151.716c.325 4.548.325 9.097.325 13.645 0 138.72-105.583 298.558-298.558 298.558-59.452 0-114.68-17.219-161.137-47.106 8.447.974 16.568 1.299 25.34 1.299 49.055 0 94.213-16.568 130.274-44.832-46.132-.975-84.792-31.188-98.112-72.772 6.498.974 12.995 1.624 19.818 1.624 9.421 0 18.843-1.3 27.614-3.573-48.081-9.747-84.143-51.98-84.143-102.985v-1.299c13.969 7.797 30.214 12.67 47.431 13.319-28.264-18.843-46.781-51.005-46.781-87.391 0-19.492 5.197-37.36 14.294-52.954 51.655 63.675 129.3 105.258 216.365 109.807-1.624-7.797-2.599-15.918-2.599-24.04 0-57.828 46.782-104.934 104.934-104.934 30.213 0 57.502 12.67 76.67 33.137 23.715-4.548 46.456-13.32 66.599-25.34-7.798 24.366-24.366 44.833-46.132 57.827 21.117-2.273 41.584-8.122 60.426-16.243-14.292 20.791-32.161 39.308-52.628 54.253"/></svg> </a> <a href=mailto:<EMAIL> target=_blank rel=noopener title=联系我们 class=md-social__link> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 512 512"><!-- Font Awesome Free 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license/free (Icons: CC BY 4.0, Fonts: SIL OFL 1.1, Code: MIT License) Copyright 2024 Fonticons, Inc.--><path d="M498.1 5.6c10.1 7 15.4 19.1 13.5 31.2l-64 416c-1.5 9.7-7.4 18.2-16 23s-18.9 5.4-28 1.6L284 427.7l-68.5 74.1c-8.9 9.7-22.9 12.9-35.2 8.1S160 493.2 160 480v-83.6c0-4 1.5-7.8 4.2-10.8l167.6-182.8c5.8-6.3 5.6-16-.4-22s-15.7-6.4-22-.7L106 360.8l-88.3-44.2C7.1 311.3.3 300.7 0 288.9s5.9-22.8 16.1-28.7l448-256c10.7-6.1 23.9-5.5 34 1.4"/></svg> </a> </div> </div> </div> </footer> </div> <div class=md-dialog data-md-component=dialog> <div class="md-dialog__inner md-typeset"></div> </div> <script id=__config type=application/json>{"base": "../../..", "features": ["navigation.instant", "navigation.tracking", "navigation.tabs", "navigation.tabs.sticky", "navigation.sections", "navigation.expand", "navigation.indexes", "navigation.top", "search.highlight", "search.share", "search.suggest", "content.code.annotate", "content.code.copy", "content.tabs.link"], "search": "../../../assets/javascripts/workers/search.d50fe291.min.js", "tags": null, "translations": {"clipboard.copied": "\u5df2\u590d\u5236", "clipboard.copy": "\u590d\u5236", "search.result.more.one": "\u5728\u8be5\u9875\u4e0a\u8fd8\u6709 1 \u4e2a\u7b26\u5408\u6761\u4ef6\u7684\u7ed3\u679c", "search.result.more.other": "\u5728\u8be5\u9875\u4e0a\u8fd8\u6709 # \u4e2a\u7b26\u5408\u6761\u4ef6\u7684\u7ed3\u679c", "search.result.none": "\u6ca1\u6709\u627e\u5230\u7b26\u5408\u6761\u4ef6\u7684\u7ed3\u679c", "search.result.one": "\u627e\u5230 1 \u4e2a\u7b26\u5408\u6761\u4ef6\u7684\u7ed3\u679c", "search.result.other": "# \u4e2a\u7b26\u5408\u6761\u4ef6\u7684\u7ed3\u679c", "search.result.placeholder": "\u952e\u5165\u4ee5\u5f00\u59cb\u641c\u7d22", "search.result.term.missing": "\u7f3a\u5c11", "select.version": "\u9009\u62e9\u5f53\u524d\u7248\u672c"}, "version": {"default": "latest", "provider": "mike"}}</script> <script src=../../../assets/javascripts/bundle.56ea9cef.min.js></script> <script src=../../../assets/javascripts/mathjax.js></script> <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script> <script src=https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js></script> </body> </html>