<!doctype html><html lang=zh class=no-js> <head><meta charset=utf-8><meta name=viewport content="width=device-width,initial-scale=1"><meta name=description content="AI驱动的投资决策系统 - 稷下学宫智能体辩论平台"><meta name=author content=炼妖壶开发团队><link href=https://jingminzhang.github.io/cauldron/explanation/mathematical_foundations/rock_mechanics_theory/ rel=canonical><link rel=icon href=../../../assets/images/favicon.png><meta name=generator content="mkdocs-1.6.1, mkdocs-material-9.6.15"><title>岩石力学金融理论 - 炼妖壶 (Cauldron)</title><link rel=stylesheet href=../../../assets/stylesheets/main.342714a4.min.css><link rel=stylesheet href=../../../assets/stylesheets/palette.06af60db.min.css><link rel=preconnect href=https://fonts.gstatic.com crossorigin><link rel=stylesheet href="https://fonts.googleapis.com/css?family=Noto+Sans+SC:300,300i,400,400i,700,700i%7CJetBrains+Mono:400,400i,700,700i&display=fallback"><style>:root{--md-text-font:"Noto Sans SC";--md-code-font:"JetBrains Mono"}</style><link rel=stylesheet href=../../../assets/stylesheets/extra.css><script>__md_scope=new URL("../../..",location),__md_hash=e=>[...e].reduce(((e,_)=>(e<<5)-e+_.charCodeAt(0)),0),__md_get=(e,_=localStorage,t=__md_scope)=>JSON.parse(_.getItem(t.pathname+"."+e)),__md_set=(e,_,t=localStorage,a=__md_scope)=>{try{t.setItem(a.pathname+"."+e,JSON.stringify(_))}catch(e){}}</script><script id=__analytics>function __md_analytics(){function e(){dataLayer.push(arguments)}window.dataLayer=window.dataLayer||[],e("js",new Date),e("config","G-XXXXXXXXXX"),document.addEventListener("DOMContentLoaded",(function(){document.forms.search&&document.forms.search.query.addEventListener("blur",(function(){this.value&&e("event","search",{search_term:this.value})}));document$.subscribe((function(){var t=document.forms.feedback;if(void 0!==t)for(var a of t.querySelectorAll("[type=submit]"))a.addEventListener("click",(function(a){a.preventDefault();var n=document.location.pathname,d=this.getAttribute("data-md-value");e("event","feedback",{page:n,data:d}),t.firstElementChild.disabled=!0;var r=t.querySelector(".md-feedback__note [data-md-value='"+d+"']");r&&(r.hidden=!1)})),t.hidden=!1})),location$.subscribe((function(t){e("config","G-XXXXXXXXXX",{page_path:t.pathname})}))}));var t=document.createElement("script");t.async=!0,t.src="https://www.googletagmanager.com/gtag/js?id=G-XXXXXXXXXX",document.getElementById("__analytics").insertAdjacentElement("afterEnd",t)}</script><script>"undefined"!=typeof __md_analytics&&__md_analytics()</script></head> <body dir=ltr data-md-color-scheme=default data-md-color-primary=indigo data-md-color-accent=amber> <input class=md-toggle data-md-toggle=drawer type=checkbox id=__drawer autocomplete=off> <input class=md-toggle data-md-toggle=search type=checkbox id=__search autocomplete=off> <label class=md-overlay for=__drawer></label> <div data-md-component=skip> <a href=#_1 class=md-skip> 跳转至 </a> </div> <div data-md-component=announce> </div> <div data-md-color-scheme=default data-md-component=outdated hidden> </div> <header class="md-header md-header--shadow md-header--lifted" data-md-component=header> <nav class="md-header__inner md-grid" aria-label=页眉> <a href=../../.. title="炼妖壶 (Cauldron)" class="md-header__button md-logo" aria-label="炼妖壶 (Cauldron)" data-md-component=logo> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M12 3 1 9l11 6 9-4.91V17h2V9M5 13.18v4L12 21l7-3.82v-4L12 17z"/></svg> </a> <label class="md-header__button md-icon" for=__drawer> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M3 6h18v2H3zm0 5h18v2H3zm0 5h18v2H3z"/></svg> </label> <div class=md-header__title data-md-component=header-title> <div class=md-header__ellipsis> <div class=md-header__topic> <span class=md-ellipsis> 炼妖壶 (Cauldron) </span> </div> <div class=md-header__topic data-md-component=header-topic> <span class=md-ellipsis> 岩石力学金融理论 </span> </div> </div> </div> <form class=md-header__option data-md-component=palette> <input class=md-option data-md-color-media="(prefers-color-scheme: light)" data-md-color-scheme=default data-md-color-primary=indigo data-md-color-accent=amber aria-label=切换到暗黑模式 type=radio name=__palette id=__palette_0> <label class="md-header__button md-icon" title=切换到暗黑模式 for=__palette_1 hidden> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M12 8a4 4 0 0 0-4 4 4 4 0 0 0 4 4 4 4 0 0 0 4-4 4 4 0 0 0-4-4m0 10a6 6 0 0 1-6-6 6 6 0 0 1 6-6 6 6 0 0 1 6 6 6 6 0 0 1-6 6m8-9.31V4h-4.69L12 .69 8.69 4H4v4.69L.69 12 4 15.31V20h4.69L12 23.31 15.31 20H20v-4.69L23.31 12z"/></svg> </label> <input class=md-option data-md-color-media="(prefers-color-scheme: dark)" data-md-color-scheme=slate data-md-color-primary=indigo data-md-color-accent=amber aria-label=切换到明亮模式 type=radio name=__palette id=__palette_1> <label class="md-header__button md-icon" title=切换到明亮模式 for=__palette_0 hidden> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M12 18c-.89 0-1.74-.2-2.5-.55C11.56 16.5 13 14.42 13 12s-1.44-4.5-3.5-5.45C10.26 6.2 11.11 6 12 6a6 6 0 0 1 6 6 6 6 0 0 1-6 6m8-9.31V4h-4.69L12 .69 8.69 4H4v4.69L.69 12 4 15.31V20h4.69L12 23.31 15.31 20H20v-4.69L23.31 12z"/></svg> </label> </form> <script>var palette=__md_get("__palette");if(palette&&palette.color){if("(prefers-color-scheme)"===palette.color.media){var media=matchMedia("(prefers-color-scheme: light)"),input=document.querySelector(media.matches?"[data-md-color-media='(prefers-color-scheme: light)']":"[data-md-color-media='(prefers-color-scheme: dark)']");palette.color.media=input.getAttribute("data-md-color-media"),palette.color.scheme=input.getAttribute("data-md-color-scheme"),palette.color.primary=input.getAttribute("data-md-color-primary"),palette.color.accent=input.getAttribute("data-md-color-accent")}for(var[key,value]of Object.entries(palette.color))document.body.setAttribute("data-md-color-"+key,value)}</script> <label class="md-header__button md-icon" for=__search> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M9.5 3A6.5 6.5 0 0 1 16 9.5c0 1.61-.59 3.09-1.56 4.23l.27.27h.79l5 5-1.5 1.5-5-5v-.79l-.27-.27A6.52 6.52 0 0 1 9.5 16 6.5 6.5 0 0 1 3 9.5 6.5 6.5 0 0 1 9.5 3m0 2C7 5 5 7 5 9.5S7 14 9.5 14 14 12 14 9.5 12 5 9.5 5"/></svg> </label> <div class=md-search data-md-component=search role=dialog> <label class=md-search__overlay for=__search></label> <div class=md-search__inner role=search> <form class=md-search__form name=search> <input type=text class=md-search__input name=query aria-label=搜索 placeholder=搜索 autocapitalize=off autocorrect=off autocomplete=off spellcheck=false data-md-component=search-query required> <label class="md-search__icon md-icon" for=__search> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M9.5 3A6.5 6.5 0 0 1 16 9.5c0 1.61-.59 3.09-1.56 4.23l.27.27h.79l5 5-1.5 1.5-5-5v-.79l-.27-.27A6.52 6.52 0 0 1 9.5 16 6.5 6.5 0 0 1 3 9.5 6.5 6.5 0 0 1 9.5 3m0 2C7 5 5 7 5 9.5S7 14 9.5 14 14 12 14 9.5 12 5 9.5 5"/></svg> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M20 11v2H8l5.5 5.5-1.42 1.42L4.16 12l7.92-7.92L13.5 5.5 8 11z"/></svg> </label> <nav class=md-search__options aria-label=查找> <a href=javascript:void(0) class="md-search__icon md-icon" title=分享 aria-label=分享 data-clipboard data-clipboard-text data-md-component=search-share tabindex=-1> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M18 16.08c-.76 0-1.44.3-1.96.77L8.91 12.7c.05-.23.09-.46.09-.7s-.04-.47-.09-.7l7.05-4.11c.54.5 1.25.81 2.04.81a3 3 0 0 0 3-3 3 3 0 0 0-3-3 3 3 0 0 0-3 3c0 .***********.7L8.04 9.81C7.5 9.31 6.79 9 6 9a3 3 0 0 0-3 3 3 3 0 0 0 3 3c.79 0 1.5-.31 2.04-.81l7.12 4.15c-.05.21-.08.43-.08.66 0 1.61 1.31 2.91 2.92 2.91s2.92-1.3 2.92-2.91A2.92 2.92 0 0 0 18 16.08"/></svg> </a> <button type=reset class="md-search__icon md-icon" title=清空当前内容 aria-label=清空当前内容 tabindex=-1> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M19 6.41 17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/></svg> </button> </nav> <div class=md-search__suggest data-md-component=search-suggest></div> </form> <div class=md-search__output> <div class=md-search__scrollwrap tabindex=0 data-md-scrollfix> <div class=md-search-result data-md-component=search-result> <div class=md-search-result__meta> 正在初始化搜索引擎 </div> <ol class=md-search-result__list role=presentation></ol> </div> </div> </div> </div> </div> <div class=md-header__source> <a href=https://github.com/jingminzhang/cauldron title=前往仓库 class=md-source data-md-component=source> <div class="md-source__icon md-icon"> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 496 512"><!-- Font Awesome Free 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license/free (Icons: CC BY 4.0, Fonts: SIL OFL 1.1, Code: MIT License) Copyright 2024 Fonticons, Inc.--><path d="M165.9 397.4c0 2-2.3 3.6-5.2 3.6-3.3.3-5.6-1.3-5.6-3.6 0-2 2.3-3.6 5.2-3.6 3-.3 5.6 1.3 5.6 3.6m-31.1-4.5c-.7 2 1.3 4.3 4.3 4.9 2.6 1 5.6 0 6.2-2s-1.3-4.3-4.3-5.2c-2.6-.7-5.5.3-6.2 2.3m44.2-1.7c-2.9.7-4.9 2.6-4.6 4.9.3 2 2.9 3.3 5.9 2.6 2.9-.7 4.9-2.6 4.6-4.6-.3-1.9-3-3.2-5.9-2.9M244.8 8C106.1 8 0 113.3 0 252c0 110.9 69.8 205.8 169.5 239.2 12.8 2.3 17.3-5.6 17.3-12.1 0-6.2-.3-40.4-.3-61.4 0 0-70 15-84.7-29.8 0 0-11.4-29.1-27.8-36.6 0 0-22.9-15.7 1.6-15.4 0 0 24.9 2 38.6 25.8 21.9 38.6 58.6 27.5 72.9 20.9 2.3-16 8.8-27.1 16-33.7-55.9-6.2-112.3-14.3-112.3-110.5 0-27.5 7.6-41.3 23.6-58.9-2.6-6.5-11.1-33.3 2.6-67.9 20.9-6.5 69 27 69 27 20-5.6 41.5-8.5 62.8-8.5s42.8 2.9 62.8 8.5c0 0 48.1-33.6 69-27 13.7 34.7 5.2 61.4 2.6 67.9 16 17.7 25.8 31.5 25.8 58.9 0 96.5-58.9 104.2-114.8 110.5 9.2 7.9 17 22.9 17 46.4 0 33.7-.3 75.4-.3 83.6 0 6.5 4.6 14.4 17.3 12.1C428.2 457.8 496 362.9 496 252 496 113.3 383.5 8 244.8 8M97.2 352.9c-1.3 1-1 3.3.7 5.2 1.6 1.6 3.9 2.3 5.2 1 1.3-1 1-3.3-.7-5.2-1.6-1.6-3.9-2.3-5.2-1m-10.8-8.1c-.7 1.3.3 2.9 2.3 3.9 1.6 1 3.6.7 4.3-.7.7-1.3-.3-2.9-2.3-3.9-2-.6-3.6-.3-4.3.7m32.4 35.6c-1.6 1.3-1 4.3 1.3 6.2 2.3 2.3 5.2 2.6 6.5 1 1.3-1.3.7-4.3-1.3-6.2-2.2-2.3-5.2-2.6-6.5-1m-11.4-14.7c-1.6 1-1.6 3.6 0 5.9s4.3 3.3 5.6 2.3c1.6-1.3 1.6-3.9 0-6.2-1.4-2.3-4-3.3-5.6-2"/></svg> </div> <div class=md-source__repository> jingminzhang/cauldron </div> </a> </div> </nav> <nav class=md-tabs aria-label=标签 data-md-component=tabs> <div class=md-grid> <ul class=md-tabs__list> <li class=md-tabs__item> <a href=../../.. class=md-tabs__link> 🏠 首页 </a> </li> <li class=md-tabs__item> <a href=../../../getting-started/quick-start/ class=md-tabs__link> 🚀 快速开始 </a> </li> <li class=md-tabs__item> <a href=../../../reference/ai_personalities/ class=md-tabs__link> 🎭 AI仙人 </a> </li> <li class=md-tabs__item> <a href=../../../architecture/ARCHITECTURE_NOTES/ class=md-tabs__link> 🏗️ 架构设计 </a> </li> <li class=md-tabs__item> <a href=../../../how-to-guides/features/ class=md-tabs__link> ⚡ 核心功能 </a> </li> <li class=md-tabs__item> <a href=../../../reference/database_overview/ class=md-tabs__link> 🔧 技术文档 </a> </li> <li class=md-tabs__item> <a href=../../../vision/three-tiers/ class=md-tabs__link> 📜 项目愿景 </a> </li> <li class=md-tabs__item> <a href=../../../how-to-guides/deployment/ class=md-tabs__link> 🚀 部署指南 </a> </li> <li class=md-tabs__item> <a href=../../../api/ class=md-tabs__link> 📡 API文档 </a> </li> <li class=md-tabs__item> <a href=../../../CONTRIBUTING/ class=md-tabs__link> 🤝 贡献指南 </a> </li> </ul> </div> </nav> </header> <div class=md-container data-md-component=container> <main class=md-main data-md-component=main> <div class="md-main__inner md-grid"> <div class="md-sidebar md-sidebar--primary" data-md-component=sidebar data-md-type=navigation> <div class=md-sidebar__scrollwrap> <div class=md-sidebar__inner> <nav class="md-nav md-nav--primary md-nav--lifted" aria-label=导航栏 data-md-level=0> <label class=md-nav__title for=__drawer> <a href=../../.. title="炼妖壶 (Cauldron)" class="md-nav__button md-logo" aria-label="炼妖壶 (Cauldron)" data-md-component=logo> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M12 3 1 9l11 6 9-4.91V17h2V9M5 13.18v4L12 21l7-3.82v-4L12 17z"/></svg> </a> 炼妖壶 (Cauldron) </label> <div class=md-nav__source> <a href=https://github.com/jingminzhang/cauldron title=前往仓库 class=md-source data-md-component=source> <div class="md-source__icon md-icon"> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 496 512"><!-- Font Awesome Free 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license/free (Icons: CC BY 4.0, Fonts: SIL OFL 1.1, Code: MIT License) Copyright 2024 Fonticons, Inc.--><path d="M165.9 397.4c0 2-2.3 3.6-5.2 3.6-3.3.3-5.6-1.3-5.6-3.6 0-2 2.3-3.6 5.2-3.6 3-.3 5.6 1.3 5.6 3.6m-31.1-4.5c-.7 2 1.3 4.3 4.3 4.9 2.6 1 5.6 0 6.2-2s-1.3-4.3-4.3-5.2c-2.6-.7-5.5.3-6.2 2.3m44.2-1.7c-2.9.7-4.9 2.6-4.6 4.9.3 2 2.9 3.3 5.9 2.6 2.9-.7 4.9-2.6 4.6-4.6-.3-1.9-3-3.2-5.9-2.9M244.8 8C106.1 8 0 113.3 0 252c0 110.9 69.8 205.8 169.5 239.2 12.8 2.3 17.3-5.6 17.3-12.1 0-6.2-.3-40.4-.3-61.4 0 0-70 15-84.7-29.8 0 0-11.4-29.1-27.8-36.6 0 0-22.9-15.7 1.6-15.4 0 0 24.9 2 38.6 25.8 21.9 38.6 58.6 27.5 72.9 20.9 2.3-16 8.8-27.1 16-33.7-55.9-6.2-112.3-14.3-112.3-110.5 0-27.5 7.6-41.3 23.6-58.9-2.6-6.5-11.1-33.3 2.6-67.9 20.9-6.5 69 27 69 27 20-5.6 41.5-8.5 62.8-8.5s42.8 2.9 62.8 8.5c0 0 48.1-33.6 69-27 13.7 34.7 5.2 61.4 2.6 67.9 16 17.7 25.8 31.5 25.8 58.9 0 96.5-58.9 104.2-114.8 110.5 9.2 7.9 17 22.9 17 46.4 0 33.7-.3 75.4-.3 83.6 0 6.5 4.6 14.4 17.3 12.1C428.2 457.8 496 362.9 496 252 496 113.3 383.5 8 244.8 8M97.2 352.9c-1.3 1-1 3.3.7 5.2 1.6 1.6 3.9 2.3 5.2 1 1.3-1 1-3.3-.7-5.2-1.6-1.6-3.9-2.3-5.2-1m-10.8-8.1c-.7 1.3.3 2.9 2.3 3.9 1.6 1 3.6.7 4.3-.7.7-1.3-.3-2.9-2.3-3.9-2-.6-3.6-.3-4.3.7m32.4 35.6c-1.6 1.3-1 4.3 1.3 6.2 2.3 2.3 5.2 2.6 6.5 1 1.3-1.3.7-4.3-1.3-6.2-2.2-2.3-5.2-2.6-6.5-1m-11.4-14.7c-1.6 1-1.6 3.6 0 5.9s4.3 3.3 5.6 2.3c1.6-1.3 1.6-3.9 0-6.2-1.4-2.3-4-3.3-5.6-2"/></svg> </div> <div class=md-source__repository> jingminzhang/cauldron </div> </a> </div> <ul class=md-nav__list data-md-scrollfix> <li class="md-nav__item md-nav__item--nested"> <input class="md-nav__toggle md-toggle md-toggle--indeterminate" type=checkbox id=__nav_1> <div class="md-nav__link md-nav__container"> <a href=../../.. class="md-nav__link "> <span class=md-ellipsis> 🏠 首页 </span> </a> <label class="md-nav__link " for=__nav_1 id=__nav_1_label tabindex=0> <span class="md-nav__icon md-icon"></span> </label> </div> <nav class=md-nav data-md-level=1 aria-labelledby=__nav_1_label aria-expanded=false> <label class=md-nav__title for=__nav_1> <span class="md-nav__icon md-icon"></span> 🏠 首页 </label> <ul class=md-nav__list data-md-scrollfix> <li class=md-nav__item> <a href=../../../roadmap/ class=md-nav__link> <span class=md-ellipsis> 发展路线图 </span> </a> </li> </ul> </nav> </li> <li class="md-nav__item md-nav__item--nested"> <input class="md-nav__toggle md-toggle md-toggle--indeterminate" type=checkbox id=__nav_2> <label class=md-nav__link for=__nav_2 id=__nav_2_label tabindex=0> <span class=md-ellipsis> 🚀 快速开始 </span> <span class="md-nav__icon md-icon"></span> </label> <nav class=md-nav data-md-level=1 aria-labelledby=__nav_2_label aria-expanded=false> <label class=md-nav__title for=__nav_2> <span class="md-nav__icon md-icon"></span> 🚀 快速开始 </label> <ul class=md-nav__list data-md-scrollfix> <li class=md-nav__item> <a href=../../../getting-started/quick-start/ class=md-nav__link> <span class=md-ellipsis> 快速开始 </span> </a> </li> <li class=md-nav__item> <a href=../../../tutorials/getting_started/ class=md-nav__link> <span class=md-ellipsis> 入门教程 </span> </a> </li> <li class=md-nav__item> <a href=../../../how-to-guides/getting_started/ class=md-nav__link> <span class=md-ellipsis> 使用指南 </span> </a> </li> </ul> </nav> </li> <li class="md-nav__item md-nav__item--nested"> <input class="md-nav__toggle md-toggle md-toggle--indeterminate" type=checkbox id=__nav_3> <label class=md-nav__link for=__nav_3 id=__nav_3_label tabindex=0> <span class=md-ellipsis> 🎭 AI仙人 </span> <span class="md-nav__icon md-icon"></span> </label> <nav class=md-nav data-md-level=1 aria-labelledby=__nav_3_label aria-expanded=false> <label class=md-nav__title for=__nav_3> <span class="md-nav__icon md-icon"></span> 🎭 AI仙人 </label> <ul class=md-nav__list data-md-scrollfix> <li class=md-nav__item> <a href=../../../reference/ai_personalities/ class=md-nav__link> <span class=md-ellipsis> AI人格设计 </span> </a> </li> </ul> </nav> </li> <li class="md-nav__item md-nav__item--nested"> <input class="md-nav__toggle md-toggle md-toggle--indeterminate" type=checkbox id=__nav_4> <label class=md-nav__link for=__nav_4 id=__nav_4_label tabindex=0> <span class=md-ellipsis> 🏗️ 架构设计 </span> <span class="md-nav__icon md-icon"></span> </label> <nav class=md-nav data-md-level=1 aria-labelledby=__nav_4_label aria-expanded=false> <label class=md-nav__title for=__nav_4> <span class="md-nav__icon md-icon"></span> 🏗️ 架构设计 </label> <ul class=md-nav__list data-md-scrollfix> <li class=md-nav__item> <a href=../../../architecture/ARCHITECTURE_NOTES/ class=md-nav__link> <span class=md-ellipsis> 架构笔记 </span> </a> </li> <li class=md-nav__item> <a href=../../../architecture/Omnipresent_AI_Agent_Architecture/ class=md-nav__link> <span class=md-ellipsis> 全能AI智能体架构 </span> </a> </li> </ul> </nav> </li> <li class="md-nav__item md-nav__item--nested"> <input class="md-nav__toggle md-toggle md-toggle--indeterminate" type=checkbox id=__nav_5> <label class=md-nav__link for=__nav_5 id=__nav_5_label tabindex=0> <span class=md-ellipsis> ⚡ 核心功能 </span> <span class="md-nav__icon md-icon"></span> </label> <nav class=md-nav data-md-level=1 aria-labelledby=__nav_5_label aria-expanded=false> <label class=md-nav__title for=__nav_5> <span class="md-nav__icon md-icon"></span> ⚡ 核心功能 </label> <ul class=md-nav__list data-md-scrollfix> <li class=md-nav__item> <a href=../../../how-to-guides/features/ class=md-nav__link> <span class=md-ellipsis> 功能概览 </span> </a> </li> <li class=md-nav__item> <a href=../../../how-to-guides/offering/ class=md-nav__link> <span class=md-ellipsis> 产品定位 </span> </a> </li> </ul> </nav> </li> <li class="md-nav__item md-nav__item--nested"> <input class="md-nav__toggle md-toggle md-toggle--indeterminate" type=checkbox id=__nav_6> <label class=md-nav__link for=__nav_6 id=__nav_6_label tabindex=0> <span class=md-ellipsis> 🔧 技术文档 </span> <span class="md-nav__icon md-icon"></span> </label> <nav class=md-nav data-md-level=1 aria-labelledby=__nav_6_label aria-expanded=false> <label class=md-nav__title for=__nav_6> <span class="md-nav__icon md-icon"></span> 🔧 技术文档 </label> <ul class=md-nav__list data-md-scrollfix> <li class=md-nav__item> <a href=../../../reference/database_overview/ class=md-nav__link> <span class=md-ellipsis> 数据库概览 </span> </a> </li> <li class=md-nav__item> <a href=../../../guides/MCP_SETUP/ class=md-nav__link> <span class=md-ellipsis> MCP设置 </span> </a> </li> <li class=md-nav__item> <a href=../../../how-to-guides/ib_fundamentals_guide/ class=md-nav__link> <span class=md-ellipsis> IB基础指南 </span> </a> </li> </ul> </nav> </li> <li class="md-nav__item md-nav__item--nested"> <input class="md-nav__toggle md-toggle md-toggle--indeterminate" type=checkbox id=__nav_7> <label class=md-nav__link for=__nav_7 id=__nav_7_label tabindex=0> <span class=md-ellipsis> 📜 项目愿景 </span> <span class="md-nav__icon md-icon"></span> </label> <nav class=md-nav data-md-level=1 aria-labelledby=__nav_7_label aria-expanded=false> <label class=md-nav__title for=__nav_7> <span class="md-nav__icon md-icon"></span> 📜 项目愿景 </label> <ul class=md-nav__list data-md-scrollfix> <li class=md-nav__item> <a href=../../../vision/three-tiers/ class=md-nav__link> <span class=md-ellipsis> 三层架构 </span> </a> </li> <li class=md-nav__item> <a href=../../../guides/OPENSOURCE_ROADMAP/ class=md-nav__link> <span class=md-ellipsis> 开源路线图 </span> </a> </li> </ul> </nav> </li> <li class="md-nav__item md-nav__item--nested"> <input class="md-nav__toggle md-toggle md-toggle--indeterminate" type=checkbox id=__nav_8> <label class=md-nav__link for=__nav_8 id=__nav_8_label tabindex=0> <span class=md-ellipsis> 🚀 部署指南 </span> <span class="md-nav__icon md-icon"></span> </label> <nav class=md-nav data-md-level=1 aria-labelledby=__nav_8_label aria-expanded=false> <label class=md-nav__title for=__nav_8> <span class="md-nav__icon md-icon"></span> 🚀 部署指南 </label> <ul class=md-nav__list data-md-scrollfix> <li class=md-nav__item> <a href=../../../how-to-guides/deployment/ class=md-nav__link> <span class=md-ellipsis> 部署指南 </span> </a> </li> </ul> </nav> </li> <li class="md-nav__item md-nav__item--nested"> <input class="md-nav__toggle md-toggle md-toggle--indeterminate" type=checkbox id=__nav_9> <label class=md-nav__link for=__nav_9 id=__nav_9_label tabindex=0> <span class=md-ellipsis> 📡 API文档 </span> <span class="md-nav__icon md-icon"></span> </label> <nav class=md-nav data-md-level=1 aria-labelledby=__nav_9_label aria-expanded=false> <label class=md-nav__title for=__nav_9> <span class="md-nav__icon md-icon"></span> 📡 API文档 </label> <ul class=md-nav__list data-md-scrollfix> <li class=md-nav__item> <a href=../../../api/ class=md-nav__link> <span class=md-ellipsis> API参考 </span> </a> </li> </ul> </nav> </li> <li class="md-nav__item md-nav__item--nested"> <input class="md-nav__toggle md-toggle md-toggle--indeterminate" type=checkbox id=__nav_10> <label class=md-nav__link for=__nav_10 id=__nav_10_label tabindex=0> <span class=md-ellipsis> 🤝 贡献指南 </span> <span class="md-nav__icon md-icon"></span> </label> <nav class=md-nav data-md-level=1 aria-labelledby=__nav_10_label aria-expanded=false> <label class=md-nav__title for=__nav_10> <span class="md-nav__icon md-icon"></span> 🤝 贡献指南 </label> <ul class=md-nav__list data-md-scrollfix> <li class=md-nav__item> <a href=../../../CONTRIBUTING/ class=md-nav__link> <span class=md-ellipsis> 贡献指南 </span> </a> </li> <li class=md-nav__item> <a href=../../../guides/DOCS_REORGANIZATION_PLAN/ class=md-nav__link> <span class=md-ellipsis> 文档重组计划 </span> </a> </li> </ul> </nav> </li> </ul> </nav> </div> </div> </div> <div class="md-sidebar md-sidebar--secondary" data-md-component=sidebar data-md-type=toc> <div class=md-sidebar__scrollwrap> <div class=md-sidebar__inner> <nav class="md-nav md-nav--secondary" aria-label=目录> <label class=md-nav__title for=__toc> <span class="md-nav__icon md-icon"></span> 目录 </label> <ul class=md-nav__list data-md-component=toc data-md-scrollfix> <li class=md-nav__item> <a href=#_2 class=md-nav__link> <span class=md-ellipsis> 🏔️ 理论基础 </span> </a> <nav class=md-nav aria-label="🏔️ 理论基础"> <ul class=md-nav__list> <li class=md-nav__item> <a href=#_3 class=md-nav__link> <span class=md-ellipsis> 核心思想 </span> </a> </li> <li class=md-nav__item> <a href=#_4 class=md-nav__link> <span class=md-ellipsis> 基本假设 </span> </a> </li> </ul> </nav> </li> <li class=md-nav__item> <a href=#_5 class=md-nav__link> <span class=md-ellipsis> 📐 数学建模 </span> </a> <nav class=md-nav aria-label="📐 数学建模"> <ul class=md-nav__list> <li class=md-nav__item> <a href=#- class=md-nav__link> <span class=md-ellipsis> 应力-应变基本关系 </span> </a> </li> <li class=md-nav__item> <a href=#_6 class=md-nav__link> <span class=md-ellipsis> 金融市场映射 </span> </a> </li> <li class=md-nav__item> <a href=#_7 class=md-nav__link> <span class=md-ellipsis> 蠕变方程 </span> </a> </li> </ul> </nav> </li> <li class=md-nav__item> <a href=#_8 class=md-nav__link> <span class=md-ellipsis> 🔍 应力分析框架 </span> </a> <nav class=md-nav aria-label="🔍 应力分析框架"> <ul class=md-nav__list> <li class=md-nav__item> <a href=#_9 class=md-nav__link> <span class=md-ellipsis> 主应力计算 </span> </a> </li> <li class=md-nav__item> <a href=#_10 class=md-nav__link> <span class=md-ellipsis> 莫尔圆分析 </span> </a> </li> </ul> </nav> </li> <li class=md-nav__item> <a href=#_11 class=md-nav__link> <span class=md-ellipsis> ⚡ 断裂力学模型 </span> </a> <nav class=md-nav aria-label="⚡ 断裂力学模型"> <ul class=md-nav__list> <li class=md-nav__item> <a href=#_12 class=md-nav__link> <span class=md-ellipsis> 格里菲斯断裂准则 </span> </a> </li> <li class=md-nav__item> <a href=#_13 class=md-nav__link> <span class=md-ellipsis> 金融市场断裂模型 </span> </a> </li> <li class=md-nav__item> <a href=#_14 class=md-nav__link> <span class=md-ellipsis> 疲劳断裂模型 </span> </a> </li> </ul> </nav> </li> <li class=md-nav__item> <a href=#_15 class=md-nav__link> <span class=md-ellipsis> 🌊 多尺度分析 </span> </a> <nav class=md-nav aria-label="🌊 多尺度分析"> <ul class=md-nav__list> <li class=md-nav__item> <a href=#_16 class=md-nav__link> <span class=md-ellipsis> 宏观尺度（市场整体） </span> </a> </li> <li class=md-nav__item> <a href=#_17 class=md-nav__link> <span class=md-ellipsis> 中观尺度（行业板块） </span> </a> </li> <li class=md-nav__item> <a href=#_18 class=md-nav__link> <span class=md-ellipsis> 微观尺度（个股） </span> </a> </li> </ul> </nav> </li> <li class=md-nav__item> <a href=#_19 class=md-nav__link> <span class=md-ellipsis> 🎯 实际应用 </span> </a> <nav class=md-nav aria-label="🎯 实际应用"> <ul class=md-nav__list> <li class=md-nav__item> <a href=#_20 class=md-nav__link> <span class=md-ellipsis> 应力监测指标 </span> </a> </li> <li class=md-nav__item> <a href=#_21 class=md-nav__link> <span class=md-ellipsis> 风险预警阈值 </span> </a> </li> <li class=md-nav__item> <a href=#_22 class=md-nav__link> <span class=md-ellipsis> 策略应用 </span> </a> </li> </ul> </nav> </li> <li class=md-nav__item> <a href=#_23 class=md-nav__link> <span class=md-ellipsis> 📊 验证与校准 </span> </a> <nav class=md-nav aria-label="📊 验证与校准"> <ul class=md-nav__list> <li class=md-nav__item> <a href=#_24 class=md-nav__link> <span class=md-ellipsis> 历史数据验证 </span> </a> </li> <li class=md-nav__item> <a href=#_25 class=md-nav__link> <span class=md-ellipsis> 模型校准方法 </span> </a> </li> </ul> </nav> </li> <li class=md-nav__item> <a href=#_26 class=md-nav__link> <span class=md-ellipsis> 🔮 未来发展 </span> </a> <nav class=md-nav aria-label="🔮 未来发展"> <ul class=md-nav__list> <li class=md-nav__item> <a href=#_27 class=md-nav__link> <span class=md-ellipsis> 理论扩展 </span> </a> </li> <li class=md-nav__item> <a href=#_28 class=md-nav__link> <span class=md-ellipsis> 技术创新 </span> </a> </li> </ul> </nav> </li> <li class=md-nav__item> <a href=#_29 class=md-nav__link> <span class=md-ellipsis> 参考文献 </span> </a> </li> </ul> </nav> </div> </div> </div> <div class=md-content data-md-component=content> <article class="md-content__inner md-typeset"> <h1 id=_1>岩石力学金融理论<a class=headerlink href=#_1 title="Permanent link">&para;</a></h1> <h2 id=_2>🏔️ 理论基础<a class=headerlink href=#_2 title="Permanent link">&para;</a></h2> <h3 id=_3>核心思想<a class=headerlink href=#_3 title="Permanent link">&para;</a></h3> <p>岩石力学金融理论将金融市场视为一个复杂的岩石体系统，通过应力-应变关系来理解市场行为。这不仅仅是一个比喻，而是基于两个复杂系统在本质上的相似性。</p> <h3 id=_4>基本假设<a class=headerlink href=#_4 title="Permanent link">&para;</a></h3> <ol> <li><strong>市场连续性假设</strong>：市场如同连续介质，可以用连续力学方法分析</li> <li><strong>应力传递假设</strong>：外部冲击通过市场结构传递，类似于岩石中的应力传播</li> <li><strong>临界状态假设</strong>：市场存在临界破坏状态，超过则发生系统性风险</li> </ol> <h2 id=_5>📐 数学建模<a class=headerlink href=#_5 title="Permanent link">&para;</a></h2> <h3 id=->应力-应变基本关系<a class=headerlink href=#- title="Permanent link">&para;</a></h3> <p>在岩石力学中，应力-应变关系遵循胡克定律的推广形式：</p> <div class=arithmatex>\[\sigma_{ij} = C_{ijkl} \varepsilon_{kl}\]</div> <p>其中： - <span class=arithmatex>\(\sigma_{ij}\)</span>：应力张量 - <span class=arithmatex>\(\varepsilon_{kl}\)</span>：应变张量<br> - <span class=arithmatex>\(C_{ijkl}\)</span>：弹性常数张量</p> <h3 id=_6>金融市场映射<a class=headerlink href=#_6 title="Permanent link">&para;</a></h3> <p>将此关系映射到金融市场：</p> <div class=arithmatex>\[P_{ij}(t) = M_{ijkl} V_{kl}(t) + \eta_{ij}(t)\]</div> <p>其中： - <span class=arithmatex>\(P_{ij}(t)\)</span>：价格应力张量（价格变化率矩阵） - <span class=arithmatex>\(V_{kl}(t)\)</span>：成交量应变张量（成交量变化率矩阵） - <span class=arithmatex>\(M_{ijkl}\)</span>：市场弹性模量张量 - <span class=arithmatex>\(\eta_{ij}(t)\)</span>：随机扰动项</p> <h3 id=_7>蠕变方程<a class=headerlink href=#_7 title="Permanent link">&para;</a></h3> <p>岩石在持续应力作用下的蠕变行为可以用以下方程描述：</p> <div class=arithmatex>\[\frac{d\varepsilon}{dt} = A\sigma^n \exp\left(-\frac{Q}{RT}\right)\]</div> <p>金融市场的对应形式：</p> <div class=arithmatex>\[\frac{dR_t}{dt} = A \cdot \text{Stress}_t^n \cdot \exp\left(-\frac{E_a}{k_B T_{market}}\right)\]</div> <p>其中： - <span class=arithmatex>\(R_t\)</span>：市场收益率 - <span class=arithmatex>\(\text{Stress}_t\)</span>：市场应力水平 - <span class=arithmatex>\(E_a\)</span>：市场激活能 - <span class=arithmatex>\(T_{market}\)</span>：市场"温度"（波动性的度量） - <span class=arithmatex>\(A, n\)</span>：材料常数（市场特征参数）</p> <h2 id=_8>🔍 应力分析框架<a class=headerlink href=#_8 title="Permanent link">&para;</a></h2> <h3 id=_9>主应力计算<a class=headerlink href=#_9 title="Permanent link">&para;</a></h3> <p>对于二维市场应力状态，主应力为：</p> <div class=arithmatex>\[\sigma_1, \sigma_2 = \frac{\sigma_x + \sigma_y}{2} \pm \sqrt{\left(\frac{\sigma_x - \sigma_y}{2}\right)^2 + \tau_{xy}^2}\]</div> <p>其中： - <span class=arithmatex>\(\sigma_x\)</span>：横向市场应力（如行业压力） - <span class=arithmatex>\(\sigma_y\)</span>：纵向市场应力（如政策压力） - <span class=arithmatex>\(\tau_{xy}\)</span>：剪切应力（如跨市场传染）</p> <h3 id=_10>莫尔圆分析<a class=headerlink href=#_10 title="Permanent link">&para;</a></h3> <p>市场应力状态可以用莫尔圆表示：</p> <div class="language-python highlight"><pre><span></span><code><span id=__span-0-1><a id=__codelineno-0-1 name=__codelineno-0-1 href=#__codelineno-0-1></a><span class=k>def</span><span class=w> </span><span class=nf>mohr_circle_analysis</span><span class=p>(</span><span class=n>sigma_x</span><span class=p>,</span> <span class=n>sigma_y</span><span class=p>,</span> <span class=n>tau_xy</span><span class=p>):</span>
</span><span id=__span-0-2><a id=__codelineno-0-2 name=__codelineno-0-2 href=#__codelineno-0-2></a><span class=w>    </span><span class=sd>&quot;&quot;&quot;</span>
</span><span id=__span-0-3><a id=__codelineno-0-3 name=__codelineno-0-3 href=#__codelineno-0-3></a><span class=sd>    莫尔圆分析市场应力状态</span>
</span><span id=__span-0-4><a id=__codelineno-0-4 name=__codelineno-0-4 href=#__codelineno-0-4></a><span class=sd>    &quot;&quot;&quot;</span>
</span><span id=__span-0-5><a id=__codelineno-0-5 name=__codelineno-0-5 href=#__codelineno-0-5></a>    <span class=c1># 圆心坐标</span>
</span><span id=__span-0-6><a id=__codelineno-0-6 name=__codelineno-0-6 href=#__codelineno-0-6></a>    <span class=n>center</span> <span class=o>=</span> <span class=p>(</span><span class=n>sigma_x</span> <span class=o>+</span> <span class=n>sigma_y</span><span class=p>)</span> <span class=o>/</span> <span class=mi>2</span>
</span><span id=__span-0-7><a id=__codelineno-0-7 name=__codelineno-0-7 href=#__codelineno-0-7></a>
</span><span id=__span-0-8><a id=__codelineno-0-8 name=__codelineno-0-8 href=#__codelineno-0-8></a>    <span class=c1># 半径</span>
</span><span id=__span-0-9><a id=__codelineno-0-9 name=__codelineno-0-9 href=#__codelineno-0-9></a>    <span class=n>radius</span> <span class=o>=</span> <span class=n>np</span><span class=o>.</span><span class=n>sqrt</span><span class=p>(((</span><span class=n>sigma_x</span> <span class=o>-</span> <span class=n>sigma_y</span><span class=p>)</span> <span class=o>/</span> <span class=mi>2</span><span class=p>)</span><span class=o>**</span><span class=mi>2</span> <span class=o>+</span> <span class=n>tau_xy</span><span class=o>**</span><span class=mi>2</span><span class=p>)</span>
</span><span id=__span-0-10><a id=__codelineno-0-10 name=__codelineno-0-10 href=#__codelineno-0-10></a>
</span><span id=__span-0-11><a id=__codelineno-0-11 name=__codelineno-0-11 href=#__codelineno-0-11></a>    <span class=c1># 主应力</span>
</span><span id=__span-0-12><a id=__codelineno-0-12 name=__codelineno-0-12 href=#__codelineno-0-12></a>    <span class=n>sigma_1</span> <span class=o>=</span> <span class=n>center</span> <span class=o>+</span> <span class=n>radius</span>
</span><span id=__span-0-13><a id=__codelineno-0-13 name=__codelineno-0-13 href=#__codelineno-0-13></a>    <span class=n>sigma_2</span> <span class=o>=</span> <span class=n>center</span> <span class=o>-</span> <span class=n>radius</span>
</span><span id=__span-0-14><a id=__codelineno-0-14 name=__codelineno-0-14 href=#__codelineno-0-14></a>
</span><span id=__span-0-15><a id=__codelineno-0-15 name=__codelineno-0-15 href=#__codelineno-0-15></a>    <span class=c1># 最大剪应力</span>
</span><span id=__span-0-16><a id=__codelineno-0-16 name=__codelineno-0-16 href=#__codelineno-0-16></a>    <span class=n>tau_max</span> <span class=o>=</span> <span class=n>radius</span>
</span><span id=__span-0-17><a id=__codelineno-0-17 name=__codelineno-0-17 href=#__codelineno-0-17></a>
</span><span id=__span-0-18><a id=__codelineno-0-18 name=__codelineno-0-18 href=#__codelineno-0-18></a>    <span class=k>return</span> <span class=n>sigma_1</span><span class=p>,</span> <span class=n>sigma_2</span><span class=p>,</span> <span class=n>tau_max</span>
</span></code></pre></div> <h2 id=_11>⚡ 断裂力学模型<a class=headerlink href=#_11 title="Permanent link">&para;</a></h2> <h3 id=_12>格里菲斯断裂准则<a class=headerlink href=#_12 title="Permanent link">&para;</a></h3> <p>岩石断裂的能量准则：</p> <div class=arithmatex>\[\sigma_c = \sqrt{\frac{2E\gamma}{\pi a}}\]</div> <p>其中： - <span class=arithmatex>\(\sigma_c\)</span>：临界应力 - <span class=arithmatex>\(E\)</span>：弹性模量 - <span class=arithmatex>\(\gamma\)</span>：表面能 - <span class=arithmatex>\(a\)</span>：裂纹长度</p> <h3 id=_13>金融市场断裂模型<a class=headerlink href=#_13 title="Permanent link">&para;</a></h3> <p>市场崩盘的临界条件：</p> <div class=arithmatex>\[\text{Stress}_{critical} = \sqrt{\frac{2 \cdot \text{Market\_Resilience} \cdot \text{Liquidity\_Energy}}{\pi \cdot \text{Vulnerability\_Length}}}\]</div> <p>其中： - <span class=arithmatex>\(\text{Market\_Resilience}\)</span>：市场韧性模量 - <span class=arithmatex>\(\text{Liquidity\_Energy}\)</span>：流动性能量密度 - <span class=arithmatex>\(\text{Vulnerability\_Length}\)</span>：市场脆弱性特征长度</p> <h3 id=_14>疲劳断裂模型<a class=headerlink href=#_14 title="Permanent link">&para;</a></h3> <p>Paris定律描述裂纹扩展：</p> <div class=arithmatex>\[\frac{da}{dN} = C(\Delta K)^m\]</div> <p>市场版本：</p> <div class=arithmatex>\[\frac{d\text{Risk}}{d\text{Cycle}} = C \cdot (\Delta \text{Stress})^m\]</div> <p>其中： - <span class=arithmatex>\(\text{Risk}\)</span>：系统性风险水平 - <span class=arithmatex>\(\text{Cycle}\)</span>：市场周期数 - <span class=arithmatex>\(\Delta \text{Stress}\)</span>：应力幅值 - <span class=arithmatex>\(C, m\)</span>：市场疲劳参数</p> <h2 id=_15>🌊 多尺度分析<a class=headerlink href=#_15 title="Permanent link">&para;</a></h2> <h3 id=_16>宏观尺度（市场整体）<a class=headerlink href=#_16 title="Permanent link">&para;</a></h3> <div class=arithmatex>\[\nabla \cdot \boldsymbol{\sigma} + \boldsymbol{f} = \rho \boldsymbol{a}\]</div> <p>其中： - <span class=arithmatex>\(\boldsymbol{\sigma}\)</span>：应力张量场 - <span class=arithmatex>\(\boldsymbol{f}\)</span>：体力（外部冲击） - <span class=arithmatex>\(\rho\)</span>：市场密度 - <span class=arithmatex>\(\boldsymbol{a}\)</span>：加速度场（价格加速度）</p> <h3 id=_17>中观尺度（行业板块）<a class=headerlink href=#_17 title="Permanent link">&para;</a></h3> <p>考虑细观结构的影响：</p> <div class=arithmatex>\[\langle\sigma\rangle = \mathcal{H}(\langle\varepsilon\rangle, \text{microstructure})\]</div> <p>其中 <span class=arithmatex>\(\mathcal{H}\)</span> 是均匀化算子。</p> <h3 id=_18>微观尺度（个股）<a class=headerlink href=#_18 title="Permanent link">&para;</a></h3> <p>分子动力学类比：</p> <div class=arithmatex>\[m_i \frac{d^2\boldsymbol{r}_i}{dt^2} = \sum_{j \neq i} \boldsymbol{F}_{ij} + \boldsymbol{F}_i^{ext}\]</div> <p>其中： - <span class=arithmatex>\(m_i\)</span>：股票i的"质量"（市值） - <span class=arithmatex>\(\boldsymbol{r}_i\)</span>：股票i的位置（价格空间） - <span class=arithmatex>\(\boldsymbol{F}_{ij}\)</span>：股票间相互作用力 - <span class=arithmatex>\(\boldsymbol{F}_i^{ext}\)</span>：外部作用力</p> <h2 id=_19>🎯 实际应用<a class=headerlink href=#_19 title="Permanent link">&para;</a></h2> <h3 id=_20>应力监测指标<a class=headerlink href=#_20 title="Permanent link">&para;</a></h3> <ol> <li><strong>主应力比</strong>：<span class=arithmatex>\(R_\sigma = \sigma_1 / \sigma_2\)</span></li> <li><strong>剪应力比</strong>：<span class=arithmatex>\(R_\tau = \tau_{max} / \sigma_1\)</span></li> <li><strong>应力集中因子</strong>：<span class=arithmatex>\(K_t = \sigma_{max} / \sigma_{nominal}\)</span></li> </ol> <h3 id=_21>风险预警阈值<a class=headerlink href=#_21 title="Permanent link">&para;</a></h3> <p>基于断裂力学的风险等级：</p> <ul> <li><strong>绿色</strong>：<span class=arithmatex>\(\text{Stress} &lt; 0.3 \sigma_c\)</span>（安全区域）</li> <li><strong>黄色</strong>：<span class=arithmatex>\(0.3 \sigma_c \leq \text{Stress} &lt; 0.7 \sigma_c\)</span>（注意区域）</li> <li><strong>橙色</strong>：<span class=arithmatex>\(0.7 \sigma_c \leq \text{Stress} &lt; 0.9 \sigma_c\)</span>（警告区域）</li> <li><strong>红色</strong>：<span class=arithmatex>\(\text{Stress} \geq 0.9 \sigma_c\)</span>（危险区域）</li> </ul> <h3 id=_22>策略应用<a class=headerlink href=#_22 title="Permanent link">&para;</a></h3> <div class="language-python highlight"><pre><span></span><code><span id=__span-1-1><a id=__codelineno-1-1 name=__codelineno-1-1 href=#__codelineno-1-1></a><span class=k>class</span><span class=w> </span><span class=nc>RockMechanicsStrategy</span><span class=p>:</span>
</span><span id=__span-1-2><a id=__codelineno-1-2 name=__codelineno-1-2 href=#__codelineno-1-2></a>    <span class=k>def</span><span class=w> </span><span class=fm>__init__</span><span class=p>(</span><span class=bp>self</span><span class=p>):</span>
</span><span id=__span-1-3><a id=__codelineno-1-3 name=__codelineno-1-3 href=#__codelineno-1-3></a>        <span class=bp>self</span><span class=o>.</span><span class=n>stress_threshold</span> <span class=o>=</span> <span class=mf>0.7</span>  <span class=c1># 应力阈值</span>
</span><span id=__span-1-4><a id=__codelineno-1-4 name=__codelineno-1-4 href=#__codelineno-1-4></a>        <span class=bp>self</span><span class=o>.</span><span class=n>fracture_energy</span> <span class=o>=</span> <span class=mf>1.0</span>   <span class=c1># 断裂能</span>
</span><span id=__span-1-5><a id=__codelineno-1-5 name=__codelineno-1-5 href=#__codelineno-1-5></a>
</span><span id=__span-1-6><a id=__codelineno-1-6 name=__codelineno-1-6 href=#__codelineno-1-6></a>    <span class=k>def</span><span class=w> </span><span class=nf>calculate_market_stress</span><span class=p>(</span><span class=bp>self</span><span class=p>,</span> <span class=n>price_data</span><span class=p>,</span> <span class=n>volume_data</span><span class=p>):</span>
</span><span id=__span-1-7><a id=__codelineno-1-7 name=__codelineno-1-7 href=#__codelineno-1-7></a><span class=w>        </span><span class=sd>&quot;&quot;&quot;计算市场应力状态&quot;&quot;&quot;</span>
</span><span id=__span-1-8><a id=__codelineno-1-8 name=__codelineno-1-8 href=#__codelineno-1-8></a>        <span class=c1># 计算应力张量</span>
</span><span id=__span-1-9><a id=__codelineno-1-9 name=__codelineno-1-9 href=#__codelineno-1-9></a>        <span class=n>stress_tensor</span> <span class=o>=</span> <span class=bp>self</span><span class=o>.</span><span class=n>compute_stress_tensor</span><span class=p>(</span><span class=n>price_data</span><span class=p>,</span> <span class=n>volume_data</span><span class=p>)</span>
</span><span id=__span-1-10><a id=__codelineno-1-10 name=__codelineno-1-10 href=#__codelineno-1-10></a>
</span><span id=__span-1-11><a id=__codelineno-1-11 name=__codelineno-1-11 href=#__codelineno-1-11></a>        <span class=c1># 主应力分析</span>
</span><span id=__span-1-12><a id=__codelineno-1-12 name=__codelineno-1-12 href=#__codelineno-1-12></a>        <span class=n>principal_stresses</span> <span class=o>=</span> <span class=bp>self</span><span class=o>.</span><span class=n>principal_stress_analysis</span><span class=p>(</span><span class=n>stress_tensor</span><span class=p>)</span>
</span><span id=__span-1-13><a id=__codelineno-1-13 name=__codelineno-1-13 href=#__codelineno-1-13></a>
</span><span id=__span-1-14><a id=__codelineno-1-14 name=__codelineno-1-14 href=#__codelineno-1-14></a>        <span class=c1># 断裂风险评估</span>
</span><span id=__span-1-15><a id=__codelineno-1-15 name=__codelineno-1-15 href=#__codelineno-1-15></a>        <span class=n>fracture_risk</span> <span class=o>=</span> <span class=bp>self</span><span class=o>.</span><span class=n>fracture_risk_assessment</span><span class=p>(</span><span class=n>principal_stresses</span><span class=p>)</span>
</span><span id=__span-1-16><a id=__codelineno-1-16 name=__codelineno-1-16 href=#__codelineno-1-16></a>
</span><span id=__span-1-17><a id=__codelineno-1-17 name=__codelineno-1-17 href=#__codelineno-1-17></a>        <span class=k>return</span> <span class=n>fracture_risk</span>
</span><span id=__span-1-18><a id=__codelineno-1-18 name=__codelineno-1-18 href=#__codelineno-1-18></a>
</span><span id=__span-1-19><a id=__codelineno-1-19 name=__codelineno-1-19 href=#__codelineno-1-19></a>    <span class=k>def</span><span class=w> </span><span class=nf>generate_signals</span><span class=p>(</span><span class=bp>self</span><span class=p>,</span> <span class=n>fracture_risk</span><span class=p>):</span>
</span><span id=__span-1-20><a id=__codelineno-1-20 name=__codelineno-1-20 href=#__codelineno-1-20></a><span class=w>        </span><span class=sd>&quot;&quot;&quot;基于断裂风险生成交易信号&quot;&quot;&quot;</span>
</span><span id=__span-1-21><a id=__codelineno-1-21 name=__codelineno-1-21 href=#__codelineno-1-21></a>        <span class=k>if</span> <span class=n>fracture_risk</span> <span class=o>&gt;</span> <span class=bp>self</span><span class=o>.</span><span class=n>stress_threshold</span><span class=p>:</span>
</span><span id=__span-1-22><a id=__codelineno-1-22 name=__codelineno-1-22 href=#__codelineno-1-22></a>            <span class=k>return</span> <span class=s2>&quot;SELL&quot;</span>  <span class=c1># 高风险，减仓</span>
</span><span id=__span-1-23><a id=__codelineno-1-23 name=__codelineno-1-23 href=#__codelineno-1-23></a>        <span class=k>elif</span> <span class=n>fracture_risk</span> <span class=o>&lt;</span> <span class=mf>0.3</span><span class=p>:</span>
</span><span id=__span-1-24><a id=__codelineno-1-24 name=__codelineno-1-24 href=#__codelineno-1-24></a>            <span class=k>return</span> <span class=s2>&quot;BUY&quot;</span>   <span class=c1># 低风险，加仓</span>
</span><span id=__span-1-25><a id=__codelineno-1-25 name=__codelineno-1-25 href=#__codelineno-1-25></a>        <span class=k>else</span><span class=p>:</span>
</span><span id=__span-1-26><a id=__codelineno-1-26 name=__codelineno-1-26 href=#__codelineno-1-26></a>            <span class=k>return</span> <span class=s2>&quot;HOLD&quot;</span>  <span class=c1># 中等风险，持有</span>
</span></code></pre></div> <h2 id=_23>📊 验证与校准<a class=headerlink href=#_23 title="Permanent link">&para;</a></h2> <h3 id=_24>历史数据验证<a class=headerlink href=#_24 title="Permanent link">&para;</a></h3> <p>使用历史金融危机数据验证模型：</p> <ol> <li><strong>1987年黑色星期一</strong></li> <li><strong>1997年亚洲金融危机</strong></li> <li><strong>2008年次贷危机</strong></li> <li><strong>2020年疫情冲击</strong></li> </ol> <h3 id=_25>模型校准方法<a class=headerlink href=#_25 title="Permanent link">&para;</a></h3> <ol> <li><strong>参数估计</strong>：最大似然估计、贝叶斯推断</li> <li><strong>模型选择</strong>：AIC、BIC准则</li> <li><strong>交叉验证</strong>：时间序列交叉验证</li> <li><strong>压力测试</strong>：极端情景模拟</li> </ol> <h2 id=_26>🔮 未来发展<a class=headerlink href=#_26 title="Permanent link">&para;</a></h2> <h3 id=_27>理论扩展<a class=headerlink href=#_27 title="Permanent link">&para;</a></h3> <ol> <li><strong>多相材料模型</strong>：考虑市场的异质性</li> <li><strong>损伤力学</strong>：累积损伤对市场的影响</li> <li><strong>断裂动力学</strong>：动态断裂过程建模</li> <li><strong>多尺度耦合</strong>：跨尺度效应的精确建模</li> </ol> <h3 id=_28>技术创新<a class=headerlink href=#_28 title="Permanent link">&para;</a></h3> <ol> <li><strong>机器学习集成</strong>：AI辅助参数识别</li> <li><strong>实时监测系统</strong>：高频数据应力分析</li> <li><strong>可视化工具</strong>：应力场可视化</li> <li><strong>预警系统</strong>：智能风险预警</li> </ol> <hr> <p><em>"如同地质学家通过岩石的裂纹预测地震，我们通过市场的应力预测金融风暴。"</em> - 太公心易</p> <h2 id=_29>参考文献<a class=headerlink href=#_29 title="Permanent link">&para;</a></h2> <ol> <li>Jaeger, J.C., Cook, N.G.W., &amp; Zimmerman, R.W. (2007). <em>Fundamentals of Rock Mechanics</em></li> <li>Mandelbrot, B.B. (1982). <em>The Fractal Geometry of Nature</em></li> <li>Sornette, D. (2003). <em>Why Stock Markets Crash</em></li> <li>Cont, R., &amp; Tankov, P. (2004). <em>Financial Modelling with Jump Processes</em></li> </ol> <aside class=md-source-file> <span class=md-source-file__fact> <span class=md-icon title=最后更新> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M21 13.1c-.1 0-.3.1-.4.2l-1 1 2.1 2.1 1-1c.2-.2.2-.6 0-.8l-1.3-1.3c-.1-.1-.2-.2-.4-.2m-1.9 1.8-6.1 6V23h2.1l6.1-6.1zM12.5 7v5.2l4 2.4-1 1L11 13V7zM11 21.9c-5.1-.5-9-4.8-9-9.9C2 6.5 6.5 2 12 2c5.3 0 9.6 4.1 10 9.3-.3-.1-.6-.2-1-.2s-.7.1-1 .2C19.6 7.2 16.2 4 12 4c-4.4 0-8 3.6-8 8 0 4.1 3.1 7.5 7.1 7.9l-.1.2z"/></svg> </span> <span class="git-revision-date-localized-plugin git-revision-date-localized-plugin-datetime" title="2025年7月10日 04:18:21 UTC">2025年7月10日 04:18:21</span> </span> <span class=md-source-file__fact> <span class=md-icon title=创建日期> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M14.47 15.08 11 13V7h1.5v5.25l3.08 1.83c-.41.28-.79.62-1.11 1m-1.39 4.84c-.36.05-.71.08-1.08.08-4.42 0-8-3.58-8-8s3.58-8 8-8 8 3.58 8 8c0 .37-.03.72-.08 1.08.69.1 1.33.32 1.92.64.1-.56.16-1.13.16-1.72 0-5.5-4.5-10-10-10S2 6.5 2 12s4.47 10 10 10c.59 0 1.16-.06 1.72-.16-.32-.59-.54-1.23-.64-1.92M18 15v3h-3v2h3v3h2v-3h3v-2h-3v-3z"/></svg> </span> <span class="git-revision-date-localized-plugin git-revision-date-localized-plugin-datetime" title="2025年7月3日 17:04:56 UTC">2025年7月3日 17:04:56</span> </span> </aside> <form class=md-feedback name=feedback hidden> <fieldset> <legend class=md-feedback__title> 这个页面有帮助吗？ </legend> <div class=md-feedback__inner> <div class=md-feedback__list> <button class="md-feedback__icon md-icon" type=submit title=有帮助 data-md-value=1> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M20 12a8 8 0 0 0-8-8 8 8 0 0 0-8 8 8 8 0 0 0 8 8 8 8 0 0 0 8-8m2 0a10 10 0 0 1-10 10A10 10 0 0 1 2 12 10 10 0 0 1 12 2a10 10 0 0 1 10 10M10 9.5c0 .8-.7 1.5-1.5 1.5S7 10.3 7 9.5 7.7 8 8.5 8s1.5.7 1.5 1.5m7 0c0 .8-.7 1.5-1.5 1.5S14 10.3 14 9.5 14.7 8 15.5 8s1.5.7 1.5 1.5m-5 7.73c-1.75 0-3.29-.73-4.19-1.81L9.23 14c.45.72 1.52 1.23 2.77 1.23s2.32-.51 2.77-1.23l1.42 1.42c-.9 1.08-2.44 1.81-4.19 1.81"/></svg> </button> <button class="md-feedback__icon md-icon" type=submit title=需要改进 data-md-value=0> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M20 12a8 8 0 0 0-8-8 8 8 0 0 0-8 8 8 8 0 0 0 8 8 8 8 0 0 0 8-8m2 0a10 10 0 0 1-10 10A10 10 0 0 1 2 12 10 10 0 0 1 12 2a10 10 0 0 1 10 10m-6.5-4c.8 0 1.5.7 1.5 1.5s-.7 1.5-1.5 1.5-1.5-.7-1.5-1.5.7-1.5 1.5-1.5M10 9.5c0 .8-.7 1.5-1.5 1.5S7 10.3 7 9.5 7.7 8 8.5 8s1.5.7 1.5 1.5m2 4.5c1.75 0 3.29.72 4.19 1.81l-1.42 1.42C14.32 16.5 13.25 16 12 16s-2.32.5-2.77 1.23l-1.42-1.42C8.71 14.72 10.25 14 12 14"/></svg> </button> </div> <div class=md-feedback__note> <div data-md-value=1 hidden> 感谢您的反馈！ </div> <div data-md-value=0 hidden> 感谢您的反馈！请通过GitHub Issues告诉我们如何改进。 </div> </div> </div> </fieldset> </form> </article> </div> <script>var tabs=__md_get("__tabs");if(Array.isArray(tabs))e:for(var set of document.querySelectorAll(".tabbed-set")){var labels=set.querySelector(".tabbed-labels");for(var tab of tabs)for(var label of labels.getElementsByTagName("label"))if(label.innerText.trim()===tab){var input=document.getElementById(label.htmlFor);input.checked=!0;continue e}}</script> <script>var target=document.getElementById(location.hash.slice(1));target&&target.name&&(target.checked=target.name.startsWith("__tabbed_"))</script> </div> <button type=button class="md-top md-icon" data-md-component=top hidden> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M13 20h-2V8l-5.5 5.5-1.42-1.42L12 4.16l7.92 7.92-1.42 1.42L13 8z"/></svg> 回到页面顶部 </button> </main> <footer class=md-footer> <div class="md-footer-meta md-typeset"> <div class="md-footer-meta__inner md-grid"> <div class=md-copyright> <div class=md-copyright__highlight> Copyright &copy; 2025 炼妖壶项目 - MIT License </div> Made with <a href=https://squidfunk.github.io/mkdocs-material/ target=_blank rel=noopener> Material for MkDocs </a> </div> <div class=md-social> <a href=https://github.com/your-username/jixia-academy target=_blank rel=noopener title=GitHub仓库 class=md-social__link> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 496 512"><!-- Font Awesome Free 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license/free (Icons: CC BY 4.0, Fonts: SIL OFL 1.1, Code: MIT License) Copyright 2024 Fonticons, Inc.--><path d="M165.9 397.4c0 2-2.3 3.6-5.2 3.6-3.3.3-5.6-1.3-5.6-3.6 0-2 2.3-3.6 5.2-3.6 3-.3 5.6 1.3 5.6 3.6m-31.1-4.5c-.7 2 1.3 4.3 4.3 4.9 2.6 1 5.6 0 6.2-2s-1.3-4.3-4.3-5.2c-2.6-.7-5.5.3-6.2 2.3m44.2-1.7c-2.9.7-4.9 2.6-4.6 4.9.3 2 2.9 3.3 5.9 2.6 2.9-.7 4.9-2.6 4.6-4.6-.3-1.9-3-3.2-5.9-2.9M244.8 8C106.1 8 0 113.3 0 252c0 110.9 69.8 205.8 169.5 239.2 12.8 2.3 17.3-5.6 17.3-12.1 0-6.2-.3-40.4-.3-61.4 0 0-70 15-84.7-29.8 0 0-11.4-29.1-27.8-36.6 0 0-22.9-15.7 1.6-15.4 0 0 24.9 2 38.6 25.8 21.9 38.6 58.6 27.5 72.9 20.9 2.3-16 8.8-27.1 16-33.7-55.9-6.2-112.3-14.3-112.3-110.5 0-27.5 7.6-41.3 23.6-58.9-2.6-6.5-11.1-33.3 2.6-67.9 20.9-6.5 69 27 69 27 20-5.6 41.5-8.5 62.8-8.5s42.8 2.9 62.8 8.5c0 0 48.1-33.6 69-27 13.7 34.7 5.2 61.4 2.6 67.9 16 17.7 25.8 31.5 25.8 58.9 0 96.5-58.9 104.2-114.8 110.5 9.2 7.9 17 22.9 17 46.4 0 33.7-.3 75.4-.3 83.6 0 6.5 4.6 14.4 17.3 12.1C428.2 457.8 496 362.9 496 252 496 113.3 383.5 8 244.8 8M97.2 352.9c-1.3 1-1 3.3.7 5.2 1.6 1.6 3.9 2.3 5.2 1 1.3-1 1-3.3-.7-5.2-1.6-1.6-3.9-2.3-5.2-1m-10.8-8.1c-.7 1.3.3 2.9 2.3 3.9 1.6 1 3.6.7 4.3-.7.7-1.3-.3-2.9-2.3-3.9-2-.6-3.6-.3-4.3.7m32.4 35.6c-1.6 1.3-1 4.3 1.3 6.2 2.3 2.3 5.2 2.6 6.5 1 1.3-1.3.7-4.3-1.3-6.2-2.2-2.3-5.2-2.6-6.5-1m-11.4-14.7c-1.6 1-1.6 3.6 0 5.9s4.3 3.3 5.6 2.3c1.6-1.3 1.6-3.9 0-6.2-1.4-2.3-4-3.3-5.6-2"/></svg> </a> <a href=https://your-mastodon-instance.com/@taishang_laojun target=_blank rel="noopener me" title=太上老君 class=md-social__link> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 448 512"><!-- Font Awesome Free 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license/free (Icons: CC BY 4.0, Fonts: SIL OFL 1.1, Code: MIT License) Copyright 2024 Fonticons, Inc.--><path d="M433 179.11c0-97.2-63.71-125.7-63.71-125.7-62.52-28.7-228.56-28.4-290.48 0 0 0-63.72 28.5-63.72 125.7 0 115.7-6.6 259.4 105.63 289.1 40.51 10.7 75.32 13 103.33 11.4 50.81-2.8 79.32-18.1 79.32-18.1l-1.7-36.9s-36.31 11.4-77.12 10.1c-40.41-1.4-83-4.4-89.63-54a102.5 102.5 0 0 1-.9-13.9c85.63 20.9 158.65 9.1 178.75 6.7 56.12-6.7 105-41.3 111.23-72.9 9.8-49.8 9-121.5 9-121.5m-75.12 125.2h-46.63v-114.2c0-49.7-64-51.6-64 6.9v62.5h-46.33V197c0-58.5-64-56.6-64-6.9v114.2H90.19c0-122.1-5.2-147.9 18.41-175 25.9-28.9 79.82-30.8 103.83 6.1l11.6 19.5 11.6-19.5c24.11-37.1 78.12-34.8 103.83-6.1 23.71 27.3 18.4 53 18.4 175z"/></svg> </a> <a href=https://twitter.com/jixia_academy target=_blank rel=noopener title=Twitter class=md-social__link> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 512 512"><!-- Font Awesome Free 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license/free (Icons: CC BY 4.0, Fonts: SIL OFL 1.1, Code: MIT License) Copyright 2024 Fonticons, Inc.--><path d="M459.37 151.716c.325 4.548.325 9.097.325 13.645 0 138.72-105.583 298.558-298.558 298.558-59.452 0-114.68-17.219-161.137-47.106 8.447.974 16.568 1.299 25.34 1.299 49.055 0 94.213-16.568 130.274-44.832-46.132-.975-84.792-31.188-98.112-72.772 6.498.974 12.995 1.624 19.818 1.624 9.421 0 18.843-1.3 27.614-3.573-48.081-9.747-84.143-51.98-84.143-102.985v-1.299c13.969 7.797 30.214 12.67 47.431 13.319-28.264-18.843-46.781-51.005-46.781-87.391 0-19.492 5.197-37.36 14.294-52.954 51.655 63.675 129.3 105.258 216.365 109.807-1.624-7.797-2.599-15.918-2.599-24.04 0-57.828 46.782-104.934 104.934-104.934 30.213 0 57.502 12.67 76.67 33.137 23.715-4.548 46.456-13.32 66.599-25.34-7.798 24.366-24.366 44.833-46.132 57.827 21.117-2.273 41.584-8.122 60.426-16.243-14.292 20.791-32.161 39.308-52.628 54.253"/></svg> </a> <a href=mailto:<EMAIL> target=_blank rel=noopener title=联系我们 class=md-social__link> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 512 512"><!-- Font Awesome Free 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license/free (Icons: CC BY 4.0, Fonts: SIL OFL 1.1, Code: MIT License) Copyright 2024 Fonticons, Inc.--><path d="M498.1 5.6c10.1 7 15.4 19.1 13.5 31.2l-64 416c-1.5 9.7-7.4 18.2-16 23s-18.9 5.4-28 1.6L284 427.7l-68.5 74.1c-8.9 9.7-22.9 12.9-35.2 8.1S160 493.2 160 480v-83.6c0-4 1.5-7.8 4.2-10.8l167.6-182.8c5.8-6.3 5.6-16-.4-22s-15.7-6.4-22-.7L106 360.8l-88.3-44.2C7.1 311.3.3 300.7 0 288.9s5.9-22.8 16.1-28.7l448-256c10.7-6.1 23.9-5.5 34 1.4"/></svg> </a> </div> </div> </div> </footer> </div> <div class=md-dialog data-md-component=dialog> <div class="md-dialog__inner md-typeset"></div> </div> <script id=__config type=application/json>{"base": "../../..", "features": ["navigation.instant", "navigation.tracking", "navigation.tabs", "navigation.tabs.sticky", "navigation.sections", "navigation.expand", "navigation.indexes", "navigation.top", "search.highlight", "search.share", "search.suggest", "content.code.annotate", "content.code.copy", "content.tabs.link"], "search": "../../../assets/javascripts/workers/search.d50fe291.min.js", "tags": null, "translations": {"clipboard.copied": "\u5df2\u590d\u5236", "clipboard.copy": "\u590d\u5236", "search.result.more.one": "\u5728\u8be5\u9875\u4e0a\u8fd8\u6709 1 \u4e2a\u7b26\u5408\u6761\u4ef6\u7684\u7ed3\u679c", "search.result.more.other": "\u5728\u8be5\u9875\u4e0a\u8fd8\u6709 # \u4e2a\u7b26\u5408\u6761\u4ef6\u7684\u7ed3\u679c", "search.result.none": "\u6ca1\u6709\u627e\u5230\u7b26\u5408\u6761\u4ef6\u7684\u7ed3\u679c", "search.result.one": "\u627e\u5230 1 \u4e2a\u7b26\u5408\u6761\u4ef6\u7684\u7ed3\u679c", "search.result.other": "# \u4e2a\u7b26\u5408\u6761\u4ef6\u7684\u7ed3\u679c", "search.result.placeholder": "\u952e\u5165\u4ee5\u5f00\u59cb\u641c\u7d22", "search.result.term.missing": "\u7f3a\u5c11", "select.version": "\u9009\u62e9\u5f53\u524d\u7248\u672c"}, "version": {"default": "latest", "provider": "mike"}}</script> <script src=../../../assets/javascripts/bundle.56ea9cef.min.js></script> <script src=../../../assets/javascripts/mathjax.js></script> <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script> <script src=https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js></script> </body> </html>