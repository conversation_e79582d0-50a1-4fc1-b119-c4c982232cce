/* 🎭 稷下学宫自定义样式 */

/* 中国风配色变量 */
:root {
  --jixia-primary: #1e3a8a;      /* 稷下学宫深蓝 */
  --jixia-secondary: #dc2626;    /* 复仇红色 */
  --jixia-accent: #f59e0b;       /* 仙人金色 */
  --jixia-tech: #10b981;         /* 科技绿色 */
  --jixia-bg: #f8fafc;           /* 背景色 */
}

/* 首页特殊样式 */
.md-typeset h1 {
  color: var(--jixia-primary);
  text-align: center;
  font-weight: 700;
  margin-bottom: 2rem;
}

/* 引用块样式 - 用于经典语录 */
.md-typeset blockquote {
  border-left: 4px solid var(--jixia-accent);
  background: linear-gradient(90deg, rgba(245, 158, 11, 0.1) 0%, transparent 100%);
  padding: 1rem 1.5rem;
  margin: 1.5rem 0;
  font-style: italic;
  position: relative;
}

.md-typeset blockquote::before {
  content: "💬";
  position: absolute;
  left: -0.5rem;
  top: 0.5rem;
  font-size: 1.2rem;
}

/* 代码块样式增强 */
.md-typeset .highlight {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 仙人卡片样式 */
.immortal-card {
  background: linear-gradient(135deg, var(--jixia-bg) 0%, rgba(30, 58, 138, 0.05) 100%);
  border: 2px solid var(--jixia-accent);
  border-radius: 12px;
  padding: 1.5rem;
  margin: 1rem 0;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.immortal-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.immortal-card h3 {
  color: var(--jixia-primary);
  margin-top: 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.immortal-card .specialty {
  background: var(--jixia-accent);
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.85rem;
  font-weight: 500;
  display: inline-block;
  margin: 0.5rem 0;
}

/* 复仇宣言特殊样式 */
.revenge-manifesto {
  background: linear-gradient(135deg, #dc2626 0%, #991b1b 100%);
  color: white;
  padding: 2rem;
  border-radius: 12px;
  margin: 2rem 0;
  box-shadow: 0 8px 32px rgba(220, 38, 38, 0.3);
}

.revenge-manifesto h2,
.revenge-manifesto h3 {
  color: #fbbf24;
}

/* 技术架构图样式 */
.architecture-diagram {
  background: var(--jixia-bg);
  border: 2px dashed var(--jixia-tech);
  border-radius: 8px;
  padding: 1.5rem;
  margin: 1.5rem 0;
  text-align: center;
  font-family: 'JetBrains Mono', monospace;
}

/* 按钮样式增强 */
.md-button {
  background: linear-gradient(135deg, var(--jixia-primary) 0%, var(--jixia-tech) 100%);
  border: none;
  border-radius: 25px;
  padding: 0.75rem 2rem;
  font-weight: 600;
  text-transform: none;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(30, 58, 138, 0.3);
}

.md-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(30, 58, 138, 0.4);
}

.md-button--primary {
  background: linear-gradient(135deg, var(--jixia-accent) 0%, #f97316 100%);
}

/* 网格卡片样式 */
.grid.cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
  margin: 2rem 0;
}

.grid.cards .card {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.grid.cards .card:hover {
  border-color: var(--jixia-accent);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

/* 统计徽章样式 */
.stats-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  background: var(--jixia-tech);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 500;
  margin: 0.25rem;
}

/* 时间线样式 */
.timeline {
  position: relative;
  padding-left: 2rem;
}

.timeline::before {
  content: '';
  position: absolute;
  left: 0.5rem;
  top: 0;
  bottom: 0;
  width: 2px;
  background: var(--jixia-accent);
}

.timeline-item {
  position: relative;
  margin-bottom: 2rem;
}

.timeline-item::before {
  content: '';
  position: absolute;
  left: -1.75rem;
  top: 0.5rem;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: var(--jixia-accent);
  border: 3px solid white;
  box-shadow: 0 0 0 2px var(--jixia-accent);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .immortal-card {
    padding: 1rem;
    margin: 0.5rem 0;
  }
  
  .grid.cards {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
  
  .revenge-manifesto {
    padding: 1rem;
    margin: 1rem 0;
  }
}

/* 暗黑模式适配 */
[data-md-color-scheme="slate"] {
  --jixia-bg: #1e293b;
}

[data-md-color-scheme="slate"] .immortal-card {
  background: linear-gradient(135deg, #1e293b 0%, rgba(30, 58, 138, 0.1) 100%);
  border-color: var(--jixia-accent);
}

[data-md-color-scheme="slate"] .grid.cards .card {
  background: #1e293b;
  border-color: #374151;
}

/* 动画效果 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.md-typeset h1,
.md-typeset h2,
.md-typeset h3 {
  animation: fadeInUp 0.6s ease-out;
}

/* 特殊emoji样式 */
.emoji-large {
  font-size: 1.5em;
  vertical-align: middle;
}

/* 代码语言标签 */
.md-typeset .highlight .filename {
  background: var(--jixia-primary);
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 4px 4px 0 0;
  font-size: 0.8rem;
  font-weight: 500;
}

/* 警告框样式 */
.md-typeset .admonition.warning {
  border-color: var(--jixia-secondary);
}

.md-typeset .admonition.tip {
  border-color: var(--jixia-tech);
}

.md-typeset .admonition.info {
  border-color: var(--jixia-primary);
}