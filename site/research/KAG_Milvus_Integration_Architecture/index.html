<!doctype html><html lang=zh class=no-js> <head><meta charset=utf-8><meta name=viewport content="width=device-width,initial-scale=1"><meta name=description content="AI驱动的投资决策系统 - 稷下学宫智能体辩论平台"><meta name=author content=炼妖壶开发团队><link href=https://jingminzhang.github.io/cauldron/research/KAG_Milvus_Integration_Architecture/ rel=canonical><link rel=icon href=../../assets/images/favicon.png><meta name=generator content="mkdocs-1.6.1, mkdocs-material-9.6.15"><title>KAG + Milvus 集成架构详解 - 炼妖壶 (Cauldron)</title><link rel=stylesheet href=../../assets/stylesheets/main.342714a4.min.css><link rel=stylesheet href=../../assets/stylesheets/palette.06af60db.min.css><link rel=preconnect href=https://fonts.gstatic.com crossorigin><link rel=stylesheet href="https://fonts.googleapis.com/css?family=Noto+Sans+SC:300,300i,400,400i,700,700i%7CJetBrains+Mono:400,400i,700,700i&display=fallback"><style>:root{--md-text-font:"Noto Sans SC";--md-code-font:"JetBrains Mono"}</style><link rel=stylesheet href=../../assets/stylesheets/extra.css><script>__md_scope=new URL("../..",location),__md_hash=e=>[...e].reduce(((e,_)=>(e<<5)-e+_.charCodeAt(0)),0),__md_get=(e,_=localStorage,t=__md_scope)=>JSON.parse(_.getItem(t.pathname+"."+e)),__md_set=(e,_,t=localStorage,a=__md_scope)=>{try{t.setItem(a.pathname+"."+e,JSON.stringify(_))}catch(e){}}</script><script id=__analytics>function __md_analytics(){function e(){dataLayer.push(arguments)}window.dataLayer=window.dataLayer||[],e("js",new Date),e("config","G-XXXXXXXXXX"),document.addEventListener("DOMContentLoaded",(function(){document.forms.search&&document.forms.search.query.addEventListener("blur",(function(){this.value&&e("event","search",{search_term:this.value})}));document$.subscribe((function(){var t=document.forms.feedback;if(void 0!==t)for(var a of t.querySelectorAll("[type=submit]"))a.addEventListener("click",(function(a){a.preventDefault();var n=document.location.pathname,d=this.getAttribute("data-md-value");e("event","feedback",{page:n,data:d}),t.firstElementChild.disabled=!0;var r=t.querySelector(".md-feedback__note [data-md-value='"+d+"']");r&&(r.hidden=!1)})),t.hidden=!1})),location$.subscribe((function(t){e("config","G-XXXXXXXXXX",{page_path:t.pathname})}))}));var t=document.createElement("script");t.async=!0,t.src="https://www.googletagmanager.com/gtag/js?id=G-XXXXXXXXXX",document.getElementById("__analytics").insertAdjacentElement("afterEnd",t)}</script><script>"undefined"!=typeof __md_analytics&&__md_analytics()</script></head> <body dir=ltr data-md-color-scheme=default data-md-color-primary=indigo data-md-color-accent=amber> <input class=md-toggle data-md-toggle=drawer type=checkbox id=__drawer autocomplete=off> <input class=md-toggle data-md-toggle=search type=checkbox id=__search autocomplete=off> <label class=md-overlay for=__drawer></label> <div data-md-component=skip> <a href=#kag-milvus class=md-skip> 跳转至 </a> </div> <div data-md-component=announce> </div> <div data-md-color-scheme=default data-md-component=outdated hidden> </div> <header class="md-header md-header--shadow md-header--lifted" data-md-component=header> <nav class="md-header__inner md-grid" aria-label=页眉> <a href=../.. title="炼妖壶 (Cauldron)" class="md-header__button md-logo" aria-label="炼妖壶 (Cauldron)" data-md-component=logo> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M12 3 1 9l11 6 9-4.91V17h2V9M5 13.18v4L12 21l7-3.82v-4L12 17z"/></svg> </a> <label class="md-header__button md-icon" for=__drawer> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M3 6h18v2H3zm0 5h18v2H3zm0 5h18v2H3z"/></svg> </label> <div class=md-header__title data-md-component=header-title> <div class=md-header__ellipsis> <div class=md-header__topic> <span class=md-ellipsis> 炼妖壶 (Cauldron) </span> </div> <div class=md-header__topic data-md-component=header-topic> <span class=md-ellipsis> KAG + Milvus 集成架构详解 </span> </div> </div> </div> <form class=md-header__option data-md-component=palette> <input class=md-option data-md-color-media="(prefers-color-scheme: light)" data-md-color-scheme=default data-md-color-primary=indigo data-md-color-accent=amber aria-label=切换到暗黑模式 type=radio name=__palette id=__palette_0> <label class="md-header__button md-icon" title=切换到暗黑模式 for=__palette_1 hidden> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M12 8a4 4 0 0 0-4 4 4 4 0 0 0 4 4 4 4 0 0 0 4-4 4 4 0 0 0-4-4m0 10a6 6 0 0 1-6-6 6 6 0 0 1 6-6 6 6 0 0 1 6 6 6 6 0 0 1-6 6m8-9.31V4h-4.69L12 .69 8.69 4H4v4.69L.69 12 4 15.31V20h4.69L12 23.31 15.31 20H20v-4.69L23.31 12z"/></svg> </label> <input class=md-option data-md-color-media="(prefers-color-scheme: dark)" data-md-color-scheme=slate data-md-color-primary=indigo data-md-color-accent=amber aria-label=切换到明亮模式 type=radio name=__palette id=__palette_1> <label class="md-header__button md-icon" title=切换到明亮模式 for=__palette_0 hidden> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M12 18c-.89 0-1.74-.2-2.5-.55C11.56 16.5 13 14.42 13 12s-1.44-4.5-3.5-5.45C10.26 6.2 11.11 6 12 6a6 6 0 0 1 6 6 6 6 0 0 1-6 6m8-9.31V4h-4.69L12 .69 8.69 4H4v4.69L.69 12 4 15.31V20h4.69L12 23.31 15.31 20H20v-4.69L23.31 12z"/></svg> </label> </form> <script>var palette=__md_get("__palette");if(palette&&palette.color){if("(prefers-color-scheme)"===palette.color.media){var media=matchMedia("(prefers-color-scheme: light)"),input=document.querySelector(media.matches?"[data-md-color-media='(prefers-color-scheme: light)']":"[data-md-color-media='(prefers-color-scheme: dark)']");palette.color.media=input.getAttribute("data-md-color-media"),palette.color.scheme=input.getAttribute("data-md-color-scheme"),palette.color.primary=input.getAttribute("data-md-color-primary"),palette.color.accent=input.getAttribute("data-md-color-accent")}for(var[key,value]of Object.entries(palette.color))document.body.setAttribute("data-md-color-"+key,value)}</script> <label class="md-header__button md-icon" for=__search> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M9.5 3A6.5 6.5 0 0 1 16 9.5c0 1.61-.59 3.09-1.56 4.23l.27.27h.79l5 5-1.5 1.5-5-5v-.79l-.27-.27A6.52 6.52 0 0 1 9.5 16 6.5 6.5 0 0 1 3 9.5 6.5 6.5 0 0 1 9.5 3m0 2C7 5 5 7 5 9.5S7 14 9.5 14 14 12 14 9.5 12 5 9.5 5"/></svg> </label> <div class=md-search data-md-component=search role=dialog> <label class=md-search__overlay for=__search></label> <div class=md-search__inner role=search> <form class=md-search__form name=search> <input type=text class=md-search__input name=query aria-label=搜索 placeholder=搜索 autocapitalize=off autocorrect=off autocomplete=off spellcheck=false data-md-component=search-query required> <label class="md-search__icon md-icon" for=__search> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M9.5 3A6.5 6.5 0 0 1 16 9.5c0 1.61-.59 3.09-1.56 4.23l.27.27h.79l5 5-1.5 1.5-5-5v-.79l-.27-.27A6.52 6.52 0 0 1 9.5 16 6.5 6.5 0 0 1 3 9.5 6.5 6.5 0 0 1 9.5 3m0 2C7 5 5 7 5 9.5S7 14 9.5 14 14 12 14 9.5 12 5 9.5 5"/></svg> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M20 11v2H8l5.5 5.5-1.42 1.42L4.16 12l7.92-7.92L13.5 5.5 8 11z"/></svg> </label> <nav class=md-search__options aria-label=查找> <a href=javascript:void(0) class="md-search__icon md-icon" title=分享 aria-label=分享 data-clipboard data-clipboard-text data-md-component=search-share tabindex=-1> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M18 16.08c-.76 0-1.44.3-1.96.77L8.91 12.7c.05-.23.09-.46.09-.7s-.04-.47-.09-.7l7.05-4.11c.54.5 1.25.81 2.04.81a3 3 0 0 0 3-3 3 3 0 0 0-3-3 3 3 0 0 0-3 3c0 .***********.7L8.04 9.81C7.5 9.31 6.79 9 6 9a3 3 0 0 0-3 3 3 3 0 0 0 3 3c.79 0 1.5-.31 2.04-.81l7.12 4.15c-.05.21-.08.43-.08.66 0 1.61 1.31 2.91 2.92 2.91s2.92-1.3 2.92-2.91A2.92 2.92 0 0 0 18 16.08"/></svg> </a> <button type=reset class="md-search__icon md-icon" title=清空当前内容 aria-label=清空当前内容 tabindex=-1> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M19 6.41 17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/></svg> </button> </nav> <div class=md-search__suggest data-md-component=search-suggest></div> </form> <div class=md-search__output> <div class=md-search__scrollwrap tabindex=0 data-md-scrollfix> <div class=md-search-result data-md-component=search-result> <div class=md-search-result__meta> 正在初始化搜索引擎 </div> <ol class=md-search-result__list role=presentation></ol> </div> </div> </div> </div> </div> <div class=md-header__source> <a href=https://github.com/jingminzhang/cauldron title=前往仓库 class=md-source data-md-component=source> <div class="md-source__icon md-icon"> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 496 512"><!-- Font Awesome Free 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license/free (Icons: CC BY 4.0, Fonts: SIL OFL 1.1, Code: MIT License) Copyright 2024 Fonticons, Inc.--><path d="M165.9 397.4c0 2-2.3 3.6-5.2 3.6-3.3.3-5.6-1.3-5.6-3.6 0-2 2.3-3.6 5.2-3.6 3-.3 5.6 1.3 5.6 3.6m-31.1-4.5c-.7 2 1.3 4.3 4.3 4.9 2.6 1 5.6 0 6.2-2s-1.3-4.3-4.3-5.2c-2.6-.7-5.5.3-6.2 2.3m44.2-1.7c-2.9.7-4.9 2.6-4.6 4.9.3 2 2.9 3.3 5.9 2.6 2.9-.7 4.9-2.6 4.6-4.6-.3-1.9-3-3.2-5.9-2.9M244.8 8C106.1 8 0 113.3 0 252c0 110.9 69.8 205.8 169.5 239.2 12.8 2.3 17.3-5.6 17.3-12.1 0-6.2-.3-40.4-.3-61.4 0 0-70 15-84.7-29.8 0 0-11.4-29.1-27.8-36.6 0 0-22.9-15.7 1.6-15.4 0 0 24.9 2 38.6 25.8 21.9 38.6 58.6 27.5 72.9 20.9 2.3-16 8.8-27.1 16-33.7-55.9-6.2-112.3-14.3-112.3-110.5 0-27.5 7.6-41.3 23.6-58.9-2.6-6.5-11.1-33.3 2.6-67.9 20.9-6.5 69 27 69 27 20-5.6 41.5-8.5 62.8-8.5s42.8 2.9 62.8 8.5c0 0 48.1-33.6 69-27 13.7 34.7 5.2 61.4 2.6 67.9 16 17.7 25.8 31.5 25.8 58.9 0 96.5-58.9 104.2-114.8 110.5 9.2 7.9 17 22.9 17 46.4 0 33.7-.3 75.4-.3 83.6 0 6.5 4.6 14.4 17.3 12.1C428.2 457.8 496 362.9 496 252 496 113.3 383.5 8 244.8 8M97.2 352.9c-1.3 1-1 3.3.7 5.2 1.6 1.6 3.9 2.3 5.2 1 1.3-1 1-3.3-.7-5.2-1.6-1.6-3.9-2.3-5.2-1m-10.8-8.1c-.7 1.3.3 2.9 2.3 3.9 1.6 1 3.6.7 4.3-.7.7-1.3-.3-2.9-2.3-3.9-2-.6-3.6-.3-4.3.7m32.4 35.6c-1.6 1.3-1 4.3 1.3 6.2 2.3 2.3 5.2 2.6 6.5 1 1.3-1.3.7-4.3-1.3-6.2-2.2-2.3-5.2-2.6-6.5-1m-11.4-14.7c-1.6 1-1.6 3.6 0 5.9s4.3 3.3 5.6 2.3c1.6-1.3 1.6-3.9 0-6.2-1.4-2.3-4-3.3-5.6-2"/></svg> </div> <div class=md-source__repository> jingminzhang/cauldron </div> </a> </div> </nav> <nav class=md-tabs aria-label=标签 data-md-component=tabs> <div class=md-grid> <ul class=md-tabs__list> <li class=md-tabs__item> <a href=../.. class=md-tabs__link> 🏠 首页 </a> </li> <li class=md-tabs__item> <a href=../../getting-started/quick-start/ class=md-tabs__link> 🚀 快速开始 </a> </li> <li class=md-tabs__item> <a href=../../reference/ai_personalities/ class=md-tabs__link> 🎭 AI仙人 </a> </li> <li class=md-tabs__item> <a href=../../architecture/ARCHITECTURE_NOTES/ class=md-tabs__link> 🏗️ 架构设计 </a> </li> <li class=md-tabs__item> <a href=../../how-to-guides/features/ class=md-tabs__link> ⚡ 核心功能 </a> </li> <li class=md-tabs__item> <a href=../../reference/database_overview/ class=md-tabs__link> 🔧 技术文档 </a> </li> <li class=md-tabs__item> <a href=../../vision/three-tiers/ class=md-tabs__link> 📜 项目愿景 </a> </li> <li class=md-tabs__item> <a href=../../how-to-guides/deployment/ class=md-tabs__link> 🚀 部署指南 </a> </li> <li class=md-tabs__item> <a href=../../api/ class=md-tabs__link> 📡 API文档 </a> </li> <li class=md-tabs__item> <a href=../../CONTRIBUTING/ class=md-tabs__link> 🤝 贡献指南 </a> </li> </ul> </div> </nav> </header> <div class=md-container data-md-component=container> <main class=md-main data-md-component=main> <div class="md-main__inner md-grid"> <div class="md-sidebar md-sidebar--primary" data-md-component=sidebar data-md-type=navigation> <div class=md-sidebar__scrollwrap> <div class=md-sidebar__inner> <nav class="md-nav md-nav--primary md-nav--lifted" aria-label=导航栏 data-md-level=0> <label class=md-nav__title for=__drawer> <a href=../.. title="炼妖壶 (Cauldron)" class="md-nav__button md-logo" aria-label="炼妖壶 (Cauldron)" data-md-component=logo> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M12 3 1 9l11 6 9-4.91V17h2V9M5 13.18v4L12 21l7-3.82v-4L12 17z"/></svg> </a> 炼妖壶 (Cauldron) </label> <div class=md-nav__source> <a href=https://github.com/jingminzhang/cauldron title=前往仓库 class=md-source data-md-component=source> <div class="md-source__icon md-icon"> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 496 512"><!-- Font Awesome Free 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license/free (Icons: CC BY 4.0, Fonts: SIL OFL 1.1, Code: MIT License) Copyright 2024 Fonticons, Inc.--><path d="M165.9 397.4c0 2-2.3 3.6-5.2 3.6-3.3.3-5.6-1.3-5.6-3.6 0-2 2.3-3.6 5.2-3.6 3-.3 5.6 1.3 5.6 3.6m-31.1-4.5c-.7 2 1.3 4.3 4.3 4.9 2.6 1 5.6 0 6.2-2s-1.3-4.3-4.3-5.2c-2.6-.7-5.5.3-6.2 2.3m44.2-1.7c-2.9.7-4.9 2.6-4.6 4.9.3 2 2.9 3.3 5.9 2.6 2.9-.7 4.9-2.6 4.6-4.6-.3-1.9-3-3.2-5.9-2.9M244.8 8C106.1 8 0 113.3 0 252c0 110.9 69.8 205.8 169.5 239.2 12.8 2.3 17.3-5.6 17.3-12.1 0-6.2-.3-40.4-.3-61.4 0 0-70 15-84.7-29.8 0 0-11.4-29.1-27.8-36.6 0 0-22.9-15.7 1.6-15.4 0 0 24.9 2 38.6 25.8 21.9 38.6 58.6 27.5 72.9 20.9 2.3-16 8.8-27.1 16-33.7-55.9-6.2-112.3-14.3-112.3-110.5 0-27.5 7.6-41.3 23.6-58.9-2.6-6.5-11.1-33.3 2.6-67.9 20.9-6.5 69 27 69 27 20-5.6 41.5-8.5 62.8-8.5s42.8 2.9 62.8 8.5c0 0 48.1-33.6 69-27 13.7 34.7 5.2 61.4 2.6 67.9 16 17.7 25.8 31.5 25.8 58.9 0 96.5-58.9 104.2-114.8 110.5 9.2 7.9 17 22.9 17 46.4 0 33.7-.3 75.4-.3 83.6 0 6.5 4.6 14.4 17.3 12.1C428.2 457.8 496 362.9 496 252 496 113.3 383.5 8 244.8 8M97.2 352.9c-1.3 1-1 3.3.7 5.2 1.6 1.6 3.9 2.3 5.2 1 1.3-1 1-3.3-.7-5.2-1.6-1.6-3.9-2.3-5.2-1m-10.8-8.1c-.7 1.3.3 2.9 2.3 3.9 1.6 1 3.6.7 4.3-.7.7-1.3-.3-2.9-2.3-3.9-2-.6-3.6-.3-4.3.7m32.4 35.6c-1.6 1.3-1 4.3 1.3 6.2 2.3 2.3 5.2 2.6 6.5 1 1.3-1.3.7-4.3-1.3-6.2-2.2-2.3-5.2-2.6-6.5-1m-11.4-14.7c-1.6 1-1.6 3.6 0 5.9s4.3 3.3 5.6 2.3c1.6-1.3 1.6-3.9 0-6.2-1.4-2.3-4-3.3-5.6-2"/></svg> </div> <div class=md-source__repository> jingminzhang/cauldron </div> </a> </div> <ul class=md-nav__list data-md-scrollfix> <li class="md-nav__item md-nav__item--nested"> <input class="md-nav__toggle md-toggle md-toggle--indeterminate" type=checkbox id=__nav_1> <div class="md-nav__link md-nav__container"> <a href=../.. class="md-nav__link "> <span class=md-ellipsis> 🏠 首页 </span> </a> <label class="md-nav__link " for=__nav_1 id=__nav_1_label tabindex=0> <span class="md-nav__icon md-icon"></span> </label> </div> <nav class=md-nav data-md-level=1 aria-labelledby=__nav_1_label aria-expanded=false> <label class=md-nav__title for=__nav_1> <span class="md-nav__icon md-icon"></span> 🏠 首页 </label> <ul class=md-nav__list data-md-scrollfix> <li class=md-nav__item> <a href=../../roadmap/ class=md-nav__link> <span class=md-ellipsis> 发展路线图 </span> </a> </li> </ul> </nav> </li> <li class="md-nav__item md-nav__item--nested"> <input class="md-nav__toggle md-toggle md-toggle--indeterminate" type=checkbox id=__nav_2> <label class=md-nav__link for=__nav_2 id=__nav_2_label tabindex=0> <span class=md-ellipsis> 🚀 快速开始 </span> <span class="md-nav__icon md-icon"></span> </label> <nav class=md-nav data-md-level=1 aria-labelledby=__nav_2_label aria-expanded=false> <label class=md-nav__title for=__nav_2> <span class="md-nav__icon md-icon"></span> 🚀 快速开始 </label> <ul class=md-nav__list data-md-scrollfix> <li class=md-nav__item> <a href=../../getting-started/quick-start/ class=md-nav__link> <span class=md-ellipsis> 快速开始 </span> </a> </li> <li class=md-nav__item> <a href=../../tutorials/getting_started/ class=md-nav__link> <span class=md-ellipsis> 入门教程 </span> </a> </li> <li class=md-nav__item> <a href=../../how-to-guides/getting_started/ class=md-nav__link> <span class=md-ellipsis> 使用指南 </span> </a> </li> </ul> </nav> </li> <li class="md-nav__item md-nav__item--nested"> <input class="md-nav__toggle md-toggle md-toggle--indeterminate" type=checkbox id=__nav_3> <label class=md-nav__link for=__nav_3 id=__nav_3_label tabindex=0> <span class=md-ellipsis> 🎭 AI仙人 </span> <span class="md-nav__icon md-icon"></span> </label> <nav class=md-nav data-md-level=1 aria-labelledby=__nav_3_label aria-expanded=false> <label class=md-nav__title for=__nav_3> <span class="md-nav__icon md-icon"></span> 🎭 AI仙人 </label> <ul class=md-nav__list data-md-scrollfix> <li class=md-nav__item> <a href=../../reference/ai_personalities/ class=md-nav__link> <span class=md-ellipsis> AI人格设计 </span> </a> </li> </ul> </nav> </li> <li class="md-nav__item md-nav__item--nested"> <input class="md-nav__toggle md-toggle md-toggle--indeterminate" type=checkbox id=__nav_4> <label class=md-nav__link for=__nav_4 id=__nav_4_label tabindex=0> <span class=md-ellipsis> 🏗️ 架构设计 </span> <span class="md-nav__icon md-icon"></span> </label> <nav class=md-nav data-md-level=1 aria-labelledby=__nav_4_label aria-expanded=false> <label class=md-nav__title for=__nav_4> <span class="md-nav__icon md-icon"></span> 🏗️ 架构设计 </label> <ul class=md-nav__list data-md-scrollfix> <li class=md-nav__item> <a href=../../architecture/ARCHITECTURE_NOTES/ class=md-nav__link> <span class=md-ellipsis> 架构笔记 </span> </a> </li> <li class=md-nav__item> <a href=../../architecture/Omnipresent_AI_Agent_Architecture/ class=md-nav__link> <span class=md-ellipsis> 全能AI智能体架构 </span> </a> </li> </ul> </nav> </li> <li class="md-nav__item md-nav__item--nested"> <input class="md-nav__toggle md-toggle md-toggle--indeterminate" type=checkbox id=__nav_5> <label class=md-nav__link for=__nav_5 id=__nav_5_label tabindex=0> <span class=md-ellipsis> ⚡ 核心功能 </span> <span class="md-nav__icon md-icon"></span> </label> <nav class=md-nav data-md-level=1 aria-labelledby=__nav_5_label aria-expanded=false> <label class=md-nav__title for=__nav_5> <span class="md-nav__icon md-icon"></span> ⚡ 核心功能 </label> <ul class=md-nav__list data-md-scrollfix> <li class=md-nav__item> <a href=../../how-to-guides/features/ class=md-nav__link> <span class=md-ellipsis> 功能概览 </span> </a> </li> <li class=md-nav__item> <a href=../../how-to-guides/offering/ class=md-nav__link> <span class=md-ellipsis> 产品定位 </span> </a> </li> </ul> </nav> </li> <li class="md-nav__item md-nav__item--nested"> <input class="md-nav__toggle md-toggle md-toggle--indeterminate" type=checkbox id=__nav_6> <label class=md-nav__link for=__nav_6 id=__nav_6_label tabindex=0> <span class=md-ellipsis> 🔧 技术文档 </span> <span class="md-nav__icon md-icon"></span> </label> <nav class=md-nav data-md-level=1 aria-labelledby=__nav_6_label aria-expanded=false> <label class=md-nav__title for=__nav_6> <span class="md-nav__icon md-icon"></span> 🔧 技术文档 </label> <ul class=md-nav__list data-md-scrollfix> <li class=md-nav__item> <a href=../../reference/database_overview/ class=md-nav__link> <span class=md-ellipsis> 数据库概览 </span> </a> </li> <li class=md-nav__item> <a href=../../guides/MCP_SETUP/ class=md-nav__link> <span class=md-ellipsis> MCP设置 </span> </a> </li> <li class=md-nav__item> <a href=../../how-to-guides/ib_fundamentals_guide/ class=md-nav__link> <span class=md-ellipsis> IB基础指南 </span> </a> </li> </ul> </nav> </li> <li class="md-nav__item md-nav__item--nested"> <input class="md-nav__toggle md-toggle md-toggle--indeterminate" type=checkbox id=__nav_7> <label class=md-nav__link for=__nav_7 id=__nav_7_label tabindex=0> <span class=md-ellipsis> 📜 项目愿景 </span> <span class="md-nav__icon md-icon"></span> </label> <nav class=md-nav data-md-level=1 aria-labelledby=__nav_7_label aria-expanded=false> <label class=md-nav__title for=__nav_7> <span class="md-nav__icon md-icon"></span> 📜 项目愿景 </label> <ul class=md-nav__list data-md-scrollfix> <li class=md-nav__item> <a href=../../vision/three-tiers/ class=md-nav__link> <span class=md-ellipsis> 三层架构 </span> </a> </li> <li class=md-nav__item> <a href=../../guides/OPENSOURCE_ROADMAP/ class=md-nav__link> <span class=md-ellipsis> 开源路线图 </span> </a> </li> </ul> </nav> </li> <li class="md-nav__item md-nav__item--nested"> <input class="md-nav__toggle md-toggle md-toggle--indeterminate" type=checkbox id=__nav_8> <label class=md-nav__link for=__nav_8 id=__nav_8_label tabindex=0> <span class=md-ellipsis> 🚀 部署指南 </span> <span class="md-nav__icon md-icon"></span> </label> <nav class=md-nav data-md-level=1 aria-labelledby=__nav_8_label aria-expanded=false> <label class=md-nav__title for=__nav_8> <span class="md-nav__icon md-icon"></span> 🚀 部署指南 </label> <ul class=md-nav__list data-md-scrollfix> <li class=md-nav__item> <a href=../../how-to-guides/deployment/ class=md-nav__link> <span class=md-ellipsis> 部署指南 </span> </a> </li> </ul> </nav> </li> <li class="md-nav__item md-nav__item--nested"> <input class="md-nav__toggle md-toggle md-toggle--indeterminate" type=checkbox id=__nav_9> <label class=md-nav__link for=__nav_9 id=__nav_9_label tabindex=0> <span class=md-ellipsis> 📡 API文档 </span> <span class="md-nav__icon md-icon"></span> </label> <nav class=md-nav data-md-level=1 aria-labelledby=__nav_9_label aria-expanded=false> <label class=md-nav__title for=__nav_9> <span class="md-nav__icon md-icon"></span> 📡 API文档 </label> <ul class=md-nav__list data-md-scrollfix> <li class=md-nav__item> <a href=../../api/ class=md-nav__link> <span class=md-ellipsis> API参考 </span> </a> </li> </ul> </nav> </li> <li class="md-nav__item md-nav__item--nested"> <input class="md-nav__toggle md-toggle md-toggle--indeterminate" type=checkbox id=__nav_10> <label class=md-nav__link for=__nav_10 id=__nav_10_label tabindex=0> <span class=md-ellipsis> 🤝 贡献指南 </span> <span class="md-nav__icon md-icon"></span> </label> <nav class=md-nav data-md-level=1 aria-labelledby=__nav_10_label aria-expanded=false> <label class=md-nav__title for=__nav_10> <span class="md-nav__icon md-icon"></span> 🤝 贡献指南 </label> <ul class=md-nav__list data-md-scrollfix> <li class=md-nav__item> <a href=../../CONTRIBUTING/ class=md-nav__link> <span class=md-ellipsis> 贡献指南 </span> </a> </li> <li class=md-nav__item> <a href=../../guides/DOCS_REORGANIZATION_PLAN/ class=md-nav__link> <span class=md-ellipsis> 文档重组计划 </span> </a> </li> </ul> </nav> </li> </ul> </nav> </div> </div> </div> <div class="md-sidebar md-sidebar--secondary" data-md-component=sidebar data-md-type=toc> <div class=md-sidebar__scrollwrap> <div class=md-sidebar__inner> <nav class="md-nav md-nav--secondary" aria-label=目录> <label class=md-nav__title for=__toc> <span class="md-nav__icon md-icon"></span> 目录 </label> <ul class=md-nav__list data-md-component=toc data-md-scrollfix> <li class=md-nav__item> <a href=#kagmilvus class=md-nav__link> <span class=md-ellipsis> 🎯 澄清：KAG与Milvus的关系 </span> </a> <nav class=md-nav aria-label="🎯 澄清：KAG与Milvus的关系"> <ul class=md-nav__list> <li class=md-nav__item> <a href=#_1 class=md-nav__link> <span class=md-ellipsis> 误解澄清 </span> </a> </li> </ul> </nav> </li> <li class=md-nav__item> <a href=#kag-milvus_1 class=md-nav__link> <span class=md-ellipsis> 🏗️ KAG + Milvus 混合架构 </span> </a> <nav class=md-nav aria-label="🏗️ KAG + Milvus 混合架构"> <ul class=md-nav__list> <li class=md-nav__item> <a href=#_2 class=md-nav__link> <span class=md-ellipsis> 架构设计 </span> </a> </li> <li class=md-nav__item> <a href=#_3 class=md-nav__link> <span class=md-ellipsis> 具体实现方案 </span> </a> <nav class=md-nav aria-label=具体实现方案> <ul class=md-nav__list> <li class=md-nav__item> <a href=#1-kag class=md-nav__link> <span class=md-ellipsis> 方案1: KAG作为知识增强层 </span> </a> </li> <li class=md-nav__item> <a href=#2 class=md-nav__link> <span class=md-ellipsis> 方案2: 分层架构 </span> </a> </li> </ul> </nav> </li> </ul> </nav> </li> <li class=md-nav__item> <a href=#_4 class=md-nav__link> <span class=md-ellipsis> 🔄 与你现有系统的集成 </span> </a> <nav class=md-nav aria-label="🔄 与你现有系统的集成"> <ul class=md-nav__list> <li class=md-nav__item> <a href=#_5 class=md-nav__link> <span class=md-ellipsis> 当前架构 </span> </a> </li> <li class=md-nav__item> <a href=#_6 class=md-nav__link> <span class=md-ellipsis> 升级后架构 </span> </a> </li> <li class=md-nav__item> <a href=#n8n class=md-nav__link> <span class=md-ellipsis> N8N集成代码示例 </span> </a> </li> </ul> </nav> </li> <li class=md-nav__item> <a href=#_7 class=md-nav__link> <span class=md-ellipsis> 🎯 集成的具体好处 </span> </a> <nav class=md-nav aria-label="🎯 集成的具体好处"> <ul class=md-nav__list> <li class=md-nav__item> <a href=#1 class=md-nav__link> <span class=md-ellipsis> 1. 保留现有投资 </span> </a> </li> <li class=md-nav__item> <a href=#2-kag class=md-nav__link> <span class=md-ellipsis> 2. 获得KAG增强 </span> </a> </li> <li class=md-nav__item> <a href=#3 class=md-nav__link> <span class=md-ellipsis> 3. 最佳实践架构 </span> </a> </li> </ul> </nav> </li> <li class=md-nav__item> <a href=#_8 class=md-nav__link> <span class=md-ellipsis> 🚀 实施路径 </span> </a> <nav class=md-nav aria-label="🚀 实施路径"> <ul class=md-nav__list> <li class=md-nav__item> <a href=#phase-1 class=md-nav__link> <span class=md-ellipsis> Phase 1: 并行部署 </span> </a> </li> <li class=md-nav__item> <a href=#phase-2 class=md-nav__link> <span class=md-ellipsis> Phase 2: 混合架构 </span> </a> </li> <li class=md-nav__item> <a href=#phase-3 class=md-nav__link> <span class=md-ellipsis> Phase 3: 优化整合 </span> </a> </li> </ul> </nav> </li> <li class=md-nav__item> <a href=#_9 class=md-nav__link> <span class=md-ellipsis> 💡 总结 </span> </a> </li> </ul> </nav> </div> </div> </div> <div class=md-content data-md-component=content> <article class="md-content__inner md-typeset"> <h1 id=kag-milvus>KAG + Milvus 集成架构详解<a class=headerlink href=#kag-milvus title="Permanent link">&para;</a></h1> <h2 id=kagmilvus>🎯 澄清：KAG与Milvus的关系<a class=headerlink href=#kagmilvus title="Permanent link">&para;</a></h2> <h3 id=_1>误解澄清<a class=headerlink href=#_1 title="Permanent link">&para;</a></h3> <p>我之前的表述可能造成了混淆。让我明确说明：</p> <p><strong>KAG ≠ 替代Milvus</strong> <strong>KAG = 增强Milvus的知识图谱能力</strong></p> <h2 id=kag-milvus_1>🏗️ KAG + Milvus 混合架构<a class=headerlink href=#kag-milvus_1 title="Permanent link">&para;</a></h2> <h3 id=_2>架构设计<a class=headerlink href=#_2 title="Permanent link">&para;</a></h3> <div class="language-text highlight"><pre><span></span><code><span id=__span-0-1><a id=__codelineno-0-1 name=__codelineno-0-1 href=#__codelineno-0-1></a>数据输入 → KAG知识抽取 → 双路存储 → 混合检索 → 增强生成
</span><span id=__span-0-2><a id=__codelineno-0-2 name=__codelineno-0-2 href=#__codelineno-0-2></a>                ↓              ↓         ↓
</span><span id=__span-0-3><a id=__codelineno-0-3 name=__codelineno-0-3 href=#__codelineno-0-3></a>            知识图谱        向量数据库    图谱+向量
</span><span id=__span-0-4><a id=__codelineno-0-4 name=__codelineno-0-4 href=#__codelineno-0-4></a>           (Neo4j/内存)      (Milvus)    联合查询
</span></code></pre></div> <h3 id=_3>具体实现方案<a class=headerlink href=#_3 title="Permanent link">&para;</a></h3> <h4 id=1-kag>方案1: KAG作为知识增强层<a class=headerlink href=#1-kag title="Permanent link">&para;</a></h4> <div class="language-python highlight"><pre><span></span><code><span id=__span-1-1><a id=__codelineno-1-1 name=__codelineno-1-1 href=#__codelineno-1-1></a><span class=k>class</span><span class=w> </span><span class=nc>KAGMilvusHybridSystem</span><span class=p>:</span>
</span><span id=__span-1-2><a id=__codelineno-1-2 name=__codelineno-1-2 href=#__codelineno-1-2></a><span class=w>    </span><span class=sd>&quot;&quot;&quot;KAG + Milvus 混合系统&quot;&quot;&quot;</span>
</span><span id=__span-1-3><a id=__codelineno-1-3 name=__codelineno-1-3 href=#__codelineno-1-3></a>
</span><span id=__span-1-4><a id=__codelineno-1-4 name=__codelineno-1-4 href=#__codelineno-1-4></a>    <span class=k>def</span><span class=w> </span><span class=fm>__init__</span><span class=p>(</span><span class=bp>self</span><span class=p>):</span>
</span><span id=__span-1-5><a id=__codelineno-1-5 name=__codelineno-1-5 href=#__codelineno-1-5></a>        <span class=c1># KAG负责知识图谱构建和推理</span>
</span><span id=__span-1-6><a id=__codelineno-1-6 name=__codelineno-1-6 href=#__codelineno-1-6></a>        <span class=bp>self</span><span class=o>.</span><span class=n>kag_processor</span> <span class=o>=</span> <span class=n>KAGProcessor</span><span class=p>()</span>
</span><span id=__span-1-7><a id=__codelineno-1-7 name=__codelineno-1-7 href=#__codelineno-1-7></a>
</span><span id=__span-1-8><a id=__codelineno-1-8 name=__codelineno-1-8 href=#__codelineno-1-8></a>        <span class=c1># Milvus负责向量存储和检索</span>
</span><span id=__span-1-9><a id=__codelineno-1-9 name=__codelineno-1-9 href=#__codelineno-1-9></a>        <span class=bp>self</span><span class=o>.</span><span class=n>milvus_client</span> <span class=o>=</span> <span class=n>MilvusClient</span><span class=p>()</span>
</span><span id=__span-1-10><a id=__codelineno-1-10 name=__codelineno-1-10 href=#__codelineno-1-10></a>
</span><span id=__span-1-11><a id=__codelineno-1-11 name=__codelineno-1-11 href=#__codelineno-1-11></a>        <span class=c1># 混合检索器</span>
</span><span id=__span-1-12><a id=__codelineno-1-12 name=__codelineno-1-12 href=#__codelineno-1-12></a>        <span class=bp>self</span><span class=o>.</span><span class=n>hybrid_retriever</span> <span class=o>=</span> <span class=n>HybridRetriever</span><span class=p>()</span>
</span><span id=__span-1-13><a id=__codelineno-1-13 name=__codelineno-1-13 href=#__codelineno-1-13></a>
</span><span id=__span-1-14><a id=__codelineno-1-14 name=__codelineno-1-14 href=#__codelineno-1-14></a>    <span class=k>async</span> <span class=k>def</span><span class=w> </span><span class=nf>process_document</span><span class=p>(</span><span class=bp>self</span><span class=p>,</span> <span class=n>document</span><span class=p>):</span>
</span><span id=__span-1-15><a id=__codelineno-1-15 name=__codelineno-1-15 href=#__codelineno-1-15></a><span class=w>        </span><span class=sd>&quot;&quot;&quot;文档处理流程&quot;&quot;&quot;</span>
</span><span id=__span-1-16><a id=__codelineno-1-16 name=__codelineno-1-16 href=#__codelineno-1-16></a>
</span><span id=__span-1-17><a id=__codelineno-1-17 name=__codelineno-1-17 href=#__codelineno-1-17></a>        <span class=c1># 1. KAG提取知识图谱</span>
</span><span id=__span-1-18><a id=__codelineno-1-18 name=__codelineno-1-18 href=#__codelineno-1-18></a>        <span class=n>knowledge_graph</span> <span class=o>=</span> <span class=k>await</span> <span class=bp>self</span><span class=o>.</span><span class=n>kag_processor</span><span class=o>.</span><span class=n>extract_knowledge</span><span class=p>(</span><span class=n>document</span><span class=p>)</span>
</span><span id=__span-1-19><a id=__codelineno-1-19 name=__codelineno-1-19 href=#__codelineno-1-19></a>
</span><span id=__span-1-20><a id=__codelineno-1-20 name=__codelineno-1-20 href=#__codelineno-1-20></a>        <span class=c1># 2. 同时进行向量化存储到Milvus</span>
</span><span id=__span-1-21><a id=__codelineno-1-21 name=__codelineno-1-21 href=#__codelineno-1-21></a>        <span class=n>vector_embedding</span> <span class=o>=</span> <span class=k>await</span> <span class=bp>self</span><span class=o>.</span><span class=n>embed_document</span><span class=p>(</span><span class=n>document</span><span class=p>)</span>
</span><span id=__span-1-22><a id=__codelineno-1-22 name=__codelineno-1-22 href=#__codelineno-1-22></a>        <span class=n>milvus_id</span> <span class=o>=</span> <span class=k>await</span> <span class=bp>self</span><span class=o>.</span><span class=n>milvus_client</span><span class=o>.</span><span class=n>insert</span><span class=p>({</span>
</span><span id=__span-1-23><a id=__codelineno-1-23 name=__codelineno-1-23 href=#__codelineno-1-23></a>            <span class=s2>&quot;vector&quot;</span><span class=p>:</span> <span class=n>vector_embedding</span><span class=p>,</span>
</span><span id=__span-1-24><a id=__codelineno-1-24 name=__codelineno-1-24 href=#__codelineno-1-24></a>            <span class=s2>&quot;text&quot;</span><span class=p>:</span> <span class=n>document</span><span class=o>.</span><span class=n>content</span><span class=p>,</span>
</span><span id=__span-1-25><a id=__codelineno-1-25 name=__codelineno-1-25 href=#__codelineno-1-25></a>            <span class=s2>&quot;metadata&quot;</span><span class=p>:</span> <span class=n>document</span><span class=o>.</span><span class=n>metadata</span><span class=p>,</span>
</span><span id=__span-1-26><a id=__codelineno-1-26 name=__codelineno-1-26 href=#__codelineno-1-26></a>            <span class=s2>&quot;kg_entities&quot;</span><span class=p>:</span> <span class=n>knowledge_graph</span><span class=o>.</span><span class=n>entities</span><span class=p>,</span>  <span class=c1># 关联KG实体</span>
</span><span id=__span-1-27><a id=__codelineno-1-27 name=__codelineno-1-27 href=#__codelineno-1-27></a>            <span class=s2>&quot;kg_relations&quot;</span><span class=p>:</span> <span class=n>knowledge_graph</span><span class=o>.</span><span class=n>relations</span>  <span class=c1># 关联KG关系</span>
</span><span id=__span-1-28><a id=__codelineno-1-28 name=__codelineno-1-28 href=#__codelineno-1-28></a>        <span class=p>})</span>
</span><span id=__span-1-29><a id=__codelineno-1-29 name=__codelineno-1-29 href=#__codelineno-1-29></a>
</span><span id=__span-1-30><a id=__codelineno-1-30 name=__codelineno-1-30 href=#__codelineno-1-30></a>        <span class=c1># 3. 在KG中记录向量ID，建立双向关联</span>
</span><span id=__span-1-31><a id=__codelineno-1-31 name=__codelineno-1-31 href=#__codelineno-1-31></a>        <span class=k>await</span> <span class=bp>self</span><span class=o>.</span><span class=n>kag_processor</span><span class=o>.</span><span class=n>link_to_vector_store</span><span class=p>(</span>
</span><span id=__span-1-32><a id=__codelineno-1-32 name=__codelineno-1-32 href=#__codelineno-1-32></a>            <span class=n>knowledge_graph</span><span class=p>,</span> 
</span><span id=__span-1-33><a id=__codelineno-1-33 name=__codelineno-1-33 href=#__codelineno-1-33></a>            <span class=n>milvus_id</span>
</span><span id=__span-1-34><a id=__codelineno-1-34 name=__codelineno-1-34 href=#__codelineno-1-34></a>        <span class=p>)</span>
</span><span id=__span-1-35><a id=__codelineno-1-35 name=__codelineno-1-35 href=#__codelineno-1-35></a>
</span><span id=__span-1-36><a id=__codelineno-1-36 name=__codelineno-1-36 href=#__codelineno-1-36></a>        <span class=k>return</span> <span class=p>{</span>
</span><span id=__span-1-37><a id=__codelineno-1-37 name=__codelineno-1-37 href=#__codelineno-1-37></a>            <span class=s2>&quot;knowledge_graph&quot;</span><span class=p>:</span> <span class=n>knowledge_graph</span><span class=p>,</span>
</span><span id=__span-1-38><a id=__codelineno-1-38 name=__codelineno-1-38 href=#__codelineno-1-38></a>            <span class=s2>&quot;vector_id&quot;</span><span class=p>:</span> <span class=n>milvus_id</span>
</span><span id=__span-1-39><a id=__codelineno-1-39 name=__codelineno-1-39 href=#__codelineno-1-39></a>        <span class=p>}</span>
</span><span id=__span-1-40><a id=__codelineno-1-40 name=__codelineno-1-40 href=#__codelineno-1-40></a>
</span><span id=__span-1-41><a id=__codelineno-1-41 name=__codelineno-1-41 href=#__codelineno-1-41></a>    <span class=k>async</span> <span class=k>def</span><span class=w> </span><span class=nf>hybrid_query</span><span class=p>(</span><span class=bp>self</span><span class=p>,</span> <span class=n>question</span><span class=p>):</span>
</span><span id=__span-1-42><a id=__codelineno-1-42 name=__codelineno-1-42 href=#__codelineno-1-42></a><span class=w>        </span><span class=sd>&quot;&quot;&quot;混合查询&quot;&quot;&quot;</span>
</span><span id=__span-1-43><a id=__codelineno-1-43 name=__codelineno-1-43 href=#__codelineno-1-43></a>
</span><span id=__span-1-44><a id=__codelineno-1-44 name=__codelineno-1-44 href=#__codelineno-1-44></a>        <span class=c1># 1. KAG图谱推理</span>
</span><span id=__span-1-45><a id=__codelineno-1-45 name=__codelineno-1-45 href=#__codelineno-1-45></a>        <span class=n>kg_results</span> <span class=o>=</span> <span class=k>await</span> <span class=bp>self</span><span class=o>.</span><span class=n>kag_processor</span><span class=o>.</span><span class=n>graph_reasoning</span><span class=p>(</span><span class=n>question</span><span class=p>)</span>
</span><span id=__span-1-46><a id=__codelineno-1-46 name=__codelineno-1-46 href=#__codelineno-1-46></a>
</span><span id=__span-1-47><a id=__codelineno-1-47 name=__codelineno-1-47 href=#__codelineno-1-47></a>        <span class=c1># 2. Milvus向量检索</span>
</span><span id=__span-1-48><a id=__codelineno-1-48 name=__codelineno-1-48 href=#__codelineno-1-48></a>        <span class=n>vector_results</span> <span class=o>=</span> <span class=k>await</span> <span class=bp>self</span><span class=o>.</span><span class=n>milvus_client</span><span class=o>.</span><span class=n>search</span><span class=p>(</span>
</span><span id=__span-1-49><a id=__codelineno-1-49 name=__codelineno-1-49 href=#__codelineno-1-49></a>            <span class=n>question</span><span class=p>,</span> 
</span><span id=__span-1-50><a id=__codelineno-1-50 name=__codelineno-1-50 href=#__codelineno-1-50></a>            <span class=n>limit</span><span class=o>=</span><span class=mi>10</span>
</span><span id=__span-1-51><a id=__codelineno-1-51 name=__codelineno-1-51 href=#__codelineno-1-51></a>        <span class=p>)</span>
</span><span id=__span-1-52><a id=__codelineno-1-52 name=__codelineno-1-52 href=#__codelineno-1-52></a>
</span><span id=__span-1-53><a id=__codelineno-1-53 name=__codelineno-1-53 href=#__codelineno-1-53></a>        <span class=c1># 3. 结果融合</span>
</span><span id=__span-1-54><a id=__codelineno-1-54 name=__codelineno-1-54 href=#__codelineno-1-54></a>        <span class=n>fused_results</span> <span class=o>=</span> <span class=k>await</span> <span class=bp>self</span><span class=o>.</span><span class=n>hybrid_retriever</span><span class=o>.</span><span class=n>fuse_results</span><span class=p>(</span>
</span><span id=__span-1-55><a id=__codelineno-1-55 name=__codelineno-1-55 href=#__codelineno-1-55></a>            <span class=n>kg_results</span><span class=p>,</span> 
</span><span id=__span-1-56><a id=__codelineno-1-56 name=__codelineno-1-56 href=#__codelineno-1-56></a>            <span class=n>vector_results</span>
</span><span id=__span-1-57><a id=__codelineno-1-57 name=__codelineno-1-57 href=#__codelineno-1-57></a>        <span class=p>)</span>
</span><span id=__span-1-58><a id=__codelineno-1-58 name=__codelineno-1-58 href=#__codelineno-1-58></a>
</span><span id=__span-1-59><a id=__codelineno-1-59 name=__codelineno-1-59 href=#__codelineno-1-59></a>        <span class=k>return</span> <span class=n>fused_results</span>
</span></code></pre></div> <h4 id=2>方案2: 分层架构<a class=headerlink href=#2 title="Permanent link">&para;</a></h4> <div class="language-python highlight"><pre><span></span><code><span id=__span-2-1><a id=__codelineno-2-1 name=__codelineno-2-1 href=#__codelineno-2-1></a><span class=k>class</span><span class=w> </span><span class=nc>LayeredKAGMilvusSystem</span><span class=p>:</span>
</span><span id=__span-2-2><a id=__codelineno-2-2 name=__codelineno-2-2 href=#__codelineno-2-2></a><span class=w>    </span><span class=sd>&quot;&quot;&quot;分层架构：KAG在上层，Milvus在底层&quot;&quot;&quot;</span>
</span><span id=__span-2-3><a id=__codelineno-2-3 name=__codelineno-2-3 href=#__codelineno-2-3></a>
</span><span id=__span-2-4><a id=__codelineno-2-4 name=__codelineno-2-4 href=#__codelineno-2-4></a>    <span class=k>def</span><span class=w> </span><span class=fm>__init__</span><span class=p>(</span><span class=bp>self</span><span class=p>):</span>
</span><span id=__span-2-5><a id=__codelineno-2-5 name=__codelineno-2-5 href=#__codelineno-2-5></a>        <span class=bp>self</span><span class=o>.</span><span class=n>layers</span> <span class=o>=</span> <span class=p>{</span>
</span><span id=__span-2-6><a id=__codelineno-2-6 name=__codelineno-2-6 href=#__codelineno-2-6></a>            <span class=s2>&quot;knowledge_layer&quot;</span><span class=p>:</span> <span class=n>KAGKnowledgeLayer</span><span class=p>(),</span>    <span class=c1># 知识图谱层</span>
</span><span id=__span-2-7><a id=__codelineno-2-7 name=__codelineno-2-7 href=#__codelineno-2-7></a>            <span class=s2>&quot;semantic_layer&quot;</span><span class=p>:</span> <span class=n>MilvusSemanticLayer</span><span class=p>(),</span>   <span class=c1># 语义向量层</span>
</span><span id=__span-2-8><a id=__codelineno-2-8 name=__codelineno-2-8 href=#__codelineno-2-8></a>            <span class=s2>&quot;fusion_layer&quot;</span><span class=p>:</span> <span class=n>ResultFusionLayer</span><span class=p>()</span>       <span class=c1># 结果融合层</span>
</span><span id=__span-2-9><a id=__codelineno-2-9 name=__codelineno-2-9 href=#__codelineno-2-9></a>        <span class=p>}</span>
</span><span id=__span-2-10><a id=__codelineno-2-10 name=__codelineno-2-10 href=#__codelineno-2-10></a>
</span><span id=__span-2-11><a id=__codelineno-2-11 name=__codelineno-2-11 href=#__codelineno-2-11></a>    <span class=k>async</span> <span class=k>def</span><span class=w> </span><span class=nf>query</span><span class=p>(</span><span class=bp>self</span><span class=p>,</span> <span class=n>question</span><span class=p>,</span> <span class=n>query_type</span><span class=o>=</span><span class=s2>&quot;hybrid&quot;</span><span class=p>):</span>
</span><span id=__span-2-12><a id=__codelineno-2-12 name=__codelineno-2-12 href=#__codelineno-2-12></a>        <span class=k>if</span> <span class=n>query_type</span> <span class=o>==</span> <span class=s2>&quot;knowledge&quot;</span><span class=p>:</span>
</span><span id=__span-2-13><a id=__codelineno-2-13 name=__codelineno-2-13 href=#__codelineno-2-13></a>            <span class=c1># 纯知识图谱查询</span>
</span><span id=__span-2-14><a id=__codelineno-2-14 name=__codelineno-2-14 href=#__codelineno-2-14></a>            <span class=k>return</span> <span class=k>await</span> <span class=bp>self</span><span class=o>.</span><span class=n>layers</span><span class=p>[</span><span class=s2>&quot;knowledge_layer&quot;</span><span class=p>]</span><span class=o>.</span><span class=n>query</span><span class=p>(</span><span class=n>question</span><span class=p>)</span>
</span><span id=__span-2-15><a id=__codelineno-2-15 name=__codelineno-2-15 href=#__codelineno-2-15></a>
</span><span id=__span-2-16><a id=__codelineno-2-16 name=__codelineno-2-16 href=#__codelineno-2-16></a>        <span class=k>elif</span> <span class=n>query_type</span> <span class=o>==</span> <span class=s2>&quot;semantic&quot;</span><span class=p>:</span>
</span><span id=__span-2-17><a id=__codelineno-2-17 name=__codelineno-2-17 href=#__codelineno-2-17></a>            <span class=c1># 纯向量检索</span>
</span><span id=__span-2-18><a id=__codelineno-2-18 name=__codelineno-2-18 href=#__codelineno-2-18></a>            <span class=k>return</span> <span class=k>await</span> <span class=bp>self</span><span class=o>.</span><span class=n>layers</span><span class=p>[</span><span class=s2>&quot;semantic_layer&quot;</span><span class=p>]</span><span class=o>.</span><span class=n>query</span><span class=p>(</span><span class=n>question</span><span class=p>)</span>
</span><span id=__span-2-19><a id=__codelineno-2-19 name=__codelineno-2-19 href=#__codelineno-2-19></a>
</span><span id=__span-2-20><a id=__codelineno-2-20 name=__codelineno-2-20 href=#__codelineno-2-20></a>        <span class=k>elif</span> <span class=n>query_type</span> <span class=o>==</span> <span class=s2>&quot;hybrid&quot;</span><span class=p>:</span>
</span><span id=__span-2-21><a id=__codelineno-2-21 name=__codelineno-2-21 href=#__codelineno-2-21></a>            <span class=c1># 混合查询</span>
</span><span id=__span-2-22><a id=__codelineno-2-22 name=__codelineno-2-22 href=#__codelineno-2-22></a>            <span class=n>kg_result</span> <span class=o>=</span> <span class=k>await</span> <span class=bp>self</span><span class=o>.</span><span class=n>layers</span><span class=p>[</span><span class=s2>&quot;knowledge_layer&quot;</span><span class=p>]</span><span class=o>.</span><span class=n>query</span><span class=p>(</span><span class=n>question</span><span class=p>)</span>
</span><span id=__span-2-23><a id=__codelineno-2-23 name=__codelineno-2-23 href=#__codelineno-2-23></a>            <span class=n>vector_result</span> <span class=o>=</span> <span class=k>await</span> <span class=bp>self</span><span class=o>.</span><span class=n>layers</span><span class=p>[</span><span class=s2>&quot;semantic_layer&quot;</span><span class=p>]</span><span class=o>.</span><span class=n>query</span><span class=p>(</span><span class=n>question</span><span class=p>)</span>
</span><span id=__span-2-24><a id=__codelineno-2-24 name=__codelineno-2-24 href=#__codelineno-2-24></a>
</span><span id=__span-2-25><a id=__codelineno-2-25 name=__codelineno-2-25 href=#__codelineno-2-25></a>            <span class=k>return</span> <span class=k>await</span> <span class=bp>self</span><span class=o>.</span><span class=n>layers</span><span class=p>[</span><span class=s2>&quot;fusion_layer&quot;</span><span class=p>]</span><span class=o>.</span><span class=n>fuse</span><span class=p>(</span>
</span><span id=__span-2-26><a id=__codelineno-2-26 name=__codelineno-2-26 href=#__codelineno-2-26></a>                <span class=n>kg_result</span><span class=p>,</span> 
</span><span id=__span-2-27><a id=__codelineno-2-27 name=__codelineno-2-27 href=#__codelineno-2-27></a>                <span class=n>vector_result</span>
</span><span id=__span-2-28><a id=__codelineno-2-28 name=__codelineno-2-28 href=#__codelineno-2-28></a>            <span class=p>)</span>
</span></code></pre></div> <h2 id=_4>🔄 与你现有系统的集成<a class=headerlink href=#_4 title="Permanent link">&para;</a></h2> <h3 id=_5>当前架构<a class=headerlink href=#_5 title="Permanent link">&para;</a></h3> <div class="language-text highlight"><pre><span></span><code><span id=__span-3-1><a id=__codelineno-3-1 name=__codelineno-3-1 href=#__codelineno-3-1></a>RSS → n8n → MongoDB → Code处理 → Milvus → AutoGen辩论
</span></code></pre></div> <h3 id=_6>升级后架构<a class=headerlink href=#_6 title="Permanent link">&para;</a></h3> <div class="language-text highlight"><pre><span></span><code><span id=__span-4-1><a id=__codelineno-4-1 name=__codelineno-4-1 href=#__codelineno-4-1></a>RSS → n8n → MongoDB → KAG+Code处理 → Milvus+KnowledgeGraph → AutoGen增强辩论
</span><span id=__span-4-2><a id=__codelineno-4-2 name=__codelineno-4-2 href=#__codelineno-4-2></a>                              ↓                    ↓
</span><span id=__span-4-3><a id=__codelineno-4-3 name=__codelineno-4-3 href=#__codelineno-4-3></a>                        知识图谱构建           向量+图谱存储
</span></code></pre></div> <h3 id=n8n>N8N集成代码示例<a class=headerlink href=#n8n title="Permanent link">&para;</a></h3> <div class="language-javascript highlight"><pre><span></span><code><span id=__span-5-1><a id=__codelineno-5-1 name=__codelineno-5-1 href=#__codelineno-5-1></a><span class=c1>// N8N中的KAG+Milvus处理节点</span>
</span><span id=__span-5-2><a id=__codelineno-5-2 name=__codelineno-5-2 href=#__codelineno-5-2></a><span class=kd>const</span><span class=w> </span><span class=nx>items</span><span class=w> </span><span class=o>=</span><span class=w> </span><span class=nx>$input</span><span class=p>.</span><span class=nx>all</span><span class=p>();</span>
</span><span id=__span-5-3><a id=__codelineno-5-3 name=__codelineno-5-3 href=#__codelineno-5-3></a><span class=kd>const</span><span class=w> </span><span class=nx>processedItems</span><span class=w> </span><span class=o>=</span><span class=w> </span><span class=p>[];</span>
</span><span id=__span-5-4><a id=__codelineno-5-4 name=__codelineno-5-4 href=#__codelineno-5-4></a>
</span><span id=__span-5-5><a id=__codelineno-5-5 name=__codelineno-5-5 href=#__codelineno-5-5></a><span class=k>for</span><span class=w> </span><span class=p>(</span><span class=kd>const</span><span class=w> </span><span class=nx>item</span><span class=w> </span><span class=k>of</span><span class=w> </span><span class=nx>items</span><span class=p>)</span><span class=w> </span><span class=p>{</span>
</span><span id=__span-5-6><a id=__codelineno-5-6 name=__codelineno-5-6 href=#__codelineno-5-6></a><span class=w>    </span><span class=k>try</span><span class=w> </span><span class=p>{</span>
</span><span id=__span-5-7><a id=__codelineno-5-7 name=__codelineno-5-7 href=#__codelineno-5-7></a><span class=w>        </span><span class=kd>const</span><span class=w> </span><span class=nx>data</span><span class=w> </span><span class=o>=</span><span class=w> </span><span class=nx>item</span><span class=p>.</span><span class=nx>json</span><span class=p>;</span>
</span><span id=__span-5-8><a id=__codelineno-5-8 name=__codelineno-5-8 href=#__codelineno-5-8></a>
</span><span id=__span-5-9><a id=__codelineno-5-9 name=__codelineno-5-9 href=#__codelineno-5-9></a><span class=w>        </span><span class=c1>// 1. 基础数据清理（保持原有逻辑）</span>
</span><span id=__span-5-10><a id=__codelineno-5-10 name=__codelineno-5-10 href=#__codelineno-5-10></a><span class=w>        </span><span class=kd>const</span><span class=w> </span><span class=nx>cleanTitle</span><span class=w> </span><span class=o>=</span><span class=w> </span><span class=nx>cleanText</span><span class=p>(</span><span class=nx>data</span><span class=p>.</span><span class=nx>title</span><span class=p>);</span>
</span><span id=__span-5-11><a id=__codelineno-5-11 name=__codelineno-5-11 href=#__codelineno-5-11></a><span class=w>        </span><span class=kd>const</span><span class=w> </span><span class=nx>publishedDate</span><span class=w> </span><span class=o>=</span><span class=w> </span><span class=nx>processDateTime</span><span class=p>(</span><span class=nx>data</span><span class=p>.</span><span class=nx>published_time</span><span class=p>);</span>
</span><span id=__span-5-12><a id=__codelineno-5-12 name=__codelineno-5-12 href=#__codelineno-5-12></a>
</span><span id=__span-5-13><a id=__codelineno-5-13 name=__codelineno-5-13 href=#__codelineno-5-13></a><span class=w>        </span><span class=c1>// 2. KAG知识抽取</span>
</span><span id=__span-5-14><a id=__codelineno-5-14 name=__codelineno-5-14 href=#__codelineno-5-14></a><span class=w>        </span><span class=kd>const</span><span class=w> </span><span class=nx>kagResult</span><span class=w> </span><span class=o>=</span><span class=w> </span><span class=k>await</span><span class=w> </span><span class=nx>fetch</span><span class=p>(</span><span class=s1>&#39;http://kag-service:8080/extract&#39;</span><span class=p>,</span><span class=w> </span><span class=p>{</span>
</span><span id=__span-5-15><a id=__codelineno-5-15 name=__codelineno-5-15 href=#__codelineno-5-15></a><span class=w>            </span><span class=nx>method</span><span class=o>:</span><span class=w> </span><span class=s1>&#39;POST&#39;</span><span class=p>,</span>
</span><span id=__span-5-16><a id=__codelineno-5-16 name=__codelineno-5-16 href=#__codelineno-5-16></a><span class=w>            </span><span class=nx>headers</span><span class=o>:</span><span class=w> </span><span class=p>{</span><span class=s1>&#39;Content-Type&#39;</span><span class=o>:</span><span class=w> </span><span class=s1>&#39;application/json&#39;</span><span class=p>},</span>
</span><span id=__span-5-17><a id=__codelineno-5-17 name=__codelineno-5-17 href=#__codelineno-5-17></a><span class=w>            </span><span class=nx>body</span><span class=o>:</span><span class=w> </span><span class=nb>JSON</span><span class=p>.</span><span class=nx>stringify</span><span class=p>({</span>
</span><span id=__span-5-18><a id=__codelineno-5-18 name=__codelineno-5-18 href=#__codelineno-5-18></a><span class=w>                </span><span class=nx>text</span><span class=o>:</span><span class=w> </span><span class=nx>cleanTitle</span><span class=p>,</span>
</span><span id=__span-5-19><a id=__codelineno-5-19 name=__codelineno-5-19 href=#__codelineno-5-19></a><span class=w>                </span><span class=nx>extract_entities</span><span class=o>:</span><span class=w> </span><span class=kc>true</span><span class=p>,</span>
</span><span id=__span-5-20><a id=__codelineno-5-20 name=__codelineno-5-20 href=#__codelineno-5-20></a><span class=w>                </span><span class=nx>extract_relations</span><span class=o>:</span><span class=w> </span><span class=kc>true</span>
</span><span id=__span-5-21><a id=__codelineno-5-21 name=__codelineno-5-21 href=#__codelineno-5-21></a><span class=w>            </span><span class=p>})</span>
</span><span id=__span-5-22><a id=__codelineno-5-22 name=__codelineno-5-22 href=#__codelineno-5-22></a><span class=w>        </span><span class=p>}).</span><span class=nx>then</span><span class=p>(</span><span class=nx>r</span><span class=w> </span><span class=p>=&gt;</span><span class=w> </span><span class=nx>r</span><span class=p>.</span><span class=nx>json</span><span class=p>());</span>
</span><span id=__span-5-23><a id=__codelineno-5-23 name=__codelineno-5-23 href=#__codelineno-5-23></a>
</span><span id=__span-5-24><a id=__codelineno-5-24 name=__codelineno-5-24 href=#__codelineno-5-24></a><span class=w>        </span><span class=c1>// 3. 创建增强的文档结构</span>
</span><span id=__span-5-25><a id=__codelineno-5-25 name=__codelineno-5-25 href=#__codelineno-5-25></a><span class=w>        </span><span class=kd>const</span><span class=w> </span><span class=nx>enhancedDocument</span><span class=w> </span><span class=o>=</span><span class=w> </span><span class=p>{</span>
</span><span id=__span-5-26><a id=__codelineno-5-26 name=__codelineno-5-26 href=#__codelineno-5-26></a><span class=w>            </span><span class=nx>pageContent</span><span class=o>:</span><span class=w> </span><span class=nx>cleanTitle</span><span class=p>,</span>
</span><span id=__span-5-27><a id=__codelineno-5-27 name=__codelineno-5-27 href=#__codelineno-5-27></a><span class=w>            </span><span class=nx>metadata</span><span class=o>:</span><span class=w> </span><span class=p>{</span>
</span><span id=__span-5-28><a id=__codelineno-5-28 name=__codelineno-5-28 href=#__codelineno-5-28></a><span class=w>                </span><span class=nx>title</span><span class=o>:</span><span class=w> </span><span class=nx>cleanTitle</span><span class=p>,</span>
</span><span id=__span-5-29><a id=__codelineno-5-29 name=__codelineno-5-29 href=#__codelineno-5-29></a><span class=w>                </span><span class=nx>published_date</span><span class=o>:</span><span class=w> </span><span class=nx>publishedDate</span><span class=p>,</span>
</span><span id=__span-5-30><a id=__codelineno-5-30 name=__codelineno-5-30 href=#__codelineno-5-30></a><span class=w>                </span><span class=nx>article_id</span><span class=o>:</span><span class=w> </span><span class=nx>data</span><span class=p>.</span><span class=nx>article_id</span><span class=p>,</span>
</span><span id=__span-5-31><a id=__codelineno-5-31 name=__codelineno-5-31 href=#__codelineno-5-31></a><span class=w>                </span><span class=nx>source</span><span class=o>:</span><span class=w> </span><span class=nx>data</span><span class=p>.</span><span class=nx>source</span><span class=p>,</span>
</span><span id=__span-5-32><a id=__codelineno-5-32 name=__codelineno-5-32 href=#__codelineno-5-32></a><span class=w>                </span><span class=c1>// KAG增强字段</span>
</span><span id=__span-5-33><a id=__codelineno-5-33 name=__codelineno-5-33 href=#__codelineno-5-33></a><span class=w>                </span><span class=nx>entities</span><span class=o>:</span><span class=w> </span><span class=nx>kagResult</span><span class=p>.</span><span class=nx>entities</span><span class=p>,</span>
</span><span id=__span-5-34><a id=__codelineno-5-34 name=__codelineno-5-34 href=#__codelineno-5-34></a><span class=w>                </span><span class=nx>relations</span><span class=o>:</span><span class=w> </span><span class=nx>kagResult</span><span class=p>.</span><span class=nx>relations</span><span class=p>,</span>
</span><span id=__span-5-35><a id=__codelineno-5-35 name=__codelineno-5-35 href=#__codelineno-5-35></a><span class=w>                </span><span class=nx>knowledge_graph_id</span><span class=o>:</span><span class=w> </span><span class=nx>kagResult</span><span class=p>.</span><span class=nx>kg_id</span>
</span><span id=__span-5-36><a id=__codelineno-5-36 name=__codelineno-5-36 href=#__codelineno-5-36></a><span class=w>            </span><span class=p>}</span>
</span><span id=__span-5-37><a id=__codelineno-5-37 name=__codelineno-5-37 href=#__codelineno-5-37></a><span class=w>        </span><span class=p>};</span>
</span><span id=__span-5-38><a id=__codelineno-5-38 name=__codelineno-5-38 href=#__codelineno-5-38></a>
</span><span id=__span-5-39><a id=__codelineno-5-39 name=__codelineno-5-39 href=#__codelineno-5-39></a><span class=w>        </span><span class=nx>processedItems</span><span class=p>.</span><span class=nx>push</span><span class=p>(</span><span class=nx>enhancedDocument</span><span class=p>);</span>
</span><span id=__span-5-40><a id=__codelineno-5-40 name=__codelineno-5-40 href=#__codelineno-5-40></a>
</span><span id=__span-5-41><a id=__codelineno-5-41 name=__codelineno-5-41 href=#__codelineno-5-41></a><span class=w>    </span><span class=p>}</span><span class=w> </span><span class=k>catch</span><span class=w> </span><span class=p>(</span><span class=nx>error</span><span class=p>)</span><span class=w> </span><span class=p>{</span>
</span><span id=__span-5-42><a id=__codelineno-5-42 name=__codelineno-5-42 href=#__codelineno-5-42></a><span class=w>        </span><span class=nx>console</span><span class=p>.</span><span class=nx>log</span><span class=p>(</span><span class=sb>`KAG处理错误，降级到原有方案: </span><span class=si>${</span><span class=nx>error</span><span class=p>.</span><span class=nx>message</span><span class=si>}</span><span class=sb>`</span><span class=p>);</span>
</span><span id=__span-5-43><a id=__codelineno-5-43 name=__codelineno-5-43 href=#__codelineno-5-43></a><span class=w>        </span><span class=c1>// 降级处理</span>
</span><span id=__span-5-44><a id=__codelineno-5-44 name=__codelineno-5-44 href=#__codelineno-5-44></a><span class=w>        </span><span class=nx>processedItems</span><span class=p>.</span><span class=nx>push</span><span class=p>(</span><span class=nx>createBasicDocument</span><span class=p>(</span><span class=nx>item</span><span class=p>.</span><span class=nx>json</span><span class=p>));</span>
</span><span id=__span-5-45><a id=__codelineno-5-45 name=__codelineno-5-45 href=#__codelineno-5-45></a><span class=w>    </span><span class=p>}</span>
</span><span id=__span-5-46><a id=__codelineno-5-46 name=__codelineno-5-46 href=#__codelineno-5-46></a><span class=p>}</span>
</span><span id=__span-5-47><a id=__codelineno-5-47 name=__codelineno-5-47 href=#__codelineno-5-47></a>
</span><span id=__span-5-48><a id=__codelineno-5-48 name=__codelineno-5-48 href=#__codelineno-5-48></a><span class=k>return</span><span class=w> </span><span class=nx>processedItems</span><span class=p>;</span>
</span></code></pre></div> <h2 id=_7>🎯 集成的具体好处<a class=headerlink href=#_7 title="Permanent link">&para;</a></h2> <h3 id=1>1. 保留现有投资<a class=headerlink href=#1 title="Permanent link">&para;</a></h3> <ul> <li>✅ Milvus继续发挥向量检索优势</li> <li>✅ 现有数据和配置不需要大改</li> <li>✅ 渐进式升级，风险可控</li> </ul> <h3 id=2-kag>2. 获得KAG增强<a class=headerlink href=#2-kag title="Permanent link">&para;</a></h3> <ul> <li>✅ 知识图谱推理能力</li> <li>✅ 实体关系理解</li> <li>✅ 多跳推理</li> <li>✅ 更好的中文处理</li> </ul> <h3 id=3>3. 最佳实践架构<a class=headerlink href=#3 title="Permanent link">&para;</a></h3> <div class="language-text highlight"><pre><span></span><code><span id=__span-6-1><a id=__codelineno-6-1 name=__codelineno-6-1 href=#__codelineno-6-1></a>简单查询 → 直接用Milvus（快速）
</span><span id=__span-6-2><a id=__codelineno-6-2 name=__codelineno-6-2 href=#__codelineno-6-2></a>复杂查询 → KAG+Milvus混合（准确）
</span><span id=__span-6-3><a id=__codelineno-6-3 name=__codelineno-6-3 href=#__codelineno-6-3></a>实体查询 → 优先用KAG（精确）
</span><span id=__span-6-4><a id=__codelineno-6-4 name=__codelineno-6-4 href=#__codelineno-6-4></a>语义查询 → 优先用Milvus（高效）
</span></code></pre></div> <h2 id=_8>🚀 实施路径<a class=headerlink href=#_8 title="Permanent link">&para;</a></h2> <h3 id=phase-1>Phase 1: 并行部署<a class=headerlink href=#phase-1 title="Permanent link">&para;</a></h3> <div class="language-text highlight"><pre><span></span><code><span id=__span-7-1><a id=__codelineno-7-1 name=__codelineno-7-1 href=#__codelineno-7-1></a>保持: RSS → n8n → Milvus → AutoGen
</span><span id=__span-7-2><a id=__codelineno-7-2 name=__codelineno-7-2 href=#__codelineno-7-2></a>新增: RSS → n8n → KAG → KnowledgeGraph
</span><span id=__span-7-3><a id=__codelineno-7-3 name=__codelineno-7-3 href=#__codelineno-7-3></a>目标: 对比效果，验证价值
</span></code></pre></div> <h3 id=phase-2>Phase 2: 混合架构<a class=headerlink href=#phase-2 title="Permanent link">&para;</a></h3> <div class="language-text highlight"><pre><span></span><code><span id=__span-8-1><a id=__codelineno-8-1 name=__codelineno-8-1 href=#__codelineno-8-1></a>升级: RSS → n8n → KAG+Milvus → 混合检索 → AutoGen
</span><span id=__span-8-2><a id=__codelineno-8-2 name=__codelineno-8-2 href=#__codelineno-8-2></a>目标: 发挥两者优势，提升效果
</span></code></pre></div> <h3 id=phase-3>Phase 3: 优化整合<a class=headerlink href=#phase-3 title="Permanent link">&para;</a></h3> <div class="language-text highlight"><pre><span></span><code><span id=__span-9-1><a id=__codelineno-9-1 name=__codelineno-9-1 href=#__codelineno-9-1></a>优化: 根据查询类型智能路由到最佳后端
</span><span id=__span-9-2><a id=__codelineno-9-2 name=__codelineno-9-2 href=#__codelineno-9-2></a>目标: 性能和准确性的最佳平衡
</span></code></pre></div> <h2 id=_9>💡 总结<a class=headerlink href=#_9 title="Permanent link">&para;</a></h2> <p><strong>KAG不是要替代Milvus，而是要增强Milvus！</strong></p> <p>这种混合架构的优势： - 🎯 <strong>互补性强</strong> - 图谱推理 + 向量检索 - 🔄 <strong>渐进升级</strong> - 保护现有投资 - ⚖️ <strong>灵活选择</strong> - 根据场景选择最佳方案 - 🚀 <strong>未来扩展</strong> - 为更复杂的AI应用打基础</p> <p>想要我帮你设计具体的KAG+Milvus集成实施方案吗？</p> <aside class=md-source-file> <span class=md-source-file__fact> <span class=md-icon title=最后更新> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M21 13.1c-.1 0-.3.1-.4.2l-1 1 2.1 2.1 1-1c.2-.2.2-.6 0-.8l-1.3-1.3c-.1-.1-.2-.2-.4-.2m-1.9 1.8-6.1 6V23h2.1l6.1-6.1zM12.5 7v5.2l4 2.4-1 1L11 13V7zM11 21.9c-5.1-.5-9-4.8-9-9.9C2 6.5 6.5 2 12 2c5.3 0 9.6 4.1 10 9.3-.3-.1-.6-.2-1-.2s-.7.1-1 .2C19.6 7.2 16.2 4 12 4c-4.4 0-8 3.6-8 8 0 4.1 3.1 7.5 7.1 7.9l-.1.2z"/></svg> </span> <span class="git-revision-date-localized-plugin git-revision-date-localized-plugin-datetime" title="2025年7月10日 04:18:21 UTC">2025年7月10日 04:18:21</span> </span> <span class=md-source-file__fact> <span class=md-icon title=创建日期> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M14.47 15.08 11 13V7h1.5v5.25l3.08 1.83c-.41.28-.79.62-1.11 1m-1.39 4.84c-.36.05-.71.08-1.08.08-4.42 0-8-3.58-8-8s3.58-8 8-8 8 3.58 8 8c0 .37-.03.72-.08 1.08.69.1 1.33.32 1.92.64.1-.56.16-1.13.16-1.72 0-5.5-4.5-10-10-10S2 6.5 2 12s4.47 10 10 10c.59 0 1.16-.06 1.72-.16-.32-.59-.54-1.23-.64-1.92M18 15v3h-3v2h3v3h2v-3h3v-2h-3v-3z"/></svg> </span> <span class="git-revision-date-localized-plugin git-revision-date-localized-plugin-datetime" title="2025年7月10日 04:18:21 UTC">2025年7月10日 04:18:21</span> </span> </aside> <form class=md-feedback name=feedback hidden> <fieldset> <legend class=md-feedback__title> 这个页面有帮助吗？ </legend> <div class=md-feedback__inner> <div class=md-feedback__list> <button class="md-feedback__icon md-icon" type=submit title=有帮助 data-md-value=1> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M20 12a8 8 0 0 0-8-8 8 8 0 0 0-8 8 8 8 0 0 0 8 8 8 8 0 0 0 8-8m2 0a10 10 0 0 1-10 10A10 10 0 0 1 2 12 10 10 0 0 1 12 2a10 10 0 0 1 10 10M10 9.5c0 .8-.7 1.5-1.5 1.5S7 10.3 7 9.5 7.7 8 8.5 8s1.5.7 1.5 1.5m7 0c0 .8-.7 1.5-1.5 1.5S14 10.3 14 9.5 14.7 8 15.5 8s1.5.7 1.5 1.5m-5 7.73c-1.75 0-3.29-.73-4.19-1.81L9.23 14c.45.72 1.52 1.23 2.77 1.23s2.32-.51 2.77-1.23l1.42 1.42c-.9 1.08-2.44 1.81-4.19 1.81"/></svg> </button> <button class="md-feedback__icon md-icon" type=submit title=需要改进 data-md-value=0> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M20 12a8 8 0 0 0-8-8 8 8 0 0 0-8 8 8 8 0 0 0 8 8 8 8 0 0 0 8-8m2 0a10 10 0 0 1-10 10A10 10 0 0 1 2 12 10 10 0 0 1 12 2a10 10 0 0 1 10 10m-6.5-4c.8 0 1.5.7 1.5 1.5s-.7 1.5-1.5 1.5-1.5-.7-1.5-1.5.7-1.5 1.5-1.5M10 9.5c0 .8-.7 1.5-1.5 1.5S7 10.3 7 9.5 7.7 8 8.5 8s1.5.7 1.5 1.5m2 4.5c1.75 0 3.29.72 4.19 1.81l-1.42 1.42C14.32 16.5 13.25 16 12 16s-2.32.5-2.77 1.23l-1.42-1.42C8.71 14.72 10.25 14 12 14"/></svg> </button> </div> <div class=md-feedback__note> <div data-md-value=1 hidden> 感谢您的反馈！ </div> <div data-md-value=0 hidden> 感谢您的反馈！请通过GitHub Issues告诉我们如何改进。 </div> </div> </div> </fieldset> </form> </article> </div> <script>var tabs=__md_get("__tabs");if(Array.isArray(tabs))e:for(var set of document.querySelectorAll(".tabbed-set")){var labels=set.querySelector(".tabbed-labels");for(var tab of tabs)for(var label of labels.getElementsByTagName("label"))if(label.innerText.trim()===tab){var input=document.getElementById(label.htmlFor);input.checked=!0;continue e}}</script> <script>var target=document.getElementById(location.hash.slice(1));target&&target.name&&(target.checked=target.name.startsWith("__tabbed_"))</script> </div> <button type=button class="md-top md-icon" data-md-component=top hidden> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M13 20h-2V8l-5.5 5.5-1.42-1.42L12 4.16l7.92 7.92-1.42 1.42L13 8z"/></svg> 回到页面顶部 </button> </main> <footer class=md-footer> <div class="md-footer-meta md-typeset"> <div class="md-footer-meta__inner md-grid"> <div class=md-copyright> <div class=md-copyright__highlight> Copyright &copy; 2025 炼妖壶项目 - MIT License </div> Made with <a href=https://squidfunk.github.io/mkdocs-material/ target=_blank rel=noopener> Material for MkDocs </a> </div> <div class=md-social> <a href=https://github.com/your-username/jixia-academy target=_blank rel=noopener title=GitHub仓库 class=md-social__link> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 496 512"><!-- Font Awesome Free 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license/free (Icons: CC BY 4.0, Fonts: SIL OFL 1.1, Code: MIT License) Copyright 2024 Fonticons, Inc.--><path d="M165.9 397.4c0 2-2.3 3.6-5.2 3.6-3.3.3-5.6-1.3-5.6-3.6 0-2 2.3-3.6 5.2-3.6 3-.3 5.6 1.3 5.6 3.6m-31.1-4.5c-.7 2 1.3 4.3 4.3 4.9 2.6 1 5.6 0 6.2-2s-1.3-4.3-4.3-5.2c-2.6-.7-5.5.3-6.2 2.3m44.2-1.7c-2.9.7-4.9 2.6-4.6 4.9.3 2 2.9 3.3 5.9 2.6 2.9-.7 4.9-2.6 4.6-4.6-.3-1.9-3-3.2-5.9-2.9M244.8 8C106.1 8 0 113.3 0 252c0 110.9 69.8 205.8 169.5 239.2 12.8 2.3 17.3-5.6 17.3-12.1 0-6.2-.3-40.4-.3-61.4 0 0-70 15-84.7-29.8 0 0-11.4-29.1-27.8-36.6 0 0-22.9-15.7 1.6-15.4 0 0 24.9 2 38.6 25.8 21.9 38.6 58.6 27.5 72.9 20.9 2.3-16 8.8-27.1 16-33.7-55.9-6.2-112.3-14.3-112.3-110.5 0-27.5 7.6-41.3 23.6-58.9-2.6-6.5-11.1-33.3 2.6-67.9 20.9-6.5 69 27 69 27 20-5.6 41.5-8.5 62.8-8.5s42.8 2.9 62.8 8.5c0 0 48.1-33.6 69-27 13.7 34.7 5.2 61.4 2.6 67.9 16 17.7 25.8 31.5 25.8 58.9 0 96.5-58.9 104.2-114.8 110.5 9.2 7.9 17 22.9 17 46.4 0 33.7-.3 75.4-.3 83.6 0 6.5 4.6 14.4 17.3 12.1C428.2 457.8 496 362.9 496 252 496 113.3 383.5 8 244.8 8M97.2 352.9c-1.3 1-1 3.3.7 5.2 1.6 1.6 3.9 2.3 5.2 1 1.3-1 1-3.3-.7-5.2-1.6-1.6-3.9-2.3-5.2-1m-10.8-8.1c-.7 1.3.3 2.9 2.3 3.9 1.6 1 3.6.7 4.3-.7.7-1.3-.3-2.9-2.3-3.9-2-.6-3.6-.3-4.3.7m32.4 35.6c-1.6 1.3-1 4.3 1.3 6.2 2.3 2.3 5.2 2.6 6.5 1 1.3-1.3.7-4.3-1.3-6.2-2.2-2.3-5.2-2.6-6.5-1m-11.4-14.7c-1.6 1-1.6 3.6 0 5.9s4.3 3.3 5.6 2.3c1.6-1.3 1.6-3.9 0-6.2-1.4-2.3-4-3.3-5.6-2"/></svg> </a> <a href=https://your-mastodon-instance.com/@taishang_laojun target=_blank rel="noopener me" title=太上老君 class=md-social__link> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 448 512"><!-- Font Awesome Free 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license/free (Icons: CC BY 4.0, Fonts: SIL OFL 1.1, Code: MIT License) Copyright 2024 Fonticons, Inc.--><path d="M433 179.11c0-97.2-63.71-125.7-63.71-125.7-62.52-28.7-228.56-28.4-290.48 0 0 0-63.72 28.5-63.72 125.7 0 115.7-6.6 259.4 105.63 289.1 40.51 10.7 75.32 13 103.33 11.4 50.81-2.8 79.32-18.1 79.32-18.1l-1.7-36.9s-36.31 11.4-77.12 10.1c-40.41-1.4-83-4.4-89.63-54a102.5 102.5 0 0 1-.9-13.9c85.63 20.9 158.65 9.1 178.75 6.7 56.12-6.7 105-41.3 111.23-72.9 9.8-49.8 9-121.5 9-121.5m-75.12 125.2h-46.63v-114.2c0-49.7-64-51.6-64 6.9v62.5h-46.33V197c0-58.5-64-56.6-64-6.9v114.2H90.19c0-122.1-5.2-147.9 18.41-175 25.9-28.9 79.82-30.8 103.83 6.1l11.6 19.5 11.6-19.5c24.11-37.1 78.12-34.8 103.83-6.1 23.71 27.3 18.4 53 18.4 175z"/></svg> </a> <a href=https://twitter.com/jixia_academy target=_blank rel=noopener title=Twitter class=md-social__link> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 512 512"><!-- Font Awesome Free 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license/free (Icons: CC BY 4.0, Fonts: SIL OFL 1.1, Code: MIT License) Copyright 2024 Fonticons, Inc.--><path d="M459.37 151.716c.325 4.548.325 9.097.325 13.645 0 138.72-105.583 298.558-298.558 298.558-59.452 0-114.68-17.219-161.137-47.106 8.447.974 16.568 1.299 25.34 1.299 49.055 0 94.213-16.568 130.274-44.832-46.132-.975-84.792-31.188-98.112-72.772 6.498.974 12.995 1.624 19.818 1.624 9.421 0 18.843-1.3 27.614-3.573-48.081-9.747-84.143-51.98-84.143-102.985v-1.299c13.969 7.797 30.214 12.67 47.431 13.319-28.264-18.843-46.781-51.005-46.781-87.391 0-19.492 5.197-37.36 14.294-52.954 51.655 63.675 129.3 105.258 216.365 109.807-1.624-7.797-2.599-15.918-2.599-24.04 0-57.828 46.782-104.934 104.934-104.934 30.213 0 57.502 12.67 76.67 33.137 23.715-4.548 46.456-13.32 66.599-25.34-7.798 24.366-24.366 44.833-46.132 57.827 21.117-2.273 41.584-8.122 60.426-16.243-14.292 20.791-32.161 39.308-52.628 54.253"/></svg> </a> <a href=mailto:<EMAIL> target=_blank rel=noopener title=联系我们 class=md-social__link> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 512 512"><!-- Font Awesome Free 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license/free (Icons: CC BY 4.0, Fonts: SIL OFL 1.1, Code: MIT License) Copyright 2024 Fonticons, Inc.--><path d="M498.1 5.6c10.1 7 15.4 19.1 13.5 31.2l-64 416c-1.5 9.7-7.4 18.2-16 23s-18.9 5.4-28 1.6L284 427.7l-68.5 74.1c-8.9 9.7-22.9 12.9-35.2 8.1S160 493.2 160 480v-83.6c0-4 1.5-7.8 4.2-10.8l167.6-182.8c5.8-6.3 5.6-16-.4-22s-15.7-6.4-22-.7L106 360.8l-88.3-44.2C7.1 311.3.3 300.7 0 288.9s5.9-22.8 16.1-28.7l448-256c10.7-6.1 23.9-5.5 34 1.4"/></svg> </a> </div> </div> </div> </footer> </div> <div class=md-dialog data-md-component=dialog> <div class="md-dialog__inner md-typeset"></div> </div> <script id=__config type=application/json>{"base": "../..", "features": ["navigation.instant", "navigation.tracking", "navigation.tabs", "navigation.tabs.sticky", "navigation.sections", "navigation.expand", "navigation.indexes", "navigation.top", "search.highlight", "search.share", "search.suggest", "content.code.annotate", "content.code.copy", "content.tabs.link"], "search": "../../assets/javascripts/workers/search.d50fe291.min.js", "tags": null, "translations": {"clipboard.copied": "\u5df2\u590d\u5236", "clipboard.copy": "\u590d\u5236", "search.result.more.one": "\u5728\u8be5\u9875\u4e0a\u8fd8\u6709 1 \u4e2a\u7b26\u5408\u6761\u4ef6\u7684\u7ed3\u679c", "search.result.more.other": "\u5728\u8be5\u9875\u4e0a\u8fd8\u6709 # \u4e2a\u7b26\u5408\u6761\u4ef6\u7684\u7ed3\u679c", "search.result.none": "\u6ca1\u6709\u627e\u5230\u7b26\u5408\u6761\u4ef6\u7684\u7ed3\u679c", "search.result.one": "\u627e\u5230 1 \u4e2a\u7b26\u5408\u6761\u4ef6\u7684\u7ed3\u679c", "search.result.other": "# \u4e2a\u7b26\u5408\u6761\u4ef6\u7684\u7ed3\u679c", "search.result.placeholder": "\u952e\u5165\u4ee5\u5f00\u59cb\u641c\u7d22", "search.result.term.missing": "\u7f3a\u5c11", "select.version": "\u9009\u62e9\u5f53\u524d\u7248\u672c"}, "version": {"default": "latest", "provider": "mike"}}</script> <script src=../../assets/javascripts/bundle.56ea9cef.min.js></script> <script src=../../assets/javascripts/mathjax.js></script> <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script> <script src=https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js></script> </body> </html>