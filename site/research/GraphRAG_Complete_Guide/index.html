<!doctype html><html lang=zh class=no-js> <head><meta charset=utf-8><meta name=viewport content="width=device-width,initial-scale=1"><meta name=description content="AI驱动的投资决策系统 - 稷下学宫智能体辩论平台"><meta name=author content=炼妖壶开发团队><link href=https://jingminzhang.github.io/cauldron/research/GraphRAG_Complete_Guide/ rel=canonical><link rel=icon href=../../assets/images/favicon.png><meta name=generator content="mkdocs-1.6.1, mkdocs-material-9.6.15"><title>GraphRAG 完整指南 - 炼妖壶 (Cauldron)</title><link rel=stylesheet href=../../assets/stylesheets/main.342714a4.min.css><link rel=stylesheet href=../../assets/stylesheets/palette.06af60db.min.css><link rel=preconnect href=https://fonts.gstatic.com crossorigin><link rel=stylesheet href="https://fonts.googleapis.com/css?family=Noto+Sans+SC:300,300i,400,400i,700,700i%7CJetBrains+Mono:400,400i,700,700i&display=fallback"><style>:root{--md-text-font:"Noto Sans SC";--md-code-font:"JetBrains Mono"}</style><link rel=stylesheet href=../../assets/stylesheets/extra.css><script>__md_scope=new URL("../..",location),__md_hash=e=>[...e].reduce(((e,_)=>(e<<5)-e+_.charCodeAt(0)),0),__md_get=(e,_=localStorage,t=__md_scope)=>JSON.parse(_.getItem(t.pathname+"."+e)),__md_set=(e,_,t=localStorage,a=__md_scope)=>{try{t.setItem(a.pathname+"."+e,JSON.stringify(_))}catch(e){}}</script><script id=__analytics>function __md_analytics(){function e(){dataLayer.push(arguments)}window.dataLayer=window.dataLayer||[],e("js",new Date),e("config","G-XXXXXXXXXX"),document.addEventListener("DOMContentLoaded",(function(){document.forms.search&&document.forms.search.query.addEventListener("blur",(function(){this.value&&e("event","search",{search_term:this.value})}));document$.subscribe((function(){var t=document.forms.feedback;if(void 0!==t)for(var a of t.querySelectorAll("[type=submit]"))a.addEventListener("click",(function(a){a.preventDefault();var n=document.location.pathname,d=this.getAttribute("data-md-value");e("event","feedback",{page:n,data:d}),t.firstElementChild.disabled=!0;var r=t.querySelector(".md-feedback__note [data-md-value='"+d+"']");r&&(r.hidden=!1)})),t.hidden=!1})),location$.subscribe((function(t){e("config","G-XXXXXXXXXX",{page_path:t.pathname})}))}));var t=document.createElement("script");t.async=!0,t.src="https://www.googletagmanager.com/gtag/js?id=G-XXXXXXXXXX",document.getElementById("__analytics").insertAdjacentElement("afterEnd",t)}</script><script>"undefined"!=typeof __md_analytics&&__md_analytics()</script></head> <body dir=ltr data-md-color-scheme=default data-md-color-primary=indigo data-md-color-accent=amber> <input class=md-toggle data-md-toggle=drawer type=checkbox id=__drawer autocomplete=off> <input class=md-toggle data-md-toggle=search type=checkbox id=__search autocomplete=off> <label class=md-overlay for=__drawer></label> <div data-md-component=skip> <a href=#graphrag class=md-skip> 跳转至 </a> </div> <div data-md-component=announce> </div> <div data-md-color-scheme=default data-md-component=outdated hidden> </div> <header class="md-header md-header--shadow md-header--lifted" data-md-component=header> <nav class="md-header__inner md-grid" aria-label=页眉> <a href=../.. title="炼妖壶 (Cauldron)" class="md-header__button md-logo" aria-label="炼妖壶 (Cauldron)" data-md-component=logo> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M12 3 1 9l11 6 9-4.91V17h2V9M5 13.18v4L12 21l7-3.82v-4L12 17z"/></svg> </a> <label class="md-header__button md-icon" for=__drawer> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M3 6h18v2H3zm0 5h18v2H3zm0 5h18v2H3z"/></svg> </label> <div class=md-header__title data-md-component=header-title> <div class=md-header__ellipsis> <div class=md-header__topic> <span class=md-ellipsis> 炼妖壶 (Cauldron) </span> </div> <div class=md-header__topic data-md-component=header-topic> <span class=md-ellipsis> GraphRAG 完整指南 </span> </div> </div> </div> <form class=md-header__option data-md-component=palette> <input class=md-option data-md-color-media="(prefers-color-scheme: light)" data-md-color-scheme=default data-md-color-primary=indigo data-md-color-accent=amber aria-label=切换到暗黑模式 type=radio name=__palette id=__palette_0> <label class="md-header__button md-icon" title=切换到暗黑模式 for=__palette_1 hidden> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M12 8a4 4 0 0 0-4 4 4 4 0 0 0 4 4 4 4 0 0 0 4-4 4 4 0 0 0-4-4m0 10a6 6 0 0 1-6-6 6 6 0 0 1 6-6 6 6 0 0 1 6 6 6 6 0 0 1-6 6m8-9.31V4h-4.69L12 .69 8.69 4H4v4.69L.69 12 4 15.31V20h4.69L12 23.31 15.31 20H20v-4.69L23.31 12z"/></svg> </label> <input class=md-option data-md-color-media="(prefers-color-scheme: dark)" data-md-color-scheme=slate data-md-color-primary=indigo data-md-color-accent=amber aria-label=切换到明亮模式 type=radio name=__palette id=__palette_1> <label class="md-header__button md-icon" title=切换到明亮模式 for=__palette_0 hidden> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M12 18c-.89 0-1.74-.2-2.5-.55C11.56 16.5 13 14.42 13 12s-1.44-4.5-3.5-5.45C10.26 6.2 11.11 6 12 6a6 6 0 0 1 6 6 6 6 0 0 1-6 6m8-9.31V4h-4.69L12 .69 8.69 4H4v4.69L.69 12 4 15.31V20h4.69L12 23.31 15.31 20H20v-4.69L23.31 12z"/></svg> </label> </form> <script>var palette=__md_get("__palette");if(palette&&palette.color){if("(prefers-color-scheme)"===palette.color.media){var media=matchMedia("(prefers-color-scheme: light)"),input=document.querySelector(media.matches?"[data-md-color-media='(prefers-color-scheme: light)']":"[data-md-color-media='(prefers-color-scheme: dark)']");palette.color.media=input.getAttribute("data-md-color-media"),palette.color.scheme=input.getAttribute("data-md-color-scheme"),palette.color.primary=input.getAttribute("data-md-color-primary"),palette.color.accent=input.getAttribute("data-md-color-accent")}for(var[key,value]of Object.entries(palette.color))document.body.setAttribute("data-md-color-"+key,value)}</script> <label class="md-header__button md-icon" for=__search> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M9.5 3A6.5 6.5 0 0 1 16 9.5c0 1.61-.59 3.09-1.56 4.23l.27.27h.79l5 5-1.5 1.5-5-5v-.79l-.27-.27A6.52 6.52 0 0 1 9.5 16 6.5 6.5 0 0 1 3 9.5 6.5 6.5 0 0 1 9.5 3m0 2C7 5 5 7 5 9.5S7 14 9.5 14 14 12 14 9.5 12 5 9.5 5"/></svg> </label> <div class=md-search data-md-component=search role=dialog> <label class=md-search__overlay for=__search></label> <div class=md-search__inner role=search> <form class=md-search__form name=search> <input type=text class=md-search__input name=query aria-label=搜索 placeholder=搜索 autocapitalize=off autocorrect=off autocomplete=off spellcheck=false data-md-component=search-query required> <label class="md-search__icon md-icon" for=__search> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M9.5 3A6.5 6.5 0 0 1 16 9.5c0 1.61-.59 3.09-1.56 4.23l.27.27h.79l5 5-1.5 1.5-5-5v-.79l-.27-.27A6.52 6.52 0 0 1 9.5 16 6.5 6.5 0 0 1 3 9.5 6.5 6.5 0 0 1 9.5 3m0 2C7 5 5 7 5 9.5S7 14 9.5 14 14 12 14 9.5 12 5 9.5 5"/></svg> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M20 11v2H8l5.5 5.5-1.42 1.42L4.16 12l7.92-7.92L13.5 5.5 8 11z"/></svg> </label> <nav class=md-search__options aria-label=查找> <a href=javascript:void(0) class="md-search__icon md-icon" title=分享 aria-label=分享 data-clipboard data-clipboard-text data-md-component=search-share tabindex=-1> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M18 16.08c-.76 0-1.44.3-1.96.77L8.91 12.7c.05-.23.09-.46.09-.7s-.04-.47-.09-.7l7.05-4.11c.54.5 1.25.81 2.04.81a3 3 0 0 0 3-3 3 3 0 0 0-3-3 3 3 0 0 0-3 3c0 .***********.7L8.04 9.81C7.5 9.31 6.79 9 6 9a3 3 0 0 0-3 3 3 3 0 0 0 3 3c.79 0 1.5-.31 2.04-.81l7.12 4.15c-.05.21-.08.43-.08.66 0 1.61 1.31 2.91 2.92 2.91s2.92-1.3 2.92-2.91A2.92 2.92 0 0 0 18 16.08"/></svg> </a> <button type=reset class="md-search__icon md-icon" title=清空当前内容 aria-label=清空当前内容 tabindex=-1> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M19 6.41 17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/></svg> </button> </nav> <div class=md-search__suggest data-md-component=search-suggest></div> </form> <div class=md-search__output> <div class=md-search__scrollwrap tabindex=0 data-md-scrollfix> <div class=md-search-result data-md-component=search-result> <div class=md-search-result__meta> 正在初始化搜索引擎 </div> <ol class=md-search-result__list role=presentation></ol> </div> </div> </div> </div> </div> <div class=md-header__source> <a href=https://github.com/jingminzhang/cauldron title=前往仓库 class=md-source data-md-component=source> <div class="md-source__icon md-icon"> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 496 512"><!-- Font Awesome Free 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license/free (Icons: CC BY 4.0, Fonts: SIL OFL 1.1, Code: MIT License) Copyright 2024 Fonticons, Inc.--><path d="M165.9 397.4c0 2-2.3 3.6-5.2 3.6-3.3.3-5.6-1.3-5.6-3.6 0-2 2.3-3.6 5.2-3.6 3-.3 5.6 1.3 5.6 3.6m-31.1-4.5c-.7 2 1.3 4.3 4.3 4.9 2.6 1 5.6 0 6.2-2s-1.3-4.3-4.3-5.2c-2.6-.7-5.5.3-6.2 2.3m44.2-1.7c-2.9.7-4.9 2.6-4.6 4.9.3 2 2.9 3.3 5.9 2.6 2.9-.7 4.9-2.6 4.6-4.6-.3-1.9-3-3.2-5.9-2.9M244.8 8C106.1 8 0 113.3 0 252c0 110.9 69.8 205.8 169.5 239.2 12.8 2.3 17.3-5.6 17.3-12.1 0-6.2-.3-40.4-.3-61.4 0 0-70 15-84.7-29.8 0 0-11.4-29.1-27.8-36.6 0 0-22.9-15.7 1.6-15.4 0 0 24.9 2 38.6 25.8 21.9 38.6 58.6 27.5 72.9 20.9 2.3-16 8.8-27.1 16-33.7-55.9-6.2-112.3-14.3-112.3-110.5 0-27.5 7.6-41.3 23.6-58.9-2.6-6.5-11.1-33.3 2.6-67.9 20.9-6.5 69 27 69 27 20-5.6 41.5-8.5 62.8-8.5s42.8 2.9 62.8 8.5c0 0 48.1-33.6 69-27 13.7 34.7 5.2 61.4 2.6 67.9 16 17.7 25.8 31.5 25.8 58.9 0 96.5-58.9 104.2-114.8 110.5 9.2 7.9 17 22.9 17 46.4 0 33.7-.3 75.4-.3 83.6 0 6.5 4.6 14.4 17.3 12.1C428.2 457.8 496 362.9 496 252 496 113.3 383.5 8 244.8 8M97.2 352.9c-1.3 1-1 3.3.7 5.2 1.6 1.6 3.9 2.3 5.2 1 1.3-1 1-3.3-.7-5.2-1.6-1.6-3.9-2.3-5.2-1m-10.8-8.1c-.7 1.3.3 2.9 2.3 3.9 1.6 1 3.6.7 4.3-.7.7-1.3-.3-2.9-2.3-3.9-2-.6-3.6-.3-4.3.7m32.4 35.6c-1.6 1.3-1 4.3 1.3 6.2 2.3 2.3 5.2 2.6 6.5 1 1.3-1.3.7-4.3-1.3-6.2-2.2-2.3-5.2-2.6-6.5-1m-11.4-14.7c-1.6 1-1.6 3.6 0 5.9s4.3 3.3 5.6 2.3c1.6-1.3 1.6-3.9 0-6.2-1.4-2.3-4-3.3-5.6-2"/></svg> </div> <div class=md-source__repository> jingminzhang/cauldron </div> </a> </div> </nav> <nav class=md-tabs aria-label=标签 data-md-component=tabs> <div class=md-grid> <ul class=md-tabs__list> <li class=md-tabs__item> <a href=../.. class=md-tabs__link> 🏠 首页 </a> </li> <li class=md-tabs__item> <a href=../../getting-started/quick-start/ class=md-tabs__link> 🚀 快速开始 </a> </li> <li class=md-tabs__item> <a href=../../reference/ai_personalities/ class=md-tabs__link> 🎭 AI仙人 </a> </li> <li class=md-tabs__item> <a href=../../architecture/ARCHITECTURE_NOTES/ class=md-tabs__link> 🏗️ 架构设计 </a> </li> <li class=md-tabs__item> <a href=../../how-to-guides/features/ class=md-tabs__link> ⚡ 核心功能 </a> </li> <li class=md-tabs__item> <a href=../../reference/database_overview/ class=md-tabs__link> 🔧 技术文档 </a> </li> <li class=md-tabs__item> <a href=../../vision/three-tiers/ class=md-tabs__link> 📜 项目愿景 </a> </li> <li class=md-tabs__item> <a href=../../how-to-guides/deployment/ class=md-tabs__link> 🚀 部署指南 </a> </li> <li class=md-tabs__item> <a href=../../api/ class=md-tabs__link> 📡 API文档 </a> </li> <li class=md-tabs__item> <a href=../../CONTRIBUTING/ class=md-tabs__link> 🤝 贡献指南 </a> </li> </ul> </div> </nav> </header> <div class=md-container data-md-component=container> <main class=md-main data-md-component=main> <div class="md-main__inner md-grid"> <div class="md-sidebar md-sidebar--primary" data-md-component=sidebar data-md-type=navigation> <div class=md-sidebar__scrollwrap> <div class=md-sidebar__inner> <nav class="md-nav md-nav--primary md-nav--lifted" aria-label=导航栏 data-md-level=0> <label class=md-nav__title for=__drawer> <a href=../.. title="炼妖壶 (Cauldron)" class="md-nav__button md-logo" aria-label="炼妖壶 (Cauldron)" data-md-component=logo> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M12 3 1 9l11 6 9-4.91V17h2V9M5 13.18v4L12 21l7-3.82v-4L12 17z"/></svg> </a> 炼妖壶 (Cauldron) </label> <div class=md-nav__source> <a href=https://github.com/jingminzhang/cauldron title=前往仓库 class=md-source data-md-component=source> <div class="md-source__icon md-icon"> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 496 512"><!-- Font Awesome Free 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license/free (Icons: CC BY 4.0, Fonts: SIL OFL 1.1, Code: MIT License) Copyright 2024 Fonticons, Inc.--><path d="M165.9 397.4c0 2-2.3 3.6-5.2 3.6-3.3.3-5.6-1.3-5.6-3.6 0-2 2.3-3.6 5.2-3.6 3-.3 5.6 1.3 5.6 3.6m-31.1-4.5c-.7 2 1.3 4.3 4.3 4.9 2.6 1 5.6 0 6.2-2s-1.3-4.3-4.3-5.2c-2.6-.7-5.5.3-6.2 2.3m44.2-1.7c-2.9.7-4.9 2.6-4.6 4.9.3 2 2.9 3.3 5.9 2.6 2.9-.7 4.9-2.6 4.6-4.6-.3-1.9-3-3.2-5.9-2.9M244.8 8C106.1 8 0 113.3 0 252c0 110.9 69.8 205.8 169.5 239.2 12.8 2.3 17.3-5.6 17.3-12.1 0-6.2-.3-40.4-.3-61.4 0 0-70 15-84.7-29.8 0 0-11.4-29.1-27.8-36.6 0 0-22.9-15.7 1.6-15.4 0 0 24.9 2 38.6 25.8 21.9 38.6 58.6 27.5 72.9 20.9 2.3-16 8.8-27.1 16-33.7-55.9-6.2-112.3-14.3-112.3-110.5 0-27.5 7.6-41.3 23.6-58.9-2.6-6.5-11.1-33.3 2.6-67.9 20.9-6.5 69 27 69 27 20-5.6 41.5-8.5 62.8-8.5s42.8 2.9 62.8 8.5c0 0 48.1-33.6 69-27 13.7 34.7 5.2 61.4 2.6 67.9 16 17.7 25.8 31.5 25.8 58.9 0 96.5-58.9 104.2-114.8 110.5 9.2 7.9 17 22.9 17 46.4 0 33.7-.3 75.4-.3 83.6 0 6.5 4.6 14.4 17.3 12.1C428.2 457.8 496 362.9 496 252 496 113.3 383.5 8 244.8 8M97.2 352.9c-1.3 1-1 3.3.7 5.2 1.6 1.6 3.9 2.3 5.2 1 1.3-1 1-3.3-.7-5.2-1.6-1.6-3.9-2.3-5.2-1m-10.8-8.1c-.7 1.3.3 2.9 2.3 3.9 1.6 1 3.6.7 4.3-.7.7-1.3-.3-2.9-2.3-3.9-2-.6-3.6-.3-4.3.7m32.4 35.6c-1.6 1.3-1 4.3 1.3 6.2 2.3 2.3 5.2 2.6 6.5 1 1.3-1.3.7-4.3-1.3-6.2-2.2-2.3-5.2-2.6-6.5-1m-11.4-14.7c-1.6 1-1.6 3.6 0 5.9s4.3 3.3 5.6 2.3c1.6-1.3 1.6-3.9 0-6.2-1.4-2.3-4-3.3-5.6-2"/></svg> </div> <div class=md-source__repository> jingminzhang/cauldron </div> </a> </div> <ul class=md-nav__list data-md-scrollfix> <li class="md-nav__item md-nav__item--nested"> <input class="md-nav__toggle md-toggle md-toggle--indeterminate" type=checkbox id=__nav_1> <div class="md-nav__link md-nav__container"> <a href=../.. class="md-nav__link "> <span class=md-ellipsis> 🏠 首页 </span> </a> <label class="md-nav__link " for=__nav_1 id=__nav_1_label tabindex=0> <span class="md-nav__icon md-icon"></span> </label> </div> <nav class=md-nav data-md-level=1 aria-labelledby=__nav_1_label aria-expanded=false> <label class=md-nav__title for=__nav_1> <span class="md-nav__icon md-icon"></span> 🏠 首页 </label> <ul class=md-nav__list data-md-scrollfix> <li class=md-nav__item> <a href=../../roadmap/ class=md-nav__link> <span class=md-ellipsis> 发展路线图 </span> </a> </li> </ul> </nav> </li> <li class="md-nav__item md-nav__item--nested"> <input class="md-nav__toggle md-toggle md-toggle--indeterminate" type=checkbox id=__nav_2> <label class=md-nav__link for=__nav_2 id=__nav_2_label tabindex=0> <span class=md-ellipsis> 🚀 快速开始 </span> <span class="md-nav__icon md-icon"></span> </label> <nav class=md-nav data-md-level=1 aria-labelledby=__nav_2_label aria-expanded=false> <label class=md-nav__title for=__nav_2> <span class="md-nav__icon md-icon"></span> 🚀 快速开始 </label> <ul class=md-nav__list data-md-scrollfix> <li class=md-nav__item> <a href=../../getting-started/quick-start/ class=md-nav__link> <span class=md-ellipsis> 快速开始 </span> </a> </li> <li class=md-nav__item> <a href=../../tutorials/getting_started/ class=md-nav__link> <span class=md-ellipsis> 入门教程 </span> </a> </li> <li class=md-nav__item> <a href=../../how-to-guides/getting_started/ class=md-nav__link> <span class=md-ellipsis> 使用指南 </span> </a> </li> </ul> </nav> </li> <li class="md-nav__item md-nav__item--nested"> <input class="md-nav__toggle md-toggle md-toggle--indeterminate" type=checkbox id=__nav_3> <label class=md-nav__link for=__nav_3 id=__nav_3_label tabindex=0> <span class=md-ellipsis> 🎭 AI仙人 </span> <span class="md-nav__icon md-icon"></span> </label> <nav class=md-nav data-md-level=1 aria-labelledby=__nav_3_label aria-expanded=false> <label class=md-nav__title for=__nav_3> <span class="md-nav__icon md-icon"></span> 🎭 AI仙人 </label> <ul class=md-nav__list data-md-scrollfix> <li class=md-nav__item> <a href=../../reference/ai_personalities/ class=md-nav__link> <span class=md-ellipsis> AI人格设计 </span> </a> </li> </ul> </nav> </li> <li class="md-nav__item md-nav__item--nested"> <input class="md-nav__toggle md-toggle md-toggle--indeterminate" type=checkbox id=__nav_4> <label class=md-nav__link for=__nav_4 id=__nav_4_label tabindex=0> <span class=md-ellipsis> 🏗️ 架构设计 </span> <span class="md-nav__icon md-icon"></span> </label> <nav class=md-nav data-md-level=1 aria-labelledby=__nav_4_label aria-expanded=false> <label class=md-nav__title for=__nav_4> <span class="md-nav__icon md-icon"></span> 🏗️ 架构设计 </label> <ul class=md-nav__list data-md-scrollfix> <li class=md-nav__item> <a href=../../architecture/ARCHITECTURE_NOTES/ class=md-nav__link> <span class=md-ellipsis> 架构笔记 </span> </a> </li> <li class=md-nav__item> <a href=../../architecture/Omnipresent_AI_Agent_Architecture/ class=md-nav__link> <span class=md-ellipsis> 全能AI智能体架构 </span> </a> </li> </ul> </nav> </li> <li class="md-nav__item md-nav__item--nested"> <input class="md-nav__toggle md-toggle md-toggle--indeterminate" type=checkbox id=__nav_5> <label class=md-nav__link for=__nav_5 id=__nav_5_label tabindex=0> <span class=md-ellipsis> ⚡ 核心功能 </span> <span class="md-nav__icon md-icon"></span> </label> <nav class=md-nav data-md-level=1 aria-labelledby=__nav_5_label aria-expanded=false> <label class=md-nav__title for=__nav_5> <span class="md-nav__icon md-icon"></span> ⚡ 核心功能 </label> <ul class=md-nav__list data-md-scrollfix> <li class=md-nav__item> <a href=../../how-to-guides/features/ class=md-nav__link> <span class=md-ellipsis> 功能概览 </span> </a> </li> <li class=md-nav__item> <a href=../../how-to-guides/offering/ class=md-nav__link> <span class=md-ellipsis> 产品定位 </span> </a> </li> </ul> </nav> </li> <li class="md-nav__item md-nav__item--nested"> <input class="md-nav__toggle md-toggle md-toggle--indeterminate" type=checkbox id=__nav_6> <label class=md-nav__link for=__nav_6 id=__nav_6_label tabindex=0> <span class=md-ellipsis> 🔧 技术文档 </span> <span class="md-nav__icon md-icon"></span> </label> <nav class=md-nav data-md-level=1 aria-labelledby=__nav_6_label aria-expanded=false> <label class=md-nav__title for=__nav_6> <span class="md-nav__icon md-icon"></span> 🔧 技术文档 </label> <ul class=md-nav__list data-md-scrollfix> <li class=md-nav__item> <a href=../../reference/database_overview/ class=md-nav__link> <span class=md-ellipsis> 数据库概览 </span> </a> </li> <li class=md-nav__item> <a href=../../guides/MCP_SETUP/ class=md-nav__link> <span class=md-ellipsis> MCP设置 </span> </a> </li> <li class=md-nav__item> <a href=../../how-to-guides/ib_fundamentals_guide/ class=md-nav__link> <span class=md-ellipsis> IB基础指南 </span> </a> </li> </ul> </nav> </li> <li class="md-nav__item md-nav__item--nested"> <input class="md-nav__toggle md-toggle md-toggle--indeterminate" type=checkbox id=__nav_7> <label class=md-nav__link for=__nav_7 id=__nav_7_label tabindex=0> <span class=md-ellipsis> 📜 项目愿景 </span> <span class="md-nav__icon md-icon"></span> </label> <nav class=md-nav data-md-level=1 aria-labelledby=__nav_7_label aria-expanded=false> <label class=md-nav__title for=__nav_7> <span class="md-nav__icon md-icon"></span> 📜 项目愿景 </label> <ul class=md-nav__list data-md-scrollfix> <li class=md-nav__item> <a href=../../vision/three-tiers/ class=md-nav__link> <span class=md-ellipsis> 三层架构 </span> </a> </li> <li class=md-nav__item> <a href=../../guides/OPENSOURCE_ROADMAP/ class=md-nav__link> <span class=md-ellipsis> 开源路线图 </span> </a> </li> </ul> </nav> </li> <li class="md-nav__item md-nav__item--nested"> <input class="md-nav__toggle md-toggle md-toggle--indeterminate" type=checkbox id=__nav_8> <label class=md-nav__link for=__nav_8 id=__nav_8_label tabindex=0> <span class=md-ellipsis> 🚀 部署指南 </span> <span class="md-nav__icon md-icon"></span> </label> <nav class=md-nav data-md-level=1 aria-labelledby=__nav_8_label aria-expanded=false> <label class=md-nav__title for=__nav_8> <span class="md-nav__icon md-icon"></span> 🚀 部署指南 </label> <ul class=md-nav__list data-md-scrollfix> <li class=md-nav__item> <a href=../../how-to-guides/deployment/ class=md-nav__link> <span class=md-ellipsis> 部署指南 </span> </a> </li> </ul> </nav> </li> <li class="md-nav__item md-nav__item--nested"> <input class="md-nav__toggle md-toggle md-toggle--indeterminate" type=checkbox id=__nav_9> <label class=md-nav__link for=__nav_9 id=__nav_9_label tabindex=0> <span class=md-ellipsis> 📡 API文档 </span> <span class="md-nav__icon md-icon"></span> </label> <nav class=md-nav data-md-level=1 aria-labelledby=__nav_9_label aria-expanded=false> <label class=md-nav__title for=__nav_9> <span class="md-nav__icon md-icon"></span> 📡 API文档 </label> <ul class=md-nav__list data-md-scrollfix> <li class=md-nav__item> <a href=../../api/ class=md-nav__link> <span class=md-ellipsis> API参考 </span> </a> </li> </ul> </nav> </li> <li class="md-nav__item md-nav__item--nested"> <input class="md-nav__toggle md-toggle md-toggle--indeterminate" type=checkbox id=__nav_10> <label class=md-nav__link for=__nav_10 id=__nav_10_label tabindex=0> <span class=md-ellipsis> 🤝 贡献指南 </span> <span class="md-nav__icon md-icon"></span> </label> <nav class=md-nav data-md-level=1 aria-labelledby=__nav_10_label aria-expanded=false> <label class=md-nav__title for=__nav_10> <span class="md-nav__icon md-icon"></span> 🤝 贡献指南 </label> <ul class=md-nav__list data-md-scrollfix> <li class=md-nav__item> <a href=../../CONTRIBUTING/ class=md-nav__link> <span class=md-ellipsis> 贡献指南 </span> </a> </li> <li class=md-nav__item> <a href=../../guides/DOCS_REORGANIZATION_PLAN/ class=md-nav__link> <span class=md-ellipsis> 文档重组计划 </span> </a> </li> </ul> </nav> </li> </ul> </nav> </div> </div> </div> <div class="md-sidebar md-sidebar--secondary" data-md-component=sidebar data-md-type=toc> <div class=md-sidebar__scrollwrap> <div class=md-sidebar__inner> <nav class="md-nav md-nav--secondary" aria-label=目录> <label class=md-nav__title for=__toc> <span class="md-nav__icon md-icon"></span> 目录 </label> <ul class=md-nav__list data-md-component=toc data-md-scrollfix> <li class=md-nav__item> <a href=#graphrag_1 class=md-nav__link> <span class=md-ellipsis> 🎯 什么是GraphRAG？ </span> </a> <nav class=md-nav aria-label="🎯 什么是GraphRAG？"> <ul class=md-nav__list> <li class=md-nav__item> <a href=#_1 class=md-nav__link> <span class=md-ellipsis> 核心优势 </span> </a> </li> </ul> </nav> </li> <li class=md-nav__item> <a href=#graphrag_2 class=md-nav__link> <span class=md-ellipsis> 🏗️ GraphRAG架构 </span> </a> <nav class=md-nav aria-label="🏗️ GraphRAG架构"> <ul class=md-nav__list> <li class=md-nav__item> <a href=#rag class=md-nav__link> <span class=md-ellipsis> 与传统RAG的区别 </span> </a> </li> </ul> </nav> </li> <li class=md-nav__item> <a href=#_2 class=md-nav__link> <span class=md-ellipsis> 🚀 部署方案 </span> </a> <nav class=md-nav aria-label="🚀 部署方案"> <ul class=md-nav__list> <li class=md-nav__item> <a href=#1-docker class=md-nav__link> <span class=md-ellipsis> 方案1: 官方Docker部署（推荐） </span> </a> </li> <li class=md-nav__item> <a href=#2-python class=md-nav__link> <span class=md-ellipsis> 方案2: Python环境部署 </span> </a> </li> <li class=md-nav__item> <a href=#3 class=md-nav__link> <span class=md-ellipsis> 方案3: 与你的太公心易系统集成 </span> </a> </li> </ul> </nav> </li> <li class=md-nav__item> <a href=#_3 class=md-nav__link> <span class=md-ellipsis> 🔧 与你现有系统的集成方案 </span> </a> <nav class=md-nav aria-label="🔧 与你现有系统的集成方案"> <ul class=md-nav__list> <li class=md-nav__item> <a href=#n8n class=md-nav__link> <span class=md-ellipsis> 集成到N8N工作流 </span> </a> </li> </ul> </nav> </li> <li class=md-nav__item> <a href=#_4 class=md-nav__link> <span class=md-ellipsis> 🎯 适合你项目的部署建议 </span> </a> <nav class=md-nav aria-label="🎯 适合你项目的部署建议"> <ul class=md-nav__list> <li class=md-nav__item> <a href=#1 class=md-nav__link> <span class=md-ellipsis> 阶段1: 试验部署 </span> </a> </li> <li class=md-nav__item> <a href=#2 class=md-nav__link> <span class=md-ellipsis> 阶段2: 集成部署 </span> </a> </li> <li class=md-nav__item> <a href=#3_1 class=md-nav__link> <span class=md-ellipsis> 阶段3: 生产部署 </span> </a> </li> </ul> </nav> </li> <li class=md-nav__item> <a href=#_5 class=md-nav__link> <span class=md-ellipsis> 💡 成本和性能考虑 </span> </a> </li> </ul> </nav> </div> </div> </div> <div class=md-content data-md-component=content> <article class="md-content__inner md-typeset"> <h1 id=graphrag>GraphRAG 完整指南<a class=headerlink href=#graphrag title="Permanent link">&para;</a></h1> <h2 id=graphrag_1>🎯 什么是GraphRAG？<a class=headerlink href=#graphrag_1 title="Permanent link">&para;</a></h2> <p>GraphRAG是微软开发的一个**知识图谱增强的检索增强生成(Retrieval-Augmented Generation)**系统，它结合了：</p> <ul> <li><strong>知识图谱</strong> - 构建实体和关系的图谱</li> <li><strong>向量检索</strong> - 传统的语义搜索</li> <li><strong>LLM推理</strong> - 大语言模型的生成能力</li> </ul> <h3 id=_1>核心优势<a class=headerlink href=#_1 title="Permanent link">&para;</a></h3> <ul> <li>🧠 <strong>多跳推理</strong> - 可以进行复杂的关系推理</li> <li>🔗 <strong>实体关系</strong> - 理解数据中的实体和关系</li> <li>📊 <strong>全局理解</strong> - 不仅仅是局部相似性匹配</li> <li>🎯 <strong>精准回答</strong> - 基于知识图谱的结构化推理</li> </ul> <h2 id=graphrag_2>🏗️ GraphRAG架构<a class=headerlink href=#graphrag_2 title="Permanent link">&para;</a></h2> <div class="language-text highlight"><pre><span></span><code><span id=__span-0-1><a id=__codelineno-0-1 name=__codelineno-0-1 href=#__codelineno-0-1></a>文档输入 → 实体提取 → 关系识别 → 知识图谱构建 → 向量化 → 检索+推理 → 生成回答
</span></code></pre></div> <h3 id=rag>与传统RAG的区别<a class=headerlink href=#rag title="Permanent link">&para;</a></h3> <table> <thead> <tr> <th>维度</th> <th>传统RAG</th> <th>GraphRAG</th> </tr> </thead> <tbody> <tr> <td><strong>检索方式</strong></td> <td>向量相似度</td> <td>图谱+向量</td> </tr> <tr> <td><strong>推理能力</strong></td> <td>单跳检索</td> <td>多跳推理</td> </tr> <tr> <td><strong>关系理解</strong></td> <td>弱</td> <td>强</td> </tr> <tr> <td><strong>全局视角</strong></td> <td>局部</td> <td>全局</td> </tr> <tr> <td><strong>复杂查询</strong></td> <td>有限</td> <td>强大</td> </tr> </tbody> </table> <h2 id=_2>🚀 部署方案<a class=headerlink href=#_2 title="Permanent link">&para;</a></h2> <h3 id=1-docker>方案1: 官方Docker部署（推荐）<a class=headerlink href=#1-docker title="Permanent link">&para;</a></h3> <div class="language-bash highlight"><pre><span></span><code><span id=__span-1-1><a id=__codelineno-1-1 name=__codelineno-1-1 href=#__codelineno-1-1></a><span class=c1># 1. 克隆项目</span>
</span><span id=__span-1-2><a id=__codelineno-1-2 name=__codelineno-1-2 href=#__codelineno-1-2></a>git<span class=w> </span>clone<span class=w> </span>https://github.com/microsoft/graphrag.git
</span><span id=__span-1-3><a id=__codelineno-1-3 name=__codelineno-1-3 href=#__codelineno-1-3></a><span class=nb>cd</span><span class=w> </span>graphrag
</span><span id=__span-1-4><a id=__codelineno-1-4 name=__codelineno-1-4 href=#__codelineno-1-4></a>
</span><span id=__span-1-5><a id=__codelineno-1-5 name=__codelineno-1-5 href=#__codelineno-1-5></a><span class=c1># 2. 创建配置文件</span>
</span><span id=__span-1-6><a id=__codelineno-1-6 name=__codelineno-1-6 href=#__codelineno-1-6></a>mkdir<span class=w> </span>-p<span class=w> </span>./ragtest/input
</span><span id=__span-1-7><a id=__codelineno-1-7 name=__codelineno-1-7 href=#__codelineno-1-7></a><span class=nb>echo</span><span class=w> </span><span class=s2>&quot;你的文档内容&quot;</span><span class=w> </span>&gt;<span class=w> </span>./ragtest/input/book.txt
</span><span id=__span-1-8><a id=__codelineno-1-8 name=__codelineno-1-8 href=#__codelineno-1-8></a>
</span><span id=__span-1-9><a id=__codelineno-1-9 name=__codelineno-1-9 href=#__codelineno-1-9></a><span class=c1># 3. 初始化配置</span>
</span><span id=__span-1-10><a id=__codelineno-1-10 name=__codelineno-1-10 href=#__codelineno-1-10></a>python<span class=w> </span>-m<span class=w> </span>graphrag.index<span class=w> </span>--init<span class=w> </span>--root<span class=w> </span>./ragtest
</span><span id=__span-1-11><a id=__codelineno-1-11 name=__codelineno-1-11 href=#__codelineno-1-11></a>
</span><span id=__span-1-12><a id=__codelineno-1-12 name=__codelineno-1-12 href=#__codelineno-1-12></a><span class=c1># 4. 配置API密钥</span>
</span><span id=__span-1-13><a id=__codelineno-1-13 name=__codelineno-1-13 href=#__codelineno-1-13></a><span class=c1># 编辑 ./ragtest/settings.yaml</span>
</span></code></pre></div> <p><strong>settings.yaml 配置示例：</strong> <div class="language-yaml highlight"><pre><span></span><code><span id=__span-2-1><a id=__codelineno-2-1 name=__codelineno-2-1 href=#__codelineno-2-1></a><span class=nt>llm</span><span class=p>:</span>
</span><span id=__span-2-2><a id=__codelineno-2-2 name=__codelineno-2-2 href=#__codelineno-2-2></a><span class=w>  </span><span class=nt>api_key</span><span class=p>:</span><span class=w> </span><span class="l l-Scalar l-Scalar-Plain">${GRAPHRAG_API_KEY}</span>
</span><span id=__span-2-3><a id=__codelineno-2-3 name=__codelineno-2-3 href=#__codelineno-2-3></a><span class=w>  </span><span class=nt>type</span><span class=p>:</span><span class=w> </span><span class="l l-Scalar l-Scalar-Plain">openai_chat</span>
</span><span id=__span-2-4><a id=__codelineno-2-4 name=__codelineno-2-4 href=#__codelineno-2-4></a><span class=w>  </span><span class=nt>model</span><span class=p>:</span><span class=w> </span><span class="l l-Scalar l-Scalar-Plain">gpt-4</span>
</span><span id=__span-2-5><a id=__codelineno-2-5 name=__codelineno-2-5 href=#__codelineno-2-5></a><span class=w>  </span><span class=nt>model_supports_json</span><span class=p>:</span><span class=w> </span><span class="l l-Scalar l-Scalar-Plain">true</span>
</span><span id=__span-2-6><a id=__codelineno-2-6 name=__codelineno-2-6 href=#__codelineno-2-6></a>
</span><span id=__span-2-7><a id=__codelineno-2-7 name=__codelineno-2-7 href=#__codelineno-2-7></a><span class=nt>embeddings</span><span class=p>:</span>
</span><span id=__span-2-8><a id=__codelineno-2-8 name=__codelineno-2-8 href=#__codelineno-2-8></a><span class=w>  </span><span class=nt>api_key</span><span class=p>:</span><span class=w> </span><span class="l l-Scalar l-Scalar-Plain">${GRAPHRAG_API_KEY}</span>
</span><span id=__span-2-9><a id=__codelineno-2-9 name=__codelineno-2-9 href=#__codelineno-2-9></a><span class=w>  </span><span class=nt>type</span><span class=p>:</span><span class=w> </span><span class="l l-Scalar l-Scalar-Plain">openai_embedding</span>
</span><span id=__span-2-10><a id=__codelineno-2-10 name=__codelineno-2-10 href=#__codelineno-2-10></a><span class=w>  </span><span class=nt>model</span><span class=p>:</span><span class=w> </span><span class="l l-Scalar l-Scalar-Plain">text-embedding-ada-002</span>
</span><span id=__span-2-11><a id=__codelineno-2-11 name=__codelineno-2-11 href=#__codelineno-2-11></a>
</span><span id=__span-2-12><a id=__codelineno-2-12 name=__codelineno-2-12 href=#__codelineno-2-12></a><span class=nt>input</span><span class=p>:</span>
</span><span id=__span-2-13><a id=__codelineno-2-13 name=__codelineno-2-13 href=#__codelineno-2-13></a><span class=w>  </span><span class=nt>type</span><span class=p>:</span><span class=w> </span><span class="l l-Scalar l-Scalar-Plain">file</span>
</span><span id=__span-2-14><a id=__codelineno-2-14 name=__codelineno-2-14 href=#__codelineno-2-14></a><span class=w>  </span><span class=nt>file_type</span><span class=p>:</span><span class=w> </span><span class="l l-Scalar l-Scalar-Plain">text</span>
</span><span id=__span-2-15><a id=__codelineno-2-15 name=__codelineno-2-15 href=#__codelineno-2-15></a><span class=w>  </span><span class=nt>base_dir</span><span class=p>:</span><span class=w> </span><span class=s>&quot;input&quot;</span>
</span><span id=__span-2-16><a id=__codelineno-2-16 name=__codelineno-2-16 href=#__codelineno-2-16></a><span class=w>  </span><span class=nt>file_encoding</span><span class=p>:</span><span class=w> </span><span class="l l-Scalar l-Scalar-Plain">utf-8</span>
</span><span id=__span-2-17><a id=__codelineno-2-17 name=__codelineno-2-17 href=#__codelineno-2-17></a><span class=w>  </span><span class=nt>file_pattern</span><span class=p>:</span><span class=w> </span><span class=s>&quot;.*\\.txt$&quot;</span>
</span><span id=__span-2-18><a id=__codelineno-2-18 name=__codelineno-2-18 href=#__codelineno-2-18></a>
</span><span id=__span-2-19><a id=__codelineno-2-19 name=__codelineno-2-19 href=#__codelineno-2-19></a><span class=nt>cache</span><span class=p>:</span>
</span><span id=__span-2-20><a id=__codelineno-2-20 name=__codelineno-2-20 href=#__codelineno-2-20></a><span class=w>  </span><span class=nt>type</span><span class=p>:</span><span class=w> </span><span class="l l-Scalar l-Scalar-Plain">file</span>
</span><span id=__span-2-21><a id=__codelineno-2-21 name=__codelineno-2-21 href=#__codelineno-2-21></a><span class=w>  </span><span class=nt>base_dir</span><span class=p>:</span><span class=w> </span><span class=s>&quot;cache&quot;</span>
</span><span id=__span-2-22><a id=__codelineno-2-22 name=__codelineno-2-22 href=#__codelineno-2-22></a>
</span><span id=__span-2-23><a id=__codelineno-2-23 name=__codelineno-2-23 href=#__codelineno-2-23></a><span class=nt>storage</span><span class=p>:</span>
</span><span id=__span-2-24><a id=__codelineno-2-24 name=__codelineno-2-24 href=#__codelineno-2-24></a><span class=w>  </span><span class=nt>type</span><span class=p>:</span><span class=w> </span><span class="l l-Scalar l-Scalar-Plain">file</span>
</span><span id=__span-2-25><a id=__codelineno-2-25 name=__codelineno-2-25 href=#__codelineno-2-25></a><span class=w>  </span><span class=nt>base_dir</span><span class=p>:</span><span class=w> </span><span class=s>&quot;output&quot;</span>
</span><span id=__span-2-26><a id=__codelineno-2-26 name=__codelineno-2-26 href=#__codelineno-2-26></a>
</span><span id=__span-2-27><a id=__codelineno-2-27 name=__codelineno-2-27 href=#__codelineno-2-27></a><span class=nt>chunk</span><span class=p>:</span>
</span><span id=__span-2-28><a id=__codelineno-2-28 name=__codelineno-2-28 href=#__codelineno-2-28></a><span class=w>  </span><span class=nt>size</span><span class=p>:</span><span class=w> </span><span class="l l-Scalar l-Scalar-Plain">300</span>
</span><span id=__span-2-29><a id=__codelineno-2-29 name=__codelineno-2-29 href=#__codelineno-2-29></a><span class=w>  </span><span class=nt>overlap</span><span class=p>:</span><span class=w> </span><span class="l l-Scalar l-Scalar-Plain">100</span>
</span><span id=__span-2-30><a id=__codelineno-2-30 name=__codelineno-2-30 href=#__codelineno-2-30></a>
</span><span id=__span-2-31><a id=__codelineno-2-31 name=__codelineno-2-31 href=#__codelineno-2-31></a><span class=nt>entity_extraction</span><span class=p>:</span>
</span><span id=__span-2-32><a id=__codelineno-2-32 name=__codelineno-2-32 href=#__codelineno-2-32></a><span class=w>  </span><span class=nt>prompt</span><span class=p>:</span><span class=w> </span><span class=s>&quot;prompts/entity_extraction.txt&quot;</span>
</span><span id=__span-2-33><a id=__codelineno-2-33 name=__codelineno-2-33 href=#__codelineno-2-33></a><span class=w>  </span><span class=nt>entity_types</span><span class=p>:</span><span class=w> </span><span class="p p-Indicator">[</span><span class=nv>person</span><span class="p p-Indicator">,</span><span class=nv>organization</span><span class="p p-Indicator">,</span><span class=nv>location</span><span class="p p-Indicator">,</span><span class=nv>event</span><span class="p p-Indicator">,</span><span class=nv>concept</span><span class="p p-Indicator">]</span>
</span><span id=__span-2-34><a id=__codelineno-2-34 name=__codelineno-2-34 href=#__codelineno-2-34></a><span class=w>  </span><span class=nt>max_gleanings</span><span class=p>:</span><span class=w> </span><span class="l l-Scalar l-Scalar-Plain">1</span>
</span><span id=__span-2-35><a id=__codelineno-2-35 name=__codelineno-2-35 href=#__codelineno-2-35></a>
</span><span id=__span-2-36><a id=__codelineno-2-36 name=__codelineno-2-36 href=#__codelineno-2-36></a><span class=nt>summarize_descriptions</span><span class=p>:</span>
</span><span id=__span-2-37><a id=__codelineno-2-37 name=__codelineno-2-37 href=#__codelineno-2-37></a><span class=w>  </span><span class=nt>prompt</span><span class=p>:</span><span class=w> </span><span class=s>&quot;prompts/summarize_descriptions.txt&quot;</span>
</span><span id=__span-2-38><a id=__codelineno-2-38 name=__codelineno-2-38 href=#__codelineno-2-38></a><span class=w>  </span><span class=nt>max_length</span><span class=p>:</span><span class=w> </span><span class="l l-Scalar l-Scalar-Plain">500</span>
</span><span id=__span-2-39><a id=__codelineno-2-39 name=__codelineno-2-39 href=#__codelineno-2-39></a>
</span><span id=__span-2-40><a id=__codelineno-2-40 name=__codelineno-2-40 href=#__codelineno-2-40></a><span class=nt>claim_extraction</span><span class=p>:</span>
</span><span id=__span-2-41><a id=__codelineno-2-41 name=__codelineno-2-41 href=#__codelineno-2-41></a><span class=w>  </span><span class=nt>enabled</span><span class=p>:</span><span class=w> </span><span class="l l-Scalar l-Scalar-Plain">true</span>
</span><span id=__span-2-42><a id=__codelineno-2-42 name=__codelineno-2-42 href=#__codelineno-2-42></a><span class=w>  </span><span class=nt>prompt</span><span class=p>:</span><span class=w> </span><span class=s>&quot;prompts/claim_extraction.txt&quot;</span>
</span><span id=__span-2-43><a id=__codelineno-2-43 name=__codelineno-2-43 href=#__codelineno-2-43></a><span class=w>  </span><span class=nt>description</span><span class=p>:</span><span class=w> </span><span class=s>&quot;Any</span><span class=nv> </span><span class=s>claims</span><span class=nv> </span><span class=s>or</span><span class=nv> </span><span class=s>facts</span><span class=nv> </span><span class=s>that</span><span class=nv> </span><span class=s>could</span><span class=nv> </span><span class=s>be</span><span class=nv> </span><span class=s>relevant</span><span class=nv> </span><span class=s>to</span><span class=nv> </span><span class=s>information</span><span class=nv> </span><span class=s>discovery.&quot;</span>
</span><span id=__span-2-44><a id=__codelineno-2-44 name=__codelineno-2-44 href=#__codelineno-2-44></a><span class=w>  </span><span class=nt>max_gleanings</span><span class=p>:</span><span class=w> </span><span class="l l-Scalar l-Scalar-Plain">1</span>
</span><span id=__span-2-45><a id=__codelineno-2-45 name=__codelineno-2-45 href=#__codelineno-2-45></a>
</span><span id=__span-2-46><a id=__codelineno-2-46 name=__codelineno-2-46 href=#__codelineno-2-46></a><span class=nt>community_detection</span><span class=p>:</span>
</span><span id=__span-2-47><a id=__codelineno-2-47 name=__codelineno-2-47 href=#__codelineno-2-47></a><span class=w>  </span><span class=nt>max_cluster_size</span><span class=p>:</span><span class=w> </span><span class="l l-Scalar l-Scalar-Plain">10</span>
</span><span id=__span-2-48><a id=__codelineno-2-48 name=__codelineno-2-48 href=#__codelineno-2-48></a>
</span><span id=__span-2-49><a id=__codelineno-2-49 name=__codelineno-2-49 href=#__codelineno-2-49></a><span class=nt>umap</span><span class=p>:</span>
</span><span id=__span-2-50><a id=__codelineno-2-50 name=__codelineno-2-50 href=#__codelineno-2-50></a><span class=w>  </span><span class=nt>enabled</span><span class=p>:</span><span class=w> </span><span class="l l-Scalar l-Scalar-Plain">true</span>
</span><span id=__span-2-51><a id=__codelineno-2-51 name=__codelineno-2-51 href=#__codelineno-2-51></a>
</span><span id=__span-2-52><a id=__codelineno-2-52 name=__codelineno-2-52 href=#__codelineno-2-52></a><span class=nt>snapshots</span><span class=p>:</span>
</span><span id=__span-2-53><a id=__codelineno-2-53 name=__codelineno-2-53 href=#__codelineno-2-53></a><span class=w>  </span><span class=nt>graphml</span><span class=p>:</span><span class=w> </span><span class="l l-Scalar l-Scalar-Plain">true</span>
</span><span id=__span-2-54><a id=__codelineno-2-54 name=__codelineno-2-54 href=#__codelineno-2-54></a><span class=w>  </span><span class=nt>raw_entities</span><span class=p>:</span><span class=w> </span><span class="l l-Scalar l-Scalar-Plain">true</span>
</span><span id=__span-2-55><a id=__codelineno-2-55 name=__codelineno-2-55 href=#__codelineno-2-55></a><span class=w>  </span><span class=nt>top_level_nodes</span><span class=p>:</span><span class=w> </span><span class="l l-Scalar l-Scalar-Plain">true</span>
</span></code></pre></div></p> <div class="language-bash highlight"><pre><span></span><code><span id=__span-3-1><a id=__codelineno-3-1 name=__codelineno-3-1 href=#__codelineno-3-1></a><span class=c1># 5. 构建知识图谱</span>
</span><span id=__span-3-2><a id=__codelineno-3-2 name=__codelineno-3-2 href=#__codelineno-3-2></a>python<span class=w> </span>-m<span class=w> </span>graphrag.index<span class=w> </span>--root<span class=w> </span>./ragtest
</span><span id=__span-3-3><a id=__codelineno-3-3 name=__codelineno-3-3 href=#__codelineno-3-3></a>
</span><span id=__span-3-4><a id=__codelineno-3-4 name=__codelineno-3-4 href=#__codelineno-3-4></a><span class=c1># 6. 查询测试</span>
</span><span id=__span-3-5><a id=__codelineno-3-5 name=__codelineno-3-5 href=#__codelineno-3-5></a>python<span class=w> </span>-m<span class=w> </span>graphrag.query<span class=w> </span><span class=se>\</span>
</span><span id=__span-3-6><a id=__codelineno-3-6 name=__codelineno-3-6 href=#__codelineno-3-6></a><span class=w>    </span>--root<span class=w> </span>./ragtest<span class=w> </span><span class=se>\</span>
</span><span id=__span-3-7><a id=__codelineno-3-7 name=__codelineno-3-7 href=#__codelineno-3-7></a><span class=w>    </span>--method<span class=w> </span>global<span class=w> </span><span class=se>\</span>
</span><span id=__span-3-8><a id=__codelineno-3-8 name=__codelineno-3-8 href=#__codelineno-3-8></a><span class=w>    </span><span class=s2>&quot;What are the main themes in this document?&quot;</span>
</span><span id=__span-3-9><a id=__codelineno-3-9 name=__codelineno-3-9 href=#__codelineno-3-9></a>
</span><span id=__span-3-10><a id=__codelineno-3-10 name=__codelineno-3-10 href=#__codelineno-3-10></a>python<span class=w> </span>-m<span class=w> </span>graphrag.query<span class=w> </span><span class=se>\</span>
</span><span id=__span-3-11><a id=__codelineno-3-11 name=__codelineno-3-11 href=#__codelineno-3-11></a><span class=w>    </span>--root<span class=w> </span>./ragtest<span class=w> </span><span class=se>\</span>
</span><span id=__span-3-12><a id=__codelineno-3-12 name=__codelineno-3-12 href=#__codelineno-3-12></a><span class=w>    </span>--method<span class=w> </span><span class=nb>local</span><span class=w> </span><span class=se>\</span>
</span><span id=__span-3-13><a id=__codelineno-3-13 name=__codelineno-3-13 href=#__codelineno-3-13></a><span class=w>    </span><span class=s2>&quot;Tell me about specific entities mentioned&quot;</span>
</span></code></pre></div> <h3 id=2-python>方案2: Python环境部署<a class=headerlink href=#2-python title="Permanent link">&para;</a></h3> <div class="language-bash highlight"><pre><span></span><code><span id=__span-4-1><a id=__codelineno-4-1 name=__codelineno-4-1 href=#__codelineno-4-1></a><span class=c1># 1. 创建虚拟环境</span>
</span><span id=__span-4-2><a id=__codelineno-4-2 name=__codelineno-4-2 href=#__codelineno-4-2></a>python<span class=w> </span>-m<span class=w> </span>venv<span class=w> </span>graphrag_env
</span><span id=__span-4-3><a id=__codelineno-4-3 name=__codelineno-4-3 href=#__codelineno-4-3></a><span class=nb>source</span><span class=w> </span>graphrag_env/bin/activate<span class=w>  </span><span class=c1># Linux/Mac</span>
</span><span id=__span-4-4><a id=__codelineno-4-4 name=__codelineno-4-4 href=#__codelineno-4-4></a><span class=c1># graphrag_env\Scripts\activate  # Windows</span>
</span><span id=__span-4-5><a id=__codelineno-4-5 name=__codelineno-4-5 href=#__codelineno-4-5></a>
</span><span id=__span-4-6><a id=__codelineno-4-6 name=__codelineno-4-6 href=#__codelineno-4-6></a><span class=c1># 2. 安装GraphRAG</span>
</span><span id=__span-4-7><a id=__codelineno-4-7 name=__codelineno-4-7 href=#__codelineno-4-7></a>pip<span class=w> </span>install<span class=w> </span>graphrag
</span><span id=__span-4-8><a id=__codelineno-4-8 name=__codelineno-4-8 href=#__codelineno-4-8></a>
</span><span id=__span-4-9><a id=__codelineno-4-9 name=__codelineno-4-9 href=#__codelineno-4-9></a><span class=c1># 3. 验证安装</span>
</span><span id=__span-4-10><a id=__codelineno-4-10 name=__codelineno-4-10 href=#__codelineno-4-10></a>python<span class=w> </span>-c<span class=w> </span><span class=s2>&quot;import graphrag; print(&#39;GraphRAG installed successfully&#39;)&quot;</span>
</span></code></pre></div> <h3 id=3>方案3: 与你的太公心易系统集成<a class=headerlink href=#3 title="Permanent link">&para;</a></h3> <div class="language-python highlight"><pre><span></span><code><span id=__span-5-1><a id=__codelineno-5-1 name=__codelineno-5-1 href=#__codelineno-5-1></a><span class=c1># 集成到你的项目中</span>
</span><span id=__span-5-2><a id=__codelineno-5-2 name=__codelineno-5-2 href=#__codelineno-5-2></a><span class=kn>import</span><span class=w> </span><span class=nn>asyncio</span>
</span><span id=__span-5-3><a id=__codelineno-5-3 name=__codelineno-5-3 href=#__codelineno-5-3></a><span class=kn>from</span><span class=w> </span><span class=nn>graphrag.query.llm.oai.chat_openai</span><span class=w> </span><span class=kn>import</span> <span class=n>ChatOpenAI</span>
</span><span id=__span-5-4><a id=__codelineno-5-4 name=__codelineno-5-4 href=#__codelineno-5-4></a><span class=kn>from</span><span class=w> </span><span class=nn>graphrag.query.llm.oai.embedding</span><span class=w> </span><span class=kn>import</span> <span class=n>OpenAIEmbedding</span>
</span><span id=__span-5-5><a id=__codelineno-5-5 name=__codelineno-5-5 href=#__codelineno-5-5></a><span class=kn>from</span><span class=w> </span><span class=nn>graphrag.query.indexer_adapters</span><span class=w> </span><span class=kn>import</span> <span class=n>read_indexer_entities</span><span class=p>,</span> <span class=n>read_indexer_reports</span>
</span><span id=__span-5-6><a id=__codelineno-5-6 name=__codelineno-5-6 href=#__codelineno-5-6></a><span class=kn>from</span><span class=w> </span><span class=nn>graphrag.query.context_builder.entity_extraction</span><span class=w> </span><span class=kn>import</span> <span class=n>EntityVectorStoreKey</span>
</span><span id=__span-5-7><a id=__codelineno-5-7 name=__codelineno-5-7 href=#__codelineno-5-7></a><span class=kn>from</span><span class=w> </span><span class=nn>graphrag.query.structured_search.global_search.community_context</span><span class=w> </span><span class=kn>import</span> <span class=n>GlobalCommunityContext</span>
</span><span id=__span-5-8><a id=__codelineno-5-8 name=__codelineno-5-8 href=#__codelineno-5-8></a><span class=kn>from</span><span class=w> </span><span class=nn>graphrag.query.structured_search.global_search.search</span><span class=w> </span><span class=kn>import</span> <span class=n>GlobalSearch</span>
</span><span id=__span-5-9><a id=__codelineno-5-9 name=__codelineno-5-9 href=#__codelineno-5-9></a>
</span><span id=__span-5-10><a id=__codelineno-5-10 name=__codelineno-5-10 href=#__codelineno-5-10></a><span class=k>class</span><span class=w> </span><span class=nc>TaigongXinyiGraphRAG</span><span class=p>:</span>
</span><span id=__span-5-11><a id=__codelineno-5-11 name=__codelineno-5-11 href=#__codelineno-5-11></a><span class=w>    </span><span class=sd>&quot;&quot;&quot;太公心易 + GraphRAG集成系统&quot;&quot;&quot;</span>
</span><span id=__span-5-12><a id=__codelineno-5-12 name=__codelineno-5-12 href=#__codelineno-5-12></a>
</span><span id=__span-5-13><a id=__codelineno-5-13 name=__codelineno-5-13 href=#__codelineno-5-13></a>    <span class=k>def</span><span class=w> </span><span class=fm>__init__</span><span class=p>(</span><span class=bp>self</span><span class=p>,</span> <span class=n>data_dir</span><span class=p>:</span> <span class=nb>str</span><span class=p>,</span> <span class=n>llm_params</span><span class=p>:</span> <span class=nb>dict</span><span class=p>,</span> <span class=n>embedding_params</span><span class=p>:</span> <span class=nb>dict</span><span class=p>):</span>
</span><span id=__span-5-14><a id=__codelineno-5-14 name=__codelineno-5-14 href=#__codelineno-5-14></a>        <span class=bp>self</span><span class=o>.</span><span class=n>data_dir</span> <span class=o>=</span> <span class=n>data_dir</span>
</span><span id=__span-5-15><a id=__codelineno-5-15 name=__codelineno-5-15 href=#__codelineno-5-15></a>        <span class=bp>self</span><span class=o>.</span><span class=n>llm</span> <span class=o>=</span> <span class=n>ChatOpenAI</span><span class=p>(</span><span class=o>**</span><span class=n>llm_params</span><span class=p>)</span>
</span><span id=__span-5-16><a id=__codelineno-5-16 name=__codelineno-5-16 href=#__codelineno-5-16></a>        <span class=bp>self</span><span class=o>.</span><span class=n>embedding</span> <span class=o>=</span> <span class=n>OpenAIEmbedding</span><span class=p>(</span><span class=o>**</span><span class=n>embedding_params</span><span class=p>)</span>
</span><span id=__span-5-17><a id=__codelineno-5-17 name=__codelineno-5-17 href=#__codelineno-5-17></a>        <span class=bp>self</span><span class=o>.</span><span class=n>setup_search_engines</span><span class=p>()</span>
</span><span id=__span-5-18><a id=__codelineno-5-18 name=__codelineno-5-18 href=#__codelineno-5-18></a>
</span><span id=__span-5-19><a id=__codelineno-5-19 name=__codelineno-5-19 href=#__codelineno-5-19></a>    <span class=k>def</span><span class=w> </span><span class=nf>setup_search_engines</span><span class=p>(</span><span class=bp>self</span><span class=p>):</span>
</span><span id=__span-5-20><a id=__codelineno-5-20 name=__codelineno-5-20 href=#__codelineno-5-20></a><span class=w>        </span><span class=sd>&quot;&quot;&quot;设置搜索引擎&quot;&quot;&quot;</span>
</span><span id=__span-5-21><a id=__codelineno-5-21 name=__codelineno-5-21 href=#__codelineno-5-21></a>        <span class=c1># 读取索引数据</span>
</span><span id=__span-5-22><a id=__codelineno-5-22 name=__codelineno-5-22 href=#__codelineno-5-22></a>        <span class=bp>self</span><span class=o>.</span><span class=n>entities</span> <span class=o>=</span> <span class=n>read_indexer_entities</span><span class=p>(</span><span class=bp>self</span><span class=o>.</span><span class=n>data_dir</span><span class=p>)</span>
</span><span id=__span-5-23><a id=__codelineno-5-23 name=__codelineno-5-23 href=#__codelineno-5-23></a>        <span class=bp>self</span><span class=o>.</span><span class=n>reports</span> <span class=o>=</span> <span class=n>read_indexer_reports</span><span class=p>(</span><span class=bp>self</span><span class=o>.</span><span class=n>data_dir</span><span class=p>)</span>
</span><span id=__span-5-24><a id=__codelineno-5-24 name=__codelineno-5-24 href=#__codelineno-5-24></a>
</span><span id=__span-5-25><a id=__codelineno-5-25 name=__codelineno-5-25 href=#__codelineno-5-25></a>        <span class=c1># 设置全局搜索</span>
</span><span id=__span-5-26><a id=__codelineno-5-26 name=__codelineno-5-26 href=#__codelineno-5-26></a>        <span class=bp>self</span><span class=o>.</span><span class=n>global_context</span> <span class=o>=</span> <span class=n>GlobalCommunityContext</span><span class=p>(</span>
</span><span id=__span-5-27><a id=__codelineno-5-27 name=__codelineno-5-27 href=#__codelineno-5-27></a>            <span class=n>community_reports</span><span class=o>=</span><span class=bp>self</span><span class=o>.</span><span class=n>reports</span><span class=p>,</span>
</span><span id=__span-5-28><a id=__codelineno-5-28 name=__codelineno-5-28 href=#__codelineno-5-28></a>            <span class=n>entities</span><span class=o>=</span><span class=bp>self</span><span class=o>.</span><span class=n>entities</span><span class=p>,</span>
</span><span id=__span-5-29><a id=__codelineno-5-29 name=__codelineno-5-29 href=#__codelineno-5-29></a>            <span class=n>token_encoder</span><span class=o>=</span><span class=bp>self</span><span class=o>.</span><span class=n>llm</span><span class=o>.</span><span class=n>get_token_encoder</span><span class=p>()</span>
</span><span id=__span-5-30><a id=__codelineno-5-30 name=__codelineno-5-30 href=#__codelineno-5-30></a>        <span class=p>)</span>
</span><span id=__span-5-31><a id=__codelineno-5-31 name=__codelineno-5-31 href=#__codelineno-5-31></a>
</span><span id=__span-5-32><a id=__codelineno-5-32 name=__codelineno-5-32 href=#__codelineno-5-32></a>        <span class=bp>self</span><span class=o>.</span><span class=n>global_search</span> <span class=o>=</span> <span class=n>GlobalSearch</span><span class=p>(</span>
</span><span id=__span-5-33><a id=__codelineno-5-33 name=__codelineno-5-33 href=#__codelineno-5-33></a>            <span class=n>llm</span><span class=o>=</span><span class=bp>self</span><span class=o>.</span><span class=n>llm</span><span class=p>,</span>
</span><span id=__span-5-34><a id=__codelineno-5-34 name=__codelineno-5-34 href=#__codelineno-5-34></a>            <span class=n>context_builder</span><span class=o>=</span><span class=bp>self</span><span class=o>.</span><span class=n>global_context</span><span class=p>,</span>
</span><span id=__span-5-35><a id=__codelineno-5-35 name=__codelineno-5-35 href=#__codelineno-5-35></a>            <span class=n>token_encoder</span><span class=o>=</span><span class=bp>self</span><span class=o>.</span><span class=n>llm</span><span class=o>.</span><span class=n>get_token_encoder</span><span class=p>(),</span>
</span><span id=__span-5-36><a id=__codelineno-5-36 name=__codelineno-5-36 href=#__codelineno-5-36></a>            <span class=n>max_tokens</span><span class=o>=</span><span class=mi>12000</span>
</span><span id=__span-5-37><a id=__codelineno-5-37 name=__codelineno-5-37 href=#__codelineno-5-37></a>        <span class=p>)</span>
</span><span id=__span-5-38><a id=__codelineno-5-38 name=__codelineno-5-38 href=#__codelineno-5-38></a>
</span><span id=__span-5-39><a id=__codelineno-5-39 name=__codelineno-5-39 href=#__codelineno-5-39></a>    <span class=k>async</span> <span class=k>def</span><span class=w> </span><span class=nf>xinyi_enhanced_query</span><span class=p>(</span><span class=bp>self</span><span class=p>,</span> <span class=n>question</span><span class=p>:</span> <span class=nb>str</span><span class=p>,</span> <span class=n>gua_context</span><span class=p>:</span> <span class=nb>dict</span> <span class=o>=</span> <span class=kc>None</span><span class=p>):</span>
</span><span id=__span-5-40><a id=__codelineno-5-40 name=__codelineno-5-40 href=#__codelineno-5-40></a><span class=w>        </span><span class=sd>&quot;&quot;&quot;结合易学智慧的增强查询&quot;&quot;&quot;</span>
</span><span id=__span-5-41><a id=__codelineno-5-41 name=__codelineno-5-41 href=#__codelineno-5-41></a>
</span><span id=__span-5-42><a id=__codelineno-5-42 name=__codelineno-5-42 href=#__codelineno-5-42></a>        <span class=c1># 1. GraphRAG全局搜索</span>
</span><span id=__span-5-43><a id=__codelineno-5-43 name=__codelineno-5-43 href=#__codelineno-5-43></a>        <span class=n>global_result</span> <span class=o>=</span> <span class=k>await</span> <span class=bp>self</span><span class=o>.</span><span class=n>global_search</span><span class=o>.</span><span class=n>asearch</span><span class=p>(</span><span class=n>question</span><span class=p>)</span>
</span><span id=__span-5-44><a id=__codelineno-5-44 name=__codelineno-5-44 href=#__codelineno-5-44></a>
</span><span id=__span-5-45><a id=__codelineno-5-45 name=__codelineno-5-45 href=#__codelineno-5-45></a>        <span class=c1># 2. 结合太公心易分析</span>
</span><span id=__span-5-46><a id=__codelineno-5-46 name=__codelineno-5-46 href=#__codelineno-5-46></a>        <span class=k>if</span> <span class=n>gua_context</span><span class=p>:</span>
</span><span id=__span-5-47><a id=__codelineno-5-47 name=__codelineno-5-47 href=#__codelineno-5-47></a>            <span class=n>enhanced_prompt</span> <span class=o>=</span> <span class=sa>f</span><span class=s2>&quot;&quot;&quot;</span>
</span><span id=__span-5-48><a id=__codelineno-5-48 name=__codelineno-5-48 href=#__codelineno-5-48></a><span class=s2>            基于以下信息进行分析：</span>
</span><span id=__span-5-49><a id=__codelineno-5-49 name=__codelineno-5-49 href=#__codelineno-5-49></a>
</span><span id=__span-5-50><a id=__codelineno-5-50 name=__codelineno-5-50 href=#__codelineno-5-50></a><span class=s2>            问题: </span><span class=si>{</span><span class=n>question</span><span class=si>}</span>
</span><span id=__span-5-51><a id=__codelineno-5-51 name=__codelineno-5-51 href=#__codelineno-5-51></a>
</span><span id=__span-5-52><a id=__codelineno-5-52 name=__codelineno-5-52 href=#__codelineno-5-52></a><span class=s2>            GraphRAG分析结果:</span>
</span><span id=__span-5-53><a id=__codelineno-5-53 name=__codelineno-5-53 href=#__codelineno-5-53></a><span class=s2>            </span><span class=si>{</span><span class=n>global_result</span><span class=o>.</span><span class=n>response</span><span class=si>}</span>
</span><span id=__span-5-54><a id=__codelineno-5-54 name=__codelineno-5-54 href=#__codelineno-5-54></a>
</span><span id=__span-5-55><a id=__codelineno-5-55 name=__codelineno-5-55 href=#__codelineno-5-55></a><span class=s2>            太公心易卦象指导:</span>
</span><span id=__span-5-56><a id=__codelineno-5-56 name=__codelineno-5-56 href=#__codelineno-5-56></a><span class=s2>            卦名: </span><span class=si>{</span><span class=n>gua_context</span><span class=o>.</span><span class=n>get</span><span class=p>(</span><span class=s1>&#39;name&#39;</span><span class=p>,</span><span class=w> </span><span class=s1>&#39;&#39;</span><span class=p>)</span><span class=si>}</span>
</span><span id=__span-5-57><a id=__codelineno-5-57 name=__codelineno-5-57 href=#__codelineno-5-57></a><span class=s2>            卦辞: </span><span class=si>{</span><span class=n>gua_context</span><span class=o>.</span><span class=n>get</span><span class=p>(</span><span class=s1>&#39;gua_ci&#39;</span><span class=p>,</span><span class=w> </span><span class=s1>&#39;&#39;</span><span class=p>)</span><span class=si>}</span>
</span><span id=__span-5-58><a id=__codelineno-5-58 name=__codelineno-5-58 href=#__codelineno-5-58></a><span class=s2>            判词: </span><span class=si>{</span><span class=n>gua_context</span><span class=o>.</span><span class=n>get</span><span class=p>(</span><span class=s1>&#39;judgment&#39;</span><span class=p>,</span><span class=w> </span><span class=s1>&#39;&#39;</span><span class=p>)</span><span class=si>}</span>
</span><span id=__span-5-59><a id=__codelineno-5-59 name=__codelineno-5-59 href=#__codelineno-5-59></a>
</span><span id=__span-5-60><a id=__codelineno-5-60 name=__codelineno-5-60 href=#__codelineno-5-60></a><span class=s2>            请结合现代数据分析和传统易学智慧，给出综合性的洞察和建议。</span>
</span><span id=__span-5-61><a id=__codelineno-5-61 name=__codelineno-5-61 href=#__codelineno-5-61></a><span class=s2>            &quot;&quot;&quot;</span>
</span><span id=__span-5-62><a id=__codelineno-5-62 name=__codelineno-5-62 href=#__codelineno-5-62></a>
</span><span id=__span-5-63><a id=__codelineno-5-63 name=__codelineno-5-63 href=#__codelineno-5-63></a>            <span class=c1># 3. 生成最终回答</span>
</span><span id=__span-5-64><a id=__codelineno-5-64 name=__codelineno-5-64 href=#__codelineno-5-64></a>            <span class=n>final_response</span> <span class=o>=</span> <span class=k>await</span> <span class=bp>self</span><span class=o>.</span><span class=n>llm</span><span class=o>.</span><span class=n>agenerate</span><span class=p>([</span><span class=n>enhanced_prompt</span><span class=p>])</span>
</span><span id=__span-5-65><a id=__codelineno-5-65 name=__codelineno-5-65 href=#__codelineno-5-65></a>
</span><span id=__span-5-66><a id=__codelineno-5-66 name=__codelineno-5-66 href=#__codelineno-5-66></a>            <span class=k>return</span> <span class=p>{</span>
</span><span id=__span-5-67><a id=__codelineno-5-67 name=__codelineno-5-67 href=#__codelineno-5-67></a>                <span class=s2>&quot;question&quot;</span><span class=p>:</span> <span class=n>question</span><span class=p>,</span>
</span><span id=__span-5-68><a id=__codelineno-5-68 name=__codelineno-5-68 href=#__codelineno-5-68></a>                <span class=s2>&quot;graphrag_analysis&quot;</span><span class=p>:</span> <span class=n>global_result</span><span class=o>.</span><span class=n>response</span><span class=p>,</span>
</span><span id=__span-5-69><a id=__codelineno-5-69 name=__codelineno-5-69 href=#__codelineno-5-69></a>                <span class=s2>&quot;xinyi_guidance&quot;</span><span class=p>:</span> <span class=n>gua_context</span><span class=p>,</span>
</span><span id=__span-5-70><a id=__codelineno-5-70 name=__codelineno-5-70 href=#__codelineno-5-70></a>                <span class=s2>&quot;integrated_insight&quot;</span><span class=p>:</span> <span class=n>final_response</span><span class=o>.</span><span class=n>generations</span><span class=p>[</span><span class=mi>0</span><span class=p>][</span><span class=mi>0</span><span class=p>]</span><span class=o>.</span><span class=n>text</span><span class=p>,</span>
</span><span id=__span-5-71><a id=__codelineno-5-71 name=__codelineno-5-71 href=#__codelineno-5-71></a>                <span class=s2>&quot;confidence&quot;</span><span class=p>:</span> <span class=n>global_result</span><span class=o>.</span><span class=n>context_data</span>
</span><span id=__span-5-72><a id=__codelineno-5-72 name=__codelineno-5-72 href=#__codelineno-5-72></a>            <span class=p>}</span>
</span><span id=__span-5-73><a id=__codelineno-5-73 name=__codelineno-5-73 href=#__codelineno-5-73></a>
</span><span id=__span-5-74><a id=__codelineno-5-74 name=__codelineno-5-74 href=#__codelineno-5-74></a>        <span class=k>return</span> <span class=n>global_result</span>
</span><span id=__span-5-75><a id=__codelineno-5-75 name=__codelineno-5-75 href=#__codelineno-5-75></a>
</span><span id=__span-5-76><a id=__codelineno-5-76 name=__codelineno-5-76 href=#__codelineno-5-76></a><span class=c1># 使用示例</span>
</span><span id=__span-5-77><a id=__codelineno-5-77 name=__codelineno-5-77 href=#__codelineno-5-77></a><span class=k>async</span> <span class=k>def</span><span class=w> </span><span class=nf>main</span><span class=p>():</span>
</span><span id=__span-5-78><a id=__codelineno-5-78 name=__codelineno-5-78 href=#__codelineno-5-78></a>    <span class=c1># 配置参数</span>
</span><span id=__span-5-79><a id=__codelineno-5-79 name=__codelineno-5-79 href=#__codelineno-5-79></a>    <span class=n>llm_params</span> <span class=o>=</span> <span class=p>{</span>
</span><span id=__span-5-80><a id=__codelineno-5-80 name=__codelineno-5-80 href=#__codelineno-5-80></a>        <span class=s2>&quot;api_key&quot;</span><span class=p>:</span> <span class=s2>&quot;your-openai-key&quot;</span><span class=p>,</span>
</span><span id=__span-5-81><a id=__codelineno-5-81 name=__codelineno-5-81 href=#__codelineno-5-81></a>        <span class=s2>&quot;model&quot;</span><span class=p>:</span> <span class=s2>&quot;gpt-4&quot;</span><span class=p>,</span>
</span><span id=__span-5-82><a id=__codelineno-5-82 name=__codelineno-5-82 href=#__codelineno-5-82></a>        <span class=s2>&quot;temperature&quot;</span><span class=p>:</span> <span class=mf>0.1</span>
</span><span id=__span-5-83><a id=__codelineno-5-83 name=__codelineno-5-83 href=#__codelineno-5-83></a>    <span class=p>}</span>
</span><span id=__span-5-84><a id=__codelineno-5-84 name=__codelineno-5-84 href=#__codelineno-5-84></a>
</span><span id=__span-5-85><a id=__codelineno-5-85 name=__codelineno-5-85 href=#__codelineno-5-85></a>    <span class=n>embedding_params</span> <span class=o>=</span> <span class=p>{</span>
</span><span id=__span-5-86><a id=__codelineno-5-86 name=__codelineno-5-86 href=#__codelineno-5-86></a>        <span class=s2>&quot;api_key&quot;</span><span class=p>:</span> <span class=s2>&quot;your-openai-key&quot;</span><span class=p>,</span> 
</span><span id=__span-5-87><a id=__codelineno-5-87 name=__codelineno-5-87 href=#__codelineno-5-87></a>        <span class=s2>&quot;model&quot;</span><span class=p>:</span> <span class=s2>&quot;text-embedding-ada-002&quot;</span>
</span><span id=__span-5-88><a id=__codelineno-5-88 name=__codelineno-5-88 href=#__codelineno-5-88></a>    <span class=p>}</span>
</span><span id=__span-5-89><a id=__codelineno-5-89 name=__codelineno-5-89 href=#__codelineno-5-89></a>
</span><span id=__span-5-90><a id=__codelineno-5-90 name=__codelineno-5-90 href=#__codelineno-5-90></a>    <span class=c1># 初始化系统</span>
</span><span id=__span-5-91><a id=__codelineno-5-91 name=__codelineno-5-91 href=#__codelineno-5-91></a>    <span class=n>xinyi_graphrag</span> <span class=o>=</span> <span class=n>TaigongXinyiGraphRAG</span><span class=p>(</span>
</span><span id=__span-5-92><a id=__codelineno-5-92 name=__codelineno-5-92 href=#__codelineno-5-92></a>        <span class=n>data_dir</span><span class=o>=</span><span class=s2>&quot;./ragtest/output&quot;</span><span class=p>,</span>
</span><span id=__span-5-93><a id=__codelineno-5-93 name=__codelineno-5-93 href=#__codelineno-5-93></a>        <span class=n>llm_params</span><span class=o>=</span><span class=n>llm_params</span><span class=p>,</span>
</span><span id=__span-5-94><a id=__codelineno-5-94 name=__codelineno-5-94 href=#__codelineno-5-94></a>        <span class=n>embedding_params</span><span class=o>=</span><span class=n>embedding_params</span>
</span><span id=__span-5-95><a id=__codelineno-5-95 name=__codelineno-5-95 href=#__codelineno-5-95></a>    <span class=p>)</span>
</span><span id=__span-5-96><a id=__codelineno-5-96 name=__codelineno-5-96 href=#__codelineno-5-96></a>
</span><span id=__span-5-97><a id=__codelineno-5-97 name=__codelineno-5-97 href=#__codelineno-5-97></a>    <span class=c1># 查询示例</span>
</span><span id=__span-5-98><a id=__codelineno-5-98 name=__codelineno-5-98 href=#__codelineno-5-98></a>    <span class=n>result</span> <span class=o>=</span> <span class=k>await</span> <span class=n>xinyi_graphrag</span><span class=o>.</span><span class=n>xinyi_enhanced_query</span><span class=p>(</span>
</span><span id=__span-5-99><a id=__codelineno-5-99 name=__codelineno-5-99 href=#__codelineno-5-99></a>        <span class=s2>&quot;当前市场趋势如何？&quot;</span><span class=p>,</span>
</span><span id=__span-5-100><a id=__codelineno-5-100 name=__codelineno-5-100 href=#__codelineno-5-100></a>        <span class=n>gua_context</span><span class=o>=</span><span class=p>{</span>
</span><span id=__span-5-101><a id=__codelineno-5-101 name=__codelineno-5-101 href=#__codelineno-5-101></a>            <span class=s2>&quot;name&quot;</span><span class=p>:</span> <span class=s2>&quot;乾&quot;</span><span class=p>,</span>
</span><span id=__span-5-102><a id=__codelineno-5-102 name=__codelineno-5-102 href=#__codelineno-5-102></a>            <span class=s2>&quot;gua_ci&quot;</span><span class=p>:</span> <span class=s2>&quot;飞龙在天，利见大人&quot;</span><span class=p>,</span>
</span><span id=__span-5-103><a id=__codelineno-5-103 name=__codelineno-5-103 href=#__codelineno-5-103></a>            <span class=s2>&quot;judgment&quot;</span><span class=p>:</span> <span class=s2>&quot;天行健，君子以自强不息&quot;</span>
</span><span id=__span-5-104><a id=__codelineno-5-104 name=__codelineno-5-104 href=#__codelineno-5-104></a>        <span class=p>}</span>
</span><span id=__span-5-105><a id=__codelineno-5-105 name=__codelineno-5-105 href=#__codelineno-5-105></a>    <span class=p>)</span>
</span><span id=__span-5-106><a id=__codelineno-5-106 name=__codelineno-5-106 href=#__codelineno-5-106></a>
</span><span id=__span-5-107><a id=__codelineno-5-107 name=__codelineno-5-107 href=#__codelineno-5-107></a>    <span class=nb>print</span><span class=p>(</span><span class=n>result</span><span class=p>)</span>
</span><span id=__span-5-108><a id=__codelineno-5-108 name=__codelineno-5-108 href=#__codelineno-5-108></a>
</span><span id=__span-5-109><a id=__codelineno-5-109 name=__codelineno-5-109 href=#__codelineno-5-109></a><span class=k>if</span> <span class=vm>__name__</span> <span class=o>==</span> <span class=s2>&quot;__main__&quot;</span><span class=p>:</span>
</span><span id=__span-5-110><a id=__codelineno-5-110 name=__codelineno-5-110 href=#__codelineno-5-110></a>    <span class=n>asyncio</span><span class=o>.</span><span class=n>run</span><span class=p>(</span><span class=n>main</span><span class=p>())</span>
</span></code></pre></div> <h2 id=_3>🔧 与你现有系统的集成方案<a class=headerlink href=#_3 title="Permanent link">&para;</a></h2> <h3 id=n8n>集成到N8N工作流<a class=headerlink href=#n8n title="Permanent link">&para;</a></h3> <div class="language-javascript highlight"><pre><span></span><code><span id=__span-6-1><a id=__codelineno-6-1 name=__codelineno-6-1 href=#__codelineno-6-1></a><span class=c1>// N8N中的GraphRAG节点代码</span>
</span><span id=__span-6-2><a id=__codelineno-6-2 name=__codelineno-6-2 href=#__codelineno-6-2></a><span class=kd>const</span><span class=w> </span><span class=p>{</span><span class=w> </span><span class=nx>GraphRAGQuery</span><span class=w> </span><span class=p>}</span><span class=w> </span><span class=o>=</span><span class=w> </span><span class=nx>require</span><span class=p>(</span><span class=s1>&#39;graphrag&#39;</span><span class=p>);</span>
</span><span id=__span-6-3><a id=__codelineno-6-3 name=__codelineno-6-3 href=#__codelineno-6-3></a>
</span><span id=__span-6-4><a id=__codelineno-6-4 name=__codelineno-6-4 href=#__codelineno-6-4></a><span class=kd>const</span><span class=w> </span><span class=nx>items</span><span class=w> </span><span class=o>=</span><span class=w> </span><span class=nx>$input</span><span class=p>.</span><span class=nx>all</span><span class=p>();</span>
</span><span id=__span-6-5><a id=__codelineno-6-5 name=__codelineno-6-5 href=#__codelineno-6-5></a><span class=kd>const</span><span class=w> </span><span class=nx>processedItems</span><span class=w> </span><span class=o>=</span><span class=w> </span><span class=p>[];</span>
</span><span id=__span-6-6><a id=__codelineno-6-6 name=__codelineno-6-6 href=#__codelineno-6-6></a>
</span><span id=__span-6-7><a id=__codelineno-6-7 name=__codelineno-6-7 href=#__codelineno-6-7></a><span class=k>for</span><span class=w> </span><span class=p>(</span><span class=kd>const</span><span class=w> </span><span class=nx>item</span><span class=w> </span><span class=k>of</span><span class=w> </span><span class=nx>items</span><span class=p>)</span><span class=w> </span><span class=p>{</span>
</span><span id=__span-6-8><a id=__codelineno-6-8 name=__codelineno-6-8 href=#__codelineno-6-8></a><span class=w>    </span><span class=k>try</span><span class=w> </span><span class=p>{</span>
</span><span id=__span-6-9><a id=__codelineno-6-9 name=__codelineno-6-9 href=#__codelineno-6-9></a><span class=w>        </span><span class=kd>const</span><span class=w> </span><span class=nx>data</span><span class=w> </span><span class=o>=</span><span class=w> </span><span class=nx>item</span><span class=p>.</span><span class=nx>json</span><span class=p>;</span>
</span><span id=__span-6-10><a id=__codelineno-6-10 name=__codelineno-6-10 href=#__codelineno-6-10></a>
</span><span id=__span-6-11><a id=__codelineno-6-11 name=__codelineno-6-11 href=#__codelineno-6-11></a><span class=w>        </span><span class=c1>// 使用GraphRAG进行增强分析</span>
</span><span id=__span-6-12><a id=__codelineno-6-12 name=__codelineno-6-12 href=#__codelineno-6-12></a><span class=w>        </span><span class=kd>const</span><span class=w> </span><span class=nx>graphragResult</span><span class=w> </span><span class=o>=</span><span class=w> </span><span class=k>await</span><span class=w> </span><span class=nx>GraphRAGQuery</span><span class=p>.</span><span class=nx>globalSearch</span><span class=p>({</span>
</span><span id=__span-6-13><a id=__codelineno-6-13 name=__codelineno-6-13 href=#__codelineno-6-13></a><span class=w>            </span><span class=nx>query</span><span class=o>:</span><span class=w> </span><span class=nx>data</span><span class=p>.</span><span class=nx>title</span><span class=w> </span><span class=o>+</span><span class=w> </span><span class=s2>&quot; &quot;</span><span class=w> </span><span class=o>+</span><span class=w> </span><span class=nx>data</span><span class=p>.</span><span class=nx>content</span><span class=p>,</span>
</span><span id=__span-6-14><a id=__codelineno-6-14 name=__codelineno-6-14 href=#__codelineno-6-14></a><span class=w>            </span><span class=nx>dataDir</span><span class=o>:</span><span class=w> </span><span class=s2>&quot;./graphrag_data&quot;</span><span class=p>,</span>
</span><span id=__span-6-15><a id=__codelineno-6-15 name=__codelineno-6-15 href=#__codelineno-6-15></a><span class=w>            </span><span class=nx>maxTokens</span><span class=o>:</span><span class=w> </span><span class=mf>8000</span>
</span><span id=__span-6-16><a id=__codelineno-6-16 name=__codelineno-6-16 href=#__codelineno-6-16></a><span class=w>        </span><span class=p>});</span>
</span><span id=__span-6-17><a id=__codelineno-6-17 name=__codelineno-6-17 href=#__codelineno-6-17></a>
</span><span id=__span-6-18><a id=__codelineno-6-18 name=__codelineno-6-18 href=#__codelineno-6-18></a><span class=w>        </span><span class=c1>// 结合原有数据</span>
</span><span id=__span-6-19><a id=__codelineno-6-19 name=__codelineno-6-19 href=#__codelineno-6-19></a><span class=w>        </span><span class=kd>const</span><span class=w> </span><span class=nx>enhancedDocument</span><span class=w> </span><span class=o>=</span><span class=w> </span><span class=p>{</span>
</span><span id=__span-6-20><a id=__codelineno-6-20 name=__codelineno-6-20 href=#__codelineno-6-20></a><span class=w>            </span><span class=nx>pageContent</span><span class=o>:</span><span class=w> </span><span class=nx>data</span><span class=p>.</span><span class=nx>title</span><span class=p>,</span>
</span><span id=__span-6-21><a id=__codelineno-6-21 name=__codelineno-6-21 href=#__codelineno-6-21></a><span class=w>            </span><span class=nx>metadata</span><span class=o>:</span><span class=w> </span><span class=p>{</span>
</span><span id=__span-6-22><a id=__codelineno-6-22 name=__codelineno-6-22 href=#__codelineno-6-22></a><span class=w>                </span><span class=nx>title</span><span class=o>:</span><span class=w> </span><span class=nx>data</span><span class=p>.</span><span class=nx>title</span><span class=p>,</span>
</span><span id=__span-6-23><a id=__codelineno-6-23 name=__codelineno-6-23 href=#__codelineno-6-23></a><span class=w>                </span><span class=nx>published_date</span><span class=o>:</span><span class=w> </span><span class=nx>data</span><span class=p>.</span><span class=nx>published_time</span><span class=p>,</span>
</span><span id=__span-6-24><a id=__codelineno-6-24 name=__codelineno-6-24 href=#__codelineno-6-24></a><span class=w>                </span><span class=nx>article_id</span><span class=o>:</span><span class=w> </span><span class=nx>data</span><span class=p>.</span><span class=nx>article_id</span><span class=p>,</span>
</span><span id=__span-6-25><a id=__codelineno-6-25 name=__codelineno-6-25 href=#__codelineno-6-25></a><span class=w>                </span><span class=nx>source</span><span class=o>:</span><span class=w> </span><span class=nx>data</span><span class=p>.</span><span class=nx>source</span><span class=p>,</span>
</span><span id=__span-6-26><a id=__codelineno-6-26 name=__codelineno-6-26 href=#__codelineno-6-26></a><span class=w>                </span><span class=nx>graphrag_analysis</span><span class=o>:</span><span class=w> </span><span class=nx>graphragResult</span><span class=p>.</span><span class=nx>response</span><span class=p>,</span>
</span><span id=__span-6-27><a id=__codelineno-6-27 name=__codelineno-6-27 href=#__codelineno-6-27></a><span class=w>                </span><span class=nx>entities</span><span class=o>:</span><span class=w> </span><span class=nx>graphragResult</span><span class=p>.</span><span class=nx>entities</span><span class=p>,</span>
</span><span id=__span-6-28><a id=__codelineno-6-28 name=__codelineno-6-28 href=#__codelineno-6-28></a><span class=w>                </span><span class=nx>relationships</span><span class=o>:</span><span class=w> </span><span class=nx>graphragResult</span><span class=p>.</span><span class=nx>relationships</span>
</span><span id=__span-6-29><a id=__codelineno-6-29 name=__codelineno-6-29 href=#__codelineno-6-29></a><span class=w>            </span><span class=p>}</span>
</span><span id=__span-6-30><a id=__codelineno-6-30 name=__codelineno-6-30 href=#__codelineno-6-30></a><span class=w>        </span><span class=p>};</span>
</span><span id=__span-6-31><a id=__codelineno-6-31 name=__codelineno-6-31 href=#__codelineno-6-31></a>
</span><span id=__span-6-32><a id=__codelineno-6-32 name=__codelineno-6-32 href=#__codelineno-6-32></a><span class=w>        </span><span class=nx>processedItems</span><span class=p>.</span><span class=nx>push</span><span class=p>(</span><span class=nx>enhancedDocument</span><span class=p>);</span>
</span><span id=__span-6-33><a id=__codelineno-6-33 name=__codelineno-6-33 href=#__codelineno-6-33></a>
</span><span id=__span-6-34><a id=__codelineno-6-34 name=__codelineno-6-34 href=#__codelineno-6-34></a><span class=w>    </span><span class=p>}</span><span class=w> </span><span class=k>catch</span><span class=w> </span><span class=p>(</span><span class=nx>error</span><span class=p>)</span><span class=w> </span><span class=p>{</span>
</span><span id=__span-6-35><a id=__codelineno-6-35 name=__codelineno-6-35 href=#__codelineno-6-35></a><span class=w>        </span><span class=nx>console</span><span class=p>.</span><span class=nx>log</span><span class=p>(</span><span class=sb>`GraphRAG处理错误: </span><span class=si>${</span><span class=nx>error</span><span class=p>.</span><span class=nx>message</span><span class=si>}</span><span class=sb>`</span><span class=p>);</span>
</span><span id=__span-6-36><a id=__codelineno-6-36 name=__codelineno-6-36 href=#__codelineno-6-36></a><span class=w>        </span><span class=c1>// 降级到原有处理方式</span>
</span><span id=__span-6-37><a id=__codelineno-6-37 name=__codelineno-6-37 href=#__codelineno-6-37></a><span class=w>        </span><span class=nx>processedItems</span><span class=p>.</span><span class=nx>push</span><span class=p>({</span>
</span><span id=__span-6-38><a id=__codelineno-6-38 name=__codelineno-6-38 href=#__codelineno-6-38></a><span class=w>            </span><span class=nx>pageContent</span><span class=o>:</span><span class=w> </span><span class=nx>item</span><span class=p>.</span><span class=nx>json</span><span class=p>.</span><span class=nx>title</span><span class=p>,</span>
</span><span id=__span-6-39><a id=__codelineno-6-39 name=__codelineno-6-39 href=#__codelineno-6-39></a><span class=w>            </span><span class=nx>metadata</span><span class=o>:</span><span class=w> </span><span class=p>{</span>
</span><span id=__span-6-40><a id=__codelineno-6-40 name=__codelineno-6-40 href=#__codelineno-6-40></a><span class=w>                </span><span class=nx>title</span><span class=o>:</span><span class=w> </span><span class=nx>item</span><span class=p>.</span><span class=nx>json</span><span class=p>.</span><span class=nx>title</span><span class=p>,</span>
</span><span id=__span-6-41><a id=__codelineno-6-41 name=__codelineno-6-41 href=#__codelineno-6-41></a><span class=w>                </span><span class=nx>published_date</span><span class=o>:</span><span class=w> </span><span class=nx>item</span><span class=p>.</span><span class=nx>json</span><span class=p>.</span><span class=nx>published_time</span><span class=p>,</span>
</span><span id=__span-6-42><a id=__codelineno-6-42 name=__codelineno-6-42 href=#__codelineno-6-42></a><span class=w>                </span><span class=nx>article_id</span><span class=o>:</span><span class=w> </span><span class=nx>item</span><span class=p>.</span><span class=nx>json</span><span class=p>.</span><span class=nx>article_id</span><span class=p>,</span>
</span><span id=__span-6-43><a id=__codelineno-6-43 name=__codelineno-6-43 href=#__codelineno-6-43></a><span class=w>                </span><span class=nx>source</span><span class=o>:</span><span class=w> </span><span class=nx>item</span><span class=p>.</span><span class=nx>json</span><span class=p>.</span><span class=nx>source</span>
</span><span id=__span-6-44><a id=__codelineno-6-44 name=__codelineno-6-44 href=#__codelineno-6-44></a><span class=w>            </span><span class=p>}</span>
</span><span id=__span-6-45><a id=__codelineno-6-45 name=__codelineno-6-45 href=#__codelineno-6-45></a><span class=w>        </span><span class=p>});</span>
</span><span id=__span-6-46><a id=__codelineno-6-46 name=__codelineno-6-46 href=#__codelineno-6-46></a><span class=w>    </span><span class=p>}</span>
</span><span id=__span-6-47><a id=__codelineno-6-47 name=__codelineno-6-47 href=#__codelineno-6-47></a><span class=p>}</span>
</span><span id=__span-6-48><a id=__codelineno-6-48 name=__codelineno-6-48 href=#__codelineno-6-48></a>
</span><span id=__span-6-49><a id=__codelineno-6-49 name=__codelineno-6-49 href=#__codelineno-6-49></a><span class=k>return</span><span class=w> </span><span class=nx>processedItems</span><span class=p>;</span>
</span></code></pre></div> <h2 id=_4>🎯 适合你项目的部署建议<a class=headerlink href=#_4 title="Permanent link">&para;</a></h2> <h3 id=1>阶段1: 试验部署<a class=headerlink href=#1 title="Permanent link">&para;</a></h3> <ol> <li>使用Docker快速部署</li> <li>用你的RSS数据测试</li> <li>对比传统RAG效果</li> </ol> <h3 id=2>阶段2: 集成部署<a class=headerlink href=#2 title="Permanent link">&para;</a></h3> <ol> <li>集成到现有N8N工作流</li> <li>与Milvus并行运行</li> <li>A/B测试效果</li> </ol> <h3 id=3_1>阶段3: 生产部署<a class=headerlink href=#3_1 title="Permanent link">&para;</a></h3> <ol> <li>优化性能和成本</li> <li>与稷下学宫深度集成</li> <li>结合太公心易智慧</li> </ol> <h2 id=_5>💡 成本和性能考虑<a class=headerlink href=#_5 title="Permanent link">&para;</a></h2> <ul> <li><strong>计算成本</strong>: 比传统RAG高2-3倍</li> <li><strong>构建时间</strong>: 初次构建较慢</li> <li><strong>查询速度</strong>: 复杂查询较慢，但结果更准确</li> <li><strong>存储需求</strong>: 需要额外存储知识图谱</li> </ul> <p>GraphRAG特别适合你的太公心易系统，因为它能理解复杂的关系和进行深度推理，这与易学的系统性思维很匹配！</p> <p>想要我帮你部署和集成吗？</p> <aside class=md-source-file> <span class=md-source-file__fact> <span class=md-icon title=最后更新> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M21 13.1c-.1 0-.3.1-.4.2l-1 1 2.1 2.1 1-1c.2-.2.2-.6 0-.8l-1.3-1.3c-.1-.1-.2-.2-.4-.2m-1.9 1.8-6.1 6V23h2.1l6.1-6.1zM12.5 7v5.2l4 2.4-1 1L11 13V7zM11 21.9c-5.1-.5-9-4.8-9-9.9C2 6.5 6.5 2 12 2c5.3 0 9.6 4.1 10 9.3-.3-.1-.6-.2-1-.2s-.7.1-1 .2C19.6 7.2 16.2 4 12 4c-4.4 0-8 3.6-8 8 0 4.1 3.1 7.5 7.1 7.9l-.1.2z"/></svg> </span> <span class="git-revision-date-localized-plugin git-revision-date-localized-plugin-datetime" title="2025年7月10日 04:18:21 UTC">2025年7月10日 04:18:21</span> </span> <span class=md-source-file__fact> <span class=md-icon title=创建日期> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M14.47 15.08 11 13V7h1.5v5.25l3.08 1.83c-.41.28-.79.62-1.11 1m-1.39 4.84c-.36.05-.71.08-1.08.08-4.42 0-8-3.58-8-8s3.58-8 8-8 8 3.58 8 8c0 .37-.03.72-.08 1.08.69.1 1.33.32 1.92.64.1-.56.16-1.13.16-1.72 0-5.5-4.5-10-10-10S2 6.5 2 12s4.47 10 10 10c.59 0 1.16-.06 1.72-.16-.32-.59-.54-1.23-.64-1.92M18 15v3h-3v2h3v3h2v-3h3v-2h-3v-3z"/></svg> </span> <span class="git-revision-date-localized-plugin git-revision-date-localized-plugin-datetime" title="2025年7月10日 04:18:21 UTC">2025年7月10日 04:18:21</span> </span> </aside> <form class=md-feedback name=feedback hidden> <fieldset> <legend class=md-feedback__title> 这个页面有帮助吗？ </legend> <div class=md-feedback__inner> <div class=md-feedback__list> <button class="md-feedback__icon md-icon" type=submit title=有帮助 data-md-value=1> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M20 12a8 8 0 0 0-8-8 8 8 0 0 0-8 8 8 8 0 0 0 8 8 8 8 0 0 0 8-8m2 0a10 10 0 0 1-10 10A10 10 0 0 1 2 12 10 10 0 0 1 12 2a10 10 0 0 1 10 10M10 9.5c0 .8-.7 1.5-1.5 1.5S7 10.3 7 9.5 7.7 8 8.5 8s1.5.7 1.5 1.5m7 0c0 .8-.7 1.5-1.5 1.5S14 10.3 14 9.5 14.7 8 15.5 8s1.5.7 1.5 1.5m-5 7.73c-1.75 0-3.29-.73-4.19-1.81L9.23 14c.45.72 1.52 1.23 2.77 1.23s2.32-.51 2.77-1.23l1.42 1.42c-.9 1.08-2.44 1.81-4.19 1.81"/></svg> </button> <button class="md-feedback__icon md-icon" type=submit title=需要改进 data-md-value=0> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M20 12a8 8 0 0 0-8-8 8 8 0 0 0-8 8 8 8 0 0 0 8 8 8 8 0 0 0 8-8m2 0a10 10 0 0 1-10 10A10 10 0 0 1 2 12 10 10 0 0 1 12 2a10 10 0 0 1 10 10m-6.5-4c.8 0 1.5.7 1.5 1.5s-.7 1.5-1.5 1.5-1.5-.7-1.5-1.5.7-1.5 1.5-1.5M10 9.5c0 .8-.7 1.5-1.5 1.5S7 10.3 7 9.5 7.7 8 8.5 8s1.5.7 1.5 1.5m2 4.5c1.75 0 3.29.72 4.19 1.81l-1.42 1.42C14.32 16.5 13.25 16 12 16s-2.32.5-2.77 1.23l-1.42-1.42C8.71 14.72 10.25 14 12 14"/></svg> </button> </div> <div class=md-feedback__note> <div data-md-value=1 hidden> 感谢您的反馈！ </div> <div data-md-value=0 hidden> 感谢您的反馈！请通过GitHub Issues告诉我们如何改进。 </div> </div> </div> </fieldset> </form> </article> </div> <script>var tabs=__md_get("__tabs");if(Array.isArray(tabs))e:for(var set of document.querySelectorAll(".tabbed-set")){var labels=set.querySelector(".tabbed-labels");for(var tab of tabs)for(var label of labels.getElementsByTagName("label"))if(label.innerText.trim()===tab){var input=document.getElementById(label.htmlFor);input.checked=!0;continue e}}</script> <script>var target=document.getElementById(location.hash.slice(1));target&&target.name&&(target.checked=target.name.startsWith("__tabbed_"))</script> </div> <button type=button class="md-top md-icon" data-md-component=top hidden> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M13 20h-2V8l-5.5 5.5-1.42-1.42L12 4.16l7.92 7.92-1.42 1.42L13 8z"/></svg> 回到页面顶部 </button> </main> <footer class=md-footer> <div class="md-footer-meta md-typeset"> <div class="md-footer-meta__inner md-grid"> <div class=md-copyright> <div class=md-copyright__highlight> Copyright &copy; 2025 炼妖壶项目 - MIT License </div> Made with <a href=https://squidfunk.github.io/mkdocs-material/ target=_blank rel=noopener> Material for MkDocs </a> </div> <div class=md-social> <a href=https://github.com/your-username/jixia-academy target=_blank rel=noopener title=GitHub仓库 class=md-social__link> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 496 512"><!-- Font Awesome Free 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license/free (Icons: CC BY 4.0, Fonts: SIL OFL 1.1, Code: MIT License) Copyright 2024 Fonticons, Inc.--><path d="M165.9 397.4c0 2-2.3 3.6-5.2 3.6-3.3.3-5.6-1.3-5.6-3.6 0-2 2.3-3.6 5.2-3.6 3-.3 5.6 1.3 5.6 3.6m-31.1-4.5c-.7 2 1.3 4.3 4.3 4.9 2.6 1 5.6 0 6.2-2s-1.3-4.3-4.3-5.2c-2.6-.7-5.5.3-6.2 2.3m44.2-1.7c-2.9.7-4.9 2.6-4.6 4.9.3 2 2.9 3.3 5.9 2.6 2.9-.7 4.9-2.6 4.6-4.6-.3-1.9-3-3.2-5.9-2.9M244.8 8C106.1 8 0 113.3 0 252c0 110.9 69.8 205.8 169.5 239.2 12.8 2.3 17.3-5.6 17.3-12.1 0-6.2-.3-40.4-.3-61.4 0 0-70 15-84.7-29.8 0 0-11.4-29.1-27.8-36.6 0 0-22.9-15.7 1.6-15.4 0 0 24.9 2 38.6 25.8 21.9 38.6 58.6 27.5 72.9 20.9 2.3-16 8.8-27.1 16-33.7-55.9-6.2-112.3-14.3-112.3-110.5 0-27.5 7.6-41.3 23.6-58.9-2.6-6.5-11.1-33.3 2.6-67.9 20.9-6.5 69 27 69 27 20-5.6 41.5-8.5 62.8-8.5s42.8 2.9 62.8 8.5c0 0 48.1-33.6 69-27 13.7 34.7 5.2 61.4 2.6 67.9 16 17.7 25.8 31.5 25.8 58.9 0 96.5-58.9 104.2-114.8 110.5 9.2 7.9 17 22.9 17 46.4 0 33.7-.3 75.4-.3 83.6 0 6.5 4.6 14.4 17.3 12.1C428.2 457.8 496 362.9 496 252 496 113.3 383.5 8 244.8 8M97.2 352.9c-1.3 1-1 3.3.7 5.2 1.6 1.6 3.9 2.3 5.2 1 1.3-1 1-3.3-.7-5.2-1.6-1.6-3.9-2.3-5.2-1m-10.8-8.1c-.7 1.3.3 2.9 2.3 3.9 1.6 1 3.6.7 4.3-.7.7-1.3-.3-2.9-2.3-3.9-2-.6-3.6-.3-4.3.7m32.4 35.6c-1.6 1.3-1 4.3 1.3 6.2 2.3 2.3 5.2 2.6 6.5 1 1.3-1.3.7-4.3-1.3-6.2-2.2-2.3-5.2-2.6-6.5-1m-11.4-14.7c-1.6 1-1.6 3.6 0 5.9s4.3 3.3 5.6 2.3c1.6-1.3 1.6-3.9 0-6.2-1.4-2.3-4-3.3-5.6-2"/></svg> </a> <a href=https://your-mastodon-instance.com/@taishang_laojun target=_blank rel="noopener me" title=太上老君 class=md-social__link> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 448 512"><!-- Font Awesome Free 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license/free (Icons: CC BY 4.0, Fonts: SIL OFL 1.1, Code: MIT License) Copyright 2024 Fonticons, Inc.--><path d="M433 179.11c0-97.2-63.71-125.7-63.71-125.7-62.52-28.7-228.56-28.4-290.48 0 0 0-63.72 28.5-63.72 125.7 0 115.7-6.6 259.4 105.63 289.1 40.51 10.7 75.32 13 103.33 11.4 50.81-2.8 79.32-18.1 79.32-18.1l-1.7-36.9s-36.31 11.4-77.12 10.1c-40.41-1.4-83-4.4-89.63-54a102.5 102.5 0 0 1-.9-13.9c85.63 20.9 158.65 9.1 178.75 6.7 56.12-6.7 105-41.3 111.23-72.9 9.8-49.8 9-121.5 9-121.5m-75.12 125.2h-46.63v-114.2c0-49.7-64-51.6-64 6.9v62.5h-46.33V197c0-58.5-64-56.6-64-6.9v114.2H90.19c0-122.1-5.2-147.9 18.41-175 25.9-28.9 79.82-30.8 103.83 6.1l11.6 19.5 11.6-19.5c24.11-37.1 78.12-34.8 103.83-6.1 23.71 27.3 18.4 53 18.4 175z"/></svg> </a> <a href=https://twitter.com/jixia_academy target=_blank rel=noopener title=Twitter class=md-social__link> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 512 512"><!-- Font Awesome Free 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license/free (Icons: CC BY 4.0, Fonts: SIL OFL 1.1, Code: MIT License) Copyright 2024 Fonticons, Inc.--><path d="M459.37 151.716c.325 4.548.325 9.097.325 13.645 0 138.72-105.583 298.558-298.558 298.558-59.452 0-114.68-17.219-161.137-47.106 8.447.974 16.568 1.299 25.34 1.299 49.055 0 94.213-16.568 130.274-44.832-46.132-.975-84.792-31.188-98.112-72.772 6.498.974 12.995 1.624 19.818 1.624 9.421 0 18.843-1.3 27.614-3.573-48.081-9.747-84.143-51.98-84.143-102.985v-1.299c13.969 7.797 30.214 12.67 47.431 13.319-28.264-18.843-46.781-51.005-46.781-87.391 0-19.492 5.197-37.36 14.294-52.954 51.655 63.675 129.3 105.258 216.365 109.807-1.624-7.797-2.599-15.918-2.599-24.04 0-57.828 46.782-104.934 104.934-104.934 30.213 0 57.502 12.67 76.67 33.137 23.715-4.548 46.456-13.32 66.599-25.34-7.798 24.366-24.366 44.833-46.132 57.827 21.117-2.273 41.584-8.122 60.426-16.243-14.292 20.791-32.161 39.308-52.628 54.253"/></svg> </a> <a href=mailto:<EMAIL> target=_blank rel=noopener title=联系我们 class=md-social__link> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 512 512"><!-- Font Awesome Free 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license/free (Icons: CC BY 4.0, Fonts: SIL OFL 1.1, Code: MIT License) Copyright 2024 Fonticons, Inc.--><path d="M498.1 5.6c10.1 7 15.4 19.1 13.5 31.2l-64 416c-1.5 9.7-7.4 18.2-16 23s-18.9 5.4-28 1.6L284 427.7l-68.5 74.1c-8.9 9.7-22.9 12.9-35.2 8.1S160 493.2 160 480v-83.6c0-4 1.5-7.8 4.2-10.8l167.6-182.8c5.8-6.3 5.6-16-.4-22s-15.7-6.4-22-.7L106 360.8l-88.3-44.2C7.1 311.3.3 300.7 0 288.9s5.9-22.8 16.1-28.7l448-256c10.7-6.1 23.9-5.5 34 1.4"/></svg> </a> </div> </div> </div> </footer> </div> <div class=md-dialog data-md-component=dialog> <div class="md-dialog__inner md-typeset"></div> </div> <script id=__config type=application/json>{"base": "../..", "features": ["navigation.instant", "navigation.tracking", "navigation.tabs", "navigation.tabs.sticky", "navigation.sections", "navigation.expand", "navigation.indexes", "navigation.top", "search.highlight", "search.share", "search.suggest", "content.code.annotate", "content.code.copy", "content.tabs.link"], "search": "../../assets/javascripts/workers/search.d50fe291.min.js", "tags": null, "translations": {"clipboard.copied": "\u5df2\u590d\u5236", "clipboard.copy": "\u590d\u5236", "search.result.more.one": "\u5728\u8be5\u9875\u4e0a\u8fd8\u6709 1 \u4e2a\u7b26\u5408\u6761\u4ef6\u7684\u7ed3\u679c", "search.result.more.other": "\u5728\u8be5\u9875\u4e0a\u8fd8\u6709 # \u4e2a\u7b26\u5408\u6761\u4ef6\u7684\u7ed3\u679c", "search.result.none": "\u6ca1\u6709\u627e\u5230\u7b26\u5408\u6761\u4ef6\u7684\u7ed3\u679c", "search.result.one": "\u627e\u5230 1 \u4e2a\u7b26\u5408\u6761\u4ef6\u7684\u7ed3\u679c", "search.result.other": "# \u4e2a\u7b26\u5408\u6761\u4ef6\u7684\u7ed3\u679c", "search.result.placeholder": "\u952e\u5165\u4ee5\u5f00\u59cb\u641c\u7d22", "search.result.term.missing": "\u7f3a\u5c11", "select.version": "\u9009\u62e9\u5f53\u524d\u7248\u672c"}, "version": {"default": "latest", "provider": "mike"}}</script> <script src=../../assets/javascripts/bundle.56ea9cef.min.js></script> <script src=../../assets/javascripts/mathjax.js></script> <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script> <script src=https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js></script> </body> </html>