<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🧠 MongoDB GraphRAG 智能问答系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        .content {
            padding: 30px;
        }
        
        .section {
            margin-bottom: 40px;
            padding: 25px;
            border-radius: 15px;
            background: #f8f9fa;
            border-left: 5px solid #667eea;
        }
        
        .section h2 {
            color: #333;
            margin-bottom: 20px;
            font-size: 1.5em;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        
        .stat-card:hover {
            transform: translateY(-5px);
        }
        
        .stat-number {
            font-size: 2.5em;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 5px;
        }
        
        .stat-label {
            color: #666;
            font-size: 0.9em;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #333;
        }
        
        .form-control {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s ease;
        }
        
        .form-control:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
        
        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-right: 10px;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }
        
        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }
        
        .btn-secondary {
            background: #6c757d;
        }
        
        .loading {
            display: none;
            text-align: center;
            padding: 20px;
            color: #667eea;
        }
        
        .loading::after {
            content: '';
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-left: 10px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .result {
            margin-top: 20px;
            padding: 20px;
            border-radius: 10px;
            border-left: 4px solid #667eea;
            background: white;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .result.error {
            border-left-color: #dc3545;
            background: #f8d7da;
            color: #721c24;
        }
        
        .result.success {
            border-left-color: #28a745;
            background: #d4edda;
            color: #155724;
        }
        
        .sync-controls {
            display: grid;
            grid-template-columns: 1fr 1fr auto;
            gap: 15px;
            align-items: end;
        }
        
        .progress-bar {
            width: 100%;
            height: 8px;
            background: #e1e5e9;
            border-radius: 4px;
            overflow: hidden;
            margin-top: 10px;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            width: 0%;
            transition: width 0.3s ease;
        }
        
        .mongodb-info {
            background: white;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        
        .mongodb-info h4 {
            color: #333;
            margin-bottom: 10px;
        }
        
        .mongodb-info p {
            color: #666;
            margin: 5px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧠 MongoDB GraphRAG</h1>
            <p>智能知识图谱问答系统 - 连接MongoDB Atlas，构建智能知识网络</p>
        </div>
        
        <div class="content">
            <!-- MongoDB连接状态 -->
            <div class="section">
                <h2>📊 MongoDB连接状态</h2>
                <div id="mongodb-info" class="mongodb-info">
                    <div class="loading">正在连接MongoDB...</div>
                </div>
                <button class="btn btn-secondary" onclick="loadMongoDBStats()">刷新连接状态</button>
            </div>
            
            <!-- 系统统计 -->
            <div class="section">
                <h2>📈 知识图谱统计</h2>
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-number" id="doc-count">-</div>
                        <div class="stat-label">文档数量</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="entity-count">-</div>
                        <div class="stat-label">实体数量</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="relation-count">-</div>
                        <div class="stat-label">关系数量</div>
                    </div>
                </div>
                <button class="btn btn-secondary" onclick="loadLocalStats()">刷新统计</button>
            </div>
            
            <!-- 数据同步 -->
            <div class="section">
                <h2>🔄 数据同步</h2>
                <div class="sync-controls">
                    <div class="form-group">
                        <label for="sync-limit">同步数量</label>
                        <input type="number" id="sync-limit" class="form-control" value="50" min="1" max="1000">
                    </div>
                    <div class="form-group">
                        <label for="sync-skip">跳过数量</label>
                        <input type="number" id="sync-skip" class="form-control" value="0" min="0">
                    </div>
                    <button class="btn" onclick="syncData()">开始同步</button>
                </div>
                <div class="loading" id="sync-loading">正在同步数据...</div>
                <div id="sync-result"></div>
            </div>
            
            <!-- 智能问答 -->
            <div class="section">
                <h2>❓ 智能问答</h2>
                <div class="form-group">
                    <label for="question">请输入您的问题</label>
                    <input type="text" id="question" class="form-control" 
                           placeholder="例如：什么是人工智能？特斯拉的最新动态？">
                </div>
                <button class="btn" onclick="askQuestion()">提问</button>
                <div class="loading" id="query-loading">AI正在思考中...</div>
                <div id="query-result"></div>
            </div>
        </div>
    </div>

    <script>
        // 页面加载时初始化
        window.onload = function() {
            loadMongoDBStats();
            loadLocalStats();
        };

        // 加载MongoDB统计信息
        function loadMongoDBStats() {
            const infoDiv = document.getElementById('mongodb-info');
            infoDiv.innerHTML = '<div class="loading">正在连接MongoDB...</div>';
            
            fetch('/api/mongodb/stats')
                .then(response => response.json())
                .then(data => {
                    if (data.error) {
                        infoDiv.innerHTML = `<p style="color: #dc3545;">❌ 连接失败: ${data.error}</p>`;
                    } else {
                        infoDiv.innerHTML = `
                            <h4>✅ MongoDB连接成功</h4>
                            <p><strong>数据库:</strong> ${data.database_name}</p>
                            <p><strong>集合:</strong> ${data.collection_name}</p>
                            <p><strong>总文档数:</strong> ${data.total_documents.toLocaleString()}</p>
                        `;
                    }
                })
                .catch(error => {
                    infoDiv.innerHTML = `<p style="color: #dc3545;">❌ 网络错误: ${error.message}</p>`;
                });
        }

        // 加载本地统计信息
        function loadLocalStats() {
            fetch('/api/stats')
                .then(response => response.json())
                .then(data => {
                    if (data.error) {
                        console.error('Error:', data.error);
                        return;
                    }
                    document.getElementById('doc-count').textContent = data.documents || 0;
                    document.getElementById('entity-count').textContent = data.entities || 0;
                    document.getElementById('relation-count').textContent = data.relations || 0;
                })
                .catch(error => {
                    console.error('Error:', error);
                });
        }

        // 同步数据
        function syncData() {
            const limit = parseInt(document.getElementById('sync-limit').value);
            const skip = parseInt(document.getElementById('sync-skip').value);
            const loadingDiv = document.getElementById('sync-loading');
            const resultDiv = document.getElementById('sync-result');
            
            loadingDiv.style.display = 'block';
            resultDiv.innerHTML = '';
            
            fetch('/api/sync', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    limit: limit,
                    skip: skip
                })
            })
            .then(response => response.json())
            .then(data => {
                loadingDiv.style.display = 'none';
                
                if (data.error) {
                    showResult(resultDiv, `❌ 同步失败: ${data.error}`, 'error');
                } else {
                    showResult(resultDiv, 
                        `✅ 同步完成！<br>
                        📄 处理文档: ${data.processed}<br>
                        ❌ 错误数量: ${data.errors}<br>
                        📊 请求数量: ${data.total_requested}`, 'success');
                    loadLocalStats(); // 刷新统计
                }
            })
            .catch(error => {
                loadingDiv.style.display = 'none';
                showResult(resultDiv, `❌ 网络错误: ${error.message}`, 'error');
            });
        }

        // 智能问答
        function askQuestion() {
            const question = document.getElementById('question').value.trim();
            const loadingDiv = document.getElementById('query-loading');
            const resultDiv = document.getElementById('query-result');
            
            if (!question) {
                showResult(resultDiv, '❌ 请输入问题', 'error');
                return;
            }
            
            loadingDiv.style.display = 'block';
            resultDiv.innerHTML = '';
            
            fetch('/api/query', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    question: question
                })
            })
            .then(response => response.json())
            .then(data => {
                loadingDiv.style.display = 'none';
                
                if (data.error) {
                    showResult(resultDiv, `❌ 查询失败: ${data.error}`, 'error');
                } else {
                    showResult(resultDiv, `<strong>🤖 AI回答:</strong><br><br>${data.answer}`, 'result');
                }
            })
            .catch(error => {
                loadingDiv.style.display = 'none';
                showResult(resultDiv, `❌ 网络错误: ${error.message}`, 'error');
            });
        }

        // 显示结果
        function showResult(element, message, type) {
            element.innerHTML = message;
            element.className = 'result ' + type;
        }

        // 回车键提问
        document.getElementById('question').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                askQuestion();
            }
        });
    </script>
</body>
</html>
