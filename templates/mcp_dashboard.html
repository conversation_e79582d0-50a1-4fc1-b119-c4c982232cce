<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MCP服务管理器</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .header p {
            opacity: 0.8;
            font-size: 1.1em;
        }
        
        .controls {
            padding: 20px 30px;
            background: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
        }
        
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }
        
        .btn-primary {
            background: #007bff;
            color: white;
        }
        
        .btn-success {
            background: #28a745;
            color: white;
        }
        
        .btn-danger {
            background: #dc3545;
            color: white;
        }
        
        .btn-warning {
            background: #ffc107;
            color: #212529;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }
        
        .services-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 20px;
            padding: 30px;
        }
        
        .service-card {
            border: 1px solid #dee2e6;
            border-radius: 8px;
            overflow: hidden;
            transition: all 0.3s ease;
        }
        
        .service-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }
        
        .service-header {
            padding: 20px;
            background: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
        }
        
        .service-name {
            font-size: 1.3em;
            font-weight: 600;
            margin-bottom: 5px;
        }
        
        .service-type {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.8em;
            font-weight: 500;
            text-transform: uppercase;
        }
        
        .type-stdio {
            background: #e3f2fd;
            color: #1976d2;
        }
        
        .type-http {
            background: #e8f5e8;
            color: #2e7d32;
        }
        
        .type-sse {
            background: #fff3e0;
            color: #f57c00;
        }
        
        .service-body {
            padding: 20px;
        }
        
        .service-description {
            color: #6c757d;
            margin-bottom: 15px;
        }
        
        .service-status {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 15px;
        }
        
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
        }
        
        .status-running {
            background: #28a745;
            animation: pulse 2s infinite;
        }
        
        .status-stopped {
            background: #dc3545;
        }
        
        .status-error {
            background: #ffc107;
        }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        
        .service-actions {
            display: flex;
            gap: 10px;
        }
        
        .service-actions .btn {
            flex: 1;
            text-align: center;
            font-size: 0.9em;
            padding: 8px 12px;
        }
        
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            padding: 30px;
            background: #f8f9fa;
        }
        
        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .stat-number {
            font-size: 2.5em;
            font-weight: 700;
            color: #2c3e50;
        }
        
        .stat-label {
            color: #6c757d;
            margin-top: 5px;
        }
        
        .loading {
            text-align: center;
            padding: 40px;
            color: #6c757d;
        }
        
        .error {
            background: #f8d7da;
            color: #721c24;
            padding: 15px;
            margin: 20px;
            border-radius: 6px;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧙‍♂️ MCP服务管理器</h1>
            <p>统一管理stdio、SSE、HTTP类型的MCP服务</p>
        </div>
        
        <div class="controls">
            <button class="btn btn-success" onclick="startAllServices()">🚀 启动所有服务</button>
            <button class="btn btn-danger" onclick="stopAllServices()">🛑 停止所有服务</button>
            <button class="btn btn-primary" onclick="refreshServices()">🔄 刷新状态</button>
            <button class="btn btn-warning" onclick="showLogs()">📋 查看日志</button>
        </div>
        
        <div class="stats" id="stats">
            <div class="stat-card">
                <div class="stat-number" id="total-services">-</div>
                <div class="stat-label">总服务数</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="running-services">-</div>
                <div class="stat-label">运行中</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="stopped-services">-</div>
                <div class="stat-label">已停止</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="error-services">-</div>
                <div class="stat-label">错误</div>
            </div>
        </div>
        
        <div class="services-grid" id="services-container">
            <div class="loading">
                <p>⏳ 正在加载服务状态...</p>
            </div>
        </div>
    </div>

    <script>
        const API_BASE = '';
        
        // 页面加载时获取服务列表
        document.addEventListener('DOMContentLoaded', function() {
            refreshServices();
            // 每30秒自动刷新
            setInterval(refreshServices, 30000);
        });
        
        async function refreshServices() {
            try {
                const response = await fetch(`${API_BASE}/services`);
                const data = await response.json();
                displayServices(data.services);
                updateStats(data.services);
            } catch (error) {
                showError('获取服务列表失败: ' + error.message);
            }
        }
        
        function displayServices(services) {
            const container = document.getElementById('services-container');
            
            if (services.length === 0) {
                container.innerHTML = '<div class="loading"><p>📭 没有配置的服务</p></div>';
                return;
            }
            
            container.innerHTML = services.map(service => `
                <div class="service-card">
                    <div class="service-header">
                        <div class="service-name">${service.name}</div>
                        <span class="service-type type-${service.type}">${service.type}</span>
                    </div>
                    <div class="service-body">
                        <div class="service-description">
                            ${getServiceDescription(service.name)}
                        </div>
                        <div class="service-status">
                            <div class="status-indicator status-${service.status}"></div>
                            <span>状态: ${getStatusText(service.status)}</span>
                            <span style="margin-left: auto; color: #6c757d;">
                                健康: ${getHealthText(service.health)}
                            </span>
                        </div>
                        <div class="service-actions">
                            <button class="btn btn-success" 
                                    onclick="startService('${service.name}')"
                                    ${service.status === 'running' ? 'disabled' : ''}>
                                启动
                            </button>
                            <button class="btn btn-danger" 
                                    onclick="stopService('${service.name}')"
                                    ${service.status === 'stopped' ? 'disabled' : ''}>
                                停止
                            </button>
                            <button class="btn btn-primary" 
                                    onclick="showServiceDetails('${service.name}')">
                                详情
                            </button>
                        </div>
                    </div>
                </div>
            `).join('');
        }
        
        function updateStats(services) {
            const total = services.length;
            const running = services.filter(s => s.status === 'running').length;
            const stopped = services.filter(s => s.status === 'stopped').length;
            const error = services.filter(s => s.status === 'error').length;
            
            document.getElementById('total-services').textContent = total;
            document.getElementById('running-services').textContent = running;
            document.getElementById('stopped-services').textContent = stopped;
            document.getElementById('error-services').textContent = error;
        }
        
        function getServiceDescription(name) {
            const descriptions = {
                'yahoo-finance': '📈 Yahoo Finance数据获取工具',
                'cauldron-financial': '🧪 炼妖壶金融分析工具集',
                'tusita-palace': '🏛️ 兜率宫N8N工作流集成',
                'heroku-inference': '☁️ Heroku托管推理服务',
                'local-conductor': '🎼 本地指挥系统',
                'zilliz-truth': '🗄️ Zilliz向量数据库服务'
            };
            return descriptions[name] || '🔧 MCP服务';
        }
        
        function getStatusText(status) {
            const statusMap = {
                'running': '运行中',
                'stopped': '已停止',
                'error': '错误',
                'starting': '启动中',
                'stopping': '停止中'
            };
            return statusMap[status] || status;
        }
        
        function getHealthText(health) {
            const healthMap = {
                'healthy': '✅ 健康',
                'unhealthy': '❌ 不健康',
                'unknown': '❓ 未知'
            };
            return healthMap[health] || health;
        }
        
        async function startService(serviceName) {
            try {
                const response = await fetch(`${API_BASE}/services/${serviceName}/start`, {
                    method: 'POST'
                });
                const result = await response.json();
                
                if (response.ok) {
                    showSuccess(`服务 ${serviceName} 启动成功`);
                    setTimeout(refreshServices, 1000);
                } else {
                    showError(`启动失败: ${result.detail}`);
                }
            } catch (error) {
                showError('启动服务失败: ' + error.message);
            }
        }
        
        async function stopService(serviceName) {
            try {
                const response = await fetch(`${API_BASE}/services/${serviceName}/stop`, {
                    method: 'POST'
                });
                const result = await response.json();
                
                if (response.ok) {
                    showSuccess(`服务 ${serviceName} 停止成功`);
                    setTimeout(refreshServices, 1000);
                } else {
                    showError(`停止失败: ${result.detail}`);
                }
            } catch (error) {
                showError('停止服务失败: ' + error.message);
            }
        }
        
        async function startAllServices() {
            try {
                const response = await fetch(`${API_BASE}/services/start-all`, {
                    method: 'POST'
                });
                const result = await response.json();
                
                if (response.ok) {
                    showSuccess('批量启动完成');
                    setTimeout(refreshServices, 2000);
                } else {
                    showError('批量启动失败');
                }
            } catch (error) {
                showError('批量启动失败: ' + error.message);
            }
        }
        
        async function stopAllServices() {
            if (!confirm('确定要停止所有服务吗？')) {
                return;
            }
            
            try {
                const response = await fetch(`${API_BASE}/services/stop-all`, {
                    method: 'POST'
                });
                const result = await response.json();
                
                if (response.ok) {
                    showSuccess('批量停止完成');
                    setTimeout(refreshServices, 2000);
                } else {
                    showError('批量停止失败');
                }
            } catch (error) {
                showError('批量停止失败: ' + error.message);
            }
        }
        
        function showServiceDetails(serviceName) {
            alert(`服务详情: ${serviceName}\n\n这里可以显示更详细的服务信息、日志等`);
        }
        
        function showLogs() {
            window.open('/logs', '_blank');
        }
        
        function showSuccess(message) {
            // 简单的成功提示
            const div = document.createElement('div');
            div.style.cssText = `
                position: fixed; top: 20px; right: 20px; z-index: 1000;
                background: #d4edda; color: #155724; padding: 15px 20px;
                border: 1px solid #c3e6cb; border-radius: 6px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            `;
            div.textContent = '✅ ' + message;
            document.body.appendChild(div);
            setTimeout(() => div.remove(), 3000);
        }
        
        function showError(message) {
            // 简单的错误提示
            const div = document.createElement('div');
            div.style.cssText = `
                position: fixed; top: 20px; right: 20px; z-index: 1000;
                background: #f8d7da; color: #721c24; padding: 15px 20px;
                border: 1px solid #f5c6cb; border-radius: 6px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            `;
            div.textContent = '❌ ' + message;
            document.body.appendChild(div);
            setTimeout(() => div.remove(), 5000);
        }
    </script>
</body>
</html>