<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎯 Yahoo Finance API矩阵实验室</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            color: #333;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin: 0;
            background: linear-gradient(45deg, #FF6B6B, #4ECDC4);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .subtitle {
            color: #666;
            font-style: italic;
            margin-top: 10px;
        }
        
        .control-panel {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin-bottom: 30px;
        }
        
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-weight: bold;
            transition: all 0.3s ease;
            font-size: 14px;
        }
        
        .btn-primary {
            background: linear-gradient(45deg, #FF6B6B, #4ECDC4);
            color: white;
        }
        
        .btn-secondary {
            background: #f8f9fa;
            color: #333;
            border: 2px solid #dee2e6;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }
        
        .api-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }
        
        .api-card {
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        
        .api-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            opacity: 0.1;
            z-index: 0;
        }
        
        .api-card-content {
            position: relative;
            z-index: 1;
        }
        
        .api-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
        }
        
        .api-icon {
            font-size: 2em;
            margin-bottom: 10px;
        }
        
        .api-name {
            font-weight: bold;
            margin-bottom: 15px;
            font-size: 1.1em;
        }
        
        .api-stats {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            margin-bottom: 15px;
        }
        
        .stat-item {
            background: rgba(255, 255, 255, 0.7);
            padding: 8px;
            border-radius: 8px;
            font-size: 0.9em;
        }
        
        .progress-bar {
            width: 100%;
            height: 8px;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 4px;
            overflow: hidden;
            margin-top: 5px;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #4CAF50, #8BC34A);
            transition: width 0.5s ease;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-active { background: #4CAF50; }
        .status-standby { background: #FFC107; }
        .status-cooling { background: #FF9800; }
        .status-error { background: #F44336; }
        
        .section {
            margin-bottom: 40px;
        }
        
        .section-title {
            font-size: 1.5em;
            margin-bottom: 20px;
            color: #333;
            border-bottom: 2px solid #4ECDC4;
            padding-bottom: 10px;
        }
        
        .synergy-matrix {
            display: grid;
            grid-template-columns: 150px repeat(6, 1fr);
            gap: 5px;
            margin-bottom: 20px;
        }
        
        .matrix-cell {
            padding: 10px;
            text-align: center;
            border-radius: 5px;
            font-size: 1.2em;
        }
        
        .matrix-header {
            background: #f8f9fa;
            font-weight: bold;
            font-size: 0.9em;
        }
        
        .matrix-label {
            background: #f8f9fa;
            font-weight: bold;
            text-align: left;
            font-size: 0.8em;
        }
        
        .logs-container {
            background: #1e1e1e;
            color: #00ff00;
            padding: 20px;
            border-radius: 10px;
            font-family: 'Courier New', monospace;
            height: 200px;
            overflow-y: auto;
            font-size: 0.9em;
        }
        
        .log-entry {
            margin-bottom: 5px;
            opacity: 0;
            animation: fadeIn 0.5s ease forwards;
        }
        
        @keyframes fadeIn {
            to { opacity: 1; }
        }
        
        .legend {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-top: 10px;
            font-size: 0.9em;
        }
        
        .legend-item {
            display: flex;
            align-items: center;
            gap: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎯 Yahoo Finance API矩阵实验室</h1>
            <p class="subtitle">观察6个API的化学反应和智能轮换策略</p>
        </div>
        
        <div class="control-panel">
            <button class="btn btn-primary" onclick="startMatrix()">🚀 启动矩阵</button>
            <button class="btn btn-secondary" onclick="pauseMatrix()">⏸️ 暂停矩阵</button>
            <button class="btn btn-secondary" onclick="resetMatrix()">🔄 重置状态</button>
        </div>
        
        <div class="section">
            <h2 class="section-title">🔬 API状态矩阵</h2>
            <div class="api-grid" id="apiGrid">
                <!-- API卡片将通过JavaScript动态生成 -->
            </div>
        </div>
        
        <div class="section">
            <h2 class="section-title">⚗️ API化学反应图</h2>
            <div class="synergy-matrix" id="synergyMatrix">
                <!-- 协同矩阵将通过JavaScript动态生成 -->
            </div>
            <div class="legend">
                <div class="legend-item">🟢 高协同</div>
                <div class="legend-item">🟡 中协同</div>
                <div class="legend-item">🔴 低协同</div>
                <div class="legend-item">🔵 自身</div>
            </div>
        </div>
        
        <div class="section">
            <h2 class="section-title">📝 实时操作日志</h2>
            <div class="logs-container" id="logsContainer">
                <!-- 日志将通过JavaScript动态添加 -->
            </div>
        </div>
    </div>

    <script>
        // API配置数据
        const apiConfigs = {
            'yahoo_finance_1': {
                name: 'Yahoo Finance 经典版',
                host: 'yahoo-finance15.p.rapidapi.com',
                specialty: '全面基础功能',
                color: '#FF6B6B',
                icon: '🏛️'
            },
            'yh_finance_complete': {
                name: 'YH Finance 完整版',
                host: 'yh-finance.p.rapidapi.com',
                specialty: '结构化深度数据',
                color: '#4ECDC4',
                icon: '🔬'
            },
            'yahoo_finance_api_data': {
                name: 'Yahoo Finance 搜索版',
                host: 'yahoo-finance-api1.p.rapidapi.com',
                specialty: '搜索和趋势',
                color: '#45B7D1',
                icon: '🔍'
            },
            'yahoo_finance_realtime': {
                name: 'Yahoo Finance 实时版',
                host: 'yahoo-finance-low-latency.p.rapidapi.com',
                specialty: '低延迟实时',
                color: '#96CEB4',
                icon: '⚡'
            },
            'yh_finance': {
                name: 'YH Finance 增强版',
                host: 'yh-finance-complete.p.rapidapi.com',
                specialty: '历史深度数据',
                color: '#FFEAA7',
                icon: '📊'
            },
            'yahoo_finance_basic': {
                name: 'Yahoo Finance 基础版',
                host: 'yahoo-finance127.p.rapidapi.com',
                specialty: '简洁高效',
                color: '#DDA0DD',
                icon: '⚡'
            }
        };
        
        // 全局状态
        let matrixRunning = false;
        let apiStatuses = {};
        let updateInterval;
        
        // 初始化API状态
        function initializeApiStatuses() {
            for (const [key, config] of Object.entries(apiConfigs)) {
                apiStatuses[key] = {
                    name: config.name,
                    host: config.host,
                    status: ['active', 'standby', 'cooling'][Math.floor(Math.random() * 3)],
                    usagePercent: Math.random() * 95,
                    responseTime: 50 + Math.random() * 250,
                    successRate: 85 + Math.random() * 14.5,
                    specialty: config.specialty
                };
            }
        }
        
        // 渲染API卡片
        function renderApiCards() {
            const grid = document.getElementById('apiGrid');
            grid.innerHTML = '';
            
            for (const [key, config] of Object.entries(apiConfigs)) {
                const status = apiStatuses[key];
                const statusIcons = {
                    'active': '🟢',
                    'cooling': '🟡',
                    'error': '🔴',
                    'standby': '⚪'
                };
                
                const card = document.createElement('div');
                card.className = 'api-card';
                card.style.background = `linear-gradient(135deg, ${config.color}20, ${config.color}10)`;
                card.style.border = `2px solid ${config.color}`;
                
                card.innerHTML = `
                    <div class="api-card-content">
                        <div class="api-icon">${config.icon}</div>
                        <div class="api-name">${config.name}</div>
                        <div class="api-stats">
                            <div class="stat-item">
                                <strong>状态:</strong> ${statusIcons[status.status]} ${status.status.toUpperCase()}
                            </div>
                            <div class="stat-item">
                                <strong>响应时间:</strong> ${status.responseTime.toFixed(0)}ms
                            </div>
                            <div class="stat-item">
                                <strong>使用率:</strong> ${status.usagePercent.toFixed(1)}%
                                <div class="progress-bar">
                                    <div class="progress-fill" style="width: ${status.usagePercent}%"></div>
                                </div>
                            </div>
                            <div class="stat-item">
                                <strong>成功率:</strong> ${status.successRate.toFixed(1)}%
                                <div class="progress-bar">
                                    <div class="progress-fill" style="width: ${status.successRate}%"></div>
                                </div>
                            </div>
                        </div>
                        <div style="font-size: 0.9em; color: #666;">
                            <strong>专长:</strong> ${status.specialty}
                        </div>
                    </div>
                `;
                
                grid.appendChild(card);
            }
        }
        
        // 渲染协同矩阵
        function renderSynergyMatrix() {
            const matrix = document.getElementById('synergyMatrix');
            matrix.innerHTML = '';
            
            const apiKeys = Object.keys(apiConfigs);
            
            // 表头
            matrix.appendChild(createMatrixCell('API', 'matrix-header'));
            for (const key of apiKeys) {
                matrix.appendChild(createMatrixCell(apiConfigs[key].icon, 'matrix-header'));
            }
            
            // 表格内容
            for (let i = 0; i < apiKeys.length; i++) {
                const api1 = apiKeys[i];
                matrix.appendChild(createMatrixCell(`${apiConfigs[api1].icon} ${apiConfigs[api1].name.substring(0, 8)}`, 'matrix-label'));
                
                for (let j = 0; j < apiKeys.length; j++) {
                    const api2 = apiKeys[j];
                    let content;
                    
                    if (i === j) {
                        content = '🔵';
                    } else {
                        const status1 = apiStatuses[api1];
                        const status2 = apiStatuses[api2];
                        const synergy = (status1.successRate + status2.successRate) / 200;
                        
                        if (synergy > 0.9) content = '🟢';
                        else if (synergy > 0.8) content = '🟡';
                        else content = '🔴';
                    }
                    
                    matrix.appendChild(createMatrixCell(content, 'matrix-cell'));
                }
            }
        }
        
        function createMatrixCell(content, className) {
            const cell = document.createElement('div');
            cell.className = className;
            cell.textContent = content;
            return cell;
        }
        
        // 添加日志条目
        function addLogEntry() {
            const container = document.getElementById('logsContainer');
            const time = new Date().toLocaleTimeString();
            const apiKeys = Object.keys(apiConfigs);
            const randomApi = apiKeys[Math.floor(Math.random() * apiKeys.length)];
            const apiName = apiConfigs[randomApi].name;
            
            const operations = [
                `✅ ${apiName} 成功获取AAPL股价数据`,
                `🔄 ${apiName} 自动切换到备用端点`,
                `⚡ ${apiName} 响应时间优化至${Math.floor(50 + Math.random() * 150)}ms`,
                `🎯 ${apiName} 命中缓存，响应加速`,
                `🔍 ${apiName} 开始搜索热门股票`
            ];
            
            const operation = operations[Math.floor(Math.random() * operations.length)];
            const logEntry = document.createElement('div');
            logEntry.className = 'log-entry';
            logEntry.textContent = `[${time}] ${operation}`;
            
            container.appendChild(logEntry);
            container.scrollTop = container.scrollHeight;
            
            // 保持最多20条日志
            while (container.children.length > 20) {
                container.removeChild(container.firstChild);
            }
        }
        
        // 更新API状态
        function updateApiStatuses() {
            for (const key of Object.keys(apiStatuses)) {
                const status = apiStatuses[key];
                
                // 随机更新状态
                if (Math.random() < 0.1) {
                    status.status = ['active', 'standby', 'cooling'][Math.floor(Math.random() * 3)];
                }
                
                // 更新使用率
                status.usagePercent += (Math.random() - 0.5) * 10;
                status.usagePercent = Math.max(0, Math.min(100, status.usagePercent));
                
                // 更新响应时间
                status.responseTime += (Math.random() - 0.5) * 40;
                status.responseTime = Math.max(30, Math.min(500, status.responseTime));
                
                // 更新成功率
                status.successRate += (Math.random() - 0.5) * 2;
                status.successRate = Math.max(80, Math.min(100, status.successRate));
            }
        }
        
        // 控制函数
        function startMatrix() {
            matrixRunning = true;
            updateInterval = setInterval(() => {
                updateApiStatuses();
                renderApiCards();
                renderSynergyMatrix();
                addLogEntry();
            }, 2000);
        }
        
        function pauseMatrix() {
            matrixRunning = false;
            if (updateInterval) {
                clearInterval(updateInterval);
            }
        }
        
        function resetMatrix() {
            pauseMatrix();
            initializeApiStatuses();
            renderApiCards();
            renderSynergyMatrix();
            document.getElementById('logsContainer').innerHTML = '';
        }
        
        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            initializeApiStatuses();
            renderApiCards();
            renderSynergyMatrix();
        });
    </script>
</body>
</html>
