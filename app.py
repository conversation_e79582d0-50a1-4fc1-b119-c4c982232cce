#!/usr/bin/env python3
"""
MongoDB GraphRAG Web应用
Flask Web界面，支持MongoDB数据同步和智能问答
"""

import os
import json
from flask import Flask, render_template, request, jsonify, send_from_directory
from mongodb_graphrag import MongoDBGraphRAG
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = Flask(__name__)

# 全局GraphRAG实例
graphrag = None

def init_graphrag():
    """初始化GraphRAG实例"""
    global graphrag
    
    # 从环境变量获取MongoDB连接字符串
    mongo_uri = os.environ.get('MONGODB_URI', 
        'mongodb+srv://jingminzhang:<EMAIL>/cauldron_rss?retryWrites=true&w=majority')
    
    try:
        graphrag = MongoDBGraphRAG(mongo_uri=mongo_uri)
        logger.info("GraphRAG初始化成功")
    except Exception as e:
        logger.error(f"GraphRAG初始化失败: {e}")
        graphrag = None

@app.route('/')
def index():
    """主页"""
    return render_template('index.html')

@app.route('/api/mongodb/stats')
def mongodb_stats():
    """获取MongoDB统计信息"""
    if not graphrag:
        return jsonify({'error': 'GraphRAG未初始化'}), 500
    
    try:
        stats = graphrag.get_mongodb_stats()
        return jsonify(stats)
    except Exception as e:
        logger.error(f"获取MongoDB统计失败: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/sync', methods=['POST'])
def sync_data():
    """同步MongoDB数据"""
    if not graphrag:
        return jsonify({'error': 'GraphRAG未初始化'}), 500
    
    try:
        data = request.json or {}
        limit = data.get('limit', 50)
        skip = data.get('skip', 0)
        
        result = graphrag.sync_from_mongodb(limit=limit, skip=skip)
        return jsonify(result)
    except Exception as e:
        logger.error(f"数据同步失败: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/query', methods=['POST'])
def query():
    """智能问答"""
    if not graphrag:
        return jsonify({'error': 'GraphRAG未初始化'}), 500
    
    try:
        data = request.json
        question = data.get('question', '')
        
        if not question:
            return jsonify({'error': '问题不能为空'}), 400
        
        answer = graphrag.query(question)
        return jsonify({'answer': answer})
    except Exception as e:
        logger.error(f"查询失败: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/stats')
def local_stats():
    """获取本地知识图谱统计"""
    if not graphrag:
        return jsonify({'error': 'GraphRAG未初始化'}), 500
    
    try:
        stats = graphrag.show_stats()
        return jsonify(stats)
    except Exception as e:
        logger.error(f"获取统计失败: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/search/documents', methods=['POST'])
def search_documents():
    """搜索文档"""
    if not graphrag:
        return jsonify({'error': 'GraphRAG未初始化'}), 500
    
    try:
        data = request.json
        query_text = data.get('query', '')
        top_k = data.get('top_k', 5)
        
        if not query_text:
            return jsonify({'error': '搜索词不能为空'}), 400
        
        results = graphrag.search_documents(query_text, top_k)
        return jsonify({'results': results})
    except Exception as e:
        logger.error(f"文档搜索失败: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/search/entities', methods=['POST'])
def search_entities():
    """搜索实体"""
    if not graphrag:
        return jsonify({'error': 'GraphRAG未初始化'}), 500
    
    try:
        data = request.json
        query_text = data.get('query', '')
        top_k = data.get('top_k', 5)
        
        if not query_text:
            return jsonify({'error': '搜索词不能为空'}), 400
        
        results = graphrag.search_entities(query_text, top_k)
        return jsonify({'results': results})
    except Exception as e:
        logger.error(f"实体搜索失败: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/health')
def health():
    """健康检查"""
    return jsonify({
        'status': 'healthy',
        'graphrag_initialized': graphrag is not None
    })

@app.errorhandler(404)
def not_found(error):
    return jsonify({'error': 'Not found'}), 404

@app.errorhandler(500)
def internal_error(error):
    return jsonify({'error': 'Internal server error'}), 500

if __name__ == '__main__':
    # 初始化GraphRAG
    init_graphrag()
    
    # 获取端口（Heroku会设置PORT环境变量）
    port = int(os.environ.get('PORT', 5000))
    
    # 启动应用
    app.run(host='0.0.0.0', port=port, debug=False)
