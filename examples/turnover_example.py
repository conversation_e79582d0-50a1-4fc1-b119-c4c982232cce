import streamlit as st
import pandas as pd
import numpy as np
import time
from datetime import datetime, timedelta
import asyncio
import nest_asyncio
nest_asyncio.apply()
from ib_insync import IB, Stock
import yfinance as yf
import os
from dotenv import load_dotenv

load_dotenv()

# 真实的IB数据服务
class IBTurnoverService:
    def __init__(self, ib_client):
        self.ib = ib_client
        self.cache = None
        self.cache_time = None
        self.loop = asyncio.get_event_loop()

    def _run_async(self, coro):
        return self.loop.run_until_complete(coro)

    async def _get_turnover_data_async(self, symbols):
        contracts = [Stock(s, 'SMART', 'USD') for s in symbols]
        await self.ib.qualifyContractsAsync(*contracts)
        tickers = await self.ib.reqTickersAsync(*contracts)
        return tickers

    def get_turnover_data(self, limit=8):
        now = datetime.now()
        if self.cache and self.cache_time and (now - self.cache_time) < timedelta(seconds=60):
            return self.cache

        symbols_input = st.session_state.get('symbols_input', 'AAPL,GOOGL,MSFT,TSLA,NVDA,AMZN,META,JPM,V,JNJ,WMT,PG')
        symbols = [s.strip().upper() for s in symbols_input.split(',') if s.strip()]
        if not symbols:
            return []

        tickers = self._run_async(self._get_turnover_data_async(symbols))

        st.session_state.api_logs = []

        data = []

        for ticker in tickers:
            contract = ticker.contract
            price = ticker.marketPrice()
            volume = ticker.volume

            # 使用yfinance获取流通股本和平均成交量
            stock_info = yf.Ticker(contract.symbol).info
            st.session_state.api_logs.append({f"{contract.symbol} yfinance info": stock_info})
            shares_outstanding = stock_info.get('sharesOutstanding', 0)
            avg_volume = stock_info.get('averageVolume', 0)
            market_cap = stock_info.get('marketCap', 0)

            if shares_outstanding > 0:
                turnover_rate = (volume / shares_outstanding) * 100 if volume else 0
            else:
                turnover_rate = 0


            data.append({
                'symbol': contract.symbol,
                'price': price,
                'turnover_rate': round(turnover_rate, 2),
                'market_cap': round(market_cap / 1e9, 2) if market_cap else 0,
                'volume': volume,
                'avg_volume': int(avg_volume),
                'last_updated': now.strftime("%H:%M:%S")
            })

        # 按换手率降序排序
        data.sort(key=lambda x: x['turnover_rate'], reverse=True)

        self.cache = data[:limit]
        self.cache_time = now
        return self.cache

    async def _get_stock_details_async(self, symbol):
        contract = Stock(symbol, 'SMART', 'USD')
        await self.ib.qualifyContractsAsync(contract)
        ticker = (await self.ib.reqTickersAsync(contract))[0]

        hist_bars = await self.ib.reqHistoricalDataAsync(
            contract,
            endDateTime='',
            durationStr='30 D',
            barSizeSetting='1 day',
            whatToShow='TRADES',
            useRTH=True
        )

        details = await self.ib.reqContractDetailsAsync(contract)
        return ticker, hist_bars, details

    def get_stock_details(self, symbol):
        ticker, hist_bars, details = self._run_async(self._get_stock_details_async(symbol))

        price = ticker.marketPrice()
        volume = ticker.volume

        stock_info = yf.Ticker(symbol).info
        shares_outstanding = stock_info.get('sharesOutstanding', 0)
        market_cap = stock_info.get('marketCap', 0)

        if shares_outstanding > 0:
            turnover_rate = (volume / shares_outstanding) * 100 if volume else 0
        else:
            turnover_rate = 0

        avg_volume = stock_info.get('averageVolume', 0)
        description = details[0].longName if details else f'No description available for {symbol}.'

        return {
            'symbol': symbol,
            'price': price,
            'turnover_rate': round(turnover_rate, 2),
            'market_cap': round(market_cap / 1e9, 2) if market_cap else 0,
            'volume': volume,
            'avg_volume': int(avg_volume),
            'last_updated': datetime.now().strftime("%H:%M:%S"),
            'description': description
        }

def main():
    st.set_page_config(
        page_title="IB换手率排行榜",
        page_icon="📊",
        layout="wide",
        initial_sidebar_state="expanded"
    )
    
    # 自定义CSS样式
    st.markdown("""
    <style>
    .main-header {
        background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
        padding: 1rem;
        border-radius: 10px;
        color: white;
        text-align: center;
        margin-bottom: 2rem;
    }
    .metric-card {
        background: white;
        padding: 1rem;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        border-left: 4px solid #667eea;
    }
    .stock-card {
        background: #f8f9fa;
        padding: 1rem;
        border-radius: 8px;
        margin: 0.5rem 0;
        border-left: 4px solid #28a745;
    }
    .turnover-high {
        color: #dc3545;
        font-weight: bold;
    }
    .turnover-medium {
        color: #ffc107;
        font-weight: bold;
    }
    .turnover-low {
        color: #28a745;
        font-weight: bold;
    }
    </style>
    """, unsafe_allow_html=True)
    
    # 页面标题
    st.markdown("""
    <div class="main-header">
        <h1>📊 IB换手率排行榜</h1>
        <p>实时获取换手率最高的8只股票</p>
    </div>
    """, unsafe_allow_html=True)
    
        # 初始化服务
    @st.cache_resource
    def get_ib_client():
        ib = IB()
        host = os.getenv('IB_HOST', '127.0.0.1')
        port = int(os.getenv('IB_PORT', 4012))
        client_id = int(os.getenv('IB_CLIENT_ID', 1))
        try:
            ib.connect(host, port, clientId=client_id)
        except Exception as e:
            st.error(f"Failed to connect to IB Gateway: {e}")
            return None
        return ib

    @st.cache_resource
    def get_turnover_service():
        ib_client = get_ib_client()
        if ib_client:
            return IBTurnoverService(ib_client)
        return None

    ib_turnover_service = get_turnover_service()
    
    # 侧边栏控制
    with st.sidebar:
        st.header("🔧 控制面板")
        
        # 自动刷新设置
        auto_refresh = st.checkbox("自动刷新", value=False)
        if auto_refresh:
            refresh_interval = st.slider("刷新间隔(秒)", 5, 60, 10)
        
        # 手动刷新按钮
        if st.button("🔄 立即刷新", type="primary"):
            st.rerun()
        
        st.divider()
        
        # 显示设置
        st.subheader("显示设置")
        show_details = st.checkbox("显示详细信息", value=True)
        show_chart = st.checkbox("显示图表", value=True)
        
        st.divider()
        
        # 信息说明
        st.text_area("输入股票代码 (逗号分隔)", key='symbols_input', value='AAPL,GOOGL,MSFT,TSLA,NVDA,AMZN,META')

        st.info("""
        **换手率说明：**
        - 🔴 >10%: 极高换手
        - 🟡 5-10%: 高换手
        - 🟢 <5%: 正常换手
        
        数据来源: Interactive Brokers (实时成交量), yfinance (流通股本)
        """)
    
    # 获取数据
    try:
        turnover_data = ib_turnover_service.get_turnover_data(8)
        
        # 显示概览指标
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            avg_turnover = np.mean([stock['turnover_rate'] for stock in turnover_data])
            st.metric("平均换手率", f"{avg_turnover:.2f}%")
        
        with col2:
            max_turnover = max([stock['turnover_rate'] for stock in turnover_data])
            st.metric("最高换手率", f"{max_turnover:.2f}%")
        
        with col3:
            total_volume = sum([stock['volume'] for stock in turnover_data])
            st.metric("总成交量", f"{total_volume/1e6:.1f}M")
        
        with col4:
            st.metric("数据更新", turnover_data[0]['last_updated'])
        
        st.divider()
        
        # 主要数据表格
        st.subheader("🏆 换手率排行榜 (Top 8)")
        
        # 创建DataFrame
        df = pd.DataFrame(turnover_data)
        
        # 格式化显示
        for i, stock in enumerate(turnover_data, 1):
            col1, col2 = st.columns([3, 1])
            
            with col1:
                # 根据换手率设置颜色
                if stock['turnover_rate'] > 10:
                    turnover_class = "turnover-high"
                    emoji = "🔴"
                elif stock['turnover_rate'] > 5:
                    turnover_class = "turnover-medium"
                    emoji = "🟡"
                else:
                    turnover_class = "turnover-low"
                    emoji = "🟢"
                
                with st.expander(f"#{i} {stock['symbol']} - 换手率: {stock['turnover_rate']}%", expanded=True):
                    st.markdown(f"""
                    **价格:** ${stock['price']}<br>
                    **实时成交量 (from IB):** {stock['volume']:,}<br>
                    **流通股本 (from yfinance):** {stock_info.get('sharesOutstanding', 0):,}<br>
                    **市值 (from yfinance):** {stock['market_cap']}B
                    """, unsafe_allow_html=True)
            
            with col2:
                if st.button(f"详情", key=f"detail_{stock['symbol']}"):
                    st.session_state.selected_stock = stock['symbol']
        
        # 显示图表
        if show_chart:
            st.subheader("📈 换手率分布图")
            
            col1, col2 = st.columns(2)
            
            with col1:
                # 柱状图
                chart_data = pd.DataFrame({
                    '股票代码': [stock['symbol'] for stock in turnover_data],
                    '换手率(%)': [stock['turnover_rate'] for stock in turnover_data]
                })
                st.bar_chart(chart_data.set_index('股票代码'))
            
            with col2:
                # 饼图数据
                high_turnover = len([s for s in turnover_data if s['turnover_rate'] > 10])
                medium_turnover = len([s for s in turnover_data if 5 <= s['turnover_rate'] <= 10])
                low_turnover = len([s for s in turnover_data if s['turnover_rate'] < 5])
                
                pie_data = pd.DataFrame({
                    '换手率区间': ['高换手(>10%)', '中换手(5-10%)', '低换手(<5%)'],
                    '数量': [int(high_turnover), int(medium_turnover), int(low_turnover)]
                })
                
                if pie_data['数量'].sum() > 0:
                    st.write("**换手率分布**")
                    for _, row in pie_data.iterrows():
                        if row['数量'] > 0:
                            st.write(f"- {row['换手率区间']}: {row['数量']}只")
        
        # 显示API日志
        if 'api_logs' in st.session_state:
            with st.expander("显示API原始数据日志"):
                st.json(st.session_state.api_logs)

        # 显示详细信息
        if 'selected_stock' in st.session_state and show_details:
            st.divider()
            st.subheader(f"📋 {st.session_state.selected_stock} 详细信息")
            
            stock_details = ib_turnover_service.get_stock_details(st.session_state.selected_stock)
            
            col1, col2, col3 = st.columns(3)
            
            with col1:
                st.write("**基本信息**")
                st.write(f"公司名称: {stock_details.get('description', 'N/A')}")
                st.write(f"股价: ${stock_details.get('price', 'N/A')}")
            
            with col2:
                st.write("**交易数据**")
                st.write(f"换手率: {stock_details['turnover_rate']}%")
                st.write(f"成交量: {stock_details['volume']:,}")
                st.write(f"平均成交量: {stock_details['avg_volume']:,}")
                st.write(f"成交量比: {stock_details['volume']/stock_details['avg_volume']:.2f}x")
            
            with col3:
                st.write("**估值数据**")
                st.write(f"市值: {stock_details['market_cap']}B")
                st.write(f"市盈率: {stock_details['pe_ratio']}")
                st.write(f"更新时间: {stock_details['last_updated']}")
            
            if st.button("关闭详情"):
                del st.session_state.selected_stock
                st.rerun()
        
        # 数据表格视图
        if st.checkbox("显示数据表格"):
            st.subheader("📊 数据表格")
            
            # 格式化DataFrame用于显示
            display_df = df.copy()
            display_df['换手率'] = display_df['turnover_rate'].apply(lambda x: f"{x}%")
            display_df['价格'] = display_df['price'].apply(lambda x: f"${x}")
            display_df['成交量'] = display_df['volume'].apply(lambda x: f"{x:,}")
            display_df['市值'] = display_df['market_cap'].apply(lambda x: f"{x}B")
            
            # 选择要显示的列
            display_columns = ['symbol', '换手率', '价格', '成交量', '市值', 'last_updated']
            column_names = ['股票代码', '换手率', '价格', '成交量', '市值', '更新时间']
            
            display_df_final = display_df[display_columns]
            display_df_final.columns = column_names
            
            st.dataframe(
                display_df_final,
                use_container_width=True,
                hide_index=True
            )
    
    except Exception as e:
        st.error(f"获取数据时出错: {str(e)}")
        st.info("请检查IB连接或稍后重试")
    
    # 自动刷新逻辑
    if auto_refresh:
        time.sleep(refresh_interval)
        st.rerun()
    
    # 页脚
    st.divider()
    st.markdown("""
    <div style="text-align: center; color: #666; padding: 1rem;">
        <p>📊 IB换手率排行榜 | 数据仅供参考，不构成投资建议</p>
        <p><small>最后更新: {}</small></p>
    </div>
    """.format(datetime.now().strftime('%Y-%m-%d %H:%M:%S')), unsafe_allow_html=True)

if __name__ == "__main__":
    main()