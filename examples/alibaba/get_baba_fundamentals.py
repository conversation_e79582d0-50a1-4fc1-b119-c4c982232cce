#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
阿里巴巴(BABA)基本面数据获取和分析

本脚本用于:
1. 从IB获取阿里巴巴美股的基本面数据
2. 分析基本面数据中的关键指标
3. 讨论美股估值和财务数据质量问题
"""

import time
import xml.etree.ElementTree as ET
from ibapi.interactors.IBMarketData import IBMarketData
from ib_insync import util
import json
from datetime import datetime

def parse_fundamental_xml(xml_data):
    """
    解析IB返回的基本面数据XML
    """
    try:
        root = ET.fromstring(xml_data)
        
        # 提取关键财务指标
        fundamentals = {}
        
        # 查找各种财务数据节点
        for elem in root.iter():
            if elem.tag and elem.text:
                fundamentals[elem.tag] = elem.text
        
        return fundamentals
    except Exception as e:
        print(f"解析XML数据时出错: {e}")
        return {}

def analyze_fundamentals(fundamentals):
    """
    分析基本面数据，重点关注:
    1. 估值指标 (PE, PB, PS等)
    2. 盈利质量 (营收vs营销费用)
    3. 财务健康度
    """
    analysis = {
        "估值分析": {},
        "盈利质量分析": {},
        "财务健康度": {},
        "风险提示": []
    }
    
    # 估值分析
    pe_ratio = fundamentals.get('PERatio', 'N/A')
    pb_ratio = fundamentals.get('PBRatio', 'N/A')
    ps_ratio = fundamentals.get('PSRatio', 'N/A')
    
    analysis["估值分析"] = {
        "PE市盈率": pe_ratio,
        "PB市净率": pb_ratio,
        "PS市销率": ps_ratio,
        "估值评价": "需要与同行业对比分析"
    }
    
    # 盈利质量分析
    revenue = fundamentals.get('TotalRevenue', 'N/A')
    marketing_expense = fundamentals.get('MarketingExpense', 'N/A')
    net_income = fundamentals.get('NetIncome', 'N/A')
    
    analysis["盈利质量分析"] = {
        "总营收": revenue,
        "营销费用": marketing_expense,
        "净利润": net_income,
        "营收质量评价": "需要关注营收增长的可持续性"
    }
    
    # 风险提示
    analysis["风险提示"] = [
        "中概股面临监管风险",
        "VIE结构存在法律风险",
        "财务数据可能存在调节空间",
        "需要关注现金流质量"
    ]
    
    return analysis

def discuss_fundamental_issues():
    """
    讨论用户提出的基本面分析问题
    """
    discussion = {
        "问题1_美股估值问题": {
            "现象": "美股市场对某些中概股不给予合理估值",
            "原因分析": [
                "1. 信息不对称: 投资者对中国企业了解有限",
                "2. 监管风险: 中美监管环境变化带来不确定性",
                "3. VIE结构风险: 可变利益实体结构的法律风险",
                "4. 财务透明度: 投资者对财务数据真实性存疑",
                "5. 地缘政治风险: 中美关系影响投资情绪"
            ],
            "影响": "导致估值折价，即使基本面良好也难获得合理定价"
        },
        
        "问题2_CEO财务能力": {
            "现象": "CEO在财务方面表现出色，被称为'财务方面的神笔马良'",
            "分析角度": [
                "1. 正面解读: 优秀的财务管理能力，提高资本效率",
                "2. 负面解读: 可能存在财务数据美化的嫌疑",
                "3. 中性解读: 在复杂商业环境下的财务策略调整"
            ],
            "关注要点": [
                "现金流与利润的匹配度",
                "会计政策的变更频率",
                "一次性收益的占比",
                "关联交易的合理性"
            ]
        },
        
        "问题3_利润表集中化": {
            "现象": "企业开始集中精力优化利润表",
            "背景": "'利润是做出来的'这一观点反映了会计处理的灵活性",
            "关注要点": [
                "1. 营收确认政策: 是否过于激进",
                "2. 成本分摊方法: 是否合理分配",
                "3. 减值准备: 是否充分计提",
                "4. 一次性项目: 是否频繁出现"
            ],
            "分析建议": [
                "重点关注经营性现金流",
                "分析营收与现金流的匹配度",
                "关注毛利率的变化趋势",
                "评估盈利的可持续性"
            ]
        },
        
        "问题4_营收营销比值": {
            "指标意义": "营收与营销费用的比值反映营销效率",
            "控制方法": [
                "1. 调整营销投入时机",
                "2. 优化营销渠道组合",
                "3. 改变营销费用确认方式",
                "4. 通过关联方调节费用"
            ],
            "分析要点": [
                "营销费用的季节性波动",
                "营销ROI的长期趋势",
                "获客成本的变化",
                "客户生命周期价值"
            ]
        },
        
        "太乙观澜价值": {
            "核心价值": "揣摩董事会行动意图，预判企业战略方向",
            "分析维度": [
                "1. 董事会构成变化",
                "2. 高管薪酬结构调整",
                "3. 股权激励计划",
                "4. 战略投资方向",
                "5. 资本运作动向"
            ],
            "实践方法": [
                "跟踪董事会决议",
                "分析管理层表态",
                "观察资本配置策略",
                "研究同业对标行为"
            ]
        }
    }
    
    return discussion

def main():
    """
    主函数：获取阿里巴巴基本面数据并进行分析讨论
    """
    print("=" * 60)
    print("阿里巴巴(BABA)基本面数据获取与分析")
    print("=" * 60)
    
    # 设置日志
    util.logToConsole()
    
    # 创建市场数据交互器
    market_data_interactor = IBMarketData()
    
    try:
        print("\n📊 正在获取阿里巴巴(BABA)基本面数据...")
        
        # 获取基本面数据
        xml_data = market_data_interactor.get_fundamental_data(
            symbol='BABA', 
            market='US', 
            report_type='ReportsFinSummary'
        )
        
        print(f"\n✅ 数据获取成功，数据长度: {len(xml_data)} 字符")
        
        # 解析数据
        fundamentals = parse_fundamental_xml(xml_data)
        
        if fundamentals:
            print("\n📈 基本面数据解析结果:")
            for key, value in list(fundamentals.items())[:10]:  # 显示前10个指标
                print(f"  {key}: {value}")
            
            # 分析数据
            analysis = analyze_fundamentals(fundamentals)
            
            print("\n🔍 基本面分析结果:")
            print(json.dumps(analysis, ensure_ascii=False, indent=2))
        
        # 保存原始数据
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        with open(f"baba_fundamentals_{timestamp}.xml", "w", encoding="utf-8") as f:
            f.write(xml_data)
        print(f"\n💾 原始数据已保存到: baba_fundamentals_{timestamp}.xml")
        
    except Exception as e:
        print(f"❌ 获取数据时出错: {e}")
        print("\n💡 可能的原因:")
        print("  1. IB连接未建立")
        print("  2. 市场数据权限不足")
        print("  3. 网络连接问题")
    
    finally:
        # 断开连接
        try:
            market_data_interactor.ibService.ib_client.disconnect()
        except:
            pass
    
    # 进行理论讨论
    print("\n" + "=" * 60)
    print("💭 基本面分析理论讨论")
    print("=" * 60)
    
    discussion = discuss_fundamental_issues()
    
    for topic, content in discussion.items():
        print(f"\n🎯 {topic.replace('_', ' - ')}")
        print("-" * 40)
        
        if isinstance(content, dict):
            for key, value in content.items():
                print(f"\n📌 {key}:")
                if isinstance(value, list):
                    for item in value:
                        print(f"   • {item}")
                else:
                    print(f"   {value}")
        else:
            print(content)
    
    print("\n" + "=" * 60)
    print("🎯 总结与建议")
    print("=" * 60)
    
    recommendations = [
        "1. 基本面分析需要结合多维度数据，不能仅依赖单一指标",
        "2. 对于中概股，要特别关注VIE结构和监管风险",
        "3. 财务数据的质量比数量更重要，现金流是关键",
        "4. 董事会动向分析是高级投资策略的重要组成部分",
        "5. 建议建立动态跟踪机制，定期更新分析结果"
    ]
    
    for rec in recommendations:
        print(f"💡 {rec}")
    
    print("\n分析完成！")

if __name__ == "__main__":
    main()