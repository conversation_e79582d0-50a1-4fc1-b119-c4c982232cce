#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
岩石力学金融分析工具 - 实用版

基于岩石力学原理的金融市场应力分析工具，包含：
1. 应力场可视化
2. 蠕变-断裂预测
3. 底层资产映射分析
4. 八卦对卦反调模拟
5. 实时市场监控

使用方法：
python rock_mechanics_analyzer.py [company_id] [--visual] [--monitor]
"""

import sys
import time
import random
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional, Any, Union
from dataclasses import dataclass, field
from enum import Enum
import uuid
from abc import ABC, abstractmethod
import matplotlib.pyplot as plt
from matplotlib.patches import Polygon
from matplotlib.colors import LinearSegmentedColormap
import matplotlib.animation as animation

# 尝试导入可选依赖
try:
    import seaborn as sns
    SEABORN_AVAILABLE = True
except ImportError:
    SEABORN_AVAILABLE = False

try:
    from tqdm import tqdm
    TQDM_AVAILABLE = True
except ImportError:
    TQDM_AVAILABLE = False

# ==================== 底层资产对象模型 ====================

class AssetType(Enum):
    """资产类型枚举"""
    REAL_ESTATE = "real_estate"  # 房地产
    MACHINERY = "machinery"      # 机械设备
    INTELLECTUAL_PROPERTY = "ip" # 知识产权
    HUMAN_CAPITAL = "human"      # 人力资本
    NATURAL_RESOURCE = "natural" # 自然资源
    FINANCIAL_INSTRUMENT = "financial" # 金融工具
    BRAND_VALUE = "brand"        # 品牌价值
    DATA_ASSET = "data"          # 数据资产

class AssetLifecycleStage(Enum):
    """资产生命周期阶段"""
    CREATION = "creation"        # 创建期
    GROWTH = "growth"           # 成长期
    MATURITY = "maturity"       # 成熟期
    DECLINE = "decline"         # 衰退期
    DISPOSAL = "disposal"       # 处置期

@dataclass
class UnderlyingAsset:
    """底层资产基础类 - 实现真实的一一映射"""
    asset_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    asset_type: AssetType = AssetType.REAL_ESTATE
    name: str = ""
    location: str = ""  # 地理位置
    creation_date: datetime = field(default_factory=datetime.now)
    lifecycle_stage: AssetLifecycleStage = AssetLifecycleStage.CREATION
    
    # 物理属性
    physical_condition: float = 1.0  # 物理状态 0-1
    depreciation_rate: float = 0.05  # 折旧率
    
    # 经济属性
    book_value: float = 0.0
    market_value: float = 0.0
    cash_flow_history: List[float] = field(default_factory=list)
    
    # 应力相关属性
    stress_tolerance: float = 1.0    # 应力承受能力
    current_stress: float = 0.0      # 当前应力水平
    creep_rate: float = 0.0          # 蠕变速率
    fracture_threshold: float = 0.8  # 断裂阈值
    
    # 历史记录
    stress_history: List[float] = field(default_factory=list)
    condition_history: List[float] = field(default_factory=list)
    timestamp_history: List[datetime] = field(default_factory=list)
    
    def update_lifecycle(self):
        """更新生命周期状态"""
        age_years = (datetime.now() - self.creation_date).days / 365.25
        
        if age_years < 2:
            self.lifecycle_stage = AssetLifecycleStage.CREATION
        elif age_years < 10:
            self.lifecycle_stage = AssetLifecycleStage.GROWTH
        elif age_years < 20:
            self.lifecycle_stage = AssetLifecycleStage.MATURITY
        elif age_years < 30:
            self.lifecycle_stage = AssetLifecycleStage.DECLINE
        else:
            self.lifecycle_stage = AssetLifecycleStage.DISPOSAL
    
    def calculate_stress_impact(self, external_stress: float, timestamp: datetime = None) -> float:
        """计算外部应力对资产的影响"""
        if timestamp is None:
            timestamp = datetime.now()
            
        self.current_stress = external_stress / self.stress_tolerance
        
        # 蠕变效应：持续应力导致缓慢变形
        if self.current_stress > 0.3:
            self.creep_rate = (self.current_stress - 0.3) * 0.1
            self.physical_condition -= self.creep_rate * 0.01
        else:
            self.creep_rate = 0
        
        # 断裂风险评估
        fracture_risk = max(0, self.current_stress - self.fracture_threshold)
        
        # 记录历史
        self.stress_history.append(self.current_stress)
        self.condition_history.append(self.physical_condition)
        self.timestamp_history.append(timestamp)
        
        return fracture_risk
    
    def get_stress_timeline(self) -> pd.DataFrame:
        """获取应力时间线数据"""
        return pd.DataFrame({
            'timestamp': self.timestamp_history,
            'stress': self.stress_history,
            'condition': self.condition_history,
            'creep_rate': [0] + [(self.condition_history[i-1] - self.condition_history[i]) 
                               for i in range(1, len(self.condition_history))]
        })
    
    def predict_failure(self, future_stress_pattern: List[float] = None) -> Optional[int]:
        """预测失效时间（天数）"""
        if not self.stress_history:
            return None
        
        # 如果没有提供未来应力模式，使用历史平均值
        if future_stress_pattern is None:
            avg_stress = sum(self.stress_history) / len(self.stress_history)
            future_stress_pattern = [avg_stress] * 365  # 假设一年
        
        # 复制当前状态进行模拟
        condition = self.physical_condition
        days_to_failure = None
        
        for i, stress in enumerate(future_stress_pattern):
            # 计算蠕变效应
            if stress > 0.3:
                creep_rate = (stress - 0.3) * 0.1
                condition -= creep_rate * 0.01
            
            # 检查是否失效（物理状态低于0.2或应力超过断裂阈值）
            if condition < 0.2 or stress > self.fracture_threshold:
                days_to_failure = i + 1
                break
        
        return days_to_failure

class AssetFactory:
    """底层资产对象工厂 - 解决一一映射问题"""
    
    def __init__(self):
        self.asset_registry: Dict[str, UnderlyingAsset] = {}
        self.company_asset_mapping: Dict[str, List[str]] = {}
    
    def create_asset(self, asset_type: AssetType, **kwargs) -> UnderlyingAsset:
        """创建底层资产对象"""
        asset = UnderlyingAsset(asset_type=asset_type, **kwargs)
        self.asset_registry[asset.asset_id] = asset
        return asset
    
    def map_company_assets(self, company_id: str, asset_ids: List[str]):
        """建立公司与底层资产的映射关系"""
        self.company_asset_mapping[company_id] = asset_ids
    
    def get_company_assets(self, company_id: str) -> List[UnderlyingAsset]:
        """获取公司的所有底层资产"""
        asset_ids = self.company_asset_mapping.get(company_id, [])
        return [self.asset_registry[aid] for aid in asset_ids if aid in self.asset_registry]
    
    def calculate_company_stress(self, company_id: str, market_stress: float, 
                               timestamp: datetime = None) -> Dict[str, Any]:
        """计算公司层面的应力分布"""
        assets = self.get_company_assets(company_id)
        stress_analysis = {}
        
        total_fracture_risk = 0
        total_value = 0
        
        for asset in assets:
            fracture_risk = asset.calculate_stress_impact(market_stress, timestamp)
            stress_analysis[asset.asset_id] = {
                'fracture_risk': fracture_risk,
                'creep_rate': asset.creep_rate,
                'current_stress': asset.current_stress,
                'asset_value': asset.market_value,
                'physical_condition': asset.physical_condition
            }
            
            total_fracture_risk += fracture_risk * asset.market_value
            total_value += asset.market_value
        
        # 加权平均断裂风险
        avg_fracture_risk = total_fracture_risk / total_value if total_value > 0 else 0
        
        stress_analysis['company_summary'] = {
            'weighted_fracture_risk': avg_fracture_risk,
            'total_asset_value': total_value,
            'asset_count': len(assets),
            'timestamp': timestamp or datetime.now()
        }
        
        return stress_analysis
    
    def get_company_stress_timeline(self, company_id: str) -> pd.DataFrame:
        """获取公司应力时间线"""
        assets = self.get_company_assets(company_id)
        if not assets:
            return pd.DataFrame()
        
        # 合并所有资产的时间线
        all_timelines = []
        for asset in assets:
            timeline = asset.get_stress_timeline()
            if not timeline.empty:
                timeline['asset_id'] = asset.asset_id
                timeline['asset_name'] = asset.name
                timeline['asset_type'] = asset.asset_type.value
                timeline['asset_value'] = asset.market_value
                all_timelines.append(timeline)
        
        if not all_timelines:
            return pd.DataFrame()
        
        return pd.concat(all_timelines, ignore_index=True)

# ==================== 八卦应力场系统 ====================

class BaGuaStress(Enum):
    """八卦应力类型"""
    QIAN = "乾"  # 天，创造力压力
    KUN = "坤"   # 地，承载力压力
    ZHEN = "震"  # 雷，冲击力压力
    XUN = "巽"   # 风，渗透力压力
    KAN = "坎"   # 水，流动力压力
    LI = "离"    # 火，燃烧力压力
    GEN = "艮"   # 山，阻挡力压力
    DUI = "兑"   # 泽，交换力压力

class StressOpposition:
    """对卦反调应力模拟系统"""
    
    # 八卦对卦关系（完全相反）
    OPPOSITION_PAIRS = {
        BaGuaStress.QIAN: BaGuaStress.KUN,  # 乾坤对立
        BaGuaStress.KUN: BaGuaStress.QIAN,
        BaGuaStress.ZHEN: BaGuaStress.XUN,  # 震巽对立
        BaGuaStress.XUN: BaGuaStress.ZHEN,
        BaGuaStress.KAN: BaGuaStress.LI,   # 坎离对立
        BaGuaStress.LI: BaGuaStress.KAN,
        BaGuaStress.GEN: BaGuaStress.DUI,  # 艮兑对立
        BaGuaStress.DUI: BaGuaStress.GEN
    }
    
    # 八卦方位（先天八卦）
    BAGUA_POSITIONS = {
        BaGuaStress.QIAN: (0, 1),    # 北
        BaGuaStress.KUN: (0, -1),    # 南
        BaGuaStress.ZHEN: (-1, -0.5), # 东北
        BaGuaStress.XUN: (1, -0.5),   # 西南
        BaGuaStress.KAN: (-1, 0.5),   # 东南
        BaGuaStress.LI: (1, 0.5),     # 西北
        BaGuaStress.GEN: (-0.5, 0),   # 东
        BaGuaStress.DUI: (0.5, 0)     # 西
    }
    
    # 八卦颜色
    BAGUA_COLORS = {
        BaGuaStress.QIAN: '#FFFFFF',  # 白色
        BaGuaStress.KUN: '#000000',   # 黑色
        BaGuaStress.ZHEN: '#7F00FF',  # 紫色
        BaGuaStress.XUN: '#00FF00',   # 绿色
        BaGuaStress.KAN: '#0000FF',   # 蓝色
        BaGuaStress.LI: '#FF0000',    # 红色
        BaGuaStress.GEN: '#8B4513',   # 棕色
        BaGuaStress.DUI: '#C0C0C0'    # 银色
    }
    
    def __init__(self):
        self.stress_field = {stress: 0.0 for stress in BaGuaStress}
        self.stress_history = []
        self.timestamp_history = []
    
    def apply_stress(self, stress_type: BaGuaStress, intensity: float, timestamp: datetime = None):
        """施加应力，同时产生对卦反调效应"""
        if timestamp is None:
            timestamp = datetime.now()
            
        self.stress_field[stress_type] += intensity
        
        # 对卦反调：相反方向的应力
        opposite_stress = self.OPPOSITION_PAIRS[stress_type]
        self.stress_field[opposite_stress] -= intensity * 0.618  # 黄金比例衰减
        
        # 记录历史
        self.stress_history.append(dict(self.stress_field))
        self.timestamp_history.append(timestamp)
    
    def calculate_net_stress(self) -> float:
        """计算净应力"""
        return sum(self.stress_field.values())
    
    def get_dominant_stress(self) -> Tuple[BaGuaStress, float]:
        """获取主导应力"""
        max_stress = max(self.stress_field.items(), key=lambda x: abs(x[1]))
        return max_stress
    
    def get_stress_timeline(self) -> pd.DataFrame:
        """获取应力场时间线数据"""
        if not self.stress_history:
            return pd.DataFrame()
        
        # 转换为DataFrame
        data = []
        for i, stress_dict in enumerate(self.stress_history):
            for stress_type, value in stress_dict.items():
                data.append({
                    'timestamp': self.timestamp_history[i],
                    'stress_type': stress_type.value,
                    'value': value
                })
        
        return pd.DataFrame(data)
    
    def visualize_stress_field(self, ax=None, title="八卦应力场"):
        """可视化八卦应力场"""
        if ax is None:
            fig, ax = plt.subplots(figsize=(10, 10))
        
        # 绘制八卦图
        circle = plt.Circle((0, 0), 1, fill=False, color='black')
        ax.add_patch(circle)
        
        # 绘制太极
        yin_yang = plt.Circle((0, 0), 0.1, fill=True, color='gray')
        ax.add_patch(yin_yang)
        
        # 绘制八卦方位和应力
        for stress_type, position in self.BAGUA_POSITIONS.items():
            stress_value = self.stress_field[stress_type]
            color = self.BAGUA_COLORS[stress_type]
            
            # 应力强度决定箭头长度
            arrow_length = abs(stress_value) * 0.5
            if arrow_length > 0.01:  # 只绘制有意义的应力
                # 箭头方向：正值向外，负值向内
                direction = 1 if stress_value > 0 else -1
                dx, dy = position[0] * arrow_length * direction, position[1] * arrow_length * direction
                
                # 起点：从八卦位置开始
                start_x, start_y = position[0] * 0.8, position[1] * 0.8
                
                # 绘制箭头
                ax.arrow(start_x, start_y, dx, dy, head_width=0.05, 
                        head_length=0.1, fc=color, ec=color, alpha=0.7)
            
            # 标注八卦名称
            ax.text(position[0] * 1.1, position[1] * 1.1, 
                   f"{stress_type.value}\n{stress_value:.2f}", 
                   ha='center', va='center', fontsize=12)
        
        # 设置图表属性
        ax.set_xlim(-1.2, 1.2)
        ax.set_ylim(-1.2, 1.2)
        ax.set_aspect('equal')
        ax.axis('off')
        ax.set_title(title)
        
        # 添加净应力标注
        net_stress = self.calculate_net_stress()
        dominant_stress, stress_intensity = self.get_dominant_stress()
        ax.text(0, -1.1, 
               f"净应力: {net_stress:.2f}\n主导: {dominant_stress.value} ({stress_intensity:.2f})", 
               ha='center', fontsize=12)
        
        return ax

# ==================== 岩石力学分析工具 ====================

class RockMechanicsAnalyzer:
    """岩石力学金融分析工具"""
    
    def __init__(self):
        self.asset_factory = AssetFactory()
        self.stress_opposition = StressOpposition()
        self.analysis_history = []
        
        print("🏔️ 岩石力学金融分析工具初始化完成")
    
    def create_demo_company(self, company_id="DEMO_COMPANY"):
        """创建演示公司数据"""
        # 创建底层资产
        assets = [
            self.asset_factory.create_asset(
                AssetType.REAL_ESTATE,
                name="总部大楼",
                location="北京中关村",
                market_value=*********,
                stress_tolerance=0.85
            ),
            self.asset_factory.create_asset(
                AssetType.MACHINERY,
                name="生产线设备群",
                location="天津开发区",
                market_value=*********,
                stress_tolerance=0.65
            ),
            self.asset_factory.create_asset(
                AssetType.INTELLECTUAL_PROPERTY,
                name="核心专利组合",
                location="全球范围",
                market_value=*********,
                stress_tolerance=0.9
            ),
            self.asset_factory.create_asset(
                AssetType.HUMAN_CAPITAL,
                name="研发团队",
                location="多地分布",
                market_value=*********,
                stress_tolerance=0.75
            ),
            self.asset_factory.create_asset(
                AssetType.FINANCIAL_INSTRUMENT,
                name="流动资金",
                location="银行账户",
                market_value=*********,
                stress_tolerance=0.95
            )
        ]
        
        # 建立公司映射
        asset_ids = [asset.asset_id for asset in assets]
        self.asset_factory.map_company_assets(company_id, asset_ids)
        
        print(f"✅ 已创建演示公司 {company_id} 及其 {len(assets)} 个底层资产")
        return company_id
    
    def generate_market_stress(self, volatility=0.2, trend=0):
        """生成模拟市场应力数据"""
        base_stress = {
            '乾': 0.5 + trend,  # 创造力压力
            '坤': -0.3 + trend, # 承载力压力
            '震': 0.6 + trend,  # 冲击力压力
            '巽': 0.2 + trend,  # 渗透力压力
            '坎': 0.4 + trend,  # 流动力压力
            '离': 0.5 + trend,  # 燃烧力压力
            '艮': -0.3 + trend, # 阻挡力压力
            '兑': 0.3 + trend,  # 交换力压力
            'volatility': 0.3 + volatility,  # 市场波动率
            'liquidity_stress': 0.4 + volatility,  # 流动性压力
            'credit_spread': 0.3 + volatility,  # 信用利差
            'regulatory_pressure': 0.2 + volatility,  # 监管压力
        }
        
        # 添加随机波动
        for key in base_stress:
            base_stress[key] += random.uniform(-volatility, volatility)
        
        return base_stress
    
    def analyze_company(self, company_id, market_data=None, timestamp=None):
        """分析公司应力状态"""
        if timestamp is None:
            timestamp = datetime.now()
            
        if market_data is None:
            market_data = self.generate_market_stress()
        
        # 应用八卦应力场
        for stress_type in BaGuaStress:
            if stress_type.value in market_data:
                self.stress_opposition.apply_stress(stress_type, market_data[stress_type.value], timestamp)
        
        # 计算公司应力分布
        stress_analysis = self.asset_factory.calculate_company_stress(
            company_id, market_data.get('volatility', 0.3), timestamp
        )
        
        # 记录分析结果
        analysis_result = {
            'timestamp': timestamp,
            'market_data': market_data,
            'stress_analysis': stress_analysis,
            'net_stress': self.stress_opposition.calculate_net_stress(),
            'dominant_stress': self.stress_opposition.get_dominant_stress()
        }
        self.analysis_history.append(analysis_result)
        
        return analysis_result
    
    def run_stress_simulation(self, company_id, days=30, volatility=0.2, trend=0):
        """运行应力模拟"""
        print(f"🔄 开始运行 {days} 天的应力模拟...")
        
        # 创建时间序列
        start_date = datetime.now()
        dates = [start_date + timedelta(days=i) for i in range(days)]
        
        # 使用tqdm显示进度条（如果可用）
        date_iterator = tqdm(dates) if TQDM_AVAILABLE else dates
        
        for date in date_iterator:
            # 生成市场数据，添加时间趋势
            day_trend = trend * (date - start_date).days / days
            market_data = self.generate_market_stress(volatility, day_trend)
            
            # 分析公司
            self.analyze_company(company_id, market_data, date)
        
        print(f"✅ 模拟完成，共生成 {len(self.analysis_history)} 个数据点")
        return self.get_simulation_results()
    
    def get_simulation_results(self):
        """获取模拟结果"""
        if not self.analysis_history:
            return None
        
        # 提取时间序列数据
        timestamps = [result['timestamp'] for result in self.analysis_history]
        net_stress = [result['net_stress'] for result in self.analysis_history]
        dominant_stress = [result['dominant_stress'][1] for result in self.analysis_history]
        
        # 提取资产数据
        asset_data = []
        for result in self.analysis_history:
            stress_analysis = result['stress_analysis']
            for asset_id, asset_info in stress_analysis.items():
                if asset_id != 'company_summary':
                    asset_data.append({
                        'timestamp': result['timestamp'],
                        'asset_id': asset_id,
                        'fracture_risk': asset_info['fracture_risk'],
                        'current_stress': asset_info['current_stress'],
                        'physical_condition': asset_info.get('physical_condition', 1.0)
                    })
        
        # 转换为DataFrame
        timeline_df = pd.DataFrame({
            'timestamp': timestamps,
            'net_stress': net_stress,
            'dominant_stress': dominant_stress
        })
        
        asset_df = pd.DataFrame(asset_data) if asset_data else pd.DataFrame()
        
        return {
            'timeline': timeline_df,
            'asset_data': asset_df,
            'raw_history': self.analysis_history
        }
    
    def visualize_stress_timeline(self, results=None):
        """可视化应力时间线"""
        if results is None:
            results = self.get_simulation_results()
            
        if results is None or results['timeline'].empty:
            print("⚠️ 没有可视化的数据")
            return
        
        # 设置样式
        if SEABORN_AVAILABLE:
            sns.set_style("whitegrid")
        
        # 创建图表
        fig, axes = plt.subplots(2, 1, figsize=(12, 10), sharex=True)
        
        # 绘制净应力时间线
        timeline = results['timeline']
        axes[0].plot(timeline['timestamp'], timeline['net_stress'], 'b-', linewidth=2)
        axes[0].set_title('净应力时间线')
        axes[0].set_ylabel('净应力值')
        axes[0].axhline(y=0, color='r', linestyle='--', alpha=0.5)
        
        # 绘制主导应力强度
        axes[1].plot(timeline['timestamp'], timeline['dominant_stress'], 'g-', linewidth=2)
        axes[1].set_title('主导应力强度时间线')
        axes[1].set_ylabel('主导应力强度')
        axes[1].set_xlabel('时间')
        
        # 格式化x轴日期
        fig.autofmt_xdate()
        
        plt.tight_layout()
        plt.show()
        
        return fig
    
    def visualize_asset_stress(self, results=None):
        """可视化资产应力分布"""
        if results is None:
            results = self.get_simulation_results()
            
        if results is None or results['asset_data'].empty:
            print("⚠️ 没有可视化的数据")
            return
        
        # 设置样式
        if SEABORN_AVAILABLE:
            sns.set_style("whitegrid")
        
        # 创建图表
        fig, axes = plt.subplots(2, 1, figsize=(12, 10), sharex=True)
        
        # 获取唯一资产ID
        asset_df = results['asset_data']
        asset_ids = asset_df['asset_id'].unique()
        
        # 为每个资产分配颜色
        colors = plt.cm.tab10(np.linspace(0, 1, len(asset_ids)))
        
        # 绘制资产应力
        for i, asset_id in enumerate(asset_ids):
            asset_data = asset_df[asset_df['asset_id'] == asset_id]
            axes[0].plot(asset_data['timestamp'], asset_data['current_stress'], 
                       '-', color=colors[i], label=f'资产 {i+1}')
        
        axes[0].set_title('资产应力时间线')
        axes[0].set_ylabel('应力水平')
        axes[0].legend()
        
        # 绘制物理状态
        for i, asset_id in enumerate(asset_ids):
            asset_data = asset_df[asset_df['asset_id'] == asset_id]
            if 'physical_condition' in asset_data.columns:
                axes[1].plot(asset_data['timestamp'], asset_data['physical_condition'], 
                           '-', color=colors[i], label=f'资产 {i+1}')
        
        axes[1].set_title('资产物理状态时间线')
        axes[1].set_ylabel('物理状态 (0-1)')
        axes[1].set_xlabel('时间')
        axes[1].legend()
        
        # 格式化x轴日期
        fig.autofmt_xdate()
        
        plt.tight_layout()
        plt.show()
        
        return fig
    
    def visualize_bagua_stress(self, timestamp_index=-1):
        """可视化八卦应力场"""
        if not self.analysis_history:
            print("⚠️ 没有可视化的数据")
            return
        
        # 获取指定时间点的应力场
        if timestamp_index == -1:
            # 使用最新的应力场
            title = "当前八卦应力场"
        else:
            # 使用指定索引的应力场
            title = f"时间点 {self.analysis_history[timestamp_index]['timestamp']} 的八卦应力场"
        
        # 创建图表
        fig, ax = plt.subplots(figsize=(10, 10))
        self.stress_opposition.visualize_stress_field(ax, title)
        
        plt.tight_layout()
        plt.show()
        
        return fig
    
    def animate_stress_field(self, interval=200):
        """创建八卦应力场动画"""
        if not self.analysis_history or len(self.analysis_history) < 2:
            print("⚠️ 没有足够的数据创建动画")
            return
        
        # 创建图表
        fig, ax = plt.subplots(figsize=(10, 10))
        
        # 初始化函数
        def init():
            ax.clear()
            return []
        
        # 动画更新函数
        def update(frame):
            ax.clear()
            # 恢复历史应力场状态
            history_point = self.analysis_history[frame]
            self.stress_opposition.stress_field = {k: v for k, v in zip(
                BaGuaStress, 
                [history_point['market_data'].get(s.value, 0) for s in BaGuaStress]
            )}
            
            # 可视化
            title = f"八卦应力场 - {history_point['timestamp'].strftime('%Y-%m-%d')}"
            self.stress_opposition.visualize_stress_field(ax, title)
            return []
        
        # 创建动画
        ani = animation.FuncAnimation(
            fig, update, frames=len(self.analysis_history),
            init_func=init, blit=True, interval=interval
        )
        
        plt.tight_layout()
        plt.show()
        
        return ani
    
    def predict_company_failure(self, company_id, future_days=365, stress_scenario="baseline"):
        """预测公司失效时间"""
        assets = self.asset_factory.get_company_assets(company_id)
        if not assets:
            print(f"⚠️ 找不到公司 {company_id} 的资产")
            return None
        
        # 生成未来应力场景
        if stress_scenario == "baseline":
            # 基准情景：当前趋势延续
            volatility = 0.2
            trend = 0
        elif stress_scenario == "deteriorating":
            # 恶化情景：应力逐渐增加
            volatility = 0.3
            trend = 0.01
        elif stress_scenario == "improving":
            # 改善情景：应力逐渐减少
            volatility = 0.1
            trend = -0.01
        elif stress_scenario == "shock":
            # 冲击情景：突然应力增加
            volatility = 0.5
            trend = 0.05
        else:
            volatility = 0.2
            trend = 0
        
        # 生成未来应力序列
        future_stress = []
        for i in range(future_days):
            day_trend = trend * i
            market_data = self.generate_market_stress(volatility, day_trend)
            future_stress.append(market_data.get('volatility', 0.3))
        
        # 预测每个资产的失效时间
        asset_failures = {}
        for asset in assets:
            days = asset.predict_failure(future_stress)
            asset_failures[asset.asset_id] = {
                'asset_name': asset.name,
                'asset_type': asset.asset_type.value,
                'days_to_failure': days,
                'failure_date': datetime.now() + timedelta(days=days) if days else None,
                'current_condition': asset.physical_condition,
                'current_stress': asset.current_stress
            }
        
        # 计算公司整体失效时间（最早的关键资产失效）
        critical_failures = []
        for asset_id, failure in asset_failures.items():
            asset = next((a for a in assets if a.asset_id == asset_id), None)
            if asset and asset.market_value > 0.2 * sum(a.market_value for a in assets):
                # 关键资产：价值超过总资产20%
                if failure['days_to_failure']:
                    critical_failures.append(failure)
        
        # 按失效时间排序
        critical_failures.sort(key=lambda x: x['days_to_failure'] if x['days_to_failure'] else float('inf'))
        
        # 生成预测报告
        report = {
            'company_id': company_id,
            'prediction_date': datetime.now(),
            'stress_scenario': stress_scenario,
            'asset_failures': asset_failures,
            'critical_failures': critical_failures,
            'company_failure': critical_failures[0] if critical_failures else None
        }
        
        return report
    
    def print_failure_prediction(self, prediction):
        """打印失效预测报告"""
        if not prediction:
            print("⚠️ 没有预测数据")
            return
        
        print("\n" + "=" * 60)
        print(f"📊 公司失效预测报告 - {prediction['company_id']}")
        print("=" * 60)
        print(f"预测日期: {prediction['prediction_date'].strftime('%Y-%m-%d')}")
        print(f"应力场景: {prediction['stress_scenario']}")
        
        if prediction['company_failure']:
            failure = prediction['company_failure']
            print(f"\n⚠️ 公司预计失效时间: {failure['days_to_failure']} 天后")
            print(f"失效日期: {failure['failure_date'].strftime('%Y-%m-%d')}")
            print(f"失效触发资产: {failure['asset_name']} ({failure['asset_type']})")
        else:
            print("\n✅ 在预测期内，公司不会发生系统性失效")
        
        print("\n📋 资产失效预测:")
        for asset_id, failure in prediction['asset_failures'].items():
            status = "🔴" if failure['days_to_failure'] and failure['days_to_failure'] < 90 else \
                    "🟡" if failure['days_to_failure'] and failure['days_to_failure'] < 180 else "🟢"
            
            failure_info = f"{failure['days_to_failure']} 天后" if failure['days_to_failure'] else "预测期内不会失效"
            print(f"  {status} {failure['asset_name']} ({failure['asset_type']}): {failure_info}")
            print(f"      当前状态: {failure['current_condition']:.2f}, 当前应力: {failure['current_stress']:.2f}")
        
        print("\n💡 风险缓解建议:")
        high_risk_assets = [f for f in prediction['asset_failures'].values() 
                          if f['days_to_failure'] and f['days_to_failure'] < 180]
        if high_risk_assets:
            print("  1. 优先处理高风险资产:")
            for asset in high_risk_assets:
                print(f"     - {asset['asset_name']}: 应在 {asset['days_to_failure'] // 2} 天内完成减压或加固")
            
            print("  2. 实施系统性应力管理:")
            print("     - 建立应力监控预警机制")
            print("     - 制定资产轮换计划")
            print("     - 优化资产组合结构")
        else:
            print("  1. 维持当前资产管理策略")
            print("  2. 定期进行应力测试")
            print("  3. 建立预防性维护计划")

# ==================== 实时监控系统 ====================

class RealTimeMonitor:
    """实时市场应力监控系统"""
    
    def __init__(self, analyzer):
        self.analyzer = analyzer
        self.monitoring = False
        self.alert_thresholds = {
            'net_stress': 0.8,
            'asset_stress': 0.7,
            'physical_condition': 0.3
        }
    
    def start_monitoring(self, company_id, interval_seconds=5, max_iterations=10):
        """开始实时监控"""
        self.monitoring = True
        iteration = 0
        
        print(f"🔍 开始实时监控公司 {company_id}...")
        print(f"⏱️ 监控间隔: {interval_seconds} 秒")
        print(f"🔄 最大迭代次数: {max_iterations}")
        print("按 Ctrl+C 停止监控\n")
        
        try:
            while self.monitoring and (max_iterations == 0 or iteration < max_iterations):
                # 生成市场数据
                market_data = self.analyzer.generate_market_stress(
                    volatility=0.1 + random.random() * 0.3,
                    trend=random.uniform(-0.1, 0.1)
                )
                
                # 分析公司
                result = self.analyzer.analyze_company(company_id, market_data)
                
                # 检查警报
                alerts = self.check_alerts(result)
                
                # 打印状态
                self.print_monitoring_status(result, alerts)
                
                # 等待下一次迭代
                time.sleep(interval_seconds)
                iteration += 1
                
        except KeyboardInterrupt:
            print("\n⏹️ 监控已停止")
        finally:
            self.monitoring = False
            print(f"✅ 监控完成，共执行 {iteration} 次迭代")
    
    def check_alerts(self, result):
        """检查是否需要发出警报"""
        alerts = []
        
        # 检查净应力
        if abs(result['net_stress']) > self.alert_thresholds['net_stress']:
            alerts.append({
                'type': 'net_stress',
                'level': 'high',
                'message': f"净应力超过阈值: {result['net_stress']:.2f}"
            })
        
        # 检查资产应力
        for asset_id, asset_info in result['stress_analysis'].items():
            if asset_id != 'company_summary':
                if asset_info['current_stress'] > self.alert_thresholds['asset_stress']:
                    alerts.append({
                        'type': 'asset_stress',
                        'asset_id': asset_id,
                        'level': 'high',
                        'message': f"资产 {asset_id} 应力超过阈值: {asset_info['current_stress']:.2f}"
                    })
                
                if 'physical_condition' in asset_info and \
                   asset_info['physical_condition'] < self.alert_thresholds['physical_condition']:
                    alerts.append({
                        'type': 'physical_condition',
                        'asset_id': asset_id,
                        'level': 'critical',
                        'message': f"资产 {asset_id} 物理状态低于阈值: {asset_info['physical_condition']:.2f}"
                    })
        
        return alerts
    
    def print_monitoring_status(self, result, alerts):
        """打印监控状态"""
        timestamp = result['timestamp'].strftime('%Y-%m-%d %H:%M:%S')
        dominant_stress, stress_intensity = result['dominant_stress']
        
        print(f"\n[{timestamp}] 监控状态更新")
        print(f"净应力: {result['net_stress']:.2f}")
        print(f"主导应力: {dominant_stress.value} ({stress_intensity:.2f})")
        
        # 打印警报
        if alerts:
            print("\n⚠️ 警报:")
            for alert in alerts:
                level_icon = "🔴" if alert['level'] == 'critical' else "🟠" if alert['level'] == 'high' else "🟡"
                print(f"  {level_icon} {alert['message']}")
        else:
            print("\n✅ 所有指标正常")

# ==================== 命令行界面 ====================

def main():
    """主函数"""
    print("=" * 80)
    print("🏔️ 岩石力学金融分析工具 v1.0")
    print("=" * 80)
    
    # 解析命令行参数
    import argparse
    parser = argparse.ArgumentParser(description="岩石力学金融分析工具")
    parser.add_argument("company_id", nargs="?", default="AUTO_DEMO", help="公司ID")
    parser.add_argument("--visual", action="store_true", help="启用可视化")
    parser.add_argument("--monitor", action="store_true", help="启用实时监控")
    parser.add_argument("--days", type=int, default=30, help="模拟天数")
    parser.add_argument("--scenario", choices=["baseline", "deteriorating", "improving", "shock"], 
                      default="baseline", help="压力场景")
    args = parser.parse_args()
    
    # 初始化分析器
    analyzer = RockMechanicsAnalyzer()
    
    # 创建演示公司或使用指定公司
    company_id = args.company_id
    if company_id == "AUTO_DEMO":
        company_id = analyzer.create_demo_company()
    
    # 运行模拟
    if args.scenario == "baseline":
        results = analyzer.run_stress_simulation(company_id, args.days, 0.2, 0)
    elif args.scenario == "deteriorating":
        results = analyzer.run_stress_simulation(company_id, args.days, 0.3, 0.01)
    elif args.scenario == "improving":
        results = analyzer.run_stress_simulation(company_id, args.days, 0.1, -0.01)
    elif args.scenario == "shock":
        results = analyzer.run_stress_simulation(company_id, args.days, 0.5, 0.05)
    
    # 预测失效
    prediction = analyzer.predict_company_failure(company_id, 365, args.scenario)
    analyzer.print_failure_prediction(prediction)
    
    # 可视化
    if args.visual:
        print("\n📊 生成可视化图表...")
        analyzer.visualize_stress_timeline(results)
        analyzer.visualize_asset_stress(results)
        analyzer.visualize_bagua_stress()
        
        # 如果模拟天数大于1，创建动画
        if args.days > 1:
            print("\n🎬 生成八卦应力场动画...")
            analyzer.animate_stress_field()
    
    # 实时监控
    if args.monitor:
        monitor = RealTimeMonitor(analyzer)
        monitor.start_monitoring(company_id, 2, 10)
    
    print("\n✅ 分析完成")

if __name__ == "__main__":
    main()