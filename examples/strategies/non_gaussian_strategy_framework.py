#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
太乙观澜：非高斯分布下的至尊会员策略框架
Non-Gaussian Strategy Framework for Supreme Members

当现实脱离正态分布假设时的高级投资策略模型
Advanced investment strategy model when reality deviates from normal distribution assumptions
"""

import numpy as np
import pandas as pd
from scipy import stats
from scipy.optimize import minimize
import matplotlib.pyplot as plt
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass
from enum import Enum
import warnings
warnings.filterwarnings('ignore')

class DistributionRegime(Enum):
    """分布状态枚举"""
    GAUSSIAN = "高斯正态"
    FAT_TAIL = "厚尾分布"
    SKEWED = "偏态分布"
    BIMODAL = "双峰分布"
    LEVY_STABLE = "列维稳定"
    CHAOS = "混沌状态"

@dataclass
class MarketRegime:
    """市场状态数据类"""
    regime_type: DistributionRegime
    volatility: float
    skewness: float
    kurtosis: float
    tail_index: float
    fractal_dimension: float
    hurst_exponent: float
    confidence: float

class NonGaussianMarkovChain:
    """
    非高斯马尔可夫链模型
    处理现实中的非正态分布、厚尾事件和结构性断裂
    """
    
    def __init__(self):
        self.states = [
            "极度乐观", "乐观", "中性", "悲观", "极度悲观", 
            "黑天鹅", "灰犀牛", "结构性断裂"
        ]
        self.regimes = {}
        self.transition_tensors = {}  # 多维转移张量
        self.jump_processes = {}      # 跳跃过程参数
        
    def detect_distribution_regime(self, returns: np.ndarray) -> MarketRegime:
        """
        检测当前市场的分布状态
        """
        # 基础统计量
        volatility = np.std(returns)
        skewness = stats.skew(returns)
        kurtosis = stats.kurtosis(returns)
        
        # 厚尾检验
        tail_index = self._estimate_tail_index(returns)
        
        # 分形维度
        fractal_dim = self._calculate_fractal_dimension(returns)
        
        # Hurst指数
        hurst = self._calculate_hurst_exponent(returns)
        
        # 分布状态判断
        regime_type = self._classify_regime(skewness, kurtosis, tail_index)
        
        # 置信度评估
        confidence = self._calculate_regime_confidence(returns, regime_type)
        
        return MarketRegime(
            regime_type=regime_type,
            volatility=volatility,
            skewness=skewness,
            kurtosis=kurtosis,
            tail_index=tail_index,
            fractal_dimension=fractal_dim,
            hurst_exponent=hurst,
            confidence=confidence
        )
    
    def _estimate_tail_index(self, returns: np.ndarray) -> float:
        """
        估计尾部指数（Hill估计量）
        """
        sorted_returns = np.sort(np.abs(returns))[::-1]
        n = len(sorted_returns)
        k = int(n * 0.1)  # 使用前10%的极值
        
        if k < 2:
            return 2.0  # 默认值
            
        log_ratios = np.log(sorted_returns[:k] / sorted_returns[k])
        tail_index = 1.0 / np.mean(log_ratios)
        
        return max(0.5, min(tail_index, 10.0))  # 限制在合理范围内
    
    def _calculate_fractal_dimension(self, returns: np.ndarray) -> float:
        """
        计算分形维度
        """
        # 使用盒计数法的简化版本
        cumulative = np.cumsum(returns)
        n = len(cumulative)
        
        scales = np.logspace(1, np.log10(n//4), 10).astype(int)
        counts = []
        
        for scale in scales:
            boxes = n // scale
            if boxes < 2:
                continue
                
            box_ranges = []
            for i in range(boxes):
                start, end = i * scale, (i + 1) * scale
                if end <= n:
                    box_range = np.max(cumulative[start:end]) - np.min(cumulative[start:end])
                    box_ranges.append(box_range)
            
            if box_ranges:
                counts.append(np.mean(box_ranges))
        
        if len(counts) < 3:
            return 1.5  # 默认值
            
        # 线性回归估计分形维度
        log_scales = np.log(scales[:len(counts)])
        log_counts = np.log(counts)
        
        slope, _ = np.polyfit(log_scales, log_counts, 1)
        fractal_dim = 2 - slope
        
        return max(1.0, min(fractal_dim, 2.0))
    
    def _calculate_hurst_exponent(self, returns: np.ndarray) -> float:
        """
        计算Hurst指数
        """
        cumulative = np.cumsum(returns - np.mean(returns))
        n = len(cumulative)
        
        # R/S分析
        lags = np.logspace(1, np.log10(n//4), 10).astype(int)
        rs_values = []
        
        for lag in lags:
            if lag >= n:
                continue
                
            segments = n // lag
            rs_segment = []
            
            for i in range(segments):
                start, end = i * lag, (i + 1) * lag
                segment = cumulative[start:end]
                
                if len(segment) > 1:
                    range_val = np.max(segment) - np.min(segment)
                    std_val = np.std(returns[start:end])
                    
                    if std_val > 0:
                        rs_segment.append(range_val / std_val)
            
            if rs_segment:
                rs_values.append(np.mean(rs_segment))
        
        if len(rs_values) < 3:
            return 0.5  # 默认值（随机游走）
            
        # 线性回归估计Hurst指数
        log_lags = np.log(lags[:len(rs_values)])
        log_rs = np.log(rs_values)
        
        hurst, _ = np.polyfit(log_lags, log_rs, 1)
        
        return max(0.0, min(hurst, 1.0))
    
    def _classify_regime(self, skewness: float, kurtosis: float, tail_index: float) -> DistributionRegime:
        """
        分类分布状态
        """
        # 正态性检验阈值
        if abs(skewness) < 0.5 and abs(kurtosis) < 1.0 and tail_index > 3.0:
            return DistributionRegime.GAUSSIAN
        
        # 厚尾检验
        if tail_index < 2.5:
            return DistributionRegime.FAT_TAIL
        
        # 偏态检验
        if abs(skewness) > 1.0:
            return DistributionRegime.SKEWED
        
        # 峰度检验（双峰或平顶）
        if kurtosis < -1.0:
            return DistributionRegime.BIMODAL
        
        # 列维稳定分布
        if tail_index < 2.0 and kurtosis > 5.0:
            return DistributionRegime.LEVY_STABLE
        
        # 混沌状态
        if kurtosis > 10.0 or abs(skewness) > 2.0:
            return DistributionRegime.CHAOS
        
        return DistributionRegime.FAT_TAIL  # 默认
    
    def _calculate_regime_confidence(self, returns: np.ndarray, regime: DistributionRegime) -> float:
        """
        计算状态识别的置信度
        """
        # 使用Kolmogorov-Smirnov检验
        if regime == DistributionRegime.GAUSSIAN:
            _, p_value = stats.kstest(returns, 'norm')
        else:
            # 对于非正态分布，使用经验分布的稳定性
            n = len(returns)
            split_point = n // 2
            
            first_half = returns[:split_point]
            second_half = returns[split_point:]
            
            _, p_value = stats.ks_2samp(first_half, second_half)
        
        return p_value

class SupremeMemberStrategy:
    """
    至尊会员策略：非高斯环境下的投资决策框架
    """
    
    def __init__(self):
        self.markov_chain = NonGaussianMarkovChain()
        self.risk_budgets = {
            DistributionRegime.GAUSSIAN: 0.15,      # 正态分布：15%风险预算
            DistributionRegime.FAT_TAIL: 0.08,      # 厚尾分布：8%风险预算
            DistributionRegime.SKEWED: 0.10,        # 偏态分布：10%风险预算
            DistributionRegime.BIMODAL: 0.12,       # 双峰分布：12%风险预算
            DistributionRegime.LEVY_STABLE: 0.05,   # 列维稳定：5%风险预算
            DistributionRegime.CHAOS: 0.03          # 混沌状态：3%风险预算
        }
        
    def generate_strategy_recommendation(self, market_data: pd.DataFrame) -> Dict:
        """
        生成策略建议
        """
        returns = market_data['returns'].values
        regime = self.markov_chain.detect_distribution_regime(returns)
        
        # 基于分布状态的策略调整
        strategy = self._regime_based_strategy(regime)
        
        # 风险管理
        risk_controls = self._adaptive_risk_controls(regime)
        
        # 仓位管理
        position_sizing = self._dynamic_position_sizing(regime)
        
        # 对冲策略
        hedging_strategy = self._tail_risk_hedging(regime)
        
        return {
            'market_regime': regime,
            'strategy': strategy,
            'risk_controls': risk_controls,
            'position_sizing': position_sizing,
            'hedging': hedging_strategy,
            'confidence_level': regime.confidence
        }
    
    def _regime_based_strategy(self, regime: MarketRegime) -> Dict:
        """
        基于分布状态的策略选择
        """
        strategies = {
            DistributionRegime.GAUSSIAN: {
                'type': '经典均值回归',
                'approach': 'mean_reversion',
                'instruments': ['股票', '债券', '期权'],
                'allocation': {'equity': 0.6, 'bonds': 0.3, 'alternatives': 0.1}
            },
            
            DistributionRegime.FAT_TAIL: {
                'type': '厚尾保护策略',
                'approach': 'tail_protection',
                'instruments': ['保护性看跌期权', '波动率产品', '黄金'],
                'allocation': {'equity': 0.4, 'bonds': 0.3, 'protection': 0.2, 'alternatives': 0.1}
            },
            
            DistributionRegime.SKEWED: {
                'type': '偏态套利策略',
                'approach': 'skewness_arbitrage',
                'instruments': ['偏态期权', '结构化产品'],
                'allocation': {'equity': 0.5, 'bonds': 0.2, 'structured': 0.2, 'cash': 0.1}
            },
            
            DistributionRegime.BIMODAL: {
                'type': '双峰对冲策略',
                'approach': 'bimodal_hedge',
                'instruments': ['跨式期权', '蝶式价差'],
                'allocation': {'equity': 0.3, 'bonds': 0.3, 'options': 0.3, 'cash': 0.1}
            },
            
            DistributionRegime.LEVY_STABLE: {
                'type': '列维跳跃策略',
                'approach': 'jump_trading',
                'instruments': ['短期期权', '高频策略'],
                'allocation': {'equity': 0.2, 'bonds': 0.4, 'short_term': 0.3, 'cash': 0.1}
            },
            
            DistributionRegime.CHAOS: {
                'type': '混沌生存策略',
                'approach': 'chaos_survival',
                'instruments': ['现金', '黄金', '加密货币'],
                'allocation': {'cash': 0.5, 'gold': 0.3, 'crypto': 0.1, 'bonds': 0.1}
            }
        }
        
        return strategies.get(regime.regime_type, strategies[DistributionRegime.FAT_TAIL])
    
    def _adaptive_risk_controls(self, regime: MarketRegime) -> Dict:
        """
        自适应风险控制
        """
        base_var = 0.05  # 基础VaR 5%
        
        # 根据分布特征调整VaR
        if regime.regime_type == DistributionRegime.FAT_TAIL:
            var_multiplier = 1.5
        elif regime.regime_type == DistributionRegime.CHAOS:
            var_multiplier = 2.0
        else:
            var_multiplier = 1.0
        
        adjusted_var = base_var * var_multiplier
        
        # 动态止损
        if regime.hurst_exponent > 0.6:  # 趋势性强
            stop_loss = 0.08
        elif regime.hurst_exponent < 0.4:  # 均值回归
            stop_loss = 0.12
        else:
            stop_loss = 0.10
        
        return {
            'var_limit': adjusted_var,
            'stop_loss': stop_loss,
            'max_drawdown': 0.15,
            'correlation_limit': 0.7,
            'leverage_limit': self._calculate_max_leverage(regime)
        }
    
    def _calculate_max_leverage(self, regime: MarketRegime) -> float:
        """
        计算最大杠杆倍数
        """
        base_leverage = 2.0
        
        # 根据尾部指数调整
        if regime.tail_index < 2.0:
            leverage_factor = 0.5
        elif regime.tail_index < 3.0:
            leverage_factor = 0.7
        else:
            leverage_factor = 1.0
        
        # 根据波动率调整
        vol_factor = min(1.0, 0.2 / regime.volatility)
        
        return base_leverage * leverage_factor * vol_factor
    
    def _dynamic_position_sizing(self, regime: MarketRegime) -> Dict:
        """
        动态仓位管理
        """
        risk_budget = self.risk_budgets[regime.regime_type]
        
        # Kelly公式的修正版本（考虑非正态分布）
        if regime.regime_type == DistributionRegime.GAUSSIAN:
            kelly_fraction = 0.25  # 标准Kelly的1/4
        else:
            # 对于非正态分布，更加保守
            kelly_fraction = 0.1 / (1 + abs(regime.skewness) + regime.kurtosis/10)
        
        return {
            'risk_budget': risk_budget,
            'kelly_fraction': kelly_fraction,
            'max_single_position': min(0.1, kelly_fraction * 2),
            'rebalance_frequency': self._get_rebalance_frequency(regime)
        }
    
    def _get_rebalance_frequency(self, regime: MarketRegime) -> str:
        """
        获取再平衡频率
        """
        if regime.regime_type in [DistributionRegime.CHAOS, DistributionRegime.LEVY_STABLE]:
            return 'daily'
        elif regime.regime_type == DistributionRegime.FAT_TAIL:
            return 'weekly'
        else:
            return 'monthly'
    
    def _tail_risk_hedging(self, regime: MarketRegime) -> Dict:
        """
        尾部风险对冲
        """
        if regime.tail_index < 2.5:  # 厚尾分布
            hedge_ratio = 0.1
            instruments = ['VIX期权', '尾部风险基金', '黄金']
        elif regime.regime_type == DistributionRegime.CHAOS:
            hedge_ratio = 0.2
            instruments = ['现金', '国债', '黄金', '加密货币']
        else:
            hedge_ratio = 0.05
            instruments = ['保护性看跌期权']
        
        return {
            'hedge_ratio': hedge_ratio,
            'instruments': instruments,
            'trigger_conditions': self._get_hedge_triggers(regime)
        }
    
    def _get_hedge_triggers(self, regime: MarketRegime) -> List[str]:
        """
        获取对冲触发条件
        """
        triggers = ['VIX > 25', '相关性 > 0.8']
        
        if regime.regime_type == DistributionRegime.FAT_TAIL:
            triggers.append('尾部事件概率 > 5%')
        elif regime.regime_type == DistributionRegime.CHAOS:
            triggers.append('分形维度 < 1.2')
        
        return triggers

def demonstrate_supreme_strategy():
    """
    演示至尊会员策略
    """
    print("🎭 太乙观澜：非高斯分布下的至尊会员策略")
    print("=" * 50)
    
    # 模拟不同分布状态的市场数据
    np.random.seed(42)
    
    scenarios = {
        '正态市场': np.random.normal(0.001, 0.02, 252),
        '厚尾市场': np.random.standard_t(3, 252) * 0.02,
        '偏态市场': np.random.gamma(2, 0.01, 252) - 0.02,
        '混沌市场': np.concatenate([
            np.random.normal(0.01, 0.01, 126),
            np.random.normal(-0.02, 0.05, 126)
        ])
    }
    
    strategy_engine = SupremeMemberStrategy()
    
    for scenario_name, returns in scenarios.items():
        print(f"\n📊 {scenario_name}分析:")
        print("-" * 30)
        
        # 创建市场数据
        market_data = pd.DataFrame({
            'returns': returns,
            'price': np.cumprod(1 + returns)
        })
        
        # 生成策略建议
        recommendation = strategy_engine.generate_strategy_recommendation(market_data)
        
        regime = recommendation['market_regime']
        print(f"分布状态: {regime.regime_type.value}")
        print(f"波动率: {regime.volatility:.4f}")
        print(f"偏度: {regime.skewness:.4f}")
        print(f"峰度: {regime.kurtosis:.4f}")
        print(f"尾部指数: {regime.tail_index:.4f}")
        print(f"Hurst指数: {regime.hurst_exponent:.4f}")
        print(f"置信度: {regime.confidence:.4f}")
        
        strategy = recommendation['strategy']
        print(f"\n推荐策略: {strategy['type']}")
        print(f"资产配置: {strategy['allocation']}")
        
        risk_controls = recommendation['risk_controls']
        print(f"\n风险控制:")
        print(f"  VaR限制: {risk_controls['var_limit']:.2%}")
        print(f"  止损线: {risk_controls['stop_loss']:.2%}")
        print(f"  最大杠杆: {risk_controls['leverage_limit']:.2f}x")
        
        position = recommendation['position_sizing']
        print(f"\n仓位管理:")
        print(f"  风险预算: {position['risk_budget']:.2%}")
        print(f"  Kelly比例: {position['kelly_fraction']:.2%}")
        print(f"  再平衡: {position['rebalance_frequency']}")
        
        hedging = recommendation['hedging']
        print(f"\n对冲策略:")
        print(f"  对冲比例: {hedging['hedge_ratio']:.2%}")
        print(f"  对冲工具: {', '.join(hedging['instruments'])}")

if __name__ == "__main__":
    demonstrate_supreme_strategy()
    
    print("\n" + "=" * 60)
    print("🎯 太乙观澜核心洞察:")
    print("\n1. 🔍 分布识别：准确识别市场的真实分布状态")
    print("2. 🎭 策略适配：根据分布特征动态调整投资策略")
    print("3. 🛡️ 风险控制：非正态分布下的高级风险管理")
    print("4. 📊 仓位优化：考虑厚尾和偏态的动态仓位管理")
    print("5. 🎪 尾部对冲：专门针对极端事件的保护机制")
    print("\n当现实脱离正态分布时，传统的投资理论失效。")
    print("太乙观澜的智慧在于：拥抱不确定性，在混沌中寻找秩序！")