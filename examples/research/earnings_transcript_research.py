#!/usr/bin/env python3
"""
财报Transcript深度研究工具
专门分析公司财报电话会议的关键信息和趋势
"""

import requests
import json
from datetime import datetime, timedelta
import os
import re

class EarningsTranscriptResearcher:
    def __init__(self, api_key=None):
        """
        初始化财报研究器
        
        Args:
            api_key: Alpha Vantage API密钥
        """
        self.api_key = api_key or os.getenv('ALPHA_VANTAGE_API', '4HDGAT4ZO02QM1KY')
        self.base_url = "https://www.alphavantage.co/query"
    
    def get_detailed_earnings_analysis(self, symbol):
        """获取详细的财报分析"""
        print(f"\n📊 {symbol} 财报深度分析")
        print("=" * 50)
        
        # 获取财报数据
        earnings = self._get_earnings_data(symbol)
        overview = self._get_company_overview(symbol)
        income_statement = self._get_income_statement(symbol)
        
        # 分析财报趋势
        self._analyze_earnings_trends(earnings)
        
        # 分析财务健康状况
        self._analyze_financial_health(overview, income_statement)
        
        # 获取相关新闻和情绪
        self._analyze_earnings_sentiment(symbol)
        
        return {
            'earnings': earnings,
            'overview': overview,
            'income_statement': income_statement
        }
    
    def _get_earnings_data(self, symbol):
        """获取财报数据"""
        params = {
            'function': 'EARNINGS',
            'symbol': symbol,
            'apikey': self.api_key
        }
        response = requests.get(self.base_url, params=params)
        return response.json()
    
    def _get_company_overview(self, symbol):
        """获取公司概览"""
        params = {
            'function': 'OVERVIEW',
            'symbol': symbol,
            'apikey': self.api_key
        }
        response = requests.get(self.base_url, params=params)
        return response.json()
    
    def _get_income_statement(self, symbol):
        """获取损益表"""
        params = {
            'function': 'INCOME_STATEMENT',
            'symbol': symbol,
            'apikey': self.api_key
        }
        response = requests.get(self.base_url, params=params)
        return response.json()
    
    def _analyze_earnings_trends(self, earnings_data):
        """分析财报趋势"""
        print("\n📈 财报趋势分析:")
        
        if 'quarterlyEarnings' not in earnings_data:
            print("❌ 无法获取季度财报数据")
            return
        
        quarters = earnings_data['quarterlyEarnings'][:8]  # 最近8个季度
        
        # 分析EPS趋势
        eps_trend = []
        surprise_trend = []
        
        for quarter in quarters:
            try:
                eps = float(quarter['reportedEPS'])
                surprise = float(quarter['surprisePercentage'])
                eps_trend.append(eps)
                surprise_trend.append(surprise)
            except (ValueError, TypeError):
                continue
        
        if eps_trend:
            print(f"  📊 EPS趋势 (最近8季度):")
            for i, quarter in enumerate(quarters[:len(eps_trend)]):
                trend_indicator = "📈" if i > 0 and eps_trend[i] > eps_trend[i-1] else "📉" if i > 0 and eps_trend[i] < eps_trend[i-1] else "➡️"
                print(f"    {quarter['fiscalDateEnding']}: ${eps_trend[i]:.2f} {trend_indicator}")
        
        if surprise_trend:
            avg_surprise = sum(surprise_trend) / len(surprise_trend)
            positive_surprises = sum(1 for s in surprise_trend if s > 0)
            print(f"\n  🎯 惊喜表现:")
            print(f"    平均惊喜: {avg_surprise:.2f}%")
            print(f"    正面惊喜次数: {positive_surprises}/{len(surprise_trend)}")
            
            if avg_surprise > 5:
                print("    ✅ 持续超预期表现")
            elif avg_surprise > 0:
                print("    ⚡ 稳定超预期表现")
            else:
                print("    ⚠️ 表现低于预期")
    
    def _analyze_financial_health(self, overview, income_statement):
        """分析财务健康状况"""
        print("\n💰 财务健康状况:")
        
        if 'Symbol' not in overview:
            print("❌ 无法获取公司概览数据")
            return
        
        # 关键财务指标
        metrics = {
            'P/E比率': overview.get('PERatio', 'N/A'),
            'PEG比率': overview.get('PEGRatio', 'N/A'),
            'ROE': overview.get('ReturnOnEquityTTM', 'N/A'),
            'ROA': overview.get('ReturnOnAssetsTTM', 'N/A'),
            '利润率': overview.get('ProfitMargin', 'N/A'),
            '毛利率': overview.get('GrossProfitTTM', 'N/A'),
            '债务股本比': overview.get('DebtToEquityRatio', 'N/A'),
            '流动比率': overview.get('CurrentRatio', 'N/A')
        }
        
        for metric, value in metrics.items():
            if value != 'N/A':
                try:
                    val = float(value)
                    if metric == 'P/E比率':
                        status = "✅ 合理" if 10 <= val <= 25 else "⚠️ 需关注" if val > 30 else "🔍 低估值"
                    elif metric == 'ROE':
                        status = "✅ 优秀" if val > 0.15 else "⚡ 良好" if val > 0.10 else "⚠️ 一般"
                    elif metric == '利润率':
                        status = "✅ 优秀" if val > 0.20 else "⚡ 良好" if val > 0.10 else "⚠️ 一般"
                    else:
                        status = ""
                    
                    print(f"  {metric}: {value} {status}")
                except ValueError:
                    print(f"  {metric}: {value}")
            else:
                print(f"  {metric}: 数据不可用")
    
    def _analyze_earnings_sentiment(self, symbol):
        """分析财报相关情绪"""
        print("\n📰 财报相关新闻情绪:")
        
        # 获取最近的新闻
        params = {
            'function': 'NEWS_SENTIMENT',
            'tickers': symbol,
            'limit': 20,
            'apikey': self.api_key
        }
        
        response = requests.get(self.base_url, params=params)
        news_data = response.json()
        
        if 'feed' not in news_data:
            print("❌ 无法获取新闻数据")
            return
        
        # 筛选财报相关新闻
        earnings_news = []
        earnings_keywords = ['earnings', 'quarterly', 'revenue', 'profit', 'eps', 'guidance', 'forecast']
        
        for article in news_data['feed']:
            title_lower = article['title'].lower()
            summary_lower = article.get('summary', '').lower()
            
            if any(keyword in title_lower or keyword in summary_lower for keyword in earnings_keywords):
                earnings_news.append(article)
        
        if earnings_news:
            print(f"  找到 {len(earnings_news)} 条财报相关新闻:")
            
            sentiment_scores = []
            for article in earnings_news[:5]:  # 显示前5条
                sentiment = article['overall_sentiment_label']
                score = article['overall_sentiment_score']
                sentiment_scores.append(score)
                
                print(f"\n  📄 {article['title'][:60]}...")
                print(f"     情绪: {sentiment} ({score:.3f})")
                print(f"     时间: {article['time_published']}")
            
            # 计算平均情绪
            if sentiment_scores:
                avg_sentiment = sum(sentiment_scores) / len(sentiment_scores)
                if avg_sentiment > 0.15:
                    sentiment_label = "🟢 积极"
                elif avg_sentiment > -0.15:
                    sentiment_label = "🟡 中性"
                else:
                    sentiment_label = "🔴 消极"
                
                print(f"\n  📊 财报新闻平均情绪: {avg_sentiment:.3f} {sentiment_label}")
        else:
            print("  未找到财报相关新闻")
    
    def compare_earnings_performance(self, symbols):
        """比较多个公司的财报表现"""
        print(f"\n🔄 财报表现对比: {', '.join(symbols)}")
        print("=" * 60)
        
        comparison_data = []
        
        for symbol in symbols:
            earnings = self._get_earnings_data(symbol)
            overview = self._get_company_overview(symbol)
            
            if 'quarterlyEarnings' in earnings and earnings['quarterlyEarnings']:
                latest = earnings['quarterlyEarnings'][0]
                
                comparison_data.append({
                    'Symbol': symbol,
                    'Latest_EPS': latest.get('reportedEPS', 'N/A'),
                    'Surprise_%': latest.get('surprisePercentage', 'N/A'),
                    'Report_Date': latest.get('reportedDate', 'N/A'),
                    'PE_Ratio': overview.get('PERatio', 'N/A'),
                    'Profit_Margin': overview.get('ProfitMargin', 'N/A')
                })
        
        # 显示对比表格
        if comparison_data:
            print(f"{'Symbol':<8} {'Latest_EPS':<12} {'Surprise_%':<12} {'PE_Ratio':<10} {'Profit_Margin':<15}")
            print("-" * 70)
            for data in comparison_data:
                print(f"{data['Symbol']:<8} {data['Latest_EPS']:<12} "
                      f"{data['Surprise_%']:<12} {data['PE_Ratio']:<10} {data['Profit_Margin']:<15}")
        
        return comparison_data

def main():
    """主函数 - 演示财报研究功能"""
    researcher = EarningsTranscriptResearcher()
    
    print("🎯 财报Transcript深度研究工具")
    print("=" * 60)
    
    # 单公司深度分析
    symbol = 'AAPL'  # 可以改为其他股票代码
    analysis = researcher.get_detailed_earnings_analysis(symbol)
    
    # 多公司财报对比
    print("\n" + "="*80)
    tech_stocks = ['AAPL', 'MSFT', 'GOOGL', 'NVDA', 'TSLA']
    comparison = researcher.compare_earnings_performance(tech_stocks)

if __name__ == "__main__":
    main()
