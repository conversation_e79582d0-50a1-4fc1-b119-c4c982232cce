#!/usr/bin/env python3
"""
Tesla Earnings Call 数据获取
使用RapidAPI的Alpha Vantage获取特斯拉财报相关数据
"""

import requests
import json
from datetime import datetime

class TeslaEarningsAnalyzer:
    def __init__(self, rapidapi_key):
        self.rapidapi_key = rapidapi_key
        self.base_headers = {
            'X-RapidAPI-Key': rapidapi_key,
            'X-RapidAPI-Host': 'alpha-vantage.p.rapidapi.com',
            'Content-Type': 'application/json'
        }
        self.base_url = "https://alpha-vantage.p.rapidapi.com/query"
    
    def get_company_overview(self):
        """获取Tesla公司概览"""
        print("📊 获取Tesla公司概览...")
        
        params = {
            'function': 'OVERVIEW',
            'symbol': 'TSLA'
        }
        
        try:
            response = requests.get(self.base_url, headers=self.base_headers, params=params, timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                
                if 'Symbol' in data:
                    print("✅ Tesla公司基本信息:")
                    print(f"   公司名称: {data.get('Name', 'N/A')}")
                    print(f"   股票代码: {data.get('Symbol', 'N/A')}")
                    print(f"   行业: {data.get('Industry', 'N/A')}")
                    print(f"   市值: ${data.get('MarketCapitalization', 'N/A')}")
                    print(f"   P/E比率: {data.get('PERatio', 'N/A')}")
                    print(f"   EPS: ${data.get('EPS', 'N/A')}")
                    print(f"   股息收益率: {data.get('DividendYield', 'N/A')}")
                    print(f"   52周最高: ${data.get('52WeekHigh', 'N/A')}")
                    print(f"   52周最低: ${data.get('52WeekLow', 'N/A')}")
                    print(f"   ROE: {data.get('ReturnOnEquityTTM', 'N/A')}")
                    print(f"   ROA: {data.get('ReturnOnAssetsTTM', 'N/A')}")
                    return data
                else:
                    print(f"⚠️ 可能达到API限制: {data}")
                    return None
            else:
                print(f"❌ 请求失败: {response.status_code}")
                print(f"响应: {response.text}")
                return None
                
        except Exception as e:
            print(f"❌ 错误: {e}")
            return None
    
    def get_earnings_data(self):
        """获取Tesla财报数据"""
        print("\n📈 获取Tesla财报数据...")
        
        params = {
            'function': 'EARNINGS',
            'symbol': 'TSLA'
        }
        
        try:
            response = requests.get(self.base_url, headers=self.base_headers, params=params, timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                
                if 'quarterlyEarnings' in data and data['quarterlyEarnings']:
                    print("✅ Tesla季度财报数据:")
                    
                    # 显示最近4个季度的数据
                    for i, quarter in enumerate(data['quarterlyEarnings'][:4], 1):
                        print(f"\n   📅 第{i}季度 ({quarter.get('fiscalDateEnding', 'N/A')}):")
                        print(f"      报告EPS: ${quarter.get('reportedEPS', 'N/A')}")
                        print(f"      预期EPS: ${quarter.get('estimatedEPS', 'N/A')}")
                        print(f"      惊喜程度: {quarter.get('surprisePercentage', 'N/A')}%")
                        
                        # 计算是否超预期
                        try:
                            reported = float(quarter.get('reportedEPS', 0))
                            estimated = float(quarter.get('estimatedEPS', 0))
                            if reported > estimated:
                                print(f"      🎉 超预期！")
                            elif reported < estimated:
                                print(f"      📉 低于预期")
                            else:
                                print(f"      ✅ 符合预期")
                        except:
                            pass
                    
                    return data
                else:
                    print(f"❌ 无财报数据或达到API限制: {data}")
                    return None
            else:
                print(f"❌ 请求失败: {response.status_code}")
                return None
                
        except Exception as e:
            print(f"❌ 错误: {e}")
            return None
    
    def get_income_statement(self):
        """获取Tesla损益表"""
        print("\n💰 获取Tesla损益表...")
        
        params = {
            'function': 'INCOME_STATEMENT',
            'symbol': 'TSLA'
        }
        
        try:
            response = requests.get(self.base_url, headers=self.base_headers, params=params, timeout=15)
            
            if response.status_code == 200:
                data = response.json()
                
                if 'quarterlyReports' in data and data['quarterlyReports']:
                    latest_quarter = data['quarterlyReports'][0]
                    
                    print("✅ Tesla最新季度财务数据:")
                    print(f"   报告期: {latest_quarter.get('fiscalDateEnding', 'N/A')}")
                    
                    # 收入数据
                    revenue = latest_quarter.get('totalRevenue', 'N/A')
                    if revenue != 'N/A':
                        revenue_b = int(revenue) / 1e9
                        print(f"   总收入: ${revenue_b:.2f}B")
                    
                    # 毛利润
                    gross_profit = latest_quarter.get('grossProfit', 'N/A')
                    if gross_profit != 'N/A':
                        gross_profit_b = int(gross_profit) / 1e9
                        print(f"   毛利润: ${gross_profit_b:.2f}B")
                        
                        if revenue != 'N/A':
                            gross_margin = (int(gross_profit) / int(revenue)) * 100
                            print(f"   毛利率: {gross_margin:.2f}%")
                    
                    # 净利润
                    net_income = latest_quarter.get('netIncome', 'N/A')
                    if net_income != 'N/A':
                        net_income_b = int(net_income) / 1e9
                        print(f"   净利润: ${net_income_b:.2f}B")
                        
                        if revenue != 'N/A':
                            net_margin = (int(net_income) / int(revenue)) * 100
                            print(f"   净利率: {net_margin:.2f}%")
                    
                    # 运营费用
                    operating_expenses = latest_quarter.get('totalOperatingExpense', 'N/A')
                    if operating_expenses != 'N/A':
                        operating_expenses_b = int(operating_expenses) / 1e9
                        print(f"   运营费用: ${operating_expenses_b:.2f}B")
                    
                    # 研发费用
                    rd_expenses = latest_quarter.get('researchAndDevelopment', 'N/A')
                    if rd_expenses != 'N/A':
                        rd_expenses_b = int(rd_expenses) / 1e9
                        print(f"   研发费用: ${rd_expenses_b:.2f}B")
                        
                        if revenue != 'N/A':
                            rd_ratio = (int(rd_expenses) / int(revenue)) * 100
                            print(f"   研发占收入比: {rd_ratio:.2f}%")
                    
                    return data
                else:
                    print(f"❌ 无损益表数据: {data}")
                    return None
            else:
                print(f"❌ 请求失败: {response.status_code}")
                return None
                
        except Exception as e:
            print(f"❌ 错误: {e}")
            return None
    
    def get_current_quote(self):
        """获取Tesla当前股价"""
        print("\n📊 获取Tesla当前股价...")
        
        params = {
            'function': 'GLOBAL_QUOTE',
            'symbol': 'TSLA'
        }
        
        try:
            response = requests.get(self.base_url, headers=self.base_headers, params=params, timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                quote = data.get('Global Quote', {})
                
                if quote:
                    print("✅ Tesla实时股价:")
                    print(f"   股票代码: {quote.get('01. symbol', 'N/A')}")
                    print(f"   当前价格: ${quote.get('05. price', 'N/A')}")
                    print(f"   涨跌额: ${quote.get('09. change', 'N/A')}")
                    print(f"   涨跌幅: {quote.get('10. change percent', 'N/A')}")
                    print(f"   开盘价: ${quote.get('02. open', 'N/A')}")
                    print(f"   最高价: ${quote.get('03. high', 'N/A')}")
                    print(f"   最低价: ${quote.get('04. low', 'N/A')}")
                    print(f"   成交量: {quote.get('06. volume', 'N/A')}")
                    print(f"   最新交易日: {quote.get('07. latest trading day', 'N/A')}")
                    
                    return data
                else:
                    print(f"❌ 无股价数据: {data}")
                    return None
            else:
                print(f"❌ 请求失败: {response.status_code}")
                return None
                
        except Exception as e:
            print(f"❌ 错误: {e}")
            return None
    
    def analyze_earnings_call_insights(self):
        """分析Tesla财报电话会议要点"""
        print("\n🎯 Tesla Earnings Call 分析要点:")
        print("=" * 50)
        
        # 获取所有数据
        overview = self.get_company_overview()
        earnings = self.get_earnings_data()
        income = self.get_income_statement()
        quote = self.get_current_quote()
        
        print("\n📋 综合分析:")
        
        if earnings and 'quarterlyEarnings' in earnings:
            latest_earnings = earnings['quarterlyEarnings'][0]
            surprise = latest_earnings.get('surprisePercentage', 0)
            
            try:
                surprise_float = float(surprise)
                if surprise_float > 10:
                    print("   🎉 强劲表现：EPS大幅超预期")
                elif surprise_float > 0:
                    print("   ✅ 良好表现：EPS超预期")
                elif surprise_float < -10:
                    print("   📉 表现不佳：EPS大幅低于预期")
                else:
                    print("   ⚖️ 符合预期：EPS基本符合预期")
            except:
                pass
        
        if overview:
            pe_ratio = overview.get('PERatio', 'N/A')
            if pe_ratio != 'N/A' and pe_ratio != 'None':
                try:
                    pe_float = float(pe_ratio)
                    if pe_float > 50:
                        print("   ⚠️ 高估值：P/E比率较高，市场预期很高")
                    elif pe_float > 25:
                        print("   📈 成长股：P/E比率中等偏高")
                    else:
                        print("   💰 价值股：P/E比率相对合理")
                except:
                    pass
        
        print("\n💡 投资要点:")
        print("   • 关注电动车交付量增长")
        print("   • 监控自动驾驶技术进展")
        print("   • 观察能源业务发展")
        print("   • 留意毛利率变化趋势")
        print("   • 跟踪新工厂产能爬坡")

def main():
    """主函数"""
    rapidapi_key = "**************************************************"
    
    print("🚗 Tesla Earnings Call 数据分析")
    print("使用RapidAPI Alpha Vantage API")
    print("=" * 60)
    
    analyzer = TeslaEarningsAnalyzer(rapidapi_key)
    analyzer.analyze_earnings_call_insights()
    
    print("\n✅ Tesla财报分析完成！")
    print("\n🎯 这些数据可以用于:")
    print("   • 稷下学宫八仙论道的数据支撑")
    print("   • 投资决策参考")
    print("   • 财报电话会议要点总结")

if __name__ == "__main__":
    main()
