#!/usr/bin/env python3
"""
交互式财报Transcript分析器
支持用户输入股票代码，深度分析公司财报信息
"""

import requests
import json
from datetime import datetime
import os

class InteractiveTranscriptAnalyzer:
    def __init__(self, api_key=None):
        self.api_key = api_key or os.getenv('ALPHA_VANTAGE_API', '4HDGAT4ZO02QM1KY')
        self.base_url = "https://www.alphavantage.co/query"
    
    def analyze_company_transcript(self, symbol):
        """分析公司财报transcript"""
        print(f"\n🔍 正在分析 {symbol.upper()} 的财报信息...")
        print("=" * 60)
        
        # 获取基本信息
        overview = self._get_overview(symbol)
        if not overview or 'Symbol' not in overview:
            print(f"❌ 无法找到股票代码 {symbol} 的信息")
            return None
        
        # 显示公司基本信息
        self._display_company_info(overview)
        
        # 获取并分析财报数据
        earnings = self._get_earnings(symbol)
        self._analyze_earnings_performance(earnings)
        
        # 获取财务报表
        income_statement = self._get_income_statement(symbol)
        self._analyze_financial_statements(income_statement)
        
        # 获取新闻情绪
        news = self._get_news_sentiment(symbol)
        self._analyze_news_sentiment(news)
        
        return {
            'overview': overview,
            'earnings': earnings,
            'income_statement': income_statement,
            'news': news
        }
    
    def _get_overview(self, symbol):
        """获取公司概览"""
        params = {
            'function': 'OVERVIEW',
            'symbol': symbol,
            'apikey': self.api_key
        }
        response = requests.get(self.base_url, params=params)
        return response.json()
    
    def _get_earnings(self, symbol):
        """获取财报数据"""
        params = {
            'function': 'EARNINGS',
            'symbol': symbol,
            'apikey': self.api_key
        }
        response = requests.get(self.base_url, params=params)
        return response.json()
    
    def _get_income_statement(self, symbol):
        """获取损益表"""
        params = {
            'function': 'INCOME_STATEMENT',
            'symbol': symbol,
            'apikey': self.api_key
        }
        response = requests.get(self.base_url, params=params)
        return response.json()
    
    def _get_news_sentiment(self, symbol):
        """获取新闻情绪"""
        params = {
            'function': 'NEWS_SENTIMENT',
            'tickers': symbol,
            'limit': 10,
            'apikey': self.api_key
        }
        response = requests.get(self.base_url, params=params)
        return response.json()
    
    def _display_company_info(self, overview):
        """显示公司基本信息"""
        print("\n📊 公司基本信息:")
        print("-" * 30)
        
        info_items = [
            ('公司名称', overview.get('Name', 'N/A')),
            ('行业', overview.get('Industry', 'N/A')),
            ('板块', overview.get('Sector', 'N/A')),
            ('市值', f"${overview.get('MarketCapitalization', 'N/A')}"),
            ('员工数', overview.get('FullTimeEmployees', 'N/A')),
            ('总部', overview.get('Country', 'N/A')),
        ]
        
        for label, value in info_items:
            print(f"  {label}: {value}")
        
        # 关键财务指标
        print("\n💰 关键财务指标:")
        print("-" * 30)
        
        financial_items = [
            ('股价', f"${overview.get('52WeekHigh', 'N/A')} (52周高) / ${overview.get('52WeekLow', 'N/A')} (52周低)"),
            ('P/E比率', overview.get('PERatio', 'N/A')),
            ('市净率', overview.get('PriceToBookRatio', 'N/A')),
            ('股息收益率', f"{float(overview.get('DividendYield', 0)) * 100:.2f}%" if overview.get('DividendYield') != 'None' else 'N/A'),
            ('ROE', f"{float(overview.get('ReturnOnEquityTTM', 0)) * 100:.2f}%" if overview.get('ReturnOnEquityTTM') != 'None' else 'N/A'),
            ('利润率', f"{float(overview.get('ProfitMargin', 0)) * 100:.2f}%" if overview.get('ProfitMargin') != 'None' else 'N/A'),
        ]
        
        for label, value in financial_items:
            print(f"  {label}: {value}")
    
    def _analyze_earnings_performance(self, earnings):
        """分析财报表现"""
        print("\n📈 财报表现分析:")
        print("-" * 30)
        
        if 'quarterlyEarnings' not in earnings or not earnings['quarterlyEarnings']:
            print("❌ 无法获取季度财报数据")
            return
        
        quarters = earnings['quarterlyEarnings'][:4]  # 最近4个季度
        
        print("  最近4个季度表现:")
        total_surprise = 0
        positive_surprises = 0
        
        for quarter in quarters:
            eps = quarter.get('reportedEPS', 'N/A')
            estimated = quarter.get('estimatedEPS', 'N/A')
            surprise = quarter.get('surprisePercentage', 'N/A')
            date = quarter.get('fiscalDateEnding', 'N/A')
            
            if surprise != 'N/A':
                try:
                    surprise_val = float(surprise)
                    total_surprise += surprise_val
                    if surprise_val > 0:
                        positive_surprises += 1
                        surprise_icon = "✅"
                    else:
                        surprise_icon = "❌"
                except ValueError:
                    surprise_icon = "❓"
            else:
                surprise_icon = "❓"
            
            print(f"    {date}: EPS ${eps} (预期 ${estimated}) {surprise_icon} {surprise}%")
        
        # 计算平均惊喜
        if len(quarters) > 0:
            avg_surprise = total_surprise / len(quarters)
            print(f"\n  📊 财报总结:")
            print(f"    平均惊喜: {avg_surprise:.2f}%")
            print(f"    正面惊喜: {positive_surprises}/{len(quarters)} 次")
            
            if avg_surprise > 5:
                print("    🎯 评价: 持续强劲超预期")
            elif avg_surprise > 0:
                print("    ⚡ 评价: 稳定超预期表现")
            elif avg_surprise > -5:
                print("    ⚠️ 评价: 基本符合预期")
            else:
                print("    🔴 评价: 表现低于预期")
    
    def _analyze_financial_statements(self, income_statement):
        """分析财务报表"""
        print("\n📋 财务报表分析:")
        print("-" * 30)
        
        if 'quarterlyReports' not in income_statement or not income_statement['quarterlyReports']:
            print("❌ 无法获取财务报表数据")
            return
        
        latest_quarter = income_statement['quarterlyReports'][0]
        
        # 关键财务数据
        revenue = latest_quarter.get('totalRevenue', 'N/A')
        gross_profit = latest_quarter.get('grossProfit', 'N/A')
        net_income = latest_quarter.get('netIncome', 'N/A')
        
        print(f"  最新季度 ({latest_quarter.get('fiscalDateEnding', 'N/A')}):")
        
        if revenue != 'N/A':
            revenue_val = int(revenue) / 1e9  # 转换为十亿
            print(f"    总收入: ${revenue_val:.2f}B")
        
        if gross_profit != 'N/A':
            gross_profit_val = int(gross_profit) / 1e9
            print(f"    毛利润: ${gross_profit_val:.2f}B")
            
            if revenue != 'N/A':
                gross_margin = (int(gross_profit) / int(revenue)) * 100
                print(f"    毛利率: {gross_margin:.2f}%")
        
        if net_income != 'N/A':
            net_income_val = int(net_income) / 1e9
            print(f"    净利润: ${net_income_val:.2f}B")
            
            if revenue != 'N/A':
                net_margin = (int(net_income) / int(revenue)) * 100
                print(f"    净利率: {net_margin:.2f}%")
    
    def _analyze_news_sentiment(self, news):
        """分析新闻情绪"""
        print("\n📰 市场情绪分析:")
        print("-" * 30)
        
        if 'feed' not in news or not news['feed']:
            print("❌ 无法获取新闻数据")
            return
        
        articles = news['feed'][:5]  # 最近5条新闻
        sentiment_scores = []
        
        print("  最新相关新闻:")
        for i, article in enumerate(articles, 1):
            title = article.get('title', 'N/A')[:50] + "..."
            sentiment = article.get('overall_sentiment_label', 'N/A')
            score = article.get('overall_sentiment_score', 0)
            source = article.get('source', 'N/A')
            
            sentiment_scores.append(score)
            
            # 情绪图标
            if sentiment == 'Bullish':
                sentiment_icon = "🟢"
            elif sentiment == 'Somewhat-Bullish':
                sentiment_icon = "🟡"
            elif sentiment == 'Neutral':
                sentiment_icon = "⚪"
            elif sentiment == 'Somewhat-Bearish':
                sentiment_icon = "🟠"
            else:
                sentiment_icon = "🔴"
            
            print(f"    {i}. {title}")
            print(f"       {sentiment_icon} {sentiment} ({score:.3f}) - {source}")
        
        # 计算平均情绪
        if sentiment_scores:
            avg_sentiment = sum(sentiment_scores) / len(sentiment_scores)
            print(f"\n  📊 整体市场情绪: {avg_sentiment:.3f}")
            
            if avg_sentiment > 0.2:
                print("    🟢 市场情绪: 积极乐观")
            elif avg_sentiment > 0:
                print("    🟡 市场情绪: 温和积极")
            elif avg_sentiment > -0.2:
                print("    ⚪ 市场情绪: 中性观望")
            else:
                print("    🔴 市场情绪: 谨慎悲观")

def main():
    """主函数 - 交互式分析"""
    analyzer = InteractiveTranscriptAnalyzer()
    
    print("🎯 交互式财报Transcript分析器")
    print("=" * 50)
    print("输入股票代码来分析公司财报信息")
    print("输入 'quit' 或 'exit' 退出程序")
    print("=" * 50)
    
    while True:
        try:
            symbol = input("\n请输入股票代码 (如 AAPL, MSFT, GOOGL): ").strip().upper()
            
            if symbol in ['QUIT', 'EXIT', 'Q']:
                print("👋 感谢使用财报分析器！")
                break
            
            if not symbol:
                print("❌ 请输入有效的股票代码")
                continue
            
            # 分析公司
            result = analyzer.analyze_company_transcript(symbol)
            
            if result:
                print(f"\n✅ {symbol} 分析完成！")
                
                # 询问是否继续
                continue_choice = input("\n是否继续分析其他公司？(y/n): ").strip().lower()
                if continue_choice not in ['y', 'yes', '是']:
                    print("👋 感谢使用财报分析器！")
                    break
            
        except KeyboardInterrupt:
            print("\n\n👋 程序已退出")
            break
        except Exception as e:
            print(f"❌ 发生错误: {e}")
            continue

if __name__ == "__main__":
    main()
