#!/usr/bin/env python3
"""
公司财报Transcript分析器
使用Alpha Vantage API获取公司财报数据、新闻情绪和基本面信息
"""

import requests
import json
from datetime import datetime
import os

# 尝试加载环境变量，如果没有dotenv就跳过
try:
    from dotenv import load_dotenv
    load_dotenv()
except ImportError:
    pass

class CompanyTranscriptAnalyzer:
    def __init__(self, api_key=None):
        """
        初始化分析器
        
        Args:
            api_key: Alpha Vantage API密钥
        """
        self.api_key = api_key or os.getenv('ALPHA_VANTAGE_API', '4HDGAT4ZO02QM1KY')
        self.base_url = "https://www.alphavantage.co/query"
    
    def get_company_overview(self, symbol):
        """获取公司基本信息"""
        params = {
            'function': 'OVERVIEW',
            'symbol': symbol,
            'apikey': self.api_key
        }
        
        response = requests.get(self.base_url, params=params)
        return response.json()
    
    def get_earnings_data(self, symbol):
        """获取财报数据"""
        params = {
            'function': 'EARNINGS',
            'symbol': symbol,
            'apikey': self.api_key
        }
        
        response = requests.get(self.base_url, params=params)
        return response.json()
    
    def get_earnings_calendar(self, horizon='3month'):
        """获取财报日历"""
        params = {
            'function': 'EARNINGS_CALENDAR',
            'horizon': horizon,
            'apikey': self.api_key
        }
        
        response = requests.get(self.base_url, params=params)
        return response.text  # CSV格式
    
    def get_news_sentiment(self, tickers, limit=50):
        """获取新闻情绪分析"""
        params = {
            'function': 'NEWS_SENTIMENT',
            'tickers': tickers,
            'limit': limit,
            'apikey': self.api_key
        }
        
        response = requests.get(self.base_url, params=params)
        return response.json()
    
    def get_income_statement(self, symbol):
        """获取损益表"""
        params = {
            'function': 'INCOME_STATEMENT',
            'symbol': symbol,
            'apikey': self.api_key
        }
        
        response = requests.get(self.base_url, params=params)
        return response.json()
    
    def get_balance_sheet(self, symbol):
        """获取资产负债表"""
        params = {
            'function': 'BALANCE_SHEET',
            'symbol': symbol,
            'apikey': self.api_key
        }
        
        response = requests.get(self.base_url, params=params)
        return response.json()
    
    def get_cash_flow(self, symbol):
        """获取现金流量表"""
        params = {
            'function': 'CASH_FLOW',
            'symbol': symbol,
            'apikey': self.api_key
        }
        
        response = requests.get(self.base_url, params=params)
        return response.json()
    
    def analyze_company(self, symbol):
        """综合分析公司"""
        print(f"\n🔍 正在分析公司: {symbol}")
        print("=" * 50)
        
        # 1. 基本信息
        print("\n📊 公司基本信息:")
        overview = self.get_company_overview(symbol)
        if 'Symbol' in overview:
            print(f"公司名称: {overview.get('Name', 'N/A')}")
            print(f"行业: {overview.get('Industry', 'N/A')}")
            print(f"市值: ${overview.get('MarketCapitalization', 'N/A')}")
            print(f"P/E比率: {overview.get('PERatio', 'N/A')}")
            print(f"股息收益率: {overview.get('DividendYield', 'N/A')}")
            print(f"52周最高: ${overview.get('52WeekHigh', 'N/A')}")
            print(f"52周最低: ${overview.get('52WeekLow', 'N/A')}")
        
        # 2. 财报数据
        print("\n📈 最近财报表现:")
        earnings = self.get_earnings_data(symbol)
        if 'quarterlyEarnings' in earnings:
            recent_quarters = earnings['quarterlyEarnings'][:4]  # 最近4个季度
            for quarter in recent_quarters:
                print(f"  {quarter['fiscalDateEnding']}: "
                      f"EPS ${quarter['reportedEPS']} "
                      f"(预期 ${quarter['estimatedEPS']}, "
                      f"惊喜 {quarter['surprisePercentage']}%)")
        
        # 3. 新闻情绪
        print("\n📰 最新新闻情绪:")
        news = self.get_news_sentiment(symbol, limit=5)
        if 'feed' in news:
            for article in news['feed'][:3]:  # 显示前3条新闻
                print(f"  📄 {article['title'][:80]}...")
                print(f"     情绪: {article['overall_sentiment_label']} "
                      f"({article['overall_sentiment_score']:.3f})")
                print(f"     来源: {article['source']} | "
                      f"时间: {article['time_published']}")
                print()
        
        return {
            'overview': overview,
            'earnings': earnings,
            'news': news
        }
    
    def compare_companies(self, symbols):
        """比较多个公司"""
        print(f"\n🔄 比较分析: {', '.join(symbols)}")
        print("=" * 60)
        
        comparison_data = []
        
        for symbol in symbols:
            overview = self.get_company_overview(symbol)
            if 'Symbol' in overview:
                comparison_data.append({
                    'Symbol': symbol,
                    'Name': overview.get('Name', 'N/A'),
                    'MarketCap': overview.get('MarketCapitalization', 'N/A'),
                    'PE_Ratio': overview.get('PERatio', 'N/A'),
                    'Dividend_Yield': overview.get('DividendYield', 'N/A'),
                    'ROE': overview.get('ReturnOnEquityTTM', 'N/A'),
                    'Profit_Margin': overview.get('ProfitMargin', 'N/A')
                })
        
        if comparison_data:
            # 简单的表格显示
            print(f"{'Symbol':<8} {'Name':<20} {'MarketCap':<15} {'PE_Ratio':<10} {'Dividend':<10}")
            print("-" * 70)
            for data in comparison_data:
                print(f"{data['Symbol']:<8} {data['Name'][:20]:<20} "
                      f"{data['MarketCap']:<15} {data['PE_Ratio']:<10} {data['Dividend_Yield']:<10}")
        
        return comparison_data
    
    def get_sector_analysis(self, symbols):
        """行业分析"""
        print(f"\n🏭 行业分析")
        print("=" * 40)
        
        sectors = {}
        for symbol in symbols:
            overview = self.get_company_overview(symbol)
            if 'Sector' in overview:
                sector = overview['Sector']
                if sector not in sectors:
                    sectors[sector] = []
                sectors[sector].append({
                    'symbol': symbol,
                    'name': overview.get('Name', 'N/A'),
                    'market_cap': overview.get('MarketCapitalization', 'N/A')
                })
        
        for sector, companies in sectors.items():
            print(f"\n📊 {sector}:")
            for company in companies:
                print(f"  • {company['symbol']}: {company['name']} "
                      f"(市值: ${company['market_cap']})")
        
        return sectors

def main():
    """主函数 - 演示用法"""
    analyzer = CompanyTranscriptAnalyzer()
    
    # 分析单个公司
    print("🎯 单公司深度分析")
    apple_analysis = analyzer.analyze_company('AAPL')
    
    # 比较多个公司
    print("\n" + "="*80)
    tech_giants = ['AAPL', 'MSFT', 'GOOGL', 'AMZN', 'META']
    comparison = analyzer.compare_companies(tech_giants)
    
    # 行业分析
    print("\n" + "="*80)
    sector_analysis = analyzer.get_sector_analysis(tech_giants)
    
    # 获取财报日历
    print("\n" + "="*80)
    print("📅 即将发布财报的公司:")
    calendar = analyzer.get_earnings_calendar()
    print(calendar[:500] + "..." if len(calendar) > 500 else calendar)

if __name__ == "__main__":
    main()
