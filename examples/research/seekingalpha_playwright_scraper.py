#!/usr/bin/env python3
"""
Seeking Alpha Playwright爬虫
使用Playwright绕过反爬虫机制
"""

import asyncio
import json
import random
from datetime import datetime
from typing import Dict, List, Optional
from playwright.async_api import async_playwright, Page, Browser
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class SeekingAlphaScraper:
    """Seeking Alpha爬虫"""
    
    def __init__(self):
        self.browser: Optional[Browser] = None
        self.page: Optional[Page] = None
        
        # 随机User-Agent池
        self.user_agents = [
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.1.15"
        ]
    
    async def init_browser(self, headless: bool = True):
        """初始化浏览器"""
        playwright = await async_playwright().start()
        
        # 启动浏览器，模拟真实用户
        self.browser = await playwright.chromium.launch(
            headless=headless,
            args=[
                '--no-sandbox',
                '--disable-blink-features=AutomationControlled',
                '--disable-dev-shm-usage',
                '--disable-web-security',
                '--disable-features=VizDisplayCompositor'
            ]
        )
        
        # 创建上下文，设置随机User-Agent
        context = await self.browser.new_context(
            user_agent=random.choice(self.user_agents),
            viewport={'width': 1920, 'height': 1080},
            locale='en-US',
            timezone_id='America/New_York'
        )
        
        self.page = await context.new_page()
        
        # 隐藏webdriver特征
        await self.page.add_init_script("""
            Object.defineProperty(navigator, 'webdriver', {
                get: () => undefined,
            });
            
            // 伪造Chrome插件
            Object.defineProperty(navigator, 'plugins', {
                get: () => [1, 2, 3, 4, 5],
            });
            
            // 伪造语言
            Object.defineProperty(navigator, 'languages', {
                get: () => ['en-US', 'en'],
            });
        """)
        
        logger.info("浏览器初始化完成")
    
    async def random_delay(self, min_seconds: float = 1.0, max_seconds: float = 3.0):
        """随机延时"""
        delay = random.uniform(min_seconds, max_seconds)
        await asyncio.sleep(delay)
    
    async def scrape_article(self, url: str) -> Dict:
        """抓取单篇文章"""
        if not self.page:
            await self.init_browser()
        
        try:
            logger.info(f"正在抓取: {url}")
            
            # 访问页面
            await self.page.goto(url, wait_until='networkidle', timeout=30000)
            
            # 等待内容加载
            await self.page.wait_for_selector('h1', timeout=10000)
            
            # 随机滚动，模拟真实用户行为
            await self.simulate_human_behavior()
            
            # 提取文章信息
            article_data = await self.extract_article_data()
            article_data['url'] = url
            article_data['scraped_at'] = datetime.now().isoformat()
            
            logger.info(f"成功抓取文章: {article_data.get('title', 'Unknown')}")
            return article_data
            
        except Exception as e:
            logger.error(f"抓取失败 {url}: {e}")
            return {'error': str(e), 'url': url}
    
    async def simulate_human_behavior(self):
        """模拟人类浏览行为"""
        # 随机滚动
        for _ in range(random.randint(2, 5)):
            await self.page.mouse.wheel(0, random.randint(200, 800))
            await self.random_delay(0.5, 1.5)
        
        # 随机移动鼠标
        await self.page.mouse.move(
            random.randint(100, 800), 
            random.randint(100, 600)
        )
        
        await self.random_delay(1, 2)
    
    async def extract_article_data(self) -> Dict:
        """提取文章数据"""
        data = {}
        
        try:
            # 标题
            title_element = await self.page.query_selector('h1')
            if title_element:
                data['title'] = await title_element.inner_text()
            
            # 作者
            author_element = await self.page.query_selector('[data-test-id="author-name"]')
            if not author_element:
                author_element = await self.page.query_selector('.author-name')
            if author_element:
                data['author'] = await author_element.inner_text()
            
            # 发布时间
            time_element = await self.page.query_selector('time')
            if time_element:
                data['published_at'] = await time_element.get_attribute('datetime')
            
            # 文章内容
            content_selectors = [
                '[data-test-id="content-container"]',
                '.article-content',
                '.sa-art',
                'article'
            ]
            
            content = ""
            for selector in content_selectors:
                content_element = await self.page.query_selector(selector)
                if content_element:
                    content = await content_element.inner_text()
                    break
            
            data['content'] = content
            
            # 标签/分类
            tags = []
            tag_elements = await self.page.query_selector_all('.tag, .topic-tag')
            for tag_element in tag_elements:
                tag_text = await tag_element.inner_text()
                if tag_text:
                    tags.append(tag_text.strip())
            data['tags'] = tags
            
            # 股票代码
            symbols = []
            symbol_elements = await self.page.query_selector_all('[data-test-id="ticker-link"]')
            for symbol_element in symbol_elements:
                symbol = await symbol_element.inner_text()
                if symbol:
                    symbols.append(symbol.strip())
            data['symbols'] = symbols
            
        except Exception as e:
            logger.error(f"提取数据时出错: {e}")
        
        return data
    
    async def search_articles(self, query: str, limit: int = 10) -> List[Dict]:
        """搜索文章"""
        if not self.page:
            await self.init_browser()
        
        try:
            # 访问搜索页面
            search_url = f"https://seekingalpha.com/search?q={query}"
            await self.page.goto(search_url, wait_until='networkidle')
            
            # 等待搜索结果
            await self.page.wait_for_selector('[data-test-id="search-result"]', timeout=10000)
            
            # 提取搜索结果链接
            result_elements = await self.page.query_selector_all('[data-test-id="search-result"] a')
            
            urls = []
            for element in result_elements[:limit]:
                href = await element.get_attribute('href')
                if href and href.startswith('/'):
                    urls.append(f"https://seekingalpha.com{href}")
            
            logger.info(f"找到 {len(urls)} 个搜索结果")
            
            # 抓取每篇文章
            articles = []
            for url in urls:
                await self.random_delay(2, 5)  # 增加延时避免被封
                article = await self.scrape_article(url)
                if 'error' not in article:
                    articles.append(article)
            
            return articles
            
        except Exception as e:
            logger.error(f"搜索失败: {e}")
            return []
    
    async def get_latest_news(self, limit: int = 20) -> List[Dict]:
        """获取最新新闻"""
        if not self.page:
            await self.init_browser()
        
        try:
            # 访问新闻页面
            await self.page.goto("https://seekingalpha.com/news", wait_until='networkidle')
            
            # 等待新闻列表加载
            await self.page.wait_for_selector('[data-test-id="post-list-item"]', timeout=10000)
            
            # 提取新闻链接
            news_elements = await self.page.query_selector_all('[data-test-id="post-list-item"] a')
            
            urls = []
            for element in news_elements[:limit]:
                href = await element.get_attribute('href')
                if href and href.startswith('/'):
                    urls.append(f"https://seekingalpha.com{href}")
            
            logger.info(f"找到 {len(urls)} 条最新新闻")
            
            # 抓取每条新闻
            news_articles = []
            for url in urls:
                await self.random_delay(1, 3)
                article = await self.scrape_article(url)
                if 'error' not in article:
                    news_articles.append(article)
            
            return news_articles
            
        except Exception as e:
            logger.error(f"获取新闻失败: {e}")
            return []
    
    async def close(self):
        """关闭浏览器"""
        if self.browser:
            await self.browser.close()
            logger.info("浏览器已关闭")

# 使用示例
async def main():
    scraper = SeekingAlphaScraper()
    
    try:
        # 测试抓取单篇文章
        test_url = "https://seekingalpha.com/pr/20162773-ai-device-startup-that-sued-openai-and-jony-ive-is-now-suing-its-own-ex-employee-over-trade"
        article = await scraper.scrape_article(test_url)
        print("文章数据:")
        print(json.dumps(article, indent=2, ensure_ascii=False))
        
        # 测试搜索
        # search_results = await scraper.search_articles("OpenAI", limit=5)
        # print(f"\n搜索结果: {len(search_results)} 篇文章")
        
        # 测试获取最新新闻
        # latest_news = await scraper.get_latest_news(limit=5)
        # print(f"\n最新新闻: {len(latest_news)} 条")
        
    finally:
        await scraper.close()

if __name__ == "__main__":
    asyncio.run(main())