#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Penny Scanner - 示例应用

这是一个使用现代化 Streamlit 框架构建的股票分析应用示例。
展示了如何使用依赖注入、中间件、插件、路由等功能。

运行方式:
streamlit run example_app.py

作者: Assistant
创建时间: 2024
"""

import streamlit as st
import pandas as pd
import numpy as np
import time
import random
from datetime import datetime, timedelta
from typing import Dict, List, Optional

# 导入框架组件
from app.utils.app_framework import (
    AppConfig, get_app_framework, streamlit_app
)
from app.utils.dependency_injection import (
    injectable, inject, register_service, DependencyScope
)
from app.utils.router import (
    route, RouteType, cached_route, require_auth, guard
)
from app.utils.middleware import (
    middleware, MiddlewareType
)
from app.utils.plugin_system import (
    plugin, hook, PluginType
)
from app.utils.asset_manager import (
    get_asset_manager, AssetType
)


# ==================== AI助手服务 ====================

@injectable
class AIAssistantService:
    """AI助手服务"""
    
    def __init__(self):
        self.conversation_history = []
        self.max_history = 10
    
    def get_response(self, user_input: str) -> str:
        """获取AI助手回复"""
        user_input_lower = user_input.lower()
        
        # 检查是否询问特定股票价格
        stock_symbols = ['aapl', 'googl', 'msft', 'tsla', 'nvda', 'amzn']
        for symbol in stock_symbols:
            if symbol in user_input_lower and ('价格' in user_input or 'price' in user_input_lower or '多少' in user_input):
                try:
                    from app.utils.app_framework import get_app_framework
                    stock_service = get_app_framework().container.resolve('stock_data_service')
                    price = stock_service.get_stock_price(symbol.upper())
                    response = f"{symbol.upper()} 当前价格为 ${price}。\n\n{self.get_stock_suggestion(symbol.upper())}"
                    self.add_to_history(user_input, response)
                    return response
                except Exception as e:
                    response = f"抱歉，获取{symbol.upper()}价格时出现错误。请稍后再试。"
                    self.add_to_history(user_input, response)
                    return response
        
        # 基本回复模式
        responses = {
            "你好": "您好！我是您的股票分析助手，有什么可以帮助您的吗？\n\n💡 您可以问我：\n• 股票价格（如：AAPL价格多少？）\n• 市场分析\n• 投资建议\n• 设置提醒",
            "股票": "我可以帮您分析股票数据、设置价格提醒、查看市场趋势等。您想了解哪只股票？\n\n📊 支持的股票：AAPL, GOOGL, MSFT, TSLA, NVDA, AMZN",
            "价格": "请告诉我您想查询的股票代码，我会为您提供最新价格信息。\n\n例如：'AAPL价格多少？' 或 'TSLA当前价格'",
            "市场": "当前市场表现良好，科技股整体呈现上涨趋势。\n\n📈 建议关注：\n• AI相关股票（NVDA）\n• 云计算股票（MSFT, GOOGL）\n• 电动车股票（TSLA）",
            "帮助": "我可以帮您：\n\n🔍 **查询功能**\n• 实时股票价格\n• 历史数据分析\n• 市场趋势\n\n📊 **分析功能**\n• 股票投资建议\n• 风险评估\n• 行业对比\n\n⚠️ **提醒功能**\n• 价格提醒设置\n• 市场异动通知\n\n请告诉我您需要什么帮助！",
            "谢谢": "不客气！随时为您服务。如果还有其他问题，请随时告诉我。\n\n💡 提示：您可以直接问我股票价格，如'AAPL多少钱？'"
        }
        
        # 关键词匹配
        for keyword, response in responses.items():
            if keyword in user_input:
                self.add_to_history(user_input, response)
                return response
        
        # 检查是否包含股票代码
        for symbol in stock_symbols:
            if symbol in user_input_lower:
                suggestion = self.get_stock_suggestion(symbol.upper())
                response = f"关于{symbol.upper()}股票：\n\n{suggestion}\n\n💡 如需查看实时价格，请说'{symbol.upper()}价格多少？'"
                self.add_to_history(user_input, response)
                return response
        
        # 默认回复
        default_response = "我理解您的问题，但我需要更多信息来帮助您。\n\n💡 您可以问我：\n• 股票价格（如：AAPL价格？）\n• 市场分析\n• 投资建议\n• 或者直接说'帮助'查看所有功能"
        self.add_to_history(user_input, default_response)
        return default_response
    
    def add_to_history(self, user_input: str, ai_response: str):
        """添加对话历史"""
        self.conversation_history.append({
            'timestamp': datetime.now().strftime('%H:%M:%S'),
            'user': user_input,
            'assistant': ai_response
        })
        
        # 保持历史记录在限制范围内
        if len(self.conversation_history) > self.max_history:
            self.conversation_history = self.conversation_history[-self.max_history:]
    
    def get_conversation_history(self) -> List[Dict]:
        """获取对话历史"""
        return self.conversation_history
    
    def clear_history(self):
        """清空对话历史"""
        self.conversation_history = []
    
    def get_stock_suggestion(self, symbol: str) -> str:
        """获取股票建议"""
        suggestions = {
            'AAPL': "苹果公司是一家稳健的科技股，建议长期持有。关注其新产品发布和季度财报。",
            'GOOGL': "谷歌母公司Alphabet在搜索和云计算领域领先，AI发展前景看好。",
            'MSFT': "微软在云计算和企业软件方面表现强劲，股息稳定，适合价值投资。",
            'TSLA': "特斯拉是电动车领域的先驱，但股价波动较大，需要注意风险管理。",
            'NVDA': "英伟达在AI芯片领域占据主导地位，但估值较高，需谨慎投资。",
            'AMZN': "亚马逊在电商和云服务方面实力雄厚，长期增长潜力巨大。"
        }
        
        return suggestions.get(symbol, f"抱歉，我暂时没有关于{symbol}的详细分析，建议您查看相关财务数据和行业报告。")


# ==================== 数据服务 ====================

@injectable
class StockDataService:
    """股票数据服务"""
    
    def __init__(self):
        self.cache = {}
        self.last_update = {}
    
    def get_stock_price(self, symbol: str) -> float:
        """获取股票价格"""
        # 模拟实时价格
        base_prices = {
            'AAPL': 150.0,
            'GOOGL': 2500.0,
            'MSFT': 300.0,
            'TSLA': 800.0,
            'NVDA': 400.0,
            'AMZN': 3200.0
        }
        
        base_price = base_prices.get(symbol, 100.0)
        # 添加随机波动
        change = random.uniform(-0.05, 0.05)
        return round(base_price * (1 + change), 2)
    
    def get_stock_history(self, symbol: str, days: int = 30) -> pd.DataFrame:
        """获取股票历史数据"""
        cache_key = f"{symbol}_{days}"
        
        # 检查缓存
        if (cache_key in self.cache and 
            cache_key in self.last_update and 
            time.time() - self.last_update[cache_key] < 300):  # 5分钟缓存
            return self.cache[cache_key]
        
        # 生成模拟数据
        dates = pd.date_range(end=datetime.now(), periods=days, freq='D')
        base_price = self.get_stock_price(symbol)
        
        prices = []
        current_price = base_price
        
        for _ in range(days):
            change = random.uniform(-0.03, 0.03)
            current_price *= (1 + change)
            prices.append(round(current_price, 2))
        
        df = pd.DataFrame({
            'Date': dates,
            'Price': prices,
            'Volume': np.random.randint(1000000, 10000000, days)
        })
        
        # 缓存结果
        self.cache[cache_key] = df
        self.last_update[cache_key] = time.time()
        
        return df
    
    def get_all_stocks(self) -> List[str]:
        """获取所有可用股票代码"""
        # 扩展股票列表，包含更多股票
        return [
            'AAPL', 'GOOGL', 'MSFT', 'TSLA', 'NVDA', 'AMZN', 'META', 'NFLX',
            'AMD', 'INTC', 'CRM', 'ORCL', 'ADBE', 'PYPL', 'UBER', 'LYFT',
            'ZOOM', 'SHOP', 'SQ', 'ROKU', 'TWTR', 'SNAP', 'PINS', 'SPOT',
            'BABA', 'JD', 'PDD', 'NIO', 'XPEV', 'LI', 'BIDU', 'BILI'
        ]
    
    def get_sorted_stocks(self, sort_by: str = 'price', ascending: bool = False) -> List[Dict]:
        """获取排序后的股票列表"""
        stocks = []
        all_symbols = self.get_all_stocks()
        
        for symbol in all_symbols:
            price = self.get_stock_price(symbol)
            change = random.uniform(-5, 5)
            change_pct = random.uniform(-0.05, 0.05)
            volume = random.randint(1000000, 50000000)
            
            stocks.append({
                'Symbol': symbol,
                'Price': price,
                'Change': round(change, 2),
                'Change%': round(change_pct * 100, 2),
                'Volume': volume,
                'MarketCap': price * random.randint(1000000, 10000000)  # 模拟市值
            })
        
        # 根据指定字段排序
        if sort_by == 'price':
            stocks.sort(key=lambda x: x['Price'], reverse=not ascending)
        elif sort_by == 'change':
            stocks.sort(key=lambda x: x['Change%'], reverse=not ascending)
        elif sort_by == 'volume':
            stocks.sort(key=lambda x: x['Volume'], reverse=not ascending)
        elif sort_by == 'market_cap':
            stocks.sort(key=lambda x: x['MarketCap'], reverse=not ascending)
        
        return stocks
    
    def get_top_stocks(self, count: int = 10, sort_by: str = 'price') -> List[str]:
        """获取排名前N的股票代码"""
        sorted_stocks = self.get_sorted_stocks(sort_by=sort_by, ascending=False)
        return [stock['Symbol'] for stock in sorted_stocks[:count]]
    
    def get_market_summary(self) -> Dict:
        """获取市场概况"""
        # 使用排序后的前6只股票作为市场概况
        top_stocks = self.get_sorted_stocks(sort_by='market_cap', ascending=False)[:6]
        return top_stocks


@injectable
class UserService:
    """用户服务"""
    
    def __init__(self):
        self.users = {
            'admin': {'password': 'admin123', 'role': 'admin'},
            'user': {'password': 'user123', 'role': 'user'}
        }
    
    def authenticate(self, username: str, password: str) -> bool:
        """用户认证"""
        user = self.users.get(username)
        if user and user['password'] == password:
            st.session_state.authenticated = True
            st.session_state.user_name = username
            st.session_state.user_role = user['role']
            return True
        return False
    
    def logout(self):
        """用户登出"""
        st.session_state.authenticated = False
        st.session_state.user_name = None
        st.session_state.user_role = None
    
    def is_authenticated(self) -> bool:
        """检查是否已认证"""
        return st.session_state.get('authenticated', False)
    
    def get_current_user(self) -> Optional[str]:
        """获取当前用户"""
        return st.session_state.get('user_name')
    
    def has_role(self, role: str) -> bool:
        """检查用户角色"""
        return st.session_state.get('user_role') == role


# ==================== 中间件 ====================

@middleware
def request_logging_middleware(context):
    """请求日志中间件"""
    start_time = time.time()
    route = context.data.get('current_route', 'unknown')
    user = st.session_state.get('user_name', 'anonymous')
    
    # 记录请求开始
    if 'request_logs' not in st.session_state:
        st.session_state.request_logs = []
    
    def log_request_end():
        duration = (time.time() - start_time) * 1000
        log_entry = {
            'timestamp': datetime.now().isoformat(),
            'route': route,
            'user': user,
            'duration_ms': round(duration, 2)
        }
        st.session_state.request_logs.append(log_entry)
        
        # 保持最近100条记录
        if len(st.session_state.request_logs) > 100:
            st.session_state.request_logs = st.session_state.request_logs[-100:]
    
    context.data['log_request_end'] = log_request_end


@middleware
def security_middleware(context):
    """安全中间件"""
    # 检查会话超时
    if 'last_activity' in st.session_state:
        if time.time() - st.session_state.last_activity > 3600:  # 1小时超时
            st.session_state.authenticated = False
            st.warning("会话已超时，请重新登录")
    
    st.session_state.last_activity = time.time()


# ==================== 插件 ====================

@plugin(
    name="stock_alerts",
    version="1.0.0",
    description="股票价格提醒插件",
    author="Assistant",
    plugin_type=PluginType.FEATURE
)
class StockAlertsPlugin:
    """股票提醒插件"""
    
    def __init__(self):
        self.alerts = {}
    
    def activate(self):
        """插件激活"""
        if 'stock_alerts' not in st.session_state:
            st.session_state.stock_alerts = {}
    
    def deactivate(self):
        """插件停用"""
        pass
    
    @hook('stock_price_updated')
    def check_price_alerts(self, symbol: str, price: float):
        """检查价格提醒"""
        alerts = st.session_state.get('stock_alerts', {})
        
        if symbol in alerts:
            alert_config = alerts[symbol]
            if alert_config['type'] == 'above' and price >= alert_config['threshold']:
                st.success(f"🚨 {symbol} 价格已达到 ${price}，超过设定阈值 ${alert_config['threshold']}")
            elif alert_config['type'] == 'below' and price <= alert_config['threshold']:
                st.warning(f"🚨 {symbol} 价格已跌至 ${price}，低于设定阈值 ${alert_config['threshold']}")
    
    def set_alert(self, symbol: str, threshold: float, alert_type: str):
        """设置提醒"""
        if 'stock_alerts' not in st.session_state:
            st.session_state.stock_alerts = {}
        
        st.session_state.stock_alerts[symbol] = {
            'threshold': threshold,
            'type': alert_type,
            'created_at': datetime.now().isoformat()
        }


# ==================== 路由守卫 ====================

def admin_guard() -> bool:
    """管理员权限守卫"""
    user_service = get_app_framework().container.resolve('user_service')
    return user_service.has_role('admin')


def auth_guard() -> bool:
    """认证守卫"""
    user_service = get_app_framework().container.resolve('user_service')
    return user_service.is_authenticated()


# ==================== 页面路由 ====================

@route('home', '/', title='首页', icon='🏠', route_type=RouteType.TAB)
@cached_route(ttl=60)  # 缓存1分钟
@inject('stock_data_service')
def home_page(stock_data_service: StockDataService):
    """首页"""
    st.title("🏠 欢迎使用 Penny Scanner")
    st.markdown("### 📈 实时股票分析平台")
    
    # 市场概况
    st.subheader("📊 市场概况")
    
    try:
        market_data = stock_data_service.get_market_summary()
        df = pd.DataFrame(market_data)
        
        # 使用颜色标识涨跌
        def color_change(val):
            color = 'green' if val > 0 else 'red' if val < 0 else 'black'
            return f'color: {color}'
        
        styled_df = df.style.applymap(color_change, subset=['Change', 'Change%'])
        st.dataframe(styled_df, use_container_width=True)
        
        # 统计信息
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            avg_change = df['Change%'].mean()
            st.metric("平均涨跌幅", f"{avg_change:.2f}%")
        
        with col2:
            total_volume = df['Volume'].sum()
            st.metric("总成交量", f"{total_volume:,}")
        
        with col3:
            gainers = len(df[df['Change%'] > 0])
            st.metric("上涨股票", f"{gainers}/{len(df)}")
        
        with col4:
            st.metric("活跃股票", len(df))
        
    except Exception as e:
        st.error(f"加载市场数据失败: {e}")


@route('us_stocks', '/us-stocks', title='美股', icon='🇺🇸', route_type=RouteType.TAB)
@inject('stock_data_service')
def us_stocks_page(stock_data_service: StockDataService):
    """美股页面"""
    st.title("🇺🇸 美股市场")
    
    # 股票选择
    col1, col2 = st.columns([2, 1])
    
    with col1:
        symbol = st.selectbox(
            "选择股票",
            ['AAPL', 'GOOGL', 'MSFT', 'TSLA', 'NVDA', 'AMZN'],
            index=0
        )
    
    with col2:
        days = st.slider("历史天数", 7, 90, 30)
    
    if symbol:
        try:
            # 获取当前价格
            current_price = stock_data_service.get_stock_price(symbol)
            
            # 触发价格更新事件（用于插件）
            plugin_manager = get_app_framework().plugin_manager
            plugin_manager.trigger_action('stock_price_updated', symbol, current_price)
            
            # 显示当前价格
            st.metric(f"{symbol} 当前价格", f"${current_price}")
            
            # 获取历史数据
            history_df = stock_data_service.get_stock_history(symbol, days)
            
            # 绘制价格图表
            st.subheader("📈 价格走势")
            st.line_chart(history_df.set_index('Date')['Price'])
            
            # 显示数据表格
            st.subheader("📋 历史数据")
            st.dataframe(history_df.tail(10), use_container_width=True)
            
        except Exception as e:
            st.error(f"加载股票数据失败: {e}")


@route('alerts', '/alerts', title='提醒', icon='🚨', route_type=RouteType.PAGE)
@require_auth
def alerts_page():
    """提醒设置页面"""
    st.title("🚨 股票提醒")
    
    # 获取插件实例
    plugin_manager = get_app_framework().plugin_manager
    alerts_plugin = plugin_manager.get_plugin('stock_alerts')
    
    if not alerts_plugin:
        st.error("提醒插件未加载")
        return
    
    # 设置新提醒
    st.subheader("➕ 添加提醒")
    
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        symbol = st.selectbox("股票代码", ['AAPL', 'GOOGL', 'MSFT', 'TSLA', 'NVDA', 'AMZN'])
    
    with col2:
        threshold = st.number_input("阈值价格", min_value=0.01, value=100.0, step=0.01)
    
    with col3:
        alert_type = st.selectbox("提醒类型", ['above', 'below'])
    
    with col4:
        if st.button("添加提醒"):
            alerts_plugin.instance.set_alert(symbol, threshold, alert_type)
            st.success(f"已为 {symbol} 设置提醒")
    
    # 显示现有提醒
    st.subheader("📋 当前提醒")
    
    alerts = st.session_state.get('stock_alerts', {})
    
    if alerts:
        for symbol, config in alerts.items():
            col1, col2, col3, col4 = st.columns(4)
            
            with col1:
                st.write(f"**{symbol}**")
            
            with col2:
                st.write(f"${config['threshold']}")
            
            with col3:
                type_text = "高于" if config['type'] == 'above' else "低于"
                st.write(type_text)
            
            with col4:
                if st.button("删除", key=f"del_{symbol}"):
                    del st.session_state.stock_alerts[symbol]
                    st.rerun()
    else:
        st.info("暂无设置的提醒")


@route('login', '/login', title='登录', icon='🔐', route_type=RouteType.PAGE)
@inject('user_service')
def login_page(user_service: UserService):
    """登录页面"""
    st.title("🔐 用户登录")
    
    if user_service.is_authenticated():
        st.success(f"欢迎回来，{user_service.get_current_user()}！")
        
        col1, col2 = st.columns(2)
        with col1:
            if st.button("前往首页"):
                from app.utils.router import navigate_to
                navigate_to('home')
        
        with col2:
            if st.button("登出"):
                user_service.logout()
                st.rerun()
    else:
        with st.form("login_form"):
            username = st.text_input("用户名")
            password = st.text_input("密码", type="password")
            
            col1, col2 = st.columns(2)
            
            with col1:
                login_button = st.form_submit_button("登录")
            
            with col2:
                st.caption("测试账号: admin/admin123 或 user/user123")
            
            if login_button:
                if user_service.authenticate(username, password):
                    st.success("登录成功！")
                    time.sleep(1)
                    st.rerun()
                else:
                    st.error("用户名或密码错误")


@route('admin', '/admin', title='管理', icon='👑', route_type=RouteType.PAGE)
@guard(admin_guard, error_message="需要管理员权限", redirect_route="login")
def admin_page():
    """管理页面"""
    st.title("👑 管理面板")
    
    tab1, tab2, tab3, tab4 = st.tabs(["系统监控", "用户管理", "插件管理", "日志查看"])
    
    with tab1:
        st.subheader("📊 系统监控")
        
        # 系统统计
        framework = get_app_framework()
        
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            uptime = time.time() - framework.startup_time
            st.metric("运行时间", f"{uptime:.0f}s")
        
        with col2:
            st.metric("请求次数", framework.request_count)
        
        with col3:
            plugin_count = len(framework.plugin_manager.get_plugin_names())
            st.metric("插件数量", plugin_count)
        
        with col4:
            route_count = len(framework.router.routes)
            st.metric("路由数量", route_count)
        
        # 性能图表
        if 'performance_logs' in st.session_state:
            perf_logs = st.session_state.performance_logs
            if perf_logs:
                df = pd.DataFrame(perf_logs)
                df['timestamp'] = pd.to_datetime(df['timestamp'])
                
                st.subheader("⚡ 响应时间")
                st.line_chart(df.set_index('timestamp')['duration_ms'])
    
    with tab2:
        st.subheader("👥 用户管理")
        st.info("用户管理功能开发中...")
    
    with tab3:
        st.subheader("🔌 插件管理")
        framework.router.render_route_analytics_dashboard()
    
    with tab4:
        st.subheader("📝 系统日志")
        
        if 'request_logs' in st.session_state:
            logs = st.session_state.request_logs
            if logs:
                df = pd.DataFrame(logs)
                st.dataframe(df.tail(20), use_container_width=True)
            else:
                st.info("暂无日志记录")
        else:
            st.info("暂无日志记录")


# ==================== 应用配置和启动 ====================

def setup_services():
    """设置服务"""
    # 注册服务
    register_service('stock_data_service', StockDataService(), DependencyScope.SINGLETON)
    register_service('user_service', UserService(), DependencyScope.SINGLETON)
    register_service('ai_assistant_service', AIAssistantService(), DependencyScope.SINGLETON)


def setup_assets():
    """设置资源"""
    asset_manager = get_asset_manager()
    
    # 注册自定义CSS
    custom_css = """
    .metric-container {
        background-color: #f0f2f6;
        padding: 1rem;
        border-radius: 0.5rem;
        margin: 0.5rem 0;
    }
    
    .stock-card {
        border: 1px solid #ddd;
        border-radius: 8px;
        padding: 1rem;
        margin: 0.5rem 0;
        background: white;
    }
    
    .alert-success {
        background-color: #d4edda;
        border-color: #c3e6cb;
        color: #155724;
    }
    
    .alert-warning {
        background-color: #fff3cd;
        border-color: #ffeaa7;
        color: #856404;
    }
    """
    
    asset_manager.register_inline_asset(
        "custom_styles",
        AssetType.CSS,
        custom_css
    )


def render_ai_assistant_sidebar():
    """渲染AI助手侧边栏"""
    with st.sidebar:
        # AI助手标题
        st.markdown("""
        <div style="
            background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
            padding: 1rem;
            border-radius: 10px;
            text-align: center;
            margin-bottom: 1rem;
        ">
            <h2 style="color: white; margin: 0;">🤖 AI股票助手</h2>
            <p style="color: #f0f0f0; margin: 0; font-size: 0.9rem;">您的专业投资顾问</p>
        </div>
        """, unsafe_allow_html=True)
        
        # 获取AI助手服务
        ai_service = get_app_framework().container.resolve('ai_assistant_service')
        
        # 快速操作按钮
        st.markdown("### ⚡ 快速操作")
        col1, col2 = st.columns(2)
        with col1:
            if st.button("📊 市场", use_container_width=True, help="查看市场概况"):
                response = ai_service.get_response("市场")
                st.rerun()
        with col2:
            if st.button("❓ 帮助", use_container_width=True, help="查看所有功能"):
                response = ai_service.get_response("帮助")
                st.rerun()
        
        # 股票快速查询
        st.markdown("### 🔍 股票快查")
        selected_stock = st.selectbox(
            "选择股票:",
            ["AAPL", "GOOGL", "MSFT", "TSLA", "NVDA", "AMZN"],
            key="stock_select",
            help="选择要查询的股票"
        )
        
        col1, col2 = st.columns(2)
        with col1:
            if st.button("💰 价格", use_container_width=True, help=f"查询{selected_stock}价格"):
                response = ai_service.get_response(f"{selected_stock}价格多少？")
                st.rerun()
        with col2:
            if st.button("📈 分析", use_container_width=True, help=f"分析{selected_stock}股票"):
                response = ai_service.get_response(f"{selected_stock}")
                st.rerun()
        
        # 用户输入区域
        st.markdown("### 💭 与AI对话")
        user_input = st.text_area(
            "请输入您的问题:", 
            height=80, 
            key="ai_input",
            placeholder="例如：AAPL价格多少？"
        )
        
        col1, col2 = st.columns([2, 1])
        with col1:
            send_button = st.button("🚀 发送", type="primary", use_container_width=True)
        with col2:
            clear_button = st.button("🗑️ 清空", use_container_width=True, help="清空对话历史")
        
        if send_button and user_input.strip():
            response = ai_service.get_response(user_input)
            st.success("✅ 已发送！")
            st.rerun()
        
        if clear_button:
            ai_service.clear_history()
            st.success("🗑️ 历史已清空！")
            st.rerun()
        
        # 对话历史
        st.markdown("### 💬 对话历史")
        history = ai_service.get_conversation_history()
        if history:
            # 只显示最近3条对话，避免侧边栏过长
            recent_conversations = history[-3:]
            for i, chat in enumerate(reversed(recent_conversations)):
                with st.expander(f"💬 对话 {len(recent_conversations)-i}", expanded=(i == 0)):
                    st.markdown(f"**👤 您:** {chat['user']}")
                    st.markdown(f"**🤖 AI:** {chat['assistant']}")
            
            if len(history) > 3:
                st.info(f"📝 共有 {len(history)} 条对话记录")
        else:
            st.info("💡 开始您的第一次对话吧！")
        
        # 底部信息
        st.markdown("---")
        st.markdown("""
        <div style="text-align: center; color: #666; font-size: 0.8rem;">
            💡 提示：直接问我股票价格<br>
            例如："AAPL多少钱？"
        </div>
        """, unsafe_allow_html=True)


@streamlit_app()
def main(app_framework):
    """主应用入口"""
    # 设置服务和资源
    setup_services()
    setup_assets()
    
    # 注册中间件
    middleware_manager = app_framework.middleware_manager
    middleware_manager.register_middleware(MiddlewareType.REQUEST, request_logging_middleware)
    middleware_manager.register_middleware(MiddlewareType.REQUEST, security_middleware)
    
    # 渲染AI助手侧边栏
    render_ai_assistant_sidebar()
    
    # 运行应用
    app_framework.run()


if __name__ == "__main__":
    # 创建应用配置
    config = AppConfig(
        app_name="Penny Scanner Demo",
        version="1.0.0",
        debug=True,
        theme="light",
        language="zh-CN",
        cache_enabled=True,
        plugin_enabled=True,
        analytics_enabled=True
    )
    
    # 运行应用
    main()