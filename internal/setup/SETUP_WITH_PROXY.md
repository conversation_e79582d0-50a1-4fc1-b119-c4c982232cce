# 使用代理服务配置<PERSON> G<PERSON>ub Actions

## 🎯 你的配置情况

你使用的是第三方代理服务来访问Claude：
```bash
ANTHROPIC_AUTH_TOKEN=sk-BQonAHs8AgFSxk2dt0SuQgWizTa0hoyEJBDWIljP1HuGuLs2
ANTHROPIC_BASE_URL=https://anyrouter.top
```

## 🔧 需要在GitHub中设置的Secrets

1. 访问你的GitHub仓库：https://github.com/your-username/cauldron/settings/secrets/actions

2. 添加以下两个secrets：

### Secret 1: ANTHROPIC_AUTH_TOKEN
- **Name**: `ANTHROPIC_AUTH_TOKEN`
- **Value**: `sk-BQonAHs8AgFSxk2dt0SuQgWizTa0hoyEJBDWIljP1HuGuLs2`

### Secret 2: ANTHROPIC_BASE_URL  
- **Name**: `ANTHROPIC_BASE_URL`
- **Value**: `https://anyrouter.top`

## ✅ 已经为你调整的配置

我已经修改了workflow文件来支持你的代理设置：

### `.github/workflows/claude.yml`
```yaml
with:
  anthropic-api-key: ${{ secrets.ANTHROPIC_AUTH_TOKEN }}
  anthropic-base-url: ${{ secrets.ANTHROPIC_BASE_URL }}
```

### `.github/workflows/claude-automation.yml`  
```yaml
with:
  anthropic_api_key: ${{ secrets.ANTHROPIC_AUTH_TOKEN }}
  anthropic_base_url: ${{ secrets.ANTHROPIC_BASE_URL }}
```

## 🚀 配置完成后的测试

1. **提交这些workflow文件到GitHub**
2. **添加上面的两个secrets**
3. **在任何Issue或PR中测试**：

```
@claude 你好！请介绍一下炼妖壶项目
```

## 💰 费用说明

使用anyrouter.top这样的代理服务：
- ✅ **通常比官方便宜**
- ✅ **国内访问更稳定**  
- ✅ **支持多种支付方式**
- ⚠️ **需要确保代理服务的稳定性**

## 🔍 可能的问题和解决方案

### 如果Claude Actions不工作：

1. **检查代理服务状态**：确保anyrouter.top正常运行
2. **验证token有效性**：在本地测试一下API调用
3. **检查GitHub Secrets**：确保名称和值都正确
4. **查看Actions日志**：GitHub Actions页面查看错误信息

### 本地测试代理连接：
```bash
curl -H "Authorization: Bearer sk-BQonAHs8AgFSxk2dt0SuQgWizTa0hoyEJBDWIljP1HuGuLs2" \
     -H "Content-Type: application/json" \
     -d '{"model":"claude-3-5-sonnet-20241022","max_tokens":100,"messages":[{"role":"user","content":"Hello"}]}' \
     https://anyrouter.top/v1/messages
```

## 🎉 配置成功后的效果

你将拥有：
- 🤖 **GitHub中的Claude助手**：直接在PR/Issue中对话
- 🔄 **自动化工作流**：每日市场分析、代码审查等
- 💰 **成本可控**：使用你现有的代理服务账户
- 🚀 **无缝集成**：与炼妖壶项目完美结合

---

**总结：你只需要把现有的token和base_url添加到GitHub Secrets中即可！** ✨