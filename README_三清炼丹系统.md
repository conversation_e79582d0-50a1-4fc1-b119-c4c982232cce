# 三清炼丹系统

三清炼丹系统是一个命令行交互界面，用于与太上老君和八仙进行对话，模拟炼丹场景，以及验证API状态。

## 功能特点

- **与太上老君对话**：向道教至高神祇太上老君请教问题
- **与八仙对话**：与八仙（铁拐李、吕洞宾、何仙姑、蓝采和、张果老、韩湘子、曹国舅、钟离权）进行交流
- **模拟炼丹场景**：体验太上老君和八仙炼丹的互动场景
- **验证API状态**：检查太上老君API和八仙炼丹炉API的可用性

## 系统要求

- Python 3.8+
- 必要的Python包：requests, python-dotenv

## 安装

1. 克隆仓库或下载源代码
2. 安装依赖：

```bash
pip install -r requirements.txt
```

3. 配置API密钥：
   - 复制`.env.example`为`.env`
   - 在`.env`文件中填入您的API密钥

## API密钥配置

系统需要以下API密钥：

- `TAISHANG_API_KEY`：太上老君API密钥（魔搭）
- `BAXIAN_API_KEY`：八仙炼丹炉API密钥（OpenRouter）

您可以使用`verify_api_keys.py`脚本验证API密钥是否正确设置。

## 使用方法

### 命令行界面

```bash
python sanqing_alchemy.py
```

### 命令行参数

- `--taishang`：直接启动太上老君对话
- `--baxian`：直接启动八仙对话
- `--scene`：直接启动炼丹场景模拟
- `--verify`：直接验证API状态

示例：

```bash
python sanqing_alchemy.py --taishang
```

## 系统架构

- `sanqing_alchemy.py`：主程序，包含命令行界面和核心功能
- `verify_api_keys.py`：API密钥验证工具

## 注意事项

- API密钥必须正确设置才能使用系统功能
- 请确保网络连接正常，以便系统能够访问API服务

## 许可证

本项目遵循MIT许可证。