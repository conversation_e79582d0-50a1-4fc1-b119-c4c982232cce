# 🏛️ 稷下学宫 (Jixia Academy)

> 基于AutoGen的多智能体金融分析与辩论系统

## 🎯 项目简介

稷下学宫是一个创新的AI金融分析平台，通过八仙智能体的多角度辩论，为投资决策提供深度洞察。

### 核心特性

- 🤖 **八仙智能体**: 吕洞宾、何仙姑等8个专业投资角色
- 📊 **实时数据**: RSS新闻流 + MongoDB存储
- 🎭 **多角度辩论**: 技术分析 vs 价值投资 vs 情绪分析
- 🔄 **自动化流程**: N8N工作流 + MCP工具集成
- 📈 **可视化界面**: Streamlit交互式界面

## 🚀 快速开始

### 环境要求
- Python 3.11+
- MongoDB Atlas
- OpenRouter API密钥

### 安装部署
```bash
# 1. 克隆项目
git clone https://github.com/your-username/jixia-academy.git
cd jixia-academy

# 2. 安装依赖
pip install -r requirements.txt

# 3. 配置环境变量
cp .env.example .env
# 编辑.env文件，填入你的API密钥

# 4. 启动应用
streamlit run app/streamlit_app.py
```

### Heroku部署
```bash
# 推送到Heroku
git push heroku main
```

## 📖 文档

- [快速开始](docs/getting_started.md)
- [系统架构](docs/architecture/)
- [API文档](docs/api/)

## 🤝 贡献

欢迎提交Issue和Pull Request！

## 📄 许可证

MIT License
