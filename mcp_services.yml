# MCP服务配置文件
# 统一管理所有MCP服务，解决stdio/SSE/HTTP混合管理问题

services:
  # Yahoo Finance MCP (stdio -> HTTP包装)
  - name: yahoo-finance
    type: stdio
    command: uv
    args: 
      - "--directory"
      - "./scripts/mcp/yahoo-finance-mcp"
      - "run"
      - "yahoo-finance-mcp"
    env:
      PYTHONPATH: "./scripts/mcp/yahoo-finance-mcp/src"
    dependencies:
      - "uv"
      - "python>=3.9"
    auto_restart: true
    description: "Yahoo Finance数据获取工具"
    
  # Cauldron Financial Tools (HTTP)
  - name: cauldron-financial
    type: http
    url: "https://cauldron.herokuapp.com/api/mcp"
    health_check: "https://cauldron.herokuapp.com/health"
    env:
      CAULDRON_API_KEY: "${CAULDRON_API_KEY}"
    auto_restart: false
    description: "炼妖壶金融分析工具集"
    
  # Tusita Palace N8N Workflows (stdio)
  - name: tusita-palace
    type: stdio
    command: python
    args:
      - "-m"
      - "jixia_academy_clean.core.tusita_palace_mcp"
    env:
      N8N_WEBHOOK_URL: "${N8N_WEBHOOK_URL}"
      N8N_API_KEY: "${N8N_API_KEY}"
      PYTHONPATH: "."
    dependencies:
      - "python>=3.9"
      - "httpx"
      - "asyncio"
    auto_restart: true
    description: "兜率宫N8N工作流集成"
    
  # Heroku Inference (SSE)
  - name: heroku-inference
    type: sse
    url: "${HEROKU_INFERENCE_URL}"
    env:
      HEROKU_INFERENCE_ID: "${HEROKU_INFERENCE_ID}"
    auto_restart: false
    description: "Heroku托管推理服务"
    
  # Local Conductor System (stdio)
  - name: local-conductor
    type: stdio
    command: python
    args:
      - "-m"
      - "src.core.local_conductor_system"
    env:
      PYTHONPATH: "."
      DATABASE_URL: "${DATABASE_URL}"
    dependencies:
      - "python>=3.9"
      - "sqlalchemy"
      - "asyncio"
    auto_restart: true
    description: "本地指挥系统"
    
  # Zilliz Truth Source (HTTP)
  - name: zilliz-truth
    type: http
    url: "https://api.cloud.zilliz.com.cn"
    health_check: "https://api.cloud.zilliz.com.cn/v1/health"
    env:
      ZILLIZ_CLOUD_TOKEN: "${ZILLIZ_CLOUD_TOKEN}"
      ZILLIZ_CLOUD_REGION: "${ZILLIZ_CLOUD_REGION}"
    auto_restart: false
    description: "Zilliz向量数据库服务"

# 全局配置
global:
  # 管理器端口
  manager_port: 8090
  
  # 日志级别
  log_level: INFO
  
  # 健康检查间隔(秒)
  health_check_interval: 30
  
  # 自动重启延迟(秒)
  restart_delay: 5
  
  # 最大重启次数
  max_restart_attempts: 3

# 服务组 - 可以批量操作
service_groups:
  financial:
    - yahoo-finance
    - cauldron-financial
    - zilliz-truth
    
  workflow:
    - tusita-palace
    - local-conductor
    
  inference:
    - heroku-inference
    
  core:
    - cauldron-financial
    - tusita-palace
    - local-conductor