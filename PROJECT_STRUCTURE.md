# 炼妖壶项目结构

## 项目概述

**炼妖壶** 是基于N8N事件驱动的智能投资决策系统，通过稷下学宫AI辩论系统提供专业投资建议。

### 核心理念
- **N8N事件驱动** - 真实市场事件通过N8N工作流自动触发
- **三清八仙架构** - 分层决策，八仙平辈辩论，三清上层决策
- **零停机设计** - 支持实时模型切换和无缝升级
- **社交网络集成** - 长毛象平台实时展示辩论过程
- **多层级产品** - 免费版炼妖壶、高级版降魔杵、至尊版打神鞭

## 项目结构

```
cauldron/
├── app/                               # 应用入口
│   ├── streamlit_app.py              # 主Streamlit应用
│   └── cauldron_app.py               # 炼妖壶核心应用
├── src/                              # 源代码
│   ├── core/                         # 核心模块
│   │   ├── debate_purification.py    # 辩论净化系统
│   │   ├── intelligent_decision_engine.py # 智能决策引擎
│   │   └── zero_downtime_orchestrator.py # 零停机编排器
│   ├── ui/                           # 用户界面
│   │   ├── jixia_academy_ui.py       # 稷下学宫UI
│   │   ├── morning_briefing_ui.py    # 晨会简报UI
│   │   └── monkey_king_gamefi_ui.py  # 猴王修仙GameFi
│   └── mcp_server.py                 # MCP服务器
├── jixia_academy_clean/              # 稷下学宫精简版
│   ├── core/                         # 核心辩论系统
│   ├── agents/                       # AI智能体配置
│   ├── config/                       # 配置文件
│   └── tests/                        # 测试文件
├── n8n_workflows/                    # N8N工作流
│   └── daily_rss_to_zilliz_workflow.json
├── config/                           # 配置目录
│   ├── deployment/                   # 部署配置
│   ├── n8n/                          # N8N配置
│   └── services/                     # 服务配置
└── docs/                             # 文档
    ├── architecture/                 # 架构文档
    ├── features/                     # 功能文档
    └── deployment/                   # 部署文档
```

## 核心模块说明

### 1. RSS触发系统 (`src/core/rss_trigger_system.py`)

**功能**：
- RSS源监控（财经、地缘政治、科技新闻）
- 关键词权重评分系统
- 影响力阈值判断
- 事件去重和时间过滤

**关键类**：
- `RSSMonitor` - RSS监控器
- `EventTriggeredTheater` - 事件触发的韭菜小剧场
- `RSSTriggeredSystem` - 完整系统集成
- `NewsEvent` - 新闻事件数据结构

**RSS源配置**：
```python
self.rss_feeds = {
    "财经新闻": [
        "https://feeds.finance.yahoo.com/rss/2.0/headline",
        "https://www.cnbc.com/id/100003114/device/rss/rss.html",
        "https://feeds.bloomberg.com/markets/news.rss"
    ],
    "地缘政治": [
        "https://feeds.reuters.com/reuters/topNews",
        "https://rss.cnn.com/rss/edition.rss"
    ],
    "科技新闻": [
        "https://feeds.feedburner.com/techcrunch/startups",
        "https://www.wired.com/feed/rss"
    ]
}
```

**关键词权重配置**：
```python
self.keyword_weights = {
    # 地缘政治 (高权重)
    "战争": 90, "冲突": 85, "制裁": 80, "导弹": 95,
    "伊朗": 85, "以色列": 85, "中东": 85,
    
    # 货币政策 (高权重)
    "美联储": 90, "加息": 85, "降息": 85, "通胀": 80,
    
    # 金融危机 (极高权重)
    "银行倒闭": 95, "金融危机": 95, "破产": 90,
    
    # 科技突破 (中高权重)
    "AI": 70, "ChatGPT": 80, "芯片": 70,
    
    # 市场异动 (中权重)
    "暴跌": 85, "暴涨": 80, "熔断": 95
}
```

### 2. 九大主演配置

**光谱风险感知设计**：
```python
self.investors = {
    "洪珏": {"risk": 10, "color": "🔴", "personality": "热血追涨型"},
    "陈琉": {"risk": 20, "color": "🟠", "personality": "盲目乐观激进型"},
    "黄琥": {"risk": 35, "color": "🟡", "personality": "盲信技术分析型"},
    "陆珀": {"risk": 50, "color": "🟢", "personality": "跟风从众型"},
    "兰琪": {"risk": 65, "color": "🔵", "personality": "消息灵通但过度解读型"},
    "典瑛": {"risk": 75, "color": "🟣", "personality": "恐惧与贪婪拉扯型"},
    "梓珂": {"risk": 85, "color": "🟪", "personality": "自以为是的控制感型"},
    "白瑞": {"risk": 95, "color": "⚪", "personality": "痛定思痛型旁观者"},
    "贺珍": {"risk": 100, "color": "⚫", "personality": "冷酷的收割者"}
}
```

### 3. UI界面系统 (`src/ui/rss_theater_ui.py`)

**主要功能**：
- 🔍 实时监控 - RSS源扫描和自动刷新
- 🎭 手动触发 - 自定义事件和手动触发
- 📊 事件分析 - 影响力分布和统计图表
- ⚙️ 系统设置 - API配置和参数调整

**界面特色**：
- 聊天气泡样式的AI对话展示
- 实时进度条和状态指示
- 响应式布局和数据可视化
- 光谱风险感知图表

### 4. OpenRouter API集成

**模型配置**：
- 主要模型：`deepseek/deepseek-chat` (免费)
- 备选模型：`meta-llama/llama-3.2-3b-instruct:free`
- API限制保护：请求间隔和错误处理
- 备用回复机制：API失败时的降级方案

**提示词模板**：
```python
prompt = f"""你是{name}，一个散户投资者。

你的特征：
- 风险感知: {config['risk']}%
- 性格: {config['personality']}

刚刚发生了重大市场事件：
标题: {event.title}
关键词: {', '.join(event.keywords)}

请以{name}的身份，用第一人称简短回应这个事件（50字以内）。
要体现你的风险感知水平和性格特点。语言要生动、口语化，像在群里聊天。

直接回复，不要加前缀："""
```

## 技术特色

### 1. 异步处理架构
- 使用AsyncIO处理RSS获取和AI API调用
- 并发处理多个RSS源
- 非阻塞的用户界面

### 2. 事件驱动设计
- RSS事件触发机制
- 影响力评分算法
- 阈值突破检测

### 3. 容错和降级
- API失败时的备用回复
- RSS源异常处理
- 网络超时保护

### 4. 数据持久化
- 会话状态管理
- 事件去重机制
- 历史记录保存

## 部署和配置

### 环境变量
```bash
# OpenRouter API Keys (支持多个)
OPENROUTER_API_KEY=your_primary_key
OPENROUTER_API_KEY_1=your_backup_key_1
OPENROUTER_API_KEY_2=your_backup_key_2
OPENROUTER_API_KEY_3=your_backup_key_3
OPENROUTER_API_KEY_4=your_backup_key_4
```

### 依赖包
```bash
pip install streamlit aiohttp feedparser plotly pandas
```

### 启动命令
```bash
streamlit run streamlit_app.py --server.port 8505
```

## 测试和验证

### 1. 独立测试 (`standalone_rss_test.py`)
- 不依赖其他模块的完整测试
- RSS源连接测试
- AI API调用测试
- 九大主演回复测试

### 2. 系统集成测试 (`test_rss_system.py`)
- 完整系统功能测试
- 模块间集成测试
- 错误处理测试

### 3. 演示脚本
- `auto_demo.py` - 自动演示版本
- `terminal_demo.py` - 终端演示版本

## 商业价值

### 1. 差异化竞争优势
- 全球首创的RSS触发AI韭菜小剧场
- 光谱风险感知的创新设计
- 寓教于乐的投资教育模式

### 2. 用户粘性
- 实时事件驱动的内容更新
- 娱乐性和教育性并重
- 社交分享和病毒传播潜力

### 3. 技术护城河
- 复杂的事件识别算法
- 多维度的风险感知模型
- 成熟的AI对话生成系统

## 未来扩展

### 1. 数据源扩展
- 更多RSS源接入
- 社交媒体情绪监控
- 实时新闻API集成

### 2. AI模型优化
- 更多免费模型支持
- 个性化对话风格
- 情绪分析增强

### 3. 功能增强
- 用户自定义角色
- 历史事件回放
- 预测模型集成

---

**作者**: 太公心易BI系统  
**版本**: v1.0  
**更新时间**: 2025-01-01  
**联系方式**: 太公心易团队
