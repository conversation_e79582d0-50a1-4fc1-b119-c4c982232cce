# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Essential Commands

### Development & Testing
- `make test` - Run all tests
- `make test-unit` - Run unit tests
- `make test-integration` - Run integration tests
- `make test-fast` - Run quick tests (skip slow ones)
- `make test-coverage` - Run tests with coverage report
- `make lint` - Check code syntax
- `pytest tests/` - Direct pytest execution
- `python run_tests.py --type all` - Custom test runner

### Application Startup
- `streamlit run app/streamlit_app.py` - Start main Streamlit app
- `python app/cauldron_app.py` - Start Cauldron app variant
- `make start-app` - Alternative app start command
- `make start-cauldron` - Start Cauldron variant

### Debugging & Diagnostics
- `make debug-ib` - Debug Interactive Brokers connection
- `make debug-zilliz` - Debug Zilliz vector database connection
- `python scripts/debug/diagnose_ib_connection.py` - IB diagnostics
- `python scripts/debug/zilliz_demo_script.py` - Zilliz diagnostics

### Dependencies
- `make install` - Install production dependencies
- `make dev-install` - Install development dependencies (includes pytest, coverage tools)
- `pip install -r requirements.txt` - Basic dependency install

### MCP (Model Context Protocol) Services
- `docker-compose -f docker-compose.mcp.yml up` - Start MCP service stack
- `python start_mcp_manager.py` - Start MCP manager
- Services include: Yahoo Finance MCP, Cauldron MCP, Tusita Palace MCP

## Architecture Overview

### Core Structure
- **稷下学宫 (Jixia Academy)**: Multi-agent AI debate system using AutoGen framework with "Three Pure Ones + Eight Immortals" (三清八仙) architecture
- **太公心易 (Taigong Xinyi)**: Main BI system integrating multiple data sources and trading strategies
- **MCP Integration**: Model Context Protocol services for modular AI tool integration

### Key Directories
- `src/` - Core business logic and utilities
- `app/` - Streamlit UI applications and components
- `jixia_academy_clean/` - Refactored AI debate system
- `scripts/` - Automation, deployment, and utility scripts
- `config/` - Configuration files for various services
- `tests/` - Test suites (unit, integration, e2e)

### Data Systems
- **PostgreSQL**: Primary database (configurable for local/Heroku)
- **MongoDB Atlas**: Knowledge graph and RSS analytics storage
- **Zilliz/Milvus**: Vector database for semantic search
- **Redis**: Caching and message queuing (in MCP stack)

### External Integrations
- **Interactive Brokers (IB)**: Real-time market data and trading
- **Yahoo Finance**: Market data via MCP service
- **N8N**: Workflow automation platform
- **RapidAPI**: Multiple financial data providers
- **OpenRouter**: AI model routing and management

## Development Patterns

### Testing Structure
- Unit tests: `tests/unit/`
- Integration tests: `tests/integration/`
- Test configuration: `pyproject.toml` pytest section
- Coverage reporting with exclusions for migrations and cache files

### AI/ML Components
- AutoGen agents in `jixia_academy_clean/agents/`
- Debate system prompts in `jixia_academy_clean/config/prompts/`
- Model configurations support multiple providers (OpenRouter, local models)

### Configuration Management
- Environment variables via `.env` files
- Doppler integration for secret management
- Multi-environment configs (local, Heroku, production)

### Code Quality
- Black formatting (line length 100)
- isort import sorting
- MyPy type checking enabled
- Comprehensive linting via `make lint`

## Key Features to Understand

### 1. Jixia Academy Debate System
Multi-agent AI system where different "immortal" personas debate financial topics. Uses structured handoff patterns and configurable agent personalities.

### 2. Market Data Pipeline
Integrates multiple data sources (IB, Yahoo Finance, domestic Chinese markets) with real-time processing and storage across multiple database systems.

### 3. MCP Service Architecture
Microservices architecture using Docker Compose with load balancing, health checks, and service discovery via Consul.

### 4. GameFi Integration
Blockchain and DeFi analysis tools integrated into the trading platform, including specialized UI components for crypto markets.

## Important Notes

- **Chinese Financial Focus**: System heavily oriented toward Chinese market analysis with A-share and domestic data providers
- **Multi-Model AI**: Supports various AI providers through OpenRouter with fallback mechanisms
- **Heroku Deployment**: Configured for Heroku with PostgreSQL addon and environment-specific configurations
- **Real-time Components**: IB integration requires careful async handling due to Streamlit event loop constraints
- **Docker Dependencies**: MCP services require Docker for full functionality