#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
太公心易BI系统 - 简化版Streamlit应用
不依赖数据库，专注核心功能展示

功能：
- 🔪 对韭当割 - RSS韭菜小剧场
- 💎 六壬察心 - IB基本面数据
- 🏛️ 系统概览

作者：太公心易BI系统
版本：v1.0
"""

import streamlit as st
import pandas as pd
import os
import sys
from pathlib import Path
from datetime import datetime
from dotenv import load_dotenv

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 加载环境变量
load_dotenv()

# RSS功能已迁移到N8N平台，移除相关导入
RSS_AVAILABLE = False
print("ℹ️ RSS功能已迁移到N8N平台")

try:
    from src.ui.ib_simple_ui import IBSimpleUI
    IB_AVAILABLE = True
except ImportError as e:
    IB_AVAILABLE = False
    print(f"IB模块导入失败: {e}")


def render_system_overview():
    """渲染系统概览"""
    st.markdown("### 🏛️ 太公心易BI系统")
    st.markdown("*炼妖壶 - 事件驱动的智能投资分析系统*")
    
    # 系统状态
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric("🔪 对韭当割", "✅ 可用" if RSS_AVAILABLE else "❌ 不可用")
    
    with col2:
        st.metric("💎 六壬察心", "✅ 可用" if IB_AVAILABLE else "❌ 不可用")
    
    with col3:
        api_key = (
            os.getenv('OPENROUTER_API_KEY') or 
            os.getenv('OPENROUTER_API_KEY_1') or
            os.getenv('OPENROUTER_API_KEY_2') or
            os.getenv('OPENROUTER_API_KEY_3') or
            os.getenv('OPENROUTER_API_KEY_4')
        )
        st.metric("🔑 API密钥", "✅ 已配置" if api_key else "❌ 未配置")
    
    with col4:
        ib_host = os.getenv("IB_HOST")
        ib_port = os.getenv("IB_PORT")
        st.metric("🔌 IB配置", "✅ 已配置" if ib_host and ib_port else "❌ 未配置")
    
    # 产品线介绍
    st.markdown("---")
    st.markdown("#### 🏛️ 太公心易三神器产品体系")
    
    col1, col2, col3 = st.columns(3)
    
    with col1:
        st.markdown("""
        **🆓 炼妖壶 (Cauldron)**
        - RSS触发韭菜小剧场
        - 九大主演AI海聊
        - 影响力评分系统
        - 完全免费开源
        """)
    
    with col2:
        st.markdown("""
        **💎 降魔杵 (Hammer) - $39/月**
        - 六壬察心：IB深度数据
        - 遁甲择时：策略超市
        - 岩石力学交易模型
        - 高级会员功能
        """)
    
    with col3:
        st.markdown("""
        **👑 打神鞭 (Loom) - $128/月**
        - 太乙观澜：全市场配置
        - 射覆系统：64卦+10天干
        - 大兵团作战方案
        - 至尊会员专享
        """)
    
    # 神话背景
    st.markdown("---")
    st.markdown("#### 🎭 神话背景")
    
    st.info("""
    **中西神话融合的产品命名：**
    
    🆓 **Cauldron (炼妖壶)**: 黑森林巫婆熬制孟婆汤的坩埚，炼制市场真相
    
    💎 **Hammer (降魔杵)**: 北欧雷神托尔之锤，降伏市场心魔的正义之器
    
    👑 **Loom (打神鞭)**: 希腊神话命运三女神的织机，编织市场命运之线
    """)
    
    # 技术架构
    st.markdown("---")
    st.markdown("#### 🏗️ 技术架构特色")
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.markdown("""
        **🔄 零停机架构：**
        - 本地Ollama指挥中心
        - Heroku蓝绿部署
        - 智能故障转移
        - 99.9%+ SLA保证
        """)
    
    with col2:
        st.markdown("""
        **🎯 事件驱动系统：**
        - RSS实时监控
        - N8N工作流触发
        - 影响力智能评分
        - 自动化AI海聊
        """)
    
    # 使用统计
    st.markdown("---")
    st.markdown("#### 📊 系统统计")
    
    # 模拟统计数据
    stats_data = {
        "指标": ["RSS源数量", "监控关键词", "投资者角色", "API调用次数", "成功率"],
        "数值": ["21个", "45个", "9位", "1,234次", "94.2%"],
        "状态": ["🟢 正常", "🟢 正常", "🟢 正常", "🟢 正常", "🟢 优秀"]
    }
    
    stats_df = pd.DataFrame(stats_data)
    st.dataframe(stats_df, use_container_width=True, hide_index=True)


def main():
    """主函数"""
    # 页面配置
    st.set_page_config(
        page_title="太公心易BI系统",
        page_icon="🏛️",
        layout="wide",
        initial_sidebar_state="expanded"
    )
    
    # 侧边栏
    with st.sidebar:
        st.markdown("### 🏛️ 太公心易BI")
        st.markdown("*炼妖壶 - 智能投资分析*")
        
        # 会员等级选择
        membership_levels = {
            "🆓 炼妖壶": "免费版 - RSS触发韭菜小剧场",
            "💎 降魔杵": "$39/月 - 六壬察心+遁甲择时", 
            "👑 打神鞭": "$128/月 - 太乙观澜+射覆系统"
        }
        
        current_membership = st.selectbox(
            "选择会员等级",
            list(membership_levels.keys()),
            index=0
        )
        
        st.info(membership_levels[current_membership])
        
        # 系统状态
        st.markdown("---")
        st.markdown("#### 📊 系统状态")
        
        if RSS_AVAILABLE:
            st.success("🔪 对韭当割 - 正常")
        else:
            st.error("🔪 对韭当割 - 不可用")
        
        if IB_AVAILABLE:
            st.success("💎 六壬察心 - 正常")
        else:
            st.error("💎 六壬察心 - 不可用")
        
        # 快速链接
        st.markdown("---")
        st.markdown("#### 🔗 快速链接")
        
        st.markdown("""
        - [📚 项目文档](https://github.com/jingminzhang/cauldron)
        - [🧪 RSS测试](scripts/standalone_feedburner_test.py)
        - [💎 IB测试](scripts/simple_ib_test.py)
        - [📊 论文](cauldron_paper.md)
        """)
    
    # 主要内容区域
    st.title("🏛️ 太公心易BI系统")
    st.markdown("*炼妖壶 - 事件驱动的智能投资分析系统*")
    
    # 创建标签页
    available_tabs = ["🏛️ 系统概览"]
    
    if RSS_AVAILABLE:
        available_tabs.append("🔪 对韭当割")
    
    if IB_AVAILABLE:
        available_tabs.append("💎 六壬察心")
    
    tabs = st.tabs(available_tabs)
    tab_index = 0
    
    # 系统概览
    with tabs[tab_index]:
        render_system_overview()
    tab_index += 1
    
    # 对韭当割功能已迁移到N8N平台
    
    # 六壬察心
    if IB_AVAILABLE:
        with tabs[tab_index]:
            st.markdown("### 💎 六壬察心 - IB基本面数据")
            st.markdown("**降魔杵专属功能** - 深度洞察市场情绪面，IB深度数据可视化")
            
            # 检查会员等级
            if current_membership == "🆓 炼妖壶":
                st.warning("⚠️ 此功能需要降魔杵会员等级 ($39/月)")
                st.info("💡 升级到降魔杵可解锁：六壬察心 + 遁甲择时功能")
            else:
                ib_ui = IBSimpleUI()
                ib_ui.render()
        tab_index += 1
    
    # 页脚
    st.markdown("---")
    st.markdown("""
    <div style='text-align: center; color: #666;'>
        <p>🏛️ 太公心易BI系统 v1.0 | 炼妖壶 - 事件驱动的智能投资分析</p>
        <p>基于中华哲学的投资决策支持系统 | 开源项目</p>
    </div>
    """, unsafe_allow_html=True)


if __name__ == "__main__":
    main()
