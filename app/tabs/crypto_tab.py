import streamlit as st
import pandas as pd
from typing import Optional, List, Dict, Any

def render_crypto_tab():
    """
    渲染加密货币Tab - 数字货币市场分析和监控
    采用组件化设计，类似HTML静态网站的模块化思维
    """
    # 页面标题
    _render_page_header()
    
    # 主要加密货币指标
    _render_main_cryptos()
    
    # 市场分析区域
    _render_market_analysis()
    
    # 链上数据和AI洞察
    _render_blockchain_insights()

def _render_page_header():
    """渲染页面头部"""
    st.markdown("### ⛓️ 币链市场")
    st.markdown("实时监控加密货币价格、DeFi协议和链上数据")

def _render_main_cryptos():
    """渲染主要加密货币指标"""
    st.subheader("💰 主要加密货币")
    
    # 创建加密货币展示区域
    crypto_col1, crypto_col2, crypto_col3, crypto_col4 = st.columns(4)
    
    # 获取加密货币数据
    crypto_data = _get_crypto_data()
    
    with crypto_col1:
        _render_crypto_card("比特币 (BTC)", crypto_data.get('btc', {}))
    with crypto_col2:
        _render_crypto_card("以太坊 (ETH)", crypto_data.get('eth', {}))
    with crypto_col3:
        _render_crypto_card("BNB", crypto_data.get('bnb', {}))
    with crypto_col4:
        _render_crypto_card("Solana (SOL)", crypto_data.get('sol', {}))

def _render_crypto_card(name: str, data: Dict[str, Any]):
    """渲染单个加密货币卡片"""
    if data:
        st.metric(
            label=name,
            value=data.get('value', 'N/A'),
            delta=data.get('change', '--')
        )
    else:
        st.metric(label=name, value="加载中...", delta="--")

def _render_market_analysis():
    """渲染市场分析区域"""
    st.markdown("---")
    
    # 创建两列布局
    analysis_col1, analysis_col2 = st.columns(2)
    
    with analysis_col1:
        _render_meme_coins()
        _render_defi_protocols()
    
    with analysis_col2:
        _render_hot_tokens()
        _render_mining_data()

def _render_meme_coins():
    """渲染Meme币行情"""
    st.subheader("🐕 Meme币行情")
    
    # 获取Meme币数据
    meme_data = _get_meme_coins_data()
    
    if meme_data:
        # 创建表格显示
        df = pd.DataFrame(meme_data)
        st.dataframe(
            df,
            use_container_width=True,
            hide_index=True,
            column_config={
                "symbol": st.column_config.TextColumn("代币", width="small"),
                "name": st.column_config.TextColumn("名称", width="medium"),
                "price": st.column_config.NumberColumn("价格", format="$%.6f"),
                "change_24h": st.column_config.NumberColumn("24h涨跌", format="%.2f%%"),
                "market_cap": st.column_config.NumberColumn("市值", format="$%.2fM")
            }
        )
    else:
        st.info("正在加载Meme币数据...")

def _render_defi_protocols():
    """渲染DeFi协议"""
    st.subheader("🏦 DeFi协议")
    
    # DeFi协议数据
    defi_data = _get_defi_protocols_data()
    
    if defi_data:
        for protocol in defi_data:
            with st.container():
                col1, col2, col3 = st.columns([2, 1, 1])
                with col1:
                    st.write(f"**{protocol['name']}**")
                with col2:
                    st.write(f"TVL: ${protocol['tvl']}M")
                with col3:
                    apy = protocol.get('apy', 0)
                    color = "🟢" if apy > 5 else "🟡" if apy > 2 else "🔴"
                    st.write(f"{color} APY: {apy}%")
    else:
        st.info("正在加载DeFi协议数据...")

def _render_hot_tokens():
    """渲染热门币种"""
    st.subheader("🔥 热门币种")
    
    # 热门币种选择
    category = st.selectbox(
        "分类",
        ["AI概念", "Layer2", "GameFi", "NFT", "元宇宙"],
        key="crypto_category"
    )
    
    # 显示对应分类的热门币种
    hot_tokens = _get_hot_tokens_by_category(category)
    
    if hot_tokens:
        for token in hot_tokens:
            col1, col2, col3 = st.columns([2, 1, 1])
            with col1:
                st.write(f"**{token['symbol']}** - {token['name']}")
            with col2:
                st.write(f"${token['price']}")
            with col3:
                change = token.get('change_24h', 0)
                color = "🟢" if change > 0 else "🔴"
                st.write(f"{color} {change:+.2f}%")
    else:
        st.info(f"正在加载{category}币种数据...")

def _render_mining_data():
    """渲染挖矿数据"""
    st.subheader("⛏️ 挖矿数据")
    
    # 挖矿指标
    mining_metrics = {
        "BTC难度": {"value": "62.46T", "change": "+2.1%"},
        "ETH Gas费": {"value": "25 Gwei", "change": "-15.3%"},
        "全网算力": {"value": "450 EH/s", "change": "+1.8%"},
        "挖矿收益": {"value": "$0.08/TH", "change": "-5.2%"}
    }
    
    for metric, data in mining_metrics.items():
        col1, col2 = st.columns([2, 1])
        with col1:
            st.metric(metric, data["value"], data["change"])
        with col2:
            # 添加趋势图标
            trend = "📈" if "+" in data["change"] else "📉"
            st.write(trend)

def _render_blockchain_insights():
    """渲染区块链数据和AI洞察"""
    st.markdown("---")
    st.subheader("🔗 链上数据")
    
    # 创建标签页
    insight_tab1, insight_tab2, insight_tab3 = st.tabs(["链上指标", "大户动向", "AI洞察"])
    
    with insight_tab1:
        _render_onchain_metrics()
    
    with insight_tab2:
        _render_whale_movements()
    
    with insight_tab3:
        _render_ai_insights()

def _render_onchain_metrics():
    """渲染链上指标"""
    col1, col2 = st.columns(2)
    
    with col1:
        st.subheader("比特币链上数据")
        btc_metrics = {
            "活跃地址数": "950,000",
            "交易数量": "280,000",
            "平均交易费": "$2.50",
            "MVRV比率": "1.85"
        }
        for metric, value in btc_metrics.items():
            st.metric(metric, value)
    
    with col2:
        st.subheader("以太坊链上数据")
        eth_metrics = {
            "Gas使用率": "65%",
            "DeFi锁仓量": "$45.2B",
            "NFT交易量": "$12.5M",
            "Layer2 TVL": "$8.9B"
        }
        for metric, value in eth_metrics.items():
            st.metric(metric, value)

def _render_whale_movements():
    """渲染大户动向"""
    st.subheader("🐋 大户交易监控")
    
    whale_data = [
        {"时间": "2小时前", "币种": "BTC", "数量": "500", "类型": "转入交易所", "地址": "1A1z...P2SH"},
        {"时间": "4小时前", "币种": "ETH", "数量": "10,000", "类型": "转出交易所", "地址": "0x742d...35Cc"},
        {"时间": "6小时前", "币种": "USDT", "数量": "50,000,000", "类型": "大额转账", "地址": "TQn9Y...3MNh"}
    ]
    
    df = pd.DataFrame(whale_data)
    st.dataframe(df, use_container_width=True, hide_index=True)

def _render_ai_insights():
    """渲染AI洞察"""
    st.subheader("🤖 AI市场洞察")
    
    insights = [
        "📊 **技术分析**: BTC在$65,000附近形成强支撑，短期有望突破$70,000阻力位",
        "📈 **情绪分析**: 市场恐慌贪婪指数为75，处于贪婪区间，建议谨慎操作",
        "🔍 **资金流向**: 过去24小时有$2.3B资金流入比特币ETF，机构需求强劲",
        "⚡ **异常监控**: 检测到某DeFi协议出现大额资金异动，请关注风险"
    ]
    
    for insight in insights:
        st.markdown(insight)
        st.markdown("")

def _get_crypto_data() -> Dict[str, Dict[str, Any]]:
    """获取加密货币数据"""
    return {
        'btc': {'value': '$67,500', 'change': '+2.3%'},
        'eth': {'value': '$3,850', 'change': '+1.8%'},
        'bnb': {'value': '$580', 'change': '+0.9%'},
        'sol': {'value': '$185', 'change': '+4.2%'}
    }

def _get_meme_coins_data() -> List[Dict[str, Any]]:
    """获取Meme币数据"""
    if ("ib_thread" in st.session_state and 
        st.session_state.ib_thread.ib_runner and
        hasattr(st.session_state.ib_thread.ib_runner, 'crypto_data')):
        
        runner = st.session_state.ib_thread.ib_runner
        data = runner.crypto_data
        
        if isinstance(data, pd.DataFrame) and not data.empty:
            return data.to_dict('records')
    
    # 返回模拟数据
    return [
        {"symbol": "DOGE", "name": "Dogecoin", "price": 0.085, "change_24h": 5.2, "market_cap": 12500},
        {"symbol": "SHIB", "name": "Shiba Inu", "price": 0.000025, "change_24h": -2.1, "market_cap": 8900},
        {"symbol": "PEPE", "name": "Pepe", "price": 0.0000085, "change_24h": 15.8, "market_cap": 3200},
        {"symbol": "FLOKI", "name": "Floki", "price": 0.00018, "change_24h": 8.5, "market_cap": 1800}
    ]

def _get_defi_protocols_data() -> List[Dict[str, Any]]:
    """获取DeFi协议数据"""
    return [
        {"name": "Uniswap", "tvl": 4500, "apy": 12.5},
        {"name": "Aave", "tvl": 8200, "apy": 8.3},
        {"name": "Compound", "tvl": 3100, "apy": 6.8},
        {"name": "MakerDAO", "tvl": 6800, "apy": 4.2}
    ]

def _get_hot_tokens_by_category(category: str) -> List[Dict[str, Any]]:
    """根据分类获取热门币种"""
    categories = {
        "AI概念": [
            {"symbol": "FET", "name": "Fetch.ai", "price": 1.25, "change_24h": 8.5},
            {"symbol": "AGIX", "name": "SingularityNET", "price": 0.68, "change_24h": 12.3},
            {"symbol": "OCEAN", "name": "Ocean Protocol", "price": 0.85, "change_24h": 6.7}
        ],
        "Layer2": [
            {"symbol": "MATIC", "name": "Polygon", "price": 0.95, "change_24h": 3.2},
            {"symbol": "ARB", "name": "Arbitrum", "price": 1.15, "change_24h": 5.8},
            {"symbol": "OP", "name": "Optimism", "price": 2.35, "change_24h": 4.1}
        ],
        "GameFi": [
            {"symbol": "AXS", "name": "Axie Infinity", "price": 8.50, "change_24h": -2.1},
            {"symbol": "SAND", "name": "The Sandbox", "price": 0.45, "change_24h": 1.8},
            {"symbol": "MANA", "name": "Decentraland", "price": 0.38, "change_24h": 0.5}
        ],
        "NFT": [
            {"symbol": "BLUR", "name": "Blur", "price": 0.28, "change_24h": 15.2},
            {"symbol": "LOOKS", "name": "LooksRare", "price": 0.12, "change_24h": 8.9},
            {"symbol": "X2Y2", "name": "X2Y2", "price": 0.05, "change_24h": 3.4}
        ],
        "元宇宙": [
            {"symbol": "META", "name": "Meta", "price": 12.50, "change_24h": 7.2},
            {"symbol": "RACA", "name": "Radio Caca", "price": 0.0002, "change_24h": 25.8},
            {"symbol": "STARL", "name": "StarLink", "price": 0.000008, "change_24h": 18.5}
        ]
    }
    
    return categories.get(category, [])