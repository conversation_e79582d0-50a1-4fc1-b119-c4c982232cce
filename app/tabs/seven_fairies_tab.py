"""
七姐妹星团 - 全球科技生态结构分析Tab
七个独立的Root恒星系统，基于中美夫妻论的三层架构分析
恒星(天子) → 行星(大夫) → 卫星(士) + 叛军势力
"""

import streamlit as st
import requests
import pandas as pd
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import os
from datetime import datetime, timedelta
import time
import json
# import asyncio
# from concurrent.futures import ThreadPoolExecutor

class SevenSistersAnalyzer:
    """七姐妹星团分析器 - 七个独立Root的三层架构分析"""

    def __init__(self):
        # 七姐妹星团 - 七个独立的Root恒星系统
        self.seven_sisters = {
            'Apple_Ecosystem': {
                'star': {'name': 'Apple', 'emoji': '🍎', 'tianming': 'iOS生态系统', 'region': 'US'},
                'planets': [
                    {'name': 'Foxconn', 'role': '代工制造', 'region': 'CN', 'type': '主内承接'},
                    {'name': 'TSMC', 'role': '芯片代工', 'region': 'TW', 'type': '核心供应'},
                    {'name': 'Samsung Display', 'role': '屏幕供应', 'region': 'KR', 'type': '关键组件'}
                ],
                'satellites': [
                    {'name': 'Luxshare', 'role': '精密制造', 'region': 'CN', 'serves': 'Foxconn'},
                    {'name': 'Goertek', 'role': '声学器件', 'region': 'CN', 'serves': 'Foxconn'},
                    {'name': 'ASML', 'role': '光刻设备', 'region': 'NL', 'serves': 'TSMC'}
                ],
                'rebels': [
                    {'name': 'Huawei (HarmonyOS)', 'status': '未上市', 'threat': '生态挑战者'},
                    {'name': 'Xiaomi', 'status': '多地上市', 'threat': '性价比竞争'}
                ]
            },
            'Microsoft_Ecosystem': {
                'star': {'name': 'Microsoft', 'emoji': '💻', 'tianming': '云计算+企业软件', 'region': 'US'},
                'planets': [
                    {'name': 'Intel', 'role': 'CPU供应', 'region': 'US', 'type': '传统盟友'},
                    {'name': 'Dell/HP', 'role': 'PC制造', 'region': 'US', 'type': '渠道伙伴'},
                    {'name': 'Nvidia', 'role': 'AI算力', 'region': 'US', 'type': '新兴合作'}
                ],
                'satellites': [
                    {'name': 'Lenovo', 'role': 'PC制造', 'region': 'CN', 'serves': 'Dell/HP'},
                    {'name': 'Quanta', 'role': '服务器代工', 'region': 'TW', 'serves': 'Dell/HP'}
                ],
                'rebels': [
                    {'name': 'Alibaba Cloud', 'status': '上市', 'threat': '云计算竞争'},
                    {'name': 'Tencent Cloud', 'status': '上市', 'threat': '企业服务'}
                ]
            },
            'Google_Ecosystem': {
                'star': {'name': 'Google', 'emoji': '🔍', 'tianming': '搜索+AI+云', 'region': 'US'},
                'planets': [
                    {'name': 'TSMC', 'role': 'TPU代工', 'region': 'TW', 'type': '核心制造'},
                    {'name': 'Samsung', 'role': 'Pixel制造', 'region': 'KR', 'type': '硬件合作'},
                    {'name': 'Qualcomm', 'role': '移动芯片', 'region': 'US', 'type': '技术供应'}
                ],
                'satellites': [
                    {'name': 'BYD Electronic', 'role': '组装制造', 'region': 'CN', 'serves': 'Samsung'},
                    {'name': 'Advanced Semiconductor', 'role': '封测', 'region': 'TW', 'serves': 'TSMC'}
                ],
                'rebels': [
                    {'name': 'Baidu', 'status': '上市', 'threat': '搜索+AI竞争'},
                    {'name': 'ByteDance', 'status': '未上市', 'threat': '内容生态'}
                ]
            },
            'Amazon_Ecosystem': {
                'star': {'name': 'Amazon', 'emoji': '📦', 'tianming': '电商+云计算', 'region': 'US'},
                'planets': [
                    {'name': 'Intel/AMD', 'role': '服务器CPU', 'region': 'US', 'type': '算力基础'},
                    {'name': 'Foxconn Industrial', 'role': '服务器制造', 'region': 'CN', 'type': '主内制造'},
                    {'name': 'Logistics Partners', 'role': '物流网络', 'region': 'Global', 'type': '基础设施'}
                ],
                'satellites': [
                    {'name': 'SF Express', 'role': '物流服务', 'region': 'CN', 'serves': 'Logistics'},
                    {'name': 'Cainiao', 'role': '智能物流', 'region': 'CN', 'serves': 'Logistics'}
                ],
                'rebels': [
                    {'name': 'Alibaba', 'status': '上市', 'threat': '电商+云双重竞争'},
                    {'name': 'JD.com', 'status': '上市', 'threat': '自营电商模式'}
                ]
            },
            'Tesla_Ecosystem': {
                'star': {'name': 'Tesla', 'emoji': '⚡', 'tianming': '电动汽车+能源', 'region': 'US'},
                'planets': [
                    {'name': 'CATL', 'role': '动力电池', 'region': 'CN', 'type': '主内核心'},
                    {'name': 'BYD', 'role': '电池+制造', 'region': 'CN', 'type': '竞合关系'},
                    {'name': 'Panasonic', 'role': '电池技术', 'region': 'JP', 'type': '传统合作'}
                ],
                'satellites': [
                    {'name': 'Ganfeng Lithium', 'role': '锂资源', 'region': 'CN', 'serves': 'CATL'},
                    {'name': 'Yahua Industrial', 'role': '锂化工', 'region': 'CN', 'serves': 'CATL'},
                    {'name': 'Kedali', 'role': '结构件', 'region': 'CN', 'serves': 'CATL'}
                ],
                'rebels': [
                    {'name': 'NIO', 'status': '多地上市', 'threat': '换电模式创新'},
                    {'name': 'XPeng', 'status': '多地上市', 'threat': '智能驾驶'},
                    {'name': 'Li Auto', 'status': '多地上市', 'threat': '增程式技术'}
                ]
            },
            'Meta_Ecosystem': {
                'star': {'name': 'Meta', 'emoji': '📱', 'tianming': '社交+元宇宙', 'region': 'US'},
                'planets': [
                    {'name': 'Qualcomm', 'role': 'VR/AR芯片', 'region': 'US', 'type': '技术核心'},
                    {'name': 'Luxshare', 'role': 'VR设备制造', 'region': 'CN', 'type': '主内制造'},
                    {'name': 'Samsung Display', 'role': 'VR显示屏', 'region': 'KR', 'type': '关键组件'}
                ],
                'satellites': [
                    {'name': 'Goertek', 'role': 'VR声学', 'region': 'CN', 'serves': 'Luxshare'},
                    {'name': 'AAC Technologies', 'role': '精密器件', 'region': 'CN', 'serves': 'Luxshare'}
                ],
                'rebels': [
                    {'name': 'TikTok/ByteDance', 'status': '未上市', 'threat': '短视频霸主'},
                    {'name': 'Tencent', 'status': '上市', 'threat': '社交+游戏生态'}
                ]
            },
            'NVIDIA_Ecosystem': {
                'star': {'name': 'NVIDIA', 'emoji': '🎮', 'tianming': 'AI算力+GPU', 'region': 'US'},
                'planets': [
                    {'name': 'TSMC', 'role': '先进制程', 'region': 'TW', 'type': '独家代工'},
                    {'name': 'SK Hynix', 'role': 'HBM内存', 'region': 'KR', 'type': '关键组件'},
                    {'name': 'Supermicro', 'role': '服务器集成', 'region': 'US', 'type': '系统集成'}
                ],
                'satellites': [
                    {'name': 'Foxconn Industrial', 'role': '服务器制造', 'region': 'CN', 'serves': 'Supermicro'},
                    {'name': 'ASML', 'role': '光刻设备', 'region': 'NL', 'serves': 'TSMC'},
                    {'name': 'Applied Materials', 'role': '半导体设备', 'region': 'US', 'serves': 'TSMC'}
                ],
                'rebels': [
                    {'name': 'AMD', 'status': '上市', 'threat': 'GPU竞争者'},
                    {'name': 'Intel', 'status': '上市', 'threat': 'AI芯片入局'},
                    {'name': 'Huawei (Ascend)', 'status': '未上市', 'threat': 'AI芯片自研'}
                ]
            }
        }

        # API配置
        self.apis = {
            'yahoo_finance': {
                'host': 'yahoo-finance15.p.rapidapi.com',
                'base_url': 'https://yahoo-finance15.p.rapidapi.com'
            },
            'alpha_vantage': {
                'host': 'alpha-vantage.p.rapidapi.com',
                'base_url': 'https://alpha-vantage.p.rapidapi.com'
            },
            'seeking_alpha': {
                'host': 'seeking-alpha.p.rapidapi.com',
                'base_url': 'https://seeking-alpha.p.rapidapi.com'
            },
            'morning_star': {
                'host': 'morning-star.p.rapidapi.com',
                'base_url': 'https://morning-star.p.rapidapi.com'
            }
        }
    
    def get_headers(self, api_name):
        """获取API请求头"""
        return {
            'X-RapidAPI-Key': self.rapidapi_key,
            'X-RapidAPI-Host': self.apis[api_name]['host']
        }
    
    def create_star_system_card(self, system_name, system_data):
        """创建恒星系统卡片 - 三层架构展示"""
        star = system_data['star']

        st.markdown(f"### ⭐ {star['emoji']} {star['name']} 恒星系统")
        st.markdown(f"**天命**: {star['tianming']} | **区域**: {star['region']}")

        # 三层架构展示
        col1, col2, col3 = st.columns(3)

        # 恒星层 (天子)
        with col1:
            st.markdown("#### 🌟 恒星 (天子)")
            st.info(f"""
            **{star['name']}** {star['emoji']}

            **天命**: {star['tianming']}
            **区域**: {star['region']}
            **地位**: 生态定义者
            """)

        # 行星层 (大夫)
        with col2:
            st.markdown("#### 🪐 行星 (大夫)")
            for planet in system_data['planets']:
                region_flag = {'US': '🇺🇸', 'CN': '🇨🇳', 'TW': '🇹🇼', 'KR': '🇰🇷', 'JP': '🇯🇵', 'NL': '🇳🇱', 'Global': '🌍'}.get(planet['region'], '🌍')
                st.success(f"""
                **{planet['name']}** {region_flag}

                **角色**: {planet['role']}
                **类型**: {planet['type']}
                """)

        # 卫星层 (士)
        with col3:
            st.markdown("#### 🛰️ 卫星 (士)")
            for satellite in system_data['satellites']:
                region_flag = {'US': '🇺🇸', 'CN': '🇨🇳', 'TW': '🇹🇼', 'KR': '🇰🇷', 'JP': '🇯🇵', 'NL': '🇳🇱'}.get(satellite['region'], '🌍')
                st.warning(f"""
                **{satellite['name']}** {region_flag}

                **角色**: {satellite['role']}
                **服务**: {satellite['serves']}
                """)

        # 叛军势力
        if system_data['rebels']:
            st.markdown("#### ⚔️ 叛军势力 (分庭抗礼)")
            rebel_cols = st.columns(len(system_data['rebels']))

            for i, rebel in enumerate(system_data['rebels']):
                with rebel_cols[i]:
                    st.error(f"""
                    **{rebel['name']}** 🏴‍☠️

                    **状态**: {rebel['status']}
                    **威胁**: {rebel['threat']}
                    """)

        st.markdown("---")

    def get_stock_quote(self, symbol):
        """获取股票实时报价"""
        url = f"{self.apis['yahoo_finance']['base_url']}/api/yahoo/qu/quote/{symbol}"
        headers = self.get_headers('yahoo_finance')

        try:
            response = requests.get(url, headers=headers, timeout=10)
            if response.status_code == 200:
                return response.json()
            return {}
        except Exception as e:
            return {}
    
    def get_company_overview(self, symbol):
        """获取公司概览 - Alpha Vantage"""
        url = f"{self.apis['alpha_vantage']['base_url']}/query"
        headers = self.get_headers('alpha_vantage')
        params = {
            'function': 'OVERVIEW',
            'symbol': symbol,
            'apikey': self.alpha_vantage_key
        }
        
        try:
            response = requests.get(url, headers=headers, params=params, timeout=10)
            if response.status_code == 200:
                return response.json()
            return {}
        except Exception as e:
            return {}
    
    def get_seeking_alpha_profile(self, symbol):
        """获取Seeking Alpha分析"""
        url = f"{self.apis['seeking_alpha']['base_url']}/symbols/get-profile"
        headers = self.get_headers('seeking_alpha')
        params = {'symbols': symbol}
        
        try:
            response = requests.get(url, headers=headers, params=params, timeout=10)
            if response.status_code == 200:
                return response.json()
            return {}
        except Exception as e:
            return {}
    
    def get_morning_star_rating(self, symbol):
        """尝试获取晨星评级"""
        # 尝试不同的晨星API端点
        endpoints = [
            f"/stock/v2/get-analysis",
            f"/stock/get-detail",
            f"/market/v2/get-quotes"
        ]
        
        headers = self.get_headers('morning_star')
        
        for endpoint in endpoints:
            try:
                url = f"{self.apis['morning_star']['base_url']}{endpoint}"
                params = {'symbol': symbol}
                response = requests.get(url, headers=headers, params=params, timeout=8)
                if response.status_code == 200:
                    return response.json()
            except:
                continue
        return {}
    
    def create_china_us_analysis(self):
        """创建中美夫妻论分析"""
        st.markdown("### 🤝 中美夫妻论分析")
        st.markdown("**核心观点**: 大量中国公司承接'主内'工作，形成中美产业分工格局")

        # 统计各区域公司数量
        region_stats = {'US': 0, 'CN': 0, 'TW': 0, 'KR': 0, 'JP': 0, 'NL': 0, 'Global': 0}
        china_roles = []
        us_roles = []

        for system_name, system_data in self.seven_sisters.items():
            # 恒星 (都是美国)
            us_roles.append({
                'company': system_data['star']['name'],
                'role': '生态定义者',
                'type': '恒星'
            })

            # 行星
            for planet in system_data['planets']:
                region_stats[planet['region']] += 1
                if planet['region'] == 'CN':
                    china_roles.append({
                        'company': planet['name'],
                        'role': planet['role'],
                        'type': '行星 (主内承接)',
                        'ecosystem': system_data['star']['name']
                    })
                elif planet['region'] == 'US':
                    us_roles.append({
                        'company': planet['name'],
                        'role': planet['role'],
                        'type': '行星'
                    })

            # 卫星
            for satellite in system_data['satellites']:
                region_stats[satellite['region']] += 1
                if satellite['region'] == 'CN':
                    china_roles.append({
                        'company': satellite['name'],
                        'role': satellite['role'],
                        'type': '卫星 (深度主内)',
                        'ecosystem': system_data['star']['name']
                    })

        col1, col2 = st.columns(2)

        with col1:
            st.markdown("#### 🇨🇳 中国公司 - '主内'承接者")
            for role in china_roles:
                st.success(f"""
                **{role['company']}**
                - 角色: {role['role']}
                - 层级: {role['type']}
                - 服务生态: {role['ecosystem']}
                """)

        with col2:
            st.markdown("#### 🇺🇸 美国公司 - 生态定义者")
            for role in us_roles[:7]:  # 只显示前7个
                st.info(f"""
                **{role['company']}**
                - 角色: {role['role']}
                - 层级: {role['type']}
                """)

        # 区域分布统计
        st.markdown("#### 🌍 全球产业分工格局")

        col1, col2, col3 = st.columns(3)

        with col1:
            st.metric("🇨🇳 中国公司", f"{region_stats['CN']}家", "主内制造")
        with col2:
            st.metric("🇺🇸 美国公司", f"{region_stats['US']}家", "生态定义")
        with col3:
            st.metric("🌏 其他地区", f"{sum(region_stats.values()) - region_stats['CN'] - region_stats['US']}家", "关键节点")

    def create_rebel_analysis(self):
        """创建叛军势力分析"""
        st.markdown("### ⚔️ 叛军势力分析")
        st.markdown("**分庭抗礼的挑战者**: 未上市巨头、多地上市公司、技术路线叛逆者")

        all_rebels = []
        for system_name, system_data in self.seven_sisters.items():
            for rebel in system_data['rebels']:
                all_rebels.append({
                    'name': rebel['name'],
                    'status': rebel['status'],
                    'threat': rebel['threat'],
                    'target_ecosystem': system_data['star']['name']
                })

        # 按状态分类
        unlisted = [r for r in all_rebels if '未上市' in r['status']]
        multi_listed = [r for r in all_rebels if '多地上市' in r['status']]
        listed = [r for r in all_rebels if r['status'] == '上市']

        col1, col2, col3 = st.columns(3)

        with col1:
            st.markdown("#### 🏴‍☠️ 未上市巨头")
            for rebel in unlisted:
                st.error(f"""
                **{rebel['name']}**
                - 威胁: {rebel['threat']}
                - 目标: {rebel['target_ecosystem']}
                - 特点: 资本市场外的隐形巨头
                """)

        with col2:
            st.markdown("#### 🌐 多地上市")
            for rebel in multi_listed:
                st.warning(f"""
                **{rebel['name']}**
                - 威胁: {rebel['threat']}
                - 目标: {rebel['target_ecosystem']}
                - 特点: 跨境资本运作
                """)

        with col3:
            st.markdown("#### 📈 传统上市")
            for rebel in listed:
                st.info(f"""
                **{rebel['name']}**
                - 威胁: {rebel['threat']}
                - 目标: {rebel['target_ecosystem']}
                - 特点: 正面竞争
                """)
    
    def format_company_data(self, overview):
        """格式化公司数据"""
        if not overview:
            return "暂无数据"
        
        return f"""
        <strong>公司名称:</strong> {overview.get('Name', 'N/A')}<br>
        <strong>行业:</strong> {overview.get('Industry', 'N/A')}<br>
        <strong>市值:</strong> ${overview.get('MarketCapitalization', 'N/A')}<br>
        <strong>P/E比率:</strong> {overview.get('PERatio', 'N/A')}<br>
        <strong>股息收益率:</strong> {overview.get('DividendYield', 'N/A')}<br>
        <strong>52周高点:</strong> ${overview.get('52WeekHigh', 'N/A')}<br>
        <strong>52周低点:</strong> ${overview.get('52WeekLow', 'N/A')}
        """
    
    def format_stock_data(self, stock):
        """格式化股票数据"""
        if not stock:
            return "暂无数据"
        
        return f"""
        <strong>股票代码:</strong> {stock.get('symbol', 'N/A')}<br>
        <strong>公司名称:</strong> {stock.get('shortName', 'N/A')}<br>
        <strong>当前价格:</strong> ${stock.get('regularMarketPrice', 0):.2f}<br>
        <strong>涨跌幅:</strong> <span style="color: {'green' if stock.get('regularMarketChangePercent', 0) > 0 else 'red'}">
            {stock.get('regularMarketChangePercent', 0):.2f}%
        </span><br>
        <strong>成交量:</strong> {stock.get('regularMarketVolume', 0):,}
        """

def render_seven_fairies_tab():
    """渲染七姐妹星团Tab"""

    # 页面标题
    st.markdown("### 🌟 七姐妹星团 - 全球科技生态结构分析")
    st.markdown("**七个独立Root恒星系统 | 中美夫妻论 | 三层架构: 恒星→行星→卫星 | 叛军势力**")
    st.markdown("---")

    # 初始化分析器
    analyzer = SevenSistersAnalyzer()

    # 分析模式选择
    analysis_mode = st.selectbox(
        "选择分析模式",
        ["恒星系统结构", "中美夫妻论", "叛军势力分析"],
        key="sisters_analysis_mode"
    )

    if analysis_mode == "恒星系统结构":
        st.markdown("## ⭐ 七大恒星系统结构分析")
        st.info("**核心理念**: 每个恒星系统都是独立的Root，拥有完整的三层架构")

        # 恒星系统选择
        selected_system = st.selectbox(
            "选择要分析的恒星系统",
            ["全部系统"] + list(analyzer.seven_sisters.keys()),
            format_func=lambda x: {
                "全部系统": "🌌 全部七大恒星系统",
                "Apple_Ecosystem": "🍎 Apple 生态系统",
                "Microsoft_Ecosystem": "💻 Microsoft 生态系统",
                "Google_Ecosystem": "🔍 Google 生态系统",
                "Amazon_Ecosystem": "📦 Amazon 生态系统",
                "Tesla_Ecosystem": "⚡ Tesla 生态系统",
                "Meta_Ecosystem": "📱 Meta 生态系统",
                "NVIDIA_Ecosystem": "🎮 NVIDIA 生态系统"
            }.get(x, x),
            key="sisters_system_select"
        )

        if selected_system == "全部系统":
            # 显示所有恒星系统
            for system_name, system_data in analyzer.seven_sisters.items():
                analyzer.create_star_system_card(system_name, system_data)
        else:
            # 显示选定的恒星系统
            analyzer.create_star_system_card(selected_system, analyzer.seven_sisters[selected_system])

    elif analysis_mode == "中美夫妻论":
        analyzer.create_china_us_analysis()

    else:  # 叛军势力分析
        analyzer.create_rebel_analysis()
    
    # 页面底部信息
    st.markdown("---")
    st.markdown("""
    ### 🌟 七姐妹星团核心理念

    **七个独立Root恒星系统**：
    - **🍎 Apple**: iOS生态系统 | 🇺🇸 定义者 + 🇨🇳 制造承接
    - **💻 Microsoft**: 云计算+企业软件 | 传统PC联盟 + 新兴AI合作
    - **🔍 Google**: 搜索+AI+云 | 硬件外包 + 算法核心
    - **📦 Amazon**: 电商+云计算 | 全球物流 + 中国制造
    - **⚡ Tesla**: 电动汽车+能源 | 🇨🇳 电池主内 + 🇺🇸 软件定义
    - **📱 Meta**: 社交+元宇宙 | VR硬件外包 + 平台控制
    - **🎮 NVIDIA**: AI算力+GPU | 🇹🇼 独家代工 + 🇺🇸 架构设计

    **中美夫妻论核心**：
    - 🇺🇸 **主外**: 生态定义、技术架构、平台控制
    - 🇨🇳 **主内**: 制造承接、供应链整合、成本优化
    - 🌏 **关键节点**: 台湾芯片、韩国存储、荷兰设备

    **三层架构**：
    - ⭐ **恒星 (天子)**: 生态定义者，拥有平台控制权
    - 🪐 **行星 (大夫)**: 核心供应商，深度绑定恒星
    - 🛰️ **卫星 (士)**: 专业供应商，服务于行星

    **叛军势力**：
    - 🏴‍☠️ **未上市巨头**: 华为鸿蒙、字节跳动等资本市场外的隐形力量
    - 🌐 **多地上市**: 小米、蔚来等跨境资本运作的挑战者
    - 📈 **传统竞争**: AMD、Intel等正面竞争的老牌势力

    ⚠️ **重要**: 本分析专注结构关系，不涉及股价等快变量。投资有风险，决策需谨慎！
    """)
