import streamlit as st
import pandas as pd
from datetime import datetime
from typing import Optional, Dict, Any

def render_home_tab():
    """
    渲染首页Tab - 市场概览和重要信息
    采用组件化设计，类似HTML静态网站的模块化思维
    """
    # 页面标题和描述
    _render_page_header()
    
    # 市场概览指标区域
    _render_market_overview()
    
    # 市场热点和重要信息
    _render_market_highlights()
    
    # 数据更新状态
    _render_update_status()

def _render_page_header():
    """渲染页面头部 - 类似HTML的header组件"""
    col1, col2 = st.columns([1, 5])
    with col1:
        st.image("app/hulu.jpg", width=80)
    with col2:
        st.markdown("### 市场概览")
        st.markdown("实时监控全球主要市场指数和重要资讯")

def _render_market_overview():
    """渲染市场概览指标 - 类似HTML的metrics组件"""
    # 创建指标展示区域
    col1, col2, col3, col4 = st.columns(4)
    
    # 获取市场数据
    market_data = _get_market_data()
    
    # 显示各项指标
    with col1:
        _render_metric_card("SPX指数", market_data.get('spx', {}))
    with col2:
        _render_metric_card("纳斯达克", market_data.get('nasdaq', {}))
    with col3:
        _render_metric_card("道琼斯", market_data.get('dow', {}))
    with col4:
        _render_metric_card("恒生指数", market_data.get('hsi', {}))

def _render_metric_card(name: str, data: Dict[str, Any]):
    """渲染单个指标卡片 - 类似HTML的card组件"""
    if data:
        st.metric(
            label=name,
            value=data.get('value', 'N/A'),
            delta=data.get('change', '--')
        )
    else:
        st.metric(label=name, value="加载中...", delta="--")

def _render_market_highlights():
    """渲染市场热点区域 - 类似HTML的content section"""
    col_left, col_right = st.columns([2, 1])
    
    with col_left:
        _render_market_news()
    
    with col_right:
        _render_important_announcements()

def _render_market_news():
    """渲染市场热点新闻"""
    st.subheader("🔥 市场热点")
    
    # 模拟新闻数据
    news_items = [
        "📈 科技股强势反弹，AI概念股领涨",
        "💰 美联储政策预期推动金融股上涨",
        "🛢️ 原油价格波动影响能源板块",
        "🏠 房地产政策利好刺激相关股票"
    ]
    
    for news in news_items:
        st.markdown(f"• {news}")

def _render_important_announcements():
    """渲染重要公告"""
    st.subheader("📢 重要公告")
    
    announcements = [
        "🔔 系统维护通知",
        "📊 新增港股实时数据",
        "⚡ 异常波动监控升级",
        "🤖 AI分析功能优化"
    ]
    
    for announcement in announcements:
        st.markdown(f"• {announcement}")

def _render_update_status():
    """渲染数据更新状态 - 类似HTML的footer信息"""
    if "last_refresh" in st.session_state:
        st.caption(f"📅 数据更新时间: {st.session_state.last_refresh.strftime('%Y-%m-%d %H:%M:%S')}")
    else:
        st.caption("📅 数据更新时间: 获取中...")

def _get_market_data() -> Dict[str, Dict[str, Any]]:
    """获取市场数据 - 类似HTML静态网站的数据获取函数"""
    # 检查是否有实时数据连接
    if ("ib_thread" in st.session_state and 
        st.session_state.ib_thread.ib_runner and
        hasattr(st.session_state.ib_thread.ib_runner, 'market_data')):
        
        runner = st.session_state.ib_thread.ib_runner
        raw_data = runner.market_data
        
        if isinstance(raw_data, dict) and raw_data:
            return {
                'spx': {'value': raw_data.get('spx', 'N/A'), 'change': '+0.5%'},
                'nasdaq': {'value': raw_data.get('nasdaq', 'N/A'), 'change': '+0.8%'},
                'dow': {'value': raw_data.get('dow', 'N/A'), 'change': '+0.3%'},
                'hsi': {'value': '18,500.00', 'change': '-0.3%'}
            }
    
    # 返回默认状态
    return {
        'spx': {},
        'nasdaq': {},
        'dow': {},
        'hsi': {}
    }