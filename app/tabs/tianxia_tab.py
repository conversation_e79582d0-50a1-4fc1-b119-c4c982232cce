"""
天下体系 - 儒门天下观资本生态分析Tab
基于"天命树"结构模型分析全球资本市场权力结构
"""

import streamlit as st
import pandas as pd
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
# import networkx as nx  # 暂时移除，简化依赖
import requests
import os
from datetime import datetime
import time

class TianxiaAnalyzer:
    """天下体系分析器 - 天命树结构分析"""
    
    def __init__(self):
        self.rapidapi_key = os.getenv('RAPIDAPI_KEY', '**************************************************')
        
        # 定义三大天命树生态系统
        self.ecosystems = {
            'AI': {
                'tianzi': {'symbol': 'NVDA', 'name': 'NVIDIA', 'tianming': 'CUDA + GPU硬件，定义AI计算范式'},
                'dafu': [
                    {'symbol': 'TSM', 'name': 'TSMC', 'role': '芯片代工', 'dependency': '高端芯片唯一代工厂'},
                    {'symbol': '000660.SZ', 'name': 'SK Hynix', 'role': 'HBM内存', 'dependency': 'GPU性能关键'},
                    {'symbol': 'MU', 'name': 'Micron', 'role': 'HBM内存', 'dependency': 'GPU性能关键'},
                    {'symbol': 'SMCI', 'name': 'Supermicro', 'role': '服务器集成', 'dependency': 'GPU转化为计算能力'}
                ],
                'shi': [
                    {'symbol': 'ASML', 'name': 'ASML', 'role': '光刻设备', 'serves': 'TSMC'},
                    {'symbol': 'AMAT', 'name': 'Applied Materials', 'role': '半导体设备', 'serves': 'TSMC'}
                ],
                'jiajie': [
                    {'symbol': 'AMD', 'name': 'AMD', 'type': '竞争天子'},
                    {'symbol': 'GOOGL', 'name': 'Google', 'type': '云计算天子'},
                    {'symbol': 'AMZN', 'name': 'Amazon', 'type': '云计算天子'}
                ]
            },
            'EV': {
                'tianzi': {'symbol': 'TSLA', 'name': 'Tesla', 'tianming': '软件定义汽车 + 超级充电网络'},
                'dafu': [
                    {'symbol': '300750.SZ', 'name': 'CATL', 'role': '动力电池', 'dependency': '动力系统基石'},
                    {'symbol': '6752.T', 'name': 'Panasonic', 'role': '动力电池', 'dependency': '动力系统基石'},
                    {'symbol': 'ALB', 'name': 'Albemarle', 'role': '锂矿', 'dependency': '源头命脉'},
                    {'symbol': '002460.SZ', 'name': 'Ganfeng Lithium', 'role': '锂矿', 'dependency': '源头命脉'}
                ],
                'shi': [
                    {'symbol': '002497.SZ', 'name': 'Yahua Industrial', 'role': '氢氧化锂', 'serves': 'CATL'},
                    {'symbol': '002850.SZ', 'name': 'Kedali', 'role': '精密结构件', 'serves': 'CATL'}
                ],
                'jiajie': [
                    {'symbol': '002594.SZ', 'name': 'BYD', 'type': '诸侯'},
                    {'symbol': 'VWAGY', 'name': 'Volkswagen', 'type': '诸侯'},
                    {'symbol': 'F', 'name': 'Ford', 'type': '诸侯'}
                ]
            },
            'Consumer_Electronics': {
                'tianzi': {'symbol': 'AAPL', 'name': 'Apple', 'tianming': 'iOS + App Store生态系统'},
                'dafu': [
                    {'symbol': '2317.TW', 'name': 'Foxconn', 'role': '代工制造', 'dependency': '物理执行者'},
                    {'symbol': 'TSM', 'name': 'TSMC', 'role': '芯片代工', 'dependency': '性能优势保障'},
                    {'symbol': '005930.KS', 'name': 'Samsung Display', 'role': '屏幕供应', 'dependency': '显示技术'},
                    {'symbol': 'QCOM', 'name': 'Qualcomm', 'role': '基带芯片', 'dependency': '通信命脉'}
                ],
                'shi': [
                    {'symbol': '002475.SZ', 'name': 'Luxshare', 'role': '精密制造', 'serves': 'Foxconn'},
                    {'symbol': '002241.SZ', 'name': 'Goertek', 'role': '声学器件', 'serves': 'Foxconn'}
                ],
                'jiajie': [
                    {'symbol': '005930.KS', 'name': 'Samsung', 'type': '亦敌亦友天子'},
                    {'symbol': '1810.HK', 'name': 'Xiaomi', 'type': '诸侯'},
                    {'symbol': 'NVDA', 'name': 'NVIDIA', 'type': '跨生态天子'}
                ]
            }
        }
    
    def get_stock_data(self, symbol):
        """获取股票数据"""
        # 简化版本，实际应该调用真实API
        try:
            # 这里应该调用Yahoo Finance或Alpha Vantage API
            # 为了演示，返回模拟数据
            import random
            return {
                'price': round(random.uniform(50, 500), 2),
                'change_pct': round(random.uniform(-5, 5), 2),
                'market_cap': f"{random.randint(100, 3000)}B",
                'volume': random.randint(1000000, 100000000)
            }
        except:
            return {'price': 'N/A', 'change_pct': 0, 'market_cap': 'N/A', 'volume': 'N/A'}
    
    def create_tianming_card(self, ecosystem_name, ecosystem_data):
        """创建天命卡片"""
        tianzi = ecosystem_data['tianzi']
        stock_data = self.get_stock_data(tianzi['symbol'])
        
        st.markdown(f"### 👑 {ecosystem_name} 天命树")
        
        # 天子信息
        col1, col2, col3 = st.columns([1, 2, 1])
        
        with col1:
            st.markdown("#### 🌟 天子")
            st.markdown(f"**{tianzi['name']}** ({tianzi['symbol']})")
        
        with col2:
            st.markdown("#### 📜 天命")
            st.info(tianzi['tianming'])
        
        with col3:
            st.metric(
                label="股价",
                value=f"${stock_data['price']}",
                delta=f"{stock_data['change_pct']:+.2f}%"
            )
        
        # 大夫层级
        st.markdown("#### 🏛️ 大夫 (核心依赖)")
        dafu_cols = st.columns(min(len(ecosystem_data['dafu']), 4))
        
        for i, dafu in enumerate(ecosystem_data['dafu']):
            col_index = i % 4
            with dafu_cols[col_index]:
                data = self.get_stock_data(dafu['symbol'])
                st.metric(
                    label=f"{dafu['name']}",
                    value=f"${data['price']}",
                    delta=f"{data['change_pct']:+.2f}%"
                )
                st.caption(f"**{dafu['role']}**: {dafu['dependency']}")
        
        # 士层级
        if ecosystem_data['shi']:
            st.markdown("#### ⚔️ 士 (专业供应商)")
            shi_cols = st.columns(min(len(ecosystem_data['shi']), 3))
            
            for i, shi in enumerate(ecosystem_data['shi']):
                col_index = i % 3
                with shi_cols[col_index]:
                    data = self.get_stock_data(shi['symbol'])
                    st.metric(
                        label=f"{shi['name']}",
                        value=f"${data['price']}",
                        delta=f"{data['change_pct']:+.2f}%"
                    )
                    st.caption(f"**{shi['role']}** → 服务于{shi['serves']}")
        
        # 嫁接关系
        if ecosystem_data['jiajie']:
            st.markdown("#### 🔗 嫁接关系 (跨生态链接)")
            jiajie_cols = st.columns(min(len(ecosystem_data['jiajie']), 4))
            
            for i, jiajie in enumerate(ecosystem_data['jiajie']):
                col_index = i % 4
                with jiajie_cols[col_index]:
                    data = self.get_stock_data(jiajie['symbol'])
                    st.metric(
                        label=f"{jiajie['name']}",
                        value=f"${data['price']}",
                        delta=f"{data['change_pct']:+.2f}%"
                    )
                    st.caption(f"**{jiajie['type']}**")
        
        st.markdown("---")
    
    def create_tianming_tree_table(self):
        """创建天命树完整表格 - 用于投资组合去相关性分析"""
        st.markdown("### 📋 天命树完整表格 - 投资组合去相关性分析")
        st.markdown("**核心理念**: 投资组合的本质是去相关性 - 从不同root下的不同spine下的不同leaf进行配置")

        # 构建完整的天命树表格
        all_stocks = []

        for eco_name, eco_data in self.ecosystems.items():
            # 天子
            tianzi = eco_data['tianzi']
            stock_data = self.get_stock_data(tianzi['symbol'])
            all_stocks.append({
                'Root': eco_name,
                'Level': '👑 天子',
                'Symbol': tianzi['symbol'],
                'Company': tianzi['name'],
                'Role': '定义范式',
                'Dependency_Path': f"{eco_name}",
                'Price': stock_data['price'],
                'Change%': stock_data['change_pct'],
                'Market_Cap': stock_data['market_cap'],
                'Correlation_Risk': '极高 - 生态核心'
            })

            # 大夫
            for dafu in eco_data['dafu']:
                stock_data = self.get_stock_data(dafu['symbol'])
                all_stocks.append({
                    'Root': eco_name,
                    'Level': '🏛️ 大夫',
                    'Symbol': dafu['symbol'],
                    'Company': dafu['name'],
                    'Role': dafu['role'],
                    'Dependency_Path': f"{eco_name} → {tianzi['name']} → {dafu['name']}",
                    'Price': stock_data['price'],
                    'Change%': stock_data['change_pct'],
                    'Market_Cap': stock_data['market_cap'],
                    'Correlation_Risk': '高 - 深度绑定天子'
                })

            # 士
            for shi in eco_data['shi']:
                stock_data = self.get_stock_data(shi['symbol'])
                all_stocks.append({
                    'Root': eco_name,
                    'Level': '⚔️ 士',
                    'Symbol': shi['symbol'],
                    'Company': shi['name'],
                    'Role': shi['role'],
                    'Dependency_Path': f"{eco_name} → {shi['serves']} → {shi['name']}",
                    'Price': stock_data['price'],
                    'Change%': stock_data['change_pct'],
                    'Market_Cap': stock_data['market_cap'],
                    'Correlation_Risk': '中 - 专业供应商'
                })

            # 嫁接
            for jiajie in eco_data['jiajie']:
                stock_data = self.get_stock_data(jiajie['symbol'])
                all_stocks.append({
                    'Root': '🔗 跨生态',
                    'Level': '🔗 嫁接',
                    'Symbol': jiajie['symbol'],
                    'Company': jiajie['name'],
                    'Role': jiajie['type'],
                    'Dependency_Path': f"多生态嫁接 → {jiajie['name']}",
                    'Price': stock_data['price'],
                    'Change%': stock_data['change_pct'],
                    'Market_Cap': stock_data['market_cap'],
                    'Correlation_Risk': '低 - 多元化依赖'
                })

        df = pd.DataFrame(all_stocks)

        # 显示表格
        st.dataframe(
            df,
            use_container_width=True,
            column_config={
                "Root": st.column_config.TextColumn("生态根节点", width="small"),
                "Level": st.column_config.TextColumn("层级", width="small"),
                "Symbol": st.column_config.TextColumn("代码", width="small"),
                "Company": st.column_config.TextColumn("公司", width="medium"),
                "Role": st.column_config.TextColumn("角色", width="medium"),
                "Dependency_Path": st.column_config.TextColumn("依赖路径", width="large"),
                "Price": st.column_config.NumberColumn("股价", format="$%.2f"),
                "Change%": st.column_config.NumberColumn("涨跌幅", format="%.2f%%"),
                "Market_Cap": st.column_config.TextColumn("市值", width="small"),
                "Correlation_Risk": st.column_config.TextColumn("相关性风险", width="medium")
            }
        )

        return df

    def create_portfolio_correlation_analysis(self, df):
        """创建投资组合相关性分析"""
        st.markdown("### 🎯 投资组合去相关性策略")

        # 按生态根节点分组统计
        root_stats = df.groupby('Root').agg({
            'Symbol': 'count',
            'Change%': 'mean'
        }).round(2)
        root_stats.columns = ['股票数量', '平均涨跌幅']

        col1, col2 = st.columns(2)

        with col1:
            st.markdown("#### 📊 生态分布")
            fig_pie = px.pie(
                values=root_stats['股票数量'],
                names=root_stats.index,
                title="各生态股票数量分布"
            )
            st.plotly_chart(fig_pie, use_container_width=True)

        with col2:
            st.markdown("#### 📈 生态表现")
            fig_bar = px.bar(
                x=root_stats.index,
                y=root_stats['平均涨跌幅'],
                title="各生态平均涨跌幅",
                color=root_stats['平均涨跌幅'],
                color_continuous_scale=['red', 'white', 'green']
            )
            st.plotly_chart(fig_bar, use_container_width=True)

        # 去相关性建议
        st.markdown("#### 💡 去相关性投资建议")

        col1, col2, col3 = st.columns(3)

        with col1:
            st.markdown("##### 🟢 低相关性组合")
            st.success("""
            **策略**: 跨生态分散投资
            - 每个生态选择1-2只核心股票
            - 重点关注"嫁接"类股票
            - 避免同一依赖路径的重复投资
            """)

        with col2:
            st.markdown("##### 🟡 中等相关性组合")
            st.warning("""
            **策略**: 生态内分散投资
            - 在单一生态内选择不同层级
            - 天子 + 大夫 + 士的组合
            - 注意供应链上下游风险
            """)

        with col3:
            st.markdown("##### 🔴 高相关性组合")
            st.error("""
            **风险**: 集中投资风险
            - 同一生态内多只股票
            - 同一依赖路径的重复暴露
            - 系统性风险过度集中
            """)

    def create_ecosystem_comparison(self):
        """创建生态系统对比图"""
        st.markdown("### 📊 三大天命树生态对比")

        # 收集所有天子数据
        tianzi_data = []
        for eco_name, eco_data in self.ecosystems.items():
            tianzi = eco_data['tianzi']
            stock_data = self.get_stock_data(tianzi['symbol'])
            tianzi_data.append({
                'Ecosystem': eco_name,
                'Company': tianzi['name'],
                'Symbol': tianzi['symbol'],
                'Price': stock_data['price'],
                'Change%': stock_data['change_pct'],
                'Market_Cap': stock_data['market_cap']
            })

        df = pd.DataFrame(tianzi_data)

        # 创建对比图表
        col1, col2 = st.columns(2)

        with col1:
            # 价格对比
            if all(isinstance(x, (int, float)) for x in df['Price']):
                fig_price = px.bar(
                    df,
                    x='Company',
                    y='Price',
                    color='Ecosystem',
                    title="天子股价对比",
                    color_discrete_map={
                        'AI': '#FF6B6B',
                        'EV': '#4ECDC4',
                        'Consumer_Electronics': '#45B7D1'
                    }
                )
                st.plotly_chart(fig_price, use_container_width=True)

        with col2:
            # 涨跌幅对比
            fig_change = px.bar(
                df,
                x='Company',
                y='Change%',
                color='Change%',
                title="天子涨跌幅对比",
                color_continuous_scale=['red', 'white', 'green']
            )
            st.plotly_chart(fig_change, use_container_width=True)

def render_tianxia_tab():
    """渲染天下体系Tab"""
    
    # 页面标题
    st.markdown("### 🏛️ 天下体系 - 儒门天下观资本生态分析")
    st.markdown("**基于'天命树'结构模型，穿透市场表象，绘制全球资本市场真实的权力结构**")
    st.markdown("---")
    
    # 初始化分析器
    analyzer = TianxiaAnalyzer()
    
    # 控制面板
    col1, col2, col3 = st.columns([1, 1, 2])
    with col1:
        auto_refresh = st.checkbox("🔄 自动刷新", value=False, key="tianxia_auto_refresh")
    with col2:
        if st.button("🏛️ 扫描天下", type="primary", key="tianxia_scan_btn"):
            st.session_state.trigger_tianxia_scan = True
    with col3:
        st.markdown("*正在分析全球资本生态权力结构...*")
    
    # 理论介绍
    with st.expander("📚 天命树理论基础"):
        st.markdown("""
        ### 🏛️ 儒门天下观核心思想
        
        **两大哲学基石：**
        1. **结构非平权**: 资本宇宙本质是不平权的、层级森严的树状结构
        2. **天命与脉络**: 每个生态都有唯一的"根节点"(天子)，拥有定义整个生态的"天命"
        
        **四层架构：**
        - **👑 天子**: 定义范式的平台型公司 (如Apple, NVIDIA, Tesla)
        - **🏛️ 大夫**: 深度绑定天子的核心供应商 (如TSMC, CATL)
        - **⚔️ 士**: 专业供应商和服务商 (如ASML, Luxshare)
        - **🔗 嫁接**: 跨生态的策略性链接关系
        """)
    
    # 自动刷新逻辑
    if auto_refresh:
        time.sleep(60)
        st.rerun()
    
    # 触发扫描或显示数据
    if st.session_state.get('trigger_tianxia_scan', False) or 'tianxia_scan_time' not in st.session_state:
        with st.spinner("🏛️ 正在扫描天下体系..."):
            st.session_state.tianxia_scan_time = datetime.now()
        st.session_state.trigger_tianxia_scan = False
    
    # 显示扫描时间
    if 'tianxia_scan_time' in st.session_state:
        st.info(f"📅 最后扫描时间: {st.session_state.tianxia_scan_time.strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 显示三大生态系统
    st.markdown("## 🌍 三大天命树生态系统")
    
    # 分析模式选择
    analysis_mode = st.selectbox(
        "选择分析模式",
        ["生态系统分析", "投资组合去相关性分析"],
        key="tianxia_analysis_mode"
    )

    if analysis_mode == "生态系统分析":
        # 生态系统选择
        selected_ecosystem = st.selectbox(
            "选择要分析的生态系统",
            ["全部", "AI", "EV", "Consumer_Electronics"],
            format_func=lambda x: {
                "全部": "🌍 全部生态系统",
                "AI": "🤖 AI人工智能生态",
                "EV": "⚡ 电动汽车生态",
                "Consumer_Electronics": "📱 消费电子生态"
            }[x],
            key="tianxia_ecosystem_select"
        )

        if selected_ecosystem == "全部":
            # 显示所有生态系统
            for eco_name, eco_data in analyzer.ecosystems.items():
                analyzer.create_tianming_card(eco_name, eco_data)

            # 显示对比分析
            analyzer.create_ecosystem_comparison()
        else:
            # 显示选定的生态系统
            analyzer.create_tianming_card(selected_ecosystem, analyzer.ecosystems[selected_ecosystem])

    else:  # 投资组合去相关性分析
        st.markdown("## 🎯 投资组合去相关性分析")
        st.info("**核心理念**: 真正的分散投资是从不同的root（天子）下的不同spine（大夫）下的不同leaf（士）进行配置")

        # 创建完整天命树表格
        df = analyzer.create_tianming_tree_table()

        # 创建投资组合相关性分析
        analyzer.create_portfolio_correlation_analysis(df)
    
    # 页面底部说明
    st.markdown("---")
    st.markdown("""
    ### 🎯 天下体系核心洞察
    
    **权力结构分析**：
    - **AI生态**: NVIDIA通过CUDA平台统治AI计算，TSMC是关键"嫁接"节点
    - **电动车生态**: Tesla定义软件汽车范式，CATL掌握电池命脉
    - **消费电子生态**: Apple建立iOS护城河，供应链高度集中化
    
    **投资策略启示**：
    1. **投资天子**: 寻找定义范式的平台型公司
    2. **关注大夫**: 深度绑定天子的核心供应商往往被低估
    3. **警惕嫁接**: 被多个天子"嫁接"的公司风险与机会并存
    4. **避开士层**: 缺乏议价能力的专业供应商投资价值有限
    
    ⚠️ **免责声明**: 天下体系分析仅供参考，投资有风险，决策需谨慎！
    """)
