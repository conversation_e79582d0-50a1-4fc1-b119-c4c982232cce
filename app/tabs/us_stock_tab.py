import streamlit as st
import pandas as pd
from typing import Optional, List, Dict, Any

def render_us_stock_tab():
    """
    渲染美股Tab - 美股市场分析和监控
    采用组件化设计，类似HTML静态网站的模块化思维
    """
    # 页面标题
    _render_page_header()
    
    # 主要内容区域
    _render_main_content()
    
    # 市场情绪和基本面分析
    _render_analysis_section()

def _render_page_header():
    """渲染页面头部"""
    col1, col2 = st.columns([1, 5])
    with col1:
        st.image("app/hulu.jpg", width=80)
    with col2:
        st.markdown("### 🇺🇸 美国国旗美股市场")
    st.markdown("实时监控美股市场的关键指标和交易信号")

def _render_main_content():
    """渲染主要内容区域"""
    # 创建两列布局
    us_col1, us_col2 = st.columns(2)
    
    with us_col1:
        _render_hot_meme_stocks()
        _render_technical_analysis()
    
    with us_col2:
        _render_abnormal_volatility()
        _render_smart_screening()

def _render_hot_meme_stocks():
    """渲染热门妖股组件"""
    st.subheader("🔥 热门妖股")
    
    # 获取妖股数据
    meme_data = _get_meme_stocks_data()
    
    if meme_data is not None and not meme_data.empty:
        # 自定义样式显示数据表格
        st.dataframe(
            meme_data,
            use_container_width=True,
            hide_index=True,
            column_config={
                "symbol": st.column_config.TextColumn("股票代码", width="small"),
                "price": st.column_config.NumberColumn("价格", format="$%.2f"),
                "change": st.column_config.NumberColumn("涨跌幅", format="%.2f%%")
            }
        )
    else:
        _render_loading_placeholder("正在加载热门妖股数据...")

def _render_abnormal_volatility():
    """渲染异常波动组件"""
    st.subheader("⚡ 异常波动")
    
    # 获取异常波动数据
    volatility_data = _get_abnormal_volatility_data()
    
    if volatility_data is not None and not volatility_data.empty:
        st.dataframe(
            volatility_data,
            use_container_width=True,
            hide_index=True,
            column_config={
                "symbol": st.column_config.TextColumn("股票代码", width="small"),
                "volatility": st.column_config.NumberColumn("波动率", format="%.2f%%"),
                "volume": st.column_config.NumberColumn("成交量", format="%d")
            }
        )
    else:
        _render_loading_placeholder("正在监控异常波动...")

def _render_technical_analysis():
    """渲染技术分析组件"""
    st.subheader("📈 技术分析")
    
    # 技术指标选择
    indicator = st.selectbox(
        "选择技术指标",
        ["RSI", "MACD", "布林带", "移动平均线"],
        key="us_tech_indicator"
    )
    
    # 显示技术分析结果
    with st.container():
        if indicator == "RSI":
            _render_rsi_analysis()
        elif indicator == "MACD":
            _render_macd_analysis()
        elif indicator == "布林带":
            _render_bollinger_analysis()
        else:
            _render_ma_analysis()

def _render_smart_screening():
    """渲染智能筛选组件"""
    st.subheader("🎯 智能筛选")
    
    # 筛选条件
    with st.expander("筛选条件", expanded=False):
        col1, col2 = st.columns(2)
        
        with col1:
            price_range = st.slider("价格范围", 0, 1000, (10, 100), key="us_price_range")
            volume_min = st.number_input("最小成交量", value=1000000, key="us_volume_min")
        
        with col2:
            market_cap = st.selectbox("市值", ["全部", "大盘股", "中盘股", "小盘股"], key="us_market_cap")
            sector = st.selectbox("行业", ["全部", "科技", "金融", "医疗", "能源"], key="us_sector")
    
    # 筛选结果
    if st.button("开始筛选", key="us_screening_btn"):
        _render_screening_results(price_range, volume_min, market_cap, sector)
    else:
        st.info("请设置筛选条件并点击开始筛选")

def _render_analysis_section():
    """渲染分析区域"""
    st.markdown("---")
    sentiment_col1, sentiment_col2 = st.columns(2)
    
    with sentiment_col1:
        _render_market_sentiment()
    
    with sentiment_col2:
        _render_fundamental_analysis()

def _render_market_sentiment():
    """渲染市场情绪分析"""
    st.subheader("📊 市场情绪")
    
    # 情绪指标
    sentiment_metrics = {
        "恐慌贪婪指数": {"value": 65, "status": "贪婪"},
        "VIX指数": {"value": 18.5, "status": "低波动"},
        "看涨看跌比": {"value": 1.2, "status": "偏乐观"}
    }
    
    for metric, data in sentiment_metrics.items():
        col1, col2 = st.columns([3, 1])
        with col1:
            st.metric(metric, data["value"])
        with col2:
            st.write(data["status"])

def _render_fundamental_analysis():
    """渲染基本面分析"""
    st.subheader("🔍 基本面分析")
    
    # 基本面指标
    fundamental_data = {
        "平均P/E比": 22.5,
        "平均P/B比": 3.2,
        "股息收益率": "2.1%",
        "ROE平均值": "15.8%"
    }
    
    for metric, value in fundamental_data.items():
        st.metric(metric, value)

def _render_loading_placeholder(message: str):
    """渲染加载占位符"""
    st.info(message)

def _render_rsi_analysis():
    """渲染RSI分析"""
    st.info("RSI技术分析功能开发中...")
    # 这里可以添加RSI图表和分析

def _render_macd_analysis():
    """渲染MACD分析"""
    st.info("MACD技术分析功能开发中...")
    # 这里可以添加MACD图表和分析

def _render_bollinger_analysis():
    """渲染布林带分析"""
    st.info("布林带技术分析功能开发中...")
    # 这里可以添加布林带图表和分析

def _render_ma_analysis():
    """渲染移动平均线分析"""
    st.info("移动平均线技术分析功能开发中...")
    # 这里可以添加移动平均线图表和分析

def _render_screening_results(price_range, volume_min, market_cap, sector):
    """渲染筛选结果"""
    st.info(f"筛选条件: 价格${price_range[0]}-${price_range[1]}, 成交量>{volume_min:,}, 市值:{market_cap}, 行业:{sector}")
    st.info("智能筛选结果将在此显示...")

def _get_meme_stocks_data() -> Optional[pd.DataFrame]:
    """获取热门妖股数据"""
    if ("ib_thread" in st.session_state and 
        st.session_state.ib_thread.ib_runner and
        hasattr(st.session_state.ib_thread.ib_runner, 'meme_stocks_data')):
        
        runner = st.session_state.ib_thread.ib_runner
        data = runner.meme_stocks_data
        
        if isinstance(data, pd.DataFrame) and not data.empty:
            return data
    
    return None

def _get_abnormal_volatility_data() -> Optional[pd.DataFrame]:
    """获取异常波动数据"""
    if ("ib_thread" in st.session_state and 
        st.session_state.ib_thread.ib_runner and
        hasattr(st.session_state.ib_thread.ib_runner, 'abnormal_stocks_data')):
        
        runner = st.session_state.ib_thread.ib_runner
        data = runner.abnormal_stocks_data
        
        if isinstance(data, pd.DataFrame) and not data.empty:
            return data
    
    return None