import streamlit as st
import pandas as pd
from typing import Optional, List, Dict, Any

def render_hk_stock_tab():
    """
    渲染港股Tab - 港股市场分析和监控
    采用组件化设计，类似HTML静态网站的模块化思维
    """
    # 页面标题
    _render_page_header()
    
    # 主要指数区域
    _render_main_indices()
    
    # 行业板块分析
    _render_sector_analysis()
    
    # 异动股票监控
    _render_unusual_activity()

def _render_page_header():
    """渲染页面头部"""
    col1, col2 = st.columns([1, 5])
    with col1:
        st.image("app/hulu.jpg", width=80)
    with col2:
        st.markdown("### 🇭🇰 香港区旗港股市场")
        st.markdown("洞察港股市场的最新动态和投资机会")

def _render_main_indices():
    """渲染主要指数区域"""
    st.subheader("📊 主要指数")
    
    # 创建指数展示区域
    idx_col1, idx_col2, idx_col3, idx_col4 = st.columns(4)
    
    # 获取指数数据
    indices_data = _get_hk_indices_data()
    
    with idx_col1:
        _render_index_card("恒生指数", indices_data.get('hsi', {}))
    with idx_col2:
        _render_index_card("恒生科技", indices_data.get('hstech', {}))
    with idx_col3:
        _render_index_card("国企指数", indices_data.get('hscei', {}))
    with idx_col4:
        _render_index_card("红筹指数", indices_data.get('hscci', {}))

def _render_index_card(name: str, data: Dict[str, Any]):
    """渲染单个指数卡片"""
    if data:
        st.metric(
            label=name,
            value=data.get('value', 'N/A'),
            delta=data.get('change', '--')
        )
    else:
        st.metric(label=name, value="加载中...", delta="--")

def _render_sector_analysis():
    """渲染行业板块分析"""
    st.markdown("---")
    st.subheader("🏭 行业板块")
    
    # 创建行业板块布局
    sector_col1, sector_col2 = st.columns(2)
    
    with sector_col1:
        _render_tech_stocks()
        _render_finance_stocks()
    
    with sector_col2:
        _render_property_stocks()
        _render_consumer_stocks()

def _render_tech_stocks():
    """渲染科技股板块"""
    st.subheader("💻 科技股")
    
    # 科技股数据
    tech_stocks = _get_tech_stocks_data()
    
    if tech_stocks:
        for stock in tech_stocks:
            col1, col2, col3 = st.columns([2, 1, 1])
            with col1:
                st.write(stock.get('name', 'N/A'))
            with col2:
                st.write(stock.get('price', 'N/A'))
            with col3:
                change = stock.get('change', 0)
                color = "🔴" if change < 0 else "🟢"
                st.write(f"{color} {change}%")
    else:
        st.info("正在加载科技股数据...")

def _render_finance_stocks():
    """渲染金融股板块"""
    st.subheader("🏦 金融股")
    
    # 金融股数据
    finance_stocks = _get_finance_stocks_data()
    
    if finance_stocks:
        for stock in finance_stocks:
            col1, col2, col3 = st.columns([2, 1, 1])
            with col1:
                st.write(stock.get('name', 'N/A'))
            with col2:
                st.write(stock.get('price', 'N/A'))
            with col3:
                change = stock.get('change', 0)
                color = "🔴" if change < 0 else "🟢"
                st.write(f"{color} {change}%")
    else:
        st.info("正在加载金融股数据...")

def _render_property_stocks():
    """渲染地产股板块"""
    st.subheader("🏠 地产股")
    
    # 地产股数据
    property_stocks = _get_property_stocks_data()
    
    if property_stocks:
        for stock in property_stocks:
            col1, col2, col3 = st.columns([2, 1, 1])
            with col1:
                st.write(stock.get('name', 'N/A'))
            with col2:
                st.write(stock.get('price', 'N/A'))
            with col3:
                change = stock.get('change', 0)
                color = "🔴" if change < 0 else "🟢"
                st.write(f"{color} {change}%")
    else:
        st.info("正在加载地产股数据...")

def _render_consumer_stocks():
    """渲染消费股板块"""
    st.subheader("🛒 消费股")
    
    # 消费股数据
    consumer_stocks = _get_consumer_stocks_data()
    
    if consumer_stocks:
        for stock in consumer_stocks:
            col1, col2, col3 = st.columns([2, 1, 1])
            with col1:
                st.write(stock.get('name', 'N/A'))
            with col2:
                st.write(stock.get('price', 'N/A'))
            with col3:
                change = stock.get('change', 0)
                color = "🔴" if change < 0 else "🟢"
                st.write(f"{color} {change}%")
    else:
        st.info("正在加载消费股数据...")

def _render_unusual_activity():
    """渲染异动股票监控"""
    st.markdown("---")
    st.subheader("⚡ 异动股票")
    
    # 异动类型选择
    activity_type = st.selectbox(
        "异动类型",
        ["涨幅榜", "跌幅榜", "成交量异常", "振幅异常"],
        key="hk_activity_type"
    )
    
    # 显示异动股票
    unusual_stocks = _get_unusual_activity_data(activity_type)
    
    if unusual_stocks:
        # 创建表格显示
        df = pd.DataFrame(unusual_stocks)
        st.dataframe(
            df,
            use_container_width=True,
            hide_index=True,
            column_config={
                "code": st.column_config.TextColumn("股票代码", width="small"),
                "name": st.column_config.TextColumn("股票名称", width="medium"),
                "price": st.column_config.NumberColumn("现价", format="HK$%.2f"),
                "change": st.column_config.NumberColumn("涨跌幅", format="%.2f%%"),
                "volume": st.column_config.NumberColumn("成交量", format="%d")
            }
        )
    else:
        st.info(f"正在监控{activity_type}数据...")
    
    # 添加筛选功能
    with st.expander("高级筛选", expanded=False):
        _render_advanced_filter()

def _render_advanced_filter():
    """渲染高级筛选功能"""
    col1, col2 = st.columns(2)
    
    with col1:
        price_range = st.slider("价格范围 (HK$)", 0, 1000, (1, 100), key="hk_price_range")
        volume_min = st.number_input("最小成交量", value=100000, key="hk_volume_min")
    
    with col2:
        market_cap = st.selectbox("市值分类", ["全部", "大型股", "中型股", "小型股"], key="hk_market_cap")
        exchange = st.selectbox("交易所", ["全部", "主板", "创业板"], key="hk_exchange")
    
    if st.button("应用筛选", key="hk_filter_btn"):
        st.info(f"筛选条件已应用: 价格HK${price_range[0]}-{price_range[1]}, 成交量>{volume_min:,}, 市值:{market_cap}, 交易所:{exchange}")

def _get_hk_indices_data() -> Dict[str, Dict[str, Any]]:
    """获取港股指数数据"""
    # 模拟指数数据
    return {
        'hsi': {'value': '18,500.00', 'change': '-0.3%'},
        'hstech': {'value': '3,850.00', 'change': '+1.2%'},
        'hscei': {'value': '6,200.00', 'change': '-0.8%'},
        'hscci': {'value': '3,100.00', 'change': '+0.5%'}
    }

def _get_tech_stocks_data() -> List[Dict[str, Any]]:
    """获取科技股数据"""
    return [
        {'name': '腾讯控股', 'price': 'HK$320.00', 'change': 1.5},
        {'name': '阿里巴巴-SW', 'price': 'HK$85.50', 'change': -0.8},
        {'name': '美团-W', 'price': 'HK$125.00', 'change': 2.1},
        {'name': '小米集团-W', 'price': 'HK$12.80', 'change': 0.5}
    ]

def _get_finance_stocks_data() -> List[Dict[str, Any]]:
    """获取金融股数据"""
    return [
        {'name': '汇丰控股', 'price': 'HK$62.50', 'change': 0.8},
        {'name': '中国银行', 'price': 'HK$3.25', 'change': -0.3},
        {'name': '建设银行', 'price': 'HK$5.80', 'change': 0.2},
        {'name': '友邦保险', 'price': 'HK$68.00', 'change': 1.2}
    ]

def _get_property_stocks_data() -> List[Dict[str, Any]]:
    """获取地产股数据"""
    return [
        {'name': '长实集团', 'price': 'HK$45.50', 'change': -1.2},
        {'name': '新鸿基地产', 'price': 'HK$98.00', 'change': -0.5},
        {'name': '恒基地产', 'price': 'HK$28.50', 'change': -0.8},
        {'name': '太古地产', 'price': 'HK$22.00', 'change': 0.3}
    ]

def _get_consumer_stocks_data() -> List[Dict[str, Any]]:
    """获取消费股数据"""
    return [
        {'name': '海底捞', 'price': 'HK$18.50', 'change': 2.8},
        {'name': '蒙牛乳业', 'price': 'HK$28.00', 'change': 1.5},
        {'name': '李宁', 'price': 'HK$15.20', 'change': -1.0},
        {'name': '安踏体育', 'price': 'HK$68.50', 'change': 0.7}
    ]

def _get_unusual_activity_data(activity_type: str) -> List[Dict[str, Any]]:
    """获取异动股票数据"""
    if activity_type == "涨幅榜":
        return [
            {'code': '00700', 'name': '腾讯控股', 'price': 320.00, 'change': 5.2, 'volume': 15000000},
            {'code': '09988', 'name': '阿里巴巴-SW', 'price': 85.50, 'change': 4.8, 'volume': 12000000},
            {'code': '03690', 'name': '美团-W', 'price': 125.00, 'change': 4.1, 'volume': 8000000}
        ]
    elif activity_type == "跌幅榜":
        return [
            {'code': '01810', 'name': '小米集团-W', 'price': 12.80, 'change': -3.5, 'volume': 20000000},
            {'code': '00005', 'name': '汇丰控股', 'price': 62.50, 'change': -2.8, 'volume': 5000000},
            {'code': '01398', 'name': '工商银行', 'price': 4.20, 'change': -2.1, 'volume': 8000000}
        ]
    else:
        return []