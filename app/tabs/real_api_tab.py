"""
真实API调用Tab - Top Movers & K线图
集成到主Streamlit应用中的真实API演示功能
"""

import streamlit as st
import requests
import pandas as pd
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import os
from datetime import datetime, timedelta
import time
import json

class RealAPIClient:
    """真实API客户端"""
    
    def __init__(self):
        self.rapidapi_key = os.getenv('RAPIDAPI_KEY', '**************************************************')
        self.base_headers = {
            'X-RapidAPI-Key': self.rapidapi_key,
            'X-RapidAPI-Host': 'yahoo-finance15.p.rapidapi.com'
        }
        
    def get_top_movers(self):
        """获取今日涨幅榜"""
        url = "https://yahoo-finance15.p.rapidapi.com/api/yahoo/co/collections/day_gainers"
        
        try:
            response = requests.get(url, headers=self.base_headers, timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                return {
                    'success': True,
                    'data': data,
                    'api_used': 'Yahoo Finance 经典版',
                    'response_time': response.elapsed.total_seconds() * 1000
                }
            else:
                return {
                    'success': False,
                    'error': f"API返回错误: {response.status_code}",
                    'response': response.text[:500]
                }
                
        except Exception as e:
            return {
                'success': False,
                'error': f"请求失败: {str(e)}"
            }
    
    def get_stock_quote(self, symbol):
        """获取股票报价"""
        url = f"https://yahoo-finance15.p.rapidapi.com/api/yahoo/qu/quote/{symbol}"
        
        try:
            response = requests.get(url, headers=self.base_headers, timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                return {
                    'success': True,
                    'data': data,
                    'api_used': 'Yahoo Finance 经典版',
                    'response_time': response.elapsed.total_seconds() * 1000
                }
            else:
                return {
                    'success': False,
                    'error': f"API返回错误: {response.status_code}",
                    'response': response.text[:500]
                }
                
        except Exception as e:
            return {
                'success': False,
                'error': f"请求失败: {str(e)}"
            }
    
    def get_historical_data(self, symbol, period="1mo"):
        """获取历史数据 - 使用Alpha Vantage API"""
        alpha_vantage_key = os.getenv('ALPHA_VANTAGE_API', '4HDGAT4ZO02QM1KY')
        
        url = "https://alpha-vantage.p.rapidapi.com/query"
        headers = {
            'X-RapidAPI-Key': self.rapidapi_key,
            'X-RapidAPI-Host': 'alpha-vantage.p.rapidapi.com'
        }
        
        params = {
            'function': 'TIME_SERIES_DAILY',
            'symbol': symbol,
            'apikey': alpha_vantage_key,
            'outputsize': 'compact'  # 最近100天
        }
        
        try:
            response = requests.get(url, headers=headers, params=params, timeout=15)
            
            if response.status_code == 200:
                data = response.json()
                return {
                    'success': True,
                    'data': data,
                    'api_used': 'Alpha Vantage',
                    'response_time': response.elapsed.total_seconds() * 1000
                }
            else:
                return {
                    'success': False,
                    'error': f"API返回错误: {response.status_code}",
                    'response': response.text[:500]
                }
                
        except Exception as e:
            return {
                'success': False,
                'error': f"请求失败: {str(e)}"
            }
    
    def create_candlestick_chart(self, historical_data, symbol):
        """创建K线图"""
        try:
            # 解析Alpha Vantage数据
            time_series = historical_data.get('Time Series (Daily)', {})
            
            if not time_series:
                return None
            
            # 转换数据格式
            dates = []
            opens = []
            highs = []
            lows = []
            closes = []
            volumes = []
            
            # 按日期排序
            sorted_dates = sorted(time_series.keys())
            
            for date in sorted_dates:
                day_data = time_series[date]
                dates.append(pd.to_datetime(date))
                opens.append(float(day_data['1. open']))
                highs.append(float(day_data['2. high']))
                lows.append(float(day_data['3. low']))
                closes.append(float(day_data['4. close']))
                volumes.append(int(day_data['5. volume']))
            
            # 创建子图
            fig = make_subplots(
                rows=2, cols=1,
                shared_xaxes=True,
                vertical_spacing=0.03,
                subplot_titles=(f'{symbol} 股价走势', '成交量'),
                row_width=[0.2, 0.7]
            )
            
            # K线图
            fig.add_trace(
                go.Candlestick(
                    x=dates,
                    open=opens,
                    high=highs,
                    low=lows,
                    close=closes,
                    name="K线",
                    increasing_line_color='#00ff00',
                    decreasing_line_color='#ff0000'
                ),
                row=1, col=1
            )
            
            # 成交量柱状图
            colors = ['green' if closes[i] >= opens[i] else 'red' for i in range(len(closes))]
            fig.add_trace(
                go.Bar(
                    x=dates,
                    y=volumes,
                    name="成交量",
                    marker_color=colors,
                    opacity=0.7
                ),
                row=2, col=1
            )
            
            # 更新布局
            fig.update_layout(
                title=f'{symbol} - 过去一个月K线图',
                yaxis_title='股价 ($)',
                yaxis2_title='成交量',
                xaxis_rangeslider_visible=False,
                height=600,
                showlegend=False
            )
            
            return fig
            
        except Exception as e:
            st.error(f"创建K线图失败: {str(e)}")
            return None

def render_real_api_tab():
    """渲染真实API调用Tab"""
    
    # 页面标题
    st.markdown("### 📈 真实API演示 - Top Movers & K线图")
    st.markdown("**使用真实RapidAPI调用获取股票数据并显示K线图**")
    st.markdown("---")
    
    # 初始化API客户端
    api_client = RealAPIClient()
    
    # 创建两列布局
    col1, col2 = st.columns([1, 1])
    
    with col1:
        st.subheader("🔥 今日涨幅榜")
        
        # 控制按钮
        if st.button("🔥 获取今日涨幅榜", type="primary", use_container_width=True):
            st.session_state.fetch_movers = True
        
        # 获取Top Movers
        if st.session_state.get('fetch_movers', False):
            with st.spinner("正在获取今日涨幅榜..."):
                result = api_client.get_top_movers()
                
                if result['success']:
                    st.success(f"✅ 成功获取数据！API: {result['api_used']}, 响应时间: {result['response_time']:.0f}ms")
                    
                    # 解析并显示数据
                    try:
                        movers_data = result['data']
                        
                        # 显示原始数据结构（调试用）
                        with st.expander("🔍 查看原始API响应"):
                            st.json(movers_data)
                        
                        # 尝试解析股票列表
                        if 'body' in movers_data:
                            stocks = movers_data['body']
                            
                            if stocks:
                                st.write(f"📊 找到 {len(stocks)} 只热门股票:")
                                
                                # 创建表格显示
                                display_data = []
                                for i, stock in enumerate(stocks[:10]):  # 显示前10只
                                    try:
                                        display_data.append({
                                            '排名': i + 1,
                                            '股票代码': stock.get('symbol', 'N/A'),
                                            '公司名称': stock.get('shortName', stock.get('longName', 'N/A'))[:30],
                                            '当前价格': f"${stock.get('regularMarketPrice', 0):.2f}",
                                            '涨跌幅': f"{stock.get('regularMarketChangePercent', 0):.2f}%",
                                            '涨跌额': f"${stock.get('regularMarketChange', 0):.2f}"
                                        })
                                    except:
                                        continue
                                
                                if display_data:
                                    df = pd.DataFrame(display_data)
                                    st.dataframe(df, use_container_width=True)
                                    
                                    # 选择股票查看K线图
                                    st.markdown("---")
                                    st.write("📈 选择股票查看K线图:")
                                    
                                    symbols = [stock.get('symbol', '') for stock in stocks[:10] if stock.get('symbol')]
                                    if symbols:
                                        selected = st.selectbox("选择股票代码", symbols, key="movers_select")
                                        if st.button(f"查看 {selected} K线图", key="movers_chart"):
                                            st.session_state.selected_symbol = selected
                                            st.session_state.fetch_chart = True
                                else:
                                    st.warning("无法解析股票数据")
                            else:
                                st.warning("API返回的数据为空")
                        else:
                            st.warning("API响应格式不符合预期")
                            
                    except Exception as e:
                        st.error(f"解析数据失败: {str(e)}")
                        
                else:
                    st.error(f"❌ 获取数据失败: {result['error']}")
                    if 'response' in result:
                        with st.expander("查看错误详情"):
                            st.text(result['response'])
            
            st.session_state.fetch_movers = False
        
        # 手动输入股票代码
        st.markdown("---")
        st.write("📝 或手动输入股票代码:")
        manual_symbol = st.text_input("股票代码", placeholder="例如: AAPL", key="manual_input")
        if st.button("📊 获取K线图", key="manual_chart") and manual_symbol:
            st.session_state.selected_symbol = manual_symbol.upper()
            st.session_state.fetch_chart = True
    
    with col2:
        st.subheader("📊 K线图分析")
        
        # 获取K线图
        if st.session_state.get('fetch_chart', False) and st.session_state.get('selected_symbol'):
            symbol = st.session_state.selected_symbol
            
            with st.spinner(f"正在获取 {symbol} 的历史数据..."):
                result = api_client.get_historical_data(symbol)
                
                if result['success']:
                    st.success(f"✅ 成功获取历史数据！API: {result['api_used']}, 响应时间: {result['response_time']:.0f}ms")
                    
                    # 创建K线图
                    fig = api_client.create_candlestick_chart(result['data'], symbol)
                    
                    if fig:
                        st.plotly_chart(fig, use_container_width=True)
                        
                        # 显示基本信息
                        try:
                            meta_data = result['data'].get('Meta Data', {})
                            st.info(f"""
                            📋 **股票信息**
                            - 股票代码: {meta_data.get('2. Symbol', symbol)}
                            - 最后更新: {meta_data.get('3. Last Refreshed', 'N/A')}
                            - 时区: {meta_data.get('5. Time Zone', 'N/A')}
                            """)
                        except:
                            pass
                    else:
                        st.error("创建K线图失败")
                        
                else:
                    st.error(f"❌ 获取历史数据失败: {result['error']}")
                    if 'response' in result:
                        with st.expander("查看错误详情"):
                            st.text(result['response'])
            
            st.session_state.fetch_chart = False
    
    # 页面底部信息
    st.markdown("---")
    st.markdown("""
    ### 🔧 技术说明
    - **涨幅榜数据**: 使用 Yahoo Finance 15 API (RapidAPI)
    - **历史数据**: 使用 Alpha Vantage API (RapidAPI)
    - **K线图**: Plotly Candlestick Chart
    - **数据更新**: 实时API调用，非模拟数据
    
    ⚠️ **注意**: 这是真实的API调用，会消耗你的RapidAPI配额！
    """)
