import streamlit as st
import pandas as pd
import subprocess
import json
import sys

def show_fengshen_tab():
    """显示封神榜主标签页。"""
    st.title("封神榜")

    # 创建子标签页
    us_stock_tab, hk_stock_tab, crypto_tab = st.tabs(["🇺🇸 美股", "🇭🇰 港股", "₿ 加密货币"])

    with us_stock_tab:
        show_us_stock_sub_tab()

    with hk_stock_tab:
        st.header("港股换手率排行")
        st.write("此功能正在开发中...")

    with crypto_tab:
        st.header("加密货币换手率排行")
        st.write("此功能正在开发中...")

def show_us_stock_sub_tab():
    """显示美股子标签页内容。"""
    st.header("昨日美股换手率 Top 8")

    # 使用会话状态来缓存数据
    if 'top_turnover_data' not in st.session_state:
        st.session_state.top_turnover_data = None

    if st.button("刷新数据"):
        st.session_state.top_turnover_data = None  # 清除旧数据以便重新获取
        with st.spinner('正在从IB Gateway获取数据...'):
            try:
                # IB连接参数
                ib_host = st.secrets.ib.host
                ib_port = str(st.secrets.ib.port)
                ib_client_id = "11"

                # 构建命令以执行外部脚本
                command = [
                    sys.executable,  # 使用当前环境的Python解释器
                    "src/core/fetch_turnover.py"
                ]

                # 执行脚本
                result = subprocess.run(
                    command,
                    capture_output=True,
                    text=True,
                    check=True,  # 如果脚本返回非零退出码，则引发异常
                    timeout=60  # 设置60秒超时
                )

                # 脚本将JSON打印到stdout
                df = pd.read_json(result.stdout, orient='records')
                st.session_state.top_turnover_data = df
                st.success("数据已刷新！")

            except subprocess.CalledProcessError as e:
                st.error(f"获取数据时出错: {e.stderr}")
                st.warning("请确保您的 IB Gateway 正在运行，并且API连接已启用。")
            except FileNotFoundError:
                st.error("错误: 'fetch_turnover.py' 脚本未找到。")
            except json.JSONDecodeError:
                st.error("错误: 无法解析从脚本返回的数据。")
            except Exception as e:
                st.error(f"发生未知错误: {e}")

    # 显示数据
    if st.session_state.top_turnover_data is not None:
        if not st.session_state.top_turnover_data.empty:
            st.dataframe(st.session_state.top_turnover_data, use_container_width=True)
            st.info("数据说明：换手率 = 昨日成交量 / 流通股数。数据来源于 Interactive Brokers。")
        else:
            st.warning("未能获取到任何股票的换手率数据，请检查后台日志。")
    else:
        st.info("点击“刷新数据”按钮以获取最新的换手率排名。")