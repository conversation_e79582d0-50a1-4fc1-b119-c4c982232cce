import streamlit as st
from typing import Dict, Any

class ThemeManager:
    """主题管理器 - 类似HTML静态网站的CSS主题管理"""
    
    def __init__(self):
        self.themes = {
            'default': self._get_default_theme(),
            'dark': self._get_dark_theme(),
            'light': self._get_light_theme(),
            'professional': self._get_professional_theme()
        }
        self.current_theme = 'default'
    
    def _get_default_theme(self) -> Dict[str, Any]:
        """默认主题配置"""
        return {
            'name': '默认主题',
            'colors': {
                'primary': '#FF6B6B',
                'secondary': '#4ECDC4',
                'success': '#45B7D1',
                'warning': '#FFA07A',
                'error': '#FF6B6B',
                'info': '#74B9FF',
                'background': '#FFFFFF',
                'surface': '#F8F9FA',
                'text_primary': '#2D3436',
                'text_secondary': '#636E72',
                'border': '#DDD6FE'
            },
            'fonts': {
                'primary': 'Inter, sans-serif',
                'secondary': 'Roboto, sans-serif',
                'monospace': 'Fira Code, monospace'
            },
            'spacing': {
                'xs': '0.25rem',
                'sm': '0.5rem',
                'md': '1rem',
                'lg': '1.5rem',
                'xl': '2rem',
                'xxl': '3rem'
            },
            'border_radius': {
                'sm': '0.25rem',
                'md': '0.5rem',
                'lg': '0.75rem',
                'xl': '1rem'
            }
        }
    
    def _get_dark_theme(self) -> Dict[str, Any]:
        """深色主题配置"""
        theme = self._get_default_theme()
        theme.update({
            'name': '深色主题',
            'colors': {
                **theme['colors'],
                'background': '#1E1E1E',
                'surface': '#2D2D2D',
                'text_primary': '#FFFFFF',
                'text_secondary': '#B0B0B0',
                'border': '#404040'
            }
        })
        return theme
    
    def _get_light_theme(self) -> Dict[str, Any]:
        """浅色主题配置"""
        theme = self._get_default_theme()
        theme.update({
            'name': '浅色主题',
            'colors': {
                **theme['colors'],
                'background': '#FAFAFA',
                'surface': '#FFFFFF',
                'text_primary': '#1A1A1A',
                'text_secondary': '#666666',
                'border': '#E0E0E0'
            }
        })
        return theme
    
    def _get_professional_theme(self) -> Dict[str, Any]:
        """专业主题配置"""
        theme = self._get_default_theme()
        theme.update({
            'name': '专业主题',
            'colors': {
                **theme['colors'],
                'primary': '#2563EB',
                'secondary': '#64748B',
                'success': '#059669',
                'warning': '#D97706',
                'error': '#DC2626',
                'info': '#0284C7',
                'background': '#F8FAFC',
                'surface': '#FFFFFF',
                'text_primary': '#0F172A',
                'text_secondary': '#475569',
                'border': '#CBD5E1'
            }
        })
        return theme
    
    def set_theme(self, theme_name: str):
        """设置当前主题"""
        if theme_name in self.themes:
            self.current_theme = theme_name
            self.apply_theme()
        else:
            st.error(f"主题 '{theme_name}' 不存在")
    
    def get_current_theme(self) -> Dict[str, Any]:
        """获取当前主题配置"""
        return self.themes[self.current_theme]
    
    def get_custom_css(self) -> str:
        """获取自定义CSS字符串"""
        theme = self.get_current_theme()
        return self._generate_css(theme)
    
    def get_theme_css(self) -> str:
        """获取主题CSS（别名方法）"""
        return self.get_custom_css()
    
    def apply_theme(self):
        """应用当前主题到Streamlit应用"""
        theme = self.get_current_theme()
        css = self._generate_css(theme)
        st.markdown(css, unsafe_allow_html=True)
    
    def _generate_css(self, theme: Dict[str, Any]) -> str:
        """生成主题CSS"""
        colors = theme['colors']
        fonts = theme['fonts']
        spacing = theme['spacing']
        border_radius = theme['border_radius']
        
        return f"""
        <style>
        /* 全局样式重置 */
        .stApp {{
            background-color: {colors['background']};
            color: {colors['text_primary']};
            font-family: {fonts['primary']};
        }}
        
        /* 主容器样式 */
        .main .block-container {{
            padding-top: {spacing['lg']};
            padding-bottom: {spacing['lg']};
            max-width: 1200px;
        }}
        
        /* 标题样式 */
        h1, h2, h3, h4, h5, h6 {{
            color: {colors['text_primary']};
            font-family: {fonts['primary']};
            font-weight: 600;
        }}
        
        h1 {{
            font-size: 2.5rem;
            margin-bottom: {spacing['lg']};
        }}
        
        h2 {{
            font-size: 2rem;
            margin-bottom: {spacing['md']};
        }}
        
        h3 {{
            font-size: 1.5rem;
            margin-bottom: {spacing['md']};
        }}
        
        /* 指标卡片样式 */
        [data-testid="metric-container"] {{
            background-color: {colors['surface']};
            border: 1px solid {colors['border']};
            border-radius: {border_radius['md']};
            padding: {spacing['md']};
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            transition: transform 0.2s ease, box-shadow 0.2s ease;
        }}
        
        [data-testid="metric-container"]:hover {{
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }}
        
        /* 按钮样式 */
        .stButton > button {{
            background-color: {colors['primary']};
            color: white;
            border: none;
            border-radius: {border_radius['md']};
            padding: {spacing['sm']} {spacing['md']};
            font-family: {fonts['primary']};
            font-weight: 500;
            transition: all 0.2s ease;
        }}
        
        .stButton > button:hover {{
            background-color: {colors['secondary']};
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }}
        
        /* 选择框样式 */
        .stSelectbox > div > div {{
            background-color: {colors['surface']};
            border: 1px solid {colors['border']};
            border-radius: {border_radius['sm']};
        }}
        
        /* 输入框样式 */
        .stTextInput > div > div > input {{
            background-color: {colors['surface']};
            border: 1px solid {colors['border']};
            border-radius: {border_radius['sm']};
            color: {colors['text_primary']};
        }}
        
        /* 数据表格样式 */
        .stDataFrame {{
            border: 1px solid {colors['border']};
            border-radius: {border_radius['md']};
            overflow: hidden;
        }}
        
        /* 侧边栏样式 */
        .css-1d391kg {{
            background-color: {colors['surface']};
            border-right: 1px solid {colors['border']};
        }}
        
        /* Tab样式 */
        .stTabs [data-baseweb="tab-list"] {{
            gap: {spacing['sm']};
        }}
        
        .stTabs [data-baseweb="tab"] {{
            background-color: {colors['surface']};
            border: 1px solid {colors['border']};
            border-radius: {border_radius['sm']};
            padding: {spacing['sm']} {spacing['md']};
            color: {colors['text_secondary']};
        }}
        
        .stTabs [aria-selected="true"] {{
            background-color: {colors['primary']};
            color: white;
            border-color: {colors['primary']};
        }}
        
        /* 展开器样式 */
        .streamlit-expanderHeader {{
            background-color: {colors['surface']};
            border: 1px solid {colors['border']};
            border-radius: {border_radius['sm']};
            color: {colors['text_primary']};
        }}
        
        /* 进度条样式 */
        .stProgress > div > div > div {{
            background-color: {colors['primary']};
        }}
        
        /* 成功消息样式 */
        .stSuccess {{
            background-color: {colors['success']}20;
            border-left: 4px solid {colors['success']};
            color: {colors['success']};
        }}
        
        /* 错误消息样式 */
        .stError {{
            background-color: {colors['error']}20;
            border-left: 4px solid {colors['error']};
            color: {colors['error']};
        }}
        
        /* 警告消息样式 */
        .stWarning {{
            background-color: {colors['warning']}20;
            border-left: 4px solid {colors['warning']};
            color: {colors['warning']};
        }}
        
        /* 信息消息样式 */
        .stInfo {{
            background-color: {colors['info']}20;
            border-left: 4px solid {colors['info']};
            color: {colors['info']};
        }}
        
        /* 自定义卡片样式 */
        .custom-card {{
            background-color: {colors['surface']};
            border: 1px solid {colors['border']};
            border-radius: {border_radius['lg']};
            padding: {spacing['lg']};
            margin: {spacing['md']} 0;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }}
        
        /* 状态徽章样式 */
        .status-badge {{
            display: inline-block;
            padding: {spacing['xs']} {spacing['sm']};
            border-radius: {border_radius['sm']};
            font-size: 0.875rem;
            font-weight: 500;
            margin: {spacing['xs']};
        }}
        
        .status-success {{
            background-color: {colors['success']}20;
            color: {colors['success']};
            border: 1px solid {colors['success']};
        }}
        
        .status-error {{
            background-color: {colors['error']}20;
            color: {colors['error']};
            border: 1px solid {colors['error']};
        }}
        
        .status-warning {{
            background-color: {colors['warning']}20;
            color: {colors['warning']};
            border: 1px solid {colors['warning']};
        }}
        
        .status-info {{
            background-color: {colors['info']}20;
            color: {colors['info']};
            border: 1px solid {colors['info']};
        }}
        
        /* 响应式设计 */
        @media (max-width: 768px) {{
            .main .block-container {{
                padding-left: {spacing['sm']};
                padding-right: {spacing['sm']};
            }}
            
            h1 {{
                font-size: 2rem;
            }}
            
            h2 {{
                font-size: 1.5rem;
            }}
        }}
        
        /* 隐藏Streamlit默认元素 */
        #MainMenu {{visibility: hidden;}}
        footer {{visibility: hidden;}}
        header {{visibility: hidden;}}
        
        /* 自定义滚动条 */
        ::-webkit-scrollbar {{
            width: 8px;
        }}
        
        ::-webkit-scrollbar-track {{
            background: {colors['surface']};
        }}
        
        ::-webkit-scrollbar-thumb {{
            background: {colors['border']};
            border-radius: {border_radius['sm']};
        }}
        
        ::-webkit-scrollbar-thumb:hover {{
            background: {colors['text_secondary']};
        }}
        </style>
        """
    
    def get_color(self, color_name: str) -> str:
        """获取当前主题的颜色值"""
        theme = self.get_current_theme()
        return theme['colors'].get(color_name, '#000000')
    
    def get_spacing(self, size: str) -> str:
        """获取当前主题的间距值"""
        theme = self.get_current_theme()
        return theme['spacing'].get(size, '1rem')
    
    def render_theme_selector(self):
        """渲染主题选择器"""
        theme_names = list(self.themes.keys())
        theme_labels = [self.themes[name]['name'] for name in theme_names]
        
        selected_index = theme_names.index(self.current_theme)
        
        new_theme_index = st.selectbox(
            "选择主题",
            range(len(theme_names)),
            index=selected_index,
            format_func=lambda x: theme_labels[x],
            key="theme_selector"
        )
        
        new_theme = theme_names[new_theme_index]
        if new_theme != self.current_theme:
            self.set_theme(new_theme)
            st.rerun()

# 全局主题管理器实例
theme_manager = ThemeManager()

def get_theme_manager() -> ThemeManager:
    """获取主题管理器实例"""
    return theme_manager

def apply_default_theme():
    """应用默认主题"""
    theme_manager.apply_theme()