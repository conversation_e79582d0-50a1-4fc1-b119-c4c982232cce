#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🎯 Yahoo Finance API矩阵页面
展示6个Yahoo Finance API的实时化学反应和智能轮换策略

设计理念：
1. 矩阵式布局 - 6个API的实时状态监控
2. 化学反应可视化 - API间的协同效应
3. 智能轮换演示 - 动态负载均衡
4. 性能对比分析 - 数据质量和响应时间
"""

import streamlit as st
import json
import time
import random
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from enum import Enum

# 导入智能调度器
try:
    from smart_api_scheduler import SmartAPIScheduler, DataType, TimeWindow
    SCHEDULER_AVAILABLE = True
except ImportError:
    SCHEDULER_AVAILABLE = False

class APIStatus:
    """API状态数据类"""
    def __init__(self, name: str, host: str, status: str, usage_percent: float,
                 response_time: float, last_call: datetime, success_rate: float, specialty: str):
        self.name = name
        self.host = host
        self.status = status  # 'active', 'cooling', 'error', 'standby'
        self.usage_percent = usage_percent
        self.response_time = response_time
        self.last_call = last_call
        self.success_rate = success_rate
        self.specialty = specialty

class YahooFinanceMatrixUI:
    """Yahoo Finance API矩阵界面"""
    
    def __init__(self):
        self.api_configs = {
            'yahoo_finance_1': {
                'name': 'Yahoo Finance 经典版',
                'host': 'yahoo-finance15.p.rapidapi.com',
                'specialty': '全面基础功能',
                'color': '#FF6B6B',
                'icon': '🏛️'
            },
            'yh_finance_complete': {
                'name': 'YH Finance 完整版',
                'host': 'yh-finance.p.rapidapi.com',
                'specialty': '结构化深度数据',
                'color': '#4ECDC4',
                'icon': '🔬'
            },
            'yahoo_finance_api_data': {
                'name': 'Yahoo Finance 搜索版',
                'host': 'yahoo-finance-api1.p.rapidapi.com',
                'specialty': '搜索和趋势',
                'color': '#45B7D1',
                'icon': '🔍'
            },
            'yahoo_finance_realtime': {
                'name': 'Yahoo Finance 实时版',
                'host': 'yahoo-finance-low-latency.p.rapidapi.com',
                'specialty': '低延迟实时',
                'color': '#96CEB4',
                'icon': '⚡'
            },
            'yh_finance': {
                'name': 'YH Finance 增强版',
                'host': 'yh-finance-complete.p.rapidapi.com',
                'specialty': '历史深度数据',
                'color': '#FFEAA7',
                'icon': '📊'
            },
            'yahoo_finance_basic': {
                'name': 'Yahoo Finance 基础版',
                'host': 'yahoo-finance127.p.rapidapi.com',
                'specialty': '简洁高效',
                'color': '#DDA0DD',
                'icon': '⚡'
            }
        }
        
        # 初始化session state
        if 'api_statuses' not in st.session_state:
            st.session_state.api_statuses = self._generate_mock_statuses()
        if 'matrix_running' not in st.session_state:
            st.session_state.matrix_running = False
        if 'performance_history' not in st.session_state:
            st.session_state.performance_history = []
        if 'smart_scheduler' not in st.session_state and SCHEDULER_AVAILABLE:
            st.session_state.smart_scheduler = SmartAPIScheduler()

    def render(self):
        """渲染主界面"""
        st.title("🎯 Yahoo Finance API矩阵实验室")
        st.markdown("*观察6个API的化学反应和智能轮换策略*")
        
        # 控制面板
        self._render_control_panel()
        
        # API矩阵网格
        self._render_api_matrix()
        
        # 化学反应可视化
        self._render_chemical_reactions()
        
        # 性能分析
        self._render_performance_analysis()
        
        # 实时日志
        self._render_real_time_logs()

        # 智能调度演示
        if SCHEDULER_AVAILABLE:
            self._render_smart_scheduler_demo()

    def _render_control_panel(self):
        """渲染控制面板"""
        st.markdown("---")
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            if st.button("🚀 启动矩阵", type="primary"):
                st.session_state.matrix_running = True
                st.rerun()
        
        with col2:
            if st.button("⏸️ 暂停矩阵"):
                st.session_state.matrix_running = False
                st.rerun()
        
        with col3:
            if st.button("🔄 重置状态"):
                st.session_state.api_statuses = self._generate_mock_statuses()
                st.session_state.performance_history = []
                st.rerun()
        
        with col4:
            auto_refresh = st.checkbox("🔄 自动刷新", value=True)
        
        # 实时状态更新
        if st.session_state.matrix_running and auto_refresh:
            time.sleep(1)
            self._update_api_statuses()
            st.rerun()

    def _render_api_matrix(self):
        """渲染API矩阵网格"""
        st.markdown("### 🔬 API状态矩阵")
        
        # 创建3x2网格
        row1_cols = st.columns(3)
        row2_cols = st.columns(3)
        
        api_keys = list(self.api_configs.keys())
        
        # 第一行
        for i, col in enumerate(row1_cols):
            if i < len(api_keys):
                self._render_api_card(col, api_keys[i])
        
        # 第二行
        for i, col in enumerate(row2_cols):
            idx = i + 3
            if idx < len(api_keys):
                self._render_api_card(col, api_keys[idx])

    def _render_api_card(self, container, api_key: str):
        """渲染单个API卡片"""
        config = self.api_configs[api_key]
        status = st.session_state.api_statuses[api_key]
        
        with container:
            # 状态颜色映射
            status_colors = {
                'active': '🟢',
                'cooling': '🟡', 
                'error': '🔴',
                'standby': '⚪'
            }
            
            # 卡片样式
            card_style = f"""
            <div style="
                border: 2px solid {config['color']};
                border-radius: 10px;
                padding: 15px;
                margin: 5px;
                background: linear-gradient(135deg, {config['color']}20, {config['color']}10);
                text-align: center;
                min-height: 200px;
            ">
                <h4>{config['icon']} {config['name']}</h4>
                <p><strong>状态:</strong> {status_colors[status.status]} {status.status.upper()}</p>
                <p><strong>使用率:</strong> {status.usage_percent:.1f}%</p>
                <p><strong>响应时间:</strong> {status.response_time:.0f}ms</p>
                <p><strong>成功率:</strong> {status.success_rate:.1f}%</p>
                <p><strong>专长:</strong> {status.specialty}</p>
            </div>
            """
            st.markdown(card_style, unsafe_allow_html=True)
            
            # 使用率进度条
            st.progress(status.usage_percent / 100)
            
            # 快速测试按钮
            if st.button(f"🧪 测试 {config['name']}", key=f"test_{api_key}"):
                self._test_api(api_key)

    def _render_chemical_reactions(self):
        """渲染化学反应可视化"""
        st.markdown("### ⚗️ API化学反应图")

        if not PLOTLY_AVAILABLE:
            # 简化版本的可视化
            st.info("📊 Plotly未安装，显示简化版本的API协同关系")

            # 创建简单的文本表格显示协同关系
            st.markdown("#### API协同效应矩阵")

            api_keys = list(self.api_configs.keys())

            # 创建协同效应表格
            synergy_data = []
            for i, api1 in enumerate(api_keys):
                row = []
                for j, api2 in enumerate(api_keys):
                    if i == j:
                        row.append("🔵")  # 自己
                    else:
                        status1 = st.session_state.api_statuses[api1]
                        status2 = st.session_state.api_statuses[api2]
                        synergy = (status1.success_rate + status2.success_rate) / 200

                        if synergy > 0.9:
                            row.append("🟢")  # 高协同
                        elif synergy > 0.8:
                            row.append("🟡")  # 中协同
                        else:
                            row.append("🔴")  # 低协同
                synergy_data.append(row)

            # 显示表格
            cols = st.columns(len(api_keys) + 1)

            # 表头
            with cols[0]:
                st.write("**API**")
            for i, api_key in enumerate(api_keys):
                with cols[i + 1]:
                    st.write(f"**{self.api_configs[api_key]['icon']}**")

            # 表格内容
            for i, (api_key, row_data) in enumerate(zip(api_keys, synergy_data)):
                cols = st.columns(len(api_keys) + 1)
                with cols[0]:
                    st.write(f"**{self.api_configs[api_key]['icon']} {self.api_configs[api_key]['name'][:8]}**")
                for j, cell_data in enumerate(row_data):
                    with cols[j + 1]:
                        st.write(cell_data)

            st.markdown("🟢 高协同 | 🟡 中协同 | 🔴 低协同 | 🔵 自身")
            return

        # 原有的Plotly版本
        # 创建网络图显示API间的协同效应
        fig = go.Figure()

        # 节点位置（圆形排列）
        import math
        n_apis = len(self.api_configs)
        positions = []
        for i, (api_key, config) in enumerate(self.api_configs.items()):
            angle = 2 * math.pi * i / n_apis
            x = math.cos(angle)
            y = math.sin(angle)
            positions.append((x, y))

            # 添加节点
            status = st.session_state.api_statuses[api_key]
            size = 20 + status.usage_percent / 2  # 根据使用率调整大小

            fig.add_trace(go.Scatter(
                x=[x], y=[y],
                mode='markers+text',
                marker=dict(
                    size=size,
                    color=config['color'],
                    line=dict(width=2, color='white')
                ),
                text=config['icon'],
                textposition="middle center",
                name=config['name'],
                hovertemplate=f"<b>{config['name']}</b><br>" +
                             f"状态: {status.status}<br>" +
                             f"使用率: {status.usage_percent:.1f}%<br>" +
                             f"响应时间: {status.response_time:.0f}ms<extra></extra>"
            ))

        # 添加连接线（表示API间的协同）
        for i in range(n_apis):
            for j in range(i+1, n_apis):
                # 根据API状态决定连接强度
                api1_key = list(self.api_configs.keys())[i]
                api2_key = list(self.api_configs.keys())[j]
                status1 = st.session_state.api_statuses[api1_key]
                status2 = st.session_state.api_statuses[api2_key]

                # 计算协同强度
                synergy = (status1.success_rate + status2.success_rate) / 200

                if synergy > 0.8:  # 只显示高协同的连接
                    fig.add_trace(go.Scatter(
                        x=[positions[i][0], positions[j][0]],
                        y=[positions[i][1], positions[j][1]],
                        mode='lines',
                        line=dict(
                            width=synergy * 3,
                            color=f'rgba(100, 100, 100, {synergy * 0.5})'
                        ),
                        showlegend=False,
                        hoverinfo='skip'
                    ))

        fig.update_layout(
            title="API协同网络 - 节点大小表示使用率，连线表示协同效应",
            showlegend=False,
            xaxis=dict(showgrid=False, zeroline=False, showticklabels=False),
            yaxis=dict(showgrid=False, zeroline=False, showticklabels=False),
            height=400,
            plot_bgcolor='rgba(0,0,0,0)'
        )

        st.plotly_chart(fig, use_container_width=True)

    def _render_performance_analysis(self):
        """渲染性能分析"""
        st.markdown("### 📊 性能分析仪表板")

        col1, col2 = st.columns(2)

        with col1:
            st.markdown("#### 📈 响应时间对比")

            if not PLOTLY_AVAILABLE:
                # 简化版本的响应时间显示
                for api_key, config in self.api_configs.items():
                    status = st.session_state.api_statuses[api_key]

                    # 创建简单的进度条显示响应时间
                    max_time = 500  # 最大响应时间500ms
                    progress = min(status.response_time / max_time, 1.0)

                    # 根据响应时间设置颜色
                    if status.response_time < 100:
                        color = "🟢"
                    elif status.response_time < 200:
                        color = "🟡"
                    else:
                        color = "🔴"

                    st.write(f"{config['icon']} **{config['name'][:12]}**: {color} {status.response_time:.0f}ms")
                    st.progress(progress)
            else:
                # 原有的Plotly版本
                response_times = [status.response_time for status in st.session_state.api_statuses.values()]
                api_names = [config['name'] for config in self.api_configs.values()]

                fig_response = px.bar(
                    x=api_names,
                    y=response_times,
                    title="API响应时间对比",
                    labels={'x': 'API', 'y': '响应时间 (ms)'},
                    color=response_times,
                    color_continuous_scale='RdYlGn_r'
                )
                fig_response.update_layout(height=300)
                st.plotly_chart(fig_response, use_container_width=True)

        with col2:
            st.markdown("#### 🎯 成功率分析")

            if not PLOTLY_AVAILABLE:
                # 简化版本的成功率显示
                for api_key, config in self.api_configs.items():
                    status = st.session_state.api_statuses[api_key]

                    # 根据成功率设置颜色
                    if status.success_rate >= 95:
                        color = "🟢"
                    elif status.success_rate >= 90:
                        color = "🟡"
                    else:
                        color = "🔴"

                    st.write(f"{config['icon']} **{config['name'][:12]}**: {color} {status.success_rate:.1f}%")
                    st.progress(status.success_rate / 100)
            else:
                # 原有的Plotly雷达图版本
                success_rates = [status.success_rate for status in st.session_state.api_statuses.values()]
                api_names = [config['name'] for config in self.api_configs.values()]

                fig_radar = go.Figure()
                fig_radar.add_trace(go.Scatterpolar(
                    r=success_rates,
                    theta=api_names,
                    fill='toself',
                    name='成功率'
                ))

                fig_radar.update_layout(
                    polar=dict(
                        radialaxis=dict(
                            visible=True,
                            range=[0, 100]
                        )),
                    title="API成功率雷达图",
                    height=300
                )
                st.plotly_chart(fig_radar, use_container_width=True)

    def _render_real_time_logs(self):
        """渲染实时日志"""
        st.markdown("### 📝 实时操作日志")
        
        # 模拟日志数据
        if st.session_state.matrix_running:
            current_time = datetime.now().strftime("%H:%M:%S")
            
            # 随机选择一个API进行操作
            api_key = random.choice(list(self.api_configs.keys()))
            api_name = self.api_configs[api_key]['name']
            
            operations = [
                f"✅ {api_name} 成功获取AAPL股价数据",
                f"🔄 {api_name} 自动切换到备用端点",
                f"⚡ {api_name} 响应时间优化至{random.randint(50, 200)}ms",
                f"🎯 {api_name} 命中缓存，响应加速",
                f"🔍 {api_name} 开始搜索热门股票"
            ]
            
            log_entry = f"[{current_time}] {random.choice(operations)}"
            
            # 保持最近20条日志
            if 'logs' not in st.session_state:
                st.session_state.logs = []
            
            st.session_state.logs.append(log_entry)
            if len(st.session_state.logs) > 20:
                st.session_state.logs.pop(0)
        
        # 显示日志
        if 'logs' in st.session_state:
            log_container = st.container()
            with log_container:
                for log in reversed(st.session_state.logs[-10:]):  # 显示最近10条
                    st.text(log)

    def _generate_mock_statuses(self) -> Dict[str, APIStatus]:
        """生成模拟API状态"""
        statuses = {}
        for api_key, config in self.api_configs.items():
            statuses[api_key] = APIStatus(
                name=config['name'],
                host=config['host'],
                status=random.choice(['active', 'standby', 'cooling']),
                usage_percent=random.uniform(0, 95),
                response_time=random.uniform(50, 300),
                last_call=datetime.now() - timedelta(seconds=random.randint(1, 300)),
                success_rate=random.uniform(85, 99.5),
                specialty=config['specialty']
            )
        return statuses

    def _update_api_statuses(self):
        """更新API状态（模拟实时变化）"""
        for api_key in st.session_state.api_statuses:
            status = st.session_state.api_statuses[api_key]
            
            # 随机更新状态
            if random.random() < 0.1:  # 10%概率改变状态
                status.status = random.choice(['active', 'standby', 'cooling'])
            
            # 更新使用率（模拟波动）
            status.usage_percent += random.uniform(-5, 5)
            status.usage_percent = max(0, min(100, status.usage_percent))
            
            # 更新响应时间
            status.response_time += random.uniform(-20, 20)
            status.response_time = max(30, min(500, status.response_time))
            
            # 更新成功率
            status.success_rate += random.uniform(-1, 1)
            status.success_rate = max(80, min(100, status.success_rate))

    def _test_api(self, api_key: str):
        """测试指定API"""
        config = self.api_configs[api_key]
        
        with st.spinner(f"正在测试 {config['name']}..."):
            time.sleep(random.uniform(0.5, 2))  # 模拟API调用
            
            # 模拟测试结果
            success = random.random() > 0.1  # 90%成功率
            
            if success:
                st.success(f"✅ {config['name']} 测试成功！响应时间: {random.randint(50, 200)}ms")
                # 更新状态
                st.session_state.api_statuses[api_key].status = 'active'
                st.session_state.api_statuses[api_key].last_call = datetime.now()
            else:
                st.error(f"❌ {config['name']} 测试失败，请检查API配置")
                st.session_state.api_statuses[api_key].status = 'error'

    def _render_smart_scheduler_demo(self):
        """渲染智能调度演示"""
        st.markdown("### 🧠 智能API调度演示")

        if not SCHEDULER_AVAILABLE:
            st.warning("⚠️ 智能调度器模块未找到")
            return

        col1, col2 = st.columns(2)

        with col1:
            st.markdown("#### 📊 调度策略")

            # 数据类型选择
            data_types = {
                "实时报价": DataType.REALTIME_QUOTE,
                "历史数据": DataType.HISTORICAL_DATA,
                "公司档案": DataType.COMPANY_PROFILE,
                "市场筛选": DataType.MARKET_SCREENER,
                "搜索趋势": DataType.SEARCH_TRENDING,
                "新闻分析": DataType.NEWS_ANALYSIS,
                "技术指标": DataType.TECHNICAL_INDICATORS,
                "批量报价": DataType.BATCH_QUOTES
            }

            selected_type = st.selectbox(
                "选择数据类型",
                options=list(data_types.keys()),
                key="data_type_selector"
            )

            if st.button("🎯 智能调度测试", key="smart_schedule_test"):
                scheduler = st.session_state.smart_scheduler
                data_type = data_types[selected_type]

                with st.spinner(f"正在智能选择API获取{selected_type}..."):
                    result = scheduler.smart_get_data(data_type)

                if result['success']:
                    st.success(f"✅ 成功！使用 {result['api_used']}")
                    st.info(f"📊 响应时间: {result['response_time']:.0f}ms")
                    st.info(f"🏆 质量评分: {result['quality_score']:.1f}")
                else:
                    st.error(f"❌ 调度失败: {result['error']}")

        with col2:
            st.markdown("#### 📈 调度器状态")

            if 'smart_scheduler' in st.session_state:
                scheduler = st.session_state.smart_scheduler
                status = scheduler.get_scheduler_status()

                # 显示总体统计
                st.metric("总调用次数", status['total_calls'])
                st.metric("成功率", f"{status['success_rate']:.1%}")
                st.metric("当前时间窗口", status['time_window'])

                # 显示API使用情况
                st.markdown("**API使用情况:**")
                for api_id, usage in status['api_usage'].items():
                    usage_ratio = usage['usage_ratio']
                    quality = usage['quality_score']

                    # 使用进度条显示使用率
                    st.write(f"**{usage['name'][:15]}**")
                    st.progress(usage_ratio)
                    st.caption(f"使用: {usage['usage']}/{usage['limit']} | 质量: {quality:.1f}")

def render_yahoo_finance_matrix():
    """渲染Yahoo Finance矩阵页面的入口函数"""
    matrix_ui = YahooFinanceMatrixUI()
    matrix_ui.render()

if __name__ == "__main__":
    render_yahoo_finance_matrix()
