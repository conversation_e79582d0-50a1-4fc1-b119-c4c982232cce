#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
太公心易BI系统 - 完整版Streamlit应用
集成内盘数据、VIP策略、AI辩论等全部功能
"""

import os
import sys
from pathlib import Path
import logging
from datetime import datetime
import random

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 设置环境变量
os.environ['STREAMLIT_SERVER_HEADLESS'] = 'true'
os.environ['STREAMLIT_SERVER_ENABLE_CORS'] = 'false'
os.environ['STREAMLIT_SERVER_ENABLE_XSRF_PROTECTION'] = 'false'

# 导入Streamlit
import streamlit as st

# Streamlit页面配置
st.set_page_config(
    page_title="太公心易BI系统",
    page_icon="🏛️",
    layout="wide",
    initial_sidebar_state="expanded",
    menu_items={
        'Get Help': 'https://github.com/jingminzhang/cauldron',
        'Report a bug': 'https://github.com/jingminzhang/cauldron/issues',
        'About': '太公心易BI系统 - 集数据、分析、交易和AI辩论于一体的综合性量化金融研究平台'
    }
)


def main():
    """主程序入口"""
    try:
        # 导入UI模块
        from src.ui.morning_briefing_ui import MorningBriefingUI
        
        # 尝试导入新功能模块
        try:
            from src.ui.domestic_market_ui import DomesticMarketUI
            domestic_available = True
        except ImportError:
            domestic_available = False
            logger.warning("内盘市场模块导入失败")
        
        try:
            from src.ui.vip_strategy_ui import VIPStrategyUI
            vip_available = True
        except ImportError:
            vip_available = False
            logger.warning("VIP策略模块导入失败")
        
        try:
            from src.ui.jixia_academy_ui import JixiaAcademyUI
            jixia_available = True
        except ImportError:
            jixia_available = False
            logger.warning("稷下学宫模块导入失败")

        try:
            from src.ui.simple_theater_ui import SimpleTheaterUI
            theater_available = True
        except ImportError:
            theater_available = False
            logger.warning("韭菜小剧场模块导入失败")
        
        # 侧边栏导航
        with st.sidebar:
            st.title("🏛️ 太公心易BI")
            st.markdown("*颠覆传统交易思维，重塑投资认知边界*")
            
            # 功能模块选择
            available_pages = ["🌅 晨报系统"]

            # 添加Yahoo Finance矩阵页面
            available_pages.append("🎯 Yahoo矩阵")

            if theater_available:
                available_pages.append("🔪 对韭当割")

            if jixia_available:
                available_pages.append("🏛️ 稷下学宫")

            if domestic_available:
                available_pages.append("🇨🇳 内盘市场")

            if vip_available:
                available_pages.append("👑 策略中心")

            available_pages.extend(["🔍 系统监控", "📊 功能演示"])
            
            page = st.selectbox("选择功能模块", available_pages)
            
            # 显示会员状态
            st.markdown("---")
            st.subheader("🎯 太公三式")
            
            # 会员等级信息
            membership_info = {
                "🆓 六壬观心": {
                    "price": "免费",
                    "features": ["历史信号查看", "基础市场分析", "社区讨论"],
                    "color": "#808080"
                },
                "💎 遁甲择时": {
                    "price": "$39/月",
                    "features": ["实时交易信号", "智能查询界面", "API访问"],
                    "color": "#4169E1"
                },
                "👑 太乙观澜": {
                    "price": "$128/月", 
                    "features": ["独立VPS部署", "完整策略源码", "私人微信服务"],
                    "color": "#FFD700"
                }
            }
            
            # 当前会员等级选择（演示用）
            current_membership = st.selectbox(
                "当前会员等级",
                list(membership_info.keys()),
                index=0
            )
            
            info = membership_info[current_membership]
            
            st.markdown(f"""
            <div style="padding: 10px; border-radius: 10px; background-color: {info['color']}20; border-left: 4px solid {info['color']};">
                <h4>{current_membership}</h4>
                <p><strong>价格:</strong> {info['price']}</p>
                <p><strong>功能:</strong></p>
                <ul>
                    {''.join([f'<li>{feature}</li>' for feature in info['features']])}
                </ul>
            </div>
            """, unsafe_allow_html=True)
            
            if current_membership == "🆓 六壬观心":
                if st.button("🚀 升级会员"):
                    st.info("升级功能开发中，敬请期待！")
            
            # 系统状态
            st.markdown("---")
            st.subheader("系统状态")
            
            status_items = [
                ("核心系统", "✅ 正常"),
                ("数据连接", "✅ 正常"),
                ("AI模型", "✅ 就绪"),
                ("韭菜剧场", "✅ 正常" if theater_available else "⚠️ 需配置"),
                ("内盘数据", "⚠️ 需配置" if not domestic_available else "✅ 正常"),
                ("VIP功能", "⚠️ 需配置" if not vip_available else "✅ 正常")
            ]
            
            for item, status in status_items:
                st.write(f"**{item}**: {status}")
        
        # 主内容区域
        if page == "🌅 晨报系统":
            morning_briefing_ui = MorningBriefingUI()
            morning_briefing_ui.run()

        elif page == "🎯 Yahoo矩阵":
            # 导入并渲染Yahoo Finance矩阵页面
            try:
                from app.pages.yahoo_finance_matrix import render_yahoo_finance_matrix
                render_yahoo_finance_matrix()
            except ImportError as e:
                st.error(f"❌ Yahoo Finance矩阵模块导入失败: {e}")
                st.info("请确保相关依赖已正确安装")

        elif page == "🔪 对韭当割" and theater_available:
            theater_ui = SimpleTheaterUI()
            theater_ui.render()

        elif page == "🏛️ 稷下学宫" and jixia_available:
            jixia_academy_ui = JixiaAcademyUI()
            jixia_academy_ui.render()

        elif page == "🇨🇳 内盘市场" and domestic_available:
            domestic_market_ui = DomesticMarketUI()
            domestic_market_ui.render()

        elif page == "👑 策略中心" and vip_available:
            vip_strategy_ui = VIPStrategyUI()
            vip_strategy_ui.render()
            
        elif page == "🔍 系统监控":
            render_system_monitoring()
            
        elif page == "📊 功能演示":
            render_feature_demo()
        
        else:
            st.error(f"功能模块 {page} 暂不可用，请检查相关依赖是否正确安装")
        
        # 页脚
        st.markdown("---")
        st.markdown(
            "<div style='text-align: center; color: grey;'>"
            "太公心易BI系统 © 2025 | 在众人向左时，智者向右"
            "</div>", 
            unsafe_allow_html=True
        )
        
    except ImportError as e:
        st.error(f"❌ 模块导入失败: {e}")
        st.write("请检查以下问题：")
        st.write("1. 是否正确安装了所有依赖包")
        st.write("2. Python路径是否正确配置")
        st.write("3. 项目结构是否完整")
        
        with st.expander("详细错误信息"):
            import traceback
            st.code(traceback.format_exc())
            
    except Exception as e:
        st.error(f"❌ 应用运行失败: {e}")
        logger.error(f"应用运行失败: {e}")
        
        with st.expander("详细错误信息"):
            import traceback
            st.code(traceback.format_exc())


def render_system_monitoring():
    """渲染系统监控页面"""
    st.title("🔍 系统监控")
    
    # 实时状态指标
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric("系统状态", "正常", delta="✅")
    
    with col2:
        cpu_usage = random.uniform(20, 80)
        st.metric("CPU使用率", f"{cpu_usage:.1f}%")
    
    with col3:
        memory_usage = random.uniform(30, 70)
        st.metric("内存使用率", f"{memory_usage:.1f}%")
    
    with col4:
        st.metric("活跃连接", random.randint(10, 50))
    
    # 功能模块状态
    st.subheader("功能模块状态")
    
    modules = [
        {"name": "晨报系统", "status": "✅ 正常", "description": "每日市场分析和AI洞察"},
        {"name": "稷下学宫", "status": "✅ 正常", "description": "AI辩论系统，三清八仙架构"},
        {"name": "内盘数据", "status": "⚠️ 部分可用", "description": "A股、期货数据接入"},
        {"name": "策略中心", "status": "✅ 正常", "description": "VIP策略源码管理"},
        {"name": "IB连接", "status": "⚠️ 需配置", "description": "Interactive Brokers数据接口"},
        {"name": "数据库", "status": "✅ 正常", "description": "PostgreSQL数据存储"}
    ]
    
    for module in modules:
        col1, col2, col3 = st.columns([2, 1, 3])
        
        with col1:
            st.write(f"**{module['name']}**")
        
        with col2:
            st.write(module['status'])
        
        with col3:
            st.write(module['description'])
    
    # 环境信息
    st.subheader("环境信息")
    
    env_info = {
        "Python版本": sys.version.split()[0],
        "工作目录": str(Path.cwd()),
        "部署环境": "Heroku" if 'DYNO' in os.environ else "本地开发",
        "数据库": "PostgreSQL (Heroku)" if 'DATABASE_URL' in os.environ else "本地数据库",
        "当前时间": datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
        "时区": str(datetime.now().astimezone().tzinfo)
    }
    
    for key, value in env_info.items():
        st.write(f"- **{key}**: {value}")


def render_feature_demo():
    """渲染功能演示页面"""
    st.title("📊 功能演示")
    
    st.markdown("""
    ## 🎯 太公心易BI系统功能概览
    
    ### 核心理念
    > **"在众人向左时，智者向右。在市场恐慌时，我们冷静。这就是太公心易的智慧。"**
    
    ### 🏛️ 太公三式架构
    """)
    
    col1, col2, col3 = st.columns(3)
    
    with col1:
        st.markdown("""
        #### 🆓 六壬观心
        **市场情绪洞察**
        - 免费开放，建立信任
        - 多源数据整合
        - 实时情绪监控
        - 风险代理机制
        """)
    
    with col2:
        st.markdown("""
        #### ⚡ 遁甲择时
        **时机把握艺术**
        - 策略逻辑透明
        - 日内交易精髓
        - 传统现代融合
        - 成败案例并重
        """)
    
    with col3:
        st.markdown("""
        #### 👑 太乙观澜
        **深度智慧体系**
        - 独立VPS部署
        - 逆向思维策略
        - 专属一对一服务
        - 持续代码更新
        """)
    
    # 技术架构展示
    st.subheader("🔧 技术架构")
    
    st.markdown("""
    ### 数据层
    - **海外市场**: Interactive Brokers (IB) API
    - **内盘市场**: Tushare Pro + AkShare
    - **区块链**: Blockchair API (已集成)
    - **新闻舆情**: 多源新闻API整合
    
    ### 分析层
    - **稷下学宫**: 三清八仙AI辩论系统
    - **妖股识别**: 多维度异常检测算法
    - **策略回测**: 高性能回测引擎
    - **风险管理**: 实时风控监控
    
    ### 应用层
    - **Web界面**: Streamlit响应式设计
    - **API服务**: FastAPI RESTful接口
    - **移动端**: 渐进式Web应用(PWA)
    - **通知系统**: 多渠道消息推送
    """)
    
    # 创新亮点
    st.subheader("💡 创新亮点")
    
    innovations = [
        {
            "title": "🎭 稷下学宫AI辩论",
            "description": "全球首创的结构化AI辩论系统，模拟古代稷下学宫的辩论模式，通过对立统一的思辨过程深度分析市场"
        },
        {
            "title": "🔮 逆向思维策略",
            "description": "挑战传统交易教条，提供反共识的投资策略和心理教练技术"
        },
        {
            "title": "👑 至尊会员源码服务",
            "description": "业界独有的策略源码完全开放模式，让用户真正掌握交易逻辑"
        },
        {
            "title": "🇨🇳 内外盘数据融合",
            "description": "无缝整合海外和国内市场数据，提供全球化投资视野"
        }
    ]
    
    for innovation in innovations:
        with st.expander(innovation["title"]):
            st.write(innovation["description"])
    
    # 路线图
    st.subheader("🗺️ 发展路线图")
    
    roadmap = {
        "✅ 已完成": [
            "核心架构搭建",
            "IB数据接入",
            "Blockchair集成",
            "基础AI分析"
        ],
        "🚧 开发中": [
            "内盘数据完善",
            "VIP策略系统",
            "稷下学宫优化",
            "移动端适配"
        ],
        "📋 计划中": [
            "期货数据接入",
            "量化策略商城",
            "社区功能",
            "国际化支持"
        ]
    }
    
    for phase, items in roadmap.items():
        st.write(f"**{phase}**")
        for item in items:
            st.write(f"  - {item}")


if __name__ == "__main__":
    main()
