# -*- coding: utf-8 -*-
"""
共享组件模块
提供可复用的UI组件和工具函数
"""

import streamlit as st
from typing import Any, Optional, Dict, List
import pandas as pd
from datetime import datetime

def format_metric_value(value: Any) -> str:
    """
    格式化指标数值显示
    
    Args:
        value: 要格式化的数值
        
    Returns:
        格式化后的字符串
    """
    if value is None:
        return "N/A"
    
    try:
        if isinstance(value, (int, float)):
            if abs(value) >= 1000000:
                return f"{value/1000000:.1f}M"
            elif abs(value) >= 1000:
                return f"{value/1000:.1f}K"
            else:
                return f"{value:,.2f}"
        else:
            return str(value)
    except (ValueError, TypeError):
        return "N/A"

def render_metric_card(title: str, value: Any, delta: Optional[str] = None, 
                      help_text: Optional[str] = None) -> None:
    """
    渲染指标卡片组件
    
    Args:
        title: 指标标题
        value: 指标数值
        delta: 变化量
        help_text: 帮助文本
    """
    formatted_value = format_metric_value(value)
    
    st.metric(
        label=title,
        value=formatted_value,
        delta=delta,
        help=help_text
    )

def render_data_table(data: pd.DataFrame, title: str = "", 
                     max_rows: int = 10) -> None:
    """
    渲染数据表格组件
    
    Args:
        data: 要显示的数据
        title: 表格标题
        max_rows: 最大显示行数
    """
    if title:
        st.subheader(title)
    
    if data.empty:
        st.info("暂无数据")
        return
    
    # 限制显示行数
    display_data = data.head(max_rows) if len(data) > max_rows else data
    
    st.dataframe(
        display_data,
        use_container_width=True,
        hide_index=True
    )
    
    if len(data) > max_rows:
        st.caption(f"显示前{max_rows}行，共{len(data)}行数据")

def render_status_indicator(status: str, message: str = "") -> None:
    """
    渲染状态指示器
    
    Args:
        status: 状态类型 ('success', 'warning', 'error', 'info')
        message: 状态消息
    """
    status_config = {
        'success': {'icon': '✅', 'method': st.success},
        'warning': {'icon': '⚠️', 'method': st.warning},
        'error': {'icon': '❌', 'method': st.error},
        'info': {'icon': 'ℹ️', 'method': st.info}
    }
    
    config = status_config.get(status, status_config['info'])
    config['method'](f"{config['icon']} {message}")

def render_loading_spinner(message: str = "加载中...") -> None:
    """
    渲染加载动画
    
    Args:
        message: 加载消息
    """
    with st.spinner(message):
        st.empty()

def render_progress_bar(progress: float, message: str = "") -> None:
    """
    渲染进度条
    
    Args:
        progress: 进度值 (0.0 - 1.0)
        message: 进度消息
    """
    if message:
        st.text(message)
    st.progress(progress)

def render_sidebar_info(title: str, content: Dict[str, Any]) -> None:
    """
    渲染侧边栏信息块
    
    Args:
        title: 信息块标题
        content: 信息内容字典
    """
    with st.sidebar:
        st.subheader(title)
        for key, value in content.items():
            st.write(f"**{key}:** {value}")

def apply_custom_css() -> None:
    """应用自定义CSS样式"""
    st.markdown("""
    <style>
    /* 自定义样式 */
    .metric-card {
        background-color: #f0f2f6;
        padding: 1rem;
        border-radius: 0.5rem;
        margin: 0.5rem 0;
    }
    
    .status-success {
        color: #28a745;
    }
    
    .status-warning {
        color: #ffc107;
    }
    
    .status-error {
        color: #dc3545;
    }
    
    .status-info {
        color: #17a2b8;
    }
    
    /* 隐藏Streamlit默认元素 */
    #MainMenu {visibility: hidden;}
    footer {visibility: hidden;}
    header {visibility: hidden;}
    
    /* 自定义按钮样式 */
    .stButton > button {
        background-color: #4CAF50;
        color: white;
        border-radius: 5px;
        border: none;
        padding: 0.5rem 1rem;
    }
    
    .stButton > button:hover {
        background-color: #45a049;
    }
    </style>
    """, unsafe_allow_html=True)

def create_download_button(data: Any, filename: str, 
                          button_text: str = "下载数据") -> None:
    """
    创建数据下载按钮
    
    Args:
        data: 要下载的数据
        filename: 文件名
        button_text: 按钮文本
    """
    if isinstance(data, pd.DataFrame):
        csv = data.to_csv(index=False)
        st.download_button(
            label=button_text,
            data=csv,
            file_name=filename,
            mime='text/csv'
        )
    else:
        st.download_button(
            label=button_text,
            data=str(data),
            file_name=filename,
            mime='text/plain'
        )

def render_alert_box(alert_type: str, title: str, message: str) -> None:
    """
    渲染警告框
    
    Args:
        alert_type: 警告类型
        title: 警告标题
        message: 警告消息
    """
    alert_styles = {
        'success': 'background-color: #d4edda; border-color: #c3e6cb; color: #155724;',
        'warning': 'background-color: #fff3cd; border-color: #ffeaa7; color: #856404;',
        'error': 'background-color: #f8d7da; border-color: #f5c6cb; color: #721c24;',
        'info': 'background-color: #d1ecf1; border-color: #bee5eb; color: #0c5460;'
    }
    
    style = alert_styles.get(alert_type, alert_styles['info'])
    
    st.markdown(f"""
    <div style="{style} padding: 1rem; border-radius: 0.25rem; border: 1px solid; margin: 1rem 0;">
        <strong>{title}</strong><br>
        {message}
    </div>
    """, unsafe_allow_html=True)

# 导出所有公共函数
__all__ = [
    'format_metric_value',
    'render_metric_card', 
    'render_data_table',
    'render_status_indicator',
    'render_loading_spinner',
    'render_progress_bar',
    'render_sidebar_info',
    'apply_custom_css',
    'create_download_button',
    'render_alert_box'
]