import os
from dotenv import load_dotenv
from typing import Dict, Any, Optional
from dataclasses import dataclass
from pathlib import Path

@dataclass
class DatabaseConfig:
    """数据库配置"""
    host: str = "localhost"
    port: int = 5432
    database: str = "cauldron_db"
    username: str = "postgres"
    password: str = ""
    pool_size: int = 10
    max_overflow: int = 20

@dataclass
class APIConfig:
    """API配置"""
    ib_host: str = "127.0.0.1"
    ib_port: int = 7497
    ib_client_id: int = 1
    timeout: int = 30
    retry_attempts: int = 3
    rate_limit: int = 100  # 每分钟请求数

@dataclass
class UIConfig:
    """UI配置"""
    page_title: str = "Cauldron - 炼妖壶"
    page_icon: str = "📊"
    layout: str = "wide"
    initial_sidebar_state: str = "expanded"
    theme: str = "default"
    items_per_page: int = 20
    refresh_interval: int = 30  # 秒

@dataclass
class DataConfig:
    """数据配置"""
    cache_ttl: int = 300  # 缓存时间（秒）
    max_cache_size: int = 1000
    data_retention_days: int = 30
    backup_enabled: bool = True
    backup_interval_hours: int = 24

@dataclass
class SecurityConfig:
    """安全配置"""
    enable_authentication: bool = False
    session_timeout: int = 3600  # 秒
    max_login_attempts: int = 5
    password_min_length: int = 8
    enable_2fa: bool = False

@dataclass
class LoggingConfig:
    """日志配置"""
    level: str = "INFO"
    format: str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    file_path: str = "logs/app.log"
    max_file_size: int = 10 * 1024 * 1024  # 10MB
    backup_count: int = 5
    enable_console: bool = True

class AppConfig:
    """应用配置管理器 - 类似HTML静态网站的配置管理"""
    
    def __init__(self):
        # 加载.env文件，使用绝对路径确保正确找到文件
        env_path = Path(__file__).parent.parent.parent / ".env"
        load_dotenv(dotenv_path=env_path)
        
        self.database = DatabaseConfig()
        self.api = APIConfig()
        self.ui = UIConfig()
        self.data = DataConfig()
        self.security = SecurityConfig()
        self.logging = LoggingConfig()
        
        # 从环境变量加载配置
        self._load_from_env()
        
        # 从配置文件加载配置
        self._load_from_file()
    
    def _load_from_env(self):
        """从环境变量加载配置"""
        # 数据库配置
        self.database.host = os.getenv('DB_HOST', self.database.host)
        self.database.port = int(os.getenv('DB_PORT', self.database.port))
        self.database.database = os.getenv('DB_NAME', self.database.database)
        self.database.username = os.getenv('DB_USER', self.database.username)
        self.database.password = os.getenv('DB_PASSWORD', self.database.password)
        
        # API配置
        self.api.ib_host = os.getenv('IB_HOST', self.api.ib_host)
        self.api.ib_port = int(os.getenv('IB_PORT', self.api.ib_port))
        self.api.ib_client_id = int(os.getenv('IB_CLIENT_ID', self.api.ib_client_id))
        
        # UI配置
        self.ui.page_title = os.getenv('APP_TITLE', self.ui.page_title)
        self.ui.theme = os.getenv('APP_THEME', self.ui.theme)
        
        # 安全配置
        self.security.enable_authentication = os.getenv('ENABLE_AUTH', 'false').lower() == 'true'
        self.security.enable_2fa = os.getenv('ENABLE_2FA', 'false').lower() == 'true'
        
        # 日志配置
        self.logging.level = os.getenv('LOG_LEVEL', self.logging.level)
        self.logging.file_path = os.getenv('LOG_FILE', self.logging.file_path)
    
    def _load_from_file(self):
        """从配置文件加载配置"""
        config_file = Path('config/app.json')
        if config_file.exists():
            try:
                import json
                with open(config_file, 'r', encoding='utf-8') as f:
                    config_data = json.load(f)
                    self._update_from_dict(config_data)
            except Exception as e:
                print(f"加载配置文件失败: {e}")
    
    def _update_from_dict(self, config_data: Dict[str, Any]):
        """从字典更新配置"""
        if 'database' in config_data:
            db_config = config_data['database']
            for key, value in db_config.items():
                if hasattr(self.database, key):
                    setattr(self.database, key, value)
        
        if 'api' in config_data:
            api_config = config_data['api']
            for key, value in api_config.items():
                if hasattr(self.api, key):
                    setattr(self.api, key, value)
        
        if 'ui' in config_data:
            ui_config = config_data['ui']
            for key, value in ui_config.items():
                if hasattr(self.ui, key):
                    setattr(self.ui, key, value)
        
        if 'data' in config_data:
            data_config = config_data['data']
            for key, value in data_config.items():
                if hasattr(self.data, key):
                    setattr(self.data, key, value)
        
        if 'security' in config_data:
            security_config = config_data['security']
            for key, value in security_config.items():
                if hasattr(self.security, key):
                    setattr(self.security, key, value)
        
        if 'logging' in config_data:
            logging_config = config_data['logging']
            for key, value in logging_config.items():
                if hasattr(self.logging, key):
                    setattr(self.logging, key, value)
    
    def get_streamlit_config(self) -> Dict[str, Any]:
        """获取Streamlit页面配置"""
        return {
            'page_title': self.ui.page_title,
            'page_icon': self.ui.page_icon,
            'layout': self.ui.layout,
            'initial_sidebar_state': self.ui.initial_sidebar_state
        }
    
    def get_database_url(self) -> str:
        """获取数据库连接URL"""
        return f"postgresql://{self.database.username}:{self.database.password}@{self.database.host}:{self.database.port}/{self.database.database}"
    
    def get_ib_connection_params(self) -> Dict[str, Any]:
        """获取IB连接参数"""
        return {
            'host': self.api.ib_host,
            'port': self.api.ib_port,
            'clientId': self.api.ib_client_id
        }
    
    def is_development(self) -> bool:
        """判断是否为开发环境"""
        return os.getenv('ENVIRONMENT', 'development').lower() == 'development'
    
    def is_production(self) -> bool:
        """判断是否为生产环境"""
        return os.getenv('ENVIRONMENT', 'development').lower() == 'production'
    
    def get_log_config(self) -> Dict[str, Any]:
        """获取日志配置"""
        return {
            'level': self.logging.level,
            'format': self.logging.format,
            'filename': self.logging.file_path,
            'maxBytes': self.logging.max_file_size,
            'backupCount': self.logging.backup_count
        }
    
    def save_to_file(self, file_path: Optional[str] = None):
        """保存配置到文件"""
        if file_path is None:
            file_path = 'config/app.json'
        
        config_data = {
            'database': {
                'host': self.database.host,
                'port': self.database.port,
                'database': self.database.database,
                'username': self.database.username,
                'pool_size': self.database.pool_size,
                'max_overflow': self.database.max_overflow
            },
            'api': {
                'ib_host': self.api.ib_host,
                'ib_port': self.api.ib_port,
                'ib_client_id': self.api.ib_client_id,
                'timeout': self.api.timeout,
                'retry_attempts': self.api.retry_attempts,
                'rate_limit': self.api.rate_limit
            },
            'ui': {
                'page_title': self.ui.page_title,
                'page_icon': self.ui.page_icon,
                'layout': self.ui.layout,
                'initial_sidebar_state': self.ui.initial_sidebar_state,
                'theme': self.ui.theme,
                'items_per_page': self.ui.items_per_page,
                'refresh_interval': self.ui.refresh_interval
            },
            'data': {
                'cache_ttl': self.data.cache_ttl,
                'max_cache_size': self.data.max_cache_size,
                'data_retention_days': self.data.data_retention_days,
                'backup_enabled': self.data.backup_enabled,
                'backup_interval_hours': self.data.backup_interval_hours
            },
            'security': {
                'enable_authentication': self.security.enable_authentication,
                'session_timeout': self.security.session_timeout,
                'max_login_attempts': self.security.max_login_attempts,
                'password_min_length': self.security.password_min_length,
                'enable_2fa': self.security.enable_2fa
            },
            'logging': {
                'level': self.logging.level,
                'format': self.logging.format,
                'file_path': self.logging.file_path,
                'max_file_size': self.logging.max_file_size,
                'backup_count': self.logging.backup_count,
                'enable_console': self.logging.enable_console
            }
        }
        
        try:
            import json
            os.makedirs(os.path.dirname(file_path), exist_ok=True)
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(config_data, f, indent=2, ensure_ascii=False)
        except Exception as e:
            print(f"保存配置文件失败: {e}")
    
    def validate_config(self) -> Dict[str, list]:
        """验证配置有效性"""
        errors = {
            'database': [],
            'api': [],
            'ui': [],
            'data': [],
            'security': [],
            'logging': []
        }
        
        # 验证数据库配置
        if not self.database.host:
            errors['database'].append('数据库主机不能为空')
        if self.database.port <= 0 or self.database.port > 65535:
            errors['database'].append('数据库端口必须在1-65535之间')
        
        # 验证API配置
        if not self.api.ib_host:
            errors['api'].append('IB主机不能为空')
        if self.api.ib_port <= 0 or self.api.ib_port > 65535:
            errors['api'].append('IB端口必须在1-65535之间')
        
        # 验证UI配置
        if not self.ui.page_title:
            errors['ui'].append('页面标题不能为空')
        if self.ui.layout not in ['centered', 'wide']:
            errors['ui'].append('页面布局必须是centered或wide')
        
        # 验证数据配置
        if self.data.cache_ttl < 0:
            errors['data'].append('缓存时间不能为负数')
        if self.data.max_cache_size <= 0:
            errors['data'].append('最大缓存大小必须大于0')
        
        # 验证安全配置
        if self.security.password_min_length < 6:
            errors['security'].append('密码最小长度不能少于6位')
        if self.security.session_timeout <= 0:
            errors['security'].append('会话超时时间必须大于0')
        
        # 验证日志配置
        if self.logging.level not in ['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL']:
            errors['logging'].append('日志级别必须是DEBUG、INFO、WARNING、ERROR或CRITICAL之一')
        
        return {k: v for k, v in errors.items() if v}

# 全局配置实例
app_config = AppConfig()

def load_app_config() -> Dict[str, Any]:
    """加载应用配置为字典格式"""
    config = app_config
    return {
        'database': {
            'host': config.database.host,
            'port': config.database.port,
            'name': config.database.database,
            'user': config.database.username,
            'password': config.database.password
        },
        'streamlit': config.get_streamlit_config(),
        'features': {
            'authentication': config.security.enable_authentication,
            'two_factor': config.security.enable_2fa
        }
    }

def get_app_config() -> AppConfig:
    """获取应用配置实例"""
    return app_config