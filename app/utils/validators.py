# -*- coding: utf-8 -*-
"""
数据验证工具
确保输入数据的有效性和安全性
"""

from typing import Any, Optional, Union, List
import re
from datetime import datetime

def validate_numeric_input(value: Any, min_val: Optional[float] = None, 
                          max_val: Optional[float] = None) -> bool:
    """
    验证数值输入
    
    Args:
        value: 要验证的值
        min_val: 最小值
        max_val: 最大值
        
    Returns:
        是否有效
    """
    try:
        num_val = float(value)
        if min_val is not None and num_val < min_val:
            return False
        if max_val is not None and num_val > max_val:
            return False
        return True
    except (ValueError, TypeError):
        return False

def validate_symbol_input(symbol: str) -> bool:
    """
    验证股票代码输入
    
    Args:
        symbol: 股票代码
        
    Returns:
        是否有效
    """
    if not isinstance(symbol, str):
        return False
    
    # 基本格式检查：字母数字组合，长度1-10
    pattern = r'^[A-Za-z0-9]{1,10}$'
    return bool(re.match(pattern, symbol.strip()))

def validate_date_range(start_date: datetime, end_date: datetime) -> bool:
    """
    验证日期范围
    
    Args:
        start_date: 开始日期
        end_date: 结束日期
        
    Returns:
        是否有效
    """
    if not isinstance(start_date, datetime) or not isinstance(end_date, datetime):
        return False
    
    return start_date <= end_date <= datetime.now()

def sanitize_user_input(input_str: str, max_length: int = 100) -> str:
    """
    清理用户输入
    
    Args:
        input_str: 用户输入字符串
        max_length: 最大长度
        
    Returns:
        清理后的字符串
    """
    if not isinstance(input_str, str):
        return ""
    
    # 移除潜在的危险字符
    cleaned = re.sub(r'[<>"\']', '', input_str.strip())
    
    # 限制长度
    return cleaned[:max_length]