from typing import Dict, Any, List, Optional, Union, Callable
from abc import ABC, abstractmethod
from dataclasses import dataclass, field
from datetime import datetime, timedelta
import asyncio
import aiohttp
import requests
import json
import time
from functools import wraps
import logging
from urllib.parse import urljoin, urlencode

# ============================================================================
# API请求和响应数据类
# ============================================================================

@dataclass
class APIRequest:
    """API请求数据类"""
    method: str  # GET, POST, PUT, DELETE
    endpoint: str
    params: Optional[Dict[str, Any]] = None
    data: Optional[Dict[str, Any]] = None
    headers: Optional[Dict[str, str]] = None
    timeout: int = 30
    retries: int = 3
    cache_ttl: Optional[int] = None  # 缓存时间（秒）
    auth_required: bool = True
    rate_limit: Optional[float] = None  # 请求间隔（秒）

@dataclass
class APIResponse:
    """API响应数据类"""
    status_code: int
    data: Any
    headers: Dict[str, str]
    url: str
    request_time: float
    cached: bool = False
    error: Optional[str] = None
    
    @property
    def success(self) -> bool:
        return 200 <= self.status_code < 300
    
    @property
    def is_rate_limited(self) -> bool:
        return self.status_code == 429
    
    @property
    def is_unauthorized(self) -> bool:
        return self.status_code == 401

@dataclass
class RateLimitInfo:
    """速率限制信息"""
    requests_per_minute: int
    requests_per_hour: int
    requests_per_day: int
    current_minute_count: int = 0
    current_hour_count: int = 0
    current_day_count: int = 0
    last_reset_minute: datetime = field(default_factory=datetime.now)
    last_reset_hour: datetime = field(default_factory=datetime.now)
    last_reset_day: datetime = field(default_factory=datetime.now)
    
    def can_make_request(self) -> bool:
        """检查是否可以发起请求"""
        now = datetime.now()
        
        # 重置计数器
        if now - self.last_reset_minute >= timedelta(minutes=1):
            self.current_minute_count = 0
            self.last_reset_minute = now
        
        if now - self.last_reset_hour >= timedelta(hours=1):
            self.current_hour_count = 0
            self.last_reset_hour = now
        
        if now - self.last_reset_day >= timedelta(days=1):
            self.current_day_count = 0
            self.last_reset_day = now
        
        # 检查限制
        return (self.current_minute_count < self.requests_per_minute and
                self.current_hour_count < self.requests_per_hour and
                self.current_day_count < self.requests_per_day)
    
    def record_request(self):
        """记录请求"""
        self.current_minute_count += 1
        self.current_hour_count += 1
        self.current_day_count += 1

# ============================================================================
# 抽象API客户端基类
# ============================================================================

class BaseAPIClient(ABC):
    """API客户端基类"""
    
    def __init__(self, base_url: str, api_key: str = None, 
                 default_headers: Dict[str, str] = None,
                 rate_limit: RateLimitInfo = None):
        self.base_url = base_url.rstrip('/')
        self.api_key = api_key
        self.default_headers = default_headers or {}
        self.rate_limit = rate_limit
        self.session = requests.Session()
        self.cache = {}  # 简单的内存缓存
        self.logger = logging.getLogger(self.__class__.__name__)
        
        # 设置默认请求头
        if api_key:
            self.default_headers.update(self._get_auth_headers())
    
    @abstractmethod
    def _get_auth_headers(self) -> Dict[str, str]:
        """获取认证头部"""
        pass
    
    def _build_url(self, endpoint: str) -> str:
        """构建完整URL"""
        return urljoin(self.base_url + '/', endpoint.lstrip('/'))
    
    def _prepare_headers(self, headers: Dict[str, str] = None) -> Dict[str, str]:
        """准备请求头部"""
        final_headers = self.default_headers.copy()
        if headers:
            final_headers.update(headers)
        return final_headers
    
    def _get_cache_key(self, request: APIRequest) -> str:
        """生成缓存键"""
        key_parts = [
            request.method,
            request.endpoint,
            str(request.params),
            str(request.data)
        ]
        return '|'.join(key_parts)
    
    def _get_cached_response(self, cache_key: str, ttl: int) -> Optional[APIResponse]:
        """获取缓存的响应"""
        if cache_key in self.cache:
            cached_time, cached_response = self.cache[cache_key]
            if datetime.now() - cached_time < timedelta(seconds=ttl):
                cached_response.cached = True
                return cached_response
            else:
                # 缓存过期，删除
                del self.cache[cache_key]
        return None
    
    def _cache_response(self, cache_key: str, response: APIResponse):
        """缓存响应"""
        self.cache[cache_key] = (datetime.now(), response)
    
    def _check_rate_limit(self) -> bool:
        """检查速率限制"""
        if self.rate_limit:
            return self.rate_limit.can_make_request()
        return True
    
    def _record_request(self):
        """记录请求（用于速率限制）"""
        if self.rate_limit:
            self.rate_limit.record_request()
    
    def _handle_rate_limit(self, response: APIResponse) -> bool:
        """处理速率限制响应"""
        if response.is_rate_limited:
            # 从响应头获取重试时间
            retry_after = response.headers.get('Retry-After', '60')
            try:
                wait_time = int(retry_after)
            except ValueError:
                wait_time = 60
            
            self.logger.warning(f"Rate limited, waiting {wait_time} seconds")
            time.sleep(wait_time)
            return True
        return False
    
    def request(self, request: APIRequest) -> APIResponse:
        """发起API请求"""
        # 检查缓存
        if request.cache_ttl:
            cache_key = self._get_cache_key(request)
            cached_response = self._get_cached_response(cache_key, request.cache_ttl)
            if cached_response:
                return cached_response
        
        # 检查速率限制
        if not self._check_rate_limit():
            return APIResponse(
                status_code=429,
                data=None,
                headers={},
                url="",
                request_time=0,
                error="Rate limit exceeded"
            )
        
        url = self._build_url(request.endpoint)
        headers = self._prepare_headers(request.headers)
        
        # 添加查询参数到URL
        if request.params:
            url += '?' + urlencode(request.params)
        
        start_time = time.time()
        last_exception = None
        
        # 重试逻辑
        for attempt in range(request.retries + 1):
            try:
                # 速率限制延迟
                if request.rate_limit and attempt > 0:
                    time.sleep(request.rate_limit)
                
                # 发起请求
                response = self.session.request(
                    method=request.method,
                    url=url,
                    headers=headers,
                    json=request.data if request.data else None,
                    timeout=request.timeout
                )
                
                request_time = time.time() - start_time
                
                # 解析响应数据
                try:
                    data = response.json()
                except ValueError:
                    data = response.text
                
                api_response = APIResponse(
                    status_code=response.status_code,
                    data=data,
                    headers=dict(response.headers),
                    url=url,
                    request_time=request_time
                )
                
                # 处理速率限制
                if self._handle_rate_limit(api_response):
                    continue
                
                # 记录请求（用于速率限制）
                self._record_request()
                
                # 缓存成功的响应
                if api_response.success and request.cache_ttl:
                    cache_key = self._get_cache_key(request)
                    self._cache_response(cache_key, api_response)
                
                return api_response
                
            except Exception as e:
                last_exception = e
                self.logger.warning(f"Request attempt {attempt + 1} failed: {str(e)}")
                
                if attempt < request.retries:
                    # 指数退避
                    wait_time = (2 ** attempt) * 1.0
                    time.sleep(wait_time)
        
        # 所有重试都失败
        return APIResponse(
            status_code=0,
            data=None,
            headers={},
            url=url,
            request_time=time.time() - start_time,
            error=str(last_exception) if last_exception else "Request failed"
        )
    
    async def async_request(self, request: APIRequest) -> APIResponse:
        """异步API请求"""
        # 检查缓存
        if request.cache_ttl:
            cache_key = self._get_cache_key(request)
            cached_response = self._get_cached_response(cache_key, request.cache_ttl)
            if cached_response:
                return cached_response
        
        # 检查速率限制
        if not self._check_rate_limit():
            return APIResponse(
                status_code=429,
                data=None,
                headers={},
                url="",
                request_time=0,
                error="Rate limit exceeded"
            )
        
        url = self._build_url(request.endpoint)
        headers = self._prepare_headers(request.headers)
        
        start_time = time.time()
        last_exception = None
        
        # 重试逻辑
        for attempt in range(request.retries + 1):
            try:
                # 速率限制延迟
                if request.rate_limit and attempt > 0:
                    await asyncio.sleep(request.rate_limit)
                
                async with aiohttp.ClientSession() as session:
                    async with session.request(
                        method=request.method,
                        url=url,
                        headers=headers,
                        params=request.params,
                        json=request.data,
                        timeout=aiohttp.ClientTimeout(total=request.timeout)
                    ) as response:
                        
                        request_time = time.time() - start_time
                        
                        # 解析响应数据
                        try:
                            data = await response.json()
                        except ValueError:
                            data = await response.text()
                        
                        api_response = APIResponse(
                            status_code=response.status,
                            data=data,
                            headers=dict(response.headers),
                            url=str(response.url),
                            request_time=request_time
                        )
                        
                        # 处理速率限制
                        if api_response.is_rate_limited:
                            retry_after = api_response.headers.get('Retry-After', '60')
                            try:
                                wait_time = int(retry_after)
                            except ValueError:
                                wait_time = 60
                            
                            await asyncio.sleep(wait_time)
                            continue
                        
                        # 记录请求（用于速率限制）
                        self._record_request()
                        
                        # 缓存成功的响应
                        if api_response.success and request.cache_ttl:
                            cache_key = self._get_cache_key(request)
                            self._cache_response(cache_key, api_response)
                        
                        return api_response
                        
            except Exception as e:
                last_exception = e
                self.logger.warning(f"Async request attempt {attempt + 1} failed: {str(e)}")
                
                if attempt < request.retries:
                    # 指数退避
                    wait_time = (2 ** attempt) * 1.0
                    await asyncio.sleep(wait_time)
        
        # 所有重试都失败
        return APIResponse(
            status_code=0,
            data=None,
            headers={},
            url=url,
            request_time=time.time() - start_time,
            error=str(last_exception) if last_exception else "Async request failed"
        )
    
    def get(self, endpoint: str, params: Dict[str, Any] = None, **kwargs) -> APIResponse:
        """GET请求"""
        request = APIRequest(
            method="GET",
            endpoint=endpoint,
            params=params,
            **kwargs
        )
        return self.request(request)
    
    def post(self, endpoint: str, data: Dict[str, Any] = None, **kwargs) -> APIResponse:
        """POST请求"""
        request = APIRequest(
            method="POST",
            endpoint=endpoint,
            data=data,
            **kwargs
        )
        return self.request(request)
    
    def put(self, endpoint: str, data: Dict[str, Any] = None, **kwargs) -> APIResponse:
        """PUT请求"""
        request = APIRequest(
            method="PUT",
            endpoint=endpoint,
            data=data,
            **kwargs
        )
        return self.request(request)
    
    def delete(self, endpoint: str, **kwargs) -> APIResponse:
        """DELETE请求"""
        request = APIRequest(
            method="DELETE",
            endpoint=endpoint,
            **kwargs
        )
        return self.request(request)
    
    def clear_cache(self):
        """清空缓存"""
        self.cache.clear()
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """获取缓存统计"""
        return {
            "cache_size": len(self.cache),
            "cache_keys": list(self.cache.keys())
        }

# ============================================================================
# 具体API客户端实现
# ============================================================================

class AlphaVantageClient(BaseAPIClient):
    """Alpha Vantage API客户端"""
    
    def __init__(self, api_key: str):
        super().__init__(
            base_url="https://www.alphavantage.co/query",
            api_key=api_key,
            rate_limit=RateLimitInfo(
                requests_per_minute=5,
                requests_per_hour=500,
                requests_per_day=500
            )
        )
    
    def _get_auth_headers(self) -> Dict[str, str]:
        return {}
    
    def get_stock_quote(self, symbol: str) -> APIResponse:
        """获取股票报价"""
        return self.get(
            endpoint="",
            params={
                "function": "GLOBAL_QUOTE",
                "symbol": symbol,
                "apikey": self.api_key
            },
            cache_ttl=60  # 缓存1分钟
        )
    
    def get_stock_daily(self, symbol: str, outputsize: str = "compact") -> APIResponse:
        """获取股票日线数据"""
        return self.get(
            endpoint="",
            params={
                "function": "TIME_SERIES_DAILY",
                "symbol": symbol,
                "outputsize": outputsize,
                "apikey": self.api_key
            },
            cache_ttl=300  # 缓存5分钟
        )
    
    def get_forex_rate(self, from_currency: str, to_currency: str) -> APIResponse:
        """获取外汇汇率"""
        return self.get(
            endpoint="",
            params={
                "function": "CURRENCY_EXCHANGE_RATE",
                "from_currency": from_currency,
                "to_currency": to_currency,
                "apikey": self.api_key
            },
            cache_ttl=60
        )

class CoinGeckoClient(BaseAPIClient):
    """CoinGecko API客户端"""
    
    def __init__(self, api_key: str = None):
        super().__init__(
            base_url="https://api.coingecko.com/api/v3",
            api_key=api_key,
            rate_limit=RateLimitInfo(
                requests_per_minute=10,
                requests_per_hour=1000,
                requests_per_day=10000
            )
        )
    
    def _get_auth_headers(self) -> Dict[str, str]:
        if self.api_key:
            return {"X-CG-Demo-API-Key": self.api_key}
        return {}
    
    def get_coin_price(self, coin_ids: str, vs_currencies: str = "usd") -> APIResponse:
        """获取加密货币价格"""
        return self.get(
            endpoint="simple/price",
            params={
                "ids": coin_ids,
                "vs_currencies": vs_currencies,
                "include_24hr_change": "true"
            },
            cache_ttl=30
        )
    
    def get_trending_coins(self) -> APIResponse:
        """获取热门加密货币"""
        return self.get(
            endpoint="search/trending",
            cache_ttl=300
        )
    
    def get_coin_market_data(self, vs_currency: str = "usd", 
                           per_page: int = 100, page: int = 1) -> APIResponse:
        """获取加密货币市场数据"""
        return self.get(
            endpoint="coins/markets",
            params={
                "vs_currency": vs_currency,
                "order": "market_cap_desc",
                "per_page": per_page,
                "page": page,
                "sparkline": "false"
            },
            cache_ttl=60
        )

class NewsAPIClient(BaseAPIClient):
    """News API客户端"""
    
    def __init__(self, api_key: str):
        super().__init__(
            base_url="https://newsapi.org/v2",
            api_key=api_key,
            rate_limit=RateLimitInfo(
                requests_per_minute=100,
                requests_per_hour=1000,
                requests_per_day=1000
            )
        )
    
    def _get_auth_headers(self) -> Dict[str, str]:
        return {"X-API-Key": self.api_key}
    
    def get_top_headlines(self, country: str = "us", category: str = None) -> APIResponse:
        """获取头条新闻"""
        params = {"country": country}
        if category:
            params["category"] = category
        
        return self.get(
            endpoint="top-headlines",
            params=params,
            cache_ttl=300
        )
    
    def search_news(self, query: str, language: str = "en", 
                   sort_by: str = "publishedAt") -> APIResponse:
        """搜索新闻"""
        return self.get(
            endpoint="everything",
            params={
                "q": query,
                "language": language,
                "sortBy": sort_by
            },
            cache_ttl=180
        )

class YahooFinanceClient(BaseAPIClient):
    """Yahoo Finance API客户端（非官方）"""
    
    def __init__(self):
        super().__init__(
            base_url="https://query1.finance.yahoo.com/v8/finance",
            rate_limit=RateLimitInfo(
                requests_per_minute=60,
                requests_per_hour=2000,
                requests_per_day=20000
            )
        )
    
    def _get_auth_headers(self) -> Dict[str, str]:
        return {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
        }
    
    def get_stock_quote(self, symbol: str) -> APIResponse:
        """获取股票报价"""
        return self.get(
            endpoint="chart",
            params={
                "symbols": symbol,
                "range": "1d",
                "interval": "1m"
            },
            cache_ttl=60
        )
    
    def get_market_summary(self) -> APIResponse:
        """获取市场概览"""
        return self.get(
            endpoint="market-summary",
            cache_ttl=300
        )

# ============================================================================
# API客户端管理器
# ============================================================================

class APIClientManager:
    """API客户端管理器"""
    
    def __init__(self):
        self.clients: Dict[str, BaseAPIClient] = {}
        self.logger = logging.getLogger(self.__class__.__name__)
    
    def register_client(self, name: str, client: BaseAPIClient):
        """注册API客户端"""
        self.clients[name] = client
        self.logger.info(f"Registered API client: {name}")
    
    def get_client(self, name: str) -> Optional[BaseAPIClient]:
        """获取API客户端"""
        return self.clients.get(name)
    
    def remove_client(self, name: str):
        """移除API客户端"""
        if name in self.clients:
            del self.clients[name]
            self.logger.info(f"Removed API client: {name}")
    
    def list_clients(self) -> List[str]:
        """列出所有客户端"""
        return list(self.clients.keys())
    
    def clear_all_caches(self):
        """清空所有客户端缓存"""
        for client in self.clients.values():
            client.clear_cache()
    
    def get_all_cache_stats(self) -> Dict[str, Dict[str, Any]]:
        """获取所有客户端缓存统计"""
        stats = {}
        for name, client in self.clients.items():
            stats[name] = client.get_cache_stats()
        return stats

# ============================================================================
# 全局实例和便捷函数
# ============================================================================

# 全局API客户端管理器
api_manager = APIClientManager()

def setup_api_clients(config: Dict[str, Any]):
    """设置API客户端"""
    # Alpha Vantage
    if config.get("alphavantage_api_key"):
        api_manager.register_client(
            "alphavantage",
            AlphaVantageClient(config["alphavantage_api_key"])
        )
    
    # CoinGecko
    api_manager.register_client(
        "coingecko",
        CoinGeckoClient(config.get("coingecko_api_key"))
    )
    
    # News API
    if config.get("news_api_key"):
        api_manager.register_client(
            "newsapi",
            NewsAPIClient(config["news_api_key"])
        )
    
    # Yahoo Finance
    api_manager.register_client(
        "yahoo_finance",
        YahooFinanceClient()
    )

def get_api_client(name: str) -> Optional[BaseAPIClient]:
    """获取API客户端的便捷函数"""
    return api_manager.get_client(name)

def make_api_request(client_name: str, request: APIRequest) -> APIResponse:
    """发起API请求的便捷函数"""
    client = get_api_client(client_name)
    if not client:
        return APIResponse(
            status_code=0,
            data=None,
            headers={},
            url="",
            request_time=0,
            error=f"API client '{client_name}' not found"
        )
    
    return client.request(request)

async def make_async_api_request(client_name: str, request: APIRequest) -> APIResponse:
    """发起异步API请求的便捷函数"""
    client = get_api_client(client_name)
    if not client:
        return APIResponse(
            status_code=0,
            data=None,
            headers={},
            url="",
            request_time=0,
            error=f"API client '{client_name}' not found"
        )
    
    return await client.async_request(request)

# ============================================================================
# 装饰器
# ============================================================================

def api_retry(max_retries: int = 3, backoff_factor: float = 1.0):
    """API重试装饰器"""
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs):
            last_exception = None
            
            for attempt in range(max_retries + 1):
                try:
                    return func(*args, **kwargs)
                except Exception as e:
                    last_exception = e
                    if attempt < max_retries:
                        wait_time = backoff_factor * (2 ** attempt)
                        time.sleep(wait_time)
            
            raise last_exception
        
        return wrapper
    return decorator

def api_cache(ttl: int = 300):
    """API缓存装饰器"""
    cache = {}
    
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs):
            # 生成缓存键
            cache_key = f"{func.__name__}:{str(args)}:{str(sorted(kwargs.items()))}"
            
            # 检查缓存
            if cache_key in cache:
                cached_time, cached_result = cache[cache_key]
                if datetime.now() - cached_time < timedelta(seconds=ttl):
                    return cached_result
                else:
                    del cache[cache_key]
            
            # 执行函数并缓存结果
            result = func(*args, **kwargs)
            cache[cache_key] = (datetime.now(), result)
            
            return result
        
        wrapper.cache = cache
        wrapper.clear_cache = lambda: cache.clear()
        
        return wrapper
    
    return decorator