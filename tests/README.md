# 测试目录结构

## 目录说明

- `unit/` - 单元测试：测试单个函数或类的功能
- `integration/` - 集成测试：测试多个组件之间的交互
- `e2e/` - 端到端测试：测试完整的用户场景

## 运行测试

### 运行所有测试
```bash
pytest
```

### 运行特定类型的测试
```bash
# 单元测试
pytest tests/unit/

# 集成测试  
pytest tests/integration/

# 端到端测试
pytest tests/e2e/
```

### 运行带标记的测试
```bash
# 只运行不需要网络的测试
pytest -m "not requires_network"

# 运行需要IB连接的测试
pytest -m "requires_ib"

# 运行需要Zilliz连接的测试
pytest -m "requires_zilliz"
```

## 测试标记说明

- `@pytest.mark.unit` - 单元测试
- `@pytest.mark.integration` - 集成测试  
- `@pytest.mark.e2e` - 端到端测试
- `@pytest.mark.slow` - 慢速测试
- `@pytest.mark.requires_network` - 需要网络连接
- `@pytest.mark.requires_ib` - 需要IB连接
- `@pytest.mark.requires_zilliz` - 需要Zilliz连接