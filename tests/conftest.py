# -*- coding: utf-8 -*-
"""
pytest配置文件
为整个测试套件提供共享的fixtures和配置
"""

import pytest
import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

@pytest.fixture(scope="session")
def test_data_dir():
    """测试数据目录"""
    return Path(__file__).parent / "data"

@pytest.fixture(scope="session")
def mock_env_vars():
    """模拟环境变量"""
    return {
        "ZILLIZ_ENDPOINT": "mock_endpoint",
        "ZILLIZ_TOKEN": "mock_token",
        "IB_GATEWAY_URL": "127.0.0.1",
        "IB_GATEWAY_PORT": "7497"
    }

@pytest.fixture
def setup_test_env(mock_env_vars):
    """设置测试环境"""
    original_env = {}
    for key, value in mock_env_vars.items():
        original_env[key] = os.environ.get(key)
        os.environ[key] = value
    
    yield
    
    # 恢复原始环境变量
    for key, value in original_env.items():
        if value is None:
            os.environ.pop(key, None)
        else:
            os.environ[key] = value