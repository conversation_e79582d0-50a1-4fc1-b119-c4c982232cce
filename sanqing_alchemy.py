#!/usr/bin/env python3
"""
三清炼丹系统 - 命令行交互界面

使用方法：
python sanqing_alchemy.py [--taishang|--baxian|--scene|--verify|--check-keys]

功能：
- 与太上老君对话
- 与八仙对话
- 模拟炼丹场景
- 验证API状态
- 检查API密钥设置
"""

import os
import sys
import time
import json
import requests
import argparse
from dotenv import load_dotenv

# 尝试加载.env文件
load_dotenv()

# 太上老君API（魔搭）
TAISHANG_API_KEY = os.getenv("TAISHANG_API_KEY", "")
TAISHANG_API_URL = "https://api-inference.modelscope.cn/v1/"
TAISHANG_MODEL_ID = "Qwen/QwQ-32B"

# 八仙炼丹炉API（OpenRouter）
BAXIAN_API_KEY = os.getenv("BAXIAN_API_KEY", "")
BAXIAN_API_URL = "https://openrouter.ai/api/v1/chat/completions"
BAXIAN_MODEL_ID = "google/gemma-3-27b-it:free"

def clear_screen():
    """清屏"""
    os.system('cls' if os.name == 'nt' else 'clear')

def print_header():
    """打印系统标题"""
    header = """
    ╔═══════════════════════════════════════════════╗
    ║                                               ║
    ║             三清炼丹系统 v1.0                ║
    ║                                               ║
    ║         道法自然，炼丹成仙                   ║
    ║                                               ║
    ╚═══════════════════════════════════════════════╝
    """
    print(header)

def print_menu():
    """打印主菜单"""
    menu = """
    请选择操作：
    
    1. 与太上老君对话
    2. 与八仙对话
    3. 模拟炼丹场景
    4. 验证API状态
    5. 检查API密钥设置
    6. 退出系统
    
    请输入选项（1-6）："""
    print(menu, end=" ")

def check_api_keys():
    """检查API密钥是否设置"""
    missing_keys = []
    
    if not TAISHANG_API_KEY:
        missing_keys.append("TAISHANG_API_KEY")
    
    if not BAXIAN_API_KEY:
        missing_keys.append("BAXIAN_API_KEY")
    
    if missing_keys:
        print("\n❌ 错误: 以下API密钥未设置:")
        for key in missing_keys:
            print(f"  - {key}")
        print("\n请在环境变量中设置这些密钥，或者在.env文件中添加它们。")
        return False
    
    return True

def call_taishang_api(prompt):
    """调用太上老君API（魔搭）"""
    url = f"{TAISHANG_API_URL}chat/completions"
    
    headers = {
        "Authorization": f"Bearer {TAISHANG_API_KEY}",
        "Content-Type": "application/json"
    }
    
    data = {
        "model": TAISHANG_MODEL_ID,
        "messages": [
            {
                "role": "system",
                "content": "你是道教至高神祇太上老君，炼丹术的创始人，八仙炼丹炉的创造者。请以太上老君的身份回答问题，语气要慈祥睿智。"
            },
            {
                "role": "user",
                "content": prompt
            }
        ],
        "temperature": 0.7,
        "max_tokens": 800,
        "stream": True
    }
    
    print(f"\n🧙 太上老君思考中...")
    
    try:
        response = requests.post(url, headers=headers, json=data, timeout=60, stream=True)
        
        if response.status_code == 200:
            print("\n🧙 太上老君：")
            full_response = ""
            for line in response.iter_lines():
                if line:
                    try:
                        line_str = line.decode('utf-8')
                        if line_str.startswith('data: '):
                            data_str = line_str[6:]
                            if data_str.strip() == '[DONE]':
                                break
                            data_json = json.loads(data_str)
                            content = data_json.get('choices', [{}])[0].get('delta', {}).get('content', '')
                            if content:
                                print(content, end='', flush=True)
                                full_response += content
                    except Exception as decode_error:
                        continue
            print("\n")
            return full_response
        else:
            print(f"\n❌ 太上老君无法回应: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"\n❌ 请求异常: {str(e)}")
        return None

def call_baxian_api(prompt, immortal_name="仙人"):
    """调用八仙炼丹炉API（OpenRouter）"""
    url = BAXIAN_API_URL
    
    headers = {
        "Authorization": f"Bearer {BAXIAN_API_KEY}",
        "HTTP-Referer": "https://github.com/microsoft/autogen",
        "X-Title": "Eight Immortals Alchemy System",
        "Content-Type": "application/json"
    }
    
    data = {
        "model": BAXIAN_MODEL_ID,
        "messages": [
            {
                "role": "system",
                "content": f"你是中国神话中的八仙之一，名为{immortal_name}。请以{immortal_name}的身份回答问题，展现你的个性和神通。"
            },
            {
                "role": "user",
                "content": prompt
            }
        ],
        "temperature": 0.7,
        "max_tokens": 800,
        "stream": True
    }
    
    print(f"\n🧚 {immortal_name}思考中...")
    
    try:
        response = requests.post(url, headers=headers, json=data, timeout=60, stream=True)
        
        if response.status_code == 200:
            print(f"\n🧚 {immortal_name}：")
            full_response = ""
            for line in response.iter_lines():
                if line:
                    try:
                        line_str = line.decode('utf-8')
                        if line_str.startswith('data: '):
                            data_str = line_str[6:]
                            if data_str.strip() == '[DONE]':
                                break
                            data_json = json.loads(data_str)
                            content = data_json.get('choices', [{}])[0].get('delta', {}).get('content', '')
                            if content:
                                print(content, end='', flush=True)
                                full_response += content
                    except Exception as decode_error:
                        continue
            print("\n")
            return full_response
        else:
            print(f"\n❌ {immortal_name}无法回应: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"\n❌ 请求异常: {str(e)}")
        return None

def verify_taishang_api():
    """验证太上老君API"""
    if not TAISHANG_API_KEY:
        print("\n❌ 太上老君API密钥未设置")
        return False
    
    print("\n🔍 正在验证太上老君API...")
    
    try:
        response = call_taishang_api("请简短介绍一下你自己。")
        if response:
            print("\n✅ 太上老君API验证成功！")
            return True
        else:
            print("\n❌ 太上老君API验证失败！")
            return False
    except Exception as e:
        print(f"\n❌ 太上老君API验证异常: {str(e)}")
        return False

def verify_baxian_api():
    """验证八仙炼丹炉API"""
    if not BAXIAN_API_KEY:
        print("\n❌ 八仙炼丹炉API密钥未设置")
        return False
    
    print("\n🔍 正在验证八仙炼丹炉API...")
    
    try:
        response = call_baxian_api("请简短介绍一下你自己。", "吕洞宾")
        if response:
            print("\n✅ 八仙炼丹炉API验证成功！")
            return True
        else:
            print("\n❌ 八仙炼丹炉API验证失败！")
            return False
    except Exception as e:
        print(f"\n❌ 八仙炼丹炉API验证异常: {str(e)}")
        return False

def verify_api():
    """验证API状态"""
    clear_screen()
    print_header()
    print("\n===== 🔍 验证API状态 =====\n")
    
    # 检查.env文件
    env_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), ".env")
    if os.path.exists(env_file):
        print(f"✅ 检测到.env文件: {env_file}")
    else:
        print(f"⚠️ 未检测到.env文件: {env_file}")
        print("  提示: 您可以复制.env.example为.env并填入您的API密钥")
    
    # 验证API
    taishang_success = verify_taishang_api()
    baxian_success = verify_baxian_api()
    
    # 总结
    print("\n===== 验证结果 =====\n")
    
    if taishang_success and baxian_success:
        print("✅ 所有API验证成功！系统可以正常使用。")
    elif taishang_success:
        print("⚠️ 太上老君API验证成功，但八仙炼丹炉API验证失败。")
        print("  您可以使用太上老君功能，但无法使用八仙相关功能。")
    elif baxian_success:
        print("⚠️ 八仙炼丹炉API验证成功，但太上老君API验证失败。")
        print("  您可以使用八仙功能，但无法使用太上老君相关功能。")
    else:
        print("❌ 所有API验证失败！请检查您的API密钥和网络连接。")
    
    input("\n按回车键返回主菜单...")

def talk_with_taishang():
    """与太上老君对话"""
    clear_screen()
    print_header()
    print("\n===== 🧙 与太上老君对话 =====\n")
    
    # 检查API密钥
    if not check_api_keys():
        input("\n按回车键返回主菜单...")
        return
    
    prompt = input("请输入您想问太上老君的问题：")
    if not prompt.strip():
        print("\n⚠️ 问题不能为空")
        time.sleep(1)
        return
    
    call_taishang_api(prompt)
    input("\n按回车键返回主菜单...")

def talk_with_baxian():
    """与八仙对话"""
    clear_screen()
    print_header()
    print("\n===== 🧚 与八仙对话 =====\n")
    
    # 检查API密钥
    if not check_api_keys():
        input("\n按回车键返回主菜单...")
        return
    
    immortals = [
        "铁拐李", "吕洞宾", "何仙姑", "蓝采和",
        "张果老", "韩湘子", "曹国舅", "钟离权"
    ]
    
    print("请选择您想对话的八仙：")
    for i, name in enumerate(immortals, 1):
        print(f"{i}. {name}")
    
    try:
        choice = int(input("\n请输入选项（1-8）："))
        if choice < 1 or choice > 8:
            print("\n⚠️ 无效的选项")
            time.sleep(1)
            return
        
        immortal = immortals[choice-1]
        prompt = input(f"\n请输入您想问{immortal}的问题：")
        if not prompt.strip():
            print("\n⚠️ 问题不能为空")
            time.sleep(1)
            return
        
        call_baxian_api(prompt, immortal)
        input("\n按回车键返回主菜单...")
    
    except ValueError:
        print("\n⚠️ 请输入有效的数字")
        time.sleep(1)
        return

def simulate_alchemy():
    """模拟炼丹场景"""
    clear_screen()
    print_header()
    print("\n===== 🔮 模拟炼丹场景 =====\n")
    
    # 检查API密钥
    if not check_api_keys():
        input("\n按回车键返回主菜单...")
        return
    
    print("欢迎来到太上老君的炼丹室，八仙正在使用炼丹炉炼制仙丹...\n")
    
    # 太上老君介绍炼丹炉
    taishang_response = call_taishang_api("请介绍一下你为八仙打造的炼丹炉有什么特别之处？")
    
    # 铁拐李回应
    tieguai_li_response = call_baxian_api("作为八仙之一的铁拐李，你如何使用太上老君的炼丹炉炼制丹药？", "铁拐李")
    
    # 太上老君点评
    if taishang_response and tieguai_li_response:
        taishang_comment = call_taishang_api(f"铁拐李说：{tieguai_li_response[:200]}...，请点评铁拐李的炼丹方法，并给出改进建议。")
    
        # 吕洞宾回应
        lu_dongbin_response = call_baxian_api("作为八仙之一的吕洞宾，你认为炼丹的关键是什么？你有什么独特的炼丹技巧？", "吕洞宾")
    
        # 太上老君总结
        if lu_dongbin_response:
            call_taishang_api(f"吕洞宾说：{lu_dongbin_response[:200]}...，请总结一下八仙炼丹的特点，以及你对他们的期望。")
    
    print("\n===== 🔮 场景演示结束 =====\n")
    input("按回车键返回主菜单...")

def check_api_keys_detailed():
    """详细检查API密钥设置"""
    clear_screen()
    print_header()
    print("\n===== API密钥验证工具 =====\n")
    
    # 检查.env文件
    env_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), ".env")
    if os.path.exists(env_file):
        print(f"✅ 检测到.env文件: {env_file}")
    else:
        print(f"⚠️ 未检测到.env文件: {env_file}")
        print("  提示: 您可以复制.env.example为.env并填入您的API密钥")
    
    def check_env_var(var_name, description, is_required=True):
        """检查环境变量是否设置"""
        value = os.getenv(var_name)
        if value:
            masked_value = value[:5] + "*" * (len(value) - 9) + value[-4:] if len(value) > 10 else "*" * len(value)
            print(f"✅ {var_name}: {masked_value} ({description})")
            return True
        else:
            status = "❌" if is_required else "⚠️"
            print(f"{status} {var_name}: 未设置 ({description})")
            return False
    
    print("\n----- 三清炼丹系统 -----")
    taishang_key = check_env_var("TAISHANG_API_KEY", "太上老君API密钥（魔搭）")
    baxian_key = check_env_var("BAXIAN_API_KEY", "八仙炼丹炉API密钥（OpenRouter）")
    
    print("\n----- 稷下学宫辩论系统 -----")
    openrouter_keys = [
        check_env_var("OPENROUTER_API_KEY_MODERATOR", "灵宝道君 - 主持人", False),
        check_env_var("OPENROUTER_API_KEY_PRO_1", "吕洞宾 - 正方一辩", False),
        check_env_var("OPENROUTER_API_KEY_CON_1", "何仙姑 - 反方一辩", False),
        check_env_var("OPENROUTER_API_KEY_PRO_2", "张果老 - 正方二辩", False),
        check_env_var("OPENROUTER_API_KEY_CON_2", "韩湘子 - 反方二辩", False),
        check_env_var("OPENROUTER_API_KEY_PRO_3", "汉钟离 - 正方三辩", False),
        check_env_var("OPENROUTER_API_KEY_CON_3", "蓝采和 - 反方三辩", False),
        check_env_var("OPENROUTER_API_KEY_PRO_4", "曹国舅 - 正方四辩", False),
        check_env_var("OPENROUTER_API_KEY_CON_4", "铁拐李 - 反方四辩", False)
    ]
    
    # 检查兼容性密钥
    print("\n----- 兼容性密钥 -----")
    legacy_keys = [
        check_env_var("OPENROUTER_API_KEY", "通用OpenRouter密钥", False),
        check_env_var("OPENROUTER_API_KEY_1", "OpenRouter密钥1", False),
        check_env_var("OPENROUTER_API_KEY_2", "OpenRouter密钥2", False),
        check_env_var("OPENROUTER_API_KEY_3", "OpenRouter密钥3", False),
        check_env_var("OPENROUTER_API_KEY_4", "OpenRouter密钥4", False)
    ]
    
    # 总结
    print("\n===== 验证结果 =====\n")
    
    if taishang_key and baxian_key:
        print("✅ 三清炼丹系统: 所有必需的API密钥都已设置")
    else:
        print("❌ 三清炼丹系统: 缺少必需的API密钥")
    
    if any(openrouter_keys):
        print("✅ 稷下学宫辩论系统: 检测到部分API密钥")
    else:
        print("⚠️ 稷下学宫辩论系统: 未检测到任何API密钥")
    
    if any(legacy_keys):
        print("✅ 兼容性密钥: 检测到旧版API密钥")
    else:
        print("⚠️ 兼容性密钥: 未检测到旧版API密钥")
    
    print("\n提示: 如果您需要使用三清炼丹系统或稷下学宫辩论系统，请确保相应的API密钥已正确设置。")
    input("\n按回车键返回主菜单...")

def main():
    """主函数"""
    # 解析命令行参数
    parser = argparse.ArgumentParser(description="三清炼丹系统 - 命令行交互界面")
    parser.add_argument("--taishang", action="store_true", help="直接启动太上老君对话")
    parser.add_argument("--baxian", action="store_true", help="直接启动八仙对话")
    parser.add_argument("--scene", action="store_true", help="直接启动炼丹场景模拟")
    parser.add_argument("--verify", action="store_true", help="直接验证API状态")
    parser.add_argument("--check-keys", action="store_true", help="检查API密钥设置")
    args = parser.parse_args()
    
    # 处理命令行参数
    if args.taishang:
        talk_with_taishang()
        return
    elif args.baxian:
        talk_with_baxian()
        return
    elif args.scene:
        simulate_alchemy()
        return
    elif args.verify:
        verify_api()
        return
    elif args.check_keys:
        check_api_keys_detailed()
        return
    
    # 主循环
    while True:
        clear_screen()
        print_header()
        print_menu()
        
        try:
            choice = input().strip()
            
            if choice == '1':
                talk_with_taishang()
            elif choice == '2':
                talk_with_baxian()
            elif choice == '3':
                simulate_alchemy()
            elif choice == '4':
                verify_api()
            elif choice == '5':
                check_api_keys_detailed()
            elif choice == '6':
                clear_screen()
                print("\n感谢使用三清炼丹系统，再会！\n")
                break
            else:
                print("\n⚠️ 无效的选项，请重新输入")
                time.sleep(1)
        
        except KeyboardInterrupt:
            clear_screen()
            print("\n感谢使用三清炼丹系统，再会！\n")
            break

if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        print(f"\n❌ 系统错误: {e}")
        input("\n按回车键退出...")