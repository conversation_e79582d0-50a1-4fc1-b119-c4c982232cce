# 太公心易项目 Makefile
# 提供常用的开发和测试命令

.PHONY: help test test-unit test-integration test-e2e test-fast test-coverage clean install dev-install

help:  ## 显示帮助信息
	@echo "太公心易项目 - 可用命令:"
	@echo ""
	@grep -E '^[a-zA-Z_-]+:.*?## .*$$' $(MAKEFILE_LIST) | sort | awk 'BEGIN {FS = ":.*?## "}; {printf "\033[36m%-20s\033[0m %s\n", $$1, $$2}'

install:  ## 安装生产依赖
	pip install -r requirements.txt

dev-install:  ## 安装开发依赖
	pip install -r requirements.txt
	pip install pytest pytest-cov pytest-asyncio pytest-mock

test:  ## 运行所有测试
	python run_tests.py --type all

test-unit:  ## 运行单元测试
	python run_tests.py --type unit

test-integration:  ## 运行集成测试
	python run_tests.py --type integration

test-e2e:  ## 运行端到端测试
	python run_tests.py --type e2e

test-fast:  ## 运行快速测试（跳过慢速测试）
	python run_tests.py --fast

test-coverage:  ## 运行测试并生成覆盖率报告
	python run_tests.py --coverage

clean:  ## 清理临时文件
	find . -type f -name "*.pyc" -delete
	find . -type d -name "__pycache__" -delete
	find . -type d -name "*.egg-info" -exec rm -rf {} +
	rm -rf htmlcov/
	rm -rf .coverage
	rm -rf .pytest_cache/

lint:  ## 代码检查
	@echo "🔍 运行代码检查..."
	@python -m py_compile $$(find . -name "*.py" | grep -v __pycache__)
	@echo "✅ 代码语法检查通过"

format:  ## 格式化代码
	@echo "🎨 格式化代码..."
	@echo "请手动运行: black . 或 autopep8 --in-place --recursive ."

start-app:  ## 启动应用
	python app/streamlit_app.py

start-cauldron:  ## 启动Cauldron应用
	python app/cauldron_app.py

debug-ib:  ## 调试IB连接
	python scripts/debug/diagnose_ib_connection.py

debug-zilliz:  ## 调试Zilliz连接
	python scripts/debug/zilliz_demo_script.py