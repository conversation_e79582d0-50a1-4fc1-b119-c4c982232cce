#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
炼妖壶MCP服务器
为Heroku MCP Agent提供标准化的工具接口
"""

import asyncio
import json
import sys
import logging
from typing import Dict, Any, List
import httpx
import os
from datetime import datetime

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("CauldronMCPServer")

# 配置
CAULDRON_API_URL = os.getenv("CAULDRON_API_URL", "https://cauldron.herokuapp.com")
CAULDRON_API_KEY = os.getenv("CAULDRON_API_KEY", "")


class CauldronMCPServer:
    """炼妖壶MCP服务器"""
    
    def __init__(self):
        self.tools = {}
        self.client = httpx.AsyncClient(timeout=30.0)
        self.load_tools()
    
    def load_tools(self):
        """加载工具配置"""
        try:
            with open("mcp_config.json", "r", encoding="utf-8") as f:
                config = json.load(f)
                self.tools = {tool["name"]: tool for tool in config["tools"]}
                logger.info(f"已加载 {len(self.tools)} 个MCP工具")
        except Exception as e:
            logger.error(f"加载工具配置失败: {e}")
            self.tools = {}
    
    async def list_tools(self) -> List[Dict[str, Any]]:
        """列出所有可用工具"""
        return [
            {
                "name": tool["name"],
                "description": tool["description"],
                "inputSchema": tool["inputSchema"]
            }
            for tool in self.tools.values()
        ]
    
    async def call_tool(self, name: str, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """调用指定工具"""
        try:
            if name not in self.tools:
                return {
                    "error": f"工具 {name} 不存在",
                    "available_tools": list(self.tools.keys())
                }
            
            # 调用炼妖壶API
            api_url = f"{CAULDRON_API_URL}/api/mcp/tools/{name}/call"
            headers = {}
            if CAULDRON_API_KEY:
                headers["Authorization"] = f"Bearer {CAULDRON_API_KEY}"
            
            payload = {
                "tool_name": name,
                "parameters": arguments,
                "user_context": {
                    "source": "heroku_mcp",
                    "timestamp": datetime.now().isoformat()
                }
            }
            
            response = await self.client.post(
                api_url,
                json=payload,
                headers=headers
            )
            
            if response.status_code == 200:
                result = response.json()
                return {
                    "content": [
                        {
                            "type": "text",
                            "text": self.format_tool_result(name, result)
                        }
                    ]
                }
            else:
                error_msg = f"API调用失败: {response.status_code}"
                try:
                    error_detail = response.json()
                    error_msg += f" - {error_detail.get('detail', '')}"
                except:
                    pass
                
                return {
                    "error": error_msg,
                    "status_code": response.status_code
                }
                
        except Exception as e:
            logger.error(f"调用工具 {name} 失败: {e}")
            return {
                "error": f"工具调用异常: {str(e)}"
            }
    
    def format_tool_result(self, tool_name: str, result: Dict[str, Any]) -> str:
        """格式化工具结果为可读文本"""
        if not result.get("success"):
            return f"❌ {tool_name} 执行失败: {result.get('message', '未知错误')}"
        
        data = result.get("data", {})
        
        if tool_name == "market_sentiment":
            return self.format_sentiment_result(data)
        elif tool_name == "trading_signal":
            return self.format_signal_result(data)
        elif tool_name == "portfolio_analysis":
            return self.format_portfolio_result(data)
        elif tool_name == "risk_assessment":
            return self.format_risk_result(data)
        elif tool_name == "news_impact":
            return self.format_news_result(data)
        else:
            return f"✅ {tool_name} 执行成功\n\n{json.dumps(data, ensure_ascii=False, indent=2)}"
    
    def format_sentiment_result(self, data: Dict[str, Any]) -> str:
        """格式化市场情绪分析结果"""
        sentiment = data.get("overall_sentiment", "unknown")
        score = data.get("sentiment_score", 0)
        confidence = data.get("confidence", 0)
        
        sentiment_emoji = {"positive": "📈", "negative": "📉", "neutral": "➡️"}.get(sentiment, "❓")
        
        result = f"""📊 **市场情绪分析**

{sentiment_emoji} **总体情绪**: {sentiment} (评分: {score:.2f})
🎯 **置信度**: {confidence:.1%}

🔍 **关键因素**:
"""
        for factor in data.get("key_factors", []):
            result += f"• {factor}\n"
        
        if "sector_sentiment" in data:
            result += "\n📈 **板块情绪**:\n"
            for sector, score in data["sector_sentiment"].items():
                emoji = "📈" if score > 0 else "📉" if score < 0 else "➡️"
                result += f"{emoji} {sector}: {score:+.2f}\n"
        
        result += f"\n⏰ 更新时间: {data.get('last_updated', 'N/A')}"
        return result
    
    def format_signal_result(self, data: Dict[str, Any]) -> str:
        """格式化交易信号结果"""
        symbol = data.get("symbol", "N/A")
        signal = data.get("signal", "unknown")
        strength = data.get("strength", 0)
        confidence = data.get("confidence", 0)
        
        signal_emoji = {"buy": "🟢", "sell": "🔴", "hold": "🟡"}.get(signal, "❓")
        
        result = f"""🎯 **交易信号 - {symbol}**

{signal_emoji} **信号**: {signal.upper()}
💪 **强度**: {strength:.1%}
🎯 **置信度**: {confidence:.1%}

💰 **价格建议**:
• 入场价: {data.get('entry_price', 'N/A')}
• 目标价: {data.get('target_price', 'N/A')}
• 止损价: {data.get('stop_loss', 'N/A')}
• 风险收益比: {data.get('risk_reward_ratio', 'N/A')}

📝 **分析理由**:
"""
        for reason in data.get("reasoning", []):
            result += f"• {reason}\n"
        
        result += f"\n⚙️ 策略: {data.get('strategy', 'N/A')}"
        result += f"\n⏰ 时间周期: {data.get('timeframe', 'N/A')}"
        return result
    
    def format_portfolio_result(self, data: Dict[str, Any]) -> str:
        """格式化投资组合分析结果"""
        total_value = data.get("total_value", 0)
        total_return = data.get("total_return", 0)
        sharpe_ratio = data.get("sharpe_ratio", 0)
        
        result = f"""📊 **投资组合分析**

💰 **总价值**: ¥{total_value:,.0f}
📈 **总收益率**: {total_return:+.1%}
⚡ **夏普比率**: {sharpe_ratio:.2f}
📉 **最大回撤**: {data.get('max_drawdown', 0):.1%}

🏭 **行业配置**:
"""
        for sector, weight in data.get("sector_allocation", {}).items():
            result += f"• {sector}: {weight:.1%}\n"
        
        result += "\n💡 **优化建议**:\n"
        for rec in data.get("recommendations", []):
            result += f"• {rec}\n"
        
        return result
    
    def format_risk_result(self, data: Dict[str, Any]) -> str:
        """格式化风险评估结果"""
        symbol = data.get("symbol", "N/A")
        risk_level = data.get("overall_risk", "unknown")
        risk_score = data.get("risk_score", 0)
        
        risk_emoji = {"low": "🟢", "medium": "🟡", "high": "🔴"}.get(risk_level, "❓")
        
        result = f"""⚠️ **风险评估 - {symbol}**

{risk_emoji} **风险等级**: {risk_level} (评分: {risk_score:.1f}/5)

📊 **风险分解**:
"""
        for factor, score in data.get("risk_factors", {}).items():
            result += f"• {factor}: {score:.1f}/5\n"
        
        result += "\n📉 **VaR估算**:\n"
        for period, var in data.get("var_estimates", {}).items():
            result += f"• {period}: {var:.1%}\n"
        
        result += "\n💡 **风险建议**:\n"
        for rec in data.get("recommendations", []):
            result += f"• {rec}\n"
        
        return result
    
    def format_news_result(self, data: Dict[str, Any]) -> str:
        """格式化新闻影响分析结果"""
        sentiment = data.get("sentiment", "neutral")
        impact_score = data.get("impact_score", 0)
        urgency = data.get("urgency", "medium")
        
        sentiment_emoji = {"positive": "📈", "negative": "📉", "neutral": "➡️"}.get(sentiment, "❓")
        urgency_emoji = {"high": "🚨", "medium": "⚠️", "low": "ℹ️"}.get(urgency, "❓")
        
        result = f"""📰 **新闻影响分析**

{sentiment_emoji} **情绪倾向**: {sentiment}
📊 **影响评分**: {impact_score:+.2f}
{urgency_emoji} **紧急程度**: {urgency}

🎯 **影响板块**: {', '.join(data.get('affected_sectors', []))}

⏰ **时间影响**:
• 短期: {data.get('market_impact', {}).get('short_term', 'N/A')}
• 中期: {data.get('market_impact', {}).get('medium_term', 'N/A')}
• 长期: {data.get('market_impact', {}).get('long_term', 'N/A')}

💡 **交易建议**:
"""
        for implication in data.get("trading_implications", []):
            result += f"• {implication}\n"
        
        return result


async def main():
    """主函数"""
    server = CauldronMCPServer()
    
    # 读取标准输入的JSON-RPC请求
    try:
        while True:
            line = await asyncio.get_event_loop().run_in_executor(None, sys.stdin.readline)
            if not line:
                break
            
            try:
                request = json.loads(line.strip())
                method = request.get("method")
                params = request.get("params", {})
                request_id = request.get("id")
                
                if method == "tools/list":
                    tools = await server.list_tools()
                    response = {
                        "jsonrpc": "2.0",
                        "id": request_id,
                        "result": {"tools": tools}
                    }
                elif method == "tools/call":
                    tool_name = params.get("name")
                    arguments = params.get("arguments", {})
                    result = await server.call_tool(tool_name, arguments)
                    response = {
                        "jsonrpc": "2.0", 
                        "id": request_id,
                        "result": result
                    }
                else:
                    response = {
                        "jsonrpc": "2.0",
                        "id": request_id,
                        "error": {
                            "code": -32601,
                            "message": f"方法 {method} 不存在"
                        }
                    }
                
                print(json.dumps(response, ensure_ascii=False))
                sys.stdout.flush()
                
            except json.JSONDecodeError as e:
                logger.error(f"JSON解析错误: {e}")
            except Exception as e:
                logger.error(f"处理请求失败: {e}")
                
    except KeyboardInterrupt:
        logger.info("MCP服务器已停止")
    finally:
        await server.client.aclose()


if __name__ == "__main__":
    asyncio.run(main())
