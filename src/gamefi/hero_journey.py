#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
太公心易 GameFi 系统 - 孙悟空英雄之旅
十二等级修仙进阶系统：从傻逼到牛逼的完整旅程

设计理念：
- 春夏秋冬四季轮回
- 每季三个等级，共十二级
- 结合西游记经典情节
- 真实的投资能力进阶

作者：太公心易系统
版本：v1.0 GameFi Edition
"""

import json
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict
from enum import Enum
import asyncio

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class Season(Enum):
    """四季枚举"""
    SPRING = "春"  # 觉醒之路
    SUMMER = "夏"  # 得道之路  
    AUTUMN = "秋"  # 试炼之路
    WINTER = "冬"  # 涅槃之路

@dataclass
class HeroLevelInfo:
    """英雄等级信息"""
    level: int
    name: str
    stage: str
    season: Season
    description: str

# 定义所有等级
HERO_LEVELS = {
    # 春季篇：觉醒之路
    1: HeroLevelInfo(1, "花果山", "新手村", Season.SPRING, "学习基础密码本映射，了解市场基本概念"),
    2: HeroLevelInfo(2, "见生死", "觉醒", Season.SPRING, "理解市场生死轮回，体验第一次亏损"),
    3: HeroLevelInfo(3, "求大道", "拜师", Season.SPRING, "掌握太公心易核心理念，学会稷下学宫辩论"),

    # 夏季篇：得道之路
    4: HeroLevelInfo(4, "得所望", "收获", Season.SUMMER, "首次成功预测获利，建立信心"),
    5: HeroLevelInfo(5, "傲气扬", "膨胀", Season.SUMMER, "连续成功预测，信心爆棚，开始炫耀技能"),
    6: HeroLevelInfo(6, "逐师门", "驱逐", Season.SUMMER, "因炫耀而被菩提祖师驱逐，不得再提师门名号"),

    # 秋季篇：试炼之路
    7: HeroLevelInfo(7, "受招安", "妥协", Season.AUTUMN, "接受传统金融思维，尝试融入主流"),
    8: HeroLevelInfo(8, "喝玉液", "迷醉", Season.AUTUMN, "沉迷于短期收益，忽视风险管理"),
    9: HeroLevelInfo(9, "砸金銮", "反叛", Season.AUTUMN, "发现传统金融局限，开始质疑权威"),

    # 冬季篇：涅槃之路
    10: HeroLevelInfo(10, "终被擒", "失败", Season.WINTER, "重大投资失误，损失惨重，跌入谷底"),
    11: HeroLevelInfo(11, "八卦炉", "淬炼", Season.WINTER, "在痛苦中重新学习，练就火眼金睛"),
    12: HeroLevelInfo(12, "五行山", "大成", Season.WINTER, "彻底掌握太公心易，成为齐天大圣")
}

@dataclass
class PlayerStats:
    """玩家属性"""
    # 基础属性
    level: int = 1
    experience: int = 0
    next_level_exp: int = 100
    
    # 核心能力
    prediction_accuracy: float = 0.0  # 预测准确率
    risk_management: float = 0.0      # 风险管理能力
    market_insight: float = 0.0       # 市场洞察力
    emotional_control: float = 0.0    # 情绪控制力
    
    # 游戏统计
    total_predictions: int = 0
    correct_predictions: int = 0
    total_profit: float = 0.0
    max_drawdown: float = 0.0
    
    # 成就系统
    achievements: List[str] = None
    badges: List[str] = None
    
    def __post_init__(self):
        if self.achievements is None:
            self.achievements = []
        if self.badges is None:
            self.badges = []

@dataclass
class QuestTask:
    """任务系统"""
    task_id: str
    name: str
    description: str
    level_requirement: int
    season: Season
    
    # 任务目标
    target_type: str  # 'prediction', 'profit', 'accuracy', 'debate'
    target_value: float
    current_progress: float = 0.0
    
    # 奖励
    exp_reward: int = 0
    skill_reward: Dict[str, float] = None
    badge_reward: str = ""
    
    # 状态
    is_completed: bool = False
    is_claimed: bool = False
    
    def __post_init__(self):
        if self.skill_reward is None:
            self.skill_reward = {}

class HeroJourneyGameFi:
    """孙悟空英雄之旅 GameFi 系统"""
    
    def __init__(self):
        self.logger = logging.getLogger('HeroJourneyGameFi')
        
        # 初始化等级系统
        self.levels = HERO_LEVELS
        
        # 初始化任务系统
        self.quests = self._initialize_quests()
        
        # 玩家数据存储（实际应该用数据库）
        self.players: Dict[str, PlayerStats] = {}
    
    def _initialize_quests(self) -> Dict[str, QuestTask]:
        """初始化任务系统"""
        quests = {}
        
        # 春季任务
        quests["spring_1"] = QuestTask(
            task_id="spring_1",
            name="初入花果山",
            description="完成第一次市场预测，无论对错",
            level_requirement=1,
            season=Season.SPRING,
            target_type="prediction",
            target_value=1,
            exp_reward=50,
            skill_reward={"market_insight": 0.1},
            badge_reward="新手猴王"
        )
        
        quests["spring_2"] = QuestTask(
            task_id="spring_2", 
            name="生死一线",
            description="体验第一次重大亏损（>10%）",
            level_requirement=2,
            season=Season.SPRING,
            target_type="drawdown",
            target_value=0.1,
            exp_reward=100,
            skill_reward={"emotional_control": 0.2},
            badge_reward="见过生死"
        )
        
        quests["spring_3"] = QuestTask(
            task_id="spring_3",
            name="拜师学艺", 
            description="参与10次稷下学宫辩论",
            level_requirement=3,
            season=Season.SPRING,
            target_type="debate",
            target_value=10,
            exp_reward=150,
            skill_reward={"prediction_accuracy": 0.15, "market_insight": 0.15},
            badge_reward="稷下学宫弟子"
        )
        
        # 夏季任务
        quests["summer_1"] = QuestTask(
            task_id="summer_1",
            name="初尝甜头",
            description="达到60%预测准确率",
            level_requirement=4,
            season=Season.SUMMER,
            target_type="accuracy",
            target_value=0.6,
            exp_reward=200,
            skill_reward={"prediction_accuracy": 0.2},
            badge_reward="小有所成"
        )
        
        quests["summer_2"] = QuestTask(
            task_id="summer_2",
            name="傲视群雄",
            description="连续获利超过50%，开始炫耀技能",
            level_requirement=5,
            season=Season.SUMMER,
            target_type="profit",
            target_value=0.5,
            exp_reward=300,
            skill_reward={"market_insight": 0.25},
            badge_reward="傲气冲天"
        )

        quests["summer_3"] = QuestTask(
            task_id="summer_3",
            name="逐出师门",
            description="因炫耀而触犯师门禁忌，被菩提祖师驱逐",
            level_requirement=6,
            season=Season.SUMMER,
            target_type="pride_punishment",
            target_value=1,
            exp_reward=200,
            skill_reward={"emotional_control": 0.3, "risk_management": 0.2},
            badge_reward="逐师门"
        )
        
        # 秋季任务
        quests["autumn_1"] = QuestTask(
            task_id="autumn_1",
            name="招安诱惑",
            description="尝试跟随传统金融建议5次",
            level_requirement=7,
            season=Season.AUTUMN,
            target_type="traditional_follow",
            target_value=5,
            exp_reward=250,
            skill_reward={"risk_management": 0.2},
            badge_reward="体制内猴"
        )
        
        # 冬季任务
        quests["winter_1"] = QuestTask(
            task_id="winter_1",
            name="跌入深渊",
            description="经历最大回撤超过30%",
            level_requirement=10,
            season=Season.WINTER,
            target_type="max_drawdown",
            target_value=0.3,
            exp_reward=500,
            skill_reward={"emotional_control": 0.5, "risk_management": 0.3},
            badge_reward="浴火重生"
        )
        
        quests["winter_2"] = QuestTask(
            task_id="winter_2",
            name="火眼金睛",
            description="在八卦炉中达到85%预测准确率",
            level_requirement=11,
            season=Season.WINTER,
            target_type="accuracy",
            target_value=0.85,
            exp_reward=800,
            skill_reward={"prediction_accuracy": 0.4, "market_insight": 0.4},
            badge_reward="火眼金睛"
        )
        
        quests["winter_3"] = QuestTask(
            task_id="winter_3",
            name="齐天大圣",
            description="成为太公心易大师，所有能力>0.9",
            level_requirement=12,
            season=Season.WINTER,
            target_type="mastery",
            target_value=0.9,
            exp_reward=1000,
            skill_reward={},
            badge_reward="齐天大圣"
        )
        
        return quests
    
    def create_player(self, player_id: str) -> PlayerStats:
        """创建新玩家"""
        if player_id not in self.players:
            self.players[player_id] = PlayerStats()
            self.logger.info(f"🐒 新玩家 {player_id} 踏上英雄之旅！")
        return self.players[player_id]
    
    def get_player_level_info(self, player_id: str) -> Dict[str, Any]:
        """获取玩家等级信息"""
        if player_id not in self.players:
            return {"error": "玩家不存在"}
        
        player = self.players[player_id]
        current_level = self.levels[player.level]
        
        return {
            "player_id": player_id,
            "level": player.level,
            "level_name": current_level.name,
            "stage": current_level.stage,
            "season": current_level.season.value,
            "description": current_level.description,
            "experience": player.experience,
            "next_level_exp": player.next_level_exp,
            "progress": player.experience / player.next_level_exp,
            "stats": {
                "prediction_accuracy": player.prediction_accuracy,
                "risk_management": player.risk_management,
                "market_insight": player.market_insight,
                "emotional_control": player.emotional_control
            },
            "achievements": player.achievements,
            "badges": player.badges
        }
    
    def update_player_performance(self, player_id: str, prediction_result: Dict[str, Any]) -> Dict[str, Any]:
        """更新玩家表现"""
        if player_id not in self.players:
            return {"error": "玩家不存在"}
        
        player = self.players[player_id]
        
        # 更新统计数据
        player.total_predictions += 1
        if prediction_result.get('is_correct', False):
            player.correct_predictions += 1
        
        # 更新准确率
        player.prediction_accuracy = player.correct_predictions / player.total_predictions
        
        # 更新收益
        profit = prediction_result.get('profit', 0.0)
        player.total_profit += profit
        
        # 更新最大回撤
        drawdown = prediction_result.get('drawdown', 0.0)
        if drawdown > player.max_drawdown:
            player.max_drawdown = drawdown
        
        # 检查任务进度
        completed_quests = self._check_quest_progress(player_id, prediction_result)
        
        # 计算经验奖励
        exp_gained = self._calculate_exp_reward(prediction_result)
        player.experience += exp_gained
        
        # 检查升级
        level_up = self._check_level_up(player_id)
        
        return {
            "exp_gained": exp_gained,
            "level_up": level_up,
            "completed_quests": completed_quests,
            "current_stats": self.get_player_level_info(player_id)
        }
    
    def _calculate_exp_reward(self, prediction_result: Dict[str, Any]) -> int:
        """计算经验奖励"""
        base_exp = 10
        
        # 准确预测奖励
        if prediction_result.get('is_correct', False):
            base_exp += 20
        
        # 收益奖励
        profit = prediction_result.get('profit', 0.0)
        if profit > 0:
            base_exp += int(profit * 100)
        
        # 风险管理奖励
        if prediction_result.get('used_stop_loss', False):
            base_exp += 15
        
        return base_exp
    
    def _check_quest_progress(self, player_id: str, result: Dict[str, Any]) -> List[str]:
        """检查任务进度"""
        completed_quests = []
        player = self.players[player_id]
        
        for quest_id, quest in self.quests.items():
            if quest.is_completed or quest.level_requirement > player.level:
                continue
            
            # 根据任务类型更新进度
            if quest.target_type == "prediction":
                quest.current_progress += 1
            elif quest.target_type == "accuracy":
                quest.current_progress = player.prediction_accuracy
            elif quest.target_type == "profit":
                quest.current_progress = player.total_profit
            elif quest.target_type == "drawdown" and result.get('drawdown', 0) >= quest.target_value:
                quest.current_progress = result.get('drawdown', 0)
            
            # 检查任务完成
            if quest.current_progress >= quest.target_value:
                quest.is_completed = True
                completed_quests.append(quest_id)
                
                # 发放奖励
                player.experience += quest.exp_reward
                for skill, bonus in quest.skill_reward.items():
                    current_value = getattr(player, skill, 0.0)
                    setattr(player, skill, min(1.0, current_value + bonus))
                
                if quest.badge_reward:
                    player.badges.append(quest.badge_reward)
                
                self.logger.info(f"🎉 玩家 {player_id} 完成任务: {quest.name}")
        
        return completed_quests
    
    def _check_level_up(self, player_id: str) -> Optional[Dict[str, Any]]:
        """检查升级"""
        player = self.players[player_id]
        
        if player.experience >= player.next_level_exp and player.level < 12:
            old_level = player.level
            player.level += 1
            player.experience -= player.next_level_exp
            player.next_level_exp = int(player.next_level_exp * 1.5)  # 升级所需经验递增
            
            new_level_info = self.levels[player.level]
            
            self.logger.info(f"🎊 玩家 {player_id} 升级到 {new_level_info.name}！")
            
            return {
                "old_level": old_level,
                "new_level": player.level,
                "level_name": new_level_info.name,
                "stage": new_level_info.stage,
                "season": new_level_info.season.value,
                "description": new_level_info.description
            }
        
        return None
    
    def get_leaderboard(self, season: Optional[Season] = None) -> List[Dict[str, Any]]:
        """获取排行榜"""
        players_data = []
        
        for player_id, player in self.players.items():
            if season and self.levels[player.level].season != season:
                continue
            
            players_data.append({
                "player_id": player_id,
                "level": player.level,
                "level_name": self.levels[player.level].name,
                "season": self.levels[player.level].season.value,
                "prediction_accuracy": player.prediction_accuracy,
                "total_profit": player.total_profit,
                "badges": len(player.badges),
                "score": self._calculate_player_score(player)
            })
        
        # 按综合得分排序
        players_data.sort(key=lambda x: x["score"], reverse=True)
        
        return players_data[:50]  # 返回前50名
    
    def _calculate_player_score(self, player: PlayerStats) -> float:
        """计算玩家综合得分"""
        return (
            player.level * 100 +
            player.prediction_accuracy * 50 +
            min(player.total_profit, 2.0) * 25 +  # 收益率上限2倍
            (1 - player.max_drawdown) * 25 +
            len(player.badges) * 10
        )

# 使用示例
async def demo_hero_journey():
    """演示英雄之旅系统"""
    game = HeroJourneyGameFi()
    
    # 创建玩家
    player_id = "monkey_king_001"
    game.create_player(player_id)
    
    print("🐒 孙悟空英雄之旅开始！")
    print("="*60)
    
    # 模拟游戏进程
    scenarios = [
        {"is_correct": False, "profit": -0.05, "drawdown": 0.05, "used_stop_loss": True},
        {"is_correct": True, "profit": 0.08, "drawdown": 0.0, "used_stop_loss": False},
        {"is_correct": True, "profit": 0.12, "drawdown": 0.0, "used_stop_loss": False},
        {"is_correct": False, "profit": -0.15, "drawdown": 0.15, "used_stop_loss": True},
        {"is_correct": True, "profit": 0.20, "drawdown": 0.0, "used_stop_loss": False},
    ]
    
    for i, scenario in enumerate(scenarios, 1):
        print(f"\n第{i}次预测结果:")
        result = game.update_player_performance(player_id, scenario)
        
        if result["level_up"]:
            print(f"🎊 升级！{result['level_up']['level_name']} - {result['level_up']['description']}")
        
        if result["completed_quests"]:
            print(f"🎉 完成任务: {', '.join(result['completed_quests'])}")
        
        print(f"📊 当前状态: 等级{result['current_stats']['level']} - {result['current_stats']['level_name']}")
    
    # 显示最终状态
    final_info = game.get_player_level_info(player_id)
    print(f"\n🏆 最终状态:")
    print(f"等级: {final_info['level']} - {final_info['level_name']}")
    print(f"阶段: {final_info['stage']} ({final_info['season']})")
    print(f"徽章: {', '.join(final_info['badges'])}")

if __name__ == "__main__":
    asyncio.run(demo_hero_journey())
