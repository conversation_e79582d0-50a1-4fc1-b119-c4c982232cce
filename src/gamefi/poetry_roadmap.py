#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
太公心易 GameFi 诗词路线图
将西游记经典诗词与十二等级修仙之路完美结合

作者：太公心易系统
版本：v1.0 Poetry Edition
"""

from typing import Dict, List, Tuple
from dataclasses import dataclass

@dataclass
class PoetryMapping:
    """诗词映射"""
    level: int
    level_name: str
    poetry_line: str
    interpretation: str
    season: str

class GameFiPoetryRoadmap:
    """GameFi诗词路线图"""
    
    def __init__(self):
        self.opening_poem = """混沌未分天地乱，茫茫渺渺无人见。
自从盘古破鸿蒙，开辟从兹清浊辨。
覆载群生仰至仁，发明万物皆为善。
欲知造化会元功，须看西游释厄传。"""
        
        self.linjiang_xian = """水帘洞内见生死，舢板入海求道。
得偿所望傲气扬，斜月三星洞，黄粱梦一场。

诏安饮马银河畔，仙桃玉液入嗓。
金銮踏破终被擒，八卦炉中炼，五行山下吟。"""
        
        # 诗词与等级的精确映射
        self.poetry_mappings = [
            # 春季篇：觉醒之路
            PoetryMapping(1, "花果山", "水帘洞内", "在花果山水帘洞中，初入投资世界", "春"),
            PoetryMapping(2, "见生死", "见生死", "体验市场的生死轮回，第一次重大亏损", "春"),
            PoetryMapping(3, "求大道", "舢板入海求道", "如小舟入海，踏上求道修仙之路", "春"),
            
            # 夏季篇：得道之路
            PoetryMapping(4, "得所望", "得偿所望", "首次成功预测，得偿所望建立信心", "夏"),
            PoetryMapping(5, "傲气扬", "傲气扬", "连续成功后信心爆棚，开始傲气冲天", "夏"),
            PoetryMapping(6, "逐师门", "黄粱梦一场", "在斜月三星洞的修行如黄粱一梦，被逐出师门", "夏"),
            
            # 秋季篇：试炼之路
            PoetryMapping(7, "受招安", "诏安饮马银河畔", "接受招安，在银河畔饮马，融入体制", "秋"),
            PoetryMapping(8, "喝玉液", "仙桃玉液入嗓", "沉迷于仙桃玉液，迷醉于短期收益", "秋"),
            PoetryMapping(9, "砸金銮", "金銮踏破", "踏破金銮殿，反叛权威和传统", "秋"),
            
            # 冬季篇：涅槃之路
            PoetryMapping(10, "终被擒", "终被擒", "反叛失败，重大失误，跌入谷底", "冬"),
            PoetryMapping(11, "八卦炉", "八卦炉中炼", "在八卦炉中淬炼，痛苦中重新学习", "冬"),
            PoetryMapping(12, "五行山", "五行山下吟", "在五行山下吟诗，达到最高修行境界", "冬")
        ]
    
    def get_poetry_for_level(self, level: int) -> PoetryMapping:
        """获取指定等级的诗词映射"""
        for mapping in self.poetry_mappings:
            if mapping.level == level:
                return mapping
        return None
    
    def get_season_poetry(self, season: str) -> List[PoetryMapping]:
        """获取指定季节的所有诗词"""
        return [mapping for mapping in self.poetry_mappings if mapping.season == season]
    
    def get_complete_roadmap(self) -> Dict[str, any]:
        """获取完整的诗词路线图"""
        return {
            "opening_poem": self.opening_poem,
            "linjiang_xian": self.linjiang_xian,
            "mappings": self.poetry_mappings,
            "seasons": {
                "春": self.get_season_poetry("春"),
                "夏": self.get_season_poetry("夏"), 
                "秋": self.get_season_poetry("秋"),
                "冬": self.get_season_poetry("冬")
            }
        }
    
    def generate_level_poem(self, level: int) -> str:
        """为指定等级生成诗词展示"""
        mapping = self.get_poetry_for_level(level)
        if not mapping:
            return ""
        
        season_emoji = {
            "春": "🌸",
            "夏": "☀️", 
            "秋": "🍂",
            "冬": "❄️"
        }
        
        return f"""
{season_emoji[mapping.season]} **第{mapping.level}级：{mapping.level_name}**

*"{mapping.poetry_line}"*

{mapping.interpretation}
        """
    
    def generate_full_poetry_display(self) -> str:
        """生成完整的诗词展示"""
        return f"""
## 📜 西游记开篇

```
{self.opening_poem}
```

## 🎵 临江仙·山下吟

```
{self.linjiang_xian}
```

### 🗺️ 诗词路线图解析

#### 🌸 春季篇：觉醒之路
- **水帘洞内见生死** → 花果山新手村，初见市场生死
- **舢板入海求道** → 踏上修仙求道之路

#### ☀️ 夏季篇：得道之路  
- **得偿所望傲气扬** → 初尝成功，信心爆棚
- **斜月三星洞，黄粱梦一场** → 菩提祖师道场，修行如梦

#### 🍂 秋季篇：试炼之路
- **诏安饮马银河畔** → 接受招安，融入体制
- **仙桃玉液入嗓** → 沉迷短期收益

#### ❄️ 冬季篇：涅槃之路
- **金銮踏破终被擒** → 反叛失败，跌入谷底
- **八卦炉中炼，五行山下吟** → 淬炼重生，达到大成

**这就是从傻逼到牛逼的完整修仙路线图！** 🏛️✨
        """

# 使用示例
def demo_poetry_roadmap():
    """演示诗词路线图"""
    roadmap = GameFiPoetryRoadmap()
    
    print("🎵 太公心易 GameFi 诗词路线图")
    print("="*60)
    
    # 显示开篇诗
    print("\n📜 西游记开篇:")
    print(roadmap.opening_poem)
    
    print("\n🎵 临江仙·山下吟:")
    print(roadmap.linjiang_xian)
    
    # 显示各等级对应的诗句
    print("\n🗺️ 十二等级诗词映射:")
    for mapping in roadmap.poetry_mappings:
        print(f"\n{mapping.season}季 第{mapping.level}级 {mapping.level_name}:")
        print(f"  诗句: 「{mapping.poetry_line}」")
        print(f"  释义: {mapping.interpretation}")
    
    # 显示特定等级的诗词
    print("\n" + "="*60)
    print("🔮 第6级诗词展示:")
    print(roadmap.generate_level_poem(6))

if __name__ == "__main__":
    demo_poetry_roadmap()
