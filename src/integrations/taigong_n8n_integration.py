
# 太公心易 → N8N Webhook 集成代码
import requests
import json
from datetime import datetime

class TaiGongXinYiN8NIntegration:
    def __init__(self):
        self.webhook_url = "https://houzhongxu-n8n-free.hf.space/webhook-test/ce40f698-832e-475a-a3c7-0895c9e2e90b"
    
    def send_rss_analysis(self, rss_data):
        """发送RSS分析数据到N8N"""
        payload = {
            "timestamp": datetime.now().isoformat(),
            "source": "太公心易RSS分析",
            "data": rss_data,
            "system": "太公心易"
        }
        
        try:
            response = requests.post(
                self.webhook_url,
                json=payload,
                timeout=30,
                headers={'Content-Type': 'application/json'}
            )
            return response.status_code == 200
        except Exception as e:
            print(f"N8N推送失败: {e}")
            return False
    
    def send_market_analysis(self, market_data):
        """发送市场分析数据到N8N"""
        payload = {
            "timestamp": datetime.now().isoformat(),
            "source": "太公心易市场分析",
            "analysis_type": "market_sentiment",
            "data": market_data
        }
        
        response = requests.post(self.webhook_url, json=payload)
        return response.status_code == 200

# 使用示例
n8n_integration = TaiGongXinYiN8NIntegration()

# 发送RSS数据
rss_data = {
    "title": "科技股强势上涨",
    "content": "市场分析内容...",
    "sentiment": "positive"
}
success = n8n_integration.send_rss_analysis(rss_data)
print("RSS数据推送:", "成功" if success else "失败")
