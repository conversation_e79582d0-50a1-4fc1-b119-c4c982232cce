#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
太公心易BI系统 - 统一启动脚本
集成管理炼股葫芦和八仙论道两个子系统
"""

import os
import sys
import subprocess
import argparse
import time
from pathlib import Path
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

class SystemManager:
    """系统管理器"""
    
    def __init__(self):
        self.base_dir = Path(__file__).parent
        self.jixia_dir = self.base_dir.parent.parent / "jixia_academy_clean"
        self.processes = []
        self.integrated_mode = True  # 集成模式：八仙论道作为分析引擎
        
    def check_dependencies(self):
        """检查依赖是否安装"""
        print("🔍 检查系统依赖...")
        
        required_packages = [
            'streamlit', 'fastapi',
            'autogen_agentchat', 'openai', 'pandas', 'numpy'
        ]
        
        missing_packages = []
        for package in required_packages:
            try:
                __import__(package)
                print(f"✅ {package} - 已安装")
            except ImportError:
                missing_packages.append(package)
                print(f"❌ {package} - 未安装")
        
        if missing_packages:
            print(f"\n⚠️  缺少依赖包: {', '.join(missing_packages)}")
            print("请运行: pip install -r requirements.txt")
            return False
        
        print("✅ 所有依赖检查通过")
        return True
    
    def start_cauldron(self):
        """启动炼妖壶系统（集成稷下学宫分析引擎）"""
        print("🚀 启动炼妖壶 (Cauldron) 系统...")
        
        # 启动 Streamlit 应用
        ui_file = self.base_dir.parent.parent / "streamlit_app.py"
        if ui_file.exists():
            cmd = [sys.executable, "-m", "streamlit", "run", str(ui_file), "--server.port", "8501"]
        else:
            print(f"❌ UI 入口文件未找到: {ui_file}")
            return None
        
        process = subprocess.Popen(cmd, cwd=self.base_dir)
        self.processes.append(("太公心易BI系统", process))
        print("✅ 太公心易BI系统已启动 (端口: 8501)")
        print("📊 炼股葫芦数据引擎已集成")
        print("🎭 八仙论道AI分析引擎已集成")
        return process
    
    def start_data_pipeline(self):
        """启动数据管道（定时分析任务）"""
        if not os.getenv('ANALYSIS_ENGINE_ENABLED', 'True').lower() == 'true':
            print("⏭️  分析引擎已禁用")
            return None
            
        print("🔄 启动数据分析管道...")
        
        # 启动定时分析任务
        pipeline_script = self.base_dir / "run_daily_analysis.py"
        if pipeline_script.exists():
            cmd = [sys.executable, str(pipeline_script), "--schedule"]
            
            process = subprocess.Popen(cmd, cwd=self.base_dir)
            self.processes.append(("数据分析管道", process))
            print("✅ 数据分析管道已启动（定时任务模式）")
            return process
        else:
            print("❌ 数据管道脚本不存在")
            return None
    
    def start_jixia_academy(self):
        """启动八仙论道独立界面（可选）"""
        if self.integrated_mode:
            print("🎭 八仙论道已集成到主系统，无需独立启动")
            return None
            
        if not os.getenv('JIXIA_ACADEMY_ENABLED', 'True').lower() == 'true':
            print("⏭️  八仙论道独立模式已禁用")
            return None
            
        print("🎭 启动八仙论道独立界面...")
        
        if not self.jixia_dir.exists():
            print("❌ 八仙论道目录不存在")
            return None
        
        # 启动Chainlit应用
        chainlit_app = self.jixia_dir / "chainlit_debate_app.py"
        if chainlit_app.exists():
            port = os.getenv('CHAINLIT_PORT', '8000')
            host = os.getenv('CHAINLIT_HOST', '0.0.0.0')
            
            cmd = [
                sys.executable, "-m", "chainlit", "run", 
                str(chainlit_app),
                "--host", host,
                "--port", port
            ]
            
            process = subprocess.Popen(cmd, cwd=self.jixia_dir)
            self.processes.append(("八仙论道独立界面", process))
            print(f"✅ 八仙论道独立界面已启动 (端口: {port})")
            return process
        else:
            print("❌ 八仙论道应用文件不存在")
            return None
    
    def start_all(self):
        """启动所有系统"""
        print("🚀 启动太公心易BI系统（集成模式）...")
        print("=" * 50)
        
        if not self.check_dependencies():
            print("❌ 依赖检查失败，请安装缺失的包")
            return False
        
        try:
            # 启动主系统（炼股葫芦 + 八仙论道集成）
            main_process = self.start_cauldron()
            time.sleep(3)  # 等待启动
            
            # 启动数据分析管道
            pipeline_process = self.start_data_pipeline()
            time.sleep(2)  # 等待启动
            
            # 可选：启动八仙论道独立界面
            jixia_process = self.start_jixia_academy()
            if jixia_process:
                time.sleep(2)  # 等待启动
            
            print("\n" + "=" * 50)
            print("✅ 太公心易BI系统启动完成！")
            print("\n📊 访问地址:")
            print("   🏛️  主系统（晨会简报）: http://localhost:8501")
            print("   📊 炼股葫芦数据引擎: 已集成")
            print("   🎭 八仙论道AI分析: 已集成")
            if jixia_process:
                jixia_port = os.getenv('CHAINLIT_PORT', '8000')
                print(f"   🎭 八仙论道独立界面: http://localhost:{jixia_port}")
            
            print("\n🔄 系统功能:")
            print("   📋 每日投资晨会报告")
            print("   🎯 AI智能投资建议")
            print("   👥 会员分级服务")
            print("   📈 历史报告查询")
            print("   🔄 手动股票分析")
            
            print("\n⚠️  按 Ctrl+C 停止所有服务")
            print("=" * 50)
            
            return True
            
        except Exception as e:
            print(f"❌ 启动失败: {e}")
            self.stop_all()
            return False
    
    def stop_all(self):
        """停止所有系统"""
        print("\n🛑 正在停止所有服务...")
        
        for name, process in self.processes:
            try:
                process.terminate()
                process.wait(timeout=5)
                print(f"✅ {name} 已停止")
            except subprocess.TimeoutExpired:
                process.kill()
                print(f"⚠️  {name} 强制停止")
            except Exception as e:
                print(f"❌ 停止 {name} 时出错: {e}")
        
        print("👋 所有服务已停止")
    
    def show_status(self):
        """显示系统状态"""
        print("📊 系统状态:")
        print("=" * 30)
        
        for name, process in self.processes:
            if process.poll() is None:
                print(f"✅ {name}: 运行中 (PID: {process.pid})")
            else:
                print(f"❌ {name}: 已停止")
        
        if not self.processes:
            print("⏸️  没有运行中的服务")

def main():
    """主函数"""
    print("🔧 太公心易BI系统启动器")
    print("=" * 30)
    
    parser = argparse.ArgumentParser(description='太公心易BI系统管理器')
    parser.add_argument('action', choices=['start', 'stop', 'status', 'cauldron', 'jixia'],
                       help='操作类型')
    
    args = parser.parse_args()
    manager = SystemManager()
    
    # 检查是否启用八仙论道系统
    if args.action == 'jixia':
        print("\n🎭 启动八仙论道系统...")
        print("\n💡 提示: 使用 'python src/scripts/start_system.py cauldron' 启动炼妖壶系统")
    elif args.action == 'cauldron':
        print("\n🚀 启动炼妖壶主程序...")
        print("\n💡 提示: 使用 'python start_system.py jixia' 启动八仙论道系统")
    
    try:
        if args.action == 'start':
            if manager.start_all():
                # 保持运行直到用户中断
                while True:
                    time.sleep(1)
        
        elif args.action == 'cauldron':
            print("🚀 仅启动炼妖壶系统...")
            if manager.check_dependencies():
                manager.start_cauldron()
                print("🔥 炼妖壶: http://localhost:8501")
                while True:
                    time.sleep(1)
        
        elif args.action == 'jixia':
            print("🎭 仅启动八仙论道系统...")
            if manager.check_dependencies():
                manager.start_jixia_academy()
                print("🎭 八仙论道: http://localhost:8000")
                while True:
                    time.sleep(1)
        
        elif args.action == 'status':
            manager.show_status()
        
        elif args.action == 'stop':
            manager.stop_all()
    
    except KeyboardInterrupt:
        manager.stop_all()
    except Exception as e:
        print(f"❌ 系统错误: {e}")
        manager.stop_all()

if __name__ == "__main__":
    main()