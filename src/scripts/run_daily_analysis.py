#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
太公心易BI系统 - 每日分析任务启动器
支持定时运行和手动触发的数据分析流程
"""

import asyncio
import argparse
import sys
import logging
from pathlib import Path
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from src.core.data_pipeline import DataPipeline
from src.core.member_system import MemberSystem
from src.core.config_manager import ConfigManager

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('daily_analysis.log')
        ]
    )
    return logging.getLogger('DailyAnalysis')

async def run_once():
    """运行一次分析"""
    logger = setup_logging()
    logger.info("🚀 开始执行每日分析任务")
    
    try:
        # 初始化系统
        pipeline = DataPipeline()
        member_system = MemberSystem()
        
        # 运行分析
        report = await pipeline.run_daily_analysis()
        
        # 保存到会员系统
        today = datetime.now().strftime('%Y-%m-%d')
        member_system.save_daily_report(today, report)
        
        logger.info("✅ 每日分析任务完成")
        print("📊 分析报告已生成并保存")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 每日分析任务失败: {e}")
        print(f"❌ 分析失败: {e}")
        return False

async def run_scheduled():
    """定时运行分析"""
    logger = setup_logging()
    logger.info("⏰ 启动定时分析任务")
    
    try:
        # 初始化系统
        pipeline = DataPipeline()
        config_manager = ConfigManager()
        config = config_manager.get_all_config()
        
        # 获取定时设置
        analysis_time = config['cauldron']['analysis_engine']['daily_analysis_time']
        symbols = config['cauldron']['analysis_engine']['default_symbols']
        
        logger.info(f"📅 定时分析设置: {analysis_time}, 股票: {symbols}")
        
        # 启动定时任务
        await pipeline.schedule_daily_analysis(symbols, analysis_time)
        
    except Exception as e:
        logger.error(f"❌ 定时任务启动失败: {e}")
        print(f"❌ 定时任务失败: {e}")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='太公心易BI系统 - 每日分析任务')
    parser.add_argument('--schedule', action='store_true', help='启动定时任务模式')
    parser.add_argument('--once', action='store_true', help='运行一次分析')
    parser.add_argument('--symbols', type=str, help='指定股票代码（逗号分隔）')
    
    args = parser.parse_args()
    
    print("🏛️ 太公心易BI系统 - 每日分析任务")
    print("=" * 50)
    
    if args.schedule:
        print("⏰ 启动定时分析模式...")
        asyncio.run(run_scheduled())
    elif args.once:
        print("🚀 执行单次分析...")
        success = asyncio.run(run_once())
        sys.exit(0 if success else 1)
    else:
        # 默认运行一次
        print("🚀 执行默认分析...")
        success = asyncio.run(run_once())
        sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()