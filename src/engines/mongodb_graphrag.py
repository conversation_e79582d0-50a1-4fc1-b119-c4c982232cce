#!/usr/bin/env python3
"""
MongoDB GraphRAG 集成系统
连接MongoDB Atlas，构建本地知识图谱，支持智能问答
"""

import sqlite3
import re
import json
import hashlib
import os
from datetime import datetime
from typing import List, Dict, Any, Optional
import pymongo
from pymongo import MongoClient
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class MongoDBGraphRAG:
    def __init__(self, mongo_uri: str, db_path: str = "mongodb_graphrag.db"):
        """
        初始化MongoDB GraphRAG系统
        
        Args:
            mongo_uri: MongoDB连接字符串
            db_path: 本地SQLite数据库路径
        """
        self.mongo_uri = mongo_uri
        self.db_path = db_path
        self.mongo_client = None
        self.mongo_db = None
        self.mongo_collection = None
        
        # 初始化MongoDB连接
        self._init_mongodb()
        
        # 初始化本地SQLite数据库
        self._init_local_db()
    
    def _init_mongodb(self):
        """初始化MongoDB连接"""
        try:
            self.mongo_client = MongoClient(self.mongo_uri)
            # 从URI中提取数据库名
            if 'cauldron_rss' in self.mongo_uri:
                self.mongo_db = self.mongo_client['cauldron_rss']
                self.mongo_collection = self.mongo_db['articles']
            else:
                # 默认数据库
                self.mongo_db = self.mongo_client['test']
                self.mongo_collection = self.mongo_db['documents']
            
            # 测试连接
            self.mongo_client.admin.command('ping')
            logger.info(f"MongoDB连接成功: {self.mongo_db.name}")
            
        except Exception as e:
            logger.error(f"MongoDB连接失败: {e}")
            raise
    
    def _init_local_db(self):
        """初始化本地SQLite数据库"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # 创建文档表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS documents (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                mongo_id TEXT UNIQUE,
                title TEXT NOT NULL,
                content TEXT NOT NULL,
                source TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # 创建实体表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS entities (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                type TEXT NOT NULL,
                description TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(name, type)
            )
        ''')
        
        # 创建关系表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS relations (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                source_entity TEXT NOT NULL,
                relation_type TEXT NOT NULL,
                target_entity TEXT NOT NULL,
                confidence REAL DEFAULT 1.0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(source_entity, relation_type, target_entity)
            )
        ''')
        
        # 创建文档-实体关联表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS document_entities (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                document_id INTEGER,
                entity_id INTEGER,
                FOREIGN KEY (document_id) REFERENCES documents (id),
                FOREIGN KEY (entity_id) REFERENCES entities (id),
                UNIQUE(document_id, entity_id)
            )
        ''')
        
        # 创建索引
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_documents_mongo_id ON documents(mongo_id)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_entities_name ON entities(name)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_relations_source ON relations(source_entity)')
        
        conn.commit()
        conn.close()
        logger.info("本地SQLite数据库初始化完成")
    
    def get_mongodb_stats(self) -> Dict[str, Any]:
        """获取MongoDB统计信息"""
        try:
            total_docs = self.mongo_collection.count_documents({})
            
            # 获取一些示例文档
            sample_docs = list(self.mongo_collection.find().limit(3))
            
            return {
                'total_documents': total_docs,
                'database_name': self.mongo_db.name,
                'collection_name': self.mongo_collection.name,
                'sample_documents': sample_docs
            }
        except Exception as e:
            logger.error(f"获取MongoDB统计信息失败: {e}")
            return {'error': str(e)}
    
    def extract_entities(self, text: str) -> List[Dict[str, str]]:
        """
        从文本中提取实体
        这是一个简化的实体提取器，实际应用中可以使用更复杂的NLP模型
        """
        entities = []
        
        # 金融相关实体模式
        patterns = {
            'COMPANY': [
                r'[A-Z][a-zA-Z]*(?:\s+[A-Z][a-zA-Z]*)*(?:\s+(?:Inc|Corp|Ltd|LLC|Co|Company|Corporation|Limited)\.?)',
                r'(?:苹果|微软|谷歌|亚马逊|特斯拉|阿里巴巴|腾讯|百度|京东|美团|字节跳动|小米|华为|比亚迪)(?:公司)?',
                r'[A-Z]{2,}(?:\s+[A-Z]{2,})*'  # 大写缩写
            ],
            'PERSON': [
                r'(?:马斯克|巴菲特|贝索斯|库克|马云|马化腾|张一鸣|雷军|任正非|王传福)',
                r'[A-Z][a-z]+\s+[A-Z][a-z]+(?:\s+[A-Z][a-z]+)?'  # 英文姓名
            ],
            'MONEY': [
                r'\$[\d,]+(?:\.\d{2})?(?:\s*(?:million|billion|trillion|万|亿))?',
                r'[\d,]+(?:\.\d{2})?\s*(?:美元|人民币|元|万元|亿元)',
                r'[\d,]+(?:\.\d{2})?\s*(?:million|billion|trillion)\s*(?:dollars|yuan)'
            ],
            'CONCEPT': [
                r'(?:人工智能|机器学习|深度学习|神经网络|区块链|加密货币|比特币|以太坊|NFT|元宇宙|Web3)',
                r'(?:电动汽车|自动驾驶|新能源|太阳能|风能|锂电池|芯片|半导体|5G|6G|物联网|云计算)',
                r'(?:股票|股价|市值|IPO|上市|融资|投资|并购|收购|估值|财报|营收|利润|亏损)'
            ]
        }
        
        for entity_type, pattern_list in patterns.items():
            for pattern in pattern_list:
                matches = re.finditer(pattern, text, re.IGNORECASE)
                for match in matches:
                    entity_text = match.group().strip()
                    if len(entity_text) > 1:  # 过滤太短的匹配
                        entities.append({
                            'name': entity_text,
                            'type': entity_type,
                            'description': f"{entity_type}: {entity_text}"
                        })
        
        # 去重
        seen = set()
        unique_entities = []
        for entity in entities:
            key = (entity['name'].lower(), entity['type'])
            if key not in seen:
                seen.add(key)
                unique_entities.append(entity)
        
        return unique_entities
    
    def extract_relations(self, text: str, entities: List[Dict[str, str]]) -> List[Dict[str, str]]:
        """
        从文本和实体中提取关系
        """
        relations = []
        entity_names = [e['name'] for e in entities]
        
        # 简单的关系模式
        relation_patterns = [
            (r'(.+?)\s+(?:收购|并购|买下)\s+(.+)', 'ACQUIRES'),
            (r'(.+?)\s+(?:投资|注资)\s+(.+)', 'INVESTS_IN'),
            (r'(.+?)\s+(?:是|为)\s+(.+?)(?:的|之)', 'IS_A'),
            (r'(.+?)\s+(?:拥有|持有)\s+(.+)', 'OWNS'),
            (r'(.+?)\s+(?:合作|合作伙伴)\s+(.+)', 'PARTNERS_WITH'),
            (r'(.+?)\s+(?:竞争|竞争对手)\s+(.+)', 'COMPETES_WITH')
        ]
        
        for pattern, relation_type in relation_patterns:
            matches = re.finditer(pattern, text, re.IGNORECASE)
            for match in matches:
                source = match.group(1).strip()
                target = match.group(2).strip()
                
                # 检查是否匹配已提取的实体
                source_match = self._find_entity_match(source, entity_names)
                target_match = self._find_entity_match(target, entity_names)
                
                if source_match and target_match and source_match != target_match:
                    relations.append({
                        'source': source_match,
                        'type': relation_type,
                        'target': target_match
                    })
        
        return relations
    
    def _find_entity_match(self, text: str, entity_names: List[str]) -> Optional[str]:
        """在实体列表中查找匹配的实体"""
        text_lower = text.lower()
        for entity_name in entity_names:
            if entity_name.lower() in text_lower or text_lower in entity_name.lower():
                return entity_name
        return None

    def add_document_to_graph(self, mongo_id: str, title: str, content: str, source: str = "mongodb") -> int:
        """将文档添加到知识图谱"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        try:
            # 检查文档是否已存在
            cursor.execute("SELECT id FROM documents WHERE mongo_id = ?", (mongo_id,))
            existing = cursor.fetchone()
            if existing:
                logger.info(f"文档已存在: {mongo_id}")
                return existing[0]

            # 插入文档
            cursor.execute('''
                INSERT INTO documents (mongo_id, title, content, source)
                VALUES (?, ?, ?, ?)
            ''', (mongo_id, title, content, source))

            doc_id = cursor.lastrowid

            # 提取实体
            entities = self.extract_entities(content)
            logger.info(f"提取到 {len(entities)} 个实体")

            # 插入实体
            entity_ids = []
            for entity in entities:
                cursor.execute('''
                    INSERT OR IGNORE INTO entities (name, type, description)
                    VALUES (?, ?, ?)
                ''', (entity['name'], entity['type'], entity['description']))

                # 获取实体ID
                cursor.execute("SELECT id FROM entities WHERE name = ? AND type = ?",
                             (entity['name'], entity['type']))
                entity_id = cursor.fetchone()[0]
                entity_ids.append(entity_id)

                # 关联文档和实体
                cursor.execute('''
                    INSERT OR IGNORE INTO document_entities (document_id, entity_id)
                    VALUES (?, ?)
                ''', (doc_id, entity_id))

            # 提取关系
            relations = self.extract_relations(content, entities)
            logger.info(f"提取到 {len(relations)} 个关系")

            # 插入关系
            for relation in relations:
                cursor.execute('''
                    INSERT OR IGNORE INTO relations (source_entity, relation_type, target_entity)
                    VALUES (?, ?, ?)
                ''', (relation['source'], relation['type'], relation['target']))

            conn.commit()
            logger.info(f"文档处理完成: {title} (ID: {doc_id})")
            return doc_id

        except Exception as e:
            conn.rollback()
            logger.error(f"处理文档失败: {e}")
            raise
        finally:
            conn.close()

    def sync_from_mongodb(self, limit: int = 100, skip: int = 0) -> Dict[str, Any]:
        """从MongoDB同步数据到本地知识图谱"""
        try:
            logger.info(f"开始同步MongoDB数据，限制: {limit}, 跳过: {skip}")

            # 查询MongoDB文档
            cursor = self.mongo_collection.find().skip(skip).limit(limit)

            processed_count = 0
            error_count = 0

            for doc in cursor:
                try:
                    mongo_id = str(doc['_id'])
                    title = doc.get('title', '无标题')
                    content = doc.get('content', doc.get('description', ''))

                    if not content:
                        logger.warning(f"文档内容为空，跳过: {mongo_id}")
                        continue

                    # 添加到知识图谱
                    self.add_document_to_graph(mongo_id, title, content)
                    processed_count += 1

                    if processed_count % 10 == 0:
                        logger.info(f"已处理 {processed_count} 个文档")

                except Exception as e:
                    error_count += 1
                    logger.error(f"处理文档失败: {e}")
                    continue

            result = {
                'processed': processed_count,
                'errors': error_count,
                'total_requested': limit
            }

            logger.info(f"同步完成: {result}")
            return result

        except Exception as e:
            logger.error(f"同步失败: {e}")
            return {'error': str(e)}

    def search_documents(self, query: str, top_k: int = 5) -> List[Dict[str, Any]]:
        """搜索相关文档"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        # 简单的文本搜索
        cursor.execute('''
            SELECT id, title, content, source
            FROM documents
            WHERE title LIKE ? OR content LIKE ?
            ORDER BY
                CASE
                    WHEN title LIKE ? THEN 1
                    WHEN content LIKE ? THEN 2
                    ELSE 3
                END
            LIMIT ?
        ''', (f'%{query}%', f'%{query}%', f'%{query}%', f'%{query}%', top_k))

        results = []
        for row in cursor.fetchall():
            results.append({
                'id': row[0],
                'title': row[1],
                'content': row[2][:500],  # 限制内容长度
                'source': row[3]
            })

        conn.close()
        return results

    def search_entities(self, query: str, top_k: int = 5) -> List[Dict[str, Any]]:
        """搜索相关实体"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute('''
            SELECT name, type, description
            FROM entities
            WHERE name LIKE ? OR description LIKE ?
            ORDER BY
                CASE
                    WHEN name LIKE ? THEN 1
                    WHEN description LIKE ? THEN 2
                    ELSE 3
                END
            LIMIT ?
        ''', (f'%{query}%', f'%{query}%', f'%{query}%', f'%{query}%', top_k))

        results = []
        for row in cursor.fetchall():
            results.append({
                'name': row[0],
                'type': row[1],
                'description': row[2]
            })

        conn.close()
        return results

    def get_entity_relations(self, entity_name: str) -> List[Dict[str, str]]:
        """获取实体的关系"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute('''
            SELECT source_entity, relation_type, target_entity
            FROM relations
            WHERE source_entity = ? OR target_entity = ?
        ''', (entity_name, entity_name))

        results = []
        for row in cursor.fetchall():
            results.append({
                'source': row[0],
                'type': row[1],
                'target': row[2]
            })

        conn.close()
        return results

    def simple_llm_response(self, context: str, question: str) -> str:
        """
        简单的LLM响应生成器
        在实际应用中，这里应该调用真正的LLM API
        """
        # 基于关键词的简单响应
        question_lower = question.lower()
        context_lower = context.lower()

        if '什么是' in question_lower or 'what is' in question_lower:
            # 定义类问题
            if '人工智能' in question_lower or 'ai' in question_lower:
                return "根据知识图谱信息，人工智能是计算机科学的一个分支，机器学习是其重要的子领域。机器学习使计算机能够从数据中学习，而深度学习则是机器学习的一个分支，使用神经网络来模拟人脑的工作方式。"
            elif '机器学习' in question_lower:
                return "机器学习是人工智能的一个子领域，它使计算机能够从数据中学习而无需明确编程。深度学习是机器学习的一个重要分支。"

        elif '关系' in question_lower or 'relationship' in question_lower:
            # 关系类问题
            if '机器学习' in question_lower and '深度学习' in question_lower:
                return "机器学习是人工智能的一个子领域，它使计算机能够从数据中学习而无需明确编程。深度学习是机器学习的一个重要分支。"

        # 默认响应
        if context.strip():
            return f"根据知识图谱中的信息：{context[:200]}..."
        else:
            return "抱歉，我在知识图谱中没有找到相关信息来回答您的问题。"

    def query(self, question: str) -> str:
        """智能问答"""
        logger.info(f"正在处理问题: {question}")

        # 搜索相关文档
        relevant_docs = self.search_documents(question, top_k=3)

        # 搜索相关实体
        relevant_entities = self.search_entities(question, top_k=3)

        # 构建上下文
        context_parts = []

        # 添加文档上下文
        if relevant_docs:
            context_parts.append("相关文档：")
            for doc in relevant_docs:
                context_parts.append(f"- {doc['title']}: {doc['content'][:200]}...")

        # 添加实体上下文
        if relevant_entities:
            context_parts.append("\n相关实体：")
            for entity in relevant_entities:
                context_parts.append(f"- {entity['name']} ({entity['type']}): {entity['description']}")

                # 添加实体关系
                relations = self.get_entity_relations(entity['name'])
                if relations:
                    for rel in relations[:2]:  # 只显示前2个关系
                        context_parts.append(f"  关系: {rel['source']} -> {rel['type']} -> {rel['target']}")

        context = "\n".join(context_parts)

        # 使用简单的LLM响应
        response = self.simple_llm_response(context, question)

        return response

    def show_stats(self):
        """显示数据库统计信息"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute("SELECT COUNT(*) FROM documents")
        doc_count = cursor.fetchone()[0]

        cursor.execute("SELECT COUNT(*) FROM entities")
        entity_count = cursor.fetchone()[0]

        cursor.execute("SELECT COUNT(*) FROM relations")
        relation_count = cursor.fetchone()[0]

        conn.close()

        print(f"\n=== 知识图谱统计 ===")
        print(f"文档数量: {doc_count}")
        print(f"实体数量: {entity_count}")
        print(f"关系数量: {relation_count}")

        return {
            'documents': doc_count,
            'entities': entity_count,
            'relations': relation_count
        }
