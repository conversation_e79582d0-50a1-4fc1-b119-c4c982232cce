#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
炼妖壶AI v1.0 用户界面 - iPhone级别的革命性体验
5分钟上手，终身受益

设计理念：
1. 极简主义 - 一个输入框解决所有问题
2. 对话式交互 - 像和朋友聊天一样自然
3. 智能引导 - 系统主动帮助用户
4. 即时反馈 - 实时显示分析过程
5. 个性化体验 - 记住用户偏好

作者：太公心易BI系统
版本：v1.0 Revolutionary UI
"""

import streamlit as st
import asyncio
import json
from datetime import datetime
from typing import Dict, List, Any, Optional
import plotly.graph_objects as go
import plotly.express as px
from streamlit_chat import message
import time

# 导入核心AI引擎
from src.core.cauldron_ai_v1 import CauldronAI, CauldronResponse, QuestionType, ConfidenceLevel


class CauldronUIv1:
    """炼妖壶AI v1.0 用户界面"""
    
    def __init__(self):
        self.setup_page_config()
        self.initialize_session_state()
        self.setup_custom_css()
    
    def setup_page_config(self):
        """页面配置"""
        st.set_page_config(
            page_title="炼妖壶AI v1.0 - 革命性金融AI助手",
            page_icon="🔥",
            layout="wide",
            initial_sidebar_state="collapsed"  # 隐藏侧边栏，保持简洁
        )
    
    def initialize_session_state(self):
        """初始化会话状态"""
        if 'cauldron_ai' not in st.session_state:
            # 这里需要从环境变量获取API密钥
            api_key = st.secrets.get("OPENROUTER_API_KEY", "demo_key")
            st.session_state.cauldron_ai = CauldronAI(api_key)
        
        if 'messages' not in st.session_state:
            st.session_state.messages = []
        
        if 'user_profile' not in st.session_state:
            st.session_state.user_profile = {
                "name": "投资者",
                "experience_level": "新手",
                "risk_tolerance": "中等",
                "investment_goals": []
            }
        
        if 'onboarding_completed' not in st.session_state:
            st.session_state.onboarding_completed = False
    
    def setup_custom_css(self):
        """自定义CSS样式 - iPhone级别的视觉设计"""
        st.markdown("""
        <style>
        /* 主容器样式 */
        .main-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        
        /* 标题样式 */
        .main-title {
            text-align: center;
            font-size: 3rem;
            font-weight: bold;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 10px;
        }
        
        .subtitle {
            text-align: center;
            font-size: 1.2rem;
            color: #666;
            margin-bottom: 30px;
        }
        
        /* 聊天界面样式 */
        .chat-container {
            background: white;
            border-radius: 20px;
            padding: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        /* 输入框样式 */
        .stTextInput > div > div > input {
            border-radius: 25px;
            border: 2px solid #e0e0e0;
            padding: 15px 20px;
            font-size: 16px;
            transition: all 0.3s ease;
        }
        
        .stTextInput > div > div > input:focus {
            border-color: #4ecdc4;
            box-shadow: 0 0 10px rgba(78, 205, 196, 0.3);
        }
        
        /* 按钮样式 */
        .stButton > button {
            background: linear-gradient(45deg, #4ecdc4, #45b7d1);
            color: white;
            border: none;
            border-radius: 25px;
            padding: 12px 30px;
            font-size: 16px;
            font-weight: bold;
            transition: all 0.3s ease;
        }
        
        .stButton > button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        
        /* 消息气泡样式 */
        .user-message {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            padding: 15px 20px;
            border-radius: 20px 20px 5px 20px;
            margin: 10px 0;
            margin-left: 20%;
        }
        
        .ai-message {
            background: linear-gradient(45deg, #f093fb, #f5576c);
            color: white;
            padding: 15px 20px;
            border-radius: 20px 20px 20px 5px;
            margin: 10px 0;
            margin-right: 20%;
        }
        
        /* 置信度指示器 */
        .confidence-indicator {
            display: inline-block;
            padding: 5px 15px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: bold;
            margin-left: 10px;
        }
        
        .confidence-high {
            background: #4CAF50;
            color: white;
        }
        
        .confidence-medium {
            background: #FF9800;
            color: white;
        }
        
        .confidence-low {
            background: #f44336;
            color: white;
        }
        
        /* 快速问题按钮 */
        .quick-question {
            background: rgba(78, 205, 196, 0.1);
            border: 1px solid #4ecdc4;
            border-radius: 20px;
            padding: 10px 20px;
            margin: 5px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .quick-question:hover {
            background: #4ecdc4;
            color: white;
        }
        
        /* 加载动画 */
        .thinking-animation {
            text-align: center;
            padding: 20px;
        }
        
        .thinking-dots {
            display: inline-block;
            animation: thinking 1.5s infinite;
        }
        
        @keyframes thinking {
            0%, 20% { opacity: 0; }
            50% { opacity: 1; }
            80%, 100% { opacity: 0; }
        }
        </style>
        """, unsafe_allow_html=True)
    
    def render_header(self):
        """渲染页面头部"""
        st.markdown("""
        <div class="main-container">
            <h1 class="main-title">🔥 炼妖壶AI</h1>
            <p class="subtitle">革命性金融AI助手 - 一个问题，智能回答</p>
        </div>
        """, unsafe_allow_html=True)
    
    def render_onboarding(self):
        """新用户引导 - 5分钟快速上手"""
        st.markdown("### 🎯 欢迎使用炼妖壶AI！")
        st.markdown("让我们花2分钟了解您的投资需求，为您提供个性化服务。")
        
        with st.form("onboarding_form"):
            col1, col2 = st.columns(2)
            
            with col1:
                name = st.text_input("您的称呼", placeholder="例如：小明")
                experience = st.selectbox(
                    "投资经验",
                    ["新手", "有一些经验", "比较有经验", "专业投资者"]
                )
            
            with col2:
                risk_tolerance = st.selectbox(
                    "风险承受能力",
                    ["保守型", "稳健型", "平衡型", "积极型", "激进型"]
                )
                goals = st.multiselect(
                    "投资目标",
                    ["资产保值", "稳定收益", "长期增长", "短期获利", "学习投资"]
                )
            
            submitted = st.form_submit_button("开始使用炼妖壶AI 🚀")
            
            if submitted:
                st.session_state.user_profile.update({
                    "name": name or "投资者",
                    "experience_level": experience,
                    "risk_tolerance": risk_tolerance,
                    "investment_goals": goals
                })
                st.session_state.onboarding_completed = True
                st.rerun()
    
    def render_quick_questions(self):
        """渲染快速问题按钮"""
        st.markdown("### 💡 快速开始")
        
        quick_questions = [
            "现在适合买股票吗？",
            "苹果股票怎么样？",
            "今天的市场新闻有什么影响？",
            "我应该如何分散投资？",
            "什么是价值投资？",
            "如何控制投资风险？"
        ]
        
        cols = st.columns(3)
        for i, question in enumerate(quick_questions):
            with cols[i % 3]:
                if st.button(question, key=f"quick_{i}"):
                    self.handle_user_input(question)
    
    def render_chat_interface(self):
        """渲染聊天界面"""
        st.markdown("### 💬 智能对话")
        
        # 显示聊天历史
        chat_container = st.container()
        with chat_container:
            for i, msg in enumerate(st.session_state.messages):
                if msg["role"] == "user":
                    st.markdown(f"""
                    <div class="user-message">
                        <strong>您:</strong> {msg["content"]}
                    </div>
                    """, unsafe_allow_html=True)
                else:
                    confidence_class = self.get_confidence_class(msg.get("confidence", "medium"))
                    st.markdown(f"""
                    <div class="ai-message">
                        <strong>炼妖壶AI:</strong> {msg["content"]}
                        <span class="confidence-indicator {confidence_class}">
                            置信度: {msg.get("confidence", "中等")}
                        </span>
                    </div>
                    """, unsafe_allow_html=True)
                    
                    # 显示推理过程（可折叠）
                    if "reasoning" in msg:
                        with st.expander("🧠 分析过程"):
                            for reason in msg["reasoning"]:
                                st.write(f"• {reason}")
                    
                    # 显示后续问题建议
                    if "follow_up_questions" in msg:
                        st.markdown("**💡 您可能还想问:**")
                        for j, follow_up in enumerate(msg["follow_up_questions"]):
                            if st.button(follow_up, key=f"follow_{i}_{j}"):
                                self.handle_user_input(follow_up)
        
        # 输入框
        user_input = st.text_input(
            "请输入您的问题...",
            placeholder="例如：苹果股票现在能买吗？",
            key="user_input"
        )
        
        col1, col2, col3 = st.columns([1, 1, 4])
        with col1:
            if st.button("发送 📤"):
                if user_input:
                    self.handle_user_input(user_input)
        
        with col2:
            if st.button("清空对话 🗑️"):
                st.session_state.messages = []
                st.session_state.cauldron_ai.clear_history()
                st.rerun()
    
    def handle_user_input(self, user_input: str):
        """处理用户输入"""
        # 添加用户消息
        st.session_state.messages.append({
            "role": "user",
            "content": user_input
        })
        
        # 显示思考动画
        thinking_placeholder = st.empty()
        thinking_placeholder.markdown("""
        <div class="thinking-animation">
            <span class="thinking-dots">🤔 炼妖壶AI正在思考...</span>
        </div>
        """, unsafe_allow_html=True)
        
        # 模拟思考时间
        time.sleep(2)
        
        # 获取AI回答（这里需要异步处理）
        try:
            # 这里应该调用真实的AI引擎
            response = self.get_ai_response(user_input)
            
            # 添加AI回答
            st.session_state.messages.append({
                "role": "assistant",
                "content": response["answer"],
                "confidence": response["confidence"],
                "reasoning": response["reasoning"],
                "follow_up_questions": response["follow_up_questions"]
            })
            
        except Exception as e:
            st.session_state.messages.append({
                "role": "assistant",
                "content": f"抱歉，我遇到了一些问题：{str(e)}。请稍后再试。",
                "confidence": "低"
            })
        
        thinking_placeholder.empty()
        st.rerun()
    
    def get_ai_response(self, user_input: str) -> Dict:
        """获取AI回答（简化版）"""
        # 这里应该调用真实的CauldronAI
        # 目前返回模拟数据
        return {
            "answer": f"基于您的问题「{user_input}」，我为您分析如下：\n\n这是一个很好的问题。根据当前市场情况和相关数据分析，我建议您谨慎考虑，分散投资风险。\n\n💡 **核心建议**：建议您先了解基本面，再考虑技术面，最后结合市场情绪做决策。",
            "confidence": "高",
            "reasoning": [
                "分析了最新的市场数据",
                "考虑了相关新闻事件的影响",
                "结合了历史趋势对比",
                "综合了多方专业观点"
            ],
            "follow_up_questions": [
                "这个建议的风险有多大？",
                "有什么具体的操作策略吗？",
                "什么时候重新评估？"
            ]
        }
    
    def get_confidence_class(self, confidence: str) -> str:
        """获取置信度CSS类"""
        if confidence in ["很高", "高"]:
            return "confidence-high"
        elif confidence in ["中等", "中"]:
            return "confidence-medium"
        else:
            return "confidence-low"
    
    def render_sidebar_stats(self):
        """渲染侧边栏统计信息"""
        with st.sidebar:
            st.markdown("### 📊 使用统计")
            st.metric("今日咨询", len(st.session_state.messages) // 2)
            st.metric("累计对话", len(st.session_state.cauldron_ai.conversation_history))
            
            st.markdown("### 👤 用户信息")
            profile = st.session_state.user_profile
            st.write(f"**称呼**: {profile['name']}")
            st.write(f"**经验**: {profile['experience_level']}")
            st.write(f"**风险偏好**: {profile['risk_tolerance']}")
    
    def run(self):
        """运行主界面"""
        self.render_header()
        
        # 新用户引导
        if not st.session_state.onboarding_completed:
            self.render_onboarding()
            return
        
        # 主界面
        if len(st.session_state.messages) == 0:
            self.render_quick_questions()
        
        self.render_chat_interface()
        
        # 侧边栏（可选）
        if st.checkbox("显示详细信息", value=False):
            self.render_sidebar_stats()


def main():
    """主函数"""
    ui = CauldronUIv1()
    ui.run()


if __name__ == "__main__":
    main()
