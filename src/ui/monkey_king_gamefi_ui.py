# -*- coding: utf-8 -*-
"""
🐒 猴王修仙GameFi界面
集成到炼妖壶主系统的散户修仙之路
"""

import streamlit as st
import plotly.graph_objects as go
import plotly.express as px
from src.core.monkey_king_journey import Monkey<PERSON>ingJourney, Season
import pandas as pd

def render_monkey_king_gamefi():
    """渲染猴王修仙GameFi界面"""
    
    st.markdown("# 🐒 散户修仙之路 - 猴王十二境界")
    st.markdown("*基于《临江仙·山下吟》的投资者成长GameFi系统*")
    
    # 初始化用户旅程
    if 'monkey_journey' not in st.session_state:
        st.session_state.monkey_journey = MonkeyKingJourney("demo_user")
    
    journey = st.session_state.monkey_journey
    progress = journey.get_journey_progress()
    
    # 顶部状态栏
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric("当前等级", f"Lv.{progress['current_level']}")
    with col2:
        st.metric("境界", progress['level_name'])
    with col3:
        st.metric("季节", progress['season'])
    with col4:
        st.metric("修仙进度", f"{progress['progress_percent']:.1f}%")
    
    # 当前境界诗句
    st.markdown("---")
    st.markdown(f"### 🎋 当前境界")
    st.markdown(f"**{progress['poem']}**")
    st.markdown(f"*{progress['description']}*")
    
    # 四季修仙路径图
    st.markdown("---")
    st.markdown("### 🗺️ 修仙路径图")
    
    # 创建修仙进度可视化
    levels_data = []
    for i, level in enumerate(journey.LEVELS, 1):
        levels_data.append({
            'level': i,
            'name': level.name,
            'season': level.season.value,
            'completed': i <= progress['current_level'],
            'current': i == progress['current_level']
        })
    
    df = pd.DataFrame(levels_data)
    
    # 绘制修仙路径
    fig = go.Figure()
    
    # 四季背景色
    season_colors = {
        '春·求道': '#90EE90',  # 浅绿
        '夏·得道': '#FFD700',  # 金色
        '秋·失道': '#FFA500',  # 橙色
        '冬·悟道': '#87CEEB'   # 天蓝
    }
    
    for season in Season:
        season_levels = df[df['season'] == season.value]
        color = season_colors[season.value]
        
        fig.add_trace(go.Scatter(
            x=season_levels['level'],
            y=[1] * len(season_levels),
            mode='markers+text',
            marker=dict(
                size=[30 if row['current'] else 20 for _, row in season_levels.iterrows()],
                color=[color if row['completed'] else 'lightgray' for _, row in season_levels.iterrows()],
                line=dict(width=2, color='black')
            ),
            text=season_levels['name'],
            textposition="bottom center",
            name=season.value,
            hovertemplate='<b>%{text}</b><br>等级: %{x}<extra></extra>'
        ))
    
    fig.update_layout(
        title="🐒 猴王修仙十二境界",
        xaxis_title="等级",
        yaxis=dict(visible=False),
        height=300,
        showlegend=True
    )
    
    st.plotly_chart(fig, use_container_width=True)
    
    # 《临江仙·山下吟》全文展示
    st.markdown("---")
    st.markdown("### 📜 临江仙·山下吟")
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.markdown("""
        **上阙·春夏**
        
        水帘洞内见生死，  
        舢板入海求道。  
        得偿所望傲气扬，  
        斜月三星洞，  
        黄粱梦一场。
        """)
    
    with col2:
        st.markdown("""
        **下阙·秋冬**
        
        诏安饮马银河畔，  
        仙桃玉液入嗓。  
        金銮踏破终被擒，  
        八卦炉中炼，  
        五行山下吟。
        """)
    
    # 四季详解
    st.markdown("---")
    st.markdown("### 🌸🌞🍂❄️ 四季修仙详解")
    
    seasons_info = {
        "🌸 春·求道篇": {
            "levels": "Lv.1-3",
            "theme": "觉醒期",
            "description": "从懵懂入市到见生死，开始思考投资的本质",
            "key_events": ["石猴出世", "水帘洞主", "见生死"]
        },
        "☀️ 夏·得道篇": {
            "levels": "Lv.4-6", 
            "theme": "学习期",
            "description": "主动求学，建立体系，掌握投资技能",
            "key_events": ["舢板求道", "斜月三星洞", "得偿所望"]
        },
        "🍂 秋·失道篇": {
            "levels": "Lv.7-9",
            "theme": "膨胀期", 
            "description": "小有成就后开始自满，被市场诱惑偏离正道",
            "key_events": ["傲气回山", "受招安", "蟠桃盛宴"]
        },
        "❄️ 冬·悟道篇": {
            "levels": "Lv.10-12",
            "theme": "觉悟期",
            "description": "经历极端考验，痛苦蜕变，最终彻底觉悟",
            "key_events": ["大闹天宫", "八卦炉炼", "五行山下"]
        }
    }
    
    for season_name, info in seasons_info.items():
        with st.expander(f"{season_name} ({info['levels']}) - {info['theme']}"):
            st.markdown(f"**主题**: {info['description']}")
            st.markdown(f"**关键事件**: {' → '.join(info['key_events'])}")
    
    # 模拟升级按钮（演示用）
    st.markdown("---")
    st.markdown("### 🎮 GameFi操作")
    
    col1, col2, col3 = st.columns(3)
    
    with col1:
        if st.button("📈 模拟交易事件"):
            # 模拟升级
            if journey.current_level < 12:
                journey.current_level += 1
                st.success(f"🆙 升级到 Lv.{journey.current_level}!")
                st.rerun()
    
    with col2:
        if st.button("🔄 重置修仙路"):
            journey.current_level = 1
            st.info("🐒 重新开始修仙之路")
            st.rerun()
    
    with col3:
        if st.button("🏆 查看成就"):
            st.info("成就系统开发中...")
    
    # 底部说明
    st.markdown("---")
    st.markdown("""
    ### 💡 GameFi设计理念
    
    这个系统将散户投资者的成长历程，映射到孙悟空的修仙之路：
    
    - **春季觉醒**：从懵懂到见生死，开始思考
    - **夏季求道**：主动学习，建立投资体系  
    - **秋季失道**：成功后自满，被诱惑偏离
    - **冬季悟道**：经历极端考验，最终觉悟
    
    每个等级都对应真实的投资心路历程，让用户在游戏化的体验中，
    反思自己的投资行为，最终成为理性的投资者。
    
    *"心诚则灵，自解码一切"* - 太公心易BI系统
    """)

if __name__ == "__main__":
    st.set_page_config(
        page_title="猴王修仙GameFi",
        page_icon="🐒",
        layout="wide"
    )
    render_monkey_king_gamefi()