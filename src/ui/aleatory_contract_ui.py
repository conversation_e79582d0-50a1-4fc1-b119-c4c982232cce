# -*- coding: utf-8 -*-
"""
🎲 射幸合同GameFi界面
揭示交易系统与彩票的真相对比
"""

import streamlit as st
import plotly.graph_objects as go
import plotly.express as px
import pandas as pd
import numpy as np
from src.core.aleatory_contract_gamefi import AleatoryContractGameFi, ContractType, WaveType
import random

def render_aleatory_contract_ui():
    """渲染射幸合同GameFi界面"""
    
    st.markdown("# 🎲 射幸合同GameFi系统")
    st.markdown("*揭示交易系统与彩票的真相对比*")
    
    # 核心洞察展示
    with st.expander("💡 核心洞察 - 射幸合同的本质"):
        st.markdown("""
        > **"射幸合同 = 执行概率较小的合同"**
        
        > **"彩票 = 500万赔率的一次性射幸"**
        
        > **"交易系统 = 无数次小波段射幸的累积"**
        
        > **"如果对系统的设计没有达到那个五百万的赔率，其实大部分人都没有资格鄙视彩票的。"**
        
        ---
        
        **🎯 真相对比**：
        - **彩票**：500万赔率，一次性射幸，概率极低但赔率极高
        - **交易系统**：大部分连1.5倍都做不到，还要承受时间成本
        - **累积效应**：只有通过无数次小波段的射幸合同累积，才可能达成"一步登天，甚至鸡犬升天"
        """)
    
    # 初始化系统
    if 'aleatory_gamefi' not in st.session_state:
        st.session_state.aleatory_gamefi = AleatoryContractGameFi("truth_seeker", 10000)
    
    gamefi = st.session_state.aleatory_gamefi
    
    # 顶部对比栏
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric("当前资金", f"${gamefi.current_capital:,.0f}")
    with col2:
        st.metric("累积赔率", f"{gamefi.cumulative_odds:.2f}x")
    with col3:
        st.metric("等价彩票赔率", f"{gamefi.lottery_equivalent_odds:.2f}x")
    with col4:
        lottery_ratio = gamefi.lottery_equivalent_odds / 5000000 * 100
        st.metric("vs彩票", f"{lottery_ratio:.4f}%")
    
    # 彩票对比警告
    if gamefi.lottery_equivalent_odds < 500000:  # 小于彩票1/10
        st.error("❌ 当前系统没资格鄙视彩票！需要大幅提升射幸性。")
    elif gamefi.lottery_equivalent_odds < 5000000:  # 小于彩票
        st.warning("⚠️ 当前系统接近彩票水平，但仍需努力。")
    else:
        st.success("🏆 恭喜！您的系统已超越彩票，有资格鄙视彩票了！")
    
    # 主要内容标签页
    tab1, tab2, tab3, tab4 = st.tabs([
        "🎲 射幸交易", 
        "📊 真相对比", 
        "🚀 登天之路",
        "💡 射幸洞察"
    ])
    
    with tab1:
        render_aleatory_trading(gamefi)
    
    with tab2:
        render_truth_comparison(gamefi)
    
    with tab3:
        render_ascension_path(gamefi)
    
    with tab4:
        render_aleatory_insights(gamefi)

def render_aleatory_trading(gamefi):
    """渲染射幸交易"""
    st.markdown("## 🎲 执行射幸交易")
    st.markdown("*每一次交易都是一份射幸合同*")
    
    # 交易参数设置
    col1, col2 = st.columns([2, 1])
    
    with col1:
        st.markdown("### 🎯 射幸合同参数")
        
        # 合同类型
        contract_type = st.selectbox(
            "合同类型",
            options=list(ContractType),
            format_func=lambda x: f"{x.contract_name} - {x.description} (最高{x.max_odds:.0f}x)"
        )
        
        # 波段类型
        wave_type = st.selectbox(
            "波段类型",
            options=list(WaveType),
            format_func=lambda x: f"{x.wave_name} - {x.description} (最高{x.max_multiplier:.0f}x)"
        )
        
        # 仓位大小
        position_ratio = st.slider(
            "仓位比例",
            min_value=0.01,
            max_value=1.0,
            value=0.1,
            step=0.01,
            format="%.2f",
            help="投入资金占总资金的比例"
        )
        
        # 目标赔率
        max_possible = min(contract_type.max_odds, wave_type.max_multiplier)
        
        # 确保slider的最大值始终大于最小值
        if max_possible <= 1.1:
            # 对于低赔率组合，使用固定的小范围
            target_odds = st.slider(
                "目标赔率",
                min_value=1.01,
                max_value=max(1.1, max_possible),
                value=max_possible,
                step=0.01,
                format="%.2fx",
                help=f"此组合最高赔率为 {max_possible:.2f}x"
            )
        else:
            # 正常的赔率范围
            max_slider_value = min(max_possible, 1000.0)
            target_odds = st.slider(
                "目标赔率",
                min_value=1.1,
                max_value=max_slider_value,
                value=min(5.0, max_slider_value),
                step=0.1,
                format="%.1fx"
            )
        
        # 市场背景
        market_context = st.text_input(
            "市场背景",
            placeholder="描述当前市场环境..."
        )
    
    with col2:
        st.markdown("### 📋 合同说明")
        
        st.info(f"**{contract_type.contract_name}**")
        st.caption(f"{contract_type.description}")
        st.caption(f"胜率: {contract_type.win_probability:.1%}")
        st.caption(f"最高赔率: {contract_type.max_odds:.0f}x")
        
        st.info(f"**{wave_type.wave_name}**")
        st.caption(f"{wave_type.description}")
        st.caption(f"概率: {wave_type.probability:.1%}")
        st.caption(f"最高倍数: {wave_type.max_multiplier:.0f}x")
        
        # 投入金额计算
        position_amount = gamefi.current_capital * position_ratio
        st.metric("投入金额", f"${position_amount:,.0f}")
    
    # 执行交易
    if st.button("🎲 执行射幸合同", type="primary"):
        trade = gamefi.execute_aleatory_trade(
            contract_type=contract_type,
            wave_type=wave_type,
            position_size_ratio=position_ratio,
            target_odds=target_odds,
            market_context=market_context or "未指定市场背景"
        )
        
        # 显示结果
        st.markdown("---")
        st.markdown("### 🎯 射幸合同执行结果")
        
        result_col1, result_col2 = st.columns(2)
        
        with result_col1:
            if trade.actual_result:
                st.success(f"✅ 射幸成功！")
                profit = trade.initial_capital * (trade.actual_odds - 1)
                st.metric("盈利", f"${profit:,.0f}")
            else:
                st.error(f"❌ 射幸失败！")
                st.metric("亏损", f"${trade.initial_capital:,.0f}")
            
            st.metric("实际赔率", f"{trade.actual_odds:.2f}x")
            st.metric("胜率预估", f"{trade.win_probability:.1%}")
        
        with result_col2:
            st.metric("累积赔率", f"{trade.cumulative_odds:.2f}x")
            st.metric("等价彩票赔率", f"{gamefi.lottery_equivalent_odds:.2f}x")
            st.metric("时间成本", f"{trade.time_cost_days}天")
            
            # 特殊事件
            if trade.is_collective_ascension:
                st.balloons()
                st.success("🌟 鸡犬升天！")
            elif trade.is_ascension:
                st.balloons()
                st.success("🚀 一步登天！")
        
        st.rerun()
    
    # 最近交易记录
    if gamefi.trades:
        st.markdown("### 📜 最近射幸合同")
        
        recent_trades = gamefi.trades[-5:]
        
        for i, trade in enumerate(reversed(recent_trades), 1):
            result_emoji = "✅" if trade.actual_result else "❌"
            with st.expander(f"第{len(gamefi.trades) - i + 1}份合同 - {result_emoji} {trade.contract_type.contract_name}"):
                st.markdown(f"**波段**: {trade.wave_type.wave_name}")
                st.markdown(f"**目标赔率**: {trade.target_odds:.2f}x")
                st.markdown(f"**实际赔率**: {trade.actual_odds:.2f}x")
                st.markdown(f"**投入资金**: ${trade.initial_capital:,.0f}")
                st.markdown(f"**时间成本**: {trade.time_cost_days}天")
                st.markdown(f"**运气因子**: {trade.luck_factor:.3f}")

def render_truth_comparison(gamefi):
    """渲染真相对比"""
    st.markdown("## 📊 射幸合同真相对比")
    st.markdown("*交易系统 vs 彩票的残酷真相*")
    
    # 彩票基准对比
    lottery_odds = 5000000
    current_odds = gamefi.lottery_equivalent_odds
    
    col1, col2, col3 = st.columns(3)
    
    with col1:
        st.markdown("### 🎲 彩票标准")
        st.metric("赔率", "5,000,000x")
        st.metric("概率", "0.00002%")
        st.metric("时间成本", "即时")
        st.info("一次性射幸合同")
    
    with col2:
        st.markdown("### 📈 您的系统")
        st.metric("等价赔率", f"{current_odds:.2f}x")
        st.metric("胜率", f"{gamefi.win_rate:.2%}")
        st.metric("时间成本", f"{gamefi.time_in_market_days}天")
        
        efficiency = gamefi.system_efficiency
        if efficiency >= 80:
            st.success(f"效率: {efficiency:.1f}/100")
        elif efficiency >= 60:
            st.warning(f"效率: {efficiency:.1f}/100")
        else:
            st.error(f"效率: {efficiency:.1f}/100")
    
    with col3:
        st.markdown("### ⚖️ 对比结果")
        ratio = current_odds / lottery_odds
        st.metric("vs彩票比例", f"{ratio:.6%}")
        
        if ratio >= 1.0:
            st.success("🏆 超越彩票！")
        elif ratio >= 0.1:
            st.warning("🎯 接近彩票")
        elif ratio >= 0.01:
            st.info("📈 有一定价值")
        else:
            st.error("❌ 不如彩票")
    
    # 可视化对比
    st.markdown("### 📊 赔率对比可视化")
    
    comparison_data = {
        "类型": ["彩票", "您的系统", "银行存款", "股票指数"],
        "赔率": [lottery_odds, max(current_odds, 1), 1.03, 1.1],
        "概率": [0.00002, gamefi.win_rate * 100, 99.9, 70],
        "时间成本": [0, gamefi.time_in_market_days, 365, 365]
    }
    
    df_comparison = pd.DataFrame(comparison_data)
    
    # 创建气泡图
    fig = go.Figure()
    
    colors = ["red", "blue", "green", "orange"]
    
    for i, row in df_comparison.iterrows():
        fig.add_trace(go.Scatter(
            x=[row["概率"]],
            y=[row["赔率"]],
            mode='markers+text',
            marker=dict(
                size=max(10, min(50, row["时间成本"] / 10)),
                color=colors[i],
                opacity=0.7
            ),
            text=row["类型"],
            textposition="middle center",
            name=row["类型"],
            hovertemplate=f'<b>{row["类型"]}</b><br>' +
                         f'赔率: {row["赔率"]:.2f}x<br>' +
                         f'概率: {row["概率"]:.2f}%<br>' +
                         f'时间成本: {row["时间成本"]}天<extra></extra>'
        ))
    
    fig.update_layout(
        title="🎲 射幸合同赔率-概率对比图",
        xaxis_title="成功概率 (%)",
        yaxis_title="赔率倍数",
        yaxis_type="log",
        height=500
    )
    
    st.plotly_chart(fig, use_container_width=True)
    
    # 系统效率分析
    if gamefi.trades:
        analysis = gamefi.analyze_aleatory_performance()
        
        st.markdown("### 📈 系统效率分析")
        
        comparison_result = analysis.get("与彩票对比", {})
        
        st.markdown(f"**{comparison_result.get('结论', '未知')}**")
        st.markdown(f"*{comparison_result.get('评价', '暂无评价')}*")
        st.info(f"💡 {comparison_result.get('建议', '继续努力')}")

def render_ascension_path(gamefi):
    """渲染登天之路"""
    st.markdown("## 🚀 登天之路")
    st.markdown("*一步登天 vs 鸡犬升天的射幸之路*")
    
    # 登天目标
    col1, col2 = st.columns(2)
    
    with col1:
        st.markdown("### 🚀 一步登天")
        ascension_target = gamefi.initial_capital * 100
        ascension_progress = min(1.0, gamefi.current_capital / ascension_target)
        
        st.progress(ascension_progress, text=f"进度: {ascension_progress:.1%}")
        st.metric("目标", f"${ascension_target:,.0f}")
        st.metric("当前", f"${gamefi.current_capital:,.0f}")
        st.metric("差距", f"${ascension_target - gamefi.current_capital:,.0f}")
    
    with col2:
        st.markdown("### 🌟 鸡犬升天")
        collective_target = gamefi.initial_capital * 1000
        collective_progress = min(1.0, gamefi.current_capital / collective_target)
        
        st.progress(collective_progress, text=f"进度: {collective_progress:.1%}")
        st.metric("目标", f"${collective_target:,.0f}")
        st.metric("当前", f"${gamefi.current_capital:,.0f}")
        st.metric("差距", f"${collective_target - gamefi.current_capital:,.0f}")
    
    # 登天记录
    if gamefi.ascension_records:
        st.markdown("### 🏆 登天成就")
        
        for record in gamefi.ascension_records:
            st.success(f"🎉 {record.ascension_type} - {record.total_odds:.2f}x倍率")
            st.caption(f"用时: {record.time_span_days}天, 交易: {record.trades_count}次, 运气: {record.luck_score:.1f}/100")
    else:
        st.info("🎯 还未达成登天成就，继续努力！")
    
    # 资金增长曲线
    if gamefi.trades:
        st.markdown("### 📈 资金增长轨迹")
        
        capital_history = [gamefi.initial_capital]
        current_capital = gamefi.initial_capital
        
        for trade in gamefi.trades:
            if trade.actual_result:
                profit = trade.initial_capital * (trade.actual_odds - 1)
                current_capital += profit
            else:
                current_capital -= trade.initial_capital
            capital_history.append(current_capital)
        
        # 创建增长曲线
        fig = go.Figure()
        
        fig.add_trace(go.Scatter(
            x=list(range(len(capital_history))),
            y=capital_history,
            mode='lines+markers',
            name='资金曲线',
            line=dict(color='blue', width=3),
            marker=dict(size=8)
        ))
        
        # 添加登天线
        fig.add_hline(
            y=gamefi.initial_capital * 100,
            line_dash="dash",
            line_color="green",
            annotation_text="一步登天线"
        )
        
        fig.add_hline(
            y=gamefi.initial_capital * 1000,
            line_dash="dash", 
            line_color="gold",
            annotation_text="鸡犬升天线"
        )
        
        fig.update_layout(
            title="🚀 射幸合同资金增长轨迹",
            xaxis_title="交易次数",
            yaxis_title="资金金额 ($)",
            height=400
        )
        
        st.plotly_chart(fig, use_container_width=True)

def render_aleatory_insights(gamefi):
    """渲染射幸洞察"""
    st.markdown("## 💡 射幸合同洞察")
    st.markdown("*深度分析您的射幸合同表现*")
    
    if not gamefi.trades:
        st.info("还没有交易记录，去执行一些射幸合同吧！")
        return
    
    # 获取分析结果
    analysis = gamefi.analyze_aleatory_performance()
    insights = gamefi.get_aleatory_insights()
    
    # 基础表现
    st.markdown("### 📊 基础表现")
    
    basic_performance = analysis.get("基础表现", {})
    
    col1, col2, col3 = st.columns(3)
    
    with col1:
        st.metric("总交易数", basic_performance.get("总交易数", 0))
        st.metric("胜率", basic_performance.get("胜率", "0%"))
    
    with col2:
        st.metric("总收益率", basic_performance.get("总收益率", "0%"))
        st.metric("年化收益率", basic_performance.get("年化收益率", "0%"))
    
    with col3:
        st.metric("最大回撤", basic_performance.get("最大回撤", "0%"))
        st.metric("市场时间", basic_performance.get("市场时间", "0天"))
    
    # 射幸合同分析
    st.markdown("### 🎲 射幸合同分析")
    
    contract_stats = analysis.get("射幸合同分析", {})
    
    if contract_stats:
        contract_data = []
        for contract_name, stats in contract_stats.items():
            win_rate = stats["wins"] / stats["count"] if stats["count"] > 0 else 0
            contract_data.append({
                "合同类型": contract_name,
                "交易次数": stats["count"],
                "胜率": f"{win_rate:.1%}",
                "累积赔率": f"{stats['total_odds']:.2f}x"
            })
        
        df_contracts = pd.DataFrame(contract_data)
        st.dataframe(df_contracts, use_container_width=True)
    
    # 洞察建议
    st.markdown("### 🧠 智能洞察")
    
    for insight in insights:
        if "💡" in insight:
            st.info(insight)
        elif "⚠️" in insight:
            st.warning(insight)
        elif "🚀" in insight:
            st.success(insight)
        elif "🎲" in insight:
            st.error(insight)
        else:
            st.markdown(insight)
    
    # 改进建议
    st.markdown("### 🎯 改进建议")
    
    if gamefi.system_efficiency < 50:
        st.markdown("""
        **🔧 系统效率提升建议**：
        1. 增加高赔率的射幸交易比例
        2. 减少低效的双务合同
        3. 寻找传说级别的波段机会
        4. 优化仓位管理，提高资金利用效率
        """)
    
    if gamefi.lottery_equivalent_odds < 100000:
        st.markdown("""
        **🎲 向彩票学习**：
        1. 彩票的成功在于极高的赔率
        2. 您需要寻找更高赔率的机会
        3. 考虑降低交易频率，提高单次赔率
        4. 学习彩票的"一次性射幸"思维
        """)

if __name__ == "__main__":
    st.set_page_config(
        page_title="射幸合同GameFi",
        page_icon="🎲",
        layout="wide"
    )
    render_aleatory_contract_ui()