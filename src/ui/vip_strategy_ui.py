# -*- coding: utf-8 -*-
"""
VIP策略管理界面
提供策略浏览、下载、定制等功能
"""

import streamlit as st
import pandas as pd
import plotly.graph_objects as go
import plotly.express as px
from datetime import datetime
import io
import zipfile
from typing import Dict, List, Any, Optional

from ..core.vip_strategy_manager import VIPStrategyManager, MembershipLevel, StrategyCategory


class VIPStrategyUI:
    """VIP策略管理界面"""
    
    def __init__(self):
        self.strategy_manager = VIPStrategyManager()
        
        # 初始化会话状态
        if 'user_membership' not in st.session_state:
            st.session_state['user_membership'] = MembershipLevel.FREE
        
        if 'user_id' not in st.session_state:
            st.session_state['user_id'] = "demo_user"
    
    def render(self):
        """渲染主界面"""
        st.title("👑 太公心易 - 策略中心")
        
        # 会员状态显示
        self._render_membership_status()
        
        # 主要功能标签页
        tab1, tab2, tab3, tab4 = st.tabs(["📚 策略库", "📊 性能分析", "🛠️ 定制策略", "📈 我的策略"])
        
        with tab1:
            self._render_strategy_library()
        
        with tab2:
            self._render_performance_analysis()
        
        with tab3:
            self._render_custom_strategy()
        
        with tab4:
            self._render_my_strategies()
    
    def _render_membership_status(self):
        """渲染会员状态"""
        col1, col2, col3 = st.columns([2, 1, 1])
        
        with col1:
            membership_level = st.session_state['user_membership']
            level_info = {
                MembershipLevel.FREE: {"name": "六壬观心", "color": "gray", "icon": "🆓"},
                MembershipLevel.PREMIUM: {"name": "遁甲择时", "color": "blue", "icon": "💎"},
                MembershipLevel.VIP: {"name": "太乙观澜", "color": "gold", "icon": "👑"}
            }
            
            info = level_info[membership_level]
            st.markdown(f"""
            <div style="padding: 10px; border-radius: 10px; background-color: {info['color']}20; border-left: 4px solid {info['color']};">
                <h3>{info['icon']} 当前会员等级: {info['name']}</h3>
            </div>
            """, unsafe_allow_html=True)
        
        with col2:
            # 会员等级切换（演示用）
            new_level = st.selectbox(
                "切换会员等级",
                [MembershipLevel.FREE, MembershipLevel.PREMIUM, MembershipLevel.VIP],
                index=[MembershipLevel.FREE, MembershipLevel.PREMIUM, MembershipLevel.VIP].index(membership_level),
                format_func=lambda x: level_info[x]["name"]
            )
            
            if new_level != membership_level:
                st.session_state['user_membership'] = new_level
                st.rerun()
        
        with col3:
            if st.button("🔄 刷新策略"):
                st.cache_data.clear()
                st.rerun()
    
    def _render_strategy_library(self):
        """渲染策略库"""
        st.subheader("📚 策略库")
        
        # 筛选选项
        col1, col2, col3 = st.columns(3)
        
        with col1:
            category_filter = st.selectbox(
                "策略分类",
                ["全部"] + [cat.value for cat in StrategyCategory],
                format_func=lambda x: {
                    "全部": "全部分类",
                    "momentum": "动量策略",
                    "mean_reversion": "均值回归",
                    "arbitrage": "套利策略",
                    "options": "期权策略",
                    "futures": "期货策略",
                    "demon_stock": "妖股识别",
                    "market_making": "做市策略",
                    "pairs_trading": "配对交易"
                }.get(x, x)
            )
        
        with col2:
            risk_filter = st.selectbox(
                "风险等级",
                ["全部", "1", "2", "3", "4", "5"],
                format_func=lambda x: f"风险等级 {x}" if x != "全部" else "全部风险等级"
            )
        
        with col3:
            search_keyword = st.text_input("搜索策略", placeholder="输入策略名称或关键词")
        
        # 获取可用策略
        available_strategies = self.strategy_manager.get_available_strategies(
            st.session_state['user_membership']
        )
        
        # 应用筛选
        filtered_strategies = available_strategies
        
        if category_filter != "全部":
            filtered_strategies = [
                s for s in filtered_strategies 
                if s.category.value == category_filter
            ]
        
        if risk_filter != "全部":
            filtered_strategies = [
                s for s in filtered_strategies 
                if s.risk_level == int(risk_filter)
            ]
        
        if search_keyword:
            keyword = search_keyword.lower()
            filtered_strategies = [
                s for s in filtered_strategies 
                if keyword in s.name.lower() or keyword in s.description.lower()
            ]
        
        # 显示策略列表
        if filtered_strategies:
            for strategy in filtered_strategies:
                self._render_strategy_card(strategy)
        else:
            st.info("没有找到符合条件的策略")
    
    def _render_strategy_card(self, strategy):
        """渲染策略卡片"""
        with st.container():
            st.markdown("---")
            
            col1, col2, col3 = st.columns([3, 2, 1])
            
            with col1:
                # 策略基本信息
                st.markdown(f"### {strategy.name}")
                st.write(f"**版本**: {strategy.version} | **作者**: {strategy.author}")
                st.write(f"**分类**: {strategy.category.value} | **风险等级**: {'⭐' * strategy.risk_level}")
                st.write(strategy.description)
            
            with col2:
                # 性能指标
                st.write("**性能指标**")
                metrics = strategy.performance_metrics
                
                if 'annual_return' in metrics:
                    st.metric("年化收益", f"{metrics['annual_return']:.1%}")
                
                if 'sharpe_ratio' in metrics:
                    st.metric("夏普比率", f"{metrics['sharpe_ratio']:.2f}")
                
                if 'max_drawdown' in metrics:
                    st.metric("最大回撤", f"{metrics['max_drawdown']:.1%}")
            
            with col3:
                # 操作按钮
                st.write("**操作**")
                
                # 查看详情按钮
                if st.button("📊 详情", key=f"detail_{strategy.id}"):
                    st.session_state['selected_strategy'] = strategy.id
                
                # 下载按钮
                if self._check_download_permission(strategy):
                    if st.button("📥 下载", key=f"download_{strategy.id}"):
                        self._download_strategy(strategy)
                else:
                    st.button("🔒 需要升级", key=f"upgrade_{strategy.id}", disabled=True)
        
        # 显示策略详情
        if st.session_state.get('selected_strategy') == strategy.id:
            self._render_strategy_detail(strategy)
    
    def _render_strategy_detail(self, strategy):
        """渲染策略详情"""
        with st.expander("📊 策略详细信息", expanded=True):
            col1, col2 = st.columns(2)
            
            with col1:
                st.write("**基本信息**")
                st.write(f"- 策略ID: {strategy.id}")
                st.write(f"- 创建时间: {strategy.created_at.strftime('%Y-%m-%d')}")
                st.write(f"- 更新时间: {strategy.updated_at.strftime('%Y-%m-%d')}")
                st.write(f"- 最低会员等级: {strategy.min_membership.value}")
                
                st.write("**依赖包**")
                for dep in strategy.dependencies:
                    st.write(f"- {dep}")
            
            with col2:
                st.write("**性能详情**")
                
                # 创建性能图表
                if strategy.performance_metrics:
                    metrics_df = pd.DataFrame([
                        {"指标": k, "数值": v} 
                        for k, v in strategy.performance_metrics.items()
                    ])
                    
                    fig = px.bar(
                        metrics_df, 
                        x="指标", 
                        y="数值",
                        title="策略性能指标"
                    )
                    st.plotly_chart(fig, use_container_width=True)
            
            # 文档说明
            if strategy.documentation:
                st.write("**使用说明**")
                st.write(strategy.documentation)
    
    def _render_performance_analysis(self):
        """渲染性能分析"""
        st.subheader("📊 策略性能分析")
        
        # 策略选择
        available_strategies = self.strategy_manager.get_available_strategies(
            st.session_state['user_membership']
        )
        
        if available_strategies:
            selected_strategies = st.multiselect(
                "选择要对比的策略",
                [s.id for s in available_strategies],
                format_func=lambda x: next(s.name for s in available_strategies if s.id == x),
                default=[available_strategies[0].id] if available_strategies else []
            )
            
            if selected_strategies:
                self._render_strategy_comparison(selected_strategies, available_strategies)
        else:
            st.info("暂无可用策略进行分析")
    
    def _render_strategy_comparison(self, selected_ids: List[str], all_strategies: List):
        """渲染策略对比"""
        selected_strategies = [s for s in all_strategies if s.id in selected_ids]
        
        # 性能对比表
        comparison_data = []
        for strategy in selected_strategies:
            metrics = strategy.performance_metrics
            comparison_data.append({
                "策略名称": strategy.name,
                "年化收益": metrics.get('annual_return', 0),
                "夏普比率": metrics.get('sharpe_ratio', 0),
                "最大回撤": metrics.get('max_drawdown', 0),
                "胜率": metrics.get('win_rate', 0),
                "风险等级": strategy.risk_level
            })
        
        if comparison_data:
            df = pd.DataFrame(comparison_data)
            st.dataframe(df, use_container_width=True)
            
            # 可视化对比
            col1, col2 = st.columns(2)
            
            with col1:
                # 收益vs风险散点图
                fig = px.scatter(
                    df,
                    x="最大回撤",
                    y="年化收益",
                    size="夏普比率",
                    color="风险等级",
                    hover_name="策略名称",
                    title="收益风险分析"
                )
                st.plotly_chart(fig, use_container_width=True)
            
            with col2:
                # 综合评分雷达图
                fig = go.Figure()
                
                for strategy in selected_strategies:
                    metrics = strategy.performance_metrics
                    fig.add_trace(go.Scatterpolar(
                        r=[
                            metrics.get('annual_return', 0) * 100,
                            metrics.get('sharpe_ratio', 0) * 20,
                            (1 - metrics.get('max_drawdown', 0)) * 100,
                            metrics.get('win_rate', 0) * 100,
                            (6 - strategy.risk_level) * 20
                        ],
                        theta=['年化收益', '夏普比率', '风控能力', '胜率', '稳定性'],
                        fill='toself',
                        name=strategy.name
                    ))
                
                fig.update_layout(
                    polar=dict(
                        radialaxis=dict(
                            visible=True,
                            range=[0, 100]
                        )),
                    showlegend=True,
                    title="策略综合评分"
                )
                
                st.plotly_chart(fig, use_container_width=True)
    
    def _render_custom_strategy(self):
        """渲染自定义策略"""
        st.subheader("🛠️ 定制策略")
        
        if st.session_state['user_membership'] != MembershipLevel.VIP:
            st.warning("⚠️ 策略定制功能仅限至尊会员使用")
            st.info("升级至至尊会员即可享受：")
            st.write("- 完整策略源码下载")
            st.write("- 个性化策略定制")
            st.write("- 一对一技术支持")
            st.write("- 独立VPS部署")
            return
        
        st.success("🎉 欢迎使用策略定制功能！")
        
        # 策略定制表单
        with st.form("custom_strategy_form"):
            st.write("**基本信息**")
            
            col1, col2 = st.columns(2)
            
            with col1:
                strategy_name = st.text_input("策略名称", placeholder="输入您的策略名称")
                strategy_category = st.selectbox(
                    "策略分类",
                    [cat.value for cat in StrategyCategory],
                    format_func=lambda x: {
                        "momentum": "动量策略",
                        "mean_reversion": "均值回归",
                        "arbitrage": "套利策略",
                        "options": "期权策略",
                        "futures": "期货策略",
                        "demon_stock": "妖股识别",
                        "market_making": "做市策略",
                        "pairs_trading": "配对交易"
                    }.get(x, x)
                )
            
            with col2:
                risk_level = st.slider("风险等级", 1, 5, 3)
                dependencies = st.text_area(
                    "依赖包",
                    placeholder="每行一个包名，例如:\nnumpy\npandas\nscikit-learn",
                    key="vip_strategy_dependencies"
                )
            
            strategy_description = st.text_area(
                "策略描述",
                placeholder="详细描述您的策略逻辑、适用场景等",
                key="vip_strategy_description"
            )
            
            st.write("**源码文件**")
            source_code = st.text_area(
                "主策略文件 (strategy.py)",
                placeholder="请输入您的策略源码...",
                height=300,
                key="vip_strategy_source_code"
            )
            
            documentation = st.text_area(
                "使用说明",
                placeholder="策略的使用方法、参数说明、注意事项等",
                key="vip_strategy_documentation"
            )
            
            submitted = st.form_submit_button("🚀 创建策略")
            
            if submitted and strategy_name and source_code:
                # 创建自定义策略
                strategy_info = {
                    "name": strategy_name,
                    "description": strategy_description,
                    "category": strategy_category,
                    "dependencies": [dep.strip() for dep in dependencies.split('\n') if dep.strip()],
                    "risk_level": risk_level,
                    "documentation": documentation
                }
                
                source_files = {
                    "strategy.py": source_code
                }
                
                try:
                    strategy_id = self.strategy_manager.add_custom_strategy(
                        strategy_info, source_files
                    )
                    st.success(f"✅ 策略创建成功！策略ID: {strategy_id}")
                    st.info("您可以在'我的策略'标签页中查看和管理您的自定义策略")
                except Exception as e:
                    st.error(f"❌ 策略创建失败: {e}")
    
    def _render_my_strategies(self):
        """渲染我的策略"""
        st.subheader("📈 我的策略")
        
        # 获取用户访问统计
        user_stats = self.strategy_manager.get_user_access_stats(st.session_state['user_id'])
        
        # 显示统计信息
        col1, col2, col3 = st.columns(3)
        
        with col1:
            st.metric("总访问次数", user_stats['total_access'])
        
        with col2:
            st.metric("访问策略数", len(user_stats['strategies_accessed']))
        
        with col3:
            last_access = user_stats.get('last_access')
            if last_access:
                last_access_date = datetime.fromisoformat(last_access).strftime('%Y-%m-%d')
                st.metric("最后访问", last_access_date)
            else:
                st.metric("最后访问", "无记录")
        
        # 访问历史
        if user_stats['strategies_accessed']:
            st.write("**访问过的策略**")
            for strategy_id in user_stats['strategies_accessed']:
                if strategy_id in self.strategy_manager.strategies:
                    strategy = self.strategy_manager.strategies[strategy_id]
                    st.write(f"- {strategy.name} ({strategy_id})")
        
        # 使用建议
        st.write("**使用建议**")
        membership_level = st.session_state['user_membership']
        
        if membership_level == MembershipLevel.FREE:
            st.info("💡 升级至遁甲择时会员，解锁更多策略和功能")
        elif membership_level == MembershipLevel.PREMIUM:
            st.info("💡 升级至太乙观澜会员，获取完整源码和定制服务")
        else:
            st.success("🎉 您已是至尊会员，享受所有功能！")
    
    def _check_download_permission(self, strategy) -> bool:
        """检查下载权限"""
        return self.strategy_manager._check_access_permission(
            strategy, st.session_state['user_membership']
        )
    
    def _download_strategy(self, strategy):
        """下载策略"""
        try:
            result = self.strategy_manager.get_strategy_source(
                strategy_id=strategy.id,
                user_id=st.session_state['user_id'],
                membership_level=st.session_state['user_membership'],
                ip_address="127.0.0.1"  # 在实际应用中应获取真实IP
            )
            
            if result:
                zip_data, filename = result
                
                st.download_button(
                    label=f"📥 下载 {strategy.name}",
                    data=zip_data,
                    file_name=filename,
                    mime="application/zip",
                    key=f"download_btn_{strategy.id}"
                )
                
                st.success(f"✅ {strategy.name} 准备就绪，点击上方按钮下载")
            else:
                st.error("❌ 下载失败，请稍后重试")
                
        except Exception as e:
            st.error(f"❌ 下载失败: {e}")
