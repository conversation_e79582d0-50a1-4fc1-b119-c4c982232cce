#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
革命性数据可视化界面 - iPhone级别的视觉体验
让复杂的金融数据变得直观易懂

设计理念：
1. 直觉化设计 - 一眼就能看懂的图表
2. 情感化表达 - 用颜色和动画传达情绪
3. 交互式探索 - 点击即可深入了解
4. 故事化叙述 - 数据背后的故事
5. 个性化视角 - 根据用户偏好定制

作者：太公心易BI系统
版本：v1.0 Revolutionary Visualization
"""

import streamlit as st
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import json
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
import math


@dataclass
class VisualizationData:
    """可视化数据结构"""
    title: str
    data: Dict
    chart_type: str
    emotional_tone: str  # positive, negative, neutral, warning
    story: str
    insights: List[str]


class RevolutionaryVisualization:
    """革命性数据可视化系统"""
    
    def __init__(self):
        self.setup_theme()
        self.color_palette = self.get_emotional_colors()
    
    def setup_theme(self):
        """设置主题"""
        # 炼妖壶专属配色方案
        self.theme = {
            "background": "#0f1419",
            "surface": "#1a1f2e", 
            "primary": "#4ecdc4",
            "secondary": "#ff6b6b",
            "accent": "#ffd93d",
            "text": "#ffffff",
            "text_secondary": "#a0a0a0"
        }
    
    def get_emotional_colors(self) -> Dict:
        """情感化配色方案"""
        return {
            "positive": ["#4ecdc4", "#45b7d1", "#96ceb4", "#ffeaa7"],
            "negative": ["#ff6b6b", "#fd79a8", "#e84393", "#ff7675"],
            "neutral": ["#74b9ff", "#a29bfe", "#6c5ce7", "#fd79a8"],
            "warning": ["#ffd93d", "#fdcb6e", "#e17055", "#d63031"],
            "success": ["#00b894", "#00cec9", "#55a3ff", "#74b9ff"],
            "gradient_positive": "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
            "gradient_negative": "linear-gradient(135deg, #f093fb 0%, #f5576c 100%)",
            "gradient_neutral": "linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)"
        }
    
    def create_decision_confidence_gauge(self, confidence: float, decision: str) -> go.Figure:
        """创建决策置信度仪表盘 - 革命性设计"""
        
        # 根据置信度确定颜色
        if confidence >= 0.8:
            color = "#00b894"  # 高置信度 - 绿色
            emotion = "非常确信"
        elif confidence >= 0.6:
            color = "#74b9ff"  # 中高置信度 - 蓝色
            emotion = "比较确信"
        elif confidence >= 0.4:
            color = "#fdcb6e"  # 中等置信度 - 黄色
            emotion = "谨慎乐观"
        else:
            color = "#ff7675"  # 低置信度 - 红色
            emotion = "需要谨慎"
        
        fig = go.Figure(go.Indicator(
            mode = "gauge+number+delta",
            value = confidence * 100,
            domain = {'x': [0, 1], 'y': [0, 1]},
            title = {
                'text': f"<b>{decision}</b><br><span style='font-size:16px;color:{color}'>{emotion}</span>",
                'font': {'size': 24, 'color': 'white'}
            },
            delta = {'reference': 70, 'increasing': {'color': color}},
            gauge = {
                'axis': {'range': [None, 100], 'tickcolor': "white", 'tickfont': {'color': 'white'}},
                'bar': {'color': color, 'thickness': 0.8},
                'bgcolor': "rgba(255,255,255,0.1)",
                'borderwidth': 2,
                'bordercolor': color,
                'steps': [
                    {'range': [0, 40], 'color': "rgba(255, 118, 117, 0.3)"},
                    {'range': [40, 70], 'color': "rgba(253, 203, 110, 0.3)"},
                    {'range': [70, 100], 'color': "rgba(0, 184, 148, 0.3)"}
                ],
                'threshold': {
                    'line': {'color': "white", 'width': 4},
                    'thickness': 0.75,
                    'value': confidence * 100
                }
            }
        ))
        
        fig.update_layout(
            paper_bgcolor="rgba(0,0,0,0)",
            plot_bgcolor="rgba(0,0,0,0)",
            font={'color': "white", 'family': "Arial"},
            height=300
        )
        
        return fig
    
    def create_market_emotion_radar(self, emotions: Dict[str, float]) -> go.Figure:
        """创建市场情绪雷达图 - 八仙情绪映射"""
        
        # 八仙对应的情绪维度
        immortals = [
            "吕洞宾-理性", "何仙姑-保守", "张果老-经验", "韩湘子-技术",
            "汉钟离-价值", "蓝采和-趋势", "曹国舅-消息", "铁拐李-逆向"
        ]
        
        # 模拟八仙的情绪数据
        values = [
            emotions.get("rational", 0.7),
            emotions.get("conservative", 0.5),
            emotions.get("experienced", 0.8),
            emotions.get("technical", 0.6),
            emotions.get("value", 0.7),
            emotions.get("trend", 0.4),
            emotions.get("news", 0.6),
            emotions.get("contrarian", 0.3)
        ]
        
        # 创建雷达图
        fig = go.Figure()
        
        fig.add_trace(go.Scatterpolar(
            r=values + [values[0]],  # 闭合图形
            theta=immortals + [immortals[0]],
            fill='toself',
            fillcolor='rgba(78, 205, 196, 0.3)',
            line=dict(color='rgba(78, 205, 196, 1)', width=3),
            name='八仙情绪指数',
            hovertemplate='<b>%{theta}</b><br>情绪指数: %{r:.2f}<extra></extra>'
        ))
        
        fig.update_layout(
            polar=dict(
                radialaxis=dict(
                    visible=True,
                    range=[0, 1],
                    tickfont=dict(color='white', size=10),
                    gridcolor='rgba(255,255,255,0.3)'
                ),
                angularaxis=dict(
                    tickfont=dict(color='white', size=12),
                    gridcolor='rgba(255,255,255,0.3)'
                ),
                bgcolor="rgba(0,0,0,0)"
            ),
            showlegend=True,
            legend=dict(font=dict(color='white')),
            paper_bgcolor="rgba(0,0,0,0)",
            plot_bgcolor="rgba(0,0,0,0)",
            font=dict(color='white'),
            title=dict(
                text="<b>八仙市场情绪雷达</b>",
                font=dict(size=20, color='white'),
                x=0.5
            ),
            height=400
        )
        
        return fig
    
    def create_risk_return_bubble(self, assets: List[Dict]) -> go.Figure:
        """创建风险收益气泡图 - 直观的投资选择"""
        
        # 准备数据
        names = [asset['name'] for asset in assets]
        returns = [asset['return'] for asset in assets]
        risks = [asset['risk'] for asset in assets]
        sizes = [asset['market_cap'] for asset in assets]
        colors = [asset['sentiment'] for asset in assets]
        
        # 创建气泡图
        fig = go.Figure(data=go.Scatter(
            x=risks,
            y=returns,
            mode='markers+text',
            marker=dict(
                size=[s/1000 for s in sizes],  # 调整气泡大小
                color=colors,
                colorscale='RdYlGn',
                showscale=True,
                colorbar=dict(
                    title="市场情绪",
                    title_font=dict(color='white'),
                    tickfont=dict(color='white')
                ),
                line=dict(width=2, color='white'),
                sizemode='diameter',
                sizeref=2.*max([s/1000 for s in sizes])/(40.**2),
                sizemin=4
            ),
            text=names,
            textposition="middle center",
            textfont=dict(color='white', size=10),
            hovertemplate='<b>%{text}</b><br>' +
                         '预期收益: %{y:.1f}%<br>' +
                         '风险水平: %{x:.1f}%<br>' +
                         '市值: %{marker.size}<br>' +
                         '<extra></extra>'
        ))
        
        # 添加理想区域标注
        fig.add_shape(
            type="rect",
            x0=0, y0=8, x1=15, y1=20,
            fillcolor="rgba(0, 184, 148, 0.2)",
            line=dict(color="rgba(0, 184, 148, 0.5)", width=2),
        )
        
        fig.add_annotation(
            x=7.5, y=14,
            text="理想投资区域<br>高收益低风险",
            showarrow=False,
            font=dict(color='#00b894', size=12),
            bgcolor="rgba(0, 184, 148, 0.1)",
            bordercolor="#00b894",
            borderwidth=1
        )
        
        fig.update_layout(
            title=dict(
                text="<b>风险收益全景图</b><br><sub>气泡大小代表市值，颜色代表市场情绪</sub>",
                font=dict(size=20, color='white'),
                x=0.5
            ),
            xaxis=dict(
                title="风险水平 (%)",
                titlefont=dict(color='white'),
                tickfont=dict(color='white'),
                gridcolor='rgba(255,255,255,0.3)'
            ),
            yaxis=dict(
                title="预期收益 (%)",
                titlefont=dict(color='white'),
                tickfont=dict(color='white'),
                gridcolor='rgba(255,255,255,0.3)'
            ),
            paper_bgcolor="rgba(0,0,0,0)",
            plot_bgcolor="rgba(0,0,0,0)",
            height=500
        )
        
        return fig
    
    def create_news_impact_timeline(self, news_events: List[Dict]) -> go.Figure:
        """创建新闻影响时间线 - 故事化展示"""
        
        # 准备数据
        dates = [event['date'] for event in news_events]
        impacts = [event['impact'] for event in news_events]
        titles = [event['title'] for event in news_events]
        sentiments = [event['sentiment'] for event in news_events]
        
        # 创建时间线图
        fig = go.Figure()
        
        # 添加影响力曲线
        fig.add_trace(go.Scatter(
            x=dates,
            y=impacts,
            mode='lines+markers',
            line=dict(color='#4ecdc4', width=3),
            marker=dict(
                size=[abs(s)*20 + 10 for s in sentiments],
                color=sentiments,
                colorscale='RdYlGn',
                showscale=True,
                colorbar=dict(
                    title="情绪倾向",
                    title_font=dict(color='white'),
                    tickfont=dict(color='white')
                ),
                line=dict(width=2, color='white')
            ),
            text=titles,
            hovertemplate='<b>%{text}</b><br>' +
                         '日期: %{x}<br>' +
                         '影响力: %{y:.1f}<br>' +
                         '<extra></extra>',
            name='新闻影响力'
        ))
        
        # 添加零线
        fig.add_hline(y=0, line_dash="dash", line_color="rgba(255,255,255,0.5)")
        
        # 添加重要事件标注
        for i, event in enumerate(news_events):
            if abs(event['impact']) > 0.7:  # 重要事件
                fig.add_annotation(
                    x=event['date'],
                    y=event['impact'],
                    text=f"📰 {event['title'][:20]}...",
                    showarrow=True,
                    arrowhead=2,
                    arrowcolor='white',
                    font=dict(color='white', size=10),
                    bgcolor="rgba(0,0,0,0.7)",
                    bordercolor='white',
                    borderwidth=1
                )
        
        fig.update_layout(
            title=dict(
                text="<b>新闻事件影响时间线</b><br><sub>气泡大小代表情绪强度，颜色代表正负倾向</sub>",
                font=dict(size=20, color='white'),
                x=0.5
            ),
            xaxis=dict(
                title="时间",
                titlefont=dict(color='white'),
                tickfont=dict(color='white'),
                gridcolor='rgba(255,255,255,0.3)'
            ),
            yaxis=dict(
                title="市场影响力",
                titlefont=dict(color='white'),
                tickfont=dict(color='white'),
                gridcolor='rgba(255,255,255,0.3)',
                range=[-1, 1]
            ),
            paper_bgcolor="rgba(0,0,0,0)",
            plot_bgcolor="rgba(0,0,0,0)",
            height=400
        )
        
        return fig
    
    def create_portfolio_health_dashboard(self, portfolio_data: Dict) -> go.Figure:
        """创建投资组合健康仪表板"""
        
        # 创建子图
        fig = make_subplots(
            rows=2, cols=2,
            subplot_titles=('资产配置', '风险分布', '收益趋势', '健康评分'),
            specs=[[{"type": "pie"}, {"type": "bar"}],
                   [{"type": "scatter"}, {"type": "indicator"}]]
        )
        
        # 1. 资产配置饼图
        fig.add_trace(
            go.Pie(
                labels=portfolio_data['assets']['labels'],
                values=portfolio_data['assets']['values'],
                hole=0.4,
                marker_colors=self.color_palette['positive']
            ),
            row=1, col=1
        )
        
        # 2. 风险分布柱状图
        fig.add_trace(
            go.Bar(
                x=portfolio_data['risks']['categories'],
                y=portfolio_data['risks']['values'],
                marker_color=self.color_palette['warning']
            ),
            row=1, col=2
        )
        
        # 3. 收益趋势线图
        fig.add_trace(
            go.Scatter(
                x=portfolio_data['returns']['dates'],
                y=portfolio_data['returns']['values'],
                mode='lines+markers',
                line=dict(color='#4ecdc4', width=3),
                marker=dict(size=8, color='#4ecdc4')
            ),
            row=2, col=1
        )
        
        # 4. 健康评分指示器
        health_score = portfolio_data['health_score']
        fig.add_trace(
            go.Indicator(
                mode="gauge+number",
                value=health_score,
                domain={'x': [0, 1], 'y': [0, 1]},
                gauge={
                    'axis': {'range': [None, 100]},
                    'bar': {'color': "#4ecdc4"},
                    'steps': [
                        {'range': [0, 50], 'color': "rgba(255, 118, 117, 0.3)"},
                        {'range': [50, 80], 'color': "rgba(253, 203, 110, 0.3)"},
                        {'range': [80, 100], 'color': "rgba(0, 184, 148, 0.3)"}
                    ]
                }
            ),
            row=2, col=2
        )
        
        fig.update_layout(
            title_text="<b>投资组合健康仪表板</b>",
            title_font=dict(size=20, color='white'),
            paper_bgcolor="rgba(0,0,0,0)",
            plot_bgcolor="rgba(0,0,0,0)",
            font=dict(color='white'),
            height=600
        )
        
        return fig
    
    def create_ai_confidence_breakdown(self, confidence_factors: Dict) -> go.Figure:
        """创建AI置信度分解图"""
        
        categories = list(confidence_factors.keys())
        values = list(confidence_factors.values())
        
        # 创建水平条形图
        fig = go.Figure(go.Bar(
            y=categories,
            x=values,
            orientation='h',
            marker=dict(
                color=values,
                colorscale='Viridis',
                showscale=True,
                colorbar=dict(
                    title="置信度",
                    title_font=dict(color='white'),
                    tickfont=dict(color='white')
                )
            ),
            text=[f'{v:.1%}' for v in values],
            textposition='inside',
            textfont=dict(color='white', size=12)
        ))
        
        fig.update_layout(
            title=dict(
                text="<b>AI决策置信度分解</b>",
                font=dict(size=18, color='white'),
                x=0.5
            ),
            xaxis=dict(
                title="置信度",
                titlefont=dict(color='white'),
                tickfont=dict(color='white'),
                gridcolor='rgba(255,255,255,0.3)',
                range=[0, 1]
            ),
            yaxis=dict(
                titlefont=dict(color='white'),
                tickfont=dict(color='white')
            ),
            paper_bgcolor="rgba(0,0,0,0)",
            plot_bgcolor="rgba(0,0,0,0)",
            height=400
        )
        
        return fig
    
    def render_visualization_dashboard(self):
        """渲染完整的可视化仪表板"""
        st.markdown("""
        <style>
        .dashboard-container {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 20px;
            padding: 20px;
            margin: 10px 0;
        }
        
        .metric-card {
            background: rgba(255,255,255,0.1);
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
        }
        
        .metric-value {
            font-size: 2.5rem;
            font-weight: bold;
            color: #4ecdc4;
        }
        
        .metric-label {
            font-size: 1rem;
            color: rgba(255,255,255,0.8);
            margin-top: 5px;
        }
        </style>
        """, unsafe_allow_html=True)
        
        # 示例数据
        sample_data = self._get_sample_data()
        
        # 第一行：核心指标
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            st.markdown("""
            <div class="metric-card">
                <div class="metric-value">85%</div>
                <div class="metric-label">AI置信度</div>
            </div>
            """, unsafe_allow_html=True)
        
        with col2:
            st.markdown("""
            <div class="metric-card">
                <div class="metric-value">买入</div>
                <div class="metric-label">推荐决策</div>
            </div>
            """, unsafe_allow_html=True)
        
        with col3:
            st.markdown("""
            <div class="metric-card">
                <div class="metric-value">中等</div>
                <div class="metric-label">风险等级</div>
            </div>
            """, unsafe_allow_html=True)
        
        with col4:
            st.markdown("""
            <div class="metric-card">
                <div class="metric-value">12%</div>
                <div class="metric-label">预期收益</div>
            </div>
            """, unsafe_allow_html=True)
        
        # 第二行：主要图表
        col1, col2 = st.columns(2)
        
        with col1:
            st.plotly_chart(
                self.create_decision_confidence_gauge(0.85, "买入建议"),
                use_container_width=True
            )
        
        with col2:
            st.plotly_chart(
                self.create_market_emotion_radar(sample_data['emotions']),
                use_container_width=True
            )
        
        # 第三行：详细分析
        st.plotly_chart(
            self.create_risk_return_bubble(sample_data['assets']),
            use_container_width=True
        )
        
        # 第四行：时间线和置信度分解
        col1, col2 = st.columns(2)
        
        with col1:
            st.plotly_chart(
                self.create_news_impact_timeline(sample_data['news_events']),
                use_container_width=True
            )
        
        with col2:
            st.plotly_chart(
                self.create_ai_confidence_breakdown(sample_data['confidence_factors']),
                use_container_width=True
            )
    
    def _get_sample_data(self) -> Dict:
        """获取示例数据"""
        return {
            'emotions': {
                'rational': 0.7,
                'conservative': 0.5,
                'experienced': 0.8,
                'technical': 0.6,
                'value': 0.7,
                'trend': 0.4,
                'news': 0.6,
                'contrarian': 0.3
            },
            'assets': [
                {'name': '苹果', 'return': 15, 'risk': 12, 'market_cap': 50000, 'sentiment': 0.8},
                {'name': '特斯拉', 'return': 25, 'risk': 28, 'market_cap': 30000, 'sentiment': 0.6},
                {'name': '微软', 'return': 12, 'risk': 8, 'market_cap': 60000, 'sentiment': 0.9},
                {'name': '腾讯', 'return': 8, 'risk': 15, 'market_cap': 40000, 'sentiment': 0.4},
                {'name': '阿里', 'return': 10, 'risk': 20, 'market_cap': 35000, 'sentiment': 0.3}
            ],
            'news_events': [
                {'date': '2024-01-01', 'impact': 0.5, 'title': '央行降准释放流动性', 'sentiment': 0.7},
                {'date': '2024-01-05', 'impact': -0.3, 'title': '地缘政治紧张', 'sentiment': -0.5},
                {'date': '2024-01-10', 'impact': 0.8, 'title': 'AI技术重大突破', 'sentiment': 0.9},
                {'date': '2024-01-15', 'impact': -0.6, 'title': '通胀数据超预期', 'sentiment': -0.7},
                {'date': '2024-01-20', 'impact': 0.4, 'title': '企业财报超预期', 'sentiment': 0.6}
            ],
            'confidence_factors': {
                '技术分析': 0.8,
                '基本面分析': 0.7,
                '市场情绪': 0.6,
                '新闻影响': 0.9,
                '历史数据': 0.8,
                '专家观点': 0.7
            }
        }


def main():
    """主函数"""
    st.set_page_config(
        page_title="革命性数据可视化",
        page_icon="📊",
        layout="wide"
    )
    
    viz = RevolutionaryVisualization()
    
    st.markdown("# 🔥 炼妖壶AI - 革命性数据可视化")
    st.markdown("---")
    
    viz.render_visualization_dashboard()


if __name__ == "__main__":
    main()
