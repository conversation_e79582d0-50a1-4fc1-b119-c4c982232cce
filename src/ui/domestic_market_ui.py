# -*- coding: utf-8 -*-
"""
内盘市场数据展示界面
支持A股、期货等国内市场数据的实时展示和分析
"""

import streamlit as st
import pandas as pd
import plotly.graph_objects as go
import plotly.express as px
from datetime import datetime, timedelta
import asyncio
from typing import Dict, List, Any, Optional

from ..data.domestic_providers import TushareDataProvider, AkshareDataProvider, DomesticFuturesProvider
from ..data.data_service import DataRequest


class DomesticMarketUI:
    """内盘市场UI界面"""
    
    def __init__(self):
        self.tushare_provider = TushareDataProvider()
        self.akshare_provider = AkshareDataProvider()
        self.futures_provider = DomesticFuturesProvider()
    
    def render(self):
        """渲染主界面"""
        st.title("🇨🇳 内盘市场数据中心")
        
        # 侧边栏配置
        with st.sidebar:
            st.header("数据源配置")
            
            # 数据源选择
            data_source = st.selectbox(
                "选择数据源",
                ["akshare", "tushare", "domestic_futures"],
                format_func=lambda x: {
                    "akshare": "AkShare (免费)",
                    "tushare": "Tushare (需Token)",
                    "domestic_futures": "期货数据"
                }[x]
            )
            
            # Tushare Token配置
            if data_source == "tushare":
                tushare_token = st.text_input(
                    "Tushare Token",
                    type="password",
                    help="请输入您的Tushare Pro Token"
                )
                if tushare_token:
                    self.tushare_provider = TushareDataProvider(tushare_token)
            
            # 刷新间隔
            refresh_interval = st.slider("刷新间隔(秒)", 5, 60, 30)
            
            # 自动刷新开关
            auto_refresh = st.checkbox("自动刷新", value=False)
        
        # 主要内容区域
        tab1, tab2, tab3, tab4 = st.tabs(["📈 A股实时", "📊 期货行情", "🔍 个股分析", "📋 自选股"])
        
        with tab1:
            self._render_a_stock_realtime(data_source)
        
        with tab2:
            self._render_futures_data()
        
        with tab3:
            self._render_stock_analysis()
        
        with tab4:
            self._render_watchlist()
        
        # 自动刷新逻辑
        if auto_refresh:
            st.rerun()
    
    def _render_a_stock_realtime(self, data_source: str):
        """渲染A股实时数据"""
        st.subheader("A股实时行情")
        
        # 数据获取按钮
        col1, col2, col3 = st.columns([1, 1, 2])
        
        with col1:
            if st.button("🔄 刷新数据", key="refresh_a_stock"):
                with st.spinner("正在获取A股数据..."):
                    stock_data = self._fetch_a_stock_data(data_source)
                    if stock_data:
                        st.session_state['a_stock_data'] = stock_data
        
        with col2:
            show_limit_stocks = st.checkbox("仅显示涨跌停", key="show_limit")
        
        with col3:
            search_keyword = st.text_input("搜索股票", placeholder="输入股票代码或名称")
        
        # 显示数据
        if 'a_stock_data' in st.session_state:
            df = pd.DataFrame([stock.__dict__ for stock in st.session_state['a_stock_data']])
            
            if not df.empty:
                # 筛选涨跌停股票
                if show_limit_stocks:
                    df = df[(df.get('limit_up', False)) | (df.get('limit_down', False))]
                
                # 搜索筛选
                if search_keyword:
                    df = df[df['symbol'].str.contains(search_keyword, case=False, na=False)]
                
                # 数据展示
                st.dataframe(
                    df[['symbol', 'price', 'change_percent', 'volume', 'turnover_rate']],
                    column_config={
                        "symbol": "股票代码",
                        "price": st.column_config.NumberColumn("最新价", format="%.2f"),
                        "change_percent": st.column_config.NumberColumn(
                            "涨跌幅(%)", 
                            format="%.2f",
                            help="涨跌幅百分比"
                        ),
                        "volume": st.column_config.NumberColumn("成交量"),
                        "turnover_rate": st.column_config.NumberColumn("换手率(%)", format="%.2f")
                    },
                    use_container_width=True
                )
                
                # 市场概况
                self._render_market_overview(df)
            else:
                st.info("暂无数据，请点击刷新按钮获取最新行情")
        else:
            st.info("请点击刷新按钮获取A股实时数据")
    
    def _render_futures_data(self):
        """渲染期货数据"""
        st.subheader("期货实时行情")
        
        col1, col2 = st.columns([1, 3])
        
        with col1:
            if st.button("🔄 刷新期货数据", key="refresh_futures"):
                with st.spinner("正在获取期货数据..."):
                    futures_data = self._fetch_futures_data()
                    if futures_data:
                        st.session_state['futures_data'] = futures_data
        
        with col2:
            exchange_filter = st.multiselect(
                "选择交易所",
                ["CFFEX", "SHFE", "DCE", "CZCE"],
                default=["CFFEX", "SHFE"]
            )
        
        # 显示期货数据
        if 'futures_data' in st.session_state:
            df = pd.DataFrame(st.session_state['futures_data'])
            
            if not df.empty:
                # 交易所筛选
                if exchange_filter:
                    df = df[df.get('exchange', '').isin(exchange_filter)]
                
                st.dataframe(
                    df,
                    column_config={
                        "symbol": "合约代码",
                        "name": "合约名称",
                        "price": st.column_config.NumberColumn("最新价", format="%.2f"),
                        "change_percent": st.column_config.NumberColumn("涨跌幅(%)", format="%.2f"),
                        "volume": st.column_config.NumberColumn("成交量"),
                        "open_interest": st.column_config.NumberColumn("持仓量"),
                        "exchange": "交易所"
                    },
                    use_container_width=True
                )
            else:
                st.info("暂无期货数据")
        else:
            st.info("请点击刷新按钮获取期货数据")
    
    def _render_stock_analysis(self):
        """渲染个股分析"""
        st.subheader("个股深度分析")
        
        # 股票选择
        stock_symbol = st.text_input("输入股票代码", placeholder="例如: 000001")
        
        if stock_symbol and st.button("📊 开始分析", key="analyze_stock"):
            with st.spinner(f"正在分析 {stock_symbol}..."):
                analysis_result = self._analyze_individual_stock(stock_symbol)
                
                if analysis_result:
                    # 基本信息
                    col1, col2, col3, col4 = st.columns(4)
                    
                    with col1:
                        st.metric("最新价", f"{analysis_result['price']:.2f}")
                    
                    with col2:
                        st.metric(
                            "涨跌幅", 
                            f"{analysis_result['change_percent']:.2f}%",
                            delta=f"{analysis_result['change']:.2f}"
                        )
                    
                    with col3:
                        st.metric("成交量", f"{analysis_result['volume']:,}")
                    
                    with col4:
                        st.metric("换手率", f"{analysis_result.get('turnover_rate', 0):.2f}%")
                    
                    # 技术分析图表
                    self._render_technical_chart(stock_symbol, analysis_result)
                    
                    # AI分析结果
                    if 'ai_analysis' in analysis_result:
                        st.subheader("🤖 AI分析结果")
                        st.write(analysis_result['ai_analysis'])
                else:
                    st.error("获取股票数据失败，请检查股票代码")
    
    def _render_watchlist(self):
        """渲染自选股"""
        st.subheader("自选股管理")
        
        # 添加自选股
        col1, col2 = st.columns([2, 1])
        
        with col1:
            new_stock = st.text_input("添加自选股", placeholder="输入股票代码")
        
        with col2:
            if st.button("➕ 添加") and new_stock:
                if 'watchlist' not in st.session_state:
                    st.session_state['watchlist'] = []
                
                if new_stock not in st.session_state['watchlist']:
                    st.session_state['watchlist'].append(new_stock)
                    st.success(f"已添加 {new_stock} 到自选股")
                else:
                    st.warning(f"{new_stock} 已在自选股中")
        
        # 显示自选股列表
        if 'watchlist' in st.session_state and st.session_state['watchlist']:
            st.write("**当前自选股:**")
            
            for i, stock in enumerate(st.session_state['watchlist']):
                col1, col2, col3 = st.columns([2, 1, 1])
                
                with col1:
                    st.write(f"📈 {stock}")
                
                with col2:
                    if st.button("📊", key=f"analyze_{i}", help="分析"):
                        st.session_state['selected_stock'] = stock
                
                with col3:
                    if st.button("🗑️", key=f"remove_{i}", help="删除"):
                        st.session_state['watchlist'].remove(stock)
                        st.rerun()
        else:
            st.info("暂无自选股，请添加关注的股票")
    
    def _render_market_overview(self, df: pd.DataFrame):
        """渲染市场概况"""
        st.subheader("📊 市场概况")
        
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            up_count = len(df[df['change_percent'] > 0])
            st.metric("上涨家数", up_count)
        
        with col2:
            down_count = len(df[df['change_percent'] < 0])
            st.metric("下跌家数", down_count)
        
        with col3:
            limit_up_count = len(df[df.get('limit_up', False)])
            st.metric("涨停家数", limit_up_count)
        
        with col4:
            limit_down_count = len(df[df.get('limit_down', False)])
            st.metric("跌停家数", limit_down_count)
        
        # 涨跌分布图
        if not df.empty:
            fig = px.histogram(
                df, 
                x='change_percent', 
                nbins=50,
                title="涨跌幅分布",
                labels={'change_percent': '涨跌幅(%)', 'count': '股票数量'}
            )
            st.plotly_chart(fig, use_container_width=True)
    
    def _render_technical_chart(self, symbol: str, data: Dict[str, Any]):
        """渲染技术分析图表"""
        st.subheader(f"📈 {symbol} 技术分析")
        
        # 这里可以添加K线图、技术指标等
        # 由于需要历史数据，这里提供一个框架
        
        st.info("技术分析图表功能开发中，将支持：")
        st.write("- K线图")
        st.write("- 移动平均线")
        st.write("- MACD指标")
        st.write("- RSI指标")
        st.write("- 成交量分析")
    
    async def _fetch_a_stock_data(self, data_source: str):
        """获取A股数据"""
        try:
            if data_source == "akshare" and self.akshare_provider.is_available():
                request = DataRequest(endpoint="stock_zh_a_spot_em", params={})
                response = await self.akshare_provider.fetch_data(request)
                return response.data if response.success else None
            
            elif data_source == "tushare" and self.tushare_provider.is_available():
                request = DataRequest(
                    endpoint="realtime_data", 
                    params={"symbols": ["000001.SZ", "000002.SZ", "600000.SH"]}
                )
                response = await self.tushare_provider.fetch_data(request)
                return response.data if response.success else None
            
            return None
            
        except Exception as e:
            st.error(f"获取数据失败: {e}")
            return None
    
    async def _fetch_futures_data(self):
        """获取期货数据"""
        try:
            request = DataRequest(endpoint="futures_main_contracts", params={})
            response = await self.futures_provider.fetch_data(request)
            return response.data if response.success else None
            
        except Exception as e:
            st.error(f"获取期货数据失败: {e}")
            return None
    
    def _analyze_individual_stock(self, symbol: str) -> Optional[Dict[str, Any]]:
        """分析个股"""
        # 这里可以集成AI分析引擎
        # 返回模拟数据
        return {
            "symbol": symbol,
            "price": 10.50,
            "change": 0.25,
            "change_percent": 2.44,
            "volume": 1500000,
            "turnover_rate": 3.2,
            "ai_analysis": "该股票技术面偏强，建议关注..."
        }
