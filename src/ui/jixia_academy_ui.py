# -*- coding: utf-8 -*-
"""
稷下学宫AI辩论系统 - Streamlit界面
整合到主应用中的AI辩论功能
"""

import streamlit as st
import asyncio
import json
from datetime import datetime
from typing import Dict, List, Optional
import sys
import os
from pathlib import Path

# 导入新的streamlit辩论系统
try:
    from src.core.streamlit_debate_system import StreamlitDebateSystem
    JIXIA_AVAILABLE = True
except ImportError:
    JIXIA_AVAILABLE = False
    st.warning("⚠️ 稷下学宫模块未找到，请检查辩论系统配置")

class JixiaAcademyUI:
    """稷下学宫AI辩论系统界面"""
    
    def __init__(self):
        self.debate_system = None
        if JIXIA_AVAILABLE:
            try:
                self.debate_system = StreamlitDebateSystem()
            except Exception as e:
                st.error(f"初始化稷下学宫失败: {e}")
    
    def run(self):
        """运行稷下学宫界面"""
        st.title("🏛️ 稷下学宫 - AI辩论系统")
        st.markdown("---")
        
        if not JIXIA_AVAILABLE or not self.debate_system:
            self._show_unavailable_message()
            return
        
        # 侧边栏配置
        with st.sidebar:
            self._render_sidebar()
        
        # 主界面
        self._render_main_interface()
    
    def _show_unavailable_message(self):
        """显示不可用消息"""
        st.error("🚫 稷下学宫AI辩论系统暂时不可用")
        st.info("""
        **可能的原因：**
        1. jixia_academy模块未正确安装
        2. 缺少必要的API密钥配置
        3. 依赖包未安装完整
        
        **解决方案：**
        1. 检查streamlit辩论系统是否正确安装
        2. 配置OpenRouter API密钥
        3. 确保autogen相关依赖已安装
        """)
    
    def _render_sidebar(self):
        """渲染侧边栏"""
        st.header("⚙️ 辩论配置")
        
        # 辩论主题输入
        st.subheader("📝 辩论主题")
        topic = st.text_area(
            "请输入辩论主题",
            value="比特币是否应该成为法定货币？",
            height=100,
            help="输入您想要八仙辩论的主题",
            key="jixia_academy_debate_topic_unique_2025_07_02"
        )
        
        # 辩论参数
        st.subheader("🎛️ 辩论参数")
        max_rounds = st.slider("最大轮数", 1, 10, 3)
        time_limit = st.slider("每轮时间限制(秒)", 30, 300, 120)
        
        # 参与者选择
        st.subheader("👥 参与者")
        st.info("""
        **八仙辩论阵容：**
        - 🎤 **灵宝道君** - 主持人
        - ⚡ **吕洞宾** (乾·天) - 正方一辩
        - 🌍 **何仙姑** (坤·地) - 反方一辩
        - 🌊 **张果老** (兑·泽) - 正方二辩
        - ⛰️ **韩湘子** (艮·山) - 反方二辩
        - 🔥 **汉钟离** (离·火) - 正方三辩
        - 💧 **蓝采和** (坎·水) - 反方三辩
        - ⚡ **曹国舅** (震·雷) - 正方四辩
        - 🌪️ **铁拐李** (巽·风) - 反方四辩
        """)
        
        # 开始辩论按钮
        if st.button("🚀 开始辩论", type="primary", use_container_width=True):
            if topic.strip():
                st.session_state.debate_topic = topic
                st.session_state.debate_config = {
                    'max_rounds': max_rounds,
                    'time_limit': time_limit
                }
                st.session_state.start_debate = True
                st.rerun()
            else:
                st.error("请输入辩论主题！")
    
    def _render_main_interface(self):
        """渲染主界面"""
        # 检查是否开始辩论
        if st.session_state.get('start_debate', False):
            self._run_debate()
        else:
            self._show_welcome_page()
    
    def _show_welcome_page(self):
        """显示欢迎页面"""
        col1, col2, col3 = st.columns([1, 2, 1])
        
        with col2:
            st.markdown("""
            <div style="text-align: center; padding: 2rem;">
                <h2>🎯 欢迎来到稷下学宫</h2>
                <p style="font-size: 1.2rem; color: #666;">基于八卦序列的AI辩论平台</p>
            </div>
            """, unsafe_allow_html=True)
        
        # 功能介绍
        st.subheader("✨ 系统特色")
        
        col1, col2 = st.columns(2)
        
        with col1:
            st.markdown("""
            **🎭 八仙辩论阵容**
            - 采用传统八卦序列排列
            - 每位仙人代表不同观点
            - 专业主持人引导辩论
            
            **🧠 AI智能分析**
            - 多模型协同辩论
            - 实时观点碰撞
            - 深度逻辑推理
            """)
        
        with col2:
            st.markdown("""
            **📊 辩论可视化**
            - 实时辩论进程展示
            - 观点立场分析
            - 辩论质量评估
            
            **🎯 应用场景**
            - 投资决策分析
            - 商业策略讨论
            - 学术观点辩论
            """)
        
        # 历史辩论
        st.subheader("📚 历史辩论")
        self._show_debate_history()
    
    def _show_debate_history(self):
        """显示辩论历史"""
        # 模拟历史辩论数据
        history_data = [
            {
                "时间": "2024-01-15 14:30",
                "主题": "人工智能是否会取代人类工作？",
                "轮数": 5,
                "胜方": "正方",
                "质量评分": 8.5
            },
            {
                "时间": "2024-01-14 10:15",
                "主题": "比特币是否应该成为法定货币？",
                "轮数": 4,
                "胜方": "反方",
                "质量评分": 7.8
            },
            {
                "时间": "2024-01-13 16:45",
                "主题": "远程工作是否比现场工作更有效？",
                "轮数": 3,
                "胜方": "正方",
                "质量评分": 9.2
            }
        ]
        
        if history_data:
            df = pd.DataFrame(history_data)
            st.dataframe(df, use_container_width=True)
        else:
            st.info("暂无历史辩论记录")
    
    def _run_debate(self):
        """运行辩论"""
        topic = st.session_state.get('debate_topic', '')
        config = st.session_state.get('debate_config', {})
        
        st.subheader(f"🎯 辩论主题: {topic}")
        
        # 初始化辩论
        if 'debate_initialized' not in st.session_state:
            self.debate_system.start_debate(topic, config.get('max_rounds', 3))
            st.session_state.debate_initialized = True
            st.session_state.debate_messages = []
        
        # 辩论进度
        progress_container = st.container()
        
        # 辩论内容显示区域
        debate_container = st.container()
        
        # 控制按钮
        col1, col2, col3, col4 = st.columns([1, 1, 1, 1])
        
        with col1:
            if st.button("▶️ 下一发言", disabled=not self.debate_system.is_running):
                st.session_state.next_speaker = True
        
        with col2:
            if st.button("⏸️ 暂停辩论"):
                st.session_state.debate_paused = True
        
        with col3:
            if st.button("▶️ 继续辩论"):
                st.session_state.debate_paused = False
        
        with col4:
            if st.button("🛑 结束辩论"):
                self.debate_system.stop_debate()
                st.session_state.start_debate = False
                st.session_state.debate_finished = True
                st.session_state.debate_initialized = False
                st.rerun()
        
        # 处理下一发言
        if st.session_state.get('next_speaker', False):
            st.session_state.next_speaker = False
            with st.spinner("AI正在思考中..."):
                try:
                    # 这里需要异步处理，但streamlit不直接支持
                    # 使用同步方式模拟
                    import asyncio
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)
                    next_message = loop.run_until_complete(self.debate_system.next_speaker())
                    if next_message:
                        st.session_state.debate_messages.append(next_message)
                    loop.close()
                except Exception as e:
                    st.error(f"生成发言时出错: {e}")
        
        # 显示辩论内容
        with debate_container:
            self._display_debate_content()
        
        # 显示辩论统计
        with progress_container:
            self._display_debate_stats()
    
    def _display_debate_content(self):
        """显示辩论内容"""
        st.markdown("### 🎭 辩论实况")
        
        # 显示辩论历史
        all_messages = self.debate_system.debate_history + st.session_state.get('debate_messages', [])
        
        if not all_messages:
            st.info("辩论尚未开始，请点击'下一发言'开始辩论。")
            return
        
        for msg in all_messages:
            # 根据立场选择头像
            if "灵宝道君" in msg['speaker']:
                avatar = "🎤"
            elif msg.get('position') == "正方":
                avatar = "⚡"
            elif msg.get('position') == "反方":
                avatar = "🌊"
            else:
                avatar = "🎭"
            
            with st.chat_message("assistant", avatar=avatar):
                st.markdown(f"**{msg['speaker']}** ({msg['timestamp']})")
                st.markdown(msg['content'])
        
        # 辩论状态提示
        if self.debate_system.is_running:
            st.info("💡 辩论正在进行中，点击'下一发言'继续辩论。")
        else:
            st.success("🎉 辩论已结束！")
    
    def _display_debate_stats(self):
        """显示辩论统计"""
        stats = self.debate_system.get_debate_stats()
        
        if not stats:
            return
        
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            st.metric("当前轮数", f"{stats.get('current_round', 0)}/{stats.get('max_rounds', 3)}")
        
        with col2:
            st.metric("发言次数", stats.get('total_messages', 0))
        
        with col3:
            st.metric("正方发言", stats.get('pro_statements', 0))
        
        with col4:
            st.metric("反方发言", stats.get('con_statements', 0))

# 导入pandas用于数据展示
import pandas as pd