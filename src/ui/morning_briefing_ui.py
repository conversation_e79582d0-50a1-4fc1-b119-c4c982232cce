# -*- coding: utf-8 -*-
"""
太公心易BI系统 - 晨会简报Web界面
为会员提供每日投资晨会报告的Web访问界面
"""

import streamlit as st
import json
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
import plotly.express as px
import plotly.graph_objects as go
from pathlib import Path

# 导入系统模块
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.core.member_system import MemberSystem, MembershipLevel
from src.core.analysis_engine import StockAnalysisEngine
from src.core.config_manager import ConfigManager
from src.utils.monitoring import monitor_performance

# 可选导入JixiaAcademyUI
try:
    from src.ui.jixia_academy_ui import JixiaAcademyUI
except ImportError:
    JixiaAcademyUI = None

# 初始化会员系统
@st.cache_resource
def get_member_system():
    return MemberSystem()

def show_login_page():
    """显示登录页面"""
    st.title("🏛️ 太公心易BI系统 - 晨会简报")
    st.markdown("---")
    
    col1, col2, col3 = st.columns([1, 2, 1])
    
    with col2:
        st.subheader("🔐 会员登录")
        
        with st.form("login_form"):
            username = st.text_input("用户名", placeholder="请输入用户名")
            password = st.text_input("密码", type="password", placeholder="请输入密码")
            
            col_login, col_guest = st.columns(2)
            
            with col_login:
                login_button = st.form_submit_button("🚀 登录", use_container_width=True)
            
            with col_guest:
                guest_button = st.form_submit_button("👤 游客登录", use_container_width=True)
        
        # 处理登录
        if login_button and username and password:
            member_system = get_member_system()
            member = member_system.authenticate_member(username, password)
            
            if member:
                st.session_state.logged_in = True
                st.session_state.current_member = member
                st.success(f"✅ 欢迎回来，{member.username}!")
                st.rerun()
            else:
                st.error("❌ 用户名或密码错误")
        
        # 处理游客登录
        if guest_button:
            member_system = get_member_system()
            guest_member = member_system.authenticate_member('guest', 'guest')
            
            if guest_member:
                st.session_state.logged_in = True
                st.session_state.current_member = guest_member
                st.success("✅ 以游客身份登录成功!")
                st.rerun()
            else:
                st.error("❌ 游客登录失败，请联系管理员")
        
        # 显示提示信息
        st.info("💡 **提示**: 可以使用游客账号体验系统功能\n\n" +
                "🔹 游客账号: guest / guest\n\n" +
                "🔹 游客可查看基础晨会报告\n\n" +
                "🔹 升级会员可享受更多功能")
        
        # 显示功能说明
        st.markdown("### 📊 系统功能")
        st.markdown("""
        - **📈 昨日复盘**: 查看前一交易日的市场表现和分析
        - **📋 今日排行**: 实时股票排行和推荐
        - **🤖 AI分析**: 基于人工智能的投资建议
        - **⚠️ 风险提示**: 及时的市场风险警告
        """)

def show_member_info():
    """显示会员信息"""
    if 'current_member' in st.session_state:
        member = st.session_state.current_member
        member_system = get_member_system()
        permissions = member_system.get_member_permissions(member)
        
        with st.sidebar:
            st.markdown("### 👤 会员信息")
            st.write(f"**用户名**: {member.username}")
            st.write(f"**会员等级**: {member.membership_level.value}")
            
            if member.subscription_expires:
                st.write(f"**订阅到期**: {member.subscription_expires.strftime('%Y-%m-%d')}")
            
            st.markdown("### 🔑 权限状态")
            permission_icons = {
                'view_daily_report': '📊',
                'view_ai_analysis': '🤖',
                'view_detailed_reasoning': '🔍',
                'access_historical_reports': '📚',
                'export_reports': '💾',
                'real_time_alerts': '🚨',
                'premium_stocks': '⭐',
                'api_access': '🔌'
            }
            
            permission_names = {
                'view_daily_report': '查看日报',
                'view_ai_analysis': 'AI分析',
                'view_detailed_reasoning': '详细推理',
                'access_historical_reports': '历史报告',
                'export_reports': '导出报告',
                'real_time_alerts': '实时提醒',
                'premium_stocks': '优质股票',
                'api_access': 'API访问'
            }
            
            for perm, enabled in permissions.items():
                icon = permission_icons.get(perm, '🔹')
                name = permission_names.get(perm, perm)
                status = "✅" if enabled else "❌"
                st.write(f"{icon} {name}: {status}")
            
            if st.button("🚪 退出登录"):
                for key in ['logged_in', 'current_member']:
                    if key in st.session_state:
                        del st.session_state[key]
                st.rerun()

# 临时简化的DataPipeline类，用于演示
class DataPipeline:
    def __init__(self):
        self.analysis_engine = StockAnalysisEngine()
    
    async def run_daily_analysis(self):
        return {
            'market_summary': {'total_analyzed': 100, 'gainers': 45},
            'market_outlook': '市场整体表现良好，科技股领涨',
            'ai_recommendations': {'buy_signals': 15, 'sell_signals': 8, 'hold_signals': 77},
            'featured_analysis': [
                {
                    'symbol': 'AAPL',
                    'recommendation': 'buy',
                    'confidence': '85%',
                    'time_horizon': '短期',
                    'reasoning': '技术指标显示强势突破，基本面支撑良好'
                }
            ],
            'risk_warning': '市场波动加大，注意风险控制',
            'system_info': {'version': '1.0', 'last_update': '2024-01-20'}
        }
    
    async def fetch_market_data(self, symbols):
        # 模拟股票数据
        class StockData:
            def __init__(self, symbol):
                self.symbol = symbol
                self.price = 150.0
                self.change = 2.5
                self.change_percent = 1.67
                self.volume = 1000000
        
        return [StockData(symbol) for symbol in symbols]

class MorningBriefingUI:
    """晨会简报Web界面"""
    
    def __init__(self):
        self.member_system = MemberSystem()
        self.data_pipeline = DataPipeline()
        self.config_manager = ConfigManager()
        
        # 页面配置已移至app.py主程序中设置
    
    @monitor_performance
    def run(self):
        """运行Web界面"""
        # 主界面
        if 'logged_in' not in st.session_state:
            st.session_state.logged_in = False
        
        if st.session_state.logged_in:
            # 侧边栏 - 用户登录
            self._render_sidebar()
            # 显示会员信息
            show_member_info()
            self._render_main_interface()
        else:
            show_login_page()
    
    def render(self):
        """渲染晨会简报界面"""
        # 检查登录状态
        if not st.session_state.get('logged_in', False):
            show_login_page()
            return
        
        # 显示会员信息
        show_member_info()
        
        # 渲染侧边栏
        self._render_sidebar()
        
        # 渲染主界面
        self._render_main_interface()
    
    def _render_sidebar(self):
        """渲染侧边栏"""
        st.sidebar.title("🏛️ 太公心易BI系统")
        st.sidebar.markdown("### 炼股葫芦 × 八仙论道")
        
        # 显示当前时间
        current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        st.sidebar.markdown(f"**当前时间**: {current_time}")
        
        # 显示系统状态
        st.sidebar.markdown("### 📊 系统状态")
        st.sidebar.success("🟢 系统运行正常")
        st.sidebar.info("📈 数据已更新")
        
        # 快速导航
        st.sidebar.markdown("### 🧭 快速导航")
        if st.sidebar.button("📈 昨日复盘", use_container_width=True):
            st.session_state.active_tab = "昨日复盘"
            st.rerun()
        if st.sidebar.button("🏆 封神榜", use_container_width=True):
            st.session_state.active_tab = "封神榜"
            st.rerun()
        if st.sidebar.button("📋 今日排行", use_container_width=True):
            st.session_state.active_tab = "今日排行"
            st.rerun()
    
    def _render_login_page(self):
        """渲染登录页面（已弃用，使用show_login_page）"""
        show_login_page()
    
    def _render_main_interface(self):
        """渲染主界面"""
        member = st.session_state.current_member
        
        st.title("📊 太公心易BI系统 - 每日投资晨会")
        st.markdown(f"**欢迎，{member.username}** | 会员等级：{member.membership_level.value}")
        
        # 主导航
        tab1, tab2, tab3, tab5, tab6, tab7, tab8 = st.tabs(["📋 今日晨会", "🔮 妖股识别", "🔄 昨日复盘", "📈 历史报告", "🏛️ 稷下学宫", "⚙️ 系统状态", "🔄 手动分析"]) 

        
        with tab1:
            self._render_daily_report(member)
        
        with tab2:
            self._render_demon_stocks(member)
        
        with tab3:
            self._render_yesterday_review(member)
        

        
        with tab5:
            self._render_historical_reports(member)
        
        with tab6:
            self._render_jixia_academy(member)
        
        with tab7:
            self._render_system_status()
        
        with tab8:
            self._render_manual_analysis(member)
    
    @monitor_performance
    def _render_daily_report(self, member):
        """渲染今日晨会报告"""
        st.header("📋 今日投资晨会报告")
        
        today = datetime.now().strftime('%Y-%m-%d')
        report = self.member_system.get_daily_report(member, today)
        
        if not report:
            st.warning("今日报告尚未生成，请稍后查看或手动触发分析")
            
            if st.button("🚀 立即生成今日报告"):
                with st.spinner("正在生成报告，请稍候..."):
                    try:
                        # 运行分析流程
                        import asyncio
                        import threading
                        
                        def run_analysis():
                            loop = asyncio.new_event_loop()
                            asyncio.set_event_loop(loop)
                            try:
                                return loop.run_until_complete(self.data_pipeline.run_daily_analysis())
                            finally:
                                loop.close()
                        
                        # 在新线程中运行异步分析
                        pipeline_report = run_analysis()
                        
                        # 保存到会员系统
                        self.member_system.save_daily_report(today, pipeline_report)
                        
                        st.success("报告生成完成！")
                        st.rerun()
                    except Exception as e:
                        st.error(f"生成报告失败：{e}")
            return
        
        # 显示报告内容
        self._display_report_content(report, member)
    
    def _display_report_content(self, report: Dict[str, Any], member):
        """显示报告内容"""
        # 报告头部信息
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            st.metric("分析股票数", report.get('market_summary', {}).get('total_analyzed', 0))
        
        with col2:
            st.metric("上涨股票", report.get('market_summary', {}).get('gainers', 0))
        
        with col3:
            st.metric("下跌股票", report.get('market_summary', {}).get('losers', 0))
        
        with col4:
            ai_enabled = report.get('ai_analysis_enabled', False)
            st.metric("AI分析", "✅ 已启用" if ai_enabled else "❌ 未启用")
        
        # 市场概览
        st.subheader("📊 市场概览")
        st.info(report.get('market_outlook', '暂无市场概览'))
        
        # AI推荐统计
        if 'ai_recommendations' in report:
            st.subheader("🎯 AI投资建议统计")
            
            rec_data = report['ai_recommendations']
            
            # 饼图显示推荐分布
            fig = px.pie(
                values=[rec_data.get('buy_signals', 0), rec_data.get('sell_signals', 0), rec_data.get('hold_signals', 0)],
                names=['买入', '卖出', '持有'],
                title="投资建议分布",
                color_discrete_map={'买入': '#00CC96', '卖出': '#EF553B', '持有': '#FFA15A'}
            )
            st.plotly_chart(fig, use_container_width=True, key="ai_recommendations_pie_chart")
        
        # 精选分析
        permissions = self.member_system.get_member_permissions(member)
        
        if 'featured_analysis' in report and report['featured_analysis']:
            st.subheader("⭐ 精选投资分析")
            
            for i, analysis in enumerate(report['featured_analysis']):
                with st.expander(f"📈 {analysis['symbol']} - {analysis['recommendation'].upper()}"):
                    col1, col2 = st.columns([2, 1])
                    
                    with col1:
                        st.markdown(f"**推荐操作：** {analysis['recommendation']}")
                        st.markdown(f"**置信度：** {analysis['confidence']}")
                        st.markdown(f"**投资期限：** {analysis['time_horizon']}")
                        
                        if permissions['view_detailed_reasoning']:
                            st.markdown(f"**分析理由：** {analysis['reasoning']}")
                        else:
                            st.markdown(f"**分析理由：** {analysis['reasoning'][:100]}...")
                            st.info("💎 升级会员以查看完整分析")
                    
                    with col2:
                        # 推荐操作的颜色标识
                        if analysis['recommendation'] == 'buy':
                            st.success("🟢 买入信号")
                        elif analysis['recommendation'] == 'sell':
                            st.error("🔴 卖出信号")
                        else:
                            st.warning("🟡 持有观望")
        
        # 风险提示
        st.subheader("⚠️ 风险提示")
        st.warning(report.get('risk_warning', '投资有风险，决策需谨慎。'))
        
        # 系统信息
        if 'system_info' in report:
            with st.expander("🔧 系统信息"):
                info = report['system_info']
                st.json(info)
    
    def _render_historical_reports(self, member):
        """渲染历史报告"""
        st.header("📈 历史投资报告")
        
        permissions = self.member_system.get_member_permissions(member)
        
        if not permissions['access_historical_reports']:
            st.warning("您的会员等级暂不支持查看历史报告，请升级会员")
            return
        
        # 日期选择
        days = st.slider("查看最近天数", 1, 30, 7)
        
        historical_reports = self.member_system.get_historical_reports(member, days)
        
        if not historical_reports:
            st.info("暂无历史报告数据")
            return
        
        # 历史趋势图
        dates = [report['date'] for report in historical_reports]
        buy_signals = [report['data'].get('ai_recommendations', {}).get('buy_signals', 0) for report in historical_reports]
        sell_signals = [report['data'].get('ai_recommendations', {}).get('sell_signals', 0) for report in historical_reports]
        
        fig = go.Figure()
        fig.add_trace(go.Scatter(x=dates, y=buy_signals, mode='lines+markers', name='买入信号', line=dict(color='green')))
        fig.add_trace(go.Scatter(x=dates, y=sell_signals, mode='lines+markers', name='卖出信号', line=dict(color='red')))
        fig.update_layout(title='历史投资信号趋势', xaxis_title='日期', yaxis_title='信号数量')
        
        st.plotly_chart(fig, use_container_width=True, key="historical_signals_trend_chart")
        
        # 历史报告列表
        st.subheader("📋 历史报告列表")
        
        for report in historical_reports:
            with st.expander(f"📅 {report['date']} 投资报告"):
                self._display_report_content(report['data'], member)
    
    def _render_system_status(self):
        """渲染系统状态"""
        st.header("⚙️ 系统运行状态")
        
        # 配置信息
        config = self.config_manager.get_all_config()
        
        col1, col2 = st.columns(2)
        
        with col1:
            st.subheader("🔧 系统配置")
            
            # 炼股葫芦状态
            st.markdown("#### 📊 炼股葫芦")
            penny_config = config['cauldron']
            st.success(f"✅ IB连接: {penny_config['ib_api']['host']}:{penny_config['ib_api']['port']}")
            st.info(f"📊 分析引擎: {'已启用' if penny_config['analysis_engine']['enabled'] else '未启用'}")
            
            # 八仙论道状态
            st.markdown("#### 🎭 八仙论道")
            jixia_config = config['jixia_academy']
            st.success(f"✅ AI分析: {'已启用' if jixia_config['enabled'] else '未启用'}")
            
            # 会员系统状态
            st.markdown("#### 👥 会员系统")
            member_stats = self.member_system.get_member_stats()
            st.metric("总会员数", member_stats.get('total_members', 0))
            st.metric("今日活跃", member_stats.get('daily_active_users', 0))
        
        with col2:
            st.subheader("📊 会员分布")
            
            if member_stats.get('level_distribution'):
                level_data = member_stats['level_distribution']
                fig = px.bar(
                    x=list(level_data.keys()),
                    y=list(level_data.values()),
                    title="会员等级分布",
                    labels={'x': '会员等级', 'y': '人数'}
                )
                st.plotly_chart(fig, use_container_width=True, key="member_level_distribution_chart")
    
    @monitor_performance
    def _render_demon_stocks(self, member):
        """渲染妖股识别界面"""
        st.header("🔮 太公心易妖股识别")
        
        # 获取会员权限
        permissions = self.member_system.get_member_permissions(member)
        
        if not permissions['view_ai_analysis']:
            st.warning("🔒 妖股识别功能需要高级会员权限")
            return
        
        # 妖股识别说明
        with st.expander("❓ 什么是妖股？"):
            st.markdown("""
            **妖股定义：**
            - 📊 **基本面特征**：IB基本面数据中，晨星没什么评级，分析师懒得研究，但在板块中不垫底
            - 📈 **技术特征**：使用CPA-MACD（累计价格行为MACD）捕捉日内行棋效率，缩小跳空缺口权重
            - 🔻 **做空特征**：做空数据明显增加，但下跌效率逐渐下降
            
            **CPA-MACD算法：**
            ```
            DCP_RATIO = 涨跌停板 ? (涨停=1, 跌停=-1) : (收盘-开盘)/(最高-最低)
            DCP_SUM = 累计(DCP_RATIO)
            DIF = EMA(DCP_SUM, 12) - EMA(DCP_SUM, 26)
            DEA = EMA(DIF, 9)
            MACD = (DIF - DEA) * 2
            ```
            """)
        
        # 实时妖股扫描
        st.subheader("🔍 实时妖股扫描")
        
        col1, col2 = st.columns([1, 3])
        
        with col1:
            scan_button = st.button("🚀 开始扫描", type="primary")
            
            # 扫描参数
            st.markdown("**扫描参数：**")
            confidence_threshold = st.slider("置信度阈值", 0.0, 1.0, 0.6, 0.1)
            market_filter = st.multiselect(
                "市场筛选", 
                ["美股", "港股", "A股", "加密货币"], 
                default=["美股", "港股"]
            )
        
        with col2:
            if scan_button:
                with st.spinner("🔮 正在扫描妖股..."):
                    # 模拟妖股扫描结果
                    demon_stocks = self._get_demon_stock_results(confidence_threshold, market_filter)
                    
                    if demon_stocks:
                        st.success(f"✅ 发现 {len(demon_stocks)} 只潜在妖股")
                        
                        # 妖股等级分布
                        level_counts = {}
                        for stock in demon_stocks:
                            level = stock['level']
                            level_counts[level] = level_counts.get(level, 0) + 1
                        
                        # 显示等级分布
                        st.markdown("**妖股等级分布：**")
                        for level, count in level_counts.items():
                            emoji = self._get_demon_level_emoji(level)
                            st.write(f"{emoji} {level}: {count}只")
                    else:
                        st.info("未发现符合条件的妖股")
        
        # 妖股榜单
        st.subheader("🏆 妖股榜单")
        
        # 模拟妖股数据
        demon_stocks = self._get_demon_stock_results(0.5, ["美股", "港股", "A股"])
        
        if demon_stocks:
            # 按等级分组显示
            levels = ["超级妖股", "确认妖股", "新兴妖股", "潜在妖股"]
            
            for level in levels:
                level_stocks = [s for s in demon_stocks if s['level'] == level]
                if level_stocks:
                    emoji = self._get_demon_level_emoji(level)
                    st.markdown(f"#### {emoji} {level} ({len(level_stocks)}只)")
                    
                    for stock in level_stocks:
                        with st.container():
                            col1, col2, col3, col4, col5 = st.columns([2, 1, 1, 1, 3])
                            
                            with col1:
                                st.write(f"**{stock['symbol']}**")
                                st.caption(stock['name'])
                            
                            with col2:
                                confidence_color = "green" if stock['confidence'] > 0.7 else "orange" if stock['confidence'] > 0.5 else "red"
                                st.markdown(f"<span style='color: {confidence_color}'>置信度: {stock['confidence']:.0%}</span>", unsafe_allow_html=True)
                            
                            with col3:
                                st.metric("CPA-MACD", f"{stock['cpa_macd']:.4f}")
                            
                            with col4:
                                st.metric("DCP比率", f"{stock['dcp_ratio']:.3f}")
                            
                            with col5:
                                st.write(f"**推理：** {stock['reasoning']}")
                                st.caption(f"⚠️ {stock['risk_warning']}")
                            
                            st.divider()
        
        # 妖股历史表现
        if permissions['view_detailed_reasoning']:
            st.subheader("📊 妖股历史表现分析")
            
            # 模拟历史数据
            import plotly.graph_objects as go
            import pandas as pd
            
            # 创建模拟的妖股表现数据
            dates = pd.date_range(start='2024-01-01', end='2024-01-30', freq='D')
            demon_performance = [5, 8, 12, 15, 18, 22, 25, 30, 28, 32, 35, 38, 42, 45, 48, 52, 55, 58, 62, 65, 68, 72, 75, 78, 82, 85, 88, 92, 95, 98]
            market_performance = [2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31]
            
            fig = go.Figure()
            fig.add_trace(go.Scatter(x=dates, y=demon_performance, mode='lines', name='妖股组合', line=dict(color='red', width=3)))
            fig.add_trace(go.Scatter(x=dates, y=market_performance, mode='lines', name='市场基准', line=dict(color='blue', width=2)))
            
            fig.update_layout(
                title='妖股识别组合 vs 市场基准表现',
                xaxis_title='日期',
                yaxis_title='累计收益率 (%)',
                hovermode='x unified'
            )
            
            st.plotly_chart(fig, use_container_width=True, key="demon_stocks_performance_chart")
            
            # 统计数据
            col1, col2, col3, col4 = st.columns(4)
            
            with col1:
                st.metric("妖股组合收益", "+98.0%", "+67.0%")
            
            with col2:
                st.metric("市场基准收益", "+31.0%", "+31.0%")
            
            with col3:
                st.metric("超额收益", "+67.0%", "+36.0%")
            
            with col4:
                st.metric("胜率", "73.3%", "+13.3%")
        
        else:
            st.info("🔒 妖股历史表现分析需要专业会员权限")
    
    def _get_demon_stock_results(self, confidence_threshold: float, markets: list) -> list:
        """获取妖股扫描结果（模拟数据）"""
        all_demon_stocks = [
            {
                'symbol': 'FFAI',
                'name': 'Faraday Future',
                'level': '超级妖股',
                'confidence': 0.92,
                'cpa_macd': 0.1847,
                'dcp_ratio': 0.756,
                'reasoning': 'CPA-MACD强势突破，做空数据激增但下跌效率下降，基本面符合妖股特征',
                'risk_warning': '极高风险：超级妖股波动巨大，建议1-2%仓位试探',
                'market': '美股'
            },
            {
                'symbol': 'SOUN',
                'name': 'SoundHound AI',
                'level': '确认妖股',
                'confidence': 0.85,
                'cpa_macd': 0.1234,
                'dcp_ratio': 0.623,
                'reasoning': 'AI概念热炒，技术面异常活跃，成交量放大明显',
                'risk_warning': '高风险：确认妖股，建议严格止损',
                'market': '美股'
            },
            {
                'symbol': '09992.HK',
                'name': '泡泡玛特',
                'level': '新兴妖股',
                'confidence': 0.78,
                'cpa_macd': 0.0987,
                'dcp_ratio': 0.445,
                'reasoning': '潮玩市场复苏，新品发布带动，港股资金关注度提升',
                'risk_warning': '中高风险：新兴妖股特征，建议小仓位关注',
                'market': '港股'
            },
            {
                'symbol': '300750',
                'name': '宁德时代',
                'level': '潜在妖股',
                'confidence': 0.65,
                'cpa_macd': 0.0654,
                'dcp_ratio': 0.321,
                'reasoning': '新能源政策利好，电池技术突破预期，机构调研增加',
                'risk_warning': '中等风险：潜在妖股需密切观察',
                'market': 'A股'
            }
        ]
        
        # 根据置信度和市场筛选
        filtered_stocks = [
            stock for stock in all_demon_stocks 
            if stock['confidence'] >= confidence_threshold and stock['market'] in markets
        ]
        
        return filtered_stocks
    
    def _get_demon_level_emoji(self, level: str) -> str:
        """获取妖股等级对应的emoji"""
        emoji_map = {
            '超级妖股': '🔥',
            '确认妖股': '⚡',
            '新兴妖股': '🌟',
            '潜在妖股': '💫',
            '正常股票': '📈'
        }
        return emoji_map.get(level, '📊')
    
    @monitor_performance
    def _render_manual_analysis(self, member):
        """渲染手动分析界面"""
        st.header("🔄 手动股票分析")
        
        permissions = self.member_system.get_member_permissions(member)
        
        if not permissions['view_ai_analysis']:
            st.warning("您的会员等级暂不支持手动分析功能，请升级会员")
            return
        
        # 股票输入
        symbols_input = st.text_input(
            "输入股票代码（用逗号分隔）",
            placeholder="例如：AAPL,GOOGL,MSFT"
        )
        
        if st.button("🚀 开始分析") and symbols_input:
            symbols = [s.strip().upper() for s in symbols_input.split(',')]
            
            with st.spinner(f"正在分析 {len(symbols)} 只股票..."):
                try:
                    # 获取数据并分析
                    import asyncio
                    import threading
                    
                    def run_fetch_and_analysis():
                        loop = asyncio.new_event_loop()
                        asyncio.set_event_loop(loop)
                        try:
                            # 获取股票数据
                            stock_data_list = loop.run_until_complete(self.data_pipeline.fetch_market_data(symbols))
                            
                            if stock_data_list:
                                # 运行AI分析
                                analysis_results = loop.run_until_complete(self.data_pipeline.analysis_engine.analyze_market_batch(stock_data_list))
                                return stock_data_list, analysis_results
                            else:
                                return None, None
                        finally:
                            loop.close()
                    
                    # 在新线程中运行异步操作
                    stock_data_list, analysis_results = run_fetch_and_analysis()
                    
                    if stock_data_list:
                        # 显示股票数据
                        st.subheader("📊 股票数据")
                        
                        data_for_table = []
                        for stock in stock_data_list:
                            data_for_table.append({
                                '股票代码': stock.symbol,
                                '当前价格': f"${stock.price:.2f}",
                                '涨跌额': f"${stock.change:.2f}",
                                '涨跌幅': f"{stock.change_percent:.2f}%",
                                '成交量': f"{stock.volume:,}"
                            })
                        
                        df = pd.DataFrame(data_for_table)
                        st.dataframe(df, use_container_width=True)
                        
                        # AI分析结果
                        st.subheader("🎭 八仙论道AI分析")
                        
                        if analysis_results:
                            for symbol, advice in analysis_results.items():
                                with st.expander(f"📈 {symbol} - AI分析结果"):
                                    col1, col2 = st.columns([2, 1])
                                    
                                    with col1:
                                        st.markdown(f"**推荐操作：** {advice.recommendation}")
                                        st.markdown(f"**置信度：** {advice.confidence_level:.1%}")
                                        st.markdown(f"**投资期限：** {advice.time_horizon}")
                                        
                                        if permissions['view_detailed_reasoning']:
                                            st.markdown(f"**分析理由：** {advice.reasoning}")
                                            st.markdown(f"**风险评估：** {advice.risk_assessment}")
                                        
                                        if hasattr(advice, 'debate_summary') and advice.debate_summary:
                                            st.markdown(f"**辩论摘要：** {advice.debate_summary[:200]}...")
                                    
                                    with col2:
                                        if advice.recommendation == 'buy':
                                            st.success("🟢 买入推荐")
                                        elif advice.recommendation == 'sell':
                                            st.error("🔴 卖出推荐")
                                        else:
                                            st.warning("🟡 持有观望")
                                        
                                        st.info(f"置信度: {advice.confidence_level:.1%}")
                        else:
                            st.warning("AI分析结果为空")
                    else:
                        st.error("无法获取股票数据")
                        
                except Exception as e:
                    st.error(f"分析失败：{e}")
    
    def _render_yesterday_review(self, member):
        """渲染昨日复盘界面"""
        st.header("🔄 昨日复盘 - 换手率预测验证")
        
        yesterday = (datetime.now() - timedelta(days=1)).strftime('%Y-%m-%d')
        
        # 获取昨日预测数据
        yesterday_predictions = self._get_yesterday_predictions(yesterday)
        
        if not yesterday_predictions:
            st.warning("暂无昨日预测数据")
            return
        
        st.subheader(f"📅 {yesterday} 预测结果验证")
        
        # 总体统计
        col1, col2, col3, col4 = st.columns(4)
        
        total_predictions = len(yesterday_predictions)
        correct_predictions = sum(1 for p in yesterday_predictions if p['prediction_correct'])
        accuracy_rate = (correct_predictions / total_predictions * 100) if total_predictions > 0 else 0
        
        with col1:
            st.metric("总预测数", total_predictions)
        with col2:
            st.metric("正确预测", correct_predictions)
        with col3:
            st.metric("准确率", f"{accuracy_rate:.1f}%")
        with col4:
            avg_turnover = sum(p['turnover_rate'] for p in yesterday_predictions) / total_predictions if total_predictions > 0 else 0
            st.metric("平均换手率", f"{avg_turnover:.2f}%")
        
        # 详细预测结果表格
        st.subheader("📊 详细预测结果")
        
        prediction_data = []
        for pred in yesterday_predictions:
            prediction_data.append({
                '股票代码': pred['symbol'],
                '预测方向': '📈 上涨' if pred['predicted_direction'] == 'up' else '📉 下跌',
                '实际方向': '📈 上涨' if pred['actual_direction'] == 'up' else '📉 下跌',
                '换手率': f"{pred['turnover_rate']:.2f}%",
                '实际涨跌幅': f"{pred['actual_change']:.2f}%",
                '预测结果': '✅ 正确' if pred['prediction_correct'] else '❌ 错误',
                '置信度': f"{pred['confidence']:.1f}%"
            })
        
        df = pd.DataFrame(prediction_data)
        st.dataframe(df, use_container_width=True)
        
        # 成功/失败原因分析
        st.subheader("🔍 预测分析总结")
        
        col1, col2 = st.columns(2)
        
        with col1:
            st.markdown("#### ✅ 成功预测分析")
            successful_preds = [p for p in yesterday_predictions if p['prediction_correct']]
            if successful_preds:
                success_reasons = self._analyze_prediction_reasons(successful_preds, 'success')
                for reason in success_reasons:
                    st.success(f"• {reason}")
            else:
                st.info("暂无成功预测")
        
        with col2:
            st.markdown("#### ❌ 失败预测分析")
            failed_preds = [p for p in yesterday_predictions if not p['prediction_correct']]
            if failed_preds:
                failure_reasons = self._analyze_prediction_reasons(failed_preds, 'failure')
                for reason in failure_reasons:
                    st.error(f"• {reason}")
            else:
                st.info("暂无失败预测")
        
        # 改进建议
        st.subheader("💡 模型优化建议")
        improvement_suggestions = self._generate_improvement_suggestions(yesterday_predictions)
        for suggestion in improvement_suggestions:
            st.info(f"💡 {suggestion}")
    
    def _render_god_list(self, member):
        """渲染封神榜界面"""
        st.header("🏆 封神榜 - 热门标的追踪系统")
        
        # 封神榜说明
        st.info("📋 封神榜规则：连续三次登榜首位的标的将被激活重点跟踪")
        
        # 获取当前封神榜数据
        god_list_data = self._get_god_list_data()
        
        # 市场分类标签
        market_tabs = st.tabs(["🇺🇸 美股", "🇭🇰 港股", "🪙 加密货币"])
        
        with market_tabs[0]:
            self._render_market_god_list("US", god_list_data.get('US', []), member)
        
        with market_tabs[1]:
            self._render_market_god_list("HK", god_list_data.get('HK', []), member)
        
        with market_tabs[2]:
            self._render_market_god_list("CRYPTO", god_list_data.get('CRYPTO', []), member)
        
        # 今日上榜分析
        st.subheader("📊 今日上榜分析")
        today_rankings = self._get_today_rankings()
        
        if today_rankings:
            for market, rankings in today_rankings.items():
                with st.expander(f"{market} 市场今日排行"):
                    ranking_data = []
                    for i, item in enumerate(rankings[:10], 1):
                        ranking_data.append({
                            '排名': i,
                            '股票代码': item['symbol'],
                            '股票名称': item.get('name', 'N/A'),
                            '涨跌幅': f"{item['change_percent']:.2f}%",
                            '换手率': f"{item['turnover_rate']:.2f}%",
                            '成交量': f"{item['volume']:,}",
                            '连续上榜': f"{item['consecutive_days']}天",
                            '状态': '🔥 激活' if item['consecutive_days'] >= 3 else '📊 观察'
                        })
                    
                    df = pd.DataFrame(ranking_data)
                    st.dataframe(df, use_container_width=True)
                    
                    # 检查是否有新的封神标的
                    new_gods = [item for item in rankings if item['consecutive_days'] == 3]
                    if new_gods:
                        st.success(f"🎉 新增封神标的：{', '.join([god['symbol'] for god in new_gods])}")
        else:
            st.warning("暂无今日排行数据")
    
    def _render_market_god_list(self, market: str, god_list: List[Dict], member):
        """渲染特定市场的封神榜"""
        market_names = {'US': '美股', 'HK': '港股', 'CRYPTO': '加密货币'}
        
        if not god_list:
            st.info(f"暂无{market_names[market]}封神标的")
            return
        
        st.subheader(f"🏆 {market_names[market]}封神榜")
        
        for god_stock in god_list:
            with st.expander(f"👑 {god_stock['symbol']} - {god_stock.get('name', 'N/A')}"):
                col1, col2, col3 = st.columns(3)
                
                with col1:
                    st.metric("连续上榜天数", f"{god_stock['consecutive_days']}天")
                    st.metric("平均换手率", f"{god_stock['avg_turnover']:.2f}%")
                
                with col2:
                    st.metric("累计涨跌幅", f"{god_stock['total_change']:.2f}%")
                    st.metric("最高单日涨幅", f"{god_stock['max_daily_change']:.2f}%")
                
                with col3:
                    st.metric("封神日期", god_stock['god_date'])
                    status_color = "🔥" if god_stock['status'] == 'active' else "⏸️"
                    st.metric("跟踪状态", f"{status_color} {god_stock['status']}")
                
                # 投研分析（仅限高级会员）
                permissions = self.member_system.get_member_permissions(member)
                if permissions['view_detailed_reasoning']:
                    st.markdown("#### 🔍 深度投研分析")
                    st.markdown(god_stock.get('analysis', '暂无深度分析'))
                    
                    # 风险提示
                    if god_stock.get('risk_warning'):
                        st.warning(f"⚠️ 风险提示：{god_stock['risk_warning']}")
                else:
                    st.info("💎 升级会员以查看深度投研分析")
    
    def _get_yesterday_predictions(self, date: str) -> List[Dict]:
        """获取昨日预测数据（模拟数据）"""
        # 这里应该从数据库或数据文件中获取实际的预测数据
        # 目前返回模拟数据
        return [
            {
                'symbol': 'FFAI',
                'predicted_direction': 'up',
                'actual_direction': 'up',
                'turnover_rate': 15.6,
                'actual_change': 8.5,
                'prediction_correct': True,
                'confidence': 85.2
            },
            {
                'symbol': 'AAPL',
                'predicted_direction': 'up',
                'actual_direction': 'down',
                'turnover_rate': 3.2,
                'actual_change': -2.1,
                'prediction_correct': False,
                'confidence': 72.1
            },
            {
                'symbol': 'TSLA',
                'predicted_direction': 'down',
                'actual_direction': 'down',
                'turnover_rate': 8.9,
                'actual_change': -4.3,
                'prediction_correct': True,
                'confidence': 78.9
            }
        ]
    
    def _get_god_list_data(self) -> Dict[str, List[Dict]]:
        """获取封神榜数据（模拟数据）"""
        return {
            'US': [
                {
                    'symbol': 'FFAI',
                    'name': 'Faraday Future',
                    'consecutive_days': 5,
                    'avg_turnover': 18.5,
                    'total_change': 45.2,
                    'max_daily_change': 12.8,
                    'god_date': '2024-01-15',
                    'status': 'active',
                    'analysis': '基于AI技术的电动车概念，市场情绪高涨，但需注意基本面风险',
                    'risk_warning': '高波动性，适合短线操作'
                }
            ],
            'HK': [
                {
                    'symbol': '09992.HK',
                    'name': '泡泡玛特',
                    'consecutive_days': 4,
                    'avg_turnover': 12.3,
                    'total_change': 28.7,
                    'max_daily_change': 9.5,
                    'god_date': '2024-01-16',
                    'status': 'active',
                    'analysis': '潮玩市场复苏，新品发布带动股价上涨',
                    'risk_warning': '消费类股票，关注宏观经济影响'
                }
            ],
            'CRYPTO': [
                {
                    'symbol': 'TON',
                    'name': 'Toncoin',
                    'consecutive_days': 6,
                    'avg_turnover': 25.8,
                    'total_change': 67.3,
                    'max_daily_change': 18.2,
                    'god_date': '2024-01-14',
                    'status': 'active',
                    'analysis': 'Telegram生态发展，meme币热潮推动',
                    'risk_warning': '极高风险，加密货币波动性巨大'
                }
            ]
        }
    
    def _get_today_rankings(self) -> Dict[str, List[Dict]]:
        """获取今日排行数据（模拟数据）"""
        return {
            '美股': [
                {'symbol': 'FFAI', 'name': 'Faraday Future', 'change_percent': 12.5, 'turnover_rate': 22.1, 'volume': 15000000, 'consecutive_days': 5},
                {'symbol': 'NVDA', 'name': 'NVIDIA', 'change_percent': 8.3, 'turnover_rate': 5.2, 'volume': 45000000, 'consecutive_days': 2},
                {'symbol': 'TSLA', 'name': 'Tesla', 'change_percent': 6.7, 'turnover_rate': 8.9, 'volume': 35000000, 'consecutive_days': 1}
            ],
            '港股': [
                {'symbol': '09992.HK', 'name': '泡泡玛特', 'change_percent': 9.8, 'turnover_rate': 15.6, 'volume': 8000000, 'consecutive_days': 4},
                {'symbol': '00700.HK', 'name': '腾讯控股', 'change_percent': 4.2, 'turnover_rate': 3.1, 'volume': 25000000, 'consecutive_days': 1}
            ]
        }
    
    def _analyze_prediction_reasons(self, predictions: List[Dict], result_type: str) -> List[str]:
        """分析预测成功/失败原因"""
        if result_type == 'success':
            return [
                "高换手率标的预测准确性较高",
                "市场情绪与技术指标一致性良好",
                "基本面支撑有效"
            ]
        else:
            return [
                "突发消息影响市场预期",
                "低换手率标的随机性较强",
                "宏观环境变化超出模型预期"
            ]
    
    def _generate_improvement_suggestions(self, predictions: List[Dict]) -> List[str]:
        """生成模型改进建议"""
        return [
            "增加突发新闻情感分析权重",
            "优化低换手率标的预测算法",
            "加强宏观经济指标整合",
            "提高置信度阈值筛选机制"
        ]
    
    def _render_jixia_academy(self, member):
        """渲染稷下学宫界面"""
        st.subheader("🏛️ 稷下学宫 - AI辩论系统")
        
        # 检查会员权限 - 现在所有会员都可以使用
        permissions = self.member_system.get_member_permissions(member)
        if permissions.get('jixia_debate_access', True):
            try:
                jixia_ui = JixiaAcademyUI()
                jixia_ui.run()
            except Exception as e:
                st.error(f"稷下学宫加载失败: {e}")
                st.info("""
                **稷下学宫暂时不可用**
                
                可能的原因：
                1. AI辩论模块未正确配置
                2. API密钥未设置
                3. 网络连接问题
                
                请联系管理员或稍后再试。
                """)
        else:
            st.warning("🔒 稷下学宫功能暂时不可用")
            st.info("""
            **稷下学宫 - AI辩论系统**
            
            ✨ **功能特色：**
            - 八仙AI智能辩论
            - 实时观点碰撞分析
            - 投资决策辅助
            - 多维度思维启发
            
            🎯 **适用场景：**
            - 投资策略讨论
            - 市场观点分析
            - 风险评估辩论
            - 商业决策支持
            
            💎 **所有会员均可使用此功能**
            """)

# 主函数
def main():
    """主函数"""
    ui = MorningBriefingUI()
    ui.run()

if __name__ == "__main__":
    main()