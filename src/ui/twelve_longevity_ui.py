# -*- coding: utf-8 -*-
"""
🐒 十二境界 × 十二长生 可视化界面
西游记投资修仙路径的完美呈现
"""

import streamlit as st
import plotly.graph_objects as go
import plotly.express as px
import pandas as pd
from src.core.twelve_longevity_system import TwelveLongevitySystem, MonkeyRealm
import numpy as np

def render_twelve_longevity_ui():
    """渲染十二长生界面"""
    
    st.markdown("# 🐒 十二境界 × 十二长生修仙系统")
    st.markdown("*西游记投资哲学的完美融合*")
    
    # 初始化系统
    if 'longevity_system' not in st.session_state:
        st.session_state.longevity_system = TwelveLongevitySystem()
    
    system = st.session_state.longevity_system
    
    # 顶部进度显示
    progress = system.get_progress_summary()
    current_realm = system.get_current_realm()
    
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric("当前境界", current_realm.name if current_realm else "未知")
    with col2:
        st.metric("修仙等级", f"Lv.{progress['当前等级']}")
    with col3:
        st.metric("当前季节", progress['当前季节'])
    with col4:
        st.metric("完成度", progress['完成度'])
    
    # 主要内容标签页
    tab1, tab2, tab3, tab4, tab5 = st.tabs([
        "📊 境界总览", 
        "🌸 第一季·春", 
        "☀️ 第二季·夏", 
        "🍂 第三季·秋", 
        "❄️ 第四季·冬"
    ])
    
    with tab1:
        render_realms_overview(system)
    
    with tab2:
        render_season_details(system, "春")
    
    with tab3:
        render_season_details(system, "夏")
    
    with tab4:
        render_season_details(system, "秋")
    
    with tab5:
        render_season_details(system, "冬")

def render_realms_overview(system):
    """渲染境界总览"""
    st.markdown("## 📊 十二境界总览表")
    
    # 创建数据表格
    df = system.create_realms_dataframe()
    
    # 使用颜色编码季节
    def color_season(val):
        colors = {
            "春": "background-color: #E8F5E8",  # 浅绿
            "夏": "background-color: #FFF8DC",  # 浅黄
            "秋": "background-color: #FFE4B5",  # 浅橙
            "冬": "background-color: #E6F3FF"   # 浅蓝
        }
        return colors.get(val, "")
    
    # 显示表格
    styled_df = df.style.applymap(color_season, subset=['季节'])
    st.dataframe(styled_df, use_container_width=True)
    
    # 力量等级可视化
    st.markdown("### ⚡ 力量等级变化图")
    
    fig = go.Figure()
    
    # 按季节分组绘制
    seasons = ["春", "夏", "秋", "冬"]
    season_colors = {
        "春": "#90EE90",  # 浅绿
        "夏": "#FFD700",  # 金色
        "秋": "#FFA500",  # 橙色
        "冬": "#87CEEB"   # 天蓝
    }
    
    for season in seasons:
        season_data = df[df['季节'] == season]
        fig.add_trace(go.Scatter(
            x=season_data['等级'],
            y=season_data['力量等级'],
            mode='lines+markers',
            name=f"{season}季",
            line=dict(color=season_colors[season], width=3),
            marker=dict(size=10),
            hovertemplate='<b>%{text}</b><br>等级: %{x}<br>力量: %{y}<extra></extra>',
            text=season_data['境界名']
        ))
    
    fig.update_layout(
        title="🐒 猴王修仙力量成长曲线",
        xaxis_title="境界等级",
        yaxis_title="力量等级",
        height=500,
        hovermode='x unified'
    )
    
    st.plotly_chart(fig, use_container_width=True)
    
    # 十二长生圆环图
    st.markdown("### 🔄 十二长生循环图")
    
    longevity_names = [realm.longevity.value[0] for realm in system.REALMS]
    realm_names = [realm.name for realm in system.REALMS]
    
    fig_circle = go.Figure()
    
    # 创建圆环
    angles = np.linspace(0, 2*np.pi, 12, endpoint=False)
    x = np.cos(angles)
    y = np.sin(angles)
    
    # 添加圆环上的点
    fig_circle.add_trace(go.Scatter(
        x=x, y=y,
        mode='markers+text',
        marker=dict(size=20, color=list(range(12)), colorscale='viridis'),
        text=longevity_names,
        textposition="middle center",
        textfont=dict(size=10, color="white"),
        hovertemplate='<b>%{customdata[0]}</b><br>%{customdata[1]}<extra></extra>',
        customdata=list(zip(realm_names, longevity_names)),
        name="十二长生"
    ))
    
    # 添加连接线
    x_line = np.append(x, x[0])  # 闭合圆环
    y_line = np.append(y, y[0])
    
    fig_circle.add_trace(go.Scatter(
        x=x_line, y=y_line,
        mode='lines',
        line=dict(color='gray', width=2),
        showlegend=False,
        hoverinfo='skip'
    ))
    
    fig_circle.update_layout(
        title="🔄 十二长生循环 - 生生不息的修仙之路",
        xaxis=dict(visible=False),
        yaxis=dict(visible=False),
        height=500,
        showlegend=False
    )
    
    st.plotly_chart(fig_circle, use_container_width=True)

def render_season_details(system, season):
    """渲染季节详情"""
    season_realms = system.get_season_realms(season)
    
    season_info = {
        "春": {"emoji": "🌸", "theme": "觉醒篇", "description": "初入市场，从懵懂到觉悟"},
        "夏": {"emoji": "☀️", "theme": "求道篇", "description": "主动学习，建立体系"},
        "秋": {"emoji": "🍂", "theme": "膨胀篇", "description": "过度自信，遭遇挫折"},
        "冬": {"emoji": "❄️", "theme": "觉悟篇", "description": "痛苦磨炼，最终觉悟"}
    }
    
    info = season_info[season]
    st.markdown(f"## {info['emoji']} 第{['一', '二', '三', '四'][['春', '夏', '秋', '冬'].index(season)]}季·{season} - {info['theme']}")
    st.markdown(f"*{info['description']}*")
    
    # 季节进度
    current_realm = system.get_current_realm()
    current_season_progress = 0
    if current_realm and current_realm.season == season:
        season_realms_levels = [r.level for r in season_realms]
        if current_realm.level in season_realms_levels:
            current_season_progress = season_realms_levels.index(current_realm.level) + 1
    
    st.progress(current_season_progress / 3, text=f"本季进度: {current_season_progress}/3")
    
    # 详细境界信息
    for i, realm in enumerate(season_realms, 1):
        is_current = current_realm and current_realm.level == realm.level
        is_completed = current_realm and current_realm.level > realm.level
        
        # 状态图标
        if is_completed:
            status_icon = "✅"
        elif is_current:
            status_icon = "🔥"
        else:
            status_icon = "🔒"
        
        with st.expander(f"{status_icon} {realm.level}. {realm.name} ({realm.longevity.value[0]})", expanded=is_current):
            col1, col2 = st.columns([2, 1])
            
            with col1:
                st.markdown(f"**🎭 西游故事**")
                st.markdown(f"> {realm.story}")
                
                st.markdown(f"**💰 投资寓意**")
                st.markdown(f"{realm.investment_meaning}")
                
                st.markdown(f"**💡 核心教训**")
                st.markdown(f"*{realm.key_lesson}*")
                
                st.markdown(f"**🎯 解锁条件**")
                st.markdown(f"`{realm.unlock_condition}`")
            
            with col2:
                st.markdown(f"**📊 境界属性**")
                st.metric("力量等级", realm.power_level)
                st.markdown(f"**十二长生**: {realm.longevity.value[0]}")
                st.markdown(f"**阴阳**: {realm.longevity.value[1]}")
                st.markdown(f"**含义**: {realm.longevity.value[2]}")
                
                # 模拟升级按钮
                if is_current and st.button(f"🆙 突破到下一境界", key=f"advance_{realm.level}"):
                    success, new_realm = system.advance_realm()
                    if success and new_realm:
                        st.success(f"🎉 成功突破到 {new_realm.name}!")
                        st.rerun()
                    else:
                        st.info("已达到最高境界！")
    
    # 季节总结
    if season == "春":
        st.markdown("---")
        st.markdown("### 🌸 第一季·春 特别说明")
        st.markdown("""
        第一季·春是整个修仙路径的基础，对应投资者的**觉醒期**：
        
        1. **日精月华 (长生)** - 如婴儿般纯真地接触市场
        2. **一跃成王 (沐浴)** - 体验成功与背叛，学会人性复杂
        3. **生死大限 (冠带)** - 认识到投资的风险，开始寻求智慧
        
        这三个境界完美诠释了从**傻逼到开始思考**的转变过程。
        """)

def render_longevity_explanation():
    """渲染十二长生解释"""
    st.markdown("### 📚 十二长生详解")
    
    longevity_data = [
        ("长生", "阳生", "新生力量的萌发", "如婴儿出生，充满潜力"),
        ("沐浴", "阳浴", "初次接触，懵懂学习", "如婴儿洗澡，接受外界"),
        ("冠带", "阳冠", "初具规模，开始成形", "如少年加冠，承担责任"),
        ("临官", "阳官", "正式登场，承担责任", "如青年入仕，展现才华"),
        ("帝旺", "阳帝", "达到巅峰，君临天下", "如帝王登基，权力巅峰"),
        ("衰", "阳衰", "盛极而衰，开始下滑", "如中年体衰，力不从心"),
        ("病", "阳病", "问题显现，需要调整", "如身体患病，需要治疗"),
        ("死", "阳死", "彻底失败，面临终结", "如生命终结，一切归零"),
        ("墓", "阳墓", "埋葬过去，准备重生", "如入土为安，等待轮回"),
        ("绝", "阳绝", "断绝旧路，寻找新径", "如绝处逢生，另辟蹊径"),
        ("胎", "阳胎", "孕育新生，积蓄力量", "如胎儿孕育，积蓄生机"),
        ("养", "阳养", "精心培育，等待时机", "如精心养育，等待成长")
    ]
    
    df_longevity = pd.DataFrame(longevity_data, columns=["长生", "阴阳", "核心含义", "生活比喻"])
    st.dataframe(df_longevity, use_container_width=True)

if __name__ == "__main__":
    st.set_page_config(
        page_title="十二境界×十二长生",
        page_icon="🐒",
        layout="wide"
    )
    render_twelve_longevity_ui()