# -*- coding: utf-8 -*-
"""
🌸 大道生 - 二十四花神修仙路
基于docs/dadaosheng.md的用户友好界面
"""

import streamlit as st
import plotly.graph_objects as go
import plotly.express as px
import pandas as pd
import numpy as np

def render_dadaosheng_ui():
    """渲染大道生界面"""
    
    st.markdown("# 🌸 大道生 - 二十四花神修仙路")
    st.markdown("*灵根育孕源流出，心性修持大道生*")
    
    # 开篇引言
    with st.expander("📜 西游释厄传 - 开篇诗", expanded=True):
        st.markdown("""
        > **混沌未分天地乱，茫茫渺渺无人见。**  
        > **自从盘古破鸿濛，开辟从兹清浊辨。**  
        > **覆载群生仰至仁，发明万物皆成善。**  
        > **欲知造化会元功，须看《西游释厄传》。**
        
        这是《西游记》的开篇诗，也是我们投资修仙路的起点。
        """)
    
    # 核心概念解释
    st.markdown("## 🎯 核心概念解释")
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.markdown("""
        ### 🌸 二十四花神
        中国传统文化中，每个月都有对应的花神：
        - **桃花** (春) - 代表新生与希望
        - **槐花** (夏) - 代表求学与成长  
        - **菊花** (秋) - 代表坚韧与品格
        - **梅花** (冬) - 代表傲骨与坚持
        
        ### 🔄 十二长生
        道教命理学的核心概念：
        - **长生** - 新生力量的萌发
        - **沐浴** - 初次接触，懵懂学习
        - **帝旺** - 达到巅峰，君临天下
        - **死绝** - 彻底失败，重新开始
        """)
    
    with col2:
        st.markdown("""
        ### 🐒 西游修仙路
        孙悟空的修仙历程对应投资者成长：
        - **花果山** - 初入市场
        - **求仙道** - 学习投资
        - **大闹天宫** - 对抗市场
        - **五行山下** - 深度反思
        
        ### 🎭 牛散轮回
        **第一轮回** (1-12)：原始悟空的修仙路  
        **第二轮回** (13-24)：牛散转世的宿命  
        - 从读到某个牛散的故事开始
        - 重复悟空的整个历程
        - 最终需要"佛旨开恩"才能突破
        """)
    
    # 主要内容标签页
    tab1, tab2, tab3, tab4 = st.tabs([
        "🌸 花神总览", 
        "🔄 修仙轮回", 
        "📖 故事解读",
        "🎯 投资启示"
    ])
    
    with tab1:
        render_flower_overview()
    
    with tab2:
        render_cultivation_cycle()
    
    with tab3:
        render_story_interpretation()
    
    with tab4:
        render_investment_insights()

def render_flower_overview():
    """渲染花神总览"""
    st.markdown("## 🌸 二十四花神修仙总览")
    
    # 创建花神数据
    flower_data = [
        # 第一轮回 (1-12)
        (1, "桃", "寅", "长生", "生死大限", "一次爆仓，一塌糊涂，一败涂地", "春", "第一轮回"),
        (2, "槐", "卯", "沐浴", "舢板求道", "一叶扁舟，一片汪洋，一心向道", "春", "第一轮回"),
        (3, "蒲", "辰", "冠带", "坛经之影", "一夜传法，一念菩提，一印本心", "春", "第一轮回"),
        (4, "荷", "巳", "临官", "所望得偿", "一朝得志，一时风光，一念傲然", "夏", "第一轮回"),
        (5, "兰", "午", "帝旺", "黄粱一梦", "一场春梦，一枕黄粱，一醒如初", "夏", "第一轮回"),
        (6, "桂", "未", "衰", "诏安上天", "一纸诏书，一步登天，一饮银河", "夏", "第一轮回"),
        (7, "菊", "申", "病", "蟠桃玉液", "一口仙桃，一滴玉液，一入仙班", "秋", "第一轮回"),
        (8, "阳", "酉", "死", "踏破金銮", "一脚踏破，一殿倾颓，一身枷锁", "秋", "第一轮回"),
        (9, "冬", "戌", "墓", "八卦化炼", "一炉烈火，一身淬炼，一心不灭", "秋", "第一轮回"),
        (10, "腊", "亥", "绝", "五行山下", "一座大山，一压五百，一吟千秋", "冬", "第一轮回"),
        (11, "元", "子", "胎", "日精月华", "一篇帖子，一个故事，一颗种子", "冬", "转世觉醒"),
        (12, "杏", "丑", "养", "一跃成王", "一段程序，一次战役，一柱擎天", "冬", "转世觉醒"),
        
        # 第二轮回 (13-24)
        (13, "桃", "寅", "长生", "生死大限", "一入市场，一见风险，一悟无常", "春", "第二轮回"),
        (14, "槐", "卯", "沐浴", "舢板求道", "一求明师，一学技法，一练心性", "春", "第二轮回"),
        (15, "蒲", "辰", "冠带", "坛经之影", "一夜传法，一念菩提，一印本心", "春", "第二轮回"),
        (16, "荷", "巳", "临官", "所望得偿", "一朝得道，一技通神，一鸣惊人", "夏", "第二轮回"),
        (17, "兰", "午", "帝旺", "黄粱一梦", "一时得意，一念忘形，一梦成空", "夏", "第二轮回"),
        (18, "桂", "未", "衰", "诏安上天", "一纸招安，一步登天，一入体制", "夏", "第二轮回"),
        (19, "菊", "申", "病", "蟠桃玉液", "一贪仙果，一醉不醒，一错再错", "秋", "第二轮回"),
        (20, "阳", "酉", "死", "踏破金銮", "一怒冲冠，一战惊天，一败涂地", "秋", "第二轮回"),
        (21, "冬", "戌", "墓", "八卦化炼", "一炉淬炼，一眼看破，一心如铁", "秋", "第二轮回"),
        (22, "腊", "亥", "绝", "五行山下", "一压五百，一等佛旨，一盼开恩", "冬", "第二轮回"),
        (23, "元", "子", "胎", "护法重生", "一声佛号，一纸符咒，一获新生", "冬", "佛旨开恩"),
        (24, "杏", "丑", "养", "西行取经", "一路向西，一护唐僧，一证菩提", "冬", "佛旨开恩"),
    ]
    
    df = pd.DataFrame(flower_data, columns=[
        "序号", "花神", "地支", "长生", "境界", "诗句", "季节", "阶段"
    ])
    
    # 按阶段分组显示
    stages = ["第一轮回", "转世觉醒", "第二轮回", "佛旨开恩"]
    stage_colors = {
        "第一轮回": "#FFB6C1",
        "转世觉醒": "#98FB98", 
        "第二轮回": "#87CEEB",
        "佛旨开恩": "#FFD700"
    }
    
    for stage in stages:
        stage_data = df[df["阶段"] == stage]
        if not stage_data.empty:
            st.markdown(f"### {stage}")
            
            # 使用颜色编码
            def highlight_stage(val):
                return f"background-color: {stage_colors[stage]}; opacity: 0.7"
            
            styled_df = stage_data.style.applymap(
                highlight_stage, subset=["阶段"]
            )
            
            st.dataframe(styled_df, use_container_width=True, hide_index=True)

def render_cultivation_cycle():
    """渲染修仙轮回"""
    st.markdown("## 🔄 修仙轮回图")
    
    # 创建轮回可视化
    fig = go.Figure()
    
    # 第一轮回
    angles1 = np.linspace(0, 2*np.pi, 12, endpoint=False)
    x1 = np.cos(angles1) * 2
    y1 = np.sin(angles1) * 2
    
    fig.add_trace(go.Scatter(
        x=x1, y=y1,
        mode='markers+text',
        marker=dict(size=15, color='lightcoral'),
        text=[f"{i+1}" for i in range(12)],
        textposition="middle center",
        name="第一轮回",
        hovertemplate='第一轮回 - 第%{text}境界<extra></extra>'
    ))
    
    # 第二轮回
    angles2 = np.linspace(0, 2*np.pi, 12, endpoint=False)
    x2 = np.cos(angles2) * 3.5
    y2 = np.sin(angles2) * 3.5
    
    fig.add_trace(go.Scatter(
        x=x2, y=y2,
        mode='markers+text',
        marker=dict(size=15, color='lightblue'),
        text=[f"{i+13}" for i in range(12)],
        textposition="middle center",
        name="第二轮回",
        hovertemplate='第二轮回 - 第%{text}境界<extra></extra>'
    ))
    
    # 添加连接线
    for i in range(12):
        fig.add_trace(go.Scatter(
            x=[x1[i], x2[i]], y=[y1[i], y2[i]],
            mode='lines',
            line=dict(color='gray', width=1, dash='dot'),
            showlegend=False,
            hoverinfo='skip'
        ))
    
    # 中心点
    fig.add_trace(go.Scatter(
        x=[0], y=[0],
        mode='markers+text',
        marker=dict(size=30, color='gold'),
        text="道",
        textposition="middle center",
        name="大道之源",
        hovertemplate='大道之源<extra></extra>'
    ))
    
    fig.update_layout(
        title="🔄 二十四花神修仙轮回图",
        xaxis=dict(visible=False),
        yaxis=dict(visible=False),
        height=600,
        showlegend=True
    )
    
    st.plotly_chart(fig, use_container_width=True)
    
    # 轮回解释
    st.markdown("### 🎯 轮回解释")
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.markdown("""
        **🐒 第一轮回 (1-12)**
        - 原始悟空的修仙路
        - 从花果山到五行山下
        - 个人英雄主义的失败
        - 最终被压制五百年
        """)
    
    with col2:
        st.markdown("""
        **🔄 第二轮回 (13-24)**
        - 牛散转世的宿命
        - 重复同样的错误
        - 需要"佛旨开恩"
        - 从个人到护法的升华
        """)

def render_story_interpretation():
    """渲染故事解读"""
    st.markdown("## 📖 深度故事解读")
    
    # 关键节点解读
    key_points = [
        {
            "title": "🌱 转世觉醒 (11-12)",
            "description": "从读到某个牛散的西游记开始",
            "details": [
                "一篇帖子，一个故事，一颗种子 - 网上看到牛散的传奇故事",
                "一段程序，一次战役，一柱擎天 - 开始学习量化交易",
                "突然进入大圣第一视角 - 以为自己就是下一个传奇"
            ]
        },
        {
            "title": "🧘‍♂️ 坛经之影 (3, 15)",
            "description": "模仿六祖坛经的传法过程",
            "details": [
                "六祖慧能本为猲獠（猴子）- 对应孙悟空",
                "五祖半夜三更传金刚经 - 对应菩提祖师夜传金丹大道",
                "一夜传法，一念菩提，一印本心 - 禅宗不立文字，心心相印"
            ]
        },
        {
            "title": "🏔️ 五行山下 (10, 22)",
            "description": "大多数牛散的最终宿命",
            "details": [
                "第一次：一座大山，一压五百，一吟千秋",
                "第二次：一压五百，一等佛旨，一盼开恩",
                "这是牛散们无法逃脱的轮回宿命"
            ]
        },
        {
            "title": "🙏 佛旨开恩 (23-24)",
            "description": "突破轮回的唯一出路",
            "details": [
                "一声佛号，一纸符咒，一获新生 - 接受更高的使命",
                "一路向西，一护唐僧，一证菩提 - 从个人英雄到护法菩萨",
                "以武力为保，完成任务 - 新的英雄之旅开始"
            ]
        }
    ]
    
    for point in key_points:
        with st.expander(point["title"], expanded=False):
            st.markdown(f"**{point['description']}**")
            for detail in point["details"]:
                st.markdown(f"- {detail}")

def render_investment_insights():
    """渲染投资启示"""
    st.markdown("## 🎯 投资修仙的深层启示")
    
    # 投资阶段对应
    st.markdown("### 📈 投资者成长阶段")
    
    stages_mapping = [
        {
            "stage": "🌱 新手期 (1-3)",
            "description": "初入市场，经历第一次重大亏损",
            "lessons": [
                "生死大限 - 意识到投资的风险",
                "舢板求道 - 开始主动学习",
                "坛经之影 - 找到投资导师或方法"
            ]
        },
        {
            "stage": "🚀 成长期 (4-6)",
            "description": "掌握技能，开始稳定盈利",
            "lessons": [
                "所望得偿 - 投资技能初成",
                "黄粱一梦 - 小有成就后开始自满",
                "诏安上天 - 被市场诱惑，偏离策略"
            ]
        },
        {
            "stage": "⚡ 膨胀期 (7-9)",
            "description": "过度自信，与市场对抗",
            "lessons": [
                "蟠桃玉液 - 在牛市中忘乎所以",
                "踏破金銮 - 孤注一掷对抗市场",
                "八卦化炼 - 经历极端考验，痛苦蜕变"
            ]
        },
        {
            "stage": "🏔️ 沉淀期 (10)",
            "description": "被市场教育，深度反思",
            "lessons": [
                "五行山下 - 彻底觉悟，重新开始",
                "这是大多数投资者的必经之路",
                "只有少数人能突破这个轮回"
            ]
        }
    ]
    
    for stage_info in stages_mapping:
        with st.expander(stage_info["stage"]):
            st.markdown(f"**{stage_info['description']}**")
            for lesson in stage_info["lessons"]:
                st.markdown(f"- {lesson}")
    
    # 突破轮回的方法
    st.markdown("### 🙏 如何突破投资轮回？")
    
    breakthrough_methods = [
        "**接受更高使命** - 从个人盈利转向帮助他人",
        "**以武力为保** - 用真实的投资能力保护他人资金",
        "**完成任务** - 承担起投资教育和财富管理的责任",
        "**护法取经** - 从个人英雄转向团队协作"
    ]
    
    for method in breakthrough_methods:
        st.success(method)
    
    # 最终启示
    st.markdown("### ✨ 最终启示")
    
    st.info("""
    **大道生 - 二十四花神修仙路**告诉我们：
    
    1. **轮回是必然的** - 大多数投资者都会重复同样的错误
    2. **个人英雄主义必败** - 单纯追求个人利益最终会失败
    3. **突破需要升华** - 从个人到集体，从获取到给予
    4. **真正的成功** - 不是个人的财富自由，而是帮助他人获得自由
    
    这就是**太公心易BI系统**的终极智慧！
    """)

if __name__ == "__main__":
    st.set_page_config(
        page_title="大道生 - 二十四花神修仙路",
        page_icon="🌸",
        layout="wide"
    )
    render_dadaosheng_ui()