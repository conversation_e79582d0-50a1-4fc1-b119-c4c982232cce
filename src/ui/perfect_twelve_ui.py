# -*- coding: utf-8 -*-
"""
🐒 完美十二境界可视化界面
基于您的神级设计：境界×长生×GameFi的完美融合
"""

import streamlit as st
import plotly.graph_objects as go
import plotly.express as px
import pandas as pd
import numpy as np
from src.core.perfect_twelve_system import PerfectTwelveSystem, PerfectRealm

def render_perfect_twelve_ui():
    """渲染完美十二境界界面"""
    
    st.markdown("# 🐒 完美十二境界系统")
    st.markdown("*基于您的神级设计：境界×长生×GameFi的完美融合*")
    st.markdown("---")
    
    # 初始化系统
    if 'perfect_system' not in st.session_state:
        st.session_state.perfect_system = PerfectTwelveSystem()
    
    system = st.session_state.perfect_system
    
    # 顶部状态栏
    status = system.get_progress_status()
    col1, col2, col3, col4, col5 = st.columns(5)
    
    with col1:
        st.metric("当前境界", status['当前境界'])
    with col2:
        st.metric("长生阶段", status['长生阶段'])
    with col3:
        st.metric("当前季节", status['当前季节'])
    with col4:
        st.metric("修仙等级", status['等级'])
    with col5:
        st.metric("觉悟点数", status['觉悟点数'])
    
    # 主要内容标签页
    tab1, tab2, tab3, tab4 = st.tabs([
        "📊 神级设计表", 
        "🔄 长生循环图", 
        "🌸🌞🍂❄️ 四季详解",
        "🎮 GameFi体验"
    ])
    
    with tab1:
        render_perfect_table(system)
    
    with tab2:
        render_longevity_cycle(system)
    
    with tab3:
        render_four_seasons(system)
    
    with tab4:
        render_gamefi_experience(system)

def render_perfect_table(system):
    """渲染完美设计表格"""
    st.markdown("## 📊 您的神级设计表格")
    st.markdown("*这是投资GameFi的终极形态！*")
    
    # 获取完美表格
    df = system.create_perfect_dataframe()
    
    # 季节颜色映射
    def style_season(val):
        colors = {
            "春": "background-color: #E8F5E8; color: #2E7D32",  # 绿色
            "夏": "background-color: #FFF8DC; color: #F57C00",  # 橙色
            "秋": "background-color: #FFE4B5; color: #D84315",  # 红色
            "冬": "background-color: #E3F2FD; color: #1565C0"   # 蓝色
        }
        return colors.get(val, "")
    
    # 长生阶段颜色映射
    def style_longevity(val):
        longevity_colors = {
            "胎": "background-color: #F3E5F5; color: #7B1FA2",
            "养": "background-color: #E8F5E8; color: #388E3C", 
            "长生": "background-color: #FFF3E0; color: #F57C00",
            "沐浴": "background-color: #E3F2FD; color: #1976D2",
            "冠带": "background-color: #FCE4EC; color: #C2185B",
            "临官": "background-color: #F1F8E9; color: #689F38",
            "帝旺": "background-color: #FFF8E1; color: #FBC02D",
            "衰": "background-color: #EFEBE9; color: #5D4037",
            "病": "background-color: #FFEBEE; color: #D32F2F",
            "死": "background-color: #FAFAFA; color: #424242",
            "墓": "background-color: #E0E0E0; color: #616161",
            "绝": "background-color: #263238; color: #FFFFFF"
        }
        return longevity_colors.get(val, "")
    
    # 应用样式
    styled_df = df.style.applymap(style_season, subset=['季节']).applymap(style_longevity, subset=['长生阶段'])
    
    st.dataframe(styled_df, use_container_width=True, height=500)
    
    # 表格说明
    st.markdown("### 📝 设计精髓")
    col1, col2 = st.columns(2)
    
    with col1:
        st.markdown("""
        **🎯 核心创新点：**
        - **境界等级**：1-12级完整修仙路径
        - **长生阶段**：十二长生的哲学映射
        - **季节轮回**：春夏秋冬的自然循环
        - **核心事件**：西游记的经典情节
        """)
    
    with col2:
        st.markdown("""
        **🚀 GameFi对应：**
        - **新手村**：市场教育阶段
        - **技能学习**：投资能力建设
        - **声誉建立**：连续获利阶段
        - **大成境界**：齐天大圣级别
        """)

def render_longevity_cycle(system):
    """渲染长生循环图"""
    st.markdown("## 🔄 十二长生循环图")
    st.markdown("*投资如人生，有起有落，关键是理解每个阶段的特点*")
    
    # 创建圆形布局
    df = system.create_perfect_dataframe()
    
    # 计算圆形位置
    angles = np.linspace(0, 2*np.pi, 12, endpoint=False)
    x = np.cos(angles) * 100
    y = np.sin(angles) * 100
    
    # 创建圆环图
    fig = go.Figure()
    
    # 添加圆环上的点
    colors = px.colors.qualitative.Set3
    
    for i, (_, row) in enumerate(df.iterrows()):
        fig.add_trace(go.Scatter(
            x=[x[i]], y=[y[i]],
            mode='markers+text',
            marker=dict(
                size=30,
                color=colors[i % len(colors)],
                line=dict(width=2, color='white')
            ),
            text=row['长生阶段'],
            textposition="middle center",
            textfont=dict(size=10, color="white", family="Arial Black"),
            hovertemplate=f'<b>{row["等级名称"]}</b><br>' +
                         f'长生阶段: {row["长生阶段"]}<br>' +
                         f'核心事件: {row["核心事件"]}<br>' +
                         f'GameFi对应: {row["GameFi对应"]}<extra></extra>',
            name=row['等级名称'],
            showlegend=False
        ))
    
    # 添加连接线形成圆环
    x_line = np.append(x, x[0])
    y_line = np.append(y, y[0])
    
    fig.add_trace(go.Scatter(
        x=x_line, y=y_line,
        mode='lines',
        line=dict(color='rgba(128,128,128,0.5)', width=3),
        showlegend=False,
        hoverinfo='skip'
    ))
    
    # 添加中心文字
    fig.add_annotation(
        x=0, y=0,
        text="十二长生<br>循环",
        showarrow=False,
        font=dict(size=16, color="black", family="Arial Black"),
        bgcolor="rgba(255,255,255,0.8)",
        bordercolor="gray",
        borderwidth=2
    )
    
    fig.update_layout(
        title="🔄 十二长生循环 - 投资人生的完整轮回",
        xaxis=dict(visible=False, range=[-150, 150]),
        yaxis=dict(visible=False, range=[-150, 150]),
        height=600,
        showlegend=False,
        plot_bgcolor='rgba(0,0,0,0)'
    )
    
    st.plotly_chart(fig, use_container_width=True)
    
    # 长生哲学解释
    st.markdown("### 🧘‍♂️ 长生哲学")
    st.markdown(system.get_longevity_cycle_explanation())

def render_four_seasons(system):
    """渲染四季详解"""
    st.markdown("## 🌸🌞🍂❄️ 四季修仙详解")
    
    seasons = ["春", "夏", "秋", "冬"]
    season_info = {
        "春": {"emoji": "🌸", "theme": "觉醒篇", "color": "#4CAF50"},
        "夏": {"emoji": "☀️", "theme": "成长篇", "color": "#FF9800"},
        "秋": {"emoji": "🍂", "theme": "膨胀篇", "color": "#F44336"},
        "冬": {"emoji": "❄️", "theme": "觉悟篇", "color": "#2196F3"}
    }
    
    df = system.create_perfect_dataframe()
    
    for season in seasons:
        info = season_info[season]
        season_data = df[df['季节'] == season]
        
        st.markdown(f"### {info['emoji']} {season}季 - {info['theme']}")
        
        # 季节进度条
        current_realm = system.get_current_realm()
        season_progress = 0
        if current_realm.season == season:
            season_levels = season_data['境界'].tolist()
            if current_realm.level in season_levels:
                season_progress = (season_levels.index(current_realm.level) + 1) / len(season_levels)
        
        st.progress(season_progress, text=f"{season}季进度")
        
        # 季节详情
        cols = st.columns(len(season_data))
        
        for i, (_, realm) in enumerate(season_data.iterrows()):
            with cols[i]:
                is_current = current_realm.level == realm['境界']
                is_completed = current_realm.level > realm['境界']
                
                if is_completed:
                    status = "✅"
                elif is_current:
                    status = "🔥"
                else:
                    status = "🔒"
                
                st.markdown(f"""
                <div style="border: 2px solid {info['color']}; border-radius: 10px; padding: 15px; margin: 10px 0; background-color: rgba{tuple(list(bytes.fromhex(info['color'][1:])) + [0.1])};">
                    <h4>{status} {realm['境界']}. {realm['等级名称']}</h4>
                    <p><strong>长生：</strong>{realm['长生阶段']}</p>
                    <p><strong>事件：</strong>{realm['核心事件']}</p>
                    <p><strong>GameFi：</strong>{realm['GameFi对应']}</p>
                    <p><strong>力量：</strong>{realm['力量等级']}</p>
                </div>
                """, unsafe_allow_html=True)

def render_gamefi_experience(system):
    """渲染GameFi体验"""
    st.markdown("## 🎮 GameFi修仙体验")
    st.markdown("*从傻逼到牛逼的完整修仙路径*")
    
    current_realm = system.get_current_realm()
    status = system.get_progress_status()
    
    # 当前状态展示
    col1, col2 = st.columns([2, 1])
    
    with col1:
        st.markdown(f"### 🔥 当前境界：{current_realm.realm_name}")
        st.markdown(f"**长生阶段**：{current_realm.longevity_stage}")
        st.markdown(f"**核心事件**：{current_realm.core_event}")
        st.markdown(f"**GameFi对应**：{current_realm.gamefi_mapping}")
        
        # 详细故事
        with st.expander("📖 查看详细故事"):
            st.markdown(f"**西游原文**：{current_realm.story_detail}")
            st.markdown(f"**投资教训**：{current_realm.investment_lesson}")
            st.markdown(f"**解锁条件**：{current_realm.unlock_condition}")
    
    with col2:
        st.markdown("### 📊 修仙属性")
        st.metric("力量等级", current_realm.power_level)
        st.metric("觉悟点数", status['觉悟点数'])
        st.metric("完成度", status['完成度'])
        
        # 升级按钮
        if st.button("🆙 突破境界", type="primary"):
            success, new_realm = system.advance_to_next_realm()
            if success and new_realm:
                st.success(f"🎉 成功突破到 {new_realm.realm_name}!")
                st.balloons()
                st.rerun()
            else:
                st.info("🏔️ 已达到五行山巅峰！")
    
    # 力量成长曲线
    st.markdown("### ⚡ 力量成长曲线")
    
    df = system.create_perfect_dataframe()
    
    fig = go.Figure()
    
    # 按季节分组
    seasons = ["春", "夏", "秋", "冬"]
    season_colors = {"春": "#4CAF50", "夏": "#FF9800", "秋": "#F44336", "冬": "#2196F3"}
    
    for season in seasons:
        season_data = df[df['季节'] == season]
        fig.add_trace(go.Scatter(
            x=season_data['境界'],
            y=season_data['力量等级'],
            mode='lines+markers',
            name=f"{season}季",
            line=dict(color=season_colors[season], width=4),
            marker=dict(size=12),
            hovertemplate='<b>%{customdata[0]}</b><br>' +
                         '境界: %{x}<br>' +
                         '力量: %{y}<br>' +
                         '长生: %{customdata[1]}<br>' +
                         'GameFi: %{customdata[2]}<extra></extra>',
            customdata=list(zip(season_data['等级名称'], season_data['长生阶段'], season_data['GameFi对应']))
        ))
    
    # 标记当前位置
    current_level = system.current_level
    current_power = current_realm.power_level
    
    fig.add_trace(go.Scatter(
        x=[current_level],
        y=[current_power],
        mode='markers',
        marker=dict(size=20, color='red', symbol='star'),
        name='当前位置',
        hovertemplate='<b>当前境界</b><br>%{x}级<br>力量: %{y}<extra></extra>'
    ))
    
    fig.update_layout(
        title="🐒 猴王修仙力量成长轨迹",
        xaxis_title="境界等级",
        yaxis_title="力量等级",
        height=500,
        hovermode='x unified'
    )
    
    st.plotly_chart(fig, use_container_width=True)
    
    # 成就系统
    st.markdown("### 🏆 修仙成就")
    
    achievements = [
        (3, "🌸 春季觉醒", "完成春季三境界"),
        (6, "☀️ 夏季成长", "完成夏季三境界"),
        (9, "🍂 秋季膨胀", "完成秋季三境界"),
        (12, "❄️ 冬季觉悟", "完成冬季三境界，成就齐天大圣")
    ]
    
    for level, title, desc in achievements:
        if system.current_level >= level:
            st.success(f"✅ {title} - {desc}")
        else:
            st.info(f"🔒 {title} - {desc}")

if __name__ == "__main__":
    st.set_page_config(
        page_title="完美十二境界",
        page_icon="🐒",
        layout="wide"
    )
    render_perfect_twelve_ui()