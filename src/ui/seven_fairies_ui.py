#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
七仙女基本面雷达 - 卯日星官星云中的七大恒星
监控科技巨头的三大另类基本面数据

作者: Assistant
创建时间: 2025-07-02
版本: v1.0
"""

import streamlit as st
import asyncio

# 在Streamlit的线程中为ib_insync设置事件循环
try:
    asyncio.get_running_loop()
except RuntimeError:
    asyncio.set_event_loop(asyncio.new_event_loop())

import pandas as pd
import numpy as np
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
import asyncio
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
import logging
import os
import xml.etree.ElementTree as ET
import math
from dotenv import load_dotenv

# 导入七姐妹基本面数据库管理器
try:
    from src.data.seven_sisters_db_manager import SevenSistersDBManager
    SISTERS_DB_AVAILABLE = True
except ImportError:
    SISTERS_DB_AVAILABLE = False

# 尝试导入IB相关模块
try:
    from ib_insync import IB, Stock
    IB_AVAILABLE = True
except ImportError:
    IB_AVAILABLE = False

# 设置日志
logger = logging.getLogger(__name__)

class SevenFairiesUI:
    """七仙女基本面雷达界面"""
    
    def __init__(self):
        """初始化七仙女UI"""
        self.seven_fairies = {
            "AAPL": {"name": "苹果", "star": "昴宿一", "emoji": "🍎"},
            "MSFT": {"name": "微软", "star": "昴宿二", "emoji": "🪟"},
            "GOOGL": {"name": "谷歌", "star": "昴宿三", "emoji": "🔍"},
            "AMZN": {"name": "亚马逊", "star": "昴宿四", "emoji": "📦"},
            "TSLA": {"name": "特斯拉", "star": "昴宿五", "emoji": "⚡"},
            "NVDA": {"name": "英伟达", "star": "昴宿六", "emoji": "🎮"},
            "META": {"name": "Meta", "star": "昴宿七", "emoji": "🌐"}
        }
        # 从环境变量加载IB配置，确保与.env文件同步
        load_dotenv(override=True)
        self.ib_host = os.getenv("IB_HOST", "127.0.0.1")
        self.ib_port = int(os.getenv("IB_PORT", 4002))
        self.ib_client_id = int(os.getenv("IB_CLIENT_ID", 2))

    def render(self):
        """渲染七仙女雷达界面"""
        st.title("🧚‍♀️ 七仙女基本面雷达")
        st.markdown("### 📡 卯日星官星云中的七大恒星监控")
        st.markdown("---")
        
        if not IB_AVAILABLE:
            self._show_unavailable_message()
            return
        
        self._render_control_panel()
        
        tab1, tab2, tab3, tab4 = st.tabs(["🌟 实时雷达", "📊 EPS横向比较", "📈 趋势分析", "🚨 异常预警"])
        
        with tab1:
            self._render_realtime_radar()
        with tab2:
            self._render_eps_comparison()
        with tab3:
            self._render_trend_analysis()
        with tab4:
            self._render_anomaly_alerts()

    def _show_unavailable_message(self):
        """显示IB模块不可用消息"""
        st.error("🚫 IB-insync模块未安装")
        st.info("请运行 `pip install ib-insync` 来安装依赖。")
        st.markdown("### 📊 演示数据")
        self._render_demo_data()

    def _render_control_panel(self):
        """渲染控制面板"""
        with st.sidebar:
            st.header("🎛️ 雷达控制")
            if st.button("🔄 立即刷新", type="primary", key="sidebar_refresh"):
                st.cache_data.clear()
                st.rerun()

    def _render_realtime_radar(self):
        """渲染实时雷达"""
        st.markdown("#### 🌟 七仙女实时状态")
        
        # 获取所有仙女的数据
        all_data = self._get_all_fairies_data()

        if not all_data:
            st.warning("无法从IB获取数据，请检查连接。")
            self._render_demo_data()
            return

        cols = st.columns(3)
        fairy_list = list(self.seven_fairies.items())
        
        for i in range(len(fairy_list)):
            symbol, info = fairy_list[i]
            data = all_data.get(symbol)
            
            col_idx = i % 3
            if i == 6: col_idx = 1

            with cols[col_idx]:
                if data:
                    self._render_fairy_card(symbol, info, data)
                else:
                    st.warning(f"无法获取 {symbol} 的数据")

        st.markdown("#### 🎯 七仙女综合雷达")
        self._render_comprehensive_radar(all_data)

    def _render_fairy_card(self, symbol: str, info: Dict, data: Dict):
        """渲染单个仙女卡片"""
        with st.container():
            st.markdown(f"""
            <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 10px; border-radius: 10px; margin-bottom: 10px; text-align: center; color: white;">
                <h4>{info['emoji']} {info['name']}</h4>
                <p>{info['star']} • {symbol}</p>
            </div>
            """, unsafe_allow_html=True)
            
            # 价格
            price = data.get('price', 0)
            st.metric("价格", f"${price:.2f}" if price else "N/A")

            # 晨星评级
            rating = data.get('morningstar_rating', 0)
            st.metric("晨星评级", "⭐" * rating if rating else "N/A")

            # 做空股数
            shares_short = data.get('shares_short', 0)
            st.metric("做空股数", f"{shares_short:,}" if shares_short else "N/A")

            # 分析师目标价
            target_price = data.get('analyst_target_price', 0.0)
            st.metric("分析师目标价", f"${target_price:.2f}" if target_price else "N/A")

    @st.cache_data(ttl=300)
    def _get_all_fairies_data(_self):
        """使用缓存获取所有仙女的数据"""
        try:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            results = loop.run_until_complete(_self._async_fetch_all_data())
            return results
        except Exception as e:
            logger.error(f"获取所有仙女数据时出错: {e}")
            st.error(f"数据获取失败: {e}")
            return {}

    async def _async_fetch_all_data(self) -> Dict[str, Dict]:
        """异步获取所有仙女的数据"""
        ib = IB()
        all_data = {}
        try:
            await ib.connectAsync(self.ib_host, self.ib_port, clientId=self.ib_client_id)
            tasks = [self._async_fetch_one_fairy(ib, symbol) for symbol in self.seven_fairies.keys()]
            results = await asyncio.gather(*tasks)
            for res in results:
                if res:
                    all_data.update(res)
        except Exception as e:
            logger.error(f"IB连接或数据获取失败: {e}")
        finally:
            if ib.isConnected():
                ib.disconnect()
        return all_data

    async def _async_fetch_one_fairy(self, ib: IB, symbol: str) -> Optional[Dict[str, Dict]]:
        """异步获取单个仙女的数据"""
        try:
            contract = Stock(symbol, 'SMART', 'USD')
            await ib.qualifyContractsAsync(contract)
            
            # 获取市场数据和基本面数据
            ticker = ib.reqMktData(contract, '', False, False)
            xml_data = await ib.reqFundamentalDataAsync(contract, 'ReportSnapshot')
            
            await asyncio.sleep(2)

            data = self._parse_fundamental_data(xml_data)
            data['price'] = ticker.last if ticker and ticker.last and not math.isnan(ticker.last) else 0
            
            ib.cancelMktData(contract)
            return {symbol: data}
        except Exception as e:
            logger.warning(f"获取 {symbol} 数据失败: {e}")
            return None

    def _parse_fundamental_data(self, xml_data: str) -> Dict:
        """解析IB返回的XML基本面数据"""
        data = {
            'morningstar_rating': 0,
            'shares_short': 0,
            'analyst_target_price': 0.0
        }
        if not xml_data: return data
        
        try:
            root = ET.fromstring(xml_data)
            
            # 查找分析师目标价
            target_price_node = root.find(".//ForecastData/Ratio[@FieldName='TargetPrice']/Value")
            if target_price_node is not None and target_price_node.text and target_price_node.text.strip():
                data['analyst_target_price'] = float(target_price_node.text.strip())

            # 注意：晨星评级和做空数据在ReportSnapshot中通常不可用
            # 此处保留逻辑以备将来报告类型更改
            rating_node = root.find(".//Ratio[@FieldName='Rating']")
            if rating_node is not None and rating_node.text and rating_node.text.strip():
                 data['morningstar_rating'] = int(float(rating_node.text.strip()))

            shares_short_node = root.find(".//Ratio[@FieldName='SharesShort']")
            if shares_short_node is not None and shares_short_node.text and shares_short_node.text.strip():
                 data['shares_short'] = int(float(shares_short_node.text.strip()))

        except Exception as e:
            logger.error(f"解析XML数据失败: {e}")
        return data

    def _get_fairy_data(self, symbol: str) -> Dict:
        """从缓存的全量数据中获取单个仙女的数据"""
        all_data = self._get_all_fairies_data()
        return all_data.get(symbol, self._get_demo_fairy_data(symbol))

    def _get_demo_fairy_data(self, symbol: str) -> Dict:
        """获取演示数据"""
        return {
            'price': np.random.uniform(100, 500),
            'morningstar_rating': np.random.randint(1, 6),
            'shares_short': np.random.randint(1000000, 50000000),
            'analyst_target_price': np.random.uniform(110, 550)
        }
    
    def _calculate_fairy_score(self, data: Dict) -> int:
        """计算仙女综合评分"""
        score = 50
        score += data.get('morningstar_rating', 0) * 5
        if data.get('shares_short', 0) > 20000000:
            score -= 20
        if data.get('analyst_target_price', 0) > data.get('price', 0) * 1.1:
            score += 10
        return max(0, min(100, score))

    def _render_comprehensive_radar(self, all_data: Dict):
        """渲染综合雷达图"""
        fairy_scores = {}
        for symbol, info in self.seven_fairies.items():
            data = all_data.get(symbol)
            if data:
                fairy_scores[info['name']] = self._calculate_fairy_score(data)
        
        if not fairy_scores:
            st.warning("无足够数据生成雷达图。")
            return

        fig = go.Figure()
        fig.add_trace(go.Scatterpolar(
            r=list(fairy_scores.values()),
            theta=list(fairy_scores.keys()),
            fill='toself',
            name='七仙女评分',
            line_color='rgb(102, 126, 234)'
        ))
        fig.update_layout(
            polar=dict(radialaxis=dict(visible=True, range=[0, 100])),
            showlegend=True,
            title="🧚‍♀️ 七仙女综合评分雷达图"
        )
        st.plotly_chart(fig, use_container_width=True)
    
    
    def _render_trend_analysis(self):
        """渲染趋势分析"""
        st.markdown("#### 📈 历史趋势分析")
        
        selected_fairy = st.selectbox(
            "选择仙女",
            options=list(self.seven_fairies.keys()),
            format_func=lambda x: f"{self.seven_fairies[x]['emoji']} {self.seven_fairies[x]['name']}",
            key="trend_analysis_fairy_selector"
        )
        
        # 生成趋势数据
        dates = pd.date_range(start='2024-01-01', end='2025-07-02', freq='D')
        trend_data = self._generate_trend_data(selected_fairy, dates)
        
        # 创建子图
        fig = make_subplots(
            rows=3, cols=1,
            subplot_titles=('做空百分比趋势', '目标价方差比趋势', '综合评分趋势'),
            vertical_spacing=0.1
        )
        
        # 做空百分比
        fig.add_trace(
            go.Scatter(x=dates, y=trend_data['short_interest'], 
                      name='做空百分比', line=dict(color='red')),
            row=1, col=1
        )
        
        # 目标价方差比
        fig.add_trace(
            go.Scatter(x=dates, y=trend_data['variance_ratio'], 
                      name='目标价方差比', line=dict(color='blue')),
            row=2, col=1
        )
        
        # 综合评分
        fig.add_trace(
            go.Scatter(x=dates, y=trend_data['score'], 
                      name='综合评分', line=dict(color='green')),
            row=3, col=1
        )
        
        fig.update_layout(height=800, title_text=f"{self.seven_fairies[selected_fairy]['name']} 趋势分析")
        st.plotly_chart(fig, use_container_width=True)
    
    def _generate_trend_data(self, symbol: str, dates) -> Dict:
        """生成趋势数据"""
        import random
        
        n = len(dates)
        base_short = {"AAPL": 2.5, "MSFT": 1.8, "GOOGL": 3.2, "AMZN": 4.1, 
                     "TSLA": 15.6, "NVDA": 8.3, "META": 6.7}
        
        # 生成带趋势的随机数据
        short_base = base_short.get(symbol, 5.0)
        short_trend = [short_base + random.uniform(-2, 2) + 0.01*i for i in range(n)]
        variance_trend = [random.uniform(10, 30) + 5*np.sin(i/30) for i in range(n)]
        score_trend = [random.uniform(40, 90) + 10*np.cos(i/50) for i in range(n)]
        
        return {
            'short_interest': short_trend,
            'variance_ratio': variance_trend,
            'score': score_trend
        }
    
    def _render_anomaly_alerts(self):
        """渲染异常预警"""
        st.markdown("#### 🚨 异常预警监控")
        
        # 检查异常
        alerts = self._check_anomalies()
        
        if alerts:
            for alert in alerts:
                if alert['level'] == 'high':
                    st.error(f"🔴 {alert['message']}")
                elif alert['level'] == 'medium':
                    st.warning(f"🟡 {alert['message']}")
                else:
                    st.info(f"🔵 {alert['message']}")
        else:
            st.success("✅ 所有仙女状态正常，无异常预警")
        
        # 预警历史
        st.markdown("#### 📋 预警历史")
        alert_history = self._get_alert_history()
        if alert_history:
            df = pd.DataFrame(alert_history)
            st.dataframe(df, use_container_width=True)
    
    def _check_anomalies(self) -> List[Dict]:
        """检查异常情况"""
        alerts = []
        
        for symbol, info in self.seven_fairies.items():
            data = self._get_fairy_data(symbol)
            
            # 检查做空百分比异常
            short_interest = data.get('short_interest', 0)
            if short_interest > 15:
                alerts.append({
                    'level': 'high',
                    'message': f"{info['name']} 做空百分比异常高: {short_interest:.2f}%"
                })
            elif short_interest > 10:
                alerts.append({
                    'level': 'medium',
                    'message': f"{info['name']} 做空百分比偏高: {short_interest:.2f}%"
                })
            
            # 检查目标价方差异常
            variance = data.get('target_variance_ratio', 0)
            if variance > 25:
                alerts.append({
                    'level': 'high',
                    'message': f"{info['name']} 目标价分歧过大: {variance:.2f}%"
                })
        
        return alerts
    
    def _get_alert_history(self) -> List[Dict]:
        """获取预警历史"""
        # 模拟预警历史数据
        return [
            {'时间': '2025-07-02 10:30', '仙女': '特斯拉仙子', '类型': '做空异常', '级别': '高'},
            {'时间': '2025-07-02 09:15', '仙女': '元宇宙仙子', '类型': '目标价分歧', '级别': '中'},
            {'时间': '2025-07-01 16:45', '仙女': '英伟达仙子', '类型': '评级下调', '级别': '中'},
        ]
    
    def _render_demo_data(self):
        """渲染演示数据"""
        st.markdown("#### 🎭 演示模式")
        
        demo_data = []
        for symbol, info in self.seven_fairies.items():
            data = self._get_demo_fairy_data(symbol)
            demo_data.append({
                '仙女': f"{info['emoji']} {info['name']}",
                '股票代码': symbol,
                '晨星评级': data['morningstar_rating'],
                '做空百分比': f"{data['short_interest']:.2f}%",
                '目标价方差比': f"{data['target_variance_ratio']:.2f}%",
                '综合评分': self._calculate_fairy_score(data)
            })
        
        df = pd.DataFrame(demo_data)
        st.dataframe(df, use_container_width=True)
    
    def _render_eps_comparison(self):
        """渲染EPS横向比较"""
        st.markdown("#### 📊 七姐妹EPS横向比较")
        st.markdown("*基于IB实时基本面数据的大白马EPS对比分析*")
        
        if not SISTERS_DB_AVAILABLE:
            st.error("❌ 七姐妹基本面数据库模块不可用")
            self._render_demo_eps_comparison()
            return
        
        # 控制按钮
        col1, col2, col3 = st.columns([1, 1, 2])
        with col1:
            if st.button("🔄 刷新数据", type="primary", key="eps_refresh_data"):
                st.cache_data.clear()
                st.rerun()
        
        with col2:
            show_relative = st.checkbox("显示相对指标", value=True, key="show_relative_metrics")
        
        # 获取数据
        with st.spinner("📊 正在从数据库获取七姐妹基本面数据..."):
            eps_data = self._get_sisters_eps_data_from_db()
        
        if eps_data.empty:
            st.warning("⚠️ 无法获取数据库数据，显示演示数据")
            st.info("💡 请先运行数据更新脚本: `python scripts/update_seven_sisters_data.py`")
            self._render_demo_eps_comparison()
            return
        
        # 显示数据表格
        st.markdown("##### 📋 基本面数据对比")
        
        # 格式化显示
        display_df = eps_data.copy()
        
        # 数值格式化
        numeric_cols = ['Current Price', 'Market Cap (M)', 'EPS (TTM)', 'PE Ratio', 
                       'EPS Growth %', 'Revenue Growth %', 'ROE %', 'Debt/Equity']
        
        for col in numeric_cols:
            if col in display_df.columns:
                if 'Growth %' in col or 'ROE %' in col:
                    display_df[col] = display_df[col].apply(lambda x: f"{x:.1f}%" if pd.notna(x) else "N/A")
                elif col == 'Market Cap (M)':
                    display_df[col] = display_df[col].apply(lambda x: f"{x:,.0f}M" if pd.notna(x) else "N/A")
                elif col in ['Current Price', 'EPS (TTM)']:
                    display_df[col] = display_df[col].apply(lambda x: f"${x:.2f}" if pd.notna(x) else "N/A")
                else:
                    display_df[col] = display_df[col].apply(lambda x: f"{x:.2f}" if pd.notna(x) else "N/A")
        
        st.dataframe(display_df, use_container_width=True)
        
        # EPS排名图表
        self._render_eps_charts(eps_data)
        
        # 相对指标分析
        if show_relative:
            self._render_relative_analysis_from_db(eps_data)
    
    @st.cache_data(ttl=3600)  # 1小时缓存，因为是每日更新的数据
    def _get_sisters_eps_data_from_db(_self) -> pd.DataFrame:
        """从数据库获取七姐妹EPS数据（带缓存）"""
        try:
            from src.data.seven_sisters_db_manager import SevenSistersDBManager
            
            manager = SevenSistersDBManager()
            
            # 获取最新基本面数据
            df = manager.get_latest_fundamentals()
            
            if df.empty:
                return pd.DataFrame()
            
            # 转换为EPS比较格式
            comparison_df = pd.DataFrame({
                'Symbol': df['symbol'],
                'Company': df['chinese_name'].fillna(df['company_name']),
                'Current Price': df['close_price'],
                'Market Cap (M)': df['market_cap'] / 1000000,  # 转换为百万
                'EPS (TTM)': df['eps_ttm'],
                'PE Ratio': df['pe_ratio'],
                'EPS Growth %': df['eps_growth_pct'],
                'Revenue Growth %': df['revenue_growth_pct'],
                'ROE %': df['roe_pct'] * 100,  # 转换为百分比
                'Debt/Equity': df['debt_to_equity'],
                'Last Updated': df['date']
            })
            
            # 按EPS排序
            comparison_df = comparison_df.sort_values('EPS (TTM)', ascending=False, na_position='last')
            
            return comparison_df
                
        except Exception as e:
            st.error(f"从数据库获取EPS数据失败: {e}")
            return pd.DataFrame()
    
    def _render_eps_charts(self, df: pd.DataFrame):
        """渲染EPS图表"""
        if df.empty:
            return
        
        col1, col2 = st.columns(2)
        
        with col1:
            st.markdown("##### 📈 EPS (TTM) 对比")
            if 'EPS (TTM)' in df.columns:
                fig_eps = px.bar(
                    df, 
                    x='Symbol', 
                    y='EPS (TTM)',
                    title="七姐妹EPS对比",
                    color='EPS (TTM)',
                    color_continuous_scale='Viridis'
                )
                fig_eps.update_layout(height=400)
                st.plotly_chart(fig_eps, use_container_width=True)
        
        with col2:
            st.markdown("##### 📊 PE比率对比")
            if 'PE Ratio' in df.columns:
                fig_pe = px.bar(
                    df, 
                    x='Symbol', 
                    y='PE Ratio',
                    title="七姐妹PE比率对比",
                    color='PE Ratio',
                    color_continuous_scale='RdYlBu_r'
                )
                fig_pe.update_layout(height=400)
                st.plotly_chart(fig_pe, use_container_width=True)
        
        # 增长率散点图
        st.markdown("##### 🚀 EPS增长率 vs PE比率")
        if all(col in df.columns for col in ['PE Ratio', 'EPS Growth %']):
            fig_scatter = px.scatter(
                df,
                x='PE Ratio',
                y='EPS Growth %',
                size='Market Cap (M)',
                color='Symbol',
                hover_data=['Company'],
                title="增长率与估值关系"
            )
            fig_scatter.update_layout(height=500)
            st.plotly_chart(fig_scatter, use_container_width=True)
    
    def _render_relative_analysis_from_db(self, df: pd.DataFrame):
        """从数据库渲染相对指标分析"""
        st.markdown("##### 🏆 相对指标排名")
        
        try:
            from src.data.seven_sisters_db_manager import SevenSistersDBManager
            manager = SevenSistersDBManager()
            
            # 从数据库获取排名数据
            ranking_df = manager.get_eps_ranking()
            
            if not ranking_df.empty:
                # 格式化显示列
                display_cols = ['symbol', 'chinese_name', 'close_price', 'eps_ttm', 'pe_ratio', 
                               'eps_growth_pct', 'composite_score', 'eps_rank']
                
                display_df = ranking_df[display_cols].copy()
                display_df.columns = ['Symbol', 'Company', 'Price', 'EPS (TTM)', 'PE Ratio', 
                                    'EPS Growth %', 'Score', 'Rank']
                
                # 格式化数值
                display_df['Price'] = display_df['Price'].apply(lambda x: f"${x:.2f}" if pd.notna(x) else "N/A")
                display_df['EPS (TTM)'] = display_df['EPS (TTM)'].apply(lambda x: f"${x:.2f}" if pd.notna(x) else "N/A")
                display_df['PE Ratio'] = display_df['PE Ratio'].apply(lambda x: f"{x:.1f}" if pd.notna(x) else "N/A")
                display_df['EPS Growth %'] = display_df['EPS Growth %'].apply(lambda x: f"{x:.1f}%" if pd.notna(x) else "N/A")
                display_df['Score'] = display_df['Score'].apply(lambda x: f"{x:.2f}" if pd.notna(x) else "N/A")
                
                st.dataframe(display_df, use_container_width=True)
                
                # 综合评分雷达图
                st.markdown("##### 🎯 综合评分雷达图")
                fig_radar = go.Figure()
                
                # 使用emoji作为标签
                labels = [f"{row['emoji']} {row['symbol']}" for _, row in ranking_df.iterrows()]
                scores = ranking_df['composite_score'].tolist()
                
                fig_radar.add_trace(go.Scatterpolar(
                    r=scores,
                    theta=labels,
                    fill='toself',
                    name='综合评分',
                    line_color='rgb(102, 126, 234)',
                    fillcolor='rgba(102, 126, 234, 0.3)'
                ))
                
                fig_radar.update_layout(
                    polar=dict(
                        radialaxis=dict(
                            visible=True, 
                            range=[0, max(scores) * 1.1] if scores else [0, 10]
                        )
                    ),
                    showlegend=True,
                    title="七姐妹综合评分雷达图",
                    height=500
                )
                st.plotly_chart(fig_radar, use_container_width=True)
                
                # 数据更新信息
                if not ranking_df.empty:
                    last_update = ranking_df['date'].iloc[0] if 'date' in ranking_df.columns else "未知"
                    st.info(f"📅 数据更新时间: {last_update}")
                
            else:
                st.warning("⚠️ 数据库中没有排名数据")
                
        except Exception as e:
            st.error(f"从数据库获取排名数据失败: {e}")
            
            # 降级到基本计算
            st.markdown("##### 📊 基本排名（基于当前数据）")
            if not df.empty and 'EPS (TTM)' in df.columns:
                basic_ranking = df[['Symbol', 'Company', 'EPS (TTM)', 'PE Ratio', 'EPS Growth %']].copy()
                basic_ranking = basic_ranking.sort_values('EPS (TTM)', ascending=False, na_position='last')
                basic_ranking['Rank'] = range(1, len(basic_ranking) + 1)
                st.dataframe(basic_ranking, use_container_width=True)
    
    def _render_demo_eps_comparison(self):
        """渲染演示EPS比较数据"""
        st.markdown("##### 🎭 演示数据")
        
        # 生成演示数据
        demo_eps_data = []
        for symbol, info in self.seven_fairies.items():
            demo_eps_data.append({
                'Symbol': symbol,
                'Company': info['name'],
                'Current Price': np.random.uniform(100, 500),
                'Market Cap (M)': np.random.uniform(500000, 3000000),
                'EPS (TTM)': np.random.uniform(2, 15),
                'PE Ratio': np.random.uniform(15, 45),
                'EPS Growth %': np.random.uniform(-10, 30),
                'Revenue Growth %': np.random.uniform(-5, 25),
                'ROE %': np.random.uniform(10, 40),
                'Debt/Equity': np.random.uniform(0.1, 2.0)
            })
        
        demo_df = pd.DataFrame(demo_eps_data)
        demo_df = demo_df.sort_values('EPS (TTM)', ascending=False)
        
        st.dataframe(demo_df, use_container_width=True)
        
        # 演示图表
        col1, col2 = st.columns(2)
        with col1:
            fig_demo = px.bar(demo_df, x='Symbol', y='EPS (TTM)', title="演示：EPS对比")
            st.plotly_chart(fig_demo, use_container_width=True)
        
        with col2:
            fig_demo2 = px.bar(demo_df, x='Symbol', y='PE Ratio', title="演示：PE比率对比")
            st.plotly_chart(fig_demo2, use_container_width=True)
