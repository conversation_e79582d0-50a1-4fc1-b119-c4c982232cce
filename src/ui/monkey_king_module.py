# -*- coding: utf-8 -*-
"""
🐒 猴王修仙核心模块
整合所有修仙相关的GameFi系统，实现渐进式解锁
"""

import streamlit as st

def render_monkey_king_module():
    """渲染猴王修仙核心模块"""
    
    st.markdown("# 🐒 猴王修仙 - 从傻逼到牛逼的完整修仙路径")
    st.markdown("*灵根育孕源流出，心性修持大道生*")
    
    # 修仙进度追踪
    if 'cultivation_progress' not in st.session_state:
        st.session_state.cultivation_progress = {
            'current_level': 1,
            'unlocked_systems': ['monkey_king_basic'],
            'total_experience': 0
        }
    
    progress = st.session_state.cultivation_progress
    
    # 顶部进度显示
    col1, col2, col3 = st.columns(3)
    
    with col1:
        st.metric("修仙等级", f"Lv.{progress['current_level']}")
    with col2:
        st.metric("解锁系统", f"{len(progress['unlocked_systems'])}/7")
    with col3:
        st.metric("修仙经验", progress['total_experience'])
    
    # 渐进式解锁的修仙系统
    cultivation_systems = [
        {
            'id': 'monkey_king_basic',
            'name': '🐒 猴王修仙',
            'description': '经典十二境界修仙路径',
            'unlock_level': 1,
            'unlock_condition': '初入修仙界'
        },
        {
            'id': 'twelve_longevity',
            'name': '🔄 十二长生',
            'description': '传统命理学的现代演绎',
            'unlock_level': 3,
            'unlock_condition': '完成"见生死"境界'
        },
        {
            'id': 'perfect_twelve',
            'name': '🏆 完美十二境界',
            'description': '境界×长生×GameFi的完美融合',
            'unlock_level': 6,
            'unlock_condition': '达到"得偿所望"境界'
        },
        {
            'id': 'dapp_gamefi',
            'name': '🚀 DApp GameFi',
            'description': '男人的勋章是伤疤',
            'unlock_level': 9,
            'unlock_condition': '经历"蟠桃盛宴"的教训'
        },
        {
            'id': 'function_arrow',
            'name': '🏹 函数射箭',
            'description': '道法、妖术、神迹的函数本质',
            'unlock_level': 12,
            'unlock_condition': '压在"五行山下"后的觉悟'
        },
        {
            'id': 'aleatory_contract',
            'name': '🎲 射幸合同',
            'description': '揭示交易系统与彩票的真相',
            'unlock_level': 15,
            'unlock_condition': '理解射幸合同的本质'
        },
        {
            'id': 'dadaosheng',
            'name': '🌸 大道生',
            'description': '二十四花神修仙路的终极智慧',
            'unlock_level': 20,
            'unlock_condition': '获得"佛旨开恩"'
        }
    ]
    
    # 显示当前可用的修仙系统
    st.markdown("## 🎯 修仙系统")
    
    # 检查解锁状态
    available_systems = []
    locked_systems = []
    
    for system in cultivation_systems:
        if progress['current_level'] >= system['unlock_level']:
            available_systems.append(system)
            if system['id'] not in progress['unlocked_systems']:
                progress['unlocked_systems'].append(system['id'])
        else:
            locked_systems.append(system)
    
    # 显示可用系统
    if available_systems:
        st.markdown("### ✅ 已解锁系统")
        
        # 创建系统选择
        system_names = [sys['name'] for sys in available_systems]
        selected_system = st.selectbox(
            "选择修仙系统",
            options=system_names,
            help="选择您要进入的修仙系统"
        )
        
        # 渲染选中的系统
        selected_id = None
        for system in available_systems:
            if system['name'] == selected_system:
                selected_id = system['id']
                break
        
        if selected_id:
            render_selected_system(selected_id)
    
    # 显示锁定系统（预览）
    if locked_systems:
        st.markdown("### 🔒 未解锁系统")
        
        for system in locked_systems[:3]:  # 只显示接下来的3个
            with st.expander(f"🔒 {system['name']} (需要Lv.{system['unlock_level']})", expanded=False):
                st.markdown(f"**{system['description']}**")
                st.markdown(f"解锁条件: {system['unlock_condition']}")
                
                # 显示解锁进度
                progress_percent = min(100, (progress['current_level'] / system['unlock_level']) * 100)
                st.progress(progress_percent / 100, text=f"解锁进度: {progress_percent:.1f}%")
    
    # 修仙经验获取
    st.markdown("---")
    col1, col2 = st.columns(2)
    
    with col1:
        if st.button("🧘‍♂️ 修炼获得经验", type="primary"):
            gained_exp = 10
            progress['total_experience'] += gained_exp
            
            # 检查是否升级
            if progress['total_experience'] >= progress['current_level'] * 100:
                progress['current_level'] += 1
                st.success(f"🎉 恭喜升级到 Lv.{progress['current_level']}!")
                st.balloons()
            else:
                st.success(f"📈 获得 {gained_exp} 点修仙经验!")
            
            st.rerun()
    
    with col2:
        if st.button("🔄 重置修仙进度"):
            st.session_state.cultivation_progress = {
                'current_level': 1,
                'unlocked_systems': ['monkey_king_basic'],
                'total_experience': 0
            }
            st.info("🐒 修仙进度已重置")
            st.rerun()

def render_selected_system(system_id: str):
    """渲染选中的修仙系统"""
    
    try:
        if system_id == 'monkey_king_basic':
            from src.ui.monkey_king_gamefi_ui import render_monkey_king_gamefi
            st.markdown("---")
            render_monkey_king_gamefi()
            
        elif system_id == 'twelve_longevity':
            from src.ui.twelve_longevity_ui import render_twelve_longevity_ui
            st.markdown("---")
            render_twelve_longevity_ui()
            
        elif system_id == 'perfect_twelve':
            from src.ui.perfect_twelve_ui import render_perfect_twelve_ui
            st.markdown("---")
            render_perfect_twelve_ui()
            
        elif system_id == 'dapp_gamefi':
            from src.ui.dapp_gamefi_ui import render_dapp_gamefi
            st.markdown("---")
            render_dapp_gamefi()
            
        elif system_id == 'function_arrow':
            from src.ui.function_arrow_ui import render_function_arrow_ui
            st.markdown("---")
            render_function_arrow_ui()
            
        elif system_id == 'aleatory_contract':
            from src.ui.aleatory_contract_ui import render_aleatory_contract_ui
            st.markdown("---")
            render_aleatory_contract_ui()
            
        elif system_id == 'dadaosheng':
            from src.ui.dadaosheng_ui import render_dadaosheng_ui
            st.markdown("---")
            render_dadaosheng_ui()
            
    except ImportError as e:
        st.error(f"❌ 系统加载失败: {e}")
        st.info("🔧 请检查系统模块是否正确安装")

if __name__ == "__main__":
    st.set_page_config(
        page_title="猴王修仙核心模块",
        page_icon="🐒",
        layout="wide"
    )
    render_monkey_king_module()