# -*- coding: utf-8 -*-
"""
🏹 函数射箭GameFi界面
基于您的哲学洞察：道法、妖术、神迹的函数本质
"""

import streamlit as st
import plotly.graph_objects as go
import plotly.express as px
import pandas as pd
import numpy as np
from src.core.function_arrow_gamefi import FunctionArrowGameFi, ArrowType, TargetType
import random

def render_function_arrow_ui():
    """渲染函数射箭GameFi界面"""
    
    st.markdown("# 🏹 函数射箭GameFi系统")
    st.markdown("*道法、妖术、神迹 - 突破传统认知的函数过程*")
    
    # 哲学引言
    with st.expander("📜 哲学思想"):
        st.markdown("""
        > **"道法，妖术，神迹，从本质上来说，都是突破传统认知的函数过程。"**
        
        > **"函数的本质是一种射箭的过程。刑法论的核心其实就是射箭。"**
        
        > **"所谓犯罪，必须是主观要射箭，客观箭头中靶，而且是因为主观导致的客观。"**
        
        > **"倒行逆施，呵佛骂祖，方得正果。"**
        
        这个系统将投资决策抽象为**函数射箭过程**，每一次决策都是一次射箭，
        需要**主观意图**、**客观结果**和**因果关系**三要素齐备。
        """)
    
    # 初始化系统
    if 'arrow_gamefi' not in st.session_state:
        st.session_state.arrow_gamefi = FunctionArrowGameFi("philosopher_archer")
    
    gamefi = st.session_state.arrow_gamefi
    
    # 顶部状态栏
    col1, col2, col3, col4, col5 = st.columns(5)
    
    with col1:
        st.metric("总得分", f"{gamefi.total_score:.1f}")
    with col2:
        st.metric("命中率", f"{gamefi.accuracy_rate:.1%}")
    with col3:
        st.metric("觉悟等级", gamefi.enlightenment_level)
    with col4:
        st.metric("神箭次数", gamefi.divine_shots)
    with col5:
        st.metric("疯话成真", gamefi.chaos_successes)
    
    # 主要内容标签页
    tab1, tab2, tab3, tab4 = st.tabs([
        "🏹 射箭练习", 
        "🏗️ 沙塔建造", 
        "📊 模式分析",
        "🎯 射箭场"
    ])
    
    with tab1:
        render_shooting_practice(gamefi)
    
    with tab2:
        render_sand_tower(gamefi)
    
    with tab3:
        render_pattern_analysis(gamefi)
    
    with tab4:
        render_shooting_range(gamefi)

def render_shooting_practice(gamefi):
    """渲染射箭练习"""
    st.markdown("## 🏹 函数射箭练习")
    st.markdown("*每一箭都是一次函数调用，从主观意图到客观结果*")
    
    # 射箭参数设置
    col1, col2 = st.columns([2, 1])
    
    with col1:
        st.markdown("### 🎯 射箭参数")
        
        # 主观意图
        intent = st.text_area(
            "主观意图 (为什么要射这一箭？)",
            placeholder="例如：我认为市场被低估，要逆向投资...",
            height=100
        )
        
        # 箭矢类型
        arrow_type = st.selectbox(
            "箭矢类型",
            options=list(ArrowType),
            format_func=lambda x: f"{x.arrow_name} - {x.description} (倍率: {x.multiplier}x)"
        )
        
        # 瞄准方向
        aim_direction = st.slider(
            "瞄准方向 (度)",
            min_value=0.0,
            max_value=360.0,
            value=0.0,
            step=1.0,
            help="0度=正前方，90度=右侧，180度=后方，270度=左侧"
        )
        
        # 用力大小
        force = st.slider(
            "用力大小",
            min_value=0.0,
            max_value=100.0,
            value=70.0,
            step=1.0,
            help="力量越大，射程越远，但也可能过头"
        )
        
        # 市场背景
        market_context = st.text_input(
            "市场背景",
            placeholder="例如：牛市末期，市场情绪极度乐观..."
        )
    
    with col2:
        st.markdown("### 🎲 箭矢说明")
        
        for arrow in ArrowType:
            if arrow == arrow_type:
                st.success(f"**{arrow.arrow_name}** (选中)")
            else:
                st.info(f"**{arrow.arrow_name}**")
            st.caption(f"{arrow.description} - 倍率: {arrow.multiplier}x")
        
        st.markdown("### ⚖️ 刑法论三要素")
        st.markdown("""
        1. **主观要射箭** - 明确的投资意图
        2. **客观箭头中靶** - 实际的市场结果  
        3. **因果关系** - 结果由意图导致
        """)
    
    # 射箭按钮
    if st.button("🏹 射箭！", type="primary", disabled=not intent.strip()):
        if intent.strip():
            # 执行射箭
            arrow = gamefi.shoot_arrow(
                intent=intent,
                arrow_type=arrow_type,
                aim_direction=aim_direction,
                force=force,
                market_context=market_context or "未指定市场背景"
            )
            
            # 显示结果
            st.markdown("---")
            st.markdown("### 🎯 射箭结果")
            
            result_col1, result_col2 = st.columns(2)
            
            with result_col1:
                # 基本结果
                if arrow.hit_target.score > 80:
                    st.success(f"🎯 {arrow.hit_target.target_name}！")
                elif arrow.hit_target.score > 50:
                    st.info(f"🎯 {arrow.hit_target.target_name}")
                elif arrow.hit_target.score > 0:
                    st.warning(f"🎯 {arrow.hit_target.target_name}")
                else:
                    st.error(f"🎯 {arrow.hit_target.target_name}")
                
                st.metric("得分", f"{arrow.final_score:.1f}")
                st.metric("距离靶心", f"{arrow.distance_from_center:.1f}")
                
                # 特殊成就
                if arrow.arrow_type == ArrowType.CHAOS_ARROW and arrow.hit_target.score > 50:
                    st.balloons()
                    st.success("🎉 疯话成真！倒行逆施，呵佛骂祖，方得正果！")
                
                if arrow.hit_target == TargetType.BULL_EYE:
                    st.balloons()
                    st.success("🏆 正中靶心！函数射箭大师！")
            
            with result_col2:
                # 详细分析
                st.markdown("**📊 详细分析**")
                st.markdown(f"**主观意图**: {arrow.subjective_intent}")
                st.markdown(f"**客观结果**: {arrow.objective_result}")
                st.markdown(f"**因果关系**: {'✅ 成立' if arrow.causal_relationship else '❌ 不成立'}")
                st.markdown(f"**市场风向**: {arrow.wind_factor:.1f}")
                st.markdown(f"**实际落点**: ({arrow.actual_position[0]:.1f}, {arrow.actual_position[1]:.1f})")
            
            st.rerun()
    
    # 最近射箭记录
    if gamefi.arrows_shot:
        st.markdown("### 📜 最近射箭记录")
        
        recent_arrows = gamefi.arrows_shot[-5:]  # 显示最近5箭
        
        for i, arrow in enumerate(reversed(recent_arrows), 1):
            with st.expander(f"第{len(gamefi.arrows_shot) - i + 1}箭 - {arrow.arrow_type.arrow_name} - {arrow.hit_target.target_name}"):
                st.markdown(f"**意图**: {arrow.subjective_intent}")
                st.markdown(f"**结果**: {arrow.objective_result}")
                st.markdown(f"**得分**: {arrow.final_score:.1f}")
                st.markdown(f"**时间**: {arrow.timestamp.strftime('%Y-%m-%d %H:%M:%S')}")

def render_sand_tower(gamefi):
    """渲染沙塔建造"""
    st.markdown("## 🏗️ 聚沙成塔 - 非对称加密的神迹")
    st.markdown("*把一个非对称加密作为第一粒沙，最终聚沙成塔*")
    
    tower_status = gamefi.get_tower_status()
    
    if "message" in tower_status:
        st.info(tower_status["message"])
        return
    
    # 沙塔状态
    col1, col2, col3 = st.columns(3)
    
    with col1:
        st.metric("沙粒总数", tower_status["total_grains"])
        st.metric("塔高", f"{tower_status['tower_height']:.2f}米")
    
    with col2:
        st.metric("总能量", f"{tower_status['total_power']:.1f}")
        is_miracle = tower_status["is_miracle"]
        st.metric("神迹状态", "✨ 已成神迹" if is_miracle else "🏗️ 建造中")
    
    with col3:
        st.markdown("**🔐 非对称基础**")
        st.code(tower_status["asymmetric_foundation"])
        st.caption("第一粒沙的加密种子")
    
    # 沙粒类型分布
    st.markdown("### 📊 沙粒类型分布")
    
    grain_types = tower_status["grain_types"]
    if grain_types:
        # 创建饼图
        fig = go.Figure(data=[go.Pie(
            labels=list(grain_types.keys()),
            values=list(grain_types.values()),
            hole=0.3,
            textinfo='label+percent',
            textposition='auto'
        )])
        
        fig.update_layout(
            title="沙粒类型分布",
            height=400
        )
        
        st.plotly_chart(fig, use_container_width=True)
        
        # 沙粒说明
        st.markdown("### 📝 沙粒类型说明")
        grain_descriptions = {
            "智慧沙粒": "正中靶心获得，代表完美的洞察",
            "疯狂沙粒": "疯话成真获得，代表突破常规的智慧",
            "因果沙粒": "因果关系成立获得，代表逻辑的力量",
            "经验沙粒": "普通射箭获得，代表积累的经验"
        }
        
        for grain_type, count in grain_types.items():
            description = grain_descriptions.get(grain_type, "未知类型")
            st.markdown(f"**{grain_type}** ({count}粒): {description}")
    
    # 沙塔可视化
    st.markdown("### 🏗️ 沙塔可视化")
    
    if gamefi.sand_tower:
        # 创建沙塔高度图
        tower_data = []
        cumulative_height = 0
        
        for i, grain in enumerate(gamefi.sand_tower):
            cumulative_height += 0.1
            tower_data.append({
                "层数": i + 1,
                "高度": cumulative_height,
                "类型": grain.grain_type,
                "能量": grain.value,
                "来源": grain.origin_arrow
            })
        
        df_tower = pd.DataFrame(tower_data)
        
        # 创建堆叠柱状图
        fig = px.bar(
            df_tower, 
            x="层数", 
            y="能量",
            color="类型",
            title="沙塔能量分布",
            hover_data=["高度", "来源"]
        )
        
        fig.update_layout(height=400)
        st.plotly_chart(fig, use_container_width=True)

def render_pattern_analysis(gamefi):
    """渲染模式分析"""
    st.markdown("## 📊 射箭模式分析")
    st.markdown("*寻找函数规律，发现射箭的奥秘*")
    
    analysis = gamefi.analyze_shooting_pattern()
    
    if "message" in analysis:
        st.info(analysis["message"])
        return
    
    # 基础统计
    col1, col2, col3 = st.columns(3)
    
    with col1:
        st.metric("总射箭数", analysis["total_shots"])
        st.metric("命中率", analysis["accuracy_rate"])
    
    with col2:
        st.metric("成功射箭", analysis["successful_shots"])
        st.metric("觉悟等级", analysis["enlightenment_level"])
    
    with col3:
        st.metric("疯话成真", analysis["chaos_successes"])
        st.metric("神箭次数", analysis["divine_shots"])
    
    # 模式洞察
    st.markdown("### 🧘‍♂️ 模式洞察")
    st.success(f"💡 {analysis['pattern_insight']}")
    
    # 箭矢偏好分析
    st.markdown("### 🏹 箭矢偏好分析")
    
    preferences = analysis["arrow_preferences"]
    if preferences:
        fig = go.Figure(data=[go.Bar(
            x=list(preferences.keys()),
            y=list(preferences.values()),
            text=list(preferences.values()),
            textposition='auto'
        )])
        
        fig.update_layout(
            title="箭矢类型使用频率",
            xaxis_title="箭矢类型",
            yaxis_title="使用次数",
            height=400
        )
        
        st.plotly_chart(fig, use_container_width=True)
    
    # 最优参数
    if analysis["successful_shots"] > 0:
        st.markdown("### 🎯 最优射箭参数")
        
        col1, col2 = st.columns(2)
        
        with col1:
            st.metric("最优力量", f"{analysis['optimal_force']:.1f}")
        
        with col2:
            st.metric("最优方向", f"{analysis['optimal_direction']:.1f}°")
        
        st.info("💡 这些参数是基于您成功射箭的平均值计算得出")

def render_shooting_range(gamefi):
    """渲染射箭场可视化"""
    st.markdown("## 🎯 射箭场可视化")
    st.markdown("*观察您的射箭轨迹，发现函数的美妙*")
    
    if not gamefi.arrows_shot:
        st.info("还没有射箭记录，去射几箭试试吧！")
        return
    
    # 创建射箭场图
    fig = go.Figure()
    
    # 添加靶心
    target_circles = [
        (10, "正中靶心", "gold"),
        (25, "内环", "silver"), 
        (50, "外环", "orange"),
        (80, "擦边", "lightcoral")
    ]
    
    for radius, name, color in target_circles:
        fig.add_shape(
            type="circle",
            x0=-radius, y0=-radius,
            x1=radius, y1=radius,
            line=dict(color=color, width=2),
            fillcolor=color,
            opacity=0.1
        )
        
        # 添加标签
        fig.add_annotation(
            x=radius * 0.7, y=radius * 0.7,
            text=name,
            showarrow=False,
            font=dict(size=10, color=color)
        )
    
    # 添加射箭轨迹
    arrow_colors = {
        ArrowType.MORTAL_ARROW: "blue",
        ArrowType.MAGIC_ARROW: "purple", 
        ArrowType.DIVINE_ARROW: "gold",
        ArrowType.CHAOS_ARROW: "red"
    }
    
    for arrow in gamefi.arrows_shot:
        color = arrow_colors.get(arrow.arrow_type, "gray")
        
        # 射箭轨迹线
        fig.add_trace(go.Scatter(
            x=[0, arrow.actual_position[0]],
            y=[0, arrow.actual_position[1]],
            mode='lines+markers',
            line=dict(color=color, width=2),
            marker=dict(size=[8, 12], color=color),
            name=f"{arrow.arrow_type.arrow_name}",
            hovertemplate=f'<b>{arrow.arrow_type.arrow_name}</b><br>' +
                         f'意图: {arrow.subjective_intent[:30]}...<br>' +
                         f'结果: {arrow.hit_target.target_name}<br>' +
                         f'得分: {arrow.final_score:.1f}<extra></extra>',
            showlegend=False
        ))
    
    # 设置图表布局
    fig.update_layout(
        title="🎯 射箭场轨迹图",
        xaxis=dict(
            title="X轴",
            range=[-100, 100],
            zeroline=True,
            zerolinecolor="black",
            zerolinewidth=2
        ),
        yaxis=dict(
            title="Y轴", 
            range=[-100, 100],
            zeroline=True,
            zerolinecolor="black",
            zerolinewidth=2
        ),
        height=600,
        showlegend=True
    )
    
    # 添加中心点
    fig.add_trace(go.Scatter(
        x=[0], y=[0],
        mode='markers',
        marker=dict(size=15, color="red", symbol="x"),
        name="靶心",
        hovertemplate="🎯 靶心中央<extra></extra>"
    ))
    
    st.plotly_chart(fig, use_container_width=True)
    
    # 射箭统计
    st.markdown("### 📈 射箭统计")
    
    if len(gamefi.arrows_shot) >= 2:
        # 创建时间序列图
        arrow_data = []
        for i, arrow in enumerate(gamefi.arrows_shot):
            arrow_data.append({
                "射箭序号": i + 1,
                "得分": arrow.final_score,
                "距离靶心": arrow.distance_from_center,
                "箭矢类型": arrow.arrow_type.arrow_name,
                "时间": arrow.timestamp
            })
        
        df_arrows = pd.DataFrame(arrow_data)
        
        # 得分趋势图
        fig_trend = px.line(
            df_arrows,
            x="射箭序号",
            y="得分", 
            color="箭矢类型",
            title="射箭得分趋势",
            markers=True
        )
        
        fig_trend.update_layout(height=400)
        st.plotly_chart(fig_trend, use_container_width=True)

if __name__ == "__main__":
    st.set_page_config(
        page_title="函数射箭GameFi",
        page_icon="🏹",
        layout="wide"
    )
    render_function_arrow_ui()