# -*- coding: utf-8 -*-
"""
散户小剧场UI界面
展示散户在重大事件冲击下的行为模拟
"""

import streamlit as st
import pandas as pd
import plotly.graph_objects as go
import plotly.express as px
from datetime import datetime, timedelta
import asyncio
from typing import Dict, List, Any

from ..core.retail_investor_theater import RetailInvestorTheater, MarketEvent, InvestorType


class RetailTheaterUI:
    """散户小剧场UI界面"""
    
    def __init__(self):
        self.theater = RetailInvestorTheater()
        
        # 预设一些经典事件
        self.classic_events = {
            "伊朗以色列冲突": MarketEvent(
                title="伊朗向以色列发射导弹",
                description="伊朗向以色列发射数百枚导弹，中东地缘政治紧张局势升级，国际原油价格大幅波动",
                impact_level=8,
                affected_sectors=["石油", "军工", "黄金", "航空"],
                event_type="geopolitical",
                timestamp=datetime.now(),
                duration_days=3
            ),
            "美联储加息": MarketEvent(
                title="美联储意外大幅加息75个基点",
                description="美联储为抑制通胀意外大幅加息，全球股市暴跌，美元走强",
                impact_level=9,
                affected_sectors=["银行", "房地产", "科技", "新兴市场"],
                event_type="economic",
                timestamp=datetime.now(),
                duration_days=5
            ),
            "ChatGPT发布": MarketEvent(
                title="OpenAI发布ChatGPT引发AI革命",
                description="ChatGPT的发布引发全球AI热潮，相关概念股暴涨",
                impact_level=7,
                affected_sectors=["人工智能", "芯片", "软件", "教育"],
                event_type="technological",
                timestamp=datetime.now(),
                duration_days=10
            ),
            "硅谷银行倒闭": MarketEvent(
                title="硅谷银行突然倒闭引发银行业恐慌",
                description="硅谷银行因流动性危机突然倒闭，引发全球银行股暴跌",
                impact_level=8,
                affected_sectors=["银行", "科技", "风投", "加密货币"],
                event_type="financial",
                timestamp=datetime.now(),
                duration_days=7
            )
        }
    
    def render(self):
        """渲染主界面"""
        st.title("🎭 散户小剧场")
        st.markdown("*观察散户在重大事件冲击下的真实反应，娱乐与教育并重*")
        
        # 功能标签页
        tab1, tab2, tab3, tab4 = st.tabs(["🎬 实时剧场", "📚 经典回顾", "📊 行为分析", "🎯 投教中心"])
        
        with tab1:
            self._render_live_theater()
        
        with tab2:
            self._render_classic_events()
        
        with tab3:
            self._render_behavior_analysis()
        
        with tab4:
            self._render_education_center()
    
    def _render_live_theater(self):
        """渲染实时剧场"""
        st.subheader("🎬 实时散户剧场")
        
        # 事件选择或自定义
        col1, col2 = st.columns([2, 1])
        
        with col1:
            event_source = st.radio(
                "选择事件来源",
                ["预设经典事件", "自定义事件", "实时新闻事件"],
                horizontal=True
            )
        
        with col2:
            if st.button("🎭 开始表演", type="primary"):
                st.session_state['start_simulation'] = True
        
        # 根据选择显示不同的输入界面
        if event_source == "预设经典事件":
            selected_event_name = st.selectbox(
                "选择经典事件",
                list(self.classic_events.keys())
            )
            
            if selected_event_name:
                event = self.classic_events[selected_event_name]
                self._display_event_info(event)
                
                if st.session_state.get('start_simulation'):
                    self._run_simulation(event)
        
        elif event_source == "自定义事件":
            self._render_custom_event_form()
        
        elif event_source == "实时新闻事件":
            st.info("🚧 实时新闻事件功能开发中，将接入新闻API自动识别重大事件")
    
    def _render_custom_event_form(self):
        """渲染自定义事件表单"""
        st.subheader("📝 自定义市场事件")
        
        with st.form("custom_event_form"):
            col1, col2 = st.columns(2)
            
            with col1:
                title = st.text_input("事件标题", placeholder="例如：某公司发布重大利好消息")
                event_type = st.selectbox(
                    "事件类型",
                    ["geopolitical", "economic", "corporate", "technological", "natural"],
                    format_func=lambda x: {
                        "geopolitical": "地缘政治",
                        "economic": "经济政策", 
                        "corporate": "公司事件",
                        "technological": "技术突破",
                        "natural": "自然灾害"
                    }[x]
                )
            
            with col2:
                impact_level = st.slider("影响等级", 1, 10, 5)
                duration_days = st.number_input("持续天数", 1, 30, 3)
            
            description = st.text_area(
                "事件描述",
                placeholder="详细描述事件的背景、影响和可能的后果...",
                key="retail_theater_event_description"
            )
            
            affected_sectors = st.multiselect(
                "影响板块",
                ["石油", "军工", "黄金", "银行", "科技", "房地产", "医药", "消费", "新能源", "芯片"],
                default=["科技"]
            )
            
            submitted = st.form_submit_button("🎭 开始模拟")
            
            if submitted and title and description:
                custom_event = MarketEvent(
                    title=title,
                    description=description,
                    impact_level=impact_level,
                    affected_sectors=affected_sectors,
                    event_type=event_type,
                    timestamp=datetime.now(),
                    duration_days=duration_days
                )
                
                self._run_simulation(custom_event)
    
    def _display_event_info(self, event: MarketEvent):
        """显示事件信息"""
        st.markdown("### 📰 事件详情")
        
        col1, col2, col3 = st.columns(3)
        
        with col1:
            st.metric("影响等级", f"{event.impact_level}/10")
        
        with col2:
            st.metric("持续天数", f"{event.duration_days}天")
        
        with col3:
            st.metric("影响板块", len(event.affected_sectors))
        
        st.write(f"**事件描述**: {event.description}")
        st.write(f"**影响板块**: {', '.join(event.affected_sectors)}")
    
    def _run_simulation(self, event: MarketEvent):
        """运行模拟"""
        st.markdown("---")
        st.subheader("🎭 散户反应模拟")
        
        with st.spinner("散户们正在激烈讨论中..."):
            # 运行异步模拟
            try:
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                result = loop.run_until_complete(self.theater.run_live_simulation(event))
                loop.close()
                
                # 显示结果
                self._display_simulation_results(result)
                
            except Exception as e:
                st.error(f"模拟运行失败: {e}")
                # 使用同步版本作为备选
                reactions = asyncio.run(self.theater.simulate_event_reaction(event))
                script = self.theater.generate_theater_script(reactions)
                
                result = {
                    "reactions": [r.__dict__ for r in reactions],
                    "script": script,
                    "entertainment_value": 0.8,
                    "educational_insights": ["这是一个模拟示例"]
                }
                
                self._display_simulation_results(result)
    
    def _display_simulation_results(self, result: Dict[str, Any]):
        """显示模拟结果"""
        
        # 娱乐价值和教育价值
        col1, col2 = st.columns(2)
        
        with col1:
            entertainment_score = result.get('entertainment_value', 0.8)
            st.metric(
                "娱乐指数", 
                f"{entertainment_score:.1%}",
                help="基于散户反应的戏剧性和多样性计算"
            )
        
        with col2:
            education_count = len(result.get('educational_insights', []))
            st.metric(
                "教育价值", 
                f"{education_count}个洞察",
                help="从散户行为中提取的投资教育要点"
            )
        
        # 散户反应概览
        st.subheader("👥 散户反应概览")
        
        reactions_data = result.get('reactions', [])
        if reactions_data:
            df = pd.DataFrame(reactions_data)
            
            # 反应类型分布
            col1, col2 = st.columns(2)
            
            with col1:
                investor_type_counts = df['investor_type'].value_counts()
                fig = px.pie(
                    values=investor_type_counts.values,
                    names=investor_type_counts.index,
                    title="散户类型分布"
                )
                st.plotly_chart(fig, use_container_width=True)
            
            with col2:
                # 信心指数 vs 杠杆倍数散点图
                fig = px.scatter(
                    df,
                    x='confidence_level',
                    y='leverage_used',
                    color='investor_type',
                    size='leverage_used',
                    hover_name='investor_name',
                    title="信心指数 vs 杠杆使用",
                    labels={
                        'confidence_level': '信心指数',
                        'leverage_used': '杠杆倍数'
                    }
                )
                st.plotly_chart(fig, use_container_width=True)
        
        # 完整剧本
        st.subheader("📜 完整剧本")
        
        script = result.get('script', '')
        if script:
            st.markdown(script)
        
        # 教育洞察
        insights = result.get('educational_insights', [])
        if insights:
            st.subheader("🎯 投资教育洞察")
            for insight in insights:
                st.info(insight)
        
        # 保存到历史记录
        if 'theater_history' not in st.session_state:
            st.session_state['theater_history'] = []
        
        st.session_state['theater_history'].append({
            'timestamp': datetime.now(),
            'event_title': result.get('event', {}).get('title', '未知事件'),
            'result': result
        })
        
        # 重置模拟状态
        st.session_state['start_simulation'] = False
    
    def _render_classic_events(self):
        """渲染经典事件回顾"""
        st.subheader("📚 经典散户行为回顾")
        
        # 历史经典案例
        classic_cases = [
            {
                "title": "2020年原油宝事件",
                "description": "中行原油宝产品在WTI原油期货价格跌至负值时，导致投资者巨额亏损",
                "lesson": "不了解产品风险，盲目投资衍生品的后果",
                "typical_behavior": "看到原油便宜就买，不了解期货交割机制"
            },
            {
                "title": "2021年GameStop轧空事件",
                "description": "散户抱团做多GameStop，与华尔街空头机构对决",
                "lesson": "散户抱团的力量，但也要警惕情绪化交易",
                "typical_behavior": "FOMO情绪驱动，社交媒体影响投资决策"
            },
            {
                "title": "2022年俄乌冲突爆发",
                "description": "俄乌冲突爆发，全球股市暴跌，大宗商品价格飙升",
                "lesson": "地缘政治事件的不可预测性，分散投资的重要性",
                "typical_behavior": "恐慌性抛售，然后追涨大宗商品"
            }
        ]
        
        for case in classic_cases:
            with st.expander(f"📖 {case['title']}"):
                st.write(f"**事件描述**: {case['description']}")
                st.write(f"**典型行为**: {case['typical_behavior']}")
                st.info(f"**投资教训**: {case['lesson']}")
        
        # 历史模拟记录
        if 'theater_history' in st.session_state and st.session_state['theater_history']:
            st.subheader("🕐 您的模拟历史")
            
            for i, record in enumerate(reversed(st.session_state['theater_history'])):
                with st.expander(f"{record['timestamp'].strftime('%Y-%m-%d %H:%M')} - {record['event_title']}"):
                    entertainment_score = record['result'].get('entertainment_value', 0)
                    st.metric("娱乐指数", f"{entertainment_score:.1%}")
                    
                    if st.button(f"🔄 重新查看", key=f"replay_{i}"):
                        self._display_simulation_results(record['result'])
    
    def _render_behavior_analysis(self):
        """渲染行为分析"""
        st.subheader("📊 散户行为模式分析")
        
        # 行为模式统计
        st.markdown("### 🧠 心理偏误分析")
        
        biases = [
            {
                "name": "确认偏误",
                "description": "只关注支持自己观点的信息，忽视反对意见",
                "example": "买了某只股票后，只看利好消息，对利空消息视而不见",
                "frequency": 85
            },
            {
                "name": "损失厌恶",
                "description": "对损失的痛苦感受比同等收益的快乐感受更强烈",
                "example": "亏损时不愿止损，盈利时急于获利了结",
                "frequency": 78
            },
            {
                "name": "羊群效应",
                "description": "盲目跟随大众行为，缺乏独立思考",
                "example": "看到别人买什么就跟着买，追涨杀跌",
                "frequency": 72
            },
            {
                "name": "过度自信",
                "description": "高估自己的判断能力和预测准确性",
                "example": "认为自己能准确预测市场走势，频繁交易",
                "frequency": 68
            }
        ]
        
        for bias in biases:
            col1, col2, col3 = st.columns([2, 1, 2])
            
            with col1:
                st.write(f"**{bias['name']}**")
                st.write(bias['description'])
            
            with col2:
                st.metric("发生频率", f"{bias['frequency']}%")
            
            with col3:
                st.write(f"*典型例子*: {bias['example']}")
        
        # 投资者类型分布
        st.markdown("### 👥 投资者类型分布")
        
        investor_stats = {
            "新手小白": {"percentage": 35, "risk": "高", "特征": "容易被情绪驱动"},
            "追涨杀跌侠": {"percentage": 25, "risk": "极高", "特征": "FOMO情绪严重"},
            "杠杆赌徒": {"percentage": 15, "risk": "极高", "特征": "喜欢高风险高收益"},
            "技术派信徒": {"percentage": 12, "risk": "中", "特征": "过度依赖技术分析"},
            "消息面跟风者": {"percentage": 8, "risk": "高", "特征": "容易被假消息误导"},
            "其他类型": {"percentage": 5, "risk": "中", "特征": "各种其他行为模式"}
        }
        
        df_stats = pd.DataFrame([
            {"类型": k, "占比": v["percentage"], "风险等级": v["risk"], "主要特征": v["特征"]}
            for k, v in investor_stats.items()
        ])
        
        st.dataframe(df_stats, use_container_width=True)
    
    def _render_education_center(self):
        """渲染投教中心"""
        st.subheader("🎯 投资者教育中心")
        
        st.markdown("""
        ### 💡 太公心易投资智慧
        
        > **"在众人向左时，智者向右。在市场恐慌时，我们冷静。"**
        
        #### 🧘 逆向思维的艺术
        
        1. **当市场极度恐慌时** - 往往是最好的买入机会
        2. **当所有人都在谈论某只股票时** - 可能已经到了顶部
        3. **当专家们意见高度一致时** - 要保持警惕
        4. **当媒体开始唱多时** - 聪明资金可能已经在撤退
        
        #### 🛡️ 风险管理原则
        
        - **永远不要把所有鸡蛋放在一个篮子里**
        - **设定止损点，严格执行**
        - **控制仓位，避免过度杠杆**
        - **保持现金储备，应对突发情况**
        
        #### 🎭 从散户小剧场学到的教训
        
        """)
        
        lessons = [
            "情绪化交易是财富的杀手",
            "跟风投资往往买在高点",
            "过度使用杠杆容易爆仓",
            "缺乏独立思考导致亏损",
            "不了解产品风险很危险",
            "消息面滞后，理性分析更重要"
        ]
        
        for i, lesson in enumerate(lessons, 1):
            st.write(f"{i}. {lesson}")
        
        st.markdown("""
        #### 🎯 太公心易的建议
        
        - **学会独立思考**，不要盲目跟风
        - **控制情绪**，理性分析市场
        - **分散投资**，降低单一风险
        - **持续学习**，提升投资能力
        - **保持耐心**，等待好的机会
        
        ---
        
        *记住：投资有风险，入市需谨慎。散户小剧场只是娱乐和教育工具，不构成投资建议。*
        """)
        
        # 互动测试
        st.subheader("🧪 投资心理测试")
        
        if st.button("开始测试"):
            self._render_psychology_test()
    
    def _render_psychology_test(self):
        """渲染投资心理测试"""
        st.markdown("### 📝 您是哪种类型的投资者？")
        
        questions = [
            {
                "question": "看到股票大涨时，您的第一反应是？",
                "options": [
                    "立即买入，怕错过机会",
                    "观察一下再决定",
                    "觉得可能已经涨多了",
                    "完全不关心"
                ],
                "scores": [3, 1, -1, 0]
            },
            {
                "question": "您通常如何获取投资信息？",
                "options": [
                    "朋友推荐和网络传言",
                    "财经新闻和分析师报告",
                    "自己研究财务报表",
                    "技术分析图表"
                ],
                "scores": [3, 1, -1, 0]
            },
            {
                "question": "面对亏损时，您会？",
                "options": [
                    "立即止损，避免更大损失",
                    "继续持有，等待反弹",
                    "加仓摊低成本",
                    "不知道该怎么办"
                ],
                "scores": [-1, 1, 3, 2]
            }
        ]
        
        total_score = 0
        
        for i, q in enumerate(questions):
            st.write(f"**{i+1}. {q['question']}**")
            choice = st.radio(
                "",
                q['options'],
                key=f"q_{i}"
            )
            
            if choice:
                score = q['scores'][q['options'].index(choice)]
                total_score += score
        
        if st.button("查看结果"):
            if total_score >= 6:
                result = "🔥 冲动型投资者"
                description = "您容易被情绪驱动，建议加强风险控制和理性分析"
            elif total_score >= 2:
                result = "😊 普通散户"
                description = "您有一定的投资意识，但还需要提升专业知识"
            elif total_score >= -2:
                result = "🤔 谨慎型投资者"
                description = "您比较理性，但可能过于保守，错失一些机会"
            else:
                result = "🧠 理性投资者"
                description = "您具备良好的投资素养，继续保持独立思考"
            
            st.success(f"您的类型：{result}")
            st.info(description)
