# -*- coding: utf-8 -*-
"""
🚀 炼妖壶DApp GameFi界面
81难第一章节 - 男人的勋章是伤疤

核心理念：
- 真实时间线上的共享体验
- 每个伤疤都是成长的印记
- 醉八仙系统映射投资者偏见
- 同一fork上的持续互动

作者：太公心易BI系统
版本：v3.0 DApp Edition
"""

import streamlit as st
import plotly.graph_objects as go
import plotly.express as px
import pandas as pd
from datetime import datetime, timedelta
import json
from src.core.dapp_gamefi_system import DAppGameFiSystem, ScarType, EightDrunkImmortals
import random

def render_dapp_gamefi():
    """渲染DApp GameFi界面"""
    
    st.markdown("# 🚀 炼妖壶DApp - 81难第一章节")
    st.markdown("*金融系统本身就是game，GameFi就是同义反复*")
    st.markdown("**男人的勋章是伤疤 - 真实时间线上的成长印记**")
    
    # 初始化DApp系统
    if 'dapp_gamefi' not in st.session_state:
        st.session_state.dapp_gamefi = DAppGameFiSystem("demo_user_" + str(random.randint(1000, 9999)))
    
    gamefi = st.session_state.dapp_gamefi
    status = gamefi.get_gamefi_status()
    
    # 顶部状态栏
    col1, col2, col3, col4, col5 = st.columns(5)
    
    with col1:
        st.metric("智慧点数", status['wisdom_points'], delta="+10" if status['total_scars'] > 0 else None)
    with col2:
        st.metric("伤疤总数", status['total_scars'])
    with col3:
        st.metric("痛苦承受", f"{status['pain_endured']}/100")
    with col4:
        st.metric("在线时长", f"{status['online_time']//60:.0f}分钟")
    with col5:
        online_status = "🟢 在线" if status['is_online'] else "🔴 离线"
        st.metric("DApp状态", online_status)
    
    # 主要内容区域
    tab1, tab2, tab3, tab4 = st.tabs(["💀 伤疤收集", "🍷 醉八仙", "📡 时间线", "🎯 81难进度"])
    
    with tab1:
        render_scar_collection(gamefi, status)
    
    with tab2:
        render_drunk_immortals(gamefi, status)
    
    with tab3:
        render_timeline(gamefi, status)
    
    with tab4:
        render_81_trials_progress(gamefi, status)

def render_scar_collection(gamefi, status):
    """渲染伤疤收集界面"""
    st.markdown("## 💀 伤疤收集 - 男人的勋章")
    st.markdown("*每一道伤疤都是成长的印记，痛苦越深，智慧越多*")
    
    # 伤疤类型说明
    with st.expander("🩸 伤疤类型说明"):
        scar_descriptions = {
            "初见血光": "第一次亏损 - 市场给新手的见面礼",
            "刻骨之痛": "重大亏损>20% - 刻骨铭心的教训",
            "倾家荡产": "爆仓 - 最痛苦但也最有价值的经历",
            "追高被套": "FOMO陷阱 - 贪婪的代价",
            "恐慌割肉": "恐慌性抛售 - 情绪失控的后果",
            "杠杆之殇": "杠杆爆仓 - 双刃剑的教训",
            "错失良机": "踏空 - 机会成本的痛苦",
            "贪婪之罚": "贪婪导致的损失 - 人性的弱点"
        }
        
        for scar_type, description in scar_descriptions.items():
            st.markdown(f"**{scar_type}**: {description}")
    
    # 当前伤疤收集状态
    st.markdown("### 🏆 当前收集状态")
    
    scar_data = []
    for scar_type_str, count in status['scar_collection'].items():
        scar_data.append({
            'type': scar_type_str,
            'count': count,
            'collected': count > 0
        })
    
    df_scars = pd.DataFrame(scar_data)
    
    if not df_scars.empty:
        # 创建伤疤收集可视化
        fig = go.Figure()
        
        colors = ['#FF6B6B' if row['collected'] else '#E0E0E0' for _, row in df_scars.iterrows()]
        
        fig.add_trace(go.Bar(
            x=df_scars['type'],
            y=df_scars['count'],
            marker_color=colors,
            text=df_scars['count'],
            textposition='auto',
            hovertemplate='<b>%{x}</b><br>收集数量: %{y}<extra></extra>'
        ))
        
        fig.update_layout(
            title="💀 伤疤收集进度",
            xaxis_title="伤疤类型",
            yaxis_title="收集数量",
            height=400
        )
        
        st.plotly_chart(fig, use_container_width=True)
    
    # 模拟创建伤疤
    st.markdown("### 🎮 模拟交易事件")
    
    col1, col2 = st.columns(2)
    
    with col1:
        selected_scar = st.selectbox(
            "选择伤疤类型",
            options=list(ScarType),
            format_func=lambda x: f"{x.value} - {x.name}"
        )
        
        loss_percentage = st.slider("亏损比例", 0.01, 0.8, 0.1, 0.01)
        
    with col2:
        emotional_states = ["震惊", "恐慌", "绝望", "愤怒", "无奈", "后悔"]
        emotional_state = st.selectbox("情绪状态", emotional_states)
        
        market_context = st.text_input("市场背景", "市场突然下跌")
    
    if st.button("💥 创建伤疤", type="primary"):
        loss_amount = loss_percentage * 10000  # 假设本金10000
        
        scar = gamefi.create_trading_scar(
            scar_type=selected_scar,
            loss_amount=loss_amount,
            loss_percentage=loss_percentage,
            market_context=market_context,
            emotional_state=emotional_state
        )
        
        # 分享到时间线
        shared_exp = gamefi.share_experience_to_timeline(scar)
        
        st.success(f"🩸 获得伤疤: {scar.scar_type.value}")
        st.info(f"💡 教训: {scar.lesson_learned}")
        st.info(f"🧠 智慧获得: +{scar.wisdom_gained}")
        st.info(f"📡 已分享到时间线，区块哈希: {shared_exp['block_hash']}")
        
        st.rerun()
    
    # 显示最稀有的伤疤
    if status['rarest_scar']:
        st.markdown("### 🏅 最稀有伤疤")
        st.success(f"🩸 {status['rarest_scar']} - 你最深刻的成长印记")

def render_drunk_immortals(gamefi, status):
    """渲染醉八仙界面"""
    st.markdown("## 🍷 醉八仙 - 八种投资者醉态")
    st.markdown("*每个投资者都有自己的'醉态'，认识自己的偏见是清醒的开始*")
    
    # 当前醉态
    if status['current_drunk_state']:
        current_immortal = status['drunk_immortal_info']
        st.markdown(f"### 🎭 当前醉态: {current_immortal['name']}")
        st.markdown(f"**醉态风格**: {current_immortal['drunk_style']}")
        st.markdown(f"**投资偏见**: {current_immortal['investment_bias']}")
        
        with st.expander("查看详细信息"):
            st.markdown("**典型错误**:")
            for mistake in current_immortal['typical_mistakes']:
                st.markdown(f"- {mistake}")
            
            st.markdown("**清醒时刻**:")
            for moment in current_immortal['sobering_moments']:
                st.markdown(f"- {moment}")
            
            st.markdown("**智慧语录**:")
            for quote in current_immortal['wisdom_quotes']:
                st.markdown(f"> {quote}")
    else:
        st.info("🤔 还未识别出你的醉态，需要更多交易数据")
    
    # 八仙介绍
    st.markdown("### 🧙‍♂️ 八仙全览")
    
    immortals = EightDrunkImmortals.IMMORTALS
    
    for i, (name, immortal) in enumerate(immortals.items()):
        if i % 2 == 0:
            col1, col2 = st.columns(2)
            current_col = col1
        else:
            current_col = col2
        
        with current_col:
            with st.container():
                st.markdown(f"#### {immortal.name}")
                st.markdown(f"**{immortal.drunk_style}**")
                st.markdown(f"偏见: {immortal.investment_bias}")
                
                if status['current_drunk_state'] == name:
                    st.success("🎯 这就是你！")

def render_timeline(gamefi, status):
    """渲染时间线界面"""
    st.markdown("## 📡 共享时间线 - 同一Fork上的真实体验")
    st.markdown("*在同一时间线上，见证彼此的痛苦与成长*")
    
    # 模拟其他用户的经历
    if 'other_experiences' not in st.session_state:
        st.session_state.other_experiences = generate_mock_experiences()
    
    # 显示共享经历
    all_experiences = st.session_state.other_experiences + gamefi.shared_experiences
    all_experiences.sort(key=lambda x: x['timestamp'], reverse=True)
    
    st.markdown(f"### 📊 时间线统计")
    col1, col2, col3 = st.columns(3)
    
    with col1:
        st.metric("总经历数", len(all_experiences))
    with col2:
        total_pain = sum(exp.get('pain_level', 0) for exp in all_experiences)
        st.metric("总痛苦值", total_pain)
    with col3:
        total_wisdom = sum(exp.get('wisdom_gained', 0) for exp in all_experiences)
        st.metric("总智慧值", total_wisdom)
    
    # 时间线展示
    st.markdown("### 📜 最近经历")
    
    for exp in all_experiences[:10]:  # 显示最近10条
        is_own = exp.get('user_id') == gamefi.user_id
        
        if is_own:
            st.markdown(f"""
            <div style="border-left: 4px solid #FF6B6B; padding: 10px; margin: 10px 0; background-color: #FFF5F5;">
                <strong>🩸 你的经历</strong><br>
                <strong>{exp['scar_type']}</strong> - 痛苦程度: {exp['pain_level']}/10<br>
                💡 {exp['lesson_learned']}<br>
                <small>🔗 {exp['block_hash']} | {exp['timestamp'][:19]}</small>
            </div>
            """, unsafe_allow_html=True)
        else:
            user_short = exp['user_id'][:8] + "..."
            st.markdown(f"""
            <div style="border-left: 4px solid #E0E0E0; padding: 10px; margin: 10px 0; background-color: #F9F9F9;">
                <strong>👥 {user_short}</strong><br>
                <strong>{exp['scar_type']}</strong> - 痛苦程度: {exp['pain_level']}/10<br>
                💡 {exp['lesson_learned']}<br>
                <small>🔗 {exp['block_hash']} | {exp['timestamp'][:19]}</small>
            </div>
            """, unsafe_allow_html=True)
    
    # 见证他人痛苦
    if st.button("👁️ 见证他人痛苦，获得智慧"):
        witnessed_wisdom = gamefi.witness_others_pain(st.session_state.other_experiences)
        
        if witnessed_wisdom:
            st.success(f"🧠 从他人痛苦中学到了 {len(witnessed_wisdom)} 条智慧")
            for wisdom in witnessed_wisdom[:3]:  # 显示前3条
                st.info(f"💡 {wisdom}")
        else:
            st.info("暂时没有新的他人经历可以学习")
        
        st.rerun()

def render_81_trials_progress(gamefi, status):
    """渲染81难进度"""
    st.markdown("## 🎯 西游81难 - DApp修仙进度")
    st.markdown("*每一难都是真实的市场考验，不可重置的人生体验*")
    
    # 当前进度
    current_trial = min(status['total_scars'] * 3 + status['wisdom_points'] // 50, 81)
    progress_percent = (current_trial / 81) * 100
    
    st.markdown(f"### 📊 当前进度: {current_trial}/81 难")
    st.progress(progress_percent / 100)
    st.markdown(f"完成度: {progress_percent:.1f}%")
    
    # 81难分类
    trials_categories = {
        "第一阶段 (1-27难)": "初入江湖，见识市场险恶",
        "第二阶段 (28-54难)": "历经磨难，逐渐成熟",
        "第三阶段 (55-81难)": "大彻大悟，成为真正的投资者"
    }
    
    for category, description in trials_categories.items():
        st.markdown(f"**{category}**: {description}")
    
    # 里程碑奖励
    milestones = [
        (9, "🐒 石猴觉醒", "开始意识到投资的复杂性"),
        (18, "🌊 水帘洞主", "建立了基本的风险意识"),
        (27, "💀 见生死", "经历了重大亏损，开始深度思考"),
        (36, "🚢 舢板求道", "主动学习投资知识"),
        (45, "🌙 斜月三星洞", "找到了适合自己的投资方法"),
        (54, "✨ 得偿所望", "开始稳定盈利"),
        (63, "👑 傲气回山", "过度自信，需要警惕"),
        (72, "🔥 大闹天宫", "与市场对抗，遭受重创"),
        (81, "🏔️ 五行山下", "彻底觉悟，成为真正的投资大师")
    ]
    
    st.markdown("### 🏆 里程碑进度")
    
    for milestone_trial, title, description in milestones:
        if current_trial >= milestone_trial:
            st.success(f"✅ {title} ({milestone_trial}难) - {description}")
        else:
            st.info(f"🔒 {title} ({milestone_trial}难) - {description}")
    
    # 下一个目标
    next_milestone = next((m for m in milestones if current_trial < m[0]), None)
    if next_milestone:
        trials_needed = next_milestone[0] - current_trial
        st.markdown(f"### 🎯 下一个目标: {next_milestone[1]}")
        st.markdown(f"还需要 {trials_needed} 难")

def generate_mock_experiences():
    """生成模拟的其他用户经历"""
    mock_experiences = []
    
    scar_types = list(ScarType)
    user_ids = [f"user_{i:04d}" for i in range(1001, 1020)]
    
    for i in range(15):
        scar_type = random.choice(scar_types)
        user_id = random.choice(user_ids)
        pain_level = random.randint(1, 10)
        wisdom_gained = pain_level * 10 + random.randint(0, 50)
        
        timestamp = datetime.now() - timedelta(minutes=random.randint(1, 1440))
        
        mock_experiences.append({
            "user_id": user_id,
            "timestamp": timestamp.isoformat(),
            "scar_type": scar_type.value,
            "pain_level": pain_level,
            "wisdom_gained": wisdom_gained,
            "lesson_learned": f"从{scar_type.value}中学到的宝贵教训",
            "block_hash": f"{random.randint(10000000, 99999999):08x}",
            "market_context": "市场波动中的真实体验"
        })
    
    return mock_experiences

if __name__ == "__main__":
    st.set_page_config(
        page_title="炼妖壶DApp GameFi",
        page_icon="🚀",
        layout="wide"
    )
    render_dapp_gamefi()