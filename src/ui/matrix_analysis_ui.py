#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
RSS矩阵分析UI界面
可视化展示事件矩阵、原型预测和假设检验结果

作者：太公心易BI系统
版本：v1.0 Matrix UI
"""

import streamlit as st
import numpy as np
import pandas as pd
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
import asyncio
import json
from datetime import datetime, timedelta
import sys
from pathlib import Path

# 添加项目路径
sys.path.append(str(Path(__file__).parent.parent.parent))
from src.core.rss_matrix_system import RSSMatrixSystem


class MatrixAnalysisUI:
    """矩阵分析UI类"""
    
    def __init__(self):
        self.matrix_system = RSSMatrixSystem()
        
    def render_main_page(self):
        """渲染主页面"""
        st.set_page_config(
            page_title="太公心易 - RSS事件矩阵分析",
            page_icon="🎯",
            layout="wide"
        )
        
        st.title("🎯 太公心易RSS事件矩阵分析系统")
        st.markdown("---")
        
        # 侧边栏控制
        with st.sidebar:
            st.header("⚙️ 分析参数")
            
            # 时间周期选择
            period = st.selectbox(
                "选择分析周期",
                options=['1day', '1week', '2weeks', '1month'],
                index=1,
                format_func=lambda x: {
                    '1day': '1天',
                    '1week': '1周', 
                    '2weeks': '2周',
                    '1month': '1月'
                }[x]
            )
            
            # 原型选择
            selected_archetypes = st.multiselect(
                "选择股票原型",
                options=['七仙女', '大白马', '妖股', '周期股'],
                default=['七仙女', '大白马', '妖股']
            )
            
            # 分析按钮
            if st.button("🚀 开始矩阵分析", type="primary"):
                st.session_state.run_analysis = True
                st.session_state.analysis_period = period
                st.session_state.selected_archetypes = selected_archetypes
        
        # 主要内容区域
        if hasattr(st.session_state, 'run_analysis') and st.session_state.run_analysis:
            self.render_analysis_results()
        else:
            self.render_welcome_page()
    
    def render_welcome_page(self):
        """渲染欢迎页面"""
        col1, col2, col3 = st.columns([1, 2, 1])
        
        with col2:
            st.markdown("""
            ## 🌟 系统核心理念
            
            ### 📊 矩阵乘法模型
            ```
            股价走势 = 标的本命矩阵 × RSS事件矩阵
            ```
            
            ### 🎭 股票原型系统
            - **七仙女**: 科技成长股，对创新敏感
            - **大白马**: 价值蓝筹，稳健可靠  
            - **妖股**: 高波动投机，情绪驱动
            - **周期股**: 跟随经济周期波动
            
            ### 🧪 假设检验框架
            - **应然价格**: 矩阵模型预测的理论价格
            - **实然价格**: 市场实际交易价格
            - **统计检验**: 验证模型的预测能力
            
            ### 📈 多维事件矩阵
            - 货币政策、地缘政治、市场情绪
            - 科技创新、金融危机、监管变化
            - 公司特定、板块轮动、流动性
            """)
    
    def render_analysis_results(self):
        """渲染分析结果"""
        period = st.session_state.analysis_period
        archetypes = st.session_state.selected_archetypes
        
        with st.spinner(f"🔄 正在分析{period}周期的事件矩阵..."):
            # 异步运行分析
            report = asyncio.run(self.matrix_system.generate_matrix_report(period))
        
        # 显示结果
        self.display_matrix_heatmap(report)
        self.display_archetype_predictions(report, archetypes)
        self.display_dimension_analysis(report)
        self.display_matrix_properties(report)
        
        # 假设检验结果
        if st.checkbox("🧪 运行假设检验"):
            self.display_hypothesis_tests(period)
    
    def display_matrix_heatmap(self, report):
        """显示事件矩阵热力图"""
        st.subheader("🔥 事件矩阵热力图")
        
        matrix = np.array(report['event_matrix'])
        dimensions = [dim.description for dim in self.matrix_system.event_dimensions]
        
        fig = go.Figure(data=go.Heatmap(
            z=matrix,
            x=dimensions,
            y=dimensions,
            colorscale='RdYlBu_r',
            text=np.round(matrix, 3),
            texttemplate="%{text}",
            textfont={"size": 10},
            hoverongaps=False
        ))
        
        fig.update_layout(
            title="事件维度关联矩阵",
            xaxis_title="事件维度",
            yaxis_title="事件维度",
            height=600
        )
        
        st.plotly_chart(fig, use_container_width=True)
        
        # 矩阵解读
        with st.expander("📖 矩阵解读"):
            st.markdown("""
            - **对角线**: 各维度的自相关强度
            - **非对角线**: 维度间的交叉影响
            - **颜色深度**: 关联强度（红色=强正相关，蓝色=强负相关）
            - **数值大小**: 具体的关联系数
            """)
    
    def display_archetype_predictions(self, report, selected_archetypes):
        """显示原型预测结果"""
        st.subheader("🎭 股票原型预测")
        
        predictions = report['archetype_predictions']
        
        # 创建预测结果表格
        pred_data = []
        for archetype in selected_archetypes:
            if archetype in predictions and 'error' not in predictions[archetype]:
                pred = predictions[archetype]
                pred_data.append({
                    '原型': archetype,
                    '预测变动': f"{pred['predicted_change']:.2%}",
                    '方向': "📈 上涨" if pred['direction'] == 'up' else "📉 下跌",
                    '置信度': f"{pred['confidence']:.2f}",
                    '变动幅度': f"{pred['magnitude']:.2%}"
                })
        
        if pred_data:
            df = pd.DataFrame(pred_data)
            st.dataframe(df, use_container_width=True)
            
            # 可视化预测结果
            fig = go.Figure()
            
            for i, row in enumerate(pred_data):
                archetype = row['原型']
                change = float(row['预测变动'].strip('%')) / 100
                confidence = float(row['置信度'])
                
                color = 'green' if change > 0 else 'red'
                fig.add_trace(go.Bar(
                    x=[archetype],
                    y=[change],
                    name=archetype,
                    marker_color=color,
                    opacity=confidence,
                    text=f"{change:.2%}",
                    textposition='auto'
                ))
            
            fig.update_layout(
                title="原型预测变动幅度",
                yaxis_title="预测变动 (%)",
                showlegend=False,
                height=400
            )
            
            st.plotly_chart(fig, use_container_width=True)
        else:
            st.warning("⚠️ 选择的原型暂无有效预测数据")
    
    def display_dimension_analysis(self, report):
        """显示维度重要性分析"""
        st.subheader("📊 事件维度重要性")
        
        importance_data = report['dimension_importance']
        
        # 准备数据
        dims = []
        scores = []
        descriptions = []
        
        for dim_name, dim_info in importance_data.items():
            dims.append(dim_info['description'])
            scores.append(dim_info['importance_score'])
            descriptions.append(dim_name)
        
        # 创建重要性排序
        sorted_data = sorted(zip(dims, scores, descriptions), key=lambda x: x[1], reverse=True)
        
        # 可视化
        fig = go.Figure(go.Bar(
            x=[item[1] for item in sorted_data],
            y=[item[0] for item in sorted_data],
            orientation='h',
            marker_color=px.colors.qualitative.Set3,
            text=[f"{item[1]:.3f}" for item in sorted_data],
            textposition='auto'
        ))
        
        fig.update_layout(
            title="事件维度重要性排序",
            xaxis_title="重要性分数",
            yaxis_title="事件维度",
            height=500
        )
        
        st.plotly_chart(fig, use_container_width=True)
        
        # 详细数据表
        with st.expander("📋 详细重要性数据"):
            importance_df = pd.DataFrame([
                {
                    '维度': item[0],
                    '重要性分数': f"{item[1]:.3f}",
                    '内部名称': item[2]
                }
                for item in sorted_data
            ])
            st.dataframe(importance_df, use_container_width=True)
    
    def display_matrix_properties(self, report):
        """显示矩阵数学属性"""
        st.subheader("🔢 矩阵数学属性")
        
        analysis = report['matrix_analysis']
        
        col1, col2, col3 = st.columns(3)
        
        with col1:
            st.metric("矩阵迹", f"{analysis['trace']:.3f}")
            st.metric("行列式", f"{analysis['determinant']:.3f}")
        
        with col2:
            st.metric("Frobenius范数", f"{analysis['frobenius_norm']:.3f}")
            st.metric("条件数", f"{analysis['condition_number']:.3f}")
        
        with col3:
            eigenvalues = analysis['eigenvalues']
            st.metric("最大特征值", f"{max(eigenvalues):.3f}")
            st.metric("最小特征值", f"{min(eigenvalues):.3f}")
        
        # 特征值分布图
        fig = go.Figure(go.Scatter(
            x=list(range(len(eigenvalues))),
            y=eigenvalues,
            mode='markers+lines',
            marker=dict(size=10, color='blue'),
            name='特征值'
        ))
        
        fig.update_layout(
            title="矩阵特征值分布",
            xaxis_title="特征值索引",
            yaxis_title="特征值大小",
            height=400
        )
        
        st.plotly_chart(fig, use_container_width=True)
        
        # 数学解释
        with st.expander("🧮 数学属性解释"):
            st.markdown("""
            - **矩阵迹**: 对角线元素之和，反映系统总体活跃度
            - **行列式**: 矩阵的"体积"，反映系统的稳定性
            - **Frobenius范数**: 矩阵的"大小"，反映整体影响强度
            - **条件数**: 矩阵的数值稳定性，越小越稳定
            - **特征值**: 矩阵的主要方向和强度
            """)
    
    def display_hypothesis_tests(self, period):
        """显示假设检验结果"""
        st.subheader("🧪 假设检验结果")
        
        with st.spinner("🔄 运行假设检验..."):
            try:
                test_results = asyncio.run(
                    self.matrix_system.run_hypothesis_test(period)
                )
                
                if 'error' in test_results:
                    st.warning(f"⚠️ {test_results['error']}")
                    return
                
                # 显示检验结果
                col1, col2 = st.columns(2)
                
                with col1:
                    st.markdown("### 📊 统计检验")
                    
                    # t检验结果
                    t_test = test_results['t_test']
                    result_color = "🟢" if t_test['result'] == 'accept' else "🔴"
                    st.markdown(f"{result_color} **t检验**: {t_test['interpretation']}")
                    st.markdown(f"   p值: {t_test['p_value']:.4f}")
                    
                    # 正态性检验
                    norm_test = test_results['normality_test']
                    result_color = "🟢" if norm_test['result'] == 'accept' else "🔴"
                    st.markdown(f"{result_color} **正态性检验**: {norm_test['interpretation']}")
                    st.markdown(f"   p值: {norm_test['p_value']:.4f}")
                    
                    # 相关性检验
                    corr_test = test_results['correlation_test']
                    result_color = "🟢" if corr_test['result'] == 'significant' else "🔴"
                    st.markdown(f"{result_color} **相关性检验**: {corr_test['interpretation']}")
                    st.markdown(f"   p值: {corr_test['p_value']:.4f}")
                
                with col2:
                    st.markdown("### 📈 准确性指标")
                    
                    metrics = test_results['accuracy_metrics']
                    st.metric("均方误差 (MSE)", f"{metrics['mse']:.4f}")
                    st.metric("平均绝对误差 (MAE)", f"{metrics['mae']:.4f}")
                    st.metric("平均绝对百分比误差 (MAPE)", f"{metrics['mape']:.2f}%")
                    st.metric("均方根误差 (RMSE)", f"{metrics['rmse']:.4f}")
                
                # 检验结果解释
                with st.expander("📖 检验结果解释"):
                    st.markdown("""
                    ### 假设检验说明
                    
                    - **t检验**: 检验预测是否存在系统性偏差
                      - H0: 预测误差均值为0（无偏预测）
                      - p > 0.05: 接受H0，预测无偏
                    
                    - **正态性检验**: 检验预测误差是否服从正态分布
                      - 正态分布是许多统计方法的前提
                    
                    - **相关性检验**: 检验预测值与实际值的线性关系
                      - 相关系数越接近1，预测效果越好
                    
                    ### 准确性指标
                    - **MAPE < 10%**: 优秀的预测模型
                    - **MAPE 10-20%**: 良好的预测模型  
                    - **MAPE > 20%**: 需要改进的模型
                    """)
                
            except Exception as e:
                st.error(f"❌ 假设检验失败: {e}")


def main():
    """主函数"""
    ui = MatrixAnalysisUI()
    ui.render_main_page()


if __name__ == "__main__":
    main()