# -*- coding: utf-8 -*-
"""
十二龙子基础类定义
基于docs/12dragon.md的诗歌体系设计

作者：太公心易BI系统
版本：v1.0 - 基础类库
"""

from abc import ABC, abstractmethod
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from datetime import datetime
import logging

logger = logging.getLogger(__name__)

@dataclass
class DragonResult:
    """龙子执行结果"""
    dragon_name: str
    role: str
    specialty: str
    confidence: float
    data: Dict[str, Any]
    timestamp: datetime
    success: bool
    error_message: Optional[str] = None

class DragonBase(ABC):
    """十二龙子基础类
    
    基于太公心易雕龙歌的设计理念：
    "十二龙子各其位，心易大道合天随"
    """
    
    def __init__(self, name: str, role: str, specialty: str):
        self.name = name
        self.role = role  
        self.specialty = specialty
        self.execution_history = []
        
    @abstractmethod
    def execute(self, task_data: Dict[str, Any]) -> DragonResult:
        """执行龙子特定任务"""
        pass
    
    def get_status(self) -> Dict[str, Any]:
        """获取龙子状态"""
        return {
            "name": self.name,
            "role": self.role,
            "specialty": self.specialty,
            "execution_count": len(self.execution_history),
            "last_execution": self.execution_history[-1].timestamp if self.execution_history else None
        }

# 第一组：信息收集龙子 🔍

class 囚牛(DragonBase):
    """囚牛 - 礼乐戎祀 / 基础搜索
    
    "囚牛低首似伏牛，乐律声声自心流。
     万物谐和礼乐起，干戈戎祀不相求。"
    """
    
    def __init__(self):
        super().__init__("囚牛", "基础搜索", "礼乐戎祀")
        
    def execute(self, task_data: Dict[str, Any]) -> DragonResult:
        """执行基础搜索任务"""
        try:
            claim = task_data.get("claim", "")
            keywords = task_data.get("keywords", [])
            
            # 基础搜索逻辑
            search_queries = self._generate_basic_queries(claim, keywords)
            search_results = self._perform_basic_search(search_queries)
            
            confidence = self._calculate_basic_confidence(search_results)
            
            result = DragonResult(
                dragon_name=self.name,
                role=self.role,
                specialty=self.specialty,
                confidence=confidence,
                data={
                    "search_queries": search_queries,
                    "search_results": search_results,
                    "result_count": len(search_results)
                },
                timestamp=datetime.now(),
                success=True
            )
            
            self.execution_history.append(result)
            logger.info(f"🐂 {self.name}执行完成，置信度: {confidence:.2f}")
            return result
            
        except Exception as e:
            error_result = DragonResult(
                dragon_name=self.name,
                role=self.role,
                specialty=self.specialty,
                confidence=0.0,
                data={},
                timestamp=datetime.now(),
                success=False,
                error_message=str(e)
            )
            logger.error(f"❌ {self.name}执行失败: {e}")
            return error_result
    
    def _generate_basic_queries(self, claim: str, keywords: List[str]) -> List[str]:
        """生成基础搜索查询"""
        queries = []
        if keywords:
            queries.append(f"{' '.join(keywords[:3])} 最新消息")
            queries.append(f"{' '.join(keywords[:2])} 新闻")
            queries.append(f"{keywords[0]} 分析")
        else:
            queries.append(f"{claim} 最新消息")
        return queries
    
    def _perform_basic_search(self, queries: List[str]) -> List[Dict[str, Any]]:
        """执行基础搜索（模拟实现）"""
        results = []
        for query in queries:
            # 这里应该调用实际的搜索API
            results.append({
                "query": query,
                "title": f"关于{query}的搜索结果",
                "url": f"https://example.com/search?q={query}",
                "snippet": f"这是关于{query}的相关信息摘要"
            })
        return results
    
    def _calculate_basic_confidence(self, results: List[Dict[str, Any]]) -> float:
        """计算基础置信度"""
        if not results:
            return 0.0
        
        # 基于结果数量的简单置信度计算
        base_confidence = min(len(results) * 0.2, 0.8)
        return base_confidence

class 睚眦(DragonBase):
    """睚眦 - 虽远必诛 / 深度挖掘
    
    "睚眦怒目豺狼形，惩恶扬善气如风。
     不平之事何处有？虽远必诛不留情。"
    """
    
    def __init__(self):
        super().__init__("睚眦", "深度挖掘", "虽远必诛")
        
    def execute(self, task_data: Dict[str, Any]) -> DragonResult:
        """执行深度挖掘任务"""
        try:
            claim = task_data.get("claim", "")
            basic_results = task_data.get("basic_results", [])
            
            # 深度挖掘逻辑
            deep_queries = self._generate_deep_queries(claim)
            deep_results = self._perform_deep_search(deep_queries)
            hidden_info = self._extract_hidden_info(deep_results)
            
            confidence = self._calculate_deep_confidence(deep_results, hidden_info)
            
            result = DragonResult(
                dragon_name=self.name,
                role=self.role,
                specialty=self.specialty,
                confidence=confidence,
                data={
                    "deep_queries": deep_queries,
                    "deep_results": deep_results,
                    "hidden_info": hidden_info,
                    "depth_score": len(hidden_info)
                },
                timestamp=datetime.now(),
                success=True
            )
            
            self.execution_history.append(result)
            logger.info(f"🐺 {self.name}执行完成，挖掘深度: {len(hidden_info)}")
            return result
            
        except Exception as e:
            error_result = DragonResult(
                dragon_name=self.name,
                role=self.role,
                specialty=self.specialty,
                confidence=0.0,
                data={},
                timestamp=datetime.now(),
                success=False,
                error_message=str(e)
            )
            logger.error(f"❌ {self.name}执行失败: {e}")
            return error_result
    
    def _generate_deep_queries(self, claim: str) -> List[str]:
        """生成深度挖掘查询"""
        return [
            f"{claim} 深度分析",
            f"{claim} 内幕消息",
            f"{claim} 详细报告",
            f"{claim} 专业评估"
        ]
    
    def _perform_deep_search(self, queries: List[str]) -> List[Dict[str, Any]]:
        """执行深度搜索"""
        results = []
        for query in queries:
            results.append({
                "query": query,
                "source": "专业财经网站",
                "content": f"关于{query}的深度分析内容",
                "credibility": 0.8
            })
        return results
    
    def _extract_hidden_info(self, results: List[Dict[str, Any]]) -> List[str]:
        """提取隐藏信息"""
        hidden_info = []
        for result in results:
            if result.get("credibility", 0) > 0.7:
                hidden_info.append(f"发现高可信度信息: {result.get('content', '')[:50]}...")
        return hidden_info
    
    def _calculate_deep_confidence(self, results: List[Dict[str, Any]], hidden_info: List[str]) -> float:
        """计算深度挖掘置信度"""
        base_confidence = 0.5
        
        # 基于深度结果调整
        if results:
            base_confidence += len(results) * 0.1
        
        # 基于隐藏信息调整
        if hidden_info:
            base_confidence += len(hidden_info) * 0.15
        
        return min(base_confidence, 1.0)

class 狻猊(DragonBase):
    """狻猊 - 讲经说法 / 权威验证
    
    "狻猊稳坐似雄狮，佛光普照智慧施。
     讲经说法度众生，醍醐灌顶破愚痴。"
    """
    
    def __init__(self):
        super().__init__("狻猊", "权威验证", "讲经说法")
        
    def execute(self, task_data: Dict[str, Any]) -> DragonResult:
        """执行权威验证任务"""
        try:
            claim = task_data.get("claim", "")
            
            # 权威验证逻辑
            authority_sources = self._identify_authority_sources(claim)
            verification_results = self._verify_with_authorities(claim, authority_sources)
            credibility_score = self._calculate_credibility(verification_results)
            
            confidence = self._calculate_authority_confidence(verification_results, credibility_score)
            
            result = DragonResult(
                dragon_name=self.name,
                role=self.role,
                specialty=self.specialty,
                confidence=confidence,
                data={
                    "authority_sources": authority_sources,
                    "verification_results": verification_results,
                    "credibility_score": credibility_score,
                    "verified_claims": len([r for r in verification_results if r.get("verified", False)])
                },
                timestamp=datetime.now(),
                success=True
            )
            
            self.execution_history.append(result)
            logger.info(f"🦁 {self.name}执行完成，权威性: {credibility_score:.2f}")
            return result
            
        except Exception as e:
            error_result = DragonResult(
                dragon_name=self.name,
                role=self.role,
                specialty=self.specialty,
                confidence=0.0,
                data={},
                timestamp=datetime.now(),
                success=False,
                error_message=str(e)
            )
            logger.error(f"❌ {self.name}执行失败: {e}")
            return error_result
    
    def _identify_authority_sources(self, claim: str) -> List[str]:
        """识别权威信息源"""
        return [
            "官方财报",
            "监管公告", 
            "权威媒体",
            "专业机构报告"
        ]
    
    def _verify_with_authorities(self, claim: str, sources: List[str]) -> List[Dict[str, Any]]:
        """通过权威源验证"""
        results = []
        for source in sources:
            results.append({
                "source": source,
                "verified": True,  # 模拟验证结果
                "confidence": 0.8,
                "evidence": f"来自{source}的验证证据"
            })
        return results
    
    def _calculate_credibility(self, results: List[Dict[str, Any]]) -> float:
        """计算可信度"""
        if not results:
            return 0.0
        
        total_confidence = sum(r.get("confidence", 0) for r in results)
        return total_confidence / len(results)
    
    def _calculate_authority_confidence(self, results: List[Dict[str, Any]], credibility: float) -> float:
        """计算权威验证置信度"""
        verified_count = len([r for r in results if r.get("verified", False)])
        total_count = len(results)
        
        if total_count == 0:
            return 0.0
        
        verification_rate = verified_count / total_count
        return (verification_rate + credibility) / 2

# 龙子工厂类
class DragonFactory:
    """十二龙子工厂类"""
    
    _dragon_classes = {
        "囚牛": 囚牛,
        "睚眦": 睚眦, 
        "狻猊": 狻猊,
        # 其他龙子类将在后续添加
    }
    
    @classmethod
    def create_dragon(cls, dragon_name: str) -> DragonBase:
        """创建指定的龙子实例"""
        dragon_class = cls._dragon_classes.get(dragon_name)
        if dragon_class:
            return dragon_class()
        else:
            raise ValueError(f"未知的龙子类型: {dragon_name}")
    
    @classmethod
    def create_all_dragons(cls) -> Dict[str, DragonBase]:
        """创建所有龙子实例"""
        dragons = {}
        for name, dragon_class in cls._dragon_classes.items():
            dragons[name] = dragon_class()
        return dragons
    
    @classmethod
    def get_available_dragons(cls) -> List[str]:
        """获取可用的龙子列表"""
        return list(cls._dragon_classes.keys())
