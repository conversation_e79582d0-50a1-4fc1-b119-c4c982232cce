#!/usr/bin/env python3
"""
MCP服务管理器 - 统一管理stdio和SSE的MCP服务
解决依赖管理和服务发现的痛点
"""

import os
import json
import asyncio
import subprocess
import logging
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict
from pathlib import Path
import yaml
import httpx
from fastapi import FastAPI, HTTPException
from fastapi.responses import JSONResponse, HTMLResponse
from fastapi.staticfiles import StaticFiles
import uvicorn

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class MCPService:
    """MCP服务配置"""
    name: str
    type: str  # stdio, sse, http
    command: Optional[str] = None
    args: Optional[List[str]] = None
    env: Optional[Dict[str, str]] = None
    url: Optional[str] = None
    port: Optional[int] = None
    health_check: Optional[str] = None
    dependencies: Optional[List[str]] = None
    auto_restart: bool = True
    status: str = "stopped"
    process: Optional[subprocess.Popen] = None

class MCPManager:
    """MCP服务管理器"""
    
    def __init__(self, config_path: str = "mcp_services.yml"):
        self.config_path = config_path
        self.services: Dict[str, MCPService] = {}
        self.app = FastAPI(title="MCP Manager", version="1.0.0")
        self.setup_routes()
        self.load_config()
    
    def load_config(self):
        """加载MCP服务配置"""
        if not os.path.exists(self.config_path):
            self.create_default_config()
        
        with open(self.config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        
        for service_config in config.get('services', []):
            service = MCPService(**service_config)
            self.services[service.name] = service
        
        logger.info(f"加载了 {len(self.services)} 个MCP服务配置")
    
    def create_default_config(self):
        """创建默认配置文件"""
        default_config = {
            'services': [
                {
                    'name': 'yahoo-finance',
                    'type': 'stdio',
                    'command': 'uv',
                    'args': ['--directory', './scripts/mcp/yahoo-finance-mcp', 'run', 'yahoo-finance-mcp'],
                    'env': {'PYTHONPATH': './scripts/mcp/yahoo-finance-mcp/src'},
                    'dependencies': ['uv', 'python>=3.9'],
                    'health_check': None
                },
                {
                    'name': 'cauldron-financial',
                    'type': 'http',
                    'url': 'https://cauldron.herokuapp.com/api/mcp',
                    'health_check': 'https://cauldron.herokuapp.com/health',
                    'env': {'CAULDRON_API_KEY': '${CAULDRON_API_KEY}'}
                },
                {
                    'name': 'tusita-palace',
                    'type': 'stdio',
                    'command': 'python',
                    'args': ['-m', 'jixia_academy_clean.core.tusita_palace_mcp'],
                    'env': {
                        'N8N_WEBHOOK_URL': '${N8N_WEBHOOK_URL}',
                        'N8N_API_KEY': '${N8N_API_KEY}'
                    },
                    'dependencies': ['python>=3.9', 'httpx', 'asyncio']
                }
            ]
        }
        
        with open(self.config_path, 'w', encoding='utf-8') as f:
            yaml.dump(default_config, f, default_flow_style=False, allow_unicode=True)
        
        logger.info(f"创建默认配置文件: {self.config_path}")
    
    async def start_service(self, service_name: str) -> bool:
        """启动MCP服务"""
        if service_name not in self.services:
            logger.error(f"服务不存在: {service_name}")
            return False
        
        service = self.services[service_name]
        
        if service.status == "running":
            logger.info(f"服务已在运行: {service_name}")
            return True
        
        try:
            # 检查依赖
            if not await self.check_dependencies(service):
                logger.error(f"依赖检查失败: {service_name}")
                return False
            
            if service.type == "stdio":
                await self.start_stdio_service(service)
            elif service.type == "http":
                await self.start_http_service(service)
            elif service.type == "sse":
                await self.start_sse_service(service)
            
            service.status = "running"
            logger.info(f"服务启动成功: {service_name}")
            return True
            
        except Exception as e:
            logger.error(f"启动服务失败 {service_name}: {e}")
            service.status = "error"
            return False
    
    async def start_stdio_service(self, service: MCPService):
        """启动stdio类型的MCP服务"""
        env = os.environ.copy()
        if service.env:
            # 处理环境变量替换
            for key, value in service.env.items():
                if value.startswith('${') and value.endswith('}'):
                    env_var = value[2:-1]
                    env[key] = os.getenv(env_var, '')
                else:
                    env[key] = value
        
        # 启动子进程
        service.process = subprocess.Popen(
            [service.command] + (service.args or []),
            env=env,
            stdin=subprocess.PIPE,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        # 等待一下确保启动
        await asyncio.sleep(1)
        
        if service.process.poll() is not None:
            raise Exception(f"进程启动失败，退出码: {service.process.returncode}")
    
    async def start_http_service(self, service: MCPService):
        """启动HTTP类型的MCP服务"""
        if service.health_check:
            async with httpx.AsyncClient() as client:
                try:
                    response = await client.get(service.health_check, timeout=10)
                    if response.status_code != 200:
                        raise Exception(f"健康检查失败: {response.status_code}")
                except Exception as e:
                    raise Exception(f"HTTP服务不可用: {e}")
    
    async def start_sse_service(self, service: MCPService):
        """启动SSE类型的MCP服务"""
        # SSE服务通常是外部服务，只需要检查连接
        if service.url:
            async with httpx.AsyncClient() as client:
                try:
                    response = await client.get(service.url, timeout=10)
                    if response.status_code not in [200, 101]:  # 101 for SSE upgrade
                        raise Exception(f"SSE连接失败: {response.status_code}")
                except Exception as e:
                    raise Exception(f"SSE服务不可用: {e}")
    
    async def stop_service(self, service_name: str) -> bool:
        """停止MCP服务"""
        if service_name not in self.services:
            return False
        
        service = self.services[service_name]
        
        try:
            if service.process:
                service.process.terminate()
                await asyncio.sleep(2)
                if service.process.poll() is None:
                    service.process.kill()
                service.process = None
            
            service.status = "stopped"
            logger.info(f"服务停止成功: {service_name}")
            return True
            
        except Exception as e:
            logger.error(f"停止服务失败 {service_name}: {e}")
            return False
    
    async def check_dependencies(self, service: MCPService) -> bool:
        """检查服务依赖"""
        if not service.dependencies:
            return True
        
        for dep in service.dependencies:
            if not await self.check_dependency(dep):
                logger.error(f"依赖检查失败: {dep}")
                return False
        
        return True
    
    async def check_dependency(self, dependency: str) -> bool:
        """检查单个依赖"""
        try:
            if dependency.startswith('python>='):
                # 检查Python版本
                import sys
                required_version = dependency.split('>=')[1]
                current_version = f"{sys.version_info.major}.{sys.version_info.minor}"
                return current_version >= required_version
            
            elif dependency in ['uv', 'npm', 'node']:
                # 检查命令行工具
                result = subprocess.run(['which', dependency], 
                                      capture_output=True, text=True)
                return result.returncode == 0
            
            else:
                # 检查Python包
                try:
                    __import__(dependency)
                    return True
                except ImportError:
                    return False
                    
        except Exception:
            return False
    
    async def get_service_status(self, service_name: str) -> Dict[str, Any]:
        """获取服务状态"""
        if service_name not in self.services:
            return {"error": "Service not found"}
        
        service = self.services[service_name]
        status = {
            "name": service.name,
            "type": service.type,
            "status": service.status,
            "url": service.url,
            "health": "unknown"
        }
        
        # 检查健康状态
        if service.type == "http" and service.health_check:
            try:
                async with httpx.AsyncClient() as client:
                    response = await client.get(service.health_check, timeout=5)
                    status["health"] = "healthy" if response.status_code == 200 else "unhealthy"
            except:
                status["health"] = "unhealthy"
        
        elif service.type == "stdio" and service.process:
            status["health"] = "healthy" if service.process.poll() is None else "unhealthy"
        
        return status
    
    def setup_routes(self):
        """设置API路由"""
        
        @self.app.get("/", response_class=HTMLResponse)
        async def dashboard():
            """MCP管理仪表板"""
            try:
                with open("templates/mcp_dashboard.html", "r", encoding="utf-8") as f:
                    return f.read()
            except FileNotFoundError:
                return HTMLResponse("""
                <h1>MCP Manager</h1>
                <p>Dashboard template not found. API is available at <a href="/docs">/docs</a></p>
                """)
        
        @self.app.get("/api")
        async def api_root():
            return {"message": "MCP Manager API", "version": "1.0.0"}
        
        @self.app.get("/services")
        async def list_services():
            """列出所有服务"""
            services = []
            for name, service in self.services.items():
                status = await self.get_service_status(name)
                services.append(status)
            return {"services": services}
        
        @self.app.post("/services/{service_name}/start")
        async def start_service_endpoint(service_name: str):
            """启动服务"""
            success = await self.start_service(service_name)
            if success:
                return {"message": f"Service {service_name} started"}
            else:
                raise HTTPException(status_code=500, detail=f"Failed to start {service_name}")
        
        @self.app.post("/services/{service_name}/stop")
        async def stop_service_endpoint(service_name: str):
            """停止服务"""
            success = await self.stop_service(service_name)
            if success:
                return {"message": f"Service {service_name} stopped"}
            else:
                raise HTTPException(status_code=500, detail=f"Failed to stop {service_name}")
        
        @self.app.get("/services/{service_name}/status")
        async def get_service_status_endpoint(service_name: str):
            """获取服务状态"""
            status = await self.get_service_status(service_name)
            return status
        
        @self.app.post("/services/start-all")
        async def start_all_services():
            """启动所有服务"""
            results = {}
            for service_name in self.services.keys():
                results[service_name] = await self.start_service(service_name)
            return {"results": results}
        
        @self.app.post("/services/stop-all")
        async def stop_all_services():
            """停止所有服务"""
            results = {}
            for service_name in self.services.keys():
                results[service_name] = await self.stop_service(service_name)
            return {"results": results}
    
    def run(self, host: str = "0.0.0.0", port: int = 8090):
        """运行MCP管理器"""
        logger.info(f"启动MCP管理器: http://{host}:{port}")
        uvicorn.run(self.app, host=host, port=port)

if __name__ == "__main__":
    manager = MCPManager()
    manager.run()