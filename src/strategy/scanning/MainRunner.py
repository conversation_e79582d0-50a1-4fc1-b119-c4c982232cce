import sys
import os

# Add project root to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

import time
import threading
from typing import Optional
import queue
import json

from ib_insync import Index, Trade, Ticker

from ibapi.domain.OptionMarketOrder import OptionMarketOrder
from strategy.scanning.Config import MAIN_CONFIG
from utils import DateUtils
from utils import OptionUtils
from runner.V2.BaseRunner import BaseRunner
from utils.technical_indicators.cpa_macd import CPAMACDIndicator


class MainRunner(BaseRunner):

    # 在MainRunner类中添加新属性和方法
    
    def __init__(self, **kwargs):
        # Merge kwargs with MAIN_CONFIG, giving priority to kwargs
        config = {**MAIN_CONFIG, **kwargs}
        super().__init__(**config)
        self.selected_symbol = MAIN_CONFIG.get('selected_symbol')
        self.fake_open_order = MAIN_CONFIG.get('fake_open_order')
        self.enalbe_trade_logic = MAIN_CONFIG.get('enalbe_trade_logic')
        self.__mock_data_enabled = MAIN_CONFIG.get('mock_data_enabled')
        self.display_rr_info = ""
        
        # 添加新的数据结构
        self.market_data = {}
        self.hot_stocks_data = []
        self.abnormal_stocks_data = []
        self.technical_indicators_data = {}
        self.trading_signals = []
        self.position_analysis_data = {}
        self.historical_performance_data = {}
        self.risk_assessment_data = {}
        self.action_recommendations = []
        
        # 初始化CPA-MACD指标
        self.cpa_macd = CPAMACDIndicator()
        self.price_history = {}
        
        # n8n推送消息队列
        self.n8n_messages = []
        
        self.listening_contracts = None
        self.__counter = 0
        self._listening_thread = None
        self._stop_listening = threading.Event()
    
    # 添加接收n8n推送的方法
    def receive_n8n_webhook(self, message_data):
        """处理来自n8n的webhook推送"""
        # 添加时间戳
        message_data['timestamp'] = DateUtils.get_today_and_time_in_us_eastern()
        self.n8n_messages.append(message_data)
        print(f"Received n8n message: {message_data}")

    def start_process(self):
        self.load_contracts()
        # The listen_a_list_to_market_data will block and run forever
        self.iBMarketData.listen_a_list_to_market_data(self.listening_contracts, self.update_on_market_price)
        self.iBMarketData.ibService.ib_client.run()

    def load_contracts(self):
        if self.selected_symbol == 'SPX':
            self.listening_contracts = [Index('SPX', 'CBOE')]
        else:
            # 默认合约
            self.listening_contracts = [Index('SPX', 'CBOE')]



    def update_on_market_price(self, ticker):
        # 获取合约代码
        symbol = ticker.contract.symbol
        
        # 初始化该合约的价格历史记录
        if symbol not in self.price_history:
            self.price_history[symbol] = []
        
        # 存储当前价格数据
        current_data = {
            'datetime': ticker.time,
            'high': ticker.high,
            'low': ticker.low,
            'close': ticker.close,
            'open': ticker.open,
            'prev_high': self.price_history[symbol][-1]['high'] if self.price_history[symbol] else None,
            'prev_low': self.price_history[symbol][-1]['low'] if self.price_history[symbol] else None
        }
        self.price_history[symbol].append(current_data)
        
        # 计算CPA-MACD指标
        if len(self.price_history[symbol]) >= 2:
            prev_data = self.price_history[symbol][-2]
            dif, dea, macd = self.cpa_macd.update(
                high=current_data['high'],
                low=current_data['low'],
                close=current_data['close'],
                open_=current_data['open'],
                prev_high=prev_data['high'],
                prev_low=prev_data['low']
            )
            
            # 存储技术指标数据
            self.technical_indicators_data[symbol] = {
                'dif': dif,
                'dea': dea,
                'macd': macd,
                'dcp_ratio': self.cpa_macd.calculate_dcp_ratio(
                    current_data['high'], current_data['low'],
                    current_data['close'], current_data['open'],
                    prev_data['high'], prev_data['low']
                ),
                'timestamp': DateUtils.get_today_and_time_in_us_eastern()
            }
        
        # Convert ticker to a serializable dict
        ticker_data = {
            'symbol': ticker.contract.symbol,
            'bid': ticker.bid,
            'ask': ticker.ask,
            'last': ticker.last,
            'close': ticker.close,
            'time': ticker.time.isoformat() if ticker.time else None
        }
        # Update the market_data dict
        self.market_data[ticker.contract.symbol] = ticker_data

        # Write the entire market_data dict to a file
        try:
            with open('market_data.json', 'w') as f:
                json.dump(self.market_data, f, indent=4)
        except Exception as e:
            print(f"Error writing to market_data.json: {e}")

if __name__ == '__main__':
    runner = MainRunner()
    try:
        runner.start_process()
    except (KeyboardInterrupt, SystemExit):
        print("MainRunner stopped by user.")
    finally:
        if runner.iBMarketData.ibService.ib_client.isConnected():
            runner.iBMarketData.ibService.ib_client.disconnect()
        print("IB connection closed.")
