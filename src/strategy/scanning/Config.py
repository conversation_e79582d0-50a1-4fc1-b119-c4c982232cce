

from ib_insync import Index


SPX_CONTRACT = Index('SPX', 'CBOE', 'USD')

MAIN_CONFIG = {
    
    'enalbe_trade_logic': True,
    
	'mock_data_enabled': False,
	'mock_data_date': "20250501",
	'delay_trade_mocks': False,
    
    'selected_symbol': 'SPX',
    'auto_rebalance_enabled': False,
    'auto_release_rebalance': False,
    'use_all_pnl_to_close_all': True,

    'single_wide_range': 50,
    'single_step': 5,
    'tv_thres_drop_percent': 50 / 100.0,
    'log_every_n_seconds': 1,
	'stop_time': "16:01:15",
    
    'take_profit_percent': 90 / 100.0,
    'single_stoploss_percent': 10000 / 100.0,
    'on_close_side_option_leave_quantity': 1,
    'use_closed_check_open_side': False,
    'max_side_quantity': 15,
    'max_profit_to_take': 10000,
    'min_price_threshold_to_rebalance': 2.5,
    
	'is_debug': False,
	'full_log': False,
	'debug_log': False,
	'fake_close_order': False,           # Always run first True just in case
    'default_option_date': None,
    


	# 'trailing_profit': 2.0,
	# 'default_sl_distance': 25,
	
	# 'normal_straddle_cost': 0.25,
	
	# 'side_take_profit': 0.75,
	
	# 'fake_open_order': False,
	
	# 'max_loss': 3.5,
}
