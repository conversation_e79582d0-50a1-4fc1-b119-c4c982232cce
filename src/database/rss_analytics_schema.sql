-- RSS分析数据库架构设计
-- 太公心易BI系统 - RSS历史数据分析

-- 创建数据库
-- CREATE DATABASE taigong_xinyi_rss;

-- 启用必要的扩展
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";  -- 模糊搜索
CREATE EXTENSION IF NOT EXISTS "btree_gin"; -- 复合索引优化

-- 1. RSS源管理表
CREATE TABLE rss_sources (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    url TEXT NOT NULL UNIQUE,
    category VARCHAR(100) NOT NULL, -- 财经、科技、军事、社会等
    language VARCHAR(10) DEFAULT 'en', -- zh, en
    region VARCHAR(50) DEFAULT 'global', -- 地区标识
    reliability_score INTEGER DEFAULT 80, -- 可靠性评分 0-100
    update_frequency INTEGER DEFAULT 60, -- 更新频率(分钟)
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 2. RSS文章主表
CREATE TABLE rss_articles (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    source_id UUID REFERENCES rss_sources(id),
    title TEXT NOT NULL,
    description TEXT,
    content TEXT, -- 完整内容(如果可获取)
    url TEXT NOT NULL,
    author VARCHAR(255),
    published_at TIMESTAMP WITH TIME ZONE NOT NULL,
    fetched_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- 分析字段
    impact_score FLOAT DEFAULT 0, -- 影响力评分 0-100
    urgency_level VARCHAR(20) DEFAULT 'low', -- high, medium, low
    sentiment_score FLOAT DEFAULT 0, -- 情绪评分 -1到1
    
    -- 分类字段
    primary_category VARCHAR(50), -- 主要分类
    secondary_categories TEXT[], -- 次要分类数组
    
    -- 元数据
    keywords TEXT[], -- 关键词数组
    entities JSONB, -- 实体识别结果
    metadata JSONB, -- 其他元数据
    
    -- 去重字段
    content_hash VARCHAR(64) UNIQUE, -- 内容哈希，用于去重
    
    UNIQUE(url, published_at) -- 防止重复抓取
);

-- 3. 关键词表
CREATE TABLE keywords (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    keyword VARCHAR(255) NOT NULL UNIQUE,
    category VARCHAR(100), -- 关键词分类
    weight INTEGER DEFAULT 50, -- 权重 0-100
    language VARCHAR(10) DEFAULT 'en',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 4. 文章-关键词关联表
CREATE TABLE article_keywords (
    article_id UUID REFERENCES rss_articles(id) ON DELETE CASCADE,
    keyword_id UUID REFERENCES keywords(id) ON DELETE CASCADE,
    relevance_score FLOAT DEFAULT 1.0, -- 相关性评分
    PRIMARY KEY (article_id, keyword_id)
);

-- 5. 事件聚类表
CREATE TABLE news_events (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    title VARCHAR(500) NOT NULL,
    description TEXT,
    event_type VARCHAR(100), -- 事件类型：政治、经济、军事、科技等
    start_time TIMESTAMP WITH TIME ZONE NOT NULL,
    end_time TIMESTAMP WITH TIME ZONE,
    impact_level VARCHAR(20) DEFAULT 'medium', -- critical, high, medium, low
    
    -- 地理信息
    countries TEXT[], -- 涉及国家
    regions TEXT[], -- 涉及地区
    
    -- 统计信息
    article_count INTEGER DEFAULT 0,
    total_impact_score FLOAT DEFAULT 0,
    avg_sentiment FLOAT DEFAULT 0,
    
    -- 元数据
    tags TEXT[],
    metadata JSONB,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 6. 文章-事件关联表
CREATE TABLE article_events (
    article_id UUID REFERENCES rss_articles(id) ON DELETE CASCADE,
    event_id UUID REFERENCES news_events(id) ON DELETE CASCADE,
    relevance_score FLOAT DEFAULT 1.0,
    PRIMARY KEY (article_id, event_id)
);

-- 7. 趋势分析表
CREATE TABLE trend_analysis (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    analysis_date DATE NOT NULL,
    time_range VARCHAR(20) NOT NULL, -- 24h, 5d, 10d, 30d
    category VARCHAR(100) NOT NULL,
    
    -- 统计数据
    total_articles INTEGER DEFAULT 0,
    high_impact_articles INTEGER DEFAULT 0,
    avg_impact_score FLOAT DEFAULT 0,
    avg_sentiment FLOAT DEFAULT 0,
    
    -- 热门关键词
    top_keywords JSONB, -- [{keyword: "", count: 0, score: 0}]
    trending_topics JSONB, -- 趋势话题
    
    -- 地理分布
    country_distribution JSONB,
    region_distribution JSONB,
    
    -- 时间分布
    hourly_distribution JSONB,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    UNIQUE(analysis_date, time_range, category)
);

-- 8. 系统配置表
CREATE TABLE system_config (
    key VARCHAR(255) PRIMARY KEY,
    value JSONB NOT NULL,
    description TEXT,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ================================
-- 索引优化
-- ================================

-- 时间范围查询优化
CREATE INDEX idx_articles_published_at ON rss_articles(published_at DESC);
CREATE INDEX idx_articles_fetched_at ON rss_articles(fetched_at DESC);

-- 分类查询优化
CREATE INDEX idx_articles_category ON rss_articles(primary_category);
CREATE INDEX idx_articles_impact_score ON rss_articles(impact_score DESC);
CREATE INDEX idx_articles_urgency ON rss_articles(urgency_level);

-- 全文搜索优化
CREATE INDEX idx_articles_title_gin ON rss_articles USING gin(to_tsvector('english', title));
CREATE INDEX idx_articles_description_gin ON rss_articles USING gin(to_tsvector('english', description));
CREATE INDEX idx_articles_keywords_gin ON rss_articles USING gin(keywords);

-- 复合索引
CREATE INDEX idx_articles_time_category ON rss_articles(published_at DESC, primary_category);
CREATE INDEX idx_articles_time_impact ON rss_articles(published_at DESC, impact_score DESC);

-- 事件分析索引
CREATE INDEX idx_events_time_type ON news_events(start_time DESC, event_type);
CREATE INDEX idx_events_impact ON news_events(impact_level, total_impact_score DESC);

-- 趋势分析索引
CREATE INDEX idx_trends_date_range ON trend_analysis(analysis_date DESC, time_range);

-- ================================
-- 视图定义
-- ================================

-- 24小时热点新闻视图
CREATE VIEW hot_news_24h AS
SELECT 
    a.id,
    a.title,
    a.description,
    a.url,
    a.published_at,
    a.impact_score,
    a.urgency_level,
    a.sentiment_score,
    a.keywords,
    s.name as source_name,
    s.category as source_category
FROM rss_articles a
JOIN rss_sources s ON a.source_id = s.id
WHERE a.published_at >= NOW() - INTERVAL '24 hours'
    AND a.impact_score >= 70
ORDER BY a.impact_score DESC, a.published_at DESC;

-- 分类趋势统计视图
CREATE VIEW category_trends AS
SELECT 
    primary_category,
    DATE_TRUNC('day', published_at) as date,
    COUNT(*) as article_count,
    AVG(impact_score) as avg_impact,
    AVG(sentiment_score) as avg_sentiment,
    COUNT(CASE WHEN impact_score >= 80 THEN 1 END) as high_impact_count
FROM rss_articles
WHERE published_at >= NOW() - INTERVAL '30 days'
GROUP BY primary_category, DATE_TRUNC('day', published_at)
ORDER BY date DESC, avg_impact DESC;

-- 关键词热度视图
CREATE VIEW keyword_trends AS
SELECT 
    k.keyword,
    k.category,
    COUNT(ak.article_id) as mention_count,
    AVG(a.impact_score) as avg_impact,
    MAX(a.published_at) as last_mentioned
FROM keywords k
JOIN article_keywords ak ON k.id = ak.keyword_id
JOIN rss_articles a ON ak.article_id = a.id
WHERE a.published_at >= NOW() - INTERVAL '7 days'
GROUP BY k.keyword, k.category
HAVING COUNT(ak.article_id) >= 3
ORDER BY mention_count DESC, avg_impact DESC;

-- ================================
-- 存储过程
-- ================================

-- 计算文章影响力评分
CREATE OR REPLACE FUNCTION calculate_impact_score(
    article_title TEXT,
    article_description TEXT DEFAULT '',
    article_keywords TEXT[] DEFAULT ARRAY[]::TEXT[]
) RETURNS FLOAT AS $$
DECLARE
    score FLOAT := 0;
    keyword_record RECORD;
BEGIN
    -- 基于关键词计算评分
    FOR keyword_record IN 
        SELECT k.keyword, k.weight 
        FROM keywords k 
        WHERE k.keyword = ANY(article_keywords)
    LOOP
        score := score + keyword_record.weight;
    END LOOP;
    
    -- 标题中的关键词权重加倍
    FOR keyword_record IN 
        SELECT k.keyword, k.weight 
        FROM keywords k 
        WHERE LOWER(article_title) LIKE '%' || LOWER(k.keyword) || '%'
    LOOP
        score := score + keyword_record.weight * 0.5;
    END LOOP;
    
    -- 限制评分范围
    RETURN LEAST(score, 100);
END;
$$ LANGUAGE plpgsql;

-- 更新趋势分析数据
CREATE OR REPLACE FUNCTION update_trend_analysis(
    target_date DATE DEFAULT CURRENT_DATE,
    time_ranges TEXT[] DEFAULT ARRAY['24h', '5d', '10d', '30d']
) RETURNS VOID AS $$
DECLARE
    time_range TEXT;
    category_record RECORD;
    interval_value INTERVAL;
BEGIN
    FOREACH time_range IN ARRAY time_ranges LOOP
        -- 确定时间间隔
        CASE time_range
            WHEN '24h' THEN interval_value := INTERVAL '24 hours';
            WHEN '5d' THEN interval_value := INTERVAL '5 days';
            WHEN '10d' THEN interval_value := INTERVAL '10 days';
            WHEN '30d' THEN interval_value := INTERVAL '30 days';
            ELSE interval_value := INTERVAL '24 hours';
        END CASE;
        
        -- 为每个分类更新趋势数据
        FOR category_record IN 
            SELECT DISTINCT primary_category 
            FROM rss_articles 
            WHERE primary_category IS NOT NULL
        LOOP
            INSERT INTO trend_analysis (
                analysis_date, time_range, category,
                total_articles, high_impact_articles,
                avg_impact_score, avg_sentiment
            )
            SELECT 
                target_date,
                time_range,
                category_record.primary_category,
                COUNT(*),
                COUNT(CASE WHEN impact_score >= 80 THEN 1 END),
                AVG(impact_score),
                AVG(sentiment_score)
            FROM rss_articles
            WHERE primary_category = category_record.primary_category
                AND published_at >= target_date - interval_value
                AND published_at < target_date + INTERVAL '1 day'
            ON CONFLICT (analysis_date, time_range, category) 
            DO UPDATE SET
                total_articles = EXCLUDED.total_articles,
                high_impact_articles = EXCLUDED.high_impact_articles,
                avg_impact_score = EXCLUDED.avg_impact_score,
                avg_sentiment = EXCLUDED.avg_sentiment,
                created_at = NOW();
        END LOOP;
    END LOOP;
END;
$$ LANGUAGE plpgsql;

-- ================================
-- 初始数据
-- ================================

-- 插入RSS源
INSERT INTO rss_sources (name, url, category, language, region) VALUES
('华尔街日报', 'https://feeds.feedburner.com/wsj/xml/rss/3_7455', '财经', 'en', 'us'),
('BizToc', 'https://feeds.feedburner.com/feedburner/gmhbo8cjuyn', '财经', 'en', 'global'),
('The Verge', 'https://feeds.feedburner.com/theverge/pia75kcrkgl', '科技', 'en', 'global'),
('界面新闻财经', 'https://feeds.feedburner.com/anyfeeder/vm7b2bsvtvl', '财经', 'zh', 'cn'),
('BBC World', 'https://feeds.bbci.co.uk/news/world/rss.xml', '国际', 'en', 'global'),
('Reuters Business', 'https://feeds.reuters.com/reuters/businessNews', '财经', 'en', 'global'),
('CNN Technology', 'https://rss.cnn.com/rss/edition_technology.rss', '科技', 'en', 'global'),
('CoinTelegraph', 'https://cointelegraph.com/rss', '加密货币', 'en', 'global');

-- 插入关键词权重
INSERT INTO keywords (keyword, category, weight, language) VALUES
-- 极高影响关键词
('核战争', '军事', 100, 'zh'),
('nuclear war', '军事', 100, 'en'),
('世界大战', '军事', 100, 'zh'),
('world war', '军事', 100, 'en'),
('金融危机', '财经', 95, 'zh'),
('financial crisis', '财经', 95, 'en'),

-- 高影响关键词
('美联储', '财经', 90, 'zh'),
('Federal Reserve', '财经', 90, 'en'),
('加息', '财经', 85, 'zh'),
('interest rate', '财经', 85, 'en'),
('战争', '军事', 90, 'zh'),
('war', '军事', 90, 'en'),

-- 科技关键词
('AI', '科技', 70, 'en'),
('人工智能', '科技', 75, 'zh'),
('artificial intelligence', '科技', 75, 'en'),
('ChatGPT', '科技', 80, 'en'),
('OpenAI', '科技', 75, 'en'),

-- 公司关键词
('苹果', '科技', 65, 'zh'),
('Apple', '科技', 65, 'en'),
('特斯拉', '科技', 70, 'zh'),
('Tesla', '科技', 70, 'en'),
('英伟达', '科技', 75, 'zh'),
('NVIDIA', '科技', 75, 'en'),

-- 加密货币关键词
('比特币', '加密货币', 60, 'zh'),
('Bitcoin', '加密货币', 60, 'en'),
('以太坊', '加密货币', 55, 'zh'),
('Ethereum', '加密货币', 55, 'en');

-- 插入系统配置
INSERT INTO system_config (key, value, description) VALUES
('rss_fetch_interval', '300', 'RSS抓取间隔(秒)'),
('impact_threshold', '70', '重要新闻影响力阈值'),
('trend_update_schedule', '"0 2 * * *"', '趋势分析更新计划(cron)'),
('max_articles_per_source', '50', '每个源最大文章数'),
('article_retention_days', '90', '文章保留天数');

-- 创建定时任务(需要pg_cron扩展)
-- SELECT cron.schedule('update-trends', '0 2 * * *', 'SELECT update_trend_analysis();');