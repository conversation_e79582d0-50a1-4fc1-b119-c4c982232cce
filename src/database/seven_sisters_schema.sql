-- 七姐妹基本面数据表结构
-- 每日更新的静态基本面数据，用于横向比较分析

-- 七姐妹基本面数据主表
CREATE TABLE IF NOT EXISTS seven_sisters_fundamentals (
    id SERIAL PRIMARY KEY,
    symbol VARCHAR(10) NOT NULL,
    company_name VARCHAR(100) NOT NULL,
    date DATE NOT NULL,
    
    -- 价格数据
    close_price DECIMAL(10,2),
    market_cap BIGINT,
    
    -- 基本面指标
    eps_ttm DECIMAL(10,4),           -- 过去12个月EPS
    pe_ratio DECIMAL(8,2),           -- 市盈率
    pb_ratio DECIMAL(8,2),           -- 市净率
    
    -- 增长指标
    eps_growth_pct DECIMAL(8,2),     -- EPS增长率
    revenue_growth_pct DECIMAL(8,2), -- 收入增长率
    
    -- 财务健康指标
    roe_pct DECIMAL(8,2),            -- 净资产收益率
    roa_pct DECIMAL(8,2),            -- 总资产收益率
    debt_to_equity DECIMAL(8,2),     -- 债务股权比
    
    -- 估值指标
    price_to_book DECIMAL(8,2),      -- 市净率
    price_to_sales DECIMAL(8,2),     -- 市销率
    
    -- 分红指标
    dividend_yield_pct DECIMAL(6,3), -- 股息收益率
    
    -- 元数据
    data_source VARCHAR(20) DEFAULT 'IB',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- 唯一约束：每个股票每天只有一条记录
    UNIQUE(symbol, date)
);

-- 创建索引提高查询性能
CREATE INDEX IF NOT EXISTS idx_seven_sisters_symbol_date ON seven_sisters_fundamentals(symbol, date DESC);
CREATE INDEX IF NOT EXISTS idx_seven_sisters_date ON seven_sisters_fundamentals(date DESC);
CREATE INDEX IF NOT EXISTS idx_seven_sisters_symbol ON seven_sisters_fundamentals(symbol);

-- 七姐妹股票信息表
CREATE TABLE IF NOT EXISTS seven_sisters_info (
    symbol VARCHAR(10) PRIMARY KEY,
    company_name VARCHAR(100) NOT NULL,
    chinese_name VARCHAR(50),
    sector VARCHAR(50) DEFAULT 'Technology',
    emoji VARCHAR(10),
    star_name VARCHAR(20),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 插入七姐妹基本信息
INSERT INTO seven_sisters_info (symbol, company_name, chinese_name, emoji, star_name) VALUES
('AAPL', 'Apple Inc.', '苹果', '🍎', '昴宿一'),
('MSFT', 'Microsoft Corporation', '微软', '🪟', '昴宿二'),
('GOOGL', 'Alphabet Inc.', '谷歌', '🔍', '昴宿三'),
('AMZN', 'Amazon.com Inc.', '亚马逊', '📦', '昴宿四'),
('TSLA', 'Tesla Inc.', '特斯拉', '⚡', '昴宿五'),
('NVDA', 'NVIDIA Corporation', '英伟达', '🎮', '昴宿六'),
('META', 'Meta Platforms Inc.', 'Meta', '🌐', '昴宿七')
ON CONFLICT (symbol) DO UPDATE SET
    company_name = EXCLUDED.company_name,
    chinese_name = EXCLUDED.chinese_name,
    emoji = EXCLUDED.emoji,
    star_name = EXCLUDED.star_name;

-- 数据更新日志表
CREATE TABLE IF NOT EXISTS seven_sisters_update_log (
    id SERIAL PRIMARY KEY,
    update_date DATE NOT NULL,
    symbols_updated TEXT[],
    success_count INTEGER DEFAULT 0,
    error_count INTEGER DEFAULT 0,
    error_details TEXT,
    duration_seconds INTEGER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建视图：最新基本面数据
CREATE OR REPLACE VIEW seven_sisters_latest AS
SELECT 
    f.*,
    i.chinese_name,
    i.emoji,
    i.star_name
FROM seven_sisters_fundamentals f
JOIN seven_sisters_info i ON f.symbol = i.symbol
WHERE f.date = (
    SELECT MAX(date) 
    FROM seven_sisters_fundamentals f2 
    WHERE f2.symbol = f.symbol
)
ORDER BY f.symbol;

-- 创建视图：EPS排名
CREATE OR REPLACE VIEW seven_sisters_eps_ranking AS
SELECT 
    *,
    RANK() OVER (ORDER BY eps_ttm DESC NULLS LAST) as eps_rank,
    RANK() OVER (ORDER BY pe_ratio ASC NULLS LAST) as pe_rank,
    RANK() OVER (ORDER BY eps_growth_pct DESC NULLS LAST) as growth_rank,
    ROUND(
        (RANK() OVER (ORDER BY eps_ttm DESC NULLS LAST) * 0.4 +
         RANK() OVER (ORDER BY pe_ratio ASC NULLS LAST) * 0.3 +
         RANK() OVER (ORDER BY eps_growth_pct DESC NULLS LAST) * 0.3), 2
    ) as composite_score
FROM seven_sisters_latest
ORDER BY composite_score;

-- 创建函数：获取历史趋势数据
CREATE OR REPLACE FUNCTION get_seven_sisters_trend(
    p_symbol VARCHAR(10),
    p_days INTEGER DEFAULT 30
)
RETURNS TABLE (
    date DATE,
    close_price DECIMAL(10,2),
    eps_ttm DECIMAL(10,4),
    pe_ratio DECIMAL(8,2),
    market_cap BIGINT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        f.date,
        f.close_price,
        f.eps_ttm,
        f.pe_ratio,
        f.market_cap
    FROM seven_sisters_fundamentals f
    WHERE f.symbol = p_symbol
      AND f.date >= CURRENT_DATE - INTERVAL '%s days' % p_days
    ORDER BY f.date DESC;
END;
$$ LANGUAGE plpgsql;

-- 创建函数：计算相对指标
CREATE OR REPLACE FUNCTION calculate_relative_metrics(p_date DATE DEFAULT CURRENT_DATE)
RETURNS TABLE (
    symbol VARCHAR(10),
    company_name VARCHAR(100),
    eps_ttm DECIMAL(10,4),
    eps_percentile DECIMAL(5,2),
    pe_percentile DECIMAL(5,2),
    growth_percentile DECIMAL(5,2),
    overall_score DECIMAL(5,2)
) AS $$
BEGIN
    RETURN QUERY
    WITH latest_data AS (
        SELECT *
        FROM seven_sisters_fundamentals f
        WHERE f.date = (
            SELECT MAX(date) 
            FROM seven_sisters_fundamentals f2 
            WHERE f2.symbol = f.symbol
            AND f2.date <= p_date
        )
    ),
    percentiles AS (
        SELECT 
            l.*,
            PERCENT_RANK() OVER (ORDER BY eps_ttm) * 100 as eps_pct,
            PERCENT_RANK() OVER (ORDER BY pe_ratio DESC) * 100 as pe_pct,
            PERCENT_RANK() OVER (ORDER BY eps_growth_pct) * 100 as growth_pct
        FROM latest_data l
    )
    SELECT 
        p.symbol,
        p.company_name,
        p.eps_ttm,
        ROUND(p.eps_pct, 2) as eps_percentile,
        ROUND(p.pe_pct, 2) as pe_percentile,
        ROUND(p.growth_pct, 2) as growth_percentile,
        ROUND((p.eps_pct * 0.4 + p.pe_pct * 0.3 + p.growth_pct * 0.3), 2) as overall_score
    FROM percentiles p
    ORDER BY overall_score DESC;
END;
$$ LANGUAGE plpgsql;