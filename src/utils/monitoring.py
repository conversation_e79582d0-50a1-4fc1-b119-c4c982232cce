# -*- coding: utf-8 -*-
"""
监控工具模块
提供性能监控、健康检查和日志功能
"""

import time
import logging
import psutil
import os
from functools import wraps
from typing import Dict, Any
from datetime import datetime

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class PerformanceMonitor:
    """
    性能监控类
    """
    
    def __init__(self):
        self.metrics = {
            'function_calls': {},
            'errors': {},
            'response_times': []
        }
    
    def monitor_performance(self, func):
        """
        性能监控装饰器
        """
        @wraps(func)
        def wrapper(*args, **kwargs):
            start_time = time.time()
            func_name = f"{func.__module__}.{func.__name__}"
            
            try:
                # 执行函数
                result = func(*args, **kwargs)
                
                # 记录成功执行
                duration = time.time() - start_time
                self._record_success(func_name, duration)
                
                return result
                
            except Exception as e:
                # 记录错误
                duration = time.time() - start_time
                self._record_error(func_name, duration, str(e))
                
                # 重新抛出异常
                raise
        
        return wrapper
    
    def _record_success(self, func_name: str, duration: float):
        """
        记录成功执行的指标
        """
        # 更新函数调用计数
        if func_name not in self.metrics['function_calls']:
            self.metrics['function_calls'][func_name] = {
                'count': 0,
                'total_time': 0,
                'avg_time': 0
            }
        
        self.metrics['function_calls'][func_name]['count'] += 1
        self.metrics['function_calls'][func_name]['total_time'] += duration
        self.metrics['function_calls'][func_name]['avg_time'] = (
            self.metrics['function_calls'][func_name]['total_time'] / 
            self.metrics['function_calls'][func_name]['count']
        )
        
        # 记录响应时间
        self.metrics['response_times'].append(duration)
        
        # 保持最近1000条记录
        if len(self.metrics['response_times']) > 1000:
            self.metrics['response_times'] = self.metrics['response_times'][-1000:]
        
        # 记录日志
        if duration > 1.0:  # 超过1秒的慢查询
            logger.warning(f"慢查询警告: {func_name} 执行时间 {duration:.2f}s")
        else:
            logger.info(f"✅ {func_name} 执行成功 ({duration:.2f}s)")
    
    def _record_error(self, func_name: str, duration: float, error_msg: str):
        """
        记录错误指标
        """
        if func_name not in self.metrics['errors']:
            self.metrics['errors'][func_name] = []
        
        self.metrics['errors'][func_name].append({
            'timestamp': datetime.now().isoformat(),
            'duration': duration,
            'error': error_msg
        })
        
        # 保持最近100条错误记录
        if len(self.metrics['errors'][func_name]) > 100:
            self.metrics['errors'][func_name] = self.metrics['errors'][func_name][-100:]
        
        logger.error(f"❌ {func_name} 执行失败 ({duration:.2f}s): {error_msg}")
    
    def get_metrics(self) -> Dict[str, Any]:
        """
        获取性能指标
        """
        return {
            'function_calls': self.metrics['function_calls'],
            'error_count': sum(len(errors) for errors in self.metrics['errors'].values()),
            'avg_response_time': (
                sum(self.metrics['response_times']) / len(self.metrics['response_times'])
                if self.metrics['response_times'] else 0
            ),
            'total_requests': len(self.metrics['response_times'])
        }

class HealthChecker:
    """
    健康检查类
    """
    
    @staticmethod
    def check_system_health() -> Dict[str, Any]:
        """
        检查系统健康状态
        """
        try:
            # CPU使用率
            cpu_percent = psutil.cpu_percent(interval=1)
            
            # 内存使用情况
            memory = psutil.virtual_memory()
            
            # 磁盘使用情况
            disk = psutil.disk_usage('/')
            
            # 数据库连接检查
            db_status = HealthChecker._check_database()
            
            health_status = {
                'timestamp': datetime.now().isoformat(),
                'status': 'healthy',
                'cpu_percent': cpu_percent,
                'memory': {
                    'total': memory.total,
                    'available': memory.available,
                    'percent': memory.percent
                },
                'disk': {
                    'total': disk.total,
                    'free': disk.free,
                    'percent': (disk.used / disk.total) * 100
                },
                'database': db_status
            }
            
            # 检查是否有警告
            warnings = []
            if cpu_percent > 80:
                warnings.append(f"CPU使用率过高: {cpu_percent}%")
            if memory.percent > 80:
                warnings.append(f"内存使用率过高: {memory.percent}%")
            if (disk.used / disk.total) * 100 > 80:
                warnings.append(f"磁盘使用率过高: {(disk.used / disk.total) * 100:.1f}%")
            
            if warnings:
                health_status['status'] = 'warning'
                health_status['warnings'] = warnings
                logger.warning(f"系统健康检查警告: {', '.join(warnings)}")
            else:
                logger.info("✅ 系统健康检查通过")
            
            return health_status
            
        except Exception as e:
            logger.error(f"健康检查失败: {e}")
            return {
                'timestamp': datetime.now().isoformat(),
                'status': 'error',
                'error': str(e)
            }
    
    @staticmethod
    def _check_database() -> Dict[str, Any]:
        """
        检查数据库连接
        """
        try:
            if 'DATABASE_URL' in os.environ:
                # 这里可以添加实际的数据库连接测试
                # 目前只检查环境变量是否存在
                return {
                    'status': 'connected',
                    'type': 'postgresql',
                    'url_configured': True
                }
            else:
                return {
                    'status': 'not_configured',
                    'type': 'local',
                    'url_configured': False
                }
        except Exception as e:
            return {
                'status': 'error',
                'error': str(e)
            }

# 全局监控实例
performance_monitor = PerformanceMonitor()

# 导出装饰器
monitor_performance = performance_monitor.monitor_performance