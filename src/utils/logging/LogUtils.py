import logging
import logging.handlers
import os
import sys
import threading

from pytz import timezone
from datetime import datetime

logging.Formatter.converter = lambda *args: datetime.now(tz=timezone('America/New_York')).timetuple()

LOG_FILEPATH = f'{os.path.dirname(os.path.abspath(__file__))}{os.sep}..{os.sep}logs{os.sep}'
# LOG_FILENAME = f'{os.path.dirname(os.path.abspath(__file__))}{os.sep}..{os.sep}logs{os.sep}application.log'

logger = logging.getLogger()


class ThreadNameFileHandler(logging.Handler):
    def emit(self, record):
        filename = f'{os.path.dirname(os.path.abspath(__file__))}{os.sep}..{os.sep}logs{os.sep}{threading.current_thread().name}.log'
        os.makedirs(os.path.dirname(filename), exist_ok=True)
        with open(filename, 'a', encoding='utf-8') as f:
            msg = self.format(record)
            f.write(f'{msg}\n')


def set_logger(filename: str = 'application.log', one_thread: bool = True):
    def decorate_emit(fn):
        # add methods we need to the class
        def new(*args):
            levelno = args[0].levelno
            if (levelno >= logging.CRITICAL):
                color = '\x1b[31;1m'
            elif (levelno >= logging.ERROR):
                color = '\x1b[31;1m'
            elif (levelno >= logging.WARNING):
                color = '\x1b[33;1m'
            elif (levelno >= logging.INFO):
                color = '\x1b[32;1m'
            elif (levelno >= logging.DEBUG):
                color = '\x1b[35;1m'
            else:
                color = '\x1b[0m'
            sys_warning = args[0].msg.startswith('Warning')
            if sys_warning:
                color = '\x1b[37;1m'
            # add colored *** in the beginning of the message
            args[0].msg = "{0}{1}\x1b[0m".format(color, args[0].msg)

            # new feature i like: bolder each args of message
            args[0].args = tuple(f'${color}' + arg + '\x1b[0m' for arg in args[0].args)
            return fn(*args)

        return new

    logger.setLevel(logging.INFO)
    formatter = logging.Formatter('%(asctime)s | %(threadName)s | %(message)s', "%H:%M:%S")

    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setFormatter(formatter)
    console_handler.emit = decorate_emit(console_handler.emit)
    logger.addHandler(console_handler)

    # create LOG_FILENAME if not exist, LOG_FILENAME may contain some non-existing folders
    log_filename = LOG_FILEPATH + filename
    if not os.path.exists(log_filename):
        os.makedirs(os.path.dirname(log_filename), exist_ok=True)
        open(log_filename, 'w').close()

    # maximum file size will be 10Mb
    file_handler = ThreadNameFileHandler()
    file_handler.setFormatter(formatter)
    logger.addHandler(file_handler)
    global_file_handler = logging.handlers.RotatingFileHandler(
        log_filename, maxBytes=10485760, backupCount=50, encoding="utf-8")
    global_file_handler.setFormatter(formatter)
    logger.addHandler(global_file_handler)

# set_logger()
