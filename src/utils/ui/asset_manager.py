from typing import Dict, Any, List, Optional, Union, Callable, Set
from dataclasses import dataclass, field
from abc import ABC, abstractmethod
from pathlib import Path
import os
import hashlib
import mimetypes
import base64
import json
import logging
from enum import Enum
import threading
import time
from urllib.parse import urljoin, urlparse
import re
from collections import defaultdict

# ============================================================================
# 资源相关类型和枚举
# ============================================================================

class AssetType(Enum):
    """资源类型"""
    CSS = "css"
    JAVASCRIPT = "javascript"
    IMAGE = "image"
    FONT = "font"
    VIDEO = "video"
    AUDIO = "audio"
    DOCUMENT = "document"
    DATA = "data"
    ICON = "icon"
    TEMPLATE = "template"
    OTHER = "other"

class AssetLocation(Enum):
    """资源位置"""
    LOCAL = "local"          # 本地文件
    REMOTE = "remote"        # 远程URL
    INLINE = "inline"        # 内联资源
    CDN = "cdn"              # CDN资源
    CACHE = "cache"          # 缓存资源

class LoadStrategy(Enum):
    """加载策略"""
    EAGER = "eager"          # 立即加载
    LAZY = "lazy"            # 懒加载
    PRELOAD = "preload"      # 预加载
    PREFETCH = "prefetch"    # 预获取
    DEFER = "defer"          # 延迟加载

@dataclass
class AssetMetadata:
    """资源元数据"""
    name: str
    path: str
    asset_type: AssetType
    location: AssetLocation = AssetLocation.LOCAL
    size: int = 0
    hash: str = ""
    mime_type: str = ""
    version: str = "1.0.0"
    dependencies: List[str] = field(default_factory=list)
    load_strategy: LoadStrategy = LoadStrategy.EAGER
    priority: int = 0
    cacheable: bool = True
    compress: bool = False
    minify: bool = False
    attributes: Dict[str, Any] = field(default_factory=dict)
    tags: List[str] = field(default_factory=list)
    created_at: float = field(default_factory=time.time)
    modified_at: float = field(default_factory=time.time)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'name': self.name,
            'path': self.path,
            'asset_type': self.asset_type.value,
            'location': self.location.value,
            'size': self.size,
            'hash': self.hash,
            'mime_type': self.mime_type,
            'version': self.version,
            'dependencies': self.dependencies,
            'load_strategy': self.load_strategy.value,
            'priority': self.priority,
            'cacheable': self.cacheable,
            'compress': self.compress,
            'minify': self.minify,
            'attributes': self.attributes,
            'tags': self.tags,
            'created_at': self.created_at,
            'modified_at': self.modified_at
        }

@dataclass
class AssetBundle:
    """资源包"""
    name: str
    assets: List[str] = field(default_factory=list)
    dependencies: List[str] = field(default_factory=list)
    load_strategy: LoadStrategy = LoadStrategy.EAGER
    priority: int = 0
    conditional: str = ""  # 条件加载表达式
    attributes: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'name': self.name,
            'assets': self.assets,
            'dependencies': self.dependencies,
            'load_strategy': self.load_strategy.value,
            'priority': self.priority,
            'conditional': self.conditional,
            'attributes': self.attributes
        }

@dataclass
class AssetManifest:
    """资源清单"""
    version: str
    assets: Dict[str, AssetMetadata] = field(default_factory=dict)
    bundles: Dict[str, AssetBundle] = field(default_factory=dict)
    base_url: str = ""
    cdn_url: str = ""
    cache_bust: bool = True
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'version': self.version,
            'assets': {name: asset.to_dict() for name, asset in self.assets.items()},
            'bundles': {name: bundle.to_dict() for name, bundle in self.bundles.items()},
            'base_url': self.base_url,
            'cdn_url': self.cdn_url,
            'cache_bust': self.cache_bust
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'AssetManifest':
        """从字典创建"""
        manifest = cls(
            version=data['version'],
            base_url=data.get('base_url', ''),
            cdn_url=data.get('cdn_url', ''),
            cache_bust=data.get('cache_bust', True)
        )
        
        # 加载资源
        for name, asset_data in data.get('assets', {}).items():
            asset = AssetMetadata(
                name=asset_data['name'],
                path=asset_data['path'],
                asset_type=AssetType(asset_data['asset_type']),
                location=AssetLocation(asset_data.get('location', 'local')),
                size=asset_data.get('size', 0),
                hash=asset_data.get('hash', ''),
                mime_type=asset_data.get('mime_type', ''),
                version=asset_data.get('version', '1.0.0'),
                dependencies=asset_data.get('dependencies', []),
                load_strategy=LoadStrategy(asset_data.get('load_strategy', 'eager')),
                priority=asset_data.get('priority', 0),
                cacheable=asset_data.get('cacheable', True),
                compress=asset_data.get('compress', False),
                minify=asset_data.get('minify', False),
                attributes=asset_data.get('attributes', {}),
                tags=asset_data.get('tags', []),
                created_at=asset_data.get('created_at', time.time()),
                modified_at=asset_data.get('modified_at', time.time())
            )
            manifest.assets[name] = asset
        
        # 加载资源包
        for name, bundle_data in data.get('bundles', {}).items():
            bundle = AssetBundle(
                name=bundle_data['name'],
                assets=bundle_data.get('assets', []),
                dependencies=bundle_data.get('dependencies', []),
                load_strategy=LoadStrategy(bundle_data.get('load_strategy', 'eager')),
                priority=bundle_data.get('priority', 0),
                conditional=bundle_data.get('conditional', ''),
                attributes=bundle_data.get('attributes', {})
            )
            manifest.bundles[name] = bundle
        
        return manifest

# ============================================================================
# 资源处理器接口
# ============================================================================

class IAssetProcessor(ABC):
    """资源处理器接口"""
    
    @abstractmethod
    def can_process(self, asset_type: AssetType) -> bool:
        """检查是否可以处理指定类型的资源"""
        pass
    
    @abstractmethod
    def process(self, content: str, metadata: AssetMetadata) -> str:
        """处理资源内容"""
        pass
    
    @abstractmethod
    def get_dependencies(self, content: str, metadata: AssetMetadata) -> List[str]:
        """获取资源依赖"""
        pass

class IAssetLoader(ABC):
    """资源加载器接口"""
    
    @abstractmethod
    def can_load(self, location: AssetLocation) -> bool:
        """检查是否可以加载指定位置的资源"""
        pass
    
    @abstractmethod
    def load(self, path: str, metadata: AssetMetadata) -> str:
        """加载资源内容"""
        pass
    
    @abstractmethod
    def exists(self, path: str) -> bool:
        """检查资源是否存在"""
        pass

# ============================================================================
# 内置资源处理器
# ============================================================================

class CSSProcessor(IAssetProcessor):
    """CSS处理器"""
    
    def can_process(self, asset_type: AssetType) -> bool:
        return asset_type == AssetType.CSS
    
    def process(self, content: str, metadata: AssetMetadata) -> str:
        """处理CSS内容"""
        processed_content = content
        
        # 压缩CSS
        if metadata.compress or metadata.minify:
            processed_content = self._minify_css(processed_content)
        
        # 处理URL
        processed_content = self._process_urls(processed_content, metadata)
        
        return processed_content
    
    def get_dependencies(self, content: str, metadata: AssetMetadata) -> List[str]:
        """获取CSS依赖"""
        dependencies = []
        
        # 查找@import
        import_pattern = r'@import\s+["\']([^"\'\']+)["\']'
        imports = re.findall(import_pattern, content)
        dependencies.extend(imports)
        
        # 查找url()
        url_pattern = r'url\(["\']?([^"\')]+)["\']?\)'
        urls = re.findall(url_pattern, content)
        dependencies.extend(urls)
        
        return dependencies
    
    def _minify_css(self, content: str) -> str:
        """压缩CSS"""
        # 移除注释
        content = re.sub(r'/\*.*?\*/', '', content, flags=re.DOTALL)
        
        # 移除多余空白
        content = re.sub(r'\s+', ' ', content)
        content = re.sub(r'\s*{\s*', '{', content)
        content = re.sub(r'\s*}\s*', '}', content)
        content = re.sub(r'\s*;\s*', ';', content)
        content = re.sub(r'\s*,\s*', ',', content)
        content = re.sub(r'\s*:\s*', ':', content)
        
        return content.strip()
    
    def _process_urls(self, content: str, metadata: AssetMetadata) -> str:
        """处理CSS中的URL"""
        def replace_url(match):
            url = match.group(1)
            # 这里可以添加URL处理逻辑，如转换相对路径等
            return f'url("{url}")'
        
        return re.sub(r'url\(["\']?([^"\')]+)["\']?\)', replace_url, content)

class JavaScriptProcessor(IAssetProcessor):
    """JavaScript处理器"""
    
    def can_process(self, asset_type: AssetType) -> bool:
        return asset_type == AssetType.JAVASCRIPT
    
    def process(self, content: str, metadata: AssetMetadata) -> str:
        """处理JavaScript内容"""
        processed_content = content
        
        # 压缩JavaScript
        if metadata.compress or metadata.minify:
            processed_content = self._minify_js(processed_content)
        
        return processed_content
    
    def get_dependencies(self, content: str, metadata: AssetMetadata) -> List[str]:
        """获取JavaScript依赖"""
        dependencies = []
        
        # 查找import语句
        import_pattern = r'import\s+.*?from\s+["\']([^"\'\']+)["\']'
        imports = re.findall(import_pattern, content)
        dependencies.extend(imports)
        
        # 查找require语句
        require_pattern = r'require\(["\']([^"\'\']+)["\']\)'
        requires = re.findall(require_pattern, content)
        dependencies.extend(requires)
        
        return dependencies
    
    def _minify_js(self, content: str) -> str:
        """简单的JavaScript压缩"""
        # 移除单行注释
        content = re.sub(r'//.*?$', '', content, flags=re.MULTILINE)
        
        # 移除多行注释
        content = re.sub(r'/\*.*?\*/', '', content, flags=re.DOTALL)
        
        # 移除多余空白
        content = re.sub(r'\s+', ' ', content)
        
        return content.strip()

class ImageProcessor(IAssetProcessor):
    """图片处理器"""
    
    def can_process(self, asset_type: AssetType) -> bool:
        return asset_type == AssetType.IMAGE
    
    def process(self, content: str, metadata: AssetMetadata) -> str:
        """处理图片内容（通常返回base64编码）"""
        # 对于图片，通常不需要处理内容
        return content
    
    def get_dependencies(self, content: str, metadata: AssetMetadata) -> List[str]:
        """图片通常没有依赖"""
        return []

# ============================================================================
# 内置资源加载器
# ============================================================================

class LocalAssetLoader(IAssetLoader):
    """本地资源加载器"""
    
    def __init__(self, base_path: str = ""):
        self.base_path = base_path
    
    def can_load(self, location: AssetLocation) -> bool:
        return location == AssetLocation.LOCAL
    
    def load(self, path: str, metadata: AssetMetadata) -> str:
        """加载本地资源"""
        full_path = os.path.join(self.base_path, path) if self.base_path else path
        
        try:
            # 根据资源类型选择读取模式
            if metadata.asset_type in [AssetType.IMAGE, AssetType.FONT, AssetType.VIDEO, AssetType.AUDIO]:
                with open(full_path, 'rb') as f:
                    content = base64.b64encode(f.read()).decode('utf-8')
            else:
                with open(full_path, 'r', encoding='utf-8') as f:
                    content = f.read()
            
            return content
        except Exception as e:
            raise FileNotFoundError(f"Failed to load asset {path}: {str(e)}")
    
    def exists(self, path: str) -> bool:
        """检查本地资源是否存在"""
        full_path = os.path.join(self.base_path, path) if self.base_path else path
        return os.path.exists(full_path)

class RemoteAssetLoader(IAssetLoader):
    """远程资源加载器"""
    
    def can_load(self, location: AssetLocation) -> bool:
        return location in [AssetLocation.REMOTE, AssetLocation.CDN]
    
    def load(self, path: str, metadata: AssetMetadata) -> str:
        """加载远程资源"""
        try:
            import requests
            response = requests.get(path, timeout=30)
            response.raise_for_status()
            
            # 根据资源类型处理内容
            if metadata.asset_type in [AssetType.IMAGE, AssetType.FONT, AssetType.VIDEO, AssetType.AUDIO]:
                content = base64.b64encode(response.content).decode('utf-8')
            else:
                content = response.text
            
            return content
        except Exception as e:
            raise ConnectionError(f"Failed to load remote asset {path}: {str(e)}")
    
    def exists(self, path: str) -> bool:
        """检查远程资源是否存在"""
        try:
            import requests
            response = requests.head(path, timeout=10)
            return response.status_code == 200
        except:
            return False

class InlineAssetLoader(IAssetLoader):
    """内联资源加载器"""
    
    def can_load(self, location: AssetLocation) -> bool:
        return location == AssetLocation.INLINE
    
    def load(self, path: str, metadata: AssetMetadata) -> str:
        """加载内联资源（path即为内容）"""
        return path
    
    def exists(self, path: str) -> bool:
        """内联资源总是存在"""
        return True

# ============================================================================
# 资源管理器实现
# ============================================================================

class AssetManager:
    """资源管理器"""
    
    def __init__(self, base_path: str = "", base_url: str = ""):
        self.base_path = base_path
        self.base_url = base_url
        self.manifest = AssetManifest(version="1.0.0", base_url=base_url)
        
        # 资源缓存
        self.content_cache: Dict[str, str] = {}
        self.metadata_cache: Dict[str, AssetMetadata] = {}
        
        # 处理器和加载器
        self.processors: List[IAssetProcessor] = []
        self.loaders: List[IAssetLoader] = []
        
        # 注册默认处理器和加载器
        self._register_default_processors()
        self._register_default_loaders()
        
        # 统计信息
        self.stats = {
            "total_assets": 0,
            "total_bundles": 0,
            "cache_hits": 0,
            "cache_misses": 0,
            "load_times": [],
            "error_count": 0
        }
        
        self.logger = logging.getLogger(self.__class__.__name__)
        self._lock = threading.Lock()
    
    def _register_default_processors(self):
        """注册默认处理器"""
        self.processors.extend([
            CSSProcessor(),
            JavaScriptProcessor(),
            ImageProcessor()
        ])
    
    def _register_default_loaders(self):
        """注册默认加载器"""
        self.loaders.extend([
            LocalAssetLoader(self.base_path),
            RemoteAssetLoader(),
            InlineAssetLoader()
        ])
    
    def register_processor(self, processor: IAssetProcessor):
        """注册资源处理器"""
        self.processors.append(processor)
        self.logger.debug(f"Registered processor: {processor.__class__.__name__}")
    
    def register_loader(self, loader: IAssetLoader):
        """注册资源加载器"""
        self.loaders.append(loader)
        self.logger.debug(f"Registered loader: {loader.__class__.__name__}")
    
    def register_asset(self, name: str, path: str, asset_type: AssetType, 
                      location: AssetLocation = AssetLocation.LOCAL,
                      **kwargs) -> AssetMetadata:
        """注册资源"""
        # 创建资源元数据
        metadata = AssetMetadata(
            name=name,
            path=path,
            asset_type=asset_type,
            location=location,
            **kwargs
        )
        
        # 如果是本地文件，获取文件信息
        if location == AssetLocation.LOCAL:
            full_path = os.path.join(self.base_path, path) if self.base_path else path
            if os.path.exists(full_path):
                stat = os.stat(full_path)
                metadata.size = stat.st_size
                metadata.modified_at = stat.st_mtime
                
                # 计算文件哈希
                metadata.hash = self._calculate_file_hash(full_path)
                
                # 获取MIME类型
                metadata.mime_type = mimetypes.guess_type(full_path)[0] or ""
        
        # 添加到清单
        with self._lock:
            self.manifest.assets[name] = metadata
            self.stats["total_assets"] += 1
        
        self.logger.debug(f"Registered asset: {name} ({asset_type.value})")
        return metadata
    
    def register_bundle(self, name: str, assets: List[str], **kwargs) -> AssetBundle:
        """注册资源包"""
        bundle = AssetBundle(
            name=name,
            assets=assets,
            **kwargs
        )
        
        with self._lock:
            self.manifest.bundles[name] = bundle
            self.stats["total_bundles"] += 1
        
        self.logger.debug(f"Registered bundle: {name} with {len(assets)} assets")
        return bundle
    
    def get_asset(self, name: str) -> Optional[AssetMetadata]:
        """获取资源元数据"""
        return self.manifest.assets.get(name)
    
    def get_bundle(self, name: str) -> Optional[AssetBundle]:
        """获取资源包"""
        return self.manifest.bundles.get(name)
    
    def load_asset_content(self, name: str, use_cache: bool = True) -> str:
        """加载资源内容"""
        start_time = time.time()
        
        try:
            # 检查缓存
            if use_cache and name in self.content_cache:
                with self._lock:
                    self.stats["cache_hits"] += 1
                return self.content_cache[name]
            
            # 获取资源元数据
            metadata = self.get_asset(name)
            if not metadata:
                raise ValueError(f"Asset not found: {name}")
            
            # 查找合适的加载器
            loader = self._find_loader(metadata.location)
            if not loader:
                raise ValueError(f"No loader found for location: {metadata.location}")
            
            # 加载内容
            content = loader.load(metadata.path, metadata)
            
            # 查找合适的处理器
            processor = self._find_processor(metadata.asset_type)
            if processor:
                content = processor.process(content, metadata)
            
            # 缓存内容
            if use_cache and metadata.cacheable:
                self.content_cache[name] = content
            
            # 更新统计
            load_time = time.time() - start_time
            with self._lock:
                self.stats["cache_misses"] += 1
                self.stats["load_times"].append(load_time)
            
            self.logger.debug(f"Loaded asset: {name} in {load_time:.3f}s")
            return content
        
        except Exception as e:
            with self._lock:
                self.stats["error_count"] += 1
            
            self.logger.error(f"Failed to load asset {name}: {str(e)}")
            raise
    
    def load_bundle_content(self, name: str, use_cache: bool = True) -> Dict[str, str]:
        """加载资源包内容"""
        bundle = self.get_bundle(name)
        if not bundle:
            raise ValueError(f"Bundle not found: {name}")
        
        # 解析依赖顺序
        ordered_assets = self._resolve_bundle_dependencies(bundle)
        
        # 加载所有资源
        content = {}
        for asset_name in ordered_assets:
            content[asset_name] = self.load_asset_content(asset_name, use_cache)
        
        return content
    
    def get_asset_url(self, name: str, with_cache_bust: bool = None) -> str:
        """获取资源URL"""
        metadata = self.get_asset(name)
        if not metadata:
            raise ValueError(f"Asset not found: {name}")
        
        # 构建URL
        if metadata.location == AssetLocation.REMOTE or metadata.location == AssetLocation.CDN:
            url = metadata.path
        else:
            base_url = self.manifest.cdn_url or self.manifest.base_url
            url = urljoin(base_url, metadata.path)
        
        # 添加缓存破坏参数
        if with_cache_bust is None:
            with_cache_bust = self.manifest.cache_bust
        
        if with_cache_bust and metadata.hash:
            separator = '&' if '?' in url else '?'
            url += f"{separator}v={metadata.hash[:8]}"
        
        return url
    
    def get_asset_tag(self, name: str, **attributes) -> str:
        """获取资源HTML标签"""
        metadata = self.get_asset(name)
        if not metadata:
            raise ValueError(f"Asset not found: {name}")
        
        url = self.get_asset_url(name)
        
        # 合并属性
        attrs = {**metadata.attributes, **attributes}
        
        # 根据资源类型生成标签
        if metadata.asset_type == AssetType.CSS:
            return self._generate_css_tag(url, attrs)
        elif metadata.asset_type == AssetType.JAVASCRIPT:
            return self._generate_js_tag(url, attrs, metadata.load_strategy)
        elif metadata.asset_type == AssetType.IMAGE:
            return self._generate_img_tag(url, attrs)
        elif metadata.asset_type == AssetType.ICON:
            return self._generate_icon_tag(url, attrs)
        else:
            return f'<!-- Unsupported asset type: {metadata.asset_type.value} -->'
    
    def get_bundle_tags(self, name: str, **attributes) -> List[str]:
        """获取资源包HTML标签"""
        bundle = self.get_bundle(name)
        if not bundle:
            raise ValueError(f"Bundle not found: {name}")
        
        # 解析依赖顺序
        ordered_assets = self._resolve_bundle_dependencies(bundle)
        
        # 生成标签
        tags = []
        for asset_name in ordered_assets:
            try:
                tag = self.get_asset_tag(asset_name, **attributes)
                tags.append(tag)
            except Exception as e:
                self.logger.warning(f"Failed to generate tag for asset {asset_name}: {e}")
        
        return tags
    
    def inline_asset(self, name: str) -> str:
        """内联资源内容"""
        metadata = self.get_asset(name)
        if not metadata:
            raise ValueError(f"Asset not found: {name}")
        
        content = self.load_asset_content(name)
        
        # 根据资源类型生成内联标签
        if metadata.asset_type == AssetType.CSS:
            return f'<style type="text/css">{content}</style>'
        elif metadata.asset_type == AssetType.JAVASCRIPT:
            return f'<script type="text/javascript">{content}</script>'
        elif metadata.asset_type == AssetType.IMAGE:
            data_url = f"data:{metadata.mime_type};base64,{content}"
            return f'<img src="{data_url}" alt="{name}" />'
        else:
            return content
    
    def clear_cache(self, asset_name: str = None):
        """清除缓存"""
        with self._lock:
            if asset_name:
                self.content_cache.pop(asset_name, None)
                self.metadata_cache.pop(asset_name, None)
            else:
                self.content_cache.clear()
                self.metadata_cache.clear()
        
        self.logger.debug(f"Cleared cache for: {asset_name or 'all assets'}")
    
    def scan_directory(self, directory: str, recursive: bool = True, 
                      auto_register: bool = True) -> List[str]:
        """扫描目录中的资源"""
        found_assets = []
        
        for root, dirs, files in os.walk(directory):
            for file in files:
                file_path = os.path.join(root, file)
                rel_path = os.path.relpath(file_path, self.base_path or directory)
                
                # 根据文件扩展名确定资源类型
                asset_type = self._detect_asset_type(file_path)
                if asset_type:
                    found_assets.append(rel_path)
                    
                    if auto_register:
                        name = os.path.splitext(os.path.basename(file))[0]
                        self.register_asset(name, rel_path, asset_type)
            
            if not recursive:
                break
        
        self.logger.info(f"Scanned directory {directory}, found {len(found_assets)} assets")
        return found_assets
    
    def export_manifest(self, file_path: str):
        """导出资源清单"""
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(self.manifest.to_dict(), f, indent=2, ensure_ascii=False)
        
        self.logger.info(f"Exported manifest to: {file_path}")
    
    def import_manifest(self, file_path: str):
        """导入资源清单"""
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        self.manifest = AssetManifest.from_dict(data)
        
        # 更新统计
        with self._lock:
            self.stats["total_assets"] = len(self.manifest.assets)
            self.stats["total_bundles"] = len(self.manifest.bundles)
        
        self.logger.info(f"Imported manifest from: {file_path}")
    
    def _find_processor(self, asset_type: AssetType) -> Optional[IAssetProcessor]:
        """查找资源处理器"""
        for processor in self.processors:
            if processor.can_process(asset_type):
                return processor
        return None
    
    def _find_loader(self, location: AssetLocation) -> Optional[IAssetLoader]:
        """查找资源加载器"""
        for loader in self.loaders:
            if loader.can_load(location):
                return loader
        return None
    
    def _calculate_file_hash(self, file_path: str) -> str:
        """计算文件哈希"""
        hash_md5 = hashlib.md5()
        try:
            with open(file_path, "rb") as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_md5.update(chunk)
            return hash_md5.hexdigest()
        except:
            return ""
    
    def _detect_asset_type(self, file_path: str) -> Optional[AssetType]:
        """检测资源类型"""
        ext = os.path.splitext(file_path)[1].lower()
        
        type_mapping = {
            '.css': AssetType.CSS,
            '.js': AssetType.JAVASCRIPT,
            '.mjs': AssetType.JAVASCRIPT,
            '.ts': AssetType.JAVASCRIPT,
            '.png': AssetType.IMAGE,
            '.jpg': AssetType.IMAGE,
            '.jpeg': AssetType.IMAGE,
            '.gif': AssetType.IMAGE,
            '.svg': AssetType.IMAGE,
            '.webp': AssetType.IMAGE,
            '.ico': AssetType.ICON,
            '.woff': AssetType.FONT,
            '.woff2': AssetType.FONT,
            '.ttf': AssetType.FONT,
            '.otf': AssetType.FONT,
            '.eot': AssetType.FONT,
            '.mp4': AssetType.VIDEO,
            '.webm': AssetType.VIDEO,
            '.ogg': AssetType.VIDEO,
            '.mp3': AssetType.AUDIO,
            '.wav': AssetType.AUDIO,
            '.flac': AssetType.AUDIO,
            '.pdf': AssetType.DOCUMENT,
            '.doc': AssetType.DOCUMENT,
            '.docx': AssetType.DOCUMENT,
            '.json': AssetType.DATA,
            '.xml': AssetType.DATA,
            '.yaml': AssetType.DATA,
            '.yml': AssetType.DATA
        }
        
        return type_mapping.get(ext)
    
    def _resolve_bundle_dependencies(self, bundle: AssetBundle) -> List[str]:
        """解析资源包依赖顺序"""
        # 简单的拓扑排序实现
        visited = set()
        temp_visited = set()
        result = []
        
        def visit(asset_name: str):
            if asset_name in temp_visited:
                raise ValueError(f"Circular dependency detected: {asset_name}")
            
            if asset_name not in visited:
                temp_visited.add(asset_name)
                
                # 访问依赖
                asset = self.get_asset(asset_name)
                if asset:
                    for dep in asset.dependencies:
                        if dep in bundle.assets:  # 只考虑包内的依赖
                            visit(dep)
                
                temp_visited.remove(asset_name)
                visited.add(asset_name)
                result.append(asset_name)
        
        # 按优先级排序
        sorted_assets = sorted(bundle.assets, key=lambda name: (
            self.get_asset(name).priority if self.get_asset(name) else 0
        ), reverse=True)
        
        for asset_name in sorted_assets:
            if asset_name not in visited:
                visit(asset_name)
        
        return result
    
    def _generate_css_tag(self, url: str, attrs: Dict[str, Any]) -> str:
        """生成CSS标签"""
        attr_str = ' '.join(f'{k}="{v}"' for k, v in attrs.items())
        return f'<link rel="stylesheet" type="text/css" href="{url}" {attr_str}>'.strip()
    
    def _generate_js_tag(self, url: str, attrs: Dict[str, Any], 
                        load_strategy: LoadStrategy) -> str:
        """生成JavaScript标签"""
        script_attrs = []
        
        if load_strategy == LoadStrategy.DEFER:
            script_attrs.append('defer')
        elif load_strategy == LoadStrategy.LAZY:
            script_attrs.append('async')
        
        for k, v in attrs.items():
            script_attrs.append(f'{k}="{v}"')
        
        attr_str = ' '.join(script_attrs)
        return f'<script type="text/javascript" src="{url}" {attr_str}></script>'.strip()
    
    def _generate_img_tag(self, url: str, attrs: Dict[str, Any]) -> str:
        """生成图片标签"""
        attr_str = ' '.join(f'{k}="{v}"' for k, v in attrs.items())
        return f'<img src="{url}" {attr_str} />'.strip()
    
    def _generate_icon_tag(self, url: str, attrs: Dict[str, Any]) -> str:
        """生成图标标签"""
        rel = attrs.pop('rel', 'icon')
        attr_str = ' '.join(f'{k}="{v}"' for k, v in attrs.items())
        return f'<link rel="{rel}" href="{url}" {attr_str}>'.strip()
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        avg_load_time = (
            sum(self.stats["load_times"]) / len(self.stats["load_times"])
            if self.stats["load_times"] else 0
        )
        
        cache_hit_rate = (
            self.stats["cache_hits"] / (self.stats["cache_hits"] + self.stats["cache_misses"])
            if (self.stats["cache_hits"] + self.stats["cache_misses"]) > 0 else 0
        )
        
        return {
            **self.stats,
            "average_load_time": avg_load_time,
            "cache_hit_rate": cache_hit_rate,
            "cache_size": len(self.content_cache),
            "processors_count": len(self.processors),
            "loaders_count": len(self.loaders)
        }

# ============================================================================
# 全局资源管理器
# ============================================================================

# 全局资源管理器
global_asset_manager = AssetManager()

# 便捷函数
def register_asset(name: str, path: str, asset_type: AssetType, **kwargs) -> AssetMetadata:
    """注册资源的便捷函数"""
    return global_asset_manager.register_asset(name, path, asset_type, **kwargs)

def register_bundle(name: str, assets: List[str], **kwargs) -> AssetBundle:
    """注册资源包的便捷函数"""
    return global_asset_manager.register_bundle(name, assets, **kwargs)

def load_asset(name: str) -> str:
    """加载资源的便捷函数"""
    return global_asset_manager.load_asset_content(name)

def get_asset_url(name: str) -> str:
    """获取资源URL的便捷函数"""
    return global_asset_manager.get_asset_url(name)

def get_asset_tag(name: str, **attributes) -> str:
    """获取资源标签的便捷函数"""
    return global_asset_manager.get_asset_tag(name, **attributes)

def inline_asset(name: str) -> str:
    """内联资源的便捷函数"""
    return global_asset_manager.inline_asset(name)

# ============================================================================
# 预定义资源设置
# ============================================================================

def setup_default_assets():
    """设置默认资源"""
    # 设置基础路径
    current_dir = os.path.dirname(os.path.abspath(__file__))
    app_dir = os.path.dirname(current_dir)
    
    # 设置资源目录
    assets_dir = os.path.join(app_dir, 'assets')
    static_dir = os.path.join(app_dir, 'static')
    
    global_asset_manager.base_path = assets_dir
    
    # 创建资源目录
    for directory in [assets_dir, static_dir]:
        os.makedirs(directory, exist_ok=True)
        
        # 创建子目录
        for subdir in ['css', 'js', 'images', 'fonts', 'icons']:
            os.makedirs(os.path.join(directory, subdir), exist_ok=True)
    
    # 扫描并注册资源
    if os.path.exists(assets_dir):
        global_asset_manager.scan_directory(assets_dir)
    
    if os.path.exists(static_dir):
        global_asset_manager.scan_directory(static_dir)

# 自动设置默认资源
setup_default_assets()