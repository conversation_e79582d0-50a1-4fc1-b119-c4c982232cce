from typing import Dict, Any, List, Optional, Union, Callable, <PERSON><PERSON>
from datetime import datetime, <PERSON><PERSON>ta
from decimal import Decimal
import re
import json
import hashlib
import base64
from functools import wraps
import streamlit as st
import pandas as pd
import numpy as np

# ============================================================================
# 数据格式化工具
# ============================================================================

def format_currency(value: Union[float, int, Decimal], currency: str = "USD", precision: int = 2) -> str:
    """格式化货币显示"""
    if value is None:
        return "N/A"
    
    try:
        value = float(value)
        
        # 处理大数值的简化显示
        if abs(value) >= 1_000_000_000:
            formatted_value = f"{value / 1_000_000_000:.{precision}f}B"
        elif abs(value) >= 1_000_000:
            formatted_value = f"{value / 1_000_000:.{precision}f}M"
        elif abs(value) >= 1_000:
            formatted_value = f"{value / 1_000:.{precision}f}K"
        else:
            formatted_value = f"{value:.{precision}f}"
        
        # 添加货币符号
        currency_symbols = {
            "USD": "$",
            "CNY": "¥",
            "EUR": "€",
            "GBP": "£",
            "JPY": "¥",
            "HKD": "HK$"
        }
        
        symbol = currency_symbols.get(currency, currency)
        return f"{symbol}{formatted_value}"
        
    except (ValueError, TypeError):
        return "N/A"

def format_percentage(value: Union[float, int], precision: int = 2, show_sign: bool = True) -> str:
    """格式化百分比显示"""
    if value is None:
        return "N/A"
    
    try:
        value = float(value)
        sign = "+" if value > 0 and show_sign else ""
        return f"{sign}{value:.{precision}f}%"
    except (ValueError, TypeError):
        return "N/A"

def format_number(value: Union[float, int], precision: int = 2, use_separator: bool = True) -> str:
    """格式化数字显示"""
    if value is None:
        return "N/A"
    
    try:
        value = float(value)
        
        if use_separator:
            return f"{value:,.{precision}f}"
        else:
            return f"{value:.{precision}f}"
    except (ValueError, TypeError):
        return "N/A"

def format_volume(value: Union[float, int]) -> str:
    """格式化交易量显示"""
    if value is None:
        return "N/A"
    
    try:
        value = float(value)
        
        if value >= 1_000_000_000:
            return f"{value / 1_000_000_000:.2f}B"
        elif value >= 1_000_000:
            return f"{value / 1_000_000:.2f}M"
        elif value >= 1_000:
            return f"{value / 1_000:.2f}K"
        else:
            return f"{value:.0f}"
    except (ValueError, TypeError):
        return "N/A"

def format_datetime(dt: Union[datetime, str], format_str: str = "%Y-%m-%d %H:%M:%S") -> str:
    """格式化日期时间显示"""
    if dt is None:
        return "N/A"
    
    try:
        if isinstance(dt, str):
            # 尝试解析ISO格式的字符串
            dt = datetime.fromisoformat(dt.replace('Z', '+00:00'))
        
        return dt.strftime(format_str)
    except (ValueError, TypeError, AttributeError):
        return str(dt) if dt else "N/A"

def format_time_ago(dt: Union[datetime, str]) -> str:
    """格式化相对时间显示（如：2小时前）"""
    if dt is None:
        return "N/A"
    
    try:
        if isinstance(dt, str):
            dt = datetime.fromisoformat(dt.replace('Z', '+00:00'))
        
        now = datetime.now()
        if dt.tzinfo and not now.tzinfo:
            now = now.replace(tzinfo=dt.tzinfo)
        elif not dt.tzinfo and now.tzinfo:
            dt = dt.replace(tzinfo=now.tzinfo)
        
        diff = now - dt
        
        if diff.days > 0:
            return f"{diff.days}天前"
        elif diff.seconds >= 3600:
            hours = diff.seconds // 3600
            return f"{hours}小时前"
        elif diff.seconds >= 60:
            minutes = diff.seconds // 60
            return f"{minutes}分钟前"
        else:
            return "刚刚"
    except (ValueError, TypeError, AttributeError):
        return "N/A"

# ============================================================================
# 颜色和样式工具
# ============================================================================

def get_color_by_value(value: Union[float, int], positive_color: str = "green", 
                      negative_color: str = "red", neutral_color: str = "gray") -> str:
    """根据数值获取颜色"""
    if value is None:
        return neutral_color
    
    try:
        value = float(value)
        if value > 0:
            return positive_color
        elif value < 0:
            return negative_color
        else:
            return neutral_color
    except (ValueError, TypeError):
        return neutral_color

def get_trend_emoji(value: Union[float, int]) -> str:
    """根据数值获取趋势表情符号"""
    if value is None:
        return "➖"
    
    try:
        value = float(value)
        if value > 0:
            return "📈"
        elif value < 0:
            return "📉"
        else:
            return "➖"
    except (ValueError, TypeError):
        return "➖"

def get_status_color(status: str) -> str:
    """根据状态获取颜色"""
    status_colors = {
        "success": "green",
        "error": "red",
        "warning": "orange",
        "info": "blue",
        "pending": "yellow",
        "active": "green",
        "inactive": "gray",
        "connected": "green",
        "disconnected": "red",
        "loading": "blue"
    }
    return status_colors.get(status.lower(), "gray")

def create_gradient_color(value: float, min_val: float, max_val: float, 
                         start_color: str = "#ff0000", end_color: str = "#00ff00") -> str:
    """创建渐变颜色"""
    if min_val == max_val:
        return start_color
    
    # 标准化值到0-1范围
    normalized = (value - min_val) / (max_val - min_val)
    normalized = max(0, min(1, normalized))  # 确保在0-1范围内
    
    # 简单的线性插值（这里可以扩展为更复杂的颜色插值）
    if normalized < 0.5:
        return start_color
    else:
        return end_color

# ============================================================================
# 数据验证工具
# ============================================================================

def validate_email(email: str) -> bool:
    """验证邮箱格式"""
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return re.match(pattern, email) is not None

def validate_phone(phone: str) -> bool:
    """验证手机号格式"""
    # 简单的手机号验证（可根据需要调整）
    pattern = r'^[1-9]\d{10}$'
    return re.match(pattern, phone.replace('-', '').replace(' ', '')) is not None

def validate_stock_symbol(symbol: str) -> bool:
    """验证股票代码格式"""
    if not symbol:
        return False
    
    # 基本的股票代码验证
    pattern = r'^[A-Z]{1,5}$'
    return re.match(pattern, symbol.upper()) is not None

def validate_number_range(value: Union[float, int], min_val: float = None, 
                         max_val: float = None) -> bool:
    """验证数字范围"""
    try:
        value = float(value)
        
        if min_val is not None and value < min_val:
            return False
        
        if max_val is not None and value > max_val:
            return False
        
        return True
    except (ValueError, TypeError):
        return False

def sanitize_input(text: str, max_length: int = 1000, 
                  allowed_chars: str = None) -> str:
    """清理用户输入"""
    if not text:
        return ""
    
    # 限制长度
    text = text[:max_length]
    
    # 如果指定了允许的字符，则过滤
    if allowed_chars:
        text = ''.join(char for char in text if char in allowed_chars)
    
    # 移除潜在的危险字符
    dangerous_chars = ['<', '>', '"', "'", '&']
    for char in dangerous_chars:
        text = text.replace(char, '')
    
    return text.strip()

# ============================================================================
# 数据处理工具
# ============================================================================

def safe_divide(numerator: Union[float, int], denominator: Union[float, int], 
               default: float = 0.0) -> float:
    """安全除法（避免除零错误）"""
    try:
        num = float(numerator)
        den = float(denominator)
        
        if den == 0:
            return default
        
        return num / den
    except (ValueError, TypeError):
        return default

def calculate_percentage_change(old_value: Union[float, int], 
                              new_value: Union[float, int]) -> Optional[float]:
    """计算百分比变化"""
    try:
        old_val = float(old_value)
        new_val = float(new_value)
        
        if old_val == 0:
            return None if new_val == 0 else float('inf')
        
        return ((new_val - old_val) / old_val) * 100
    except (ValueError, TypeError):
        return None

def moving_average(data: List[Union[float, int]], window: int) -> List[float]:
    """计算移动平均"""
    if len(data) < window:
        return []
    
    result = []
    for i in range(window - 1, len(data)):
        avg = sum(data[i - window + 1:i + 1]) / window
        result.append(avg)
    
    return result

def normalize_data(data: List[Union[float, int]], 
                  method: str = "min_max") -> List[float]:
    """数据标准化"""
    if not data:
        return []
    
    data = [float(x) for x in data if x is not None]
    
    if method == "min_max":
        min_val = min(data)
        max_val = max(data)
        
        if min_val == max_val:
            return [0.5] * len(data)
        
        return [(x - min_val) / (max_val - min_val) for x in data]
    
    elif method == "z_score":
        mean_val = sum(data) / len(data)
        variance = sum((x - mean_val) ** 2 for x in data) / len(data)
        std_dev = variance ** 0.5
        
        if std_dev == 0:
            return [0.0] * len(data)
        
        return [(x - mean_val) / std_dev for x in data]
    
    else:
        raise ValueError(f"不支持的标准化方法: {method}")

def detect_outliers(data: List[Union[float, int]], 
                   method: str = "iqr") -> List[bool]:
    """检测异常值"""
    if not data:
        return []
    
    data = [float(x) for x in data if x is not None]
    
    if method == "iqr":
        # 使用四分位距方法
        sorted_data = sorted(data)
        n = len(sorted_data)
        
        q1_idx = n // 4
        q3_idx = 3 * n // 4
        
        q1 = sorted_data[q1_idx]
        q3 = sorted_data[q3_idx]
        iqr = q3 - q1
        
        lower_bound = q1 - 1.5 * iqr
        upper_bound = q3 + 1.5 * iqr
        
        return [x < lower_bound or x > upper_bound for x in data]
    
    elif method == "z_score":
        # 使用Z分数方法
        mean_val = sum(data) / len(data)
        variance = sum((x - mean_val) ** 2 for x in data) / len(data)
        std_dev = variance ** 0.5
        
        if std_dev == 0:
            return [False] * len(data)
        
        z_scores = [abs((x - mean_val) / std_dev) for x in data]
        return [z > 3 for z in z_scores]  # 3个标准差之外视为异常值
    
    else:
        raise ValueError(f"不支持的异常值检测方法: {method}")

# ============================================================================
# 缓存和性能工具
# ============================================================================

def memoize(func: Callable) -> Callable:
    """简单的记忆化装饰器"""
    cache = {}
    
    @wraps(func)
    def wrapper(*args, **kwargs):
        # 创建缓存键
        key = str(args) + str(sorted(kwargs.items()))
        
        if key not in cache:
            cache[key] = func(*args, **kwargs)
        
        return cache[key]
    
    wrapper.cache = cache
    wrapper.clear_cache = lambda: cache.clear()
    
    return wrapper

def timed_cache(ttl_seconds: int = 300):
    """带时间过期的缓存装饰器"""
    def decorator(func: Callable) -> Callable:
        cache = {}
        
        @wraps(func)
        def wrapper(*args, **kwargs):
            key = str(args) + str(sorted(kwargs.items()))
            now = datetime.now()
            
            # 检查缓存是否存在且未过期
            if key in cache:
                cached_time, cached_result = cache[key]
                if now - cached_time < timedelta(seconds=ttl_seconds):
                    return cached_result
            
            # 执行函数并缓存结果
            result = func(*args, **kwargs)
            cache[key] = (now, result)
            
            return result
        
        wrapper.cache = cache
        wrapper.clear_cache = lambda: cache.clear()
        
        return wrapper
    
    return decorator

def batch_process(items: List[Any], batch_size: int = 100, 
                 processor: Callable = None) -> List[Any]:
    """批量处理数据"""
    if not items:
        return []
    
    if processor is None:
        processor = lambda x: x
    
    results = []
    for i in range(0, len(items), batch_size):
        batch = items[i:i + batch_size]
        batch_results = [processor(item) for item in batch]
        results.extend(batch_results)
    
    return results

# ============================================================================
# 安全工具
# ============================================================================

def generate_hash(data: str, algorithm: str = "sha256") -> str:
    """生成数据哈希"""
    if algorithm == "md5":
        return hashlib.md5(data.encode()).hexdigest()
    elif algorithm == "sha1":
        return hashlib.sha1(data.encode()).hexdigest()
    elif algorithm == "sha256":
        return hashlib.sha256(data.encode()).hexdigest()
    else:
        raise ValueError(f"不支持的哈希算法: {algorithm}")

def encode_base64(data: str) -> str:
    """Base64编码"""
    return base64.b64encode(data.encode()).decode()

def decode_base64(encoded_data: str) -> str:
    """Base64解码"""
    try:
        return base64.b64decode(encoded_data).decode()
    except Exception:
        raise ValueError("无效的Base64编码")

def mask_sensitive_data(data: str, mask_char: str = "*", 
                       visible_chars: int = 4) -> str:
    """遮蔽敏感数据"""
    if len(data) <= visible_chars:
        return mask_char * len(data)
    
    visible_part = data[:visible_chars]
    masked_part = mask_char * (len(data) - visible_chars)
    
    return visible_part + masked_part

# ============================================================================
# Streamlit特定工具
# ============================================================================

def create_download_link(data: Union[str, bytes], filename: str, 
                        link_text: str = "下载文件") -> str:
    """创建下载链接"""
    if isinstance(data, str):
        data = data.encode()
    
    b64_data = base64.b64encode(data).decode()
    
    return f'<a href="data:file/octet-stream;base64,{b64_data}" download="{filename}">{link_text}</a>'

def show_loading_spinner(text: str = "加载中..."):
    """显示加载动画"""
    return st.spinner(text)

def create_metric_card(title: str, value: str, delta: str = None, 
                      delta_color: str = "normal") -> None:
    """创建指标卡片"""
    st.metric(
        label=title,
        value=value,
        delta=delta,
        delta_color=delta_color
    )

def create_alert(message: str, alert_type: str = "info") -> None:
    """创建警告框"""
    if alert_type == "success":
        st.success(message)
    elif alert_type == "error":
        st.error(message)
    elif alert_type == "warning":
        st.warning(message)
    else:
        st.info(message)

def create_expander_section(title: str, content: Callable, 
                           expanded: bool = False) -> None:
    """创建可展开区域"""
    with st.expander(title, expanded=expanded):
        content()

# ============================================================================
# 数据导出工具
# ============================================================================

def export_to_csv(data: Union[List[Dict], pd.DataFrame], filename: str = None) -> str:
    """导出数据为CSV"""
    if isinstance(data, list):
        df = pd.DataFrame(data)
    else:
        df = data
    
    if filename is None:
        filename = f"export_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
    
    csv_data = df.to_csv(index=False)
    return create_download_link(csv_data, filename, "下载CSV文件")

def export_to_json(data: Any, filename: str = None, indent: int = 2) -> str:
    """导出数据为JSON"""
    if filename is None:
        filename = f"export_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    
    json_data = json.dumps(data, indent=indent, ensure_ascii=False, default=str)
    return create_download_link(json_data, filename, "下载JSON文件")

# ============================================================================
# 错误处理工具
# ============================================================================

def safe_execute(func: Callable, default_return: Any = None, 
                show_error: bool = True) -> Any:
    """安全执行函数（捕获异常）"""
    try:
        return func()
    except Exception as e:
        if show_error:
            st.error(f"执行出错: {str(e)}")
        return default_return

def retry_on_failure(max_retries: int = 3, delay: float = 1.0):
    """失败重试装饰器"""
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs):
            last_exception = None
            
            for attempt in range(max_retries):
                try:
                    return func(*args, **kwargs)
                except Exception as e:
                    last_exception = e
                    if attempt < max_retries - 1:
                        time.sleep(delay * (2 ** attempt))  # 指数退避
            
            # 如果所有重试都失败，抛出最后一个异常
            raise last_exception
        
        return wrapper
    return decorator

# ============================================================================
# 调试工具
# ============================================================================

def debug_info(obj: Any, title: str = "Debug Info") -> None:
    """显示调试信息"""
    with st.expander(f"🐛 {title}", expanded=False):
        st.write(f"**类型:** {type(obj).__name__}")
        st.write(f"**值:** {repr(obj)}")
        
        if hasattr(obj, '__dict__'):
            st.write("**属性:**")
            st.json(obj.__dict__)
        
        if isinstance(obj, (list, tuple)):
            st.write(f"**长度:** {len(obj)}")
        
        if isinstance(obj, dict):
            st.write(f"**键数量:** {len(obj)}")
            st.write("**键列表:**")
            st.write(list(obj.keys()))

def performance_monitor(func: Callable) -> Callable:
    """性能监控装饰器"""
    @wraps(func)
    def wrapper(*args, **kwargs):
        start_time = time.time()
        result = func(*args, **kwargs)
        end_time = time.time()
        
        execution_time = end_time - start_time
        
        # 在Streamlit侧边栏显示性能信息
        with st.sidebar:
            st.write(f"⏱️ {func.__name__}: {execution_time:.3f}s")
        
        return result
    
    return wrapper