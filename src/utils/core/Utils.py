

def combine_2_list_remove_duplicate(list1, list2):
	combined_list = list2[:]
	
	for item2 in list1:
		if not any(item2 == item1 for item1 in combined_list):
			combined_list.append(item2)
	
	return combined_list


def smaller_nonzero(a: float, b: float) -> float:
	if a == 0:
		return b
	elif b == 0:
		return a
	else:
		return min(a, b)


def smaller_abs_nonzero(a: float, b: float) -> float:
	return smaller_nonzero(abs(a), abs(b))


def get_quantity_str(quantity: int) -> str:
	return "" if abs(quantity) == 1 else f" *{quantity:.0f}"
