from typing import Dict, Any, Optional, Callable, List, Union
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
import streamlit as st
import json
import threading
from functools import wraps

class StateScope(Enum):
    """状态作用域枚举"""
    SESSION = "session"  # 会话级别
    USER = "user"        # 用户级别
    GLOBAL = "global"    # 全局级别
    CACHE = "cache"      # 缓存级别

@dataclass
class StateItem:
    """状态项配置"""
    key: str
    value: Any
    scope: StateScope = StateScope.SESSION
    ttl: Optional[int] = None  # 生存时间（秒）
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: datetime = field(default_factory=datetime.now)
    access_count: int = 0
    persistent: bool = False  # 是否持久化
    encrypted: bool = False   # 是否加密存储
    validators: List[Callable] = field(default_factory=list)
    
    def is_expired(self) -> bool:
        """检查是否过期"""
        if self.ttl is None:
            return False
        return datetime.now() > self.created_at + timedelta(seconds=self.ttl)
    
    def touch(self):
        """更新访问时间和计数"""
        self.updated_at = datetime.now()
        self.access_count += 1
    
    def validate(self, value: Any) -> bool:
        """验证值是否有效"""
        for validator in self.validators:
            if not validator(value):
                return False
        return True

class StateManager:
    """状态管理器 - 类似前端框架的状态管理"""
    
    def __init__(self):
        self._states: Dict[str, StateItem] = {}
        self._subscribers: Dict[str, List[Callable]] = {}
        self._middleware: List[Callable] = []
        self._lock = threading.Lock()
        
        # 初始化默认状态
        self._init_default_states()
    
    def _init_default_states(self):
        """初始化默认状态"""
        # 应用状态
        self.set_state("app_initialized", False, StateScope.SESSION)
        self.set_state("current_user", None, StateScope.SESSION)
        self.set_state("theme", "default", StateScope.USER, persistent=True)
        self.set_state("language", "zh", StateScope.USER, persistent=True)
        
        # 数据状态
        self.set_state("market_data", {}, StateScope.CACHE, ttl=300)  # 5分钟缓存
        self.set_state("stock_watchlist", [], StateScope.USER, persistent=True)
        self.set_state("portfolio_data", {}, StateScope.USER, persistent=True)
        
        # UI状态
        self.set_state("sidebar_expanded", True, StateScope.SESSION)
        self.set_state("current_tab", "home", StateScope.SESSION)
        self.set_state("loading_states", {}, StateScope.SESSION)
        self.set_state("error_messages", [], StateScope.SESSION)
        
        # 系统状态
        self.set_state("last_update", datetime.now(), StateScope.GLOBAL)
        self.set_state("system_health", "healthy", StateScope.GLOBAL, ttl=60)
        self.set_state("api_status", {}, StateScope.GLOBAL, ttl=30)
    
    def set_state(self, key: str, value: Any, scope: StateScope = StateScope.SESSION, 
                  ttl: Optional[int] = None, persistent: bool = False, 
                  encrypted: bool = False, validators: List[Callable] = None) -> bool:
        """设置状态"""
        with self._lock:
            # 验证值
            if validators:
                for validator in validators:
                    if not validator(value):
                        return False
            
            # 检查现有状态
            if key in self._states:
                existing_item = self._states[key]
                if not existing_item.validate(value):
                    return False
                
                # 更新现有状态
                existing_item.value = value
                existing_item.updated_at = datetime.now()
                existing_item.touch()
            else:
                # 创建新状态
                self._states[key] = StateItem(
                    key=key,
                    value=value,
                    scope=scope,
                    ttl=ttl,
                    persistent=persistent,
                    encrypted=encrypted,
                    validators=validators or []
                )
            
            # 执行中间件
            for middleware in self._middleware:
                middleware('set', key, value, scope)
            
            # 通知订阅者
            self._notify_subscribers(key, value)
            
            # 同步到Streamlit session_state
            self._sync_to_streamlit(key, value, scope)
            
            return True
    
    def get_state(self, key: str, default: Any = None) -> Any:
        """获取状态"""
        with self._lock:
            if key not in self._states:
                # 尝试从Streamlit session_state获取
                if key in st.session_state:
                    return st.session_state[key]
                return default
            
            state_item = self._states[key]
            
            # 检查是否过期
            if state_item.is_expired():
                self.remove_state(key)
                return default
            
            # 更新访问信息
            state_item.touch()
            
            # 执行中间件
            for middleware in self._middleware:
                middleware('get', key, state_item.value, state_item.scope)
            
            return state_item.value
    
    def has_state(self, key: str) -> bool:
        """检查状态是否存在"""
        with self._lock:
            if key in self._states:
                state_item = self._states[key]
                if state_item.is_expired():
                    self.remove_state(key)
                    return False
                return True
            return key in st.session_state
    
    def remove_state(self, key: str) -> bool:
        """删除状态"""
        with self._lock:
            removed = False
            
            if key in self._states:
                del self._states[key]
                removed = True
            
            if key in st.session_state:
                del st.session_state[key]
                removed = True
            
            if removed:
                # 执行中间件
                for middleware in self._middleware:
                    middleware('remove', key, None, None)
                
                # 通知订阅者
                self._notify_subscribers(key, None)
            
            return removed
    
    def update_state(self, key: str, updater: Callable[[Any], Any]) -> bool:
        """更新状态（使用更新函数）"""
        current_value = self.get_state(key)
        new_value = updater(current_value)
        return self.set_state(key, new_value)
    
    def merge_state(self, key: str, partial_value: Dict[str, Any]) -> bool:
        """合并状态（适用于字典类型）"""
        current_value = self.get_state(key, {})
        if isinstance(current_value, dict) and isinstance(partial_value, dict):
            merged_value = {**current_value, **partial_value}
            return self.set_state(key, merged_value)
        return False
    
    def subscribe(self, key: str, callback: Callable[[str, Any], None]):
        """订阅状态变化"""
        if key not in self._subscribers:
            self._subscribers[key] = []
        self._subscribers[key].append(callback)
    
    def unsubscribe(self, key: str, callback: Callable[[str, Any], None]):
        """取消订阅"""
        if key in self._subscribers and callback in self._subscribers[key]:
            self._subscribers[key].remove(callback)
    
    def _notify_subscribers(self, key: str, value: Any):
        """通知订阅者"""
        if key in self._subscribers:
            for callback in self._subscribers[key]:
                try:
                    callback(key, value)
                except Exception as e:
                    st.error(f"状态订阅回调错误: {e}")
    
    def add_middleware(self, middleware: Callable[[str, str, Any, StateScope], None]):
        """添加中间件"""
        self._middleware.append(middleware)
    
    def _sync_to_streamlit(self, key: str, value: Any, scope: StateScope):
        """同步到Streamlit session_state"""
        if scope == StateScope.SESSION:
            st.session_state[key] = value
    
    def clear_expired_states(self):
        """清理过期状态"""
        with self._lock:
            expired_keys = []
            for key, state_item in self._states.items():
                if state_item.is_expired():
                    expired_keys.append(key)
            
            for key in expired_keys:
                self.remove_state(key)
    
    def get_states_by_scope(self, scope: StateScope) -> Dict[str, Any]:
        """按作用域获取状态"""
        with self._lock:
            result = {}
            for key, state_item in self._states.items():
                if state_item.scope == scope and not state_item.is_expired():
                    result[key] = state_item.value
            return result
    
    def export_states(self, scope: Optional[StateScope] = None) -> Dict[str, Any]:
        """导出状态"""
        with self._lock:
            result = {}
            for key, state_item in self._states.items():
                if scope is None or state_item.scope == scope:
                    if not state_item.is_expired():
                        result[key] = {
                            'value': state_item.value,
                            'scope': state_item.scope.value,
                            'created_at': state_item.created_at.isoformat(),
                            'updated_at': state_item.updated_at.isoformat(),
                            'access_count': state_item.access_count
                        }
            return result
    
    def import_states(self, states_data: Dict[str, Any]):
        """导入状态"""
        for key, state_data in states_data.items():
            if isinstance(state_data, dict) and 'value' in state_data:
                scope = StateScope(state_data.get('scope', 'session'))
                self.set_state(key, state_data['value'], scope)
    
    def get_analytics(self) -> Dict[str, Any]:
        """获取状态分析数据"""
        with self._lock:
            total_states = len(self._states)
            expired_states = sum(1 for item in self._states.values() if item.is_expired())
            
            scope_counts = {}
            for scope in StateScope:
                scope_counts[scope.value] = sum(
                    1 for item in self._states.values() 
                    if item.scope == scope and not item.is_expired()
                )
            
            return {
                'total_states': total_states,
                'active_states': total_states - expired_states,
                'expired_states': expired_states,
                'scope_distribution': scope_counts,
                'total_subscribers': sum(len(subs) for subs in self._subscribers.values()),
                'middleware_count': len(self._middleware)
            }

# 状态管理装饰器
def with_state(key: str, default: Any = None, scope: StateScope = StateScope.SESSION):
    """状态管理装饰器"""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            # 获取状态管理器
            state_manager = get_state_manager()
            
            # 确保状态存在
            if not state_manager.has_state(key):
                state_manager.set_state(key, default, scope)
            
            # 将状态作为参数传递给函数
            state_value = state_manager.get_state(key, default)
            return func(state_value, *args, **kwargs)
        return wrapper
    return decorator

def reactive_state(key: str):
    """响应式状态装饰器"""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            state_manager = get_state_manager()
            
            # 订阅状态变化
            def on_state_change(changed_key, new_value):
                if changed_key == key:
                    st.rerun()
            
            state_manager.subscribe(key, on_state_change)
            
            try:
                return func(*args, **kwargs)
            finally:
                state_manager.unsubscribe(key, on_state_change)
        return wrapper
    return decorator

# 全局状态管理器实例
_state_manager = None

def get_state_manager() -> StateManager:
    """获取状态管理器实例"""
    global _state_manager
    if _state_manager is None:
        _state_manager = StateManager()
    return _state_manager

# 便捷函数
def set_state(key: str, value: Any, scope: StateScope = StateScope.SESSION, **kwargs) -> bool:
    """设置状态的便捷函数"""
    return get_state_manager().set_state(key, value, scope, **kwargs)

def get_state(key: str, default: Any = None) -> Any:
    """获取状态的便捷函数"""
    return get_state_manager().get_state(key, default)

def has_state(key: str) -> bool:
    """检查状态是否存在的便捷函数"""
    return get_state_manager().has_state(key)

def remove_state(key: str) -> bool:
    """删除状态的便捷函数"""
    return get_state_manager().remove_state(key)

def update_state(key: str, updater: Callable[[Any], Any]) -> bool:
    """更新状态的便捷函数"""
    return get_state_manager().update_state(key, updater)

def merge_state(key: str, partial_value: Dict[str, Any]) -> bool:
    """合并状态的便捷函数"""
    return get_state_manager().merge_state(key, partial_value)

def subscribe_state(key: str, callback: Callable[[str, Any], None]):
    """订阅状态变化的便捷函数"""
    get_state_manager().subscribe(key, callback)

def clear_expired_states():
    """清理过期状态的便捷函数"""
    get_state_manager().clear_expired_states()

# 常用状态管理函数
def set_loading(key: str, loading: bool = True):
    """设置加载状态"""
    loading_states = get_state("loading_states", {})
    loading_states[key] = loading
    set_state("loading_states", loading_states)

def is_loading(key: str) -> bool:
    """检查是否正在加载"""
    loading_states = get_state("loading_states", {})
    return loading_states.get(key, False)

def add_error(message: str, error_type: str = "error"):
    """添加错误消息"""
    errors = get_state("error_messages", [])
    errors.append({
        "message": message,
        "type": error_type,
        "timestamp": datetime.now().isoformat()
    })
    set_state("error_messages", errors)

def clear_errors():
    """清除错误消息"""
    set_state("error_messages", [])

def get_errors() -> List[Dict[str, Any]]:
    """获取错误消息"""
    return get_state("error_messages", [])