from typing import Dict, Any, List, Optional, Callable, Union, Type, TypeVar, Generic
from dataclasses import dataclass, field
from abc import ABC, abstractmethod
from functools import wraps
import inspect
import threading
import logging
from enum import Enum
import weakref
from contextlib import contextmanager

# ============================================================================
# 依赖注入相关类型和枚举
# ============================================================================

T = TypeVar('T')

class Scope(Enum):
    """依赖作用域"""
    SINGLETON = "singleton"  # 单例
    TRANSIENT = "transient"  # 瞬态
    SCOPED = "scoped"        # 作用域
    REQUEST = "request"      # 请求级别

class LifetimeScope(Enum):
    """生命周期作用域"""
    APPLICATION = "application"
    SESSION = "session"
    REQUEST = "request"
    COMPONENT = "component"

@dataclass
class DependencyInfo:
    """依赖信息"""
    name: str
    dependency_type: Type
    scope: Scope = Scope.TRANSIENT
    lifetime_scope: LifetimeScope = LifetimeScope.APPLICATION
    factory: Optional[Callable] = None
    instance: Optional[Any] = None
    dependencies: List[str] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)
    created_at: Optional[float] = None
    access_count: int = 0
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "name": self.name,
            "type": self.dependency_type.__name__ if self.dependency_type else None,
            "scope": self.scope.value,
            "lifetime_scope": self.lifetime_scope.value,
            "has_factory": self.factory is not None,
            "has_instance": self.instance is not None,
            "dependencies": self.dependencies,
            "metadata": self.metadata,
            "created_at": self.created_at,
            "access_count": self.access_count
        }

@dataclass
class InjectionContext:
    """注入上下文"""
    scope_name: str
    parent_context: Optional['InjectionContext'] = None
    instances: Dict[str, Any] = field(default_factory=dict)
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def get_instance(self, name: str) -> Optional[Any]:
        """获取实例"""
        if name in self.instances:
            return self.instances[name]
        elif self.parent_context:
            return self.parent_context.get_instance(name)
        return None
    
    def set_instance(self, name: str, instance: Any):
        """设置实例"""
        self.instances[name] = instance
    
    def has_instance(self, name: str) -> bool:
        """检查是否有实例"""
        return name in self.instances or (self.parent_context and self.parent_context.has_instance(name))

# ============================================================================
# 依赖注入接口
# ============================================================================

class IDependencyContainer(ABC):
    """依赖容器接口"""
    
    @abstractmethod
    def register(self, name: str, dependency_type: Type[T], 
                scope: Scope = Scope.TRANSIENT, **kwargs) -> 'IDependencyContainer':
        """注册依赖"""
        pass
    
    @abstractmethod
    def register_factory(self, name: str, factory: Callable[[], T], 
                        scope: Scope = Scope.TRANSIENT, **kwargs) -> 'IDependencyContainer':
        """注册工厂"""
        pass
    
    @abstractmethod
    def register_instance(self, name: str, instance: T, **kwargs) -> 'IDependencyContainer':
        """注册实例"""
        pass
    
    @abstractmethod
    def resolve(self, name: str, context: Optional[InjectionContext] = None) -> Any:
        """解析依赖"""
        pass
    
    @abstractmethod
    def resolve_type(self, dependency_type: Type[T], context: Optional[InjectionContext] = None) -> T:
        """按类型解析依赖"""
        pass
    
    @abstractmethod
    def is_registered(self, name: str) -> bool:
        """检查是否已注册"""
        pass
    
    @abstractmethod
    def unregister(self, name: str) -> bool:
        """注销依赖"""
        pass

# ============================================================================
# 依赖容器实现
# ============================================================================

class DependencyContainer(IDependencyContainer):
    """依赖容器实现"""
    
    def __init__(self, name: str = "default"):
        self.name = name
        self.dependencies: Dict[str, DependencyInfo] = {}
        self.type_mappings: Dict[Type, str] = {}  # 类型到名称的映射
        self.singletons: Dict[str, Any] = {}
        self.scoped_instances: Dict[str, Dict[str, Any]] = {}  # scope_id -> {name -> instance}
        self.contexts: Dict[str, InjectionContext] = {}
        self.logger = logging.getLogger(f"{self.__class__.__name__}[{name}]")
        self._lock = threading.Lock()
        self._resolution_stack: List[str] = []  # 用于检测循环依赖
        
        # 注册容器自身
        self.register_instance("container", self)
        self.register_instance("dependency_container", self)
    
    def register(self, name: str, dependency_type: Type[T], 
                scope: Scope = Scope.TRANSIENT, **kwargs) -> 'DependencyContainer':
        """注册依赖"""
        with self._lock:
            # 分析依赖
            dependencies = self._analyze_dependencies(dependency_type)
            
            dependency_info = DependencyInfo(
                name=name,
                dependency_type=dependency_type,
                scope=scope,
                lifetime_scope=kwargs.get('lifetime_scope', LifetimeScope.APPLICATION),
                dependencies=dependencies,
                metadata=kwargs.get('metadata', {})
            )
            
            self.dependencies[name] = dependency_info
            self.type_mappings[dependency_type] = name
            
            self.logger.debug(f"Registered dependency: {name} -> {dependency_type.__name__} ({scope.value})")
            
            return self
    
    def register_factory(self, name: str, factory: Callable[[], T], 
                        scope: Scope = Scope.TRANSIENT, **kwargs) -> 'DependencyContainer':
        """注册工厂"""
        with self._lock:
            # 尝试从工厂函数推断返回类型
            return_type = self._get_factory_return_type(factory)
            
            dependency_info = DependencyInfo(
                name=name,
                dependency_type=return_type,
                scope=scope,
                lifetime_scope=kwargs.get('lifetime_scope', LifetimeScope.APPLICATION),
                factory=factory,
                metadata=kwargs.get('metadata', {})
            )
            
            self.dependencies[name] = dependency_info
            if return_type:
                self.type_mappings[return_type] = name
            
            self.logger.debug(f"Registered factory: {name} ({scope.value})")
            
            return self
    
    def register_instance(self, name: str, instance: T, **kwargs) -> 'DependencyContainer':
        """注册实例"""
        with self._lock:
            dependency_info = DependencyInfo(
                name=name,
                dependency_type=type(instance),
                scope=Scope.SINGLETON,
                lifetime_scope=kwargs.get('lifetime_scope', LifetimeScope.APPLICATION),
                instance=instance,
                metadata=kwargs.get('metadata', {})
            )
            
            self.dependencies[name] = dependency_info
            self.type_mappings[type(instance)] = name
            self.singletons[name] = instance
            
            self.logger.debug(f"Registered instance: {name} -> {type(instance).__name__}")
            
            return self
    
    def resolve(self, name: str, context: Optional[InjectionContext] = None) -> Any:
        """解析依赖"""
        if name not in self.dependencies:
            raise ValueError(f"Dependency '{name}' not registered")
        
        # 检测循环依赖
        if name in self._resolution_stack:
            cycle = " -> ".join(self._resolution_stack + [name])
            raise ValueError(f"Circular dependency detected: {cycle}")
        
        dependency_info = self.dependencies[name]
        dependency_info.access_count += 1
        
        try:
            self._resolution_stack.append(name)
            
            # 根据作用域获取实例
            if dependency_info.scope == Scope.SINGLETON:
                return self._get_singleton(name, dependency_info, context)
            elif dependency_info.scope == Scope.SCOPED:
                return self._get_scoped_instance(name, dependency_info, context)
            else:  # TRANSIENT or REQUEST
                return self._create_instance(name, dependency_info, context)
        
        finally:
            self._resolution_stack.pop()
    
    def resolve_type(self, dependency_type: Type[T], context: Optional[InjectionContext] = None) -> T:
        """按类型解析依赖"""
        if dependency_type in self.type_mappings:
            name = self.type_mappings[dependency_type]
            return self.resolve(name, context)
        else:
            raise ValueError(f"Type '{dependency_type.__name__}' not registered")
    
    def resolve_all(self, dependency_type: Type[T], context: Optional[InjectionContext] = None) -> List[T]:
        """解析所有指定类型的依赖"""
        instances = []
        for name, info in self.dependencies.items():
            if info.dependency_type == dependency_type or (info.dependency_type and issubclass(info.dependency_type, dependency_type)):
                instances.append(self.resolve(name, context))
        return instances
    
    def is_registered(self, name: str) -> bool:
        """检查是否已注册"""
        return name in self.dependencies
    
    def is_type_registered(self, dependency_type: Type) -> bool:
        """检查类型是否已注册"""
        return dependency_type in self.type_mappings
    
    def unregister(self, name: str) -> bool:
        """注销依赖"""
        with self._lock:
            if name in self.dependencies:
                dependency_info = self.dependencies[name]
                
                # 移除类型映射
                if dependency_info.dependency_type in self.type_mappings:
                    del self.type_mappings[dependency_info.dependency_type]
                
                # 移除单例实例
                if name in self.singletons:
                    del self.singletons[name]
                
                # 移除作用域实例
                for scope_instances in self.scoped_instances.values():
                    if name in scope_instances:
                        del scope_instances[name]
                
                del self.dependencies[name]
                
                self.logger.debug(f"Unregistered dependency: {name}")
                return True
            
            return False
    
    def create_scope(self, scope_name: str, parent_scope: str = None) -> InjectionContext:
        """创建注入作用域"""
        parent_context = self.contexts.get(parent_scope) if parent_scope else None
        context = InjectionContext(scope_name, parent_context)
        self.contexts[scope_name] = context
        
        if scope_name not in self.scoped_instances:
            self.scoped_instances[scope_name] = {}
        
        self.logger.debug(f"Created injection scope: {scope_name}")
        return context
    
    def dispose_scope(self, scope_name: str):
        """销毁注入作用域"""
        if scope_name in self.contexts:
            context = self.contexts[scope_name]
            
            # 清理作用域实例
            if scope_name in self.scoped_instances:
                for instance in self.scoped_instances[scope_name].values():
                    self._dispose_instance(instance)
                del self.scoped_instances[scope_name]
            
            del self.contexts[scope_name]
            
            self.logger.debug(f"Disposed injection scope: {scope_name}")
    
    def _get_singleton(self, name: str, dependency_info: DependencyInfo, context: Optional[InjectionContext]) -> Any:
        """获取单例实例"""
        if name in self.singletons:
            return self.singletons[name]
        
        with self._lock:
            # 双重检查锁定
            if name in self.singletons:
                return self.singletons[name]
            
            instance = self._create_instance(name, dependency_info, context)
            self.singletons[name] = instance
            
            import time
            dependency_info.created_at = time.time()
            
            return instance
    
    def _get_scoped_instance(self, name: str, dependency_info: DependencyInfo, context: Optional[InjectionContext]) -> Any:
        """获取作用域实例"""
        scope_name = context.scope_name if context else "default"
        
        if scope_name not in self.scoped_instances:
            self.scoped_instances[scope_name] = {}
        
        scope_instances = self.scoped_instances[scope_name]
        
        if name in scope_instances:
            return scope_instances[name]
        
        instance = self._create_instance(name, dependency_info, context)
        scope_instances[name] = instance
        
        return instance
    
    def _create_instance(self, name: str, dependency_info: DependencyInfo, context: Optional[InjectionContext]) -> Any:
        """创建实例"""
        if dependency_info.instance is not None:
            return dependency_info.instance
        
        if dependency_info.factory:
            # 使用工厂创建
            return self._invoke_factory(dependency_info.factory, context)
        
        if dependency_info.dependency_type:
            # 使用构造函数创建
            return self._invoke_constructor(dependency_info.dependency_type, dependency_info.dependencies, context)
        
        raise ValueError(f"Cannot create instance for dependency '{name}': no factory or type specified")
    
    def _invoke_factory(self, factory: Callable, context: Optional[InjectionContext]) -> Any:
        """调用工厂函数"""
        # 分析工厂函数的参数
        sig = inspect.signature(factory)
        kwargs = {}
        
        for param_name, param in sig.parameters.items():
            if param.annotation != inspect.Parameter.empty:
                # 尝试按类型解析
                try:
                    kwargs[param_name] = self.resolve_type(param.annotation, context)
                except ValueError:
                    # 尝试按名称解析
                    if self.is_registered(param_name):
                        kwargs[param_name] = self.resolve(param_name, context)
                    elif param.default != inspect.Parameter.empty:
                        # 使用默认值
                        continue
                    else:
                        raise ValueError(f"Cannot resolve parameter '{param_name}' for factory")
            elif self.is_registered(param_name):
                kwargs[param_name] = self.resolve(param_name, context)
            elif param.default == inspect.Parameter.empty:
                raise ValueError(f"Cannot resolve parameter '{param_name}' for factory")
        
        return factory(**kwargs)
    
    def _invoke_constructor(self, dependency_type: Type, dependencies: List[str], context: Optional[InjectionContext]) -> Any:
        """调用构造函数"""
        # 获取构造函数签名
        sig = inspect.signature(dependency_type.__init__)
        kwargs = {}
        
        for param_name, param in sig.parameters.items():
            if param_name == 'self':
                continue
            
            if param.annotation != inspect.Parameter.empty:
                # 尝试按类型解析
                try:
                    kwargs[param_name] = self.resolve_type(param.annotation, context)
                except ValueError:
                    # 尝试按名称解析
                    if self.is_registered(param_name):
                        kwargs[param_name] = self.resolve(param_name, context)
                    elif param.default != inspect.Parameter.empty:
                        # 使用默认值
                        continue
                    else:
                        self.logger.warning(f"Cannot resolve parameter '{param_name}' for {dependency_type.__name__}")
            elif self.is_registered(param_name):
                kwargs[param_name] = self.resolve(param_name, context)
            elif param.default == inspect.Parameter.empty:
                self.logger.warning(f"Cannot resolve parameter '{param_name}' for {dependency_type.__name__}")
        
        return dependency_type(**kwargs)
    
    def _analyze_dependencies(self, dependency_type: Type) -> List[str]:
        """分析依赖关系"""
        dependencies = []
        
        try:
            sig = inspect.signature(dependency_type.__init__)
            for param_name, param in sig.parameters.items():
                if param_name == 'self':
                    continue
                
                if param.annotation != inspect.Parameter.empty:
                    # 检查是否有对应的注册
                    if param.annotation in self.type_mappings:
                        dependencies.append(self.type_mappings[param.annotation])
                    elif self.is_registered(param_name):
                        dependencies.append(param_name)
                elif self.is_registered(param_name):
                    dependencies.append(param_name)
        
        except (ValueError, TypeError):
            # 无法分析依赖关系
            pass
        
        return dependencies
    
    def _get_factory_return_type(self, factory: Callable) -> Optional[Type]:
        """获取工厂函数的返回类型"""
        try:
            sig = inspect.signature(factory)
            if sig.return_annotation != inspect.Signature.empty:
                return sig.return_annotation
        except (ValueError, TypeError):
            pass
        
        return None
    
    def _dispose_instance(self, instance: Any):
        """销毁实例"""
        # 如果实例有dispose方法，调用它
        if hasattr(instance, 'dispose') and callable(getattr(instance, 'dispose')):
            try:
                instance.dispose()
            except Exception as e:
                self.logger.error(f"Error disposing instance: {e}")
        
        # 如果实例有__del__方法，调用它
        elif hasattr(instance, '__del__') and callable(getattr(instance, '__del__')):
            try:
                instance.__del__()
            except Exception as e:
                self.logger.error(f"Error calling __del__ on instance: {e}")
    
    def get_dependency_info(self, name: str) -> Optional[DependencyInfo]:
        """获取依赖信息"""
        return self.dependencies.get(name)
    
    def get_all_dependencies(self) -> Dict[str, DependencyInfo]:
        """获取所有依赖信息"""
        return self.dependencies.copy()
    
    def get_dependency_graph(self) -> Dict[str, List[str]]:
        """获取依赖图"""
        graph = {}
        for name, info in self.dependencies.items():
            graph[name] = info.dependencies.copy()
        return graph
    
    def validate_dependencies(self) -> List[str]:
        """验证依赖关系"""
        errors = []
        
        for name, info in self.dependencies.items():
            # 检查依赖是否存在
            for dep_name in info.dependencies:
                if not self.is_registered(dep_name):
                    errors.append(f"Dependency '{name}' requires '{dep_name}' which is not registered")
            
            # 检查循环依赖
            try:
                self._check_circular_dependency(name, set())
            except ValueError as e:
                errors.append(str(e))
        
        return errors
    
    def _check_circular_dependency(self, name: str, visited: set):
        """检查循环依赖"""
        if name in visited:
            raise ValueError(f"Circular dependency detected involving '{name}'")
        
        visited.add(name)
        
        if name in self.dependencies:
            for dep_name in self.dependencies[name].dependencies:
                self._check_circular_dependency(dep_name, visited.copy())
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        total_dependencies = len(self.dependencies)
        scope_counts = {}
        type_counts = {}
        
        for info in self.dependencies.values():
            # 统计作用域
            scope = info.scope.value
            scope_counts[scope] = scope_counts.get(scope, 0) + 1
            
            # 统计类型
            if info.dependency_type:
                type_name = info.dependency_type.__name__
                type_counts[type_name] = type_counts.get(type_name, 0) + 1
        
        return {
            "total_dependencies": total_dependencies,
            "scope_distribution": scope_counts,
            "type_distribution": type_counts,
            "singleton_count": len(self.singletons),
            "scoped_contexts": len(self.contexts),
            "total_access_count": sum(info.access_count for info in self.dependencies.values())
        }
    
    def clear(self):
        """清空容器"""
        with self._lock:
            # 销毁所有实例
            for instance in self.singletons.values():
                self._dispose_instance(instance)
            
            for scope_instances in self.scoped_instances.values():
                for instance in scope_instances.values():
                    self._dispose_instance(instance)
            
            # 清空所有数据
            self.dependencies.clear()
            self.type_mappings.clear()
            self.singletons.clear()
            self.scoped_instances.clear()
            self.contexts.clear()
            
            # 重新注册容器自身
            self.register_instance("container", self)
            self.register_instance("dependency_container", self)
            
            self.logger.info("Container cleared")

# ============================================================================
# 依赖注入装饰器
# ============================================================================

def injectable(name: str = None, scope: Scope = Scope.TRANSIENT, 
              lifetime_scope: LifetimeScope = LifetimeScope.APPLICATION):
    """可注入装饰器"""
    def decorator(cls: Type[T]) -> Type[T]:
        # 自动注册到全局容器
        dependency_name = name or cls.__name__.lower()
        global_container.register(
            dependency_name, 
            cls, 
            scope=scope, 
            lifetime_scope=lifetime_scope
        )
        
        # 添加元数据
        cls._injectable_name = dependency_name
        cls._injectable_scope = scope
        cls._injectable_lifetime_scope = lifetime_scope
        
        return cls
    
    return decorator

def inject(name: str = None, required: bool = True):
    """注入装饰器"""
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs):
            # 分析函数参数并注入依赖
            sig = inspect.signature(func)
            injected_kwargs = kwargs.copy()
            
            for param_name, param in sig.parameters.items():
                if param_name not in kwargs:
                    dependency_name = name or param_name
                    
                    try:
                        if param.annotation != inspect.Parameter.empty:
                            # 按类型注入
                            injected_kwargs[param_name] = global_container.resolve_type(param.annotation)
                        else:
                            # 按名称注入
                            injected_kwargs[param_name] = global_container.resolve(dependency_name)
                    except ValueError as e:
                        if required and param.default == inspect.Parameter.empty:
                            raise ValueError(f"Cannot inject dependency '{dependency_name}': {e}")
            
            return func(*args, **injected_kwargs)
        
        return wrapper
    
    return decorator

def auto_inject(func: Callable) -> Callable:
    """自动注入装饰器"""
    @wraps(func)
    def wrapper(*args, **kwargs):
        # 自动分析并注入所有可能的依赖
        sig = inspect.signature(func)
        injected_kwargs = kwargs.copy()
        
        for param_name, param in sig.parameters.items():
            if param_name not in kwargs:
                try:
                    if param.annotation != inspect.Parameter.empty:
                        # 尝试按类型注入
                        if global_container.is_type_registered(param.annotation):
                            injected_kwargs[param_name] = global_container.resolve_type(param.annotation)
                    else:
                        # 尝试按名称注入
                        if global_container.is_registered(param_name):
                            injected_kwargs[param_name] = global_container.resolve(param_name)
                except ValueError:
                    # 忽略无法注入的依赖
                    pass
        
        return func(*args, **injected_kwargs)
    
    return wrapper

# ============================================================================
# 上下文管理器
# ============================================================================

@contextmanager
def injection_scope(container: DependencyContainer, scope_name: str, parent_scope: str = None):
    """注入作用域上下文管理器"""
    context = container.create_scope(scope_name, parent_scope)
    try:
        yield context
    finally:
        container.dispose_scope(scope_name)

# ============================================================================
# 全局容器实例
# ============================================================================

# 全局依赖容器
global_container = DependencyContainer("global")

# 便捷函数
def register(name: str, dependency_type: Type[T], scope: Scope = Scope.TRANSIENT, **kwargs) -> DependencyContainer:
    """注册依赖的便捷函数"""
    return global_container.register(name, dependency_type, scope, **kwargs)

def register_factory(name: str, factory: Callable[[], T], scope: Scope = Scope.TRANSIENT, **kwargs) -> DependencyContainer:
    """注册工厂的便捷函数"""
    return global_container.register_factory(name, factory, scope, **kwargs)

def register_instance(name: str, instance: T, **kwargs) -> DependencyContainer:
    """注册实例的便捷函数"""
    return global_container.register_instance(name, instance, **kwargs)

def resolve(name: str, context: Optional[InjectionContext] = None) -> Any:
    """解析依赖的便捷函数"""
    return global_container.resolve(name, context)

def resolve_type(dependency_type: Type[T], context: Optional[InjectionContext] = None) -> T:
    """按类型解析依赖的便捷函数"""
    return global_container.resolve_type(dependency_type, context)

def create_scope(scope_name: str, parent_scope: str = None) -> InjectionContext:
    """创建作用域的便捷函数"""
    return global_container.create_scope(scope_name, parent_scope)

# ============================================================================
# 预定义服务注册
# ============================================================================

def setup_default_services():
    """设置默认服务"""
    from .state_manager import global_state_manager
    from .event_system import global_event_bus
    from ..config.app_config import AppConfig
    
    # 注册核心服务
    global_container.register_instance("state_manager", global_state_manager)
    global_container.register_instance("event_bus", global_event_bus)
    
    # 注册配置服务
    global_container.register_factory(
        "app_config",
        lambda: AppConfig(),
        scope=Scope.SINGLETON
    )
    
    # 注册日志服务
    global_container.register_factory(
        "logger",
        lambda: logging.getLogger("app"),
        scope=Scope.SINGLETON
    )

# 自动设置默认服务
setup_default_services()