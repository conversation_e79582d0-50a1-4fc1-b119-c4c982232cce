from typing import Dict, Any, Callable, Optional, List
from dataclasses import dataclass, field
from enum import Enum
from typing import Callable, Dict, List, Optional, Any, Union
import streamlit as st
import re
import urllib.parse
from datetime import datetime
import json
from pathlib import Path

class RouteType(Enum):
    """路由类型枚举"""
    TAB = "tab"
    PAGE = "page"
    MODAL = "modal"
    SIDEBAR = "sidebar"

@dataclass
class RouteGuard:
    """路由守卫配置"""
    name: str
    check_function: Callable[[], bool]
    redirect_route: Optional[str] = None
    error_message: str = "访问被拒绝"

@dataclass
class RouteMiddleware:
    """路由中间件配置"""
    name: str
    before_function: Optional[Callable] = None
    after_function: Optional[Callable] = None
    order: int = 0

@dataclass
class RouteMeta:
    """路由元数据"""
    seo_title: Optional[str] = None
    seo_description: Optional[str] = None
    seo_keywords: List[str] = field(default_factory=list)
    cache_duration: int = 0  # 缓存时长（秒）
    last_modified: Optional[datetime] = None
    access_count: int = 0
    custom_data: Dict[str, Any] = field(default_factory=dict)

@dataclass
class Route:
    """路由配置类"""
    name: str
    path: str
    title: str
    icon: str
    component: Callable
    route_type: RouteType = RouteType.TAB
    requires_auth: bool = False
    description: str = ""
    keywords: List[str] = field(default_factory=list)
    parent: Optional[str] = None
    order: int = 0
    visible: bool = True
    guards: List[RouteGuard] = field(default_factory=list)
    middleware: List[RouteMiddleware] = field(default_factory=list)
    meta: RouteMeta = field(default_factory=RouteMeta)
    layout: Optional[str] = None  # 布局模板
    lazy_load: bool = False  # 懒加载
    
    def __post_init__(self):
        if not self.keywords:
            self.keywords = []
        if not self.meta:
            self.meta = RouteMeta()
        # 自动生成SEO标题
        if not self.meta.seo_title:
            self.meta.seo_title = self.title
        if not self.meta.seo_description:
            self.meta.seo_description = self.description
    
    def matches_path(self, path: str) -> bool:
        """检查路径是否匹配此路由"""
        pattern = self._path_to_regex()
        return bool(re.match(pattern, path))
    
    def extract_params(self, path: str) -> Dict[str, str]:
        """从路径中提取参数"""
        pattern = self._path_to_regex()
        match = re.match(pattern, path)
        if match:
            return match.groupdict()
        return {}
    
    def _path_to_regex(self) -> str:
        """将路由路径转换为正则表达式"""
        # 将 {param} 转换为命名捕获组
        pattern = re.sub(r'\{(\w+)\}', r'(?P<\1>[^/]+)', self.path)
        # 确保完全匹配
        return f'^{pattern}$'
    
    def build_url(self, **params) -> str:
        """构建URL"""
        url = self.path
        for key, value in params.items():
            url = url.replace(f'{{{key}}}', str(value))
        return url
    
    def can_access(self) -> bool:
        """检查是否可以访问此路由"""
        # 检查认证
        if self.requires_auth and not st.session_state.get("authenticated", False):
            return False
        
        # 检查路由守卫
        for guard in self.guards:
            if not guard.check_function():
                return False
        
        return True

class Router:
    """路由管理器 - 类似静态网站的路由系统"""
    
    def __init__(self):
        self.routes: Dict[str, Route] = {}
        self.current_route: Optional[str] = None
        self.current_path: Optional[str] = None
        self.current_params: Dict[str, str] = {}
        self.route_history: List[Dict[str, Any]] = []
        self.breadcrumbs: List[Dict[str, str]] = []
        self.global_middleware: List[RouteMiddleware] = []
        self.global_guards: List[RouteGuard] = []
        self.route_cache: Dict[str, Any] = {}
        self.layouts: Dict[str, Callable] = {}
        self.error_handlers: Dict[int, Callable] = {}
        
        # 初始化会话状态
        self._init_session_state()
        
        # 注册默认路由
        self._register_default_routes()
        
        # 注册默认错误处理器
        self._register_default_error_handlers()
    
    def _init_session_state(self):
        """初始化会话状态"""
        if 'router_initialized' not in st.session_state:
            st.session_state.router_initialized = True
            st.session_state.route_access_log = []
            st.session_state.route_cache = {}
            st.session_state.current_url = '/'
    
    def _register_default_error_handlers(self):
        """注册默认错误处理器"""
        def handle_404():
            st.error("🔍 页面未找到")
            st.markdown("您访问的页面不存在，请检查URL或返回首页。")
            if st.button("返回首页"):
                self.navigate_to("home")
        
        def handle_403():
            st.error("🚫 访问被拒绝")
            st.markdown("您没有权限访问此页面。")
            if st.button("返回首页"):
                self.navigate_to("home")
        
        self.error_handlers[404] = handle_404
        self.error_handlers[403] = handle_403
    
    def _register_default_routes(self):
        """注册默认路由"""
        # 主要Tab路由
        self.register_route(Route(
            name="home",
            path="/",
            title="首页",
            icon="🏠",
            component=None,  # 将在实际使用时设置
            route_type=RouteType.TAB,
            description="市场概览和重要信息",
            keywords=["首页", "概览", "市场", "home"],
            order=1
        ))
        
        self.register_route(Route(
            name="us_stock",
            path="/us-stock",
            title="美股",
            icon="🇺🇸",
            component=None,
            route_type=RouteType.TAB,
            description="美股市场分析和监控",
            keywords=["美股", "美国", "股票", "us", "stock"],
            order=2
        ))
        
        self.register_route(Route(
            name="hk_stock",
            path="/hk-stock",
            title="港股",
            icon="🇭🇰",
            component=None,
            route_type=RouteType.TAB,
            description="港股市场分析和监控",
            keywords=["港股", "香港", "股票", "hk", "hong kong"],
            order=3
        ))
        
        self.register_route(Route(
            name="crypto",
            path="/crypto",
            title="币链",
            icon="₿",
            component=None,
            route_type=RouteType.TAB,
            description="加密货币和区块链分析",
            keywords=["币链", "加密货币", "区块链", "crypto", "blockchain"],
            order=4
        ))
        
        # 子页面路由
        self.register_route(Route(
            name="stock_detail",
            path="/stock/{symbol}",
            title="股票详情",
            icon="📊",
            component=None,
            route_type=RouteType.PAGE,
            description="个股详细分析页面",
            keywords=["股票", "详情", "分析", "detail"],
            visible=False
        ))
        
        self.register_route(Route(
            name="portfolio",
            path="/portfolio",
            title="投资组合",
            icon="💼",
            component=None,
            route_type=RouteType.PAGE,
            description="投资组合管理",
            keywords=["投资组合", "portfolio", "管理"],
            requires_auth=True,
            order=5
        ))
        
        self.register_route(Route(
            name="settings",
            path="/settings",
            title="设置",
            icon="⚙️",
            component=None,
            route_type=RouteType.PAGE,
            description="应用设置和配置",
            keywords=["设置", "配置", "settings"],
            order=6
        ))
    
    def register_route(self, route: Route):
        """注册路由"""
        self.routes[route.name] = route
        # 记录注册时间
        route.meta.last_modified = datetime.now()
    
    def unregister_route(self, name: str):
        """注销路由"""
        if name in self.routes:
            del self.routes[name]
            # 清理相关缓存
            self._clear_route_cache(name)
    
    def register_layout(self, name: str, layout_func: Callable):
        """注册布局模板"""
        self.layouts[name] = layout_func
    
    def register_global_middleware(self, middleware: RouteMiddleware):
        """注册全局中间件"""
        self.global_middleware.append(middleware)
        # 按优先级排序
        self.global_middleware.sort(key=lambda x: x.order)
    
    def register_global_guard(self, guard: RouteGuard):
        """注册全局路由守卫"""
        self.global_guards.append(guard)
    
    def register_error_handler(self, status_code: int, handler: Callable):
        """注册错误处理器"""
        self.error_handlers[status_code] = handler
    
    def resolve_route_by_path(self, path: str) -> Optional[Route]:
        """根据路径解析路由"""
        for route in self.routes.values():
            if route.matches_path(path):
                return route
        return None
    
    def parse_url(self, url: str) -> Dict[str, Any]:
        """解析URL"""
        parsed = urllib.parse.urlparse(url)
        path = parsed.path
        query_params = urllib.parse.parse_qs(parsed.query)
        
        # 查找匹配的路由
        route = self.resolve_route_by_path(path)
        if route:
            path_params = route.extract_params(path)
            return {
                'route': route,
                'path': path,
                'path_params': path_params,
                'query_params': query_params,
                'fragment': parsed.fragment
            }
        
        return {
            'route': None,
            'path': path,
            'path_params': {},
            'query_params': query_params,
            'fragment': parsed.fragment
        }
    
    def _clear_route_cache(self, route_name: str = None):
        """清理路由缓存"""
        if route_name:
            # 清理特定路由的缓存
            keys_to_remove = [k for k in self.route_cache.keys() if k.startswith(f"{route_name}_")]
            for key in keys_to_remove:
                del self.route_cache[key]
        else:
            # 清理所有缓存
            self.route_cache.clear()
    
    def _execute_middleware(self, middleware_list: List[RouteMiddleware], phase: str, context: Dict[str, Any]):
        """执行中间件"""
        for middleware in sorted(middleware_list, key=lambda x: x.order):
            try:
                if phase == 'before' and middleware.before_function:
                    middleware.before_function(context)
                elif phase == 'after' and middleware.after_function:
                    middleware.after_function(context)
            except Exception as e:
                st.error(f"中间件 '{middleware.name}' 执行失败: {str(e)}")
    
    def _check_guards(self, guards: List[RouteGuard]) -> bool:
        """检查路由守卫"""
        for guard in guards:
            try:
                if not guard.check_function():
                    if guard.redirect_route:
                        self.navigate_to(guard.redirect_route)
                    else:
                        st.error(guard.error_message)
                    return False
            except Exception as e:
                st.error(f"路由守卫 '{guard.name}' 检查失败: {str(e)}")
                return False
        return True
    
    def get_route(self, name: str) -> Optional[Route]:
        """获取路由"""
        return self.routes.get(name)
    
    def get_routes_by_type(self, route_type: RouteType) -> List[Route]:
        """按类型获取路由"""
        return [route for route in self.routes.values() if route.route_type == route_type]
    
    def get_visible_routes(self) -> List[Route]:
        """获取可见路由"""
        return [route for route in self.routes.values() if route.visible]
    
    def get_tab_routes(self) -> List[Route]:
        """获取Tab路由（按顺序排序）"""
        tab_routes = self.get_routes_by_type(RouteType.TAB)
        return sorted([route for route in tab_routes if route.visible], key=lambda x: x.order)
    
    def navigate_to(self, route_name: str, **params):
        """导航到指定路由"""
        if route_name not in self.routes:
            if 404 in self.error_handlers:
                self.error_handlers[404]()
            else:
                st.error(f"路由 '{route_name}' 不存在")
            return False
        
        route = self.routes[route_name]
        
        # 构建导航上下文
        context = {
            'route': route,
            'params': params,
            'current_route': self.current_route,
            'timestamp': datetime.now()
        }
        
        # 执行前置中间件
        self._execute_middleware(self.global_middleware, 'before', context)
        self._execute_middleware(route.middleware, 'before', context)
        
        # 检查全局守卫
        if not self._check_guards(self.global_guards):
            return False
        
        # 检查路由守卫
        if not self._check_guards(route.guards):
            return False
        
        # 检查路由访问权限
        if not route.can_access():
            if 403 in self.error_handlers:
                self.error_handlers[403]()
            else:
                st.error("您没有权限访问此页面")
            return False
        
        # 记录历史
        if self.current_route:
            self.route_history.append({
                'route_name': self.current_route,
                'path': self.current_path,
                'params': self.current_params.copy(),
                'timestamp': datetime.now(),
                'duration': (datetime.now() - context['timestamp']).total_seconds()
            })
        
        # 更新当前路由信息
        self.current_route = route_name
        self.current_path = route.build_url(**params)
        self.current_params = params
        
        # 更新会话状态
        st.session_state.current_url = self.current_path
        
        # 记录访问日志
        access_log = {
            'route_name': route_name,
            'path': self.current_path,
            'params': params,
            'timestamp': datetime.now(),
            'user_agent': st.session_state.get('user_agent', 'unknown')
        }
        st.session_state.route_access_log.append(access_log)
        
        # 更新路由访问统计
        route.meta.access_count += 1
        
        # 更新面包屑导航
        self._update_breadcrumbs(route, **params)
        
        # 存储路由参数
        if params:
            st.session_state[f"route_params_{route_name}"] = params
        
        # 执行后置中间件
        self._execute_middleware(route.middleware, 'after', context)
        self._execute_middleware(self.global_middleware, 'after', context)
        
        return True
    
    def navigate_to_path(self, path: str, **query_params):
        """根据路径导航"""
        url_info = self.parse_url(path)
        if url_info['route']:
            # 合并路径参数和查询参数
            all_params = {**url_info['path_params'], **query_params}
            return self.navigate_to(url_info['route'].name, **all_params)
        else:
            if 404 in self.error_handlers:
                self.error_handlers[404]()
            else:
                st.error(f"路径 '{path}' 未找到对应路由")
            return False
    
    def get_current_route(self) -> Optional[Route]:
        """获取当前路由"""
        if self.current_route:
            return self.routes.get(self.current_route)
        return None
    
    def get_route_params(self, route_name: str = None) -> Dict[str, Any]:
        """获取路由参数"""
        if route_name is None:
            route_name = self.current_route
        
        if route_name:
            return st.session_state.get(f"route_params_{route_name}", {})
        return {}
    
    def go_back(self):
        """返回上一页"""
        if self.route_history:
            previous_entry = self.route_history.pop()
            return self.navigate_to(
                previous_entry['route_name'], 
                **previous_entry['params']
            )
        return False
    
    def can_go_back(self) -> bool:
        """检查是否可以返回上一页"""
        return len(self.route_history) > 0
    
    def get_history_length(self) -> int:
        """获取历史记录长度"""
        return len(self.route_history)
    
    def clear_history(self):
        """清空历史记录"""
        self.route_history.clear()
    
    def get_cached_content(self, cache_key: str) -> Any:
        """获取缓存内容"""
        return self.route_cache.get(cache_key)
    
    def set_cached_content(self, cache_key: str, content: Any, ttl: int = 300):
        """设置缓存内容"""
        self.route_cache[cache_key] = {
            'content': content,
            'timestamp': datetime.now(),
            'ttl': ttl
        }
    
    def is_cache_valid(self, cache_key: str) -> bool:
        """检查缓存是否有效"""
        if cache_key not in self.route_cache:
            return False
        
        cache_entry = self.route_cache[cache_key]
        elapsed = (datetime.now() - cache_entry['timestamp']).total_seconds()
        return elapsed < cache_entry['ttl']
    
    def render_with_layout(self, route: Route, content_func: Callable):
        """使用布局渲染内容"""
        if route.layout and route.layout in self.layouts:
            layout_func = self.layouts[route.layout]
            layout_func(content_func, route)
        else:
            # 使用默认布局
            self._render_default_layout(content_func, route)
    
    def _render_default_layout(self, content_func: Callable, route: Route):
        """渲染默认布局"""
        # 页面标题
        if route.meta.seo_title:
            st.title(f"{route.icon} {route.meta.seo_title}")
        
        # 面包屑导航
        self.render_breadcrumbs()
        
        # 页面内容
        content_func()
        
        # 页面元信息
        if route.meta.seo_description:
            st.caption(route.meta.seo_description)
    
    def search_routes(self, query: str) -> List[Route]:
        """搜索路由"""
        query = query.lower()
        results = []
        
        for route in self.routes.values():
            if not route.visible:
                continue
            
            # 搜索标题
            if query in route.title.lower():
                results.append(route)
                continue
            
            # 搜索描述
            if query in route.description.lower():
                results.append(route)
                continue
            
            # 搜索关键词
            for keyword in route.keywords:
                if query in keyword.lower():
                    results.append(route)
                    break
        
        return results
    
    def _update_breadcrumbs(self, route: Route, **params):
        """更新面包屑导航"""
        self.breadcrumbs = []
        
        # 添加首页
        if route.name != "home":
            home_route = self.routes.get("home")
            if home_route:
                self.breadcrumbs.append({
                    "name": home_route.name,
                    "title": home_route.title,
                    "path": home_route.path
                })
        
        # 添加父级路由
        if route.parent:
            parent_route = self.routes.get(route.parent)
            if parent_route:
                self.breadcrumbs.append({
                    "name": parent_route.name,
                    "title": parent_route.title,
                    "path": parent_route.path
                })
        
        # 添加当前路由
        current_title = route.title
        if params and "symbol" in params:
            current_title = f"{route.title} - {params['symbol']}"
        
        self.breadcrumbs.append({
            "name": route.name,
            "title": current_title,
            "path": route.path
        })
    
    def get_breadcrumbs(self) -> List[Dict[str, str]]:
        """获取面包屑导航"""
        return self.breadcrumbs
    
    def render_breadcrumbs(self):
        """渲染面包屑导航"""
        if len(self.breadcrumbs) <= 1:
            return
        
        breadcrumb_items = []
        for i, crumb in enumerate(self.breadcrumbs):
            if i == len(self.breadcrumbs) - 1:
                # 当前页面，不可点击
                breadcrumb_items.append(crumb["title"])
            else:
                # 可点击的链接
                breadcrumb_items.append(f"[{crumb['title']}](#{crumb['name']})")
        
        st.markdown(" > ".join(breadcrumb_items))
    
    def render_navigation_menu(self):
        """渲染导航菜单"""
        tab_routes = self.get_tab_routes()
        
        if not tab_routes:
            return
        
        # 创建Tab标签
        tab_names = [f"{route.icon} {route.title}" for route in tab_routes]
        
        # 获取当前选中的Tab索引
        current_index = 0
        if self.current_route:
            for i, route in enumerate(tab_routes):
                if route.name == self.current_route:
                    current_index = i
                    break
        
        # 渲染Tab
        selected_tab = st.tabs(tab_names)
        
        # 更新当前路由
        if selected_tab:
            selected_route = tab_routes[current_index]
            self.navigate_to(selected_route.name)
            
            # 渲染选中Tab的内容
            with selected_tab[current_index]:
                if selected_route.component:
                    selected_route.component()
    
    def render_sidebar_navigation(self):
        """渲染侧边栏导航"""
        with st.sidebar:
            st.markdown("### 📍 导航")
            
            # 主要Tab导航
            tab_routes = self.get_tab_routes()
            for route in tab_routes:
                if st.button(
                    f"{route.icon} {route.title}",
                    key=f"nav_{route.name}",
                    help=route.description,
                    use_container_width=True
                ):
                    self.navigate_to(route.name)
            
            st.divider()
            
            # 其他页面导航
            page_routes = [r for r in self.get_routes_by_type(RouteType.PAGE) if r.visible]
            if page_routes:
                st.markdown("### 🔗 其他页面")
                for route in sorted(page_routes, key=lambda x: x.order):
                    if st.button(
                        f"{route.icon} {route.title}",
                        key=f"page_nav_{route.name}",
                        help=route.description,
                        use_container_width=True
                    ):
                        self.navigate_to(route.name)
    
    def render_search_box(self):
        """渲染搜索框"""
        search_query = st.text_input(
            "🔍 搜索页面",
            placeholder="输入关键词搜索页面...",
            key="route_search"
        )
        
        if search_query:
            results = self.search_routes(search_query)
            if results:
                st.markdown("**搜索结果:**")
                for route in results[:5]:  # 最多显示5个结果
                    if st.button(
                        f"{route.icon} {route.title} - {route.description}",
                        key=f"search_result_{route.name}",
                        use_container_width=True
                    ):
                        self.navigate_to(route.name)
                        st.rerun()
            else:
                st.info("未找到相关页面")
    
    def _is_authenticated(self) -> bool:
        """检查用户是否已认证"""
        # 这里可以实现实际的认证逻辑
        return st.session_state.get("authenticated", False)
    
    def get_route_analytics(self) -> Dict[str, Any]:
        """获取路由访问统计"""
        access_log = st.session_state.get('route_access_log', [])
        
        # 计算访问统计
        route_access_counts = {}
        for log_entry in access_log:
            route_name = log_entry['route_name']
            route_access_counts[route_name] = route_access_counts.get(route_name, 0) + 1
        
        # 最受欢迎的路由
        popular_routes = sorted(route_access_counts.items(), key=lambda x: x[1], reverse=True)[:5]
        
        analytics = {
            "total_routes": len(self.routes),
            "visible_routes": len(self.get_visible_routes()),
            "tab_routes": len(self.get_tab_routes()),
            "page_routes": len(self.get_routes_by_type(RouteType.PAGE)),
            "modal_routes": len(self.get_routes_by_type(RouteType.MODAL)),
            "sidebar_routes": len(self.get_routes_by_type(RouteType.SIDEBAR)),
            "protected_routes": len([r for r in self.routes.values() if r.requires_auth]),
            "cached_routes": len([r for r in self.routes.values() if r.meta.cache_duration > 0]),
            "lazy_load_routes": len([r for r in self.routes.values() if r.lazy_load]),
            "current_route": self.current_route,
            "current_path": self.current_path,
            "route_history_length": len(self.route_history),
            "total_access_count": len(access_log),
            "popular_routes": popular_routes,
            "cache_hit_rate": self._calculate_cache_hit_rate(),
            "global_middleware_count": len(self.global_middleware),
            "global_guards_count": len(self.global_guards),
            "registered_layouts": list(self.layouts.keys()),
            "error_handlers": list(self.error_handlers.keys())
        }
        return analytics
    
    def _calculate_cache_hit_rate(self) -> float:
        """计算缓存命中率"""
        if not self.route_cache:
            return 0.0
        
        valid_cache_count = sum(1 for key in self.route_cache.keys() if self.is_cache_valid(key))
        return valid_cache_count / len(self.route_cache) if self.route_cache else 0.0
    
    def get_performance_metrics(self) -> Dict[str, Any]:
        """获取性能指标"""
        access_log = st.session_state.get('route_access_log', [])
        
        if not access_log:
            return {"message": "暂无访问数据"}
        
        # 计算平均响应时间（基于历史记录中的duration）
        durations = [entry.get('duration', 0) for entry in self.route_history if 'duration' in entry]
        avg_duration = sum(durations) / len(durations) if durations else 0
        
        # 最近访问的路由
        recent_routes = access_log[-10:] if len(access_log) >= 10 else access_log
        
        return {
            "average_navigation_time": avg_duration,
            "total_navigations": len(self.route_history),
            "recent_routes": [entry['route_name'] for entry in recent_routes],
            "cache_size": len(self.route_cache),
            "active_middleware": len(self.global_middleware),
            "active_guards": len(self.global_guards)
        }
    
    def render_navigation_menu(self):
        """渲染导航菜单"""
        tab_routes = self.get_routes_by_type(RouteType.TAB)
        if tab_routes:
            tab_names = [route.title for route in tab_routes]
            route_names = [route.name for route in tab_routes]
            
            # 获取当前选中的tab索引
            current_index = 0
            if self.current_route:
                try:
                    current_index = route_names.index(self.current_route)
                except ValueError:
                    pass
            
            # 创建tabs
            selected_tab = st.tabs(tab_names)
            
            # 更新当前路由
            if selected_tab:
                selected_route = route_names[current_index]
                if selected_route != self.current_route:
                    self.navigate_to(selected_route)
    
    def render_sidebar_navigation(self):
        """渲染侧边栏导航"""
        with st.sidebar:
            st.title("📱 导航菜单")
            
            # 主要页面
            st.subheader("📊 主要功能")
            tab_routes = self.get_routes_by_type(RouteType.TAB)
            for route in tab_routes:
                if st.button(f"{route.icon} {route.title}", key=f"nav_{route.name}"):
                    self.navigate_to(route.name)
            
            st.divider()
            
            # 页面路由
            page_routes = self.get_routes_by_type(RouteType.PAGE)
            if page_routes:
                st.subheader("📄 页面")
                for route in page_routes:
                    if st.button(f"{route.icon} {route.title}", key=f"page_{route.name}"):
                        self.navigate_to(route.name)
                
                st.divider()
            
            # 返回按钮
            if self.can_go_back():
                if st.button("⬅️ 返回", key="nav_back"):
                    self.go_back()
            
            # 搜索功能
            st.subheader("🔍 搜索")
            search_query = st.text_input("搜索路由", key="nav_search")
            if search_query:
                results = self.search_routes(search_query)
                for route in results[:5]:  # 显示前5个结果
                    if st.button(f"{route.icon} {route.title}", key=f"search_{route.name}"):
                        self.navigate_to(route.name)
    
    def render_breadcrumbs(self):
        """渲染面包屑导航"""
        breadcrumbs = self.get_breadcrumbs()
        if len(breadcrumbs) > 1:
            breadcrumb_text = " > ".join([f"{item['icon']} {item['title']}" for item in breadcrumbs])
            st.caption(f"📍 {breadcrumb_text}")
    
    def render_search_box(self):
        """渲染搜索框"""
        col1, col2 = st.columns([3, 1])
        
        with col1:
            search_query = st.text_input(
                "🔍 搜索页面", 
                placeholder="输入页面名称或关键词...",
                key="global_search"
            )
        
        with col2:
            search_button = st.button("搜索", key="search_btn")
        
        if search_query and (search_button or search_query):
            results = self.search_routes(search_query)
            
            if results:
                st.write("**搜索结果:**")
                for route in results[:10]:  # 显示前10个结果
                    col1, col2, col3 = st.columns([1, 3, 1])
                    
                    with col1:
                        st.write(route.icon)
                    
                    with col2:
                        st.write(f"**{route.title}**")
                        if route.description:
                            st.caption(route.description)
                    
                    with col3:
                        if st.button("访问", key=f"visit_{route.name}"):
                            self.navigate_to(route.name)
            else:
                st.info("未找到匹配的页面")
    
    def render_route_analytics_dashboard(self):
        """渲染路由分析仪表板"""
        st.subheader("📊 路由分析")
        
        analytics = self.get_route_analytics()
        performance = self.get_performance_metrics()
        
        # 基础统计
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            st.metric("总路由数", analytics['total_routes'])
        
        with col2:
            st.metric("总访问次数", analytics['total_access_count'])
        
        with col3:
            st.metric("缓存命中率", f"{analytics['cache_hit_rate']:.1%}")
        
        with col4:
            st.metric("平均导航时间", f"{performance['average_navigation_time']:.2f}ms")
        
        # 最受欢迎的路由
        if analytics['popular_routes']:
            st.subheader("🔥 最受欢迎的路由")
            for route_name, count in analytics['popular_routes'][:5]:
                route = self.get_route(route_name)
                if route:
                    col1, col2 = st.columns([3, 1])
                    with col1:
                        st.write(f"{route.icon} {route.title}")
                    with col2:
                        st.write(f"{count} 次访问")
        
        # 路由类型分布
        st.subheader("📈 路由类型分布")
        type_data = {
            'Tab路由': analytics['tab_routes'],
            '页面路由': analytics['page_routes'],
            '模态路由': analytics['modal_routes'],
            '侧边栏路由': analytics['sidebar_routes']
        }
        
        for route_type, count in type_data.items():
            st.write(f"- {route_type}: {count}")
    
    def render_performance_dashboard(self):
        """渲染性能仪表板"""
        st.subheader("⚡ 性能监控")
        
        performance = self.get_performance_metrics()
        
        # 性能指标
        col1, col2, col3 = st.columns(3)
        
        with col1:
            st.metric(
                "总导航次数", 
                performance['total_navigations'],
                delta=None
            )
        
        with col2:
            st.metric(
                "平均导航时间", 
                f"{performance['average_navigation_time']:.2f}ms",
                delta=None
            )
        
        with col3:
            st.metric(
                "缓存大小", 
                performance['cache_size'],
                delta=None
            )
        
        # 最近访问的路由
        if performance['recent_routes']:
            st.subheader("🕒 最近访问")
            for route_name in performance['recent_routes'][-5:]:
                route = self.get_route(route_name)
                if route:
                    st.write(f"- {route.icon} {route.title}")
        
        # 中间件和守卫状态
        col1, col2 = st.columns(2)
        
        with col1:
            st.metric("活跃中间件", performance['active_middleware'])
        
        with col2:
            st.metric("活跃守卫", performance['active_guards'])
    
    def export_sitemap(self) -> Dict[str, Any]:
        """导出站点地图"""
        sitemap = {
            "routes": [],
            "generated_at": str(st.session_state.get("current_time", "unknown")),
            "total_routes": len(self.routes)
        }
        
        for route in self.routes.values():
            if route.visible:
                sitemap["routes"].append({
                    "name": route.name,
                    "path": route.path,
                    "title": route.title,
                    "description": route.description,
                    "type": route.route_type.value,
                    "requires_auth": route.requires_auth,
                    "keywords": route.keywords
                })
        
        return sitemap

# 全局路由实例
app_router = Router()

def get_router() -> Router:
    """获取路由实例"""
    return app_router

def register_tab_component(route_name: str, component: Callable):
    """注册Tab组件"""
    route = app_router.get_route(route_name)
    if route:
        route.component = component
    else:
        st.error(f"路由 '{route_name}' 不存在")

def navigate_to(route_name: str, **params):
    """快捷导航函数"""
    return app_router.navigate_to(route_name, **params)

def navigate_to_path(path: str, **query_params):
    """根据路径导航"""
    return app_router.navigate_to_path(path, **query_params)

def get_current_route_name() -> Optional[str]:
    """获取当前路由名称"""
    return app_router.current_route

def get_current_route() -> Optional[Route]:
    """获取当前路由对象"""
    return app_router.get_current_route()

def get_current_params() -> Dict[str, str]:
    """获取当前路由参数"""
    return app_router.current_params

def go_back() -> bool:
    """返回上一页"""
    return app_router.go_back()

def can_go_back() -> bool:
    """检查是否可以返回"""
    return app_router.can_go_back()

# 装饰器
def route(name: str, path: str, **route_kwargs):
    """路由装饰器"""
    def decorator(func: Callable):
        route_obj = Route(
            name=name,
            path=path,
            title=route_kwargs.get('title', name.title()),
            icon=route_kwargs.get('icon', '📄'),
            component=func,
            **{k: v for k, v in route_kwargs.items() if k not in ['title', 'icon']}
        )
        app_router.register_route(route_obj)
        return func
    return decorator

def guard(check_func: Callable[[], bool], error_message: str = "访问被拒绝", redirect_route: str = None):
    """路由守卫装饰器"""
    def decorator(func: Callable):
        # 这里需要在运行时添加守卫到对应的路由
        # 可以通过函数名或其他方式关联
        guard_obj = RouteGuard(
            name=f"guard_{func.__name__}",
            check_function=check_func,
            error_message=error_message,
            redirect_route=redirect_route
        )
        # 将守卫信息存储在函数上，稍后注册路由时使用
        if not hasattr(func, '_route_guards'):
            func._route_guards = []
        func._route_guards.append(guard_obj)
        return func
    return decorator

def middleware(before_func: Callable = None, after_func: Callable = None, order: int = 0):
    """路由中间件装饰器"""
    def decorator(func: Callable):
        middleware_obj = RouteMiddleware(
            name=f"middleware_{func.__name__}",
            before_function=before_func,
            after_function=after_func,
            order=order
        )
        # 将中间件信息存储在函数上
        if not hasattr(func, '_route_middleware'):
            func._route_middleware = []
        func._route_middleware.append(middleware_obj)
        return func
    return decorator

def cached_route(ttl: int = 300):
    """缓存路由装饰器"""
    def decorator(func: Callable):
        def wrapper(*args, **kwargs):
            cache_key = f"route_{func.__name__}_{hash(str(kwargs))}"
            
            # 检查缓存
            if app_router.is_cache_valid(cache_key):
                cached_content = app_router.get_cached_content(cache_key)
                if cached_content:
                    return cached_content['content']
            
            # 执行函数并缓存结果
            result = func(*args, **kwargs)
            app_router.set_cached_content(cache_key, result, ttl)
            return result
        
        return wrapper
    return decorator

def require_auth(func: Callable):
    """需要认证的路由装饰器"""
    def wrapper(*args, **kwargs):
        if not st.session_state.get('authenticated', False):
            st.error("🔒 需要登录才能访问此页面")
            if st.button("前往登录"):
                navigate_to("login")
            return
        return func(*args, **kwargs)
    return wrapper

# 布局装饰器
def layout(layout_name: str):
    """布局装饰器"""
    def decorator(func: Callable):
        func._layout = layout_name
        return func
    return decorator

# 便捷函数
def register_layout(name: str, layout_func: Callable):
    """注册布局"""
    app_router.register_layout(name, layout_func)

def register_global_middleware(middleware: RouteMiddleware):
    """注册全局中间件"""
    app_router.register_global_middleware(middleware)

def register_global_guard(guard: RouteGuard):
    """注册全局守卫"""
    app_router.register_global_guard(guard)

def register_error_handler(status_code: int, handler: Callable):
    """注册错误处理器"""
    app_router.register_error_handler(status_code, handler)

def get_route_analytics() -> Dict[str, Any]:
    """获取路由分析数据"""
    return app_router.get_route_analytics()

def get_performance_metrics() -> Dict[str, Any]:
    """获取性能指标"""
    return app_router.get_performance_metrics()

def export_sitemap() -> Dict[str, Any]:
    """导出站点地图"""
    return app_router.export_sitemap()

def clear_cache():
    """清理所有缓存"""
    app_router._clear_route_cache()

def render_navigation():
    """渲染导航菜单"""
    app_router.render_navigation_menu()

def render_sidebar_navigation():
    """渲染侧边栏导航"""
    app_router.render_sidebar_navigation()

def render_breadcrumbs():
    """渲染面包屑导航"""
    app_router.render_breadcrumbs()

def render_search_box():
    """渲染搜索框"""
    app_router.render_search_box()