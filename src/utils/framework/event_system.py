from typing import Dict, Any, List, Optional, Callable, Union
from dataclasses import dataclass, field
from datetime import datetime
from abc import ABC, abstractmethod
from functools import wraps
import asyncio
import threading
import logging
import weakref
from enum import Enum
import json

# ============================================================================
# 事件相关数据类和枚举
# ============================================================================

class EventPriority(Enum):
    """事件优先级"""
    LOW = 1
    NORMAL = 2
    HIGH = 3
    CRITICAL = 4

@dataclass
class Event:
    """事件数据类"""
    name: str
    data: Any = None
    source: str = "unknown"
    timestamp: datetime = field(default_factory=datetime.now)
    priority: EventPriority = EventPriority.NORMAL
    propagation_stopped: bool = False
    default_prevented: bool = False
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def stop_propagation(self):
        """停止事件传播"""
        self.propagation_stopped = True
    
    def prevent_default(self):
        """阻止默认行为"""
        self.default_prevented = True
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "name": self.name,
            "data": self.data,
            "source": self.source,
            "timestamp": self.timestamp.isoformat(),
            "priority": self.priority.name,
            "propagation_stopped": self.propagation_stopped,
            "default_prevented": self.default_prevented,
            "metadata": self.metadata
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Event':
        """从字典创建事件"""
        return cls(
            name=data["name"],
            data=data.get("data"),
            source=data.get("source", "unknown"),
            timestamp=datetime.fromisoformat(data.get("timestamp", datetime.now().isoformat())),
            priority=EventPriority[data.get("priority", "NORMAL")],
            propagation_stopped=data.get("propagation_stopped", False),
            default_prevented=data.get("default_prevented", False),
            metadata=data.get("metadata", {})
        )

@dataclass
class EventListener:
    """事件监听器"""
    callback: Callable
    priority: EventPriority = EventPriority.NORMAL
    once: bool = False  # 是否只执行一次
    async_handler: bool = False  # 是否异步处理
    condition: Optional[Callable[[Event], bool]] = None  # 执行条件
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def should_execute(self, event: Event) -> bool:
        """检查是否应该执行"""
        if self.condition:
            return self.condition(event)
        return True
    
    def execute(self, event: Event) -> Any:
        """执行回调"""
        if not self.should_execute(event):
            return None
        
        try:
            if self.async_handler:
                # 异步处理
                return asyncio.create_task(self.callback(event))
            else:
                # 同步处理
                return self.callback(event)
        except Exception as e:
            logging.error(f"Event listener execution failed: {e}")
            return None

# ============================================================================
# 事件总线接口
# ============================================================================

class EventBus(ABC):
    """事件总线抽象基类"""
    
    @abstractmethod
    def emit(self, event_name: str, data: Any = None, **kwargs) -> Event:
        """发射事件"""
        pass
    
    @abstractmethod
    def on(self, event_name: str, callback: Callable, **kwargs) -> str:
        """注册事件监听器"""
        pass
    
    @abstractmethod
    def off(self, event_name: str, listener_id: str = None) -> bool:
        """移除事件监听器"""
        pass
    
    @abstractmethod
    def once(self, event_name: str, callback: Callable, **kwargs) -> str:
        """注册一次性事件监听器"""
        pass
    
    @abstractmethod
    def has_listeners(self, event_name: str) -> bool:
        """检查是否有监听器"""
        pass
    
    @abstractmethod
    def get_listener_count(self, event_name: str) -> int:
        """获取监听器数量"""
        pass

# ============================================================================
# 本地事件总线实现
# ============================================================================

class LocalEventBus(EventBus):
    """本地事件总线实现"""
    
    def __init__(self, max_listeners: int = 100):
        self.listeners: Dict[str, Dict[str, EventListener]] = {}
        self.max_listeners = max_listeners
        self.event_history: List[Event] = []
        self.max_history = 1000
        self.logger = logging.getLogger(self.__class__.__name__)
        self._listener_counter = 0
        self._lock = threading.Lock()
    
    def _generate_listener_id(self) -> str:
        """生成监听器ID"""
        with self._lock:
            self._listener_counter += 1
            return f"listener_{self._listener_counter}"
    
    def _add_to_history(self, event: Event):
        """添加到事件历史"""
        self.event_history.append(event)
        if len(self.event_history) > self.max_history:
            self.event_history.pop(0)
    
    def emit(self, event_name: str, data: Any = None, **kwargs) -> Event:
        """发射事件"""
        # 创建事件对象
        event = Event(
            name=event_name,
            data=data,
            source=kwargs.get('source', 'unknown'),
            priority=kwargs.get('priority', EventPriority.NORMAL),
            metadata=kwargs.get('metadata', {})
        )
        
        # 添加到历史记录
        self._add_to_history(event)
        
        # 获取监听器
        listeners = self.listeners.get(event_name, {})
        
        if not listeners:
            self.logger.debug(f"No listeners for event: {event_name}")
            return event
        
        # 按优先级排序监听器
        sorted_listeners = sorted(
            listeners.items(),
            key=lambda x: x[1].priority.value,
            reverse=True
        )
        
        # 执行监听器
        to_remove = []
        for listener_id, listener in sorted_listeners:
            if event.propagation_stopped:
                break
            
            try:
                listener.execute(event)
                
                # 如果是一次性监听器，标记为删除
                if listener.once:
                    to_remove.append(listener_id)
                    
            except Exception as e:
                self.logger.error(f"Error executing listener {listener_id}: {e}")
        
        # 移除一次性监听器
        for listener_id in to_remove:
            del listeners[listener_id]
        
        return event
    
    def on(self, event_name: str, callback: Callable, **kwargs) -> str:
        """注册事件监听器"""
        if event_name not in self.listeners:
            self.listeners[event_name] = {}
        
        # 检查监听器数量限制
        if len(self.listeners[event_name]) >= self.max_listeners:
            raise ValueError(f"Too many listeners for event: {event_name}")
        
        # 创建监听器
        listener_id = self._generate_listener_id()
        listener = EventListener(
            callback=callback,
            priority=kwargs.get('priority', EventPriority.NORMAL),
            once=kwargs.get('once', False),
            async_handler=kwargs.get('async_handler', False),
            condition=kwargs.get('condition'),
            metadata=kwargs.get('metadata', {})
        )
        
        self.listeners[event_name][listener_id] = listener
        
        self.logger.debug(f"Registered listener {listener_id} for event: {event_name}")
        return listener_id
    
    def off(self, event_name: str, listener_id: str = None) -> bool:
        """移除事件监听器"""
        if event_name not in self.listeners:
            return False
        
        if listener_id is None:
            # 移除所有监听器
            del self.listeners[event_name]
            self.logger.debug(f"Removed all listeners for event: {event_name}")
            return True
        else:
            # 移除特定监听器
            if listener_id in self.listeners[event_name]:
                del self.listeners[event_name][listener_id]
                self.logger.debug(f"Removed listener {listener_id} for event: {event_name}")
                
                # 如果没有监听器了，删除事件键
                if not self.listeners[event_name]:
                    del self.listeners[event_name]
                
                return True
        
        return False
    
    def once(self, event_name: str, callback: Callable, **kwargs) -> str:
        """注册一次性事件监听器"""
        kwargs['once'] = True
        return self.on(event_name, callback, **kwargs)
    
    def has_listeners(self, event_name: str) -> bool:
        """检查是否有监听器"""
        return event_name in self.listeners and len(self.listeners[event_name]) > 0
    
    def get_listener_count(self, event_name: str) -> int:
        """获取监听器数量"""
        return len(self.listeners.get(event_name, {}))
    
    def get_all_events(self) -> List[str]:
        """获取所有事件名称"""
        return list(self.listeners.keys())
    
    def get_event_history(self, event_name: str = None, limit: int = None) -> List[Event]:
        """获取事件历史"""
        history = self.event_history
        
        if event_name:
            history = [e for e in history if e.name == event_name]
        
        if limit:
            history = history[-limit:]
        
        return history
    
    def clear_history(self):
        """清空事件历史"""
        self.event_history.clear()
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        total_listeners = sum(len(listeners) for listeners in self.listeners.values())
        
        return {
            "total_events": len(self.listeners),
            "total_listeners": total_listeners,
            "history_size": len(self.event_history),
            "events": {
                event_name: len(listeners)
                for event_name, listeners in self.listeners.items()
            }
        }

# ============================================================================
# 分布式事件总线（基于Redis）
# ============================================================================

class RedisEventBus(EventBus):
    """基于Redis的分布式事件总线"""
    
    def __init__(self, redis_client, channel_prefix: str = "events:"):
        self.redis = redis_client
        self.channel_prefix = channel_prefix
        self.local_listeners: Dict[str, Dict[str, EventListener]] = {}
        self.logger = logging.getLogger(self.__class__.__name__)
        self._listener_counter = 0
        self._pubsub = None
        self._listening_thread = None
        self._start_listening()
    
    def _start_listening(self):
        """开始监听Redis消息"""
        self._pubsub = self.redis.pubsub()
        self._listening_thread = threading.Thread(target=self._listen_loop, daemon=True)
        self._listening_thread.start()
    
    def _listen_loop(self):
        """监听循环"""
        try:
            for message in self._pubsub.listen():
                if message['type'] == 'message':
                    self._handle_redis_message(message)
        except Exception as e:
            self.logger.error(f"Redis listening error: {e}")
    
    def _handle_redis_message(self, message):
        """处理Redis消息"""
        try:
            # 解析事件数据
            event_data = json.loads(message['data'])
            event = Event.from_dict(event_data)
            
            # 执行本地监听器
            self._execute_local_listeners(event)
            
        except Exception as e:
            self.logger.error(f"Error handling Redis message: {e}")
    
    def _execute_local_listeners(self, event: Event):
        """执行本地监听器"""
        listeners = self.local_listeners.get(event.name, {})
        
        # 按优先级排序并执行
        sorted_listeners = sorted(
            listeners.items(),
            key=lambda x: x[1].priority.value,
            reverse=True
        )
        
        to_remove = []
        for listener_id, listener in sorted_listeners:
            if event.propagation_stopped:
                break
            
            try:
                listener.execute(event)
                
                if listener.once:
                    to_remove.append(listener_id)
                    
            except Exception as e:
                self.logger.error(f"Error executing listener {listener_id}: {e}")
        
        # 移除一次性监听器
        for listener_id in to_remove:
            del listeners[listener_id]
    
    def emit(self, event_name: str, data: Any = None, **kwargs) -> Event:
        """发射事件"""
        event = Event(
            name=event_name,
            data=data,
            source=kwargs.get('source', 'unknown'),
            priority=kwargs.get('priority', EventPriority.NORMAL),
            metadata=kwargs.get('metadata', {})
        )
        
        # 发布到Redis
        channel = f"{self.channel_prefix}{event_name}"
        message = json.dumps(event.to_dict(), default=str)
        
        try:
            self.redis.publish(channel, message)
        except Exception as e:
            self.logger.error(f"Error publishing event to Redis: {e}")
        
        return event
    
    def on(self, event_name: str, callback: Callable, **kwargs) -> str:
        """注册事件监听器"""
        if event_name not in self.local_listeners:
            self.local_listeners[event_name] = {}
            # 订阅Redis频道
            channel = f"{self.channel_prefix}{event_name}"
            self._pubsub.subscribe(channel)
        
        # 创建监听器
        self._listener_counter += 1
        listener_id = f"redis_listener_{self._listener_counter}"
        
        listener = EventListener(
            callback=callback,
            priority=kwargs.get('priority', EventPriority.NORMAL),
            once=kwargs.get('once', False),
            async_handler=kwargs.get('async_handler', False),
            condition=kwargs.get('condition'),
            metadata=kwargs.get('metadata', {})
        )
        
        self.local_listeners[event_name][listener_id] = listener
        
        return listener_id
    
    def off(self, event_name: str, listener_id: str = None) -> bool:
        """移除事件监听器"""
        if event_name not in self.local_listeners:
            return False
        
        if listener_id is None:
            # 移除所有监听器并取消订阅
            del self.local_listeners[event_name]
            channel = f"{self.channel_prefix}{event_name}"
            self._pubsub.unsubscribe(channel)
            return True
        else:
            # 移除特定监听器
            if listener_id in self.local_listeners[event_name]:
                del self.local_listeners[event_name][listener_id]
                
                # 如果没有监听器了，取消订阅
                if not self.local_listeners[event_name]:
                    del self.local_listeners[event_name]
                    channel = f"{self.channel_prefix}{event_name}"
                    self._pubsub.unsubscribe(channel)
                
                return True
        
        return False
    
    def once(self, event_name: str, callback: Callable, **kwargs) -> str:
        """注册一次性事件监听器"""
        kwargs['once'] = True
        return self.on(event_name, callback, **kwargs)
    
    def has_listeners(self, event_name: str) -> bool:
        """检查是否有监听器"""
        return event_name in self.local_listeners and len(self.local_listeners[event_name]) > 0
    
    def get_listener_count(self, event_name: str) -> int:
        """获取监听器数量"""
        return len(self.local_listeners.get(event_name, {}))

# ============================================================================
# 事件装饰器
# ============================================================================

def event_handler(event_name: str, priority: EventPriority = EventPriority.NORMAL,
                 once: bool = False, condition: Callable = None):
    """事件处理装饰器"""
    def decorator(func: Callable) -> Callable:
        # 将函数标记为事件处理器
        func._event_handler = True
        func._event_name = event_name
        func._event_priority = priority
        func._event_once = once
        func._event_condition = condition
        
        return func
    
    return decorator

def emit_event(event_name: str, data_key: str = None, source: str = None):
    """事件发射装饰器"""
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs):
            result = func(*args, **kwargs)
            
            # 确定事件数据
            if data_key and isinstance(result, dict):
                event_data = result.get(data_key, result)
            else:
                event_data = result
            
            # 发射事件
            global_event_bus.emit(
                event_name,
                data=event_data,
                source=source or func.__name__
            )
            
            return result
        
        return wrapper
    
    return decorator

# ============================================================================
# 事件聚合器
# ============================================================================

class EventAggregator:
    """事件聚合器 - 用于组合多个事件"""
    
    def __init__(self, event_bus: EventBus):
        self.event_bus = event_bus
        self.aggregations: Dict[str, Dict[str, Any]] = {}
    
    def create_aggregation(self, name: str, events: List[str], 
                          callback: Callable, timeout: float = None):
        """创建事件聚合"""
        aggregation = {
            "events": events,
            "callback": callback,
            "timeout": timeout,
            "received_events": {},
            "start_time": datetime.now(),
            "listener_ids": []
        }
        
        # 为每个事件注册监听器
        for event_name in events:
            listener_id = self.event_bus.on(
                event_name,
                lambda e, agg_name=name: self._handle_aggregated_event(agg_name, e)
            )
            aggregation["listener_ids"].append(listener_id)
        
        self.aggregations[name] = aggregation
    
    def _handle_aggregated_event(self, aggregation_name: str, event: Event):
        """处理聚合事件"""
        if aggregation_name not in self.aggregations:
            return
        
        aggregation = self.aggregations[aggregation_name]
        aggregation["received_events"][event.name] = event
        
        # 检查是否所有事件都已接收
        if len(aggregation["received_events"]) == len(aggregation["events"]):
            self._complete_aggregation(aggregation_name)
        
        # 检查超时
        elif aggregation["timeout"]:
            elapsed = (datetime.now() - aggregation["start_time"]).total_seconds()
            if elapsed >= aggregation["timeout"]:
                self._timeout_aggregation(aggregation_name)
    
    def _complete_aggregation(self, aggregation_name: str):
        """完成聚合"""
        aggregation = self.aggregations[aggregation_name]
        
        try:
            aggregation["callback"](aggregation["received_events"])
        except Exception as e:
            logging.error(f"Error in aggregation callback: {e}")
        
        self._cleanup_aggregation(aggregation_name)
    
    def _timeout_aggregation(self, aggregation_name: str):
        """聚合超时"""
        aggregation = self.aggregations[aggregation_name]
        
        # 发射超时事件
        self.event_bus.emit(
            f"{aggregation_name}_timeout",
            data=aggregation["received_events"]
        )
        
        self._cleanup_aggregation(aggregation_name)
    
    def _cleanup_aggregation(self, aggregation_name: str):
        """清理聚合"""
        if aggregation_name in self.aggregations:
            aggregation = self.aggregations[aggregation_name]
            
            # 移除监听器
            for event_name, listener_id in zip(aggregation["events"], aggregation["listener_ids"]):
                self.event_bus.off(event_name, listener_id)
            
            del self.aggregations[aggregation_name]

# ============================================================================
# 全局事件总线实例
# ============================================================================

# 全局事件总线
global_event_bus = LocalEventBus()

# 便捷函数
def emit(event_name: str, data: Any = None, **kwargs) -> Event:
    """发射事件的便捷函数"""
    return global_event_bus.emit(event_name, data, **kwargs)

def on(event_name: str, callback: Callable, **kwargs) -> str:
    """注册监听器的便捷函数"""
    return global_event_bus.on(event_name, callback, **kwargs)

def off(event_name: str, listener_id: str = None) -> bool:
    """移除监听器的便捷函数"""
    return global_event_bus.off(event_name, listener_id)

def once(event_name: str, callback: Callable, **kwargs) -> str:
    """注册一次性监听器的便捷函数"""
    return global_event_bus.once(event_name, callback, **kwargs)

# ============================================================================
# 自动注册事件处理器
# ============================================================================

def auto_register_handlers(obj):
    """自动注册对象中的事件处理器"""
    for attr_name in dir(obj):
        attr = getattr(obj, attr_name)
        
        if hasattr(attr, '_event_handler') and attr._event_handler:
            global_event_bus.on(
                attr._event_name,
                attr,
                priority=attr._event_priority,
                once=attr._event_once,
                condition=attr._event_condition
            )

# ============================================================================
# 预定义事件常量
# ============================================================================

class Events:
    """预定义事件常量"""
    
    # 应用生命周期事件
    APP_STARTED = "app.started"
    APP_STOPPED = "app.stopped"
    APP_ERROR = "app.error"
    
    # 数据事件
    DATA_LOADED = "data.loaded"
    DATA_UPDATED = "data.updated"
    DATA_ERROR = "data.error"
    
    # 用户事件
    USER_LOGIN = "user.login"
    USER_LOGOUT = "user.logout"
    USER_ACTION = "user.action"
    
    # 市场数据事件
    MARKET_DATA_UPDATED = "market.data.updated"
    STOCK_PRICE_CHANGED = "stock.price.changed"
    ALERT_TRIGGERED = "alert.triggered"
    
    # UI事件
    TAB_CHANGED = "ui.tab.changed"
    FILTER_APPLIED = "ui.filter.applied"
    CHART_UPDATED = "ui.chart.updated"
    
    # 系统事件
    CACHE_CLEARED = "system.cache.cleared"
    CONFIG_CHANGED = "system.config.changed"
    CONNECTION_STATUS = "system.connection.status"