#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Streamlit应用框架 - 整合所有工具类的完整示例

这个文件展示了如何使用我们创建的所有工具类来构建一个现代化的Streamlit应用：
- 依赖注入容器
- 中间件系统
- 插件系统
- 资源管理器
- 模板引擎
- 路由系统

作者: Assistant
创建时间: 2024
"""

import streamlit as st
from typing import Dict, Any, Optional, Callable, List
from dataclasses import dataclass
import time
import json
from pathlib import Path

# 导入我们的工具类
from .dependency_injection import (
    container, injectable, inject, auto_inject,
    DependencyScope, get_service, register_service
)
from .middleware import (
    MiddlewareManager, MiddlewareType, ExecutionOrder,
    middleware, use_middleware, get_middleware_manager
)
from .plugin_system import (
    get_plugin_manager, plugin, hook,
    PluginType, PluginStatus
)
from .asset_manager import (
    get_asset_manager, AssetType, LoadStrategy
)
from .template_engine import (
    get_template_engine, template_filter, template_function
)
from .router import (
    get_router, route, guard, middleware as route_middleware,
    cached_route, require_auth, layout, RouteType
)


@dataclass
class AppConfig:
    """应用配置"""
    app_name: str = "Penny Scanner"
    version: str = "1.0.0"
    debug: bool = False
    theme: str = "light"
    language: str = "zh-CN"
    cache_enabled: bool = True
    plugin_enabled: bool = True
    analytics_enabled: bool = True


class StreamlitAppFramework:
    """Streamlit应用框架
    
    整合所有工具类，提供统一的应用框架
    """
    
    def __init__(self, config: AppConfig = None):
        self.config = config or AppConfig()
        self.initialized = False
        
        # 工具类实例
        self.container = container
        self.middleware_manager = get_middleware_manager()
        self.plugin_manager = get_plugin_manager()
        self.asset_manager = get_asset_manager()
        self.template_engine = get_template_engine()
        self.router = get_router()
        
        # 应用状态
        self.startup_time = time.time()
        self.request_count = 0
    
    def initialize(self):
        """初始化应用框架"""
        if self.initialized:
            return
        
        # 1. 注册核心服务
        self._register_core_services()
        
        # 2. 设置中间件
        self._setup_middleware()
        
        # 3. 加载插件
        if self.config.plugin_enabled:
            self._load_plugins()
        
        # 4. 注册资源
        self._register_assets()
        
        # 5. 设置模板
        self._setup_templates()
        
        # 6. 注册路由
        self._register_routes()
        
        # 7. 设置页面配置
        self._setup_page_config()
        
        self.initialized = True
        
        # 触发初始化完成事件
        self.plugin_manager.trigger_action('app_initialized', self)
    
    def _register_core_services(self):
        """注册核心服务"""
        # 注册配置服务
        register_service('app_config', self.config, DependencyScope.SINGLETON)
        
        # 注册框架实例
        register_service('app_framework', self, DependencyScope.SINGLETON)
        
        # 注册工具类服务
        register_service('middleware_manager', self.middleware_manager, DependencyScope.SINGLETON)
        register_service('plugin_manager', self.plugin_manager, DependencyScope.SINGLETON)
        register_service('asset_manager', self.asset_manager, DependencyScope.SINGLETON)
        register_service('template_engine', self.template_engine, DependencyScope.SINGLETON)
        register_service('router', self.router, DependencyScope.SINGLETON)
    
    def _setup_middleware(self):
        """设置中间件"""
        # 请求计数中间件
        @middleware
        def request_counter_middleware(context):
            self.request_count += 1
            context.data['request_id'] = self.request_count
            context.data['request_time'] = time.time()
        
        # 性能监控中间件
        @middleware
        def performance_middleware(context):
            start_time = time.time()
            
            def after_request():
                duration = (time.time() - start_time) * 1000
                if 'performance_logs' not in st.session_state:
                    st.session_state.performance_logs = []
                
                st.session_state.performance_logs.append({
                    'request_id': context.data.get('request_id'),
                    'route': context.data.get('current_route'),
                    'duration': duration,
                    'timestamp': time.time()
                })
                
                # 保持最近100条记录
                if len(st.session_state.performance_logs) > 100:
                    st.session_state.performance_logs = st.session_state.performance_logs[-100:]
            
            context.data['after_request'] = after_request
        
        # 错误处理中间件
        @middleware
        def error_handling_middleware(context):
            try:
                yield
            except Exception as e:
                st.error(f"应用错误: {str(e)}")
                if self.config.debug:
                    st.exception(e)
                
                # 记录错误
                if 'error_logs' not in st.session_state:
                    st.session_state.error_logs = []
                
                st.session_state.error_logs.append({
                    'error': str(e),
                    'route': context.data.get('current_route'),
                    'timestamp': time.time(),
                    'request_id': context.data.get('request_id')
                })
        
        # 注册中间件
        self.middleware_manager.register_middleware(
            MiddlewareType.REQUEST, request_counter_middleware
        )
        self.middleware_manager.register_middleware(
            MiddlewareType.REQUEST, performance_middleware
        )
        self.middleware_manager.register_middleware(
            MiddlewareType.ERROR, error_handling_middleware
        )
    
    def _load_plugins(self):
        """加载插件"""
        # 自动发现并加载插件
        plugin_dir = Path("plugins")
        if plugin_dir.exists():
            self.plugin_manager.load_plugins_from_directory(str(plugin_dir))
        
        # 激活所有插件
        for plugin_name in self.plugin_manager.get_plugin_names():
            try:
                self.plugin_manager.activate_plugin(plugin_name)
            except Exception as e:
                if self.config.debug:
                    st.warning(f"插件 {plugin_name} 激活失败: {e}")
    
    def _register_assets(self):
        """注册资源"""
        # 注册CSS资源
        self.asset_manager.register_asset(
            "app_styles",
            AssetType.CSS,
            "static/css/app.css",
            load_strategy=LoadStrategy.EAGER
        )
        
        # 注册JavaScript资源
        self.asset_manager.register_asset(
            "app_scripts",
            AssetType.JAVASCRIPT,
            "static/js/app.js",
            load_strategy=LoadStrategy.LAZY
        )
        
        # 注册图标资源
        self.asset_manager.register_asset(
            "app_icon",
            AssetType.IMAGE,
            "static/images/icon.png"
        )
        
        # 自动扫描静态资源目录
        static_dir = Path("static")
        if static_dir.exists():
            self.asset_manager.scan_directory(str(static_dir))
    
    def _setup_templates(self):
        """设置模板"""
        # 注册模板过滤器
        @template_filter('currency')
        def currency_filter(value, currency='USD'):
            """货币格式化过滤器"""
            if currency == 'USD':
                return f"${value:,.2f}"
            elif currency == 'CNY':
                return f"¥{value:,.2f}"
            else:
                return f"{value:,.2f} {currency}"
        
        @template_filter('percentage')
        def percentage_filter(value, decimals=2):
            """百分比格式化过滤器"""
            return f"{value:.{decimals}%}"
        
        # 注册模板函数
        @template_function('get_user_name')
        def get_user_name():
            """获取用户名"""
            return st.session_state.get('user_name', 'Guest')
        
        @template_function('format_time')
        def format_time(timestamp):
            """格式化时间"""
            import datetime
            dt = datetime.datetime.fromtimestamp(timestamp)
            return dt.strftime('%Y-%m-%d %H:%M:%S')
    
    def _register_routes(self):
        """注册路由"""
        # 主页路由
        @route('home', '/', title='首页', icon='🏠', route_type=RouteType.TAB)
        @cached_route(ttl=300)
        def home_page():
            st.title("🏠 欢迎使用 Penny Scanner")
            st.write("这是一个现代化的股票分析应用")
            
            # 显示应用统计
            col1, col2, col3, col4 = st.columns(4)
            
            with col1:
                st.metric("运行时间", f"{(time.time() - self.startup_time):.0f}s")
            
            with col2:
                st.metric("请求次数", self.request_count)
            
            with col3:
                plugin_count = len(self.plugin_manager.get_plugin_names())
                st.metric("已加载插件", plugin_count)
            
            with col4:
                route_count = len(self.router.routes)
                st.metric("注册路由", route_count)
        
        # 美股页面
        @route('us_stocks', '/us-stocks', title='美股', icon='🇺🇸', route_type=RouteType.TAB)
        def us_stocks_page():
            st.title("🇺🇸 美股市场")
            st.write("美股数据和分析")
        
        # 港股页面
        @route('hk_stocks', '/hk-stocks', title='港股', icon='🇭🇰', route_type=RouteType.TAB)
        def hk_stocks_page():
            st.title("🇭🇰 港股市场")
            st.write("港股数据和分析")
        
        # 设置页面
        @route('settings', '/settings', title='设置', icon='⚙️', route_type=RouteType.PAGE)
        def settings_page():
            st.title("⚙️ 应用设置")
            
            # 主题设置
            theme = st.selectbox(
                "主题",
                ["light", "dark"],
                index=0 if self.config.theme == "light" else 1
            )
            
            if theme != self.config.theme:
                self.config.theme = theme
                st.success("主题设置已更新")
            
            # 语言设置
            language = st.selectbox(
                "语言",
                ["zh-CN", "en-US"],
                index=0 if self.config.language == "zh-CN" else 1
            )
            
            if language != self.config.language:
                self.config.language = language
                st.success("语言设置已更新")
        
        # 管理员页面（需要认证）
        @route('admin', '/admin', title='管理', icon='👑', route_type=RouteType.PAGE)
        @require_auth
        def admin_page():
            st.title("👑 管理面板")
            
            tab1, tab2, tab3, tab4 = st.tabs(["路由分析", "性能监控", "插件管理", "系统信息"])
            
            with tab1:
                self.router.render_route_analytics_dashboard()
            
            with tab2:
                self.router.render_performance_dashboard()
            
            with tab3:
                self._render_plugin_management()
            
            with tab4:
                self._render_system_info()
    
    def _setup_page_config(self):
        """设置页面配置"""
        st.set_page_config(
            page_title=self.config.app_name,
            page_icon="📈",
            layout="wide",
            initial_sidebar_state="expanded"
        )
        
        # 注入CSS样式
        if self.asset_manager.has_asset("app_styles"):
            css_content = self.asset_manager.get_asset_content("app_styles")
            if css_content:
                st.markdown(f"<style>{css_content}</style>", unsafe_allow_html=True)
    
    def _render_plugin_management(self):
        """渲染插件管理界面"""
        st.subheader("🔌 插件管理")
        
        plugins = self.plugin_manager.get_all_plugins()
        
        if not plugins:
            st.info("暂无已加载的插件")
            return
        
        for plugin_name, plugin_info in plugins.items():
            with st.expander(f"{plugin_info.metadata.display_name} v{plugin_info.metadata.version}"):
                col1, col2, col3 = st.columns([2, 1, 1])
                
                with col1:
                    st.write(f"**描述:** {plugin_info.metadata.description}")
                    st.write(f"**作者:** {plugin_info.metadata.author}")
                    st.write(f"**状态:** {plugin_info.status.value}")
                
                with col2:
                    if plugin_info.status == PluginStatus.ACTIVE:
                        if st.button("停用", key=f"deactivate_{plugin_name}"):
                            self.plugin_manager.deactivate_plugin(plugin_name)
                            st.rerun()
                    else:
                        if st.button("激活", key=f"activate_{plugin_name}"):
                            self.plugin_manager.activate_plugin(plugin_name)
                            st.rerun()
                
                with col3:
                    if st.button("卸载", key=f"unload_{plugin_name}"):
                        self.plugin_manager.unload_plugin(plugin_name)
                        st.rerun()
    
    def _render_system_info(self):
        """渲染系统信息"""
        st.subheader("💻 系统信息")
        
        # 应用信息
        st.write("**应用信息:**")
        st.json({
            "名称": self.config.app_name,
            "版本": self.config.version,
            "运行时间": f"{(time.time() - self.startup_time):.2f}秒",
            "请求次数": self.request_count,
            "调试模式": self.config.debug
        })
        
        # 依赖注入容器信息
        st.write("**依赖注入容器:**")
        container_info = self.container.get_container_info()
        st.json(container_info)
        
        # 中间件信息
        st.write("**中间件信息:**")
        middleware_info = self.middleware_manager.get_statistics()
        st.json(middleware_info)
        
        # 资源管理器信息
        st.write("**资源管理器:**")
        asset_info = self.asset_manager.get_statistics()
        st.json(asset_info)
    
    def run(self):
        """运行应用"""
        # 初始化框架
        self.initialize()
        
        # 执行请求中间件
        context = self.middleware_manager.create_context()
        context.data['current_route'] = self.router.current_route
        
        try:
            # 执行前置中间件
            self.middleware_manager.execute_middleware(
                MiddlewareType.REQUEST, context, ExecutionOrder.BEFORE
            )
            
            # 渲染应用界面
            self._render_app()
            
            # 执行后置中间件
            self.middleware_manager.execute_middleware(
                MiddlewareType.REQUEST, context, ExecutionOrder.AFTER
            )
            
            # 执行清理回调
            if 'after_request' in context.data:
                context.data['after_request']()
                
        except Exception as e:
            # 执行错误中间件
            context.data['error'] = e
            self.middleware_manager.execute_middleware(
                MiddlewareType.ERROR, context
            )
    
    def _render_app(self):
        """渲染应用界面"""
        # 渲染侧边栏导航
        self.router.render_sidebar_navigation()
        
        # 渲染主内容区域
        with st.container():
            # 渲染面包屑导航
            self.router.render_breadcrumbs()
            
            # 渲染搜索框
            with st.expander("🔍 搜索", expanded=False):
                self.router.render_search_box()
            
            # 渲染主要导航tabs
            self.router.render_navigation_menu()
            
            # 渲染当前路由内容
            current_route = self.router.get_current_route()
            if current_route and current_route.component:
                try:
                    # 应用布局（如果有）
                    if hasattr(current_route.component, '_layout'):
                        layout_name = current_route.component._layout
                        content = current_route.component()
                        self.router.render_with_layout(layout_name, content)
                    else:
                        current_route.component()
                except Exception as e:
                    st.error(f"页面渲染错误: {str(e)}")
                    if self.config.debug:
                        st.exception(e)
            else:
                st.error("页面未找到")


# 全局应用实例
app_framework = None

def get_app_framework(config: AppConfig = None) -> StreamlitAppFramework:
    """获取应用框架实例"""
    global app_framework
    if app_framework is None:
        app_framework = StreamlitAppFramework(config)
    return app_framework

def create_app(config: AppConfig = None) -> StreamlitAppFramework:
    """创建新的应用实例"""
    return StreamlitAppFramework(config)

def run_app(config: AppConfig = None):
    """运行应用的便捷函数"""
    app = get_app_framework(config)
    app.run()

# 装饰器
def streamlit_app(config: AppConfig = None):
    """Streamlit应用装饰器"""
    def decorator(func):
        def wrapper():
            app = get_app_framework(config)
            app.initialize()
            return func(app)
        return wrapper
    return decorator

# 示例用法
if __name__ == "__main__":
    # 创建应用配置
    config = AppConfig(
        app_name="Penny Scanner Demo",
        version="1.0.0",
        debug=True,
        theme="light",
        language="zh-CN"
    )
    
    # 运行应用
    run_app(config)