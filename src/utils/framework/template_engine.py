from typing import Dict, Any, List, Optional, Union, Callable, Type, TypeVar
from dataclasses import dataclass, field
from abc import ABC, abstractmethod
import os
import re
import json
import logging
from enum import Enum
import threading
import time
from pathlib import Path
from collections import defaultdict, ChainMap
import ast
import inspect
from functools import wraps
import html
import urllib.parse
from datetime import datetime, date
from decimal import Decimal

# ============================================================================
# 模板相关类型和枚举
# ============================================================================

T = TypeVar('T')

class TemplateType(Enum):
    """模板类型"""
    PAGE = "page"              # 页面模板
    LAYOUT = "layout"          # 布局模板
    COMPONENT = "component"    # 组件模板
    PARTIAL = "partial"        # 部分模板
    EMAIL = "email"            # 邮件模板
    WIDGET = "widget"          # 小部件模板

class RenderMode(Enum):
    """渲染模式"""
    HTML = "html"              # HTML渲染
    MARKDOWN = "markdown"      # Markdown渲染
    TEXT = "text"              # 纯文本渲染
    JSON = "json"              # JSON渲染
    XML = "xml"                # XML渲染

@dataclass
class TemplateMetadata:
    """模板元数据"""
    name: str
    path: str
    template_type: TemplateType = TemplateType.PAGE
    render_mode: RenderMode = RenderMode.HTML
    extends: Optional[str] = None
    includes: List[str] = field(default_factory=list)
    blocks: List[str] = field(default_factory=list)
    variables: List[str] = field(default_factory=list)
    filters: List[str] = field(default_factory=list)
    functions: List[str] = field(default_factory=list)
    cache_key: str = ""
    cache_ttl: int = 0
    version: str = "1.0.0"
    author: str = ""
    description: str = ""
    tags: List[str] = field(default_factory=list)
    created_at: float = field(default_factory=time.time)
    modified_at: float = field(default_factory=time.time)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'name': self.name,
            'path': self.path,
            'template_type': self.template_type.value,
            'render_mode': self.render_mode.value,
            'extends': self.extends,
            'includes': self.includes,
            'blocks': self.blocks,
            'variables': self.variables,
            'filters': self.filters,
            'functions': self.functions,
            'cache_key': self.cache_key,
            'cache_ttl': self.cache_ttl,
            'version': self.version,
            'author': self.author,
            'description': self.description,
            'tags': self.tags,
            'created_at': self.created_at,
            'modified_at': self.modified_at
        }

@dataclass
class TemplateContext:
    """模板上下文"""
    variables: Dict[str, Any] = field(default_factory=dict)
    globals: Dict[str, Any] = field(default_factory=dict)
    filters: Dict[str, Callable] = field(default_factory=dict)
    functions: Dict[str, Callable] = field(default_factory=dict)
    blocks: Dict[str, str] = field(default_factory=dict)
    parent_context: Optional['TemplateContext'] = None
    
    def get(self, key: str, default: Any = None) -> Any:
        """获取变量值"""
        # 优先级：variables > globals > parent_context
        if key in self.variables:
            return self.variables[key]
        elif key in self.globals:
            return self.globals[key]
        elif self.parent_context:
            return self.parent_context.get(key, default)
        else:
            return default
    
    def set(self, key: str, value: Any):
        """设置变量值"""
        self.variables[key] = value
    
    def update(self, data: Dict[str, Any]):
        """更新变量"""
        self.variables.update(data)
    
    def create_child(self, variables: Dict[str, Any] = None) -> 'TemplateContext':
        """创建子上下文"""
        child = TemplateContext(
            variables=variables or {},
            parent_context=self
        )
        return child

@dataclass
class TemplateBlock:
    """模板块"""
    name: str
    content: str
    parent_content: str = ""
    is_super: bool = False
    
    def render(self, context: TemplateContext) -> str:
        """渲染块内容"""
        if self.is_super and self.parent_content:
            return self.parent_content + self.content
        return self.content

# ============================================================================
# 模板解析器
# ============================================================================

class TemplateParser:
    """模板解析器"""
    
    def __init__(self):
        # 模板语法正则表达式
        self.variable_pattern = re.compile(r'\{\{\s*([^}]+)\s*\}\}')
        self.block_pattern = re.compile(r'\{%\s*(\w+)\s*([^%]*)\s*%\}(.*?)\{%\s*end\1\s*%\}', re.DOTALL)
        self.statement_pattern = re.compile(r'\{%\s*([^%]+)\s*%\}')
        self.comment_pattern = re.compile(r'\{#\s*([^#]*)\s*#\}')
        
        # 内置语句处理器
        self.statement_handlers = {
            'extends': self._handle_extends,
            'include': self._handle_include,
            'block': self._handle_block,
            'if': self._handle_if,
            'for': self._handle_for,
            'set': self._handle_set,
            'with': self._handle_with
        }
        
        self.logger = logging.getLogger(self.__class__.__name__)
    
    def parse(self, template_content: str) -> Dict[str, Any]:
        """解析模板"""
        result = {
            'content': template_content,
            'extends': None,
            'includes': [],
            'blocks': {},
            'variables': set(),
            'filters': set(),
            'functions': set()
        }
        
        # 移除注释
        content = self.comment_pattern.sub('', template_content)
        
        # 解析extends
        extends_match = re.search(r'\{%\s*extends\s+["\']([^"\'\']+)["\']\s*%\}', content)
        if extends_match:
            result['extends'] = extends_match.group(1)
            content = content.replace(extends_match.group(0), '')
        
        # 解析includes
        include_matches = re.findall(r'\{%\s*include\s+["\']([^"\'\']+)["\']\s*%\}', content)
        result['includes'] = include_matches
        
        # 解析blocks
        block_matches = self.block_pattern.findall(content)
        for block_type, block_args, block_content in block_matches:
            if block_type == 'block':
                block_name = block_args.strip().strip('"\'')
                result['blocks'][block_name] = block_content.strip()
        
        # 解析变量
        variable_matches = self.variable_pattern.findall(content)
        for var_expr in variable_matches:
            # 解析变量表达式
            var_parts = var_expr.split('|')
            var_name = var_parts[0].strip().split('.')[0].split('[')[0]
            result['variables'].add(var_name)
            
            # 解析过滤器
            if len(var_parts) > 1:
                for filter_expr in var_parts[1:]:
                    filter_name = filter_expr.strip().split('(')[0]
                    result['filters'].add(filter_name)
        
        # 解析函数调用
        function_matches = re.findall(r'(\w+)\s*\(', content)
        result['functions'].update(function_matches)
        
        result['content'] = content
        return result
    
    def _handle_extends(self, args: str, content: str, context: TemplateContext) -> str:
        """处理extends语句"""
        return ''  # extends在解析阶段处理
    
    def _handle_include(self, args: str, content: str, context: TemplateContext) -> str:
        """处理include语句"""
        return ''  # include在渲染阶段处理
    
    def _handle_block(self, args: str, content: str, context: TemplateContext) -> str:
        """处理block语句"""
        return content  # block内容直接返回
    
    def _handle_if(self, args: str, content: str, context: TemplateContext) -> str:
        """处理if语句"""
        condition = self._evaluate_expression(args, context)
        return content if condition else ''
    
    def _handle_for(self, args: str, content: str, context: TemplateContext) -> str:
        """处理for语句"""
        # 解析for语句：item in items
        match = re.match(r'(\w+)\s+in\s+(.+)', args)
        if not match:
            return ''
        
        var_name, iterable_expr = match.groups()
        iterable = self._evaluate_expression(iterable_expr, context)
        
        if not iterable:
            return ''
        
        result = []
        for item in iterable:
            child_context = context.create_child({var_name: item})
            rendered = self._render_content(content, child_context)
            result.append(rendered)
        
        return ''.join(result)
    
    def _handle_set(self, args: str, content: str, context: TemplateContext) -> str:
        """处理set语句"""
        # 解析set语句：var = value
        match = re.match(r'(\w+)\s*=\s*(.+)', args)
        if match:
            var_name, value_expr = match.groups()
            value = self._evaluate_expression(value_expr, context)
            context.set(var_name, value)
        return ''
    
    def _handle_with(self, args: str, content: str, context: TemplateContext) -> str:
        """处理with语句"""
        # 解析with语句：var = value
        variables = {}
        for assignment in args.split(','):
            match = re.match(r'(\w+)\s*=\s*(.+)', assignment.strip())
            if match:
                var_name, value_expr = match.groups()
                value = self._evaluate_expression(value_expr, context)
                variables[var_name] = value
        
        child_context = context.create_child(variables)
        return self._render_content(content, child_context)
    
    def _evaluate_expression(self, expr: str, context: TemplateContext) -> Any:
        """评估表达式"""
        expr = expr.strip()
        
        # 处理字符串字面量
        if (expr.startswith('"') and expr.endswith('"')) or (expr.startswith("'") and expr.endswith("'")):
            return expr[1:-1]
        
        # 处理数字字面量
        try:
            if '.' in expr:
                return float(expr)
            else:
                return int(expr)
        except ValueError:
            pass
        
        # 处理布尔值
        if expr.lower() == 'true':
            return True
        elif expr.lower() == 'false':
            return False
        elif expr.lower() == 'none' or expr.lower() == 'null':
            return None
        
        # 处理变量和属性访问
        if '.' in expr:
            parts = expr.split('.')
            value = context.get(parts[0])
            for part in parts[1:]:
                if value is None:
                    break
                if hasattr(value, part):
                    value = getattr(value, part)
                elif isinstance(value, dict) and part in value:
                    value = value[part]
                else:
                    value = None
                    break
            return value
        
        # 处理简单变量
        return context.get(expr)
    
    def _render_content(self, content: str, context: TemplateContext) -> str:
        """渲染内容"""
        # 这里应该调用模板引擎的渲染方法
        # 为了避免循环依赖，这里简化处理
        return content

# ============================================================================
# 模板过滤器
# ============================================================================

class TemplateFilters:
    """模板过滤器"""
    
    @staticmethod
    def escape(value: Any) -> str:
        """HTML转义"""
        return html.escape(str(value))
    
    @staticmethod
    def safe(value: Any) -> str:
        """标记为安全（不转义）"""
        return str(value)
    
    @staticmethod
    def upper(value: Any) -> str:
        """转换为大写"""
        return str(value).upper()
    
    @staticmethod
    def lower(value: Any) -> str:
        """转换为小写"""
        return str(value).lower()
    
    @staticmethod
    def title(value: Any) -> str:
        """转换为标题格式"""
        return str(value).title()
    
    @staticmethod
    def capitalize(value: Any) -> str:
        """首字母大写"""
        return str(value).capitalize()
    
    @staticmethod
    def length(value: Any) -> int:
        """获取长度"""
        try:
            return len(value)
        except TypeError:
            return 0
    
    @staticmethod
    def default(value: Any, default_value: Any = "") -> Any:
        """默认值"""
        return value if value is not None else default_value
    
    @staticmethod
    def join(value: List[Any], separator: str = ", ") -> str:
        """连接列表"""
        if isinstance(value, (list, tuple)):
            return separator.join(str(item) for item in value)
        return str(value)
    
    @staticmethod
    def slice(value: List[Any], start: int, end: int = None) -> List[Any]:
        """切片"""
        if isinstance(value, (list, tuple, str)):
            return value[start:end]
        return value
    
    @staticmethod
    def first(value: List[Any]) -> Any:
        """获取第一个元素"""
        if isinstance(value, (list, tuple)) and value:
            return value[0]
        return None
    
    @staticmethod
    def last(value: List[Any]) -> Any:
        """获取最后一个元素"""
        if isinstance(value, (list, tuple)) and value:
            return value[-1]
        return None
    
    @staticmethod
    def reverse(value: List[Any]) -> List[Any]:
        """反转列表"""
        if isinstance(value, (list, tuple)):
            return list(reversed(value))
        elif isinstance(value, str):
            return value[::-1]
        return value
    
    @staticmethod
    def sort(value: List[Any], key: str = None, reverse: bool = False) -> List[Any]:
        """排序列表"""
        if isinstance(value, (list, tuple)):
            if key:
                return sorted(value, key=lambda x: getattr(x, key, x), reverse=reverse)
            else:
                return sorted(value, reverse=reverse)
        return value
    
    @staticmethod
    def format_date(value: Any, format_str: str = "%Y-%m-%d") -> str:
        """格式化日期"""
        if isinstance(value, (datetime, date)):
            return value.strftime(format_str)
        return str(value)
    
    @staticmethod
    def format_number(value: Any, decimals: int = 2) -> str:
        """格式化数字"""
        try:
            if isinstance(value, (int, float, Decimal)):
                return f"{value:.{decimals}f}"
        except:
            pass
        return str(value)
    
    @staticmethod
    def url_encode(value: Any) -> str:
        """URL编码"""
        return urllib.parse.quote(str(value))
    
    @staticmethod
    def json_encode(value: Any) -> str:
        """JSON编码"""
        try:
            return json.dumps(value, ensure_ascii=False)
        except:
            return str(value)
    
    @staticmethod
    def truncate(value: Any, length: int = 100, suffix: str = "...") -> str:
        """截断文本"""
        text = str(value)
        if len(text) <= length:
            return text
        return text[:length - len(suffix)] + suffix
    
    @staticmethod
    def wordwrap(value: Any, width: int = 80) -> str:
        """自动换行"""
        import textwrap
        return textwrap.fill(str(value), width=width)

# ============================================================================
# 模板函数
# ============================================================================

class TemplateFunctions:
    """模板函数"""
    
    @staticmethod
    def range(start: int, stop: int = None, step: int = 1) -> range:
        """生成范围"""
        if stop is None:
            return range(start)
        return range(start, stop, step)
    
    @staticmethod
    def url_for(endpoint: str, **kwargs) -> str:
        """生成URL"""
        # 这里应该集成路由系统
        query_string = '&'.join(f"{k}={v}" for k, v in kwargs.items())
        return f"{endpoint}?{query_string}" if query_string else endpoint
    
    @staticmethod
    def asset_url(asset_name: str) -> str:
        """生成资源URL"""
        # 这里应该集成资源管理器
        from .asset_manager import global_asset_manager
        try:
            return global_asset_manager.get_asset_url(asset_name)
        except:
            return f"/assets/{asset_name}"
    
    @staticmethod
    def csrf_token() -> str:
        """生成CSRF令牌"""
        import secrets
        return secrets.token_urlsafe(32)
    
    @staticmethod
    def now() -> datetime:
        """获取当前时间"""
        return datetime.now()
    
    @staticmethod
    def config(key: str, default: Any = None) -> Any:
        """获取配置值"""
        # 这里应该集成配置管理器
        from .dependency_injection import get_service
        try:
            config_service = get_service('app_config')
            return config_service.get(key, default)
        except:
            return default
    
    @staticmethod
    def trans(key: str, **kwargs) -> str:
        """翻译文本"""
        # 这里应该集成国际化系统
        # 简化实现
        text = key
        for k, v in kwargs.items():
            text = text.replace(f"{{{k}}}", str(v))
        return text

# ============================================================================
# 模板引擎实现
# ============================================================================

class TemplateEngine:
    """模板引擎"""
    
    def __init__(self, template_dirs: List[str] = None, auto_escape: bool = True):
        self.template_dirs = template_dirs or []
        self.auto_escape = auto_escape
        
        # 模板缓存
        self.template_cache: Dict[str, str] = {}
        self.metadata_cache: Dict[str, TemplateMetadata] = {}
        self.compiled_cache: Dict[str, Dict[str, Any]] = {}
        
        # 解析器
        self.parser = TemplateParser()
        
        # 全局上下文
        self.global_context = TemplateContext()
        
        # 注册默认过滤器和函数
        self._register_default_filters()
        self._register_default_functions()
        
        # 统计信息
        self.stats = {
            "total_templates": 0,
            "cache_hits": 0,
            "cache_misses": 0,
            "render_times": [],
            "error_count": 0
        }
        
        self.logger = logging.getLogger(self.__class__.__name__)
        self._lock = threading.Lock()
    
    def add_template_dir(self, template_dir: str):
        """添加模板目录"""
        if template_dir not in self.template_dirs:
            self.template_dirs.append(template_dir)
            self.logger.debug(f"Added template directory: {template_dir}")
    
    def register_filter(self, name: str, filter_func: Callable):
        """注册过滤器"""
        self.global_context.filters[name] = filter_func
        self.logger.debug(f"Registered filter: {name}")
    
    def register_function(self, name: str, func: Callable):
        """注册函数"""
        self.global_context.functions[name] = func
        self.logger.debug(f"Registered function: {name}")
    
    def register_global(self, name: str, value: Any):
        """注册全局变量"""
        self.global_context.globals[name] = value
        self.logger.debug(f"Registered global: {name}")
    
    def load_template(self, template_name: str) -> str:
        """加载模板内容"""
        # 检查缓存
        if template_name in self.template_cache:
            with self._lock:
                self.stats["cache_hits"] += 1
            return self.template_cache[template_name]
        
        # 查找模板文件
        template_path = self._find_template(template_name)
        if not template_path:
            raise FileNotFoundError(f"Template not found: {template_name}")
        
        # 读取模板内容
        try:
            with open(template_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 缓存模板内容
            self.template_cache[template_name] = content
            
            with self._lock:
                self.stats["cache_misses"] += 1
                self.stats["total_templates"] += 1
            
            self.logger.debug(f"Loaded template: {template_name}")
            return content
        
        except Exception as e:
            with self._lock:
                self.stats["error_count"] += 1
            
            self.logger.error(f"Failed to load template {template_name}: {str(e)}")
            raise
    
    def compile_template(self, template_name: str) -> Dict[str, Any]:
        """编译模板"""
        # 检查编译缓存
        if template_name in self.compiled_cache:
            return self.compiled_cache[template_name]
        
        # 加载模板内容
        content = self.load_template(template_name)
        
        # 解析模板
        parsed = self.parser.parse(content)
        
        # 处理继承
        if parsed['extends']:
            parent_compiled = self.compile_template(parsed['extends'])
            # 合并父模板的块
            for block_name, block_content in parent_compiled['blocks'].items():
                if block_name not in parsed['blocks']:
                    parsed['blocks'][block_name] = block_content
            
            # 使用父模板的内容作为基础
            parsed['base_content'] = parent_compiled['content']
        
        # 缓存编译结果
        self.compiled_cache[template_name] = parsed
        
        self.logger.debug(f"Compiled template: {template_name}")
        return parsed
    
    def render(self, template_name: str, context: Dict[str, Any] = None, **kwargs) -> str:
        """渲染模板"""
        start_time = time.time()
        
        try:
            # 编译模板
            compiled = self.compile_template(template_name)
            
            # 创建渲染上下文
            render_context = self.global_context.create_child()
            if context:
                render_context.update(context)
            render_context.update(kwargs)
            
            # 渲染模板
            result = self._render_compiled(compiled, render_context)
            
            # 更新统计
            render_time = time.time() - start_time
            with self._lock:
                self.stats["render_times"].append(render_time)
            
            self.logger.debug(f"Rendered template: {template_name} in {render_time:.3f}s")
            return result
        
        except Exception as e:
            with self._lock:
                self.stats["error_count"] += 1
            
            self.logger.error(f"Failed to render template {template_name}: {str(e)}")
            raise
    
    def render_string(self, template_string: str, context: Dict[str, Any] = None, **kwargs) -> str:
        """渲染模板字符串"""
        # 解析模板字符串
        parsed = self.parser.parse(template_string)
        
        # 创建渲染上下文
        render_context = self.global_context.create_child()
        if context:
            render_context.update(context)
        render_context.update(kwargs)
        
        # 渲染模板
        return self._render_compiled(parsed, render_context)
    
    def _render_compiled(self, compiled: Dict[str, Any], context: TemplateContext) -> str:
        """渲染编译后的模板"""
        content = compiled['content']
        
        # 处理继承
        if 'base_content' in compiled:
            content = compiled['base_content']
            
            # 替换块内容
            for block_name, block_content in compiled['blocks'].items():
                block_pattern = re.compile(
                    rf'\{{%\s*block\s+{re.escape(block_name)}\s*%\}}.*?\{{%\s*endblock\s*%\}}',
                    re.DOTALL
                )
                content = block_pattern.sub(block_content, content)
        
        # 处理includes
        for include_name in compiled['includes']:
            try:
                include_content = self.render(include_name, context.variables)
                include_pattern = rf'\{{%\s*include\s+["\']?{re.escape(include_name)}["\']?\s*%\}}'
                content = re.sub(include_pattern, include_content, content)
            except Exception as e:
                self.logger.warning(f"Failed to include template {include_name}: {e}")
                content = re.sub(include_pattern, '', content)
        
        # 渲染变量和语句
        content = self._render_variables(content, context)
        content = self._render_statements(content, context)
        
        return content
    
    def _render_variables(self, content: str, context: TemplateContext) -> str:
        """渲染变量"""
        def replace_variable(match):
            var_expr = match.group(1).strip()
            
            # 解析过滤器
            parts = var_expr.split('|')
            var_name = parts[0].strip()
            
            # 获取变量值
            value = self._evaluate_expression(var_name, context)
            
            # 应用过滤器
            for filter_expr in parts[1:]:
                filter_parts = filter_expr.strip().split('(')
                filter_name = filter_parts[0].strip()
                
                if filter_name in context.filters:
                    filter_func = context.filters[filter_name]
                    
                    # 解析过滤器参数
                    if len(filter_parts) > 1:
                        args_str = filter_parts[1].rstrip(')')
                        args = [self._evaluate_expression(arg.strip(), context) 
                               for arg in args_str.split(',') if arg.strip()]
                        value = filter_func(value, *args)
                    else:
                        value = filter_func(value)
            
            # 自动转义
            if self.auto_escape and not getattr(value, '_safe', False):
                value = html.escape(str(value))
            
            return str(value)
        
        return self.parser.variable_pattern.sub(replace_variable, content)
    
    def _render_statements(self, content: str, context: TemplateContext) -> str:
        """渲染语句"""
        # 处理块语句
        def replace_block(match):
            statement_type = match.group(1)
            statement_args = match.group(2).strip()
            statement_content = match.group(3)
            
            if statement_type in self.parser.statement_handlers:
                handler = self.parser.statement_handlers[statement_type]
                return handler(statement_args, statement_content, context)
            
            return match.group(0)  # 保持原样
        
        content = self.parser.block_pattern.sub(replace_block, content)
        
        # 处理单行语句
        def replace_statement(match):
            statement = match.group(1).strip()
            parts = statement.split(None, 1)
            
            if not parts:
                return ''
            
            statement_type = parts[0]
            statement_args = parts[1] if len(parts) > 1 else ''
            
            if statement_type in self.parser.statement_handlers:
                handler = self.parser.statement_handlers[statement_type]
                return handler(statement_args, '', context)
            
            return ''  # 移除未知语句
        
        content = self.parser.statement_pattern.sub(replace_statement, content)
        
        return content
    
    def _evaluate_expression(self, expr: str, context: TemplateContext) -> Any:
        """评估表达式"""
        return self.parser._evaluate_expression(expr, context)
    
    def _find_template(self, template_name: str) -> Optional[str]:
        """查找模板文件"""
        for template_dir in self.template_dirs:
            template_path = os.path.join(template_dir, template_name)
            
            # 尝试不同的扩展名
            for ext in ['', '.html', '.htm', '.txt', '.md']:
                full_path = template_path + ext
                if os.path.exists(full_path):
                    return full_path
        
        return None
    
    def _register_default_filters(self):
        """注册默认过滤器"""
        filters = TemplateFilters()
        for name in dir(filters):
            if not name.startswith('_'):
                filter_func = getattr(filters, name)
                if callable(filter_func):
                    self.register_filter(name, filter_func)
    
    def _register_default_functions(self):
        """注册默认函数"""
        functions = TemplateFunctions()
        for name in dir(functions):
            if not name.startswith('_'):
                func = getattr(functions, name)
                if callable(func):
                    self.register_function(name, func)
    
    def clear_cache(self, template_name: str = None):
        """清除缓存"""
        with self._lock:
            if template_name:
                self.template_cache.pop(template_name, None)
                self.compiled_cache.pop(template_name, None)
                self.metadata_cache.pop(template_name, None)
            else:
                self.template_cache.clear()
                self.compiled_cache.clear()
                self.metadata_cache.clear()
        
        self.logger.debug(f"Cleared cache for: {template_name or 'all templates'}")
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        avg_render_time = (
            sum(self.stats["render_times"]) / len(self.stats["render_times"])
            if self.stats["render_times"] else 0
        )
        
        cache_hit_rate = (
            self.stats["cache_hits"] / (self.stats["cache_hits"] + self.stats["cache_misses"])
            if (self.stats["cache_hits"] + self.stats["cache_misses"]) > 0 else 0
        )
        
        return {
            **self.stats,
            "average_render_time": avg_render_time,
            "cache_hit_rate": cache_hit_rate,
            "template_cache_size": len(self.template_cache),
            "compiled_cache_size": len(self.compiled_cache),
            "template_directories": self.template_dirs,
            "filters_count": len(self.global_context.filters),
            "functions_count": len(self.global_context.functions),
            "globals_count": len(self.global_context.globals)
        }

# ============================================================================
# 模板装饰器
# ============================================================================

def template(template_name: str, **default_context):
    """模板装饰器"""
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs):
            # 调用原函数获取上下文
            result = func(*args, **kwargs)
            
            # 如果返回字符串，直接返回
            if isinstance(result, str):
                return result
            
            # 如果返回字典，作为模板上下文
            context = default_context.copy()
            if isinstance(result, dict):
                context.update(result)
            
            # 渲染模板
            return global_template_engine.render(template_name, context)
        
        return wrapper
    
    return decorator

def render_template(template_name: str, **context) -> str:
    """渲染模板的便捷函数"""
    return global_template_engine.render(template_name, context)

def render_string(template_string: str, **context) -> str:
    """渲染模板字符串的便捷函数"""
    return global_template_engine.render_string(template_string, context)

# ============================================================================
# 全局模板引擎
# ============================================================================

# 全局模板引擎
global_template_engine = TemplateEngine()

# 便捷函数
def register_filter(name: str, filter_func: Callable):
    """注册过滤器的便捷函数"""
    global_template_engine.register_filter(name, filter_func)

def register_function(name: str, func: Callable):
    """注册函数的便捷函数"""
    global_template_engine.register_function(name, func)

def register_global(name: str, value: Any):
    """注册全局变量的便捷函数"""
    global_template_engine.register_global(name, value)

def add_template_dir(template_dir: str):
    """添加模板目录的便捷函数"""
    global_template_engine.add_template_dir(template_dir)

# ============================================================================
# 预定义模板设置
# ============================================================================

def setup_default_templates():
    """设置默认模板"""
    # 设置模板目录
    current_dir = os.path.dirname(os.path.abspath(__file__))
    app_dir = os.path.dirname(current_dir)
    
    template_dirs = [
        os.path.join(app_dir, 'templates'),
        os.path.join(app_dir, 'views'),
        os.path.join(app_dir, 'components')
    ]
    
    for template_dir in template_dirs:
        global_template_engine.add_template_dir(template_dir)
        
        # 创建目录（如果不存在）
        os.makedirs(template_dir, exist_ok=True)
        
        # 创建子目录
        for subdir in ['layouts', 'pages', 'components', 'partials', 'emails']:
            os.makedirs(os.path.join(template_dir, subdir), exist_ok=True)
    
    # 注册常用全局变量
    global_template_engine.register_global('app_name', 'Penny Scanner')
    global_template_engine.register_global('version', '1.0.0')

# 自动设置默认模板
setup_default_templates()