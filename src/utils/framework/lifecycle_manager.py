from typing import Dict, Any, List, Optional, Callable, Union, Type
from dataclasses import dataclass, field
from datetime import datetime
from abc import ABC, abstractmethod
from functools import wraps
import streamlit as st
import threading
import logging
import weakref
from enum import Enum
import inspect
import uuid

from .event_system import global_event_bus, Events
from .state_manager import global_state_manager

# ============================================================================
# 生命周期相关枚举和数据类
# ============================================================================

class ComponentState(Enum):
    """组件状态"""
    CREATED = "created"
    MOUNTING = "mounting"
    MOUNTED = "mounted"
    UPDATING = "updating"
    UPDATED = "updated"
    UNMOUNTING = "unmounting"
    UNMOUNTED = "unmounted"
    ERROR = "error"

class LifecyclePhase(Enum):
    """生命周期阶段"""
    BEFORE_MOUNT = "before_mount"
    AFTER_MOUNT = "after_mount"
    BEFORE_UPDATE = "before_update"
    AFTER_UPDATE = "after_update"
    BEFORE_UNMOUNT = "before_unmount"
    AFTER_UNMOUNT = "after_unmount"
    ON_ERROR = "on_error"

@dataclass
class ComponentInfo:
    """组件信息"""
    id: str
    name: str
    component_type: str
    state: ComponentState = ComponentState.CREATED
    created_at: datetime = field(default_factory=datetime.now)
    mounted_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    unmounted_at: Optional[datetime] = None
    props: Dict[str, Any] = field(default_factory=dict)
    context: Dict[str, Any] = field(default_factory=dict)
    parent_id: Optional[str] = None
    children_ids: List[str] = field(default_factory=list)
    error: Optional[Exception] = None
    render_count: int = 0
    update_count: int = 0
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "id": self.id,
            "name": self.name,
            "component_type": self.component_type,
            "state": self.state.value,
            "created_at": self.created_at.isoformat(),
            "mounted_at": self.mounted_at.isoformat() if self.mounted_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
            "unmounted_at": self.unmounted_at.isoformat() if self.unmounted_at else None,
            "props": self.props,
            "context": self.context,
            "parent_id": self.parent_id,
            "children_ids": self.children_ids,
            "error": str(self.error) if self.error else None,
            "render_count": self.render_count,
            "update_count": self.update_count
        }

# ============================================================================
# 生命周期钩子接口
# ============================================================================

class LifecycleHooks(ABC):
    """生命周期钩子抽象基类"""
    
    def before_mount(self, props: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """挂载前钩子"""
        return props
    
    def after_mount(self, props: Dict[str, Any], context: Dict[str, Any]):
        """挂载后钩子"""
        pass
    
    def before_update(self, new_props: Dict[str, Any], old_props: Dict[str, Any], 
                     context: Dict[str, Any]) -> bool:
        """更新前钩子，返回False可阻止更新"""
        return True
    
    def after_update(self, props: Dict[str, Any], context: Dict[str, Any]):
        """更新后钩子"""
        pass
    
    def before_unmount(self, props: Dict[str, Any], context: Dict[str, Any]):
        """卸载前钩子"""
        pass
    
    def after_unmount(self, props: Dict[str, Any], context: Dict[str, Any]):
        """卸载后钩子"""
        pass
    
    def on_error(self, error: Exception, props: Dict[str, Any], context: Dict[str, Any]):
        """错误处理钩子"""
        pass

# ============================================================================
# 组件基类
# ============================================================================

class StreamlitComponent(LifecycleHooks):
    """Streamlit组件基类"""
    
    def __init__(self, name: str = None, **props):
        self.id = str(uuid.uuid4())
        self.name = name or self.__class__.__name__
        self.props = props
        self.context = {}
        self.state = ComponentState.CREATED
        self.children: List['StreamlitComponent'] = []
        self.parent: Optional['StreamlitComponent'] = None
        self.logger = logging.getLogger(f"{self.__class__.__name__}[{self.id[:8]}]")
        self._mounted = False
        self._cleanup_callbacks: List[Callable] = []
        
        # 注册到生命周期管理器
        lifecycle_manager.register_component(self)
    
    def render(self) -> Any:
        """渲染组件 - 子类需要实现"""
        raise NotImplementedError("Subclasses must implement render method")
    
    def mount(self, **context) -> Any:
        """挂载组件"""
        return lifecycle_manager.mount_component(self.id, **context)
    
    def update(self, **new_props) -> Any:
        """更新组件"""
        return lifecycle_manager.update_component(self.id, **new_props)
    
    def unmount(self):
        """卸载组件"""
        lifecycle_manager.unmount_component(self.id)
    
    def add_child(self, child: 'StreamlitComponent'):
        """添加子组件"""
        child.parent = self
        self.children.append(child)
        lifecycle_manager.add_child_component(self.id, child.id)
    
    def remove_child(self, child: 'StreamlitComponent'):
        """移除子组件"""
        if child in self.children:
            child.parent = None
            self.children.remove(child)
            lifecycle_manager.remove_child_component(self.id, child.id)
    
    def add_cleanup(self, callback: Callable):
        """添加清理回调"""
        self._cleanup_callbacks.append(callback)
    
    def cleanup(self):
        """执行清理"""
        for callback in self._cleanup_callbacks:
            try:
                callback()
            except Exception as e:
                self.logger.error(f"Cleanup callback error: {e}")
        self._cleanup_callbacks.clear()
    
    def get_state(self, key: str, default: Any = None) -> Any:
        """获取组件状态"""
        return global_state_manager.get(f"component.{self.id}.{key}", default)
    
    def set_state(self, key: str, value: Any):
        """设置组件状态"""
        global_state_manager.set(f"component.{self.id}.{key}", value)
    
    def emit_event(self, event_name: str, data: Any = None):
        """发射事件"""
        global_event_bus.emit(
            event_name,
            data=data,
            source=f"{self.name}[{self.id[:8]}]"
        )
    
    def listen_event(self, event_name: str, callback: Callable) -> str:
        """监听事件"""
        listener_id = global_event_bus.on(event_name, callback)
        self.add_cleanup(lambda: global_event_bus.off(event_name, listener_id))
        return listener_id
    
    def __enter__(self):
        """上下文管理器入口"""
        self.mount()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        self.unmount()

# ============================================================================
# 生命周期管理器
# ============================================================================

class LifecycleManager:
    """生命周期管理器"""
    
    def __init__(self):
        self.components: Dict[str, ComponentInfo] = {}
        self.component_instances: Dict[str, weakref.ref] = {}
        self.lifecycle_hooks: Dict[str, List[Callable]] = {
            phase.value: [] for phase in LifecyclePhase
        }
        self.logger = logging.getLogger(self.__class__.__name__)
        self._lock = threading.Lock()
        
        # 监听Streamlit会话事件
        self._setup_session_hooks()
    
    def _setup_session_hooks(self):
        """设置会话钩子"""
        # 监听页面刷新/关闭事件
        if 'lifecycle_manager_initialized' not in st.session_state:
            st.session_state.lifecycle_manager_initialized = True
            
            # 注册会话结束时的清理
            def cleanup_session():
                self.cleanup_all_components()
            
            # 在Streamlit中注册清理函数
            if hasattr(st, 'on_session_end'):
                st.on_session_end(cleanup_session)
    
    def register_component(self, component: StreamlitComponent) -> str:
        """注册组件"""
        with self._lock:
            component_info = ComponentInfo(
                id=component.id,
                name=component.name,
                component_type=component.__class__.__name__,
                props=component.props.copy(),
                context=component.context.copy()
            )
            
            self.components[component.id] = component_info
            self.component_instances[component.id] = weakref.ref(component)
            
            self.logger.debug(f"Registered component: {component.name}[{component.id[:8]}]")
            
            # 发射注册事件
            global_event_bus.emit(
                "component.registered",
                data=component_info.to_dict(),
                source="lifecycle_manager"
            )
            
            return component.id
    
    def unregister_component(self, component_id: str) -> bool:
        """注销组件"""
        with self._lock:
            if component_id in self.components:
                component_info = self.components[component_id]
                
                # 先卸载组件
                if component_info.state not in [ComponentState.UNMOUNTED, ComponentState.ERROR]:
                    self.unmount_component(component_id)
                
                # 移除组件信息
                del self.components[component_id]
                if component_id in self.component_instances:
                    del self.component_instances[component_id]
                
                self.logger.debug(f"Unregistered component: {component_info.name}[{component_id[:8]}]")
                
                # 发射注销事件
                global_event_bus.emit(
                    "component.unregistered",
                    data=component_info.to_dict(),
                    source="lifecycle_manager"
                )
                
                return True
            
            return False
    
    def mount_component(self, component_id: str, **context) -> Any:
        """挂载组件"""
        if component_id not in self.components:
            raise ValueError(f"Component {component_id} not registered")
        
        component_info = self.components[component_id]
        component_ref = self.component_instances.get(component_id)
        
        if not component_ref or not component_ref():
            raise ValueError(f"Component {component_id} instance not found")
        
        component = component_ref()
        
        try:
            # 更新状态
            component_info.state = ComponentState.MOUNTING
            component_info.context.update(context)
            
            # 执行挂载前钩子
            self._execute_lifecycle_hooks(LifecyclePhase.BEFORE_MOUNT, component_info, component)
            component_info.props = component.before_mount(component_info.props, component_info.context)
            
            # 渲染组件
            result = component.render()
            
            # 更新状态
            component_info.state = ComponentState.MOUNTED
            component_info.mounted_at = datetime.now()
            component_info.render_count += 1
            component._mounted = True
            
            # 执行挂载后钩子
            component.after_mount(component_info.props, component_info.context)
            self._execute_lifecycle_hooks(LifecyclePhase.AFTER_MOUNT, component_info, component)
            
            self.logger.debug(f"Mounted component: {component_info.name}[{component_id[:8]}]")
            
            # 发射挂载事件
            global_event_bus.emit(
                "component.mounted",
                data=component_info.to_dict(),
                source="lifecycle_manager"
            )
            
            return result
            
        except Exception as e:
            # 处理错误
            component_info.state = ComponentState.ERROR
            component_info.error = e
            
            self.logger.error(f"Error mounting component {component_info.name}[{component_id[:8]}]: {e}")
            
            # 执行错误钩子
            try:
                component.on_error(e, component_info.props, component_info.context)
                self._execute_lifecycle_hooks(LifecyclePhase.ON_ERROR, component_info, component)
            except Exception as hook_error:
                self.logger.error(f"Error in error hook: {hook_error}")
            
            # 发射错误事件
            global_event_bus.emit(
                "component.error",
                data={"component": component_info.to_dict(), "error": str(e)},
                source="lifecycle_manager"
            )
            
            raise
    
    def update_component(self, component_id: str, **new_props) -> Any:
        """更新组件"""
        if component_id not in self.components:
            raise ValueError(f"Component {component_id} not registered")
        
        component_info = self.components[component_id]
        component_ref = self.component_instances.get(component_id)
        
        if not component_ref or not component_ref():
            raise ValueError(f"Component {component_id} instance not found")
        
        component = component_ref()
        
        if component_info.state != ComponentState.MOUNTED:
            self.logger.warning(f"Attempting to update unmounted component: {component_info.name}[{component_id[:8]}]")
            return None
        
        try:
            # 保存旧props
            old_props = component_info.props.copy()
            
            # 更新状态
            component_info.state = ComponentState.UPDATING
            
            # 执行更新前钩子
            self._execute_lifecycle_hooks(LifecyclePhase.BEFORE_UPDATE, component_info, component)
            should_update = component.before_update(new_props, old_props, component_info.context)
            
            if not should_update:
                component_info.state = ComponentState.MOUNTED
                return None
            
            # 更新props
            component_info.props.update(new_props)
            component.props.update(new_props)
            
            # 重新渲染
            result = component.render()
            
            # 更新状态
            component_info.state = ComponentState.MOUNTED
            component_info.updated_at = datetime.now()
            component_info.update_count += 1
            component_info.render_count += 1
            
            # 执行更新后钩子
            component.after_update(component_info.props, component_info.context)
            self._execute_lifecycle_hooks(LifecyclePhase.AFTER_UPDATE, component_info, component)
            
            self.logger.debug(f"Updated component: {component_info.name}[{component_id[:8]}]")
            
            # 发射更新事件
            global_event_bus.emit(
                "component.updated",
                data=component_info.to_dict(),
                source="lifecycle_manager"
            )
            
            return result
            
        except Exception as e:
            # 处理错误
            component_info.state = ComponentState.ERROR
            component_info.error = e
            
            self.logger.error(f"Error updating component {component_info.name}[{component_id[:8]}]: {e}")
            
            # 执行错误钩子
            try:
                component.on_error(e, component_info.props, component_info.context)
                self._execute_lifecycle_hooks(LifecyclePhase.ON_ERROR, component_info, component)
            except Exception as hook_error:
                self.logger.error(f"Error in error hook: {hook_error}")
            
            # 发射错误事件
            global_event_bus.emit(
                "component.error",
                data={"component": component_info.to_dict(), "error": str(e)},
                source="lifecycle_manager"
            )
            
            raise
    
    def unmount_component(self, component_id: str):
        """卸载组件"""
        if component_id not in self.components:
            return
        
        component_info = self.components[component_id]
        component_ref = self.component_instances.get(component_id)
        
        if component_ref and component_ref():
            component = component_ref()
            
            try:
                # 更新状态
                component_info.state = ComponentState.UNMOUNTING
                
                # 执行卸载前钩子
                self._execute_lifecycle_hooks(LifecyclePhase.BEFORE_UNMOUNT, component_info, component)
                component.before_unmount(component_info.props, component_info.context)
                
                # 清理组件
                component.cleanup()
                
                # 卸载所有子组件
                for child_id in component_info.children_ids.copy():
                    self.unmount_component(child_id)
                
                # 更新状态
                component_info.state = ComponentState.UNMOUNTED
                component_info.unmounted_at = datetime.now()
                component._mounted = False
                
                # 执行卸载后钩子
                component.after_unmount(component_info.props, component_info.context)
                self._execute_lifecycle_hooks(LifecyclePhase.AFTER_UNMOUNT, component_info, component)
                
                self.logger.debug(f"Unmounted component: {component_info.name}[{component_id[:8]}]")
                
                # 发射卸载事件
                global_event_bus.emit(
                    "component.unmounted",
                    data=component_info.to_dict(),
                    source="lifecycle_manager"
                )
                
            except Exception as e:
                self.logger.error(f"Error unmounting component {component_info.name}[{component_id[:8]}]: {e}")
                component_info.state = ComponentState.ERROR
                component_info.error = e
    
    def add_child_component(self, parent_id: str, child_id: str):
        """添加子组件"""
        if parent_id in self.components and child_id in self.components:
            parent_info = self.components[parent_id]
            child_info = self.components[child_id]
            
            if child_id not in parent_info.children_ids:
                parent_info.children_ids.append(child_id)
                child_info.parent_id = parent_id
                
                self.logger.debug(f"Added child {child_info.name}[{child_id[:8]}] to {parent_info.name}[{parent_id[:8]}]")
    
    def remove_child_component(self, parent_id: str, child_id: str):
        """移除子组件"""
        if parent_id in self.components and child_id in self.components:
            parent_info = self.components[parent_id]
            child_info = self.components[child_id]
            
            if child_id in parent_info.children_ids:
                parent_info.children_ids.remove(child_id)
                child_info.parent_id = None
                
                self.logger.debug(f"Removed child {child_info.name}[{child_id[:8]}] from {parent_info.name}[{parent_id[:8]}]")
    
    def add_lifecycle_hook(self, phase: LifecyclePhase, hook: Callable):
        """添加生命周期钩子"""
        self.lifecycle_hooks[phase.value].append(hook)
    
    def remove_lifecycle_hook(self, phase: LifecyclePhase, hook: Callable):
        """移除生命周期钩子"""
        if hook in self.lifecycle_hooks[phase.value]:
            self.lifecycle_hooks[phase.value].remove(hook)
    
    def _execute_lifecycle_hooks(self, phase: LifecyclePhase, component_info: ComponentInfo, component: StreamlitComponent):
        """执行生命周期钩子"""
        for hook in self.lifecycle_hooks[phase.value]:
            try:
                hook(component_info, component)
            except Exception as e:
                self.logger.error(f"Error in lifecycle hook {hook.__name__}: {e}")
    
    def get_component_info(self, component_id: str) -> Optional[ComponentInfo]:
        """获取组件信息"""
        return self.components.get(component_id)
    
    def get_all_components(self) -> Dict[str, ComponentInfo]:
        """获取所有组件信息"""
        return self.components.copy()
    
    def get_components_by_type(self, component_type: str) -> List[ComponentInfo]:
        """按类型获取组件"""
        return [info for info in self.components.values() if info.component_type == component_type]
    
    def get_components_by_state(self, state: ComponentState) -> List[ComponentInfo]:
        """按状态获取组件"""
        return [info for info in self.components.values() if info.state == state]
    
    def cleanup_all_components(self):
        """清理所有组件"""
        # 获取所有根组件（没有父组件的组件）
        root_components = [info for info in self.components.values() if info.parent_id is None]
        
        # 卸载所有根组件（会递归卸载子组件）
        for component_info in root_components:
            self.unmount_component(component_info.id)
        
        # 清理剩余组件
        for component_id in list(self.components.keys()):
            self.unregister_component(component_id)
        
        self.logger.info("Cleaned up all components")
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        total_components = len(self.components)
        state_counts = {}
        type_counts = {}
        
        for component_info in self.components.values():
            # 统计状态
            state = component_info.state.value
            state_counts[state] = state_counts.get(state, 0) + 1
            
            # 统计类型
            comp_type = component_info.component_type
            type_counts[comp_type] = type_counts.get(comp_type, 0) + 1
        
        return {
            "total_components": total_components,
            "state_distribution": state_counts,
            "type_distribution": type_counts,
            "total_renders": sum(info.render_count for info in self.components.values()),
            "total_updates": sum(info.update_count for info in self.components.values())
        }

# ============================================================================
# 生命周期装饰器
# ============================================================================

def lifecycle_component(name: str = None, auto_mount: bool = True):
    """生命周期组件装饰器"""
    def decorator(cls: Type[StreamlitComponent]) -> Type[StreamlitComponent]:
        original_init = cls.__init__
        
        @wraps(original_init)
        def new_init(self, *args, **kwargs):
            # 设置组件名称
            if name:
                kwargs['name'] = name
            
            original_init(self, *args, **kwargs)
            
            # 自动挂载
            if auto_mount and hasattr(st, 'session_state'):
                # 在Streamlit上下文中自动挂载
                if f'component_{self.id}_mounted' not in st.session_state:
                    st.session_state[f'component_{self.id}_mounted'] = True
                    self.mount()
        
        cls.__init__ = new_init
        return cls
    
    return decorator

def with_lifecycle(mount_on_create: bool = False):
    """为函数添加生命周期管理"""
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs):
            # 创建临时组件
            class FunctionComponent(StreamlitComponent):
                def __init__(self):
                    super().__init__(name=func.__name__)
                    self.func = func
                    self.args = args
                    self.kwargs = kwargs
                
                def render(self):
                    return self.func(*self.args, **self.kwargs)
            
            component = FunctionComponent()
            
            if mount_on_create:
                return component.mount()
            else:
                return component
        
        return wrapper
    
    return decorator

# ============================================================================
# 全局生命周期管理器实例
# ============================================================================

# 全局生命周期管理器
lifecycle_manager = LifecycleManager()

# 便捷函数
def register_component(component: StreamlitComponent) -> str:
    """注册组件的便捷函数"""
    return lifecycle_manager.register_component(component)

def mount_component(component_id: str, **context) -> Any:
    """挂载组件的便捷函数"""
    return lifecycle_manager.mount_component(component_id, **context)

def update_component(component_id: str, **new_props) -> Any:
    """更新组件的便捷函数"""
    return lifecycle_manager.update_component(component_id, **new_props)

def unmount_component(component_id: str):
    """卸载组件的便捷函数"""
    lifecycle_manager.unmount_component(component_id)

def add_lifecycle_hook(phase: LifecyclePhase, hook: Callable):
    """添加生命周期钩子的便捷函数"""
    lifecycle_manager.add_lifecycle_hook(phase, hook)

# ============================================================================
# 预定义组件类
# ============================================================================

class TabComponent(StreamlitComponent):
    """Tab组件基类"""
    
    def __init__(self, tab_name: str, **props):
        super().__init__(name=f"Tab_{tab_name}", **props)
        self.tab_name = tab_name
    
    def render(self):
        """渲染Tab内容"""
        with st.container():
            return self.render_content()
    
    def render_content(self):
        """渲染Tab具体内容 - 子类需要实现"""
        st.write(f"Tab: {self.tab_name}")

class WidgetComponent(StreamlitComponent):
    """Widget组件基类"""
    
    def __init__(self, widget_type: str, **props):
        super().__init__(name=f"Widget_{widget_type}", **props)
        self.widget_type = widget_type
    
    def render(self):
        """渲染Widget"""
        return self.render_widget()
    
    def render_widget(self):
        """渲染Widget具体内容 - 子类需要实现"""
        st.write(f"Widget: {self.widget_type}")

class ChartComponent(StreamlitComponent):
    """图表组件基类"""
    
    def __init__(self, chart_type: str, data: Any = None, **props):
        super().__init__(name=f"Chart_{chart_type}", data=data, **props)
        self.chart_type = chart_type
        self.data = data
    
    def render(self):
        """渲染图表"""
        if self.data is not None:
            return self.render_chart(self.data)
        else:
            st.warning(f"No data provided for {self.chart_type} chart")
    
    def render_chart(self, data: Any):
        """渲染图表具体内容 - 子类需要实现"""
        st.write(f"Chart: {self.chart_type}")
        st.write(data)