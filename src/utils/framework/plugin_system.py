from typing import Dict, Any, List, Optional, Callable, Union, Type, TypeVar, Set
from dataclasses import dataclass, field
from abc import ABC, abstractmethod
from functools import wraps
import importlib
import inspect
import os
import sys
import json
import logging
from enum import Enum
import threading
import weakref
from pathlib import Path
import traceback
from collections import defaultdict
import time

# ============================================================================
# 插件相关类型和枚举
# ============================================================================

T = TypeVar('T')

class PluginStatus(Enum):
    """插件状态"""
    UNLOADED = "unloaded"      # 未加载
    LOADING = "loading"        # 加载中
    LOADED = "loaded"          # 已加载
    ACTIVE = "active"          # 激活
    INACTIVE = "inactive"      # 未激活
    ERROR = "error"            # 错误
    DISABLED = "disabled"      # 禁用

class PluginType(Enum):
    """插件类型"""
    CORE = "core"              # 核心插件
    EXTENSION = "extension"    # 扩展插件
    THEME = "theme"            # 主题插件
    WIDGET = "widget"          # 组件插件
    MIDDLEWARE = "middleware"  # 中间件插件
    SERVICE = "service"        # 服务插件
    CUSTOM = "custom"          # 自定义插件

class HookType(Enum):
    """钩子类型"""
    ACTION = "action"          # 动作钩子
    FILTER = "filter"          # 过滤器钩子
    EVENT = "event"            # 事件钩子

@dataclass
class PluginMetadata:
    """插件元数据"""
    name: str
    version: str
    description: str = ""
    author: str = ""
    email: str = ""
    website: str = ""
    license: str = ""
    plugin_type: PluginType = PluginType.CUSTOM
    dependencies: List[str] = field(default_factory=list)
    requirements: List[str] = field(default_factory=list)
    min_app_version: str = ""
    max_app_version: str = ""
    tags: List[str] = field(default_factory=list)
    category: str = ""
    priority: int = 0
    auto_activate: bool = False
    settings_schema: Dict[str, Any] = field(default_factory=dict)
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'PluginMetadata':
        """从字典创建"""
        plugin_type = PluginType(data.get('plugin_type', 'custom'))
        return cls(
            name=data['name'],
            version=data['version'],
            description=data.get('description', ''),
            author=data.get('author', ''),
            email=data.get('email', ''),
            website=data.get('website', ''),
            license=data.get('license', ''),
            plugin_type=plugin_type,
            dependencies=data.get('dependencies', []),
            requirements=data.get('requirements', []),
            min_app_version=data.get('min_app_version', ''),
            max_app_version=data.get('max_app_version', ''),
            tags=data.get('tags', []),
            category=data.get('category', ''),
            priority=data.get('priority', 0),
            auto_activate=data.get('auto_activate', False),
            settings_schema=data.get('settings_schema', {})
        )
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'name': self.name,
            'version': self.version,
            'description': self.description,
            'author': self.author,
            'email': self.email,
            'website': self.website,
            'license': self.license,
            'plugin_type': self.plugin_type.value,
            'dependencies': self.dependencies,
            'requirements': self.requirements,
            'min_app_version': self.min_app_version,
            'max_app_version': self.max_app_version,
            'tags': self.tags,
            'category': self.category,
            'priority': self.priority,
            'auto_activate': self.auto_activate,
            'settings_schema': self.settings_schema
        }

@dataclass
class PluginInfo:
    """插件信息"""
    metadata: PluginMetadata
    status: PluginStatus = PluginStatus.UNLOADED
    module_path: str = ""
    module: Optional[Any] = None
    instance: Optional[Any] = None
    load_time: Optional[float] = None
    error_message: str = ""
    settings: Dict[str, Any] = field(default_factory=dict)
    hooks: List[str] = field(default_factory=list)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'metadata': self.metadata.to_dict(),
            'status': self.status.value,
            'module_path': self.module_path,
            'load_time': self.load_time,
            'error_message': self.error_message,
            'settings': self.settings,
            'hooks': self.hooks
        }

@dataclass
class Hook:
    """钩子"""
    name: str
    hook_type: HookType
    callback: Callable
    priority: int = 0
    plugin_name: str = ""
    description: str = ""
    
    def __call__(self, *args, **kwargs):
        """调用钩子"""
        return self.callback(*args, **kwargs)

# ============================================================================
# 插件接口
# ============================================================================

class IPlugin(ABC):
    """插件接口"""
    
    @abstractmethod
    def get_metadata(self) -> PluginMetadata:
        """获取插件元数据"""
        pass
    
    def on_load(self):
        """插件加载时调用"""
        pass
    
    def on_activate(self):
        """插件激活时调用"""
        pass
    
    def on_deactivate(self):
        """插件停用时调用"""
        pass
    
    def on_unload(self):
        """插件卸载时调用"""
        pass
    
    def on_install(self):
        """插件安装时调用"""
        pass
    
    def on_uninstall(self):
        """插件卸载时调用"""
        pass
    
    def get_settings_schema(self) -> Dict[str, Any]:
        """获取设置模式"""
        return {}
    
    def get_default_settings(self) -> Dict[str, Any]:
        """获取默认设置"""
        return {}
    
    def validate_settings(self, settings: Dict[str, Any]) -> bool:
        """验证设置"""
        return True
    
    def get_hooks(self) -> List[Hook]:
        """获取钩子"""
        return []

class IPluginManager(ABC):
    """插件管理器接口"""
    
    @abstractmethod
    def load_plugin(self, plugin_path: str) -> bool:
        """加载插件"""
        pass
    
    @abstractmethod
    def unload_plugin(self, plugin_name: str) -> bool:
        """卸载插件"""
        pass
    
    @abstractmethod
    def activate_plugin(self, plugin_name: str) -> bool:
        """激活插件"""
        pass
    
    @abstractmethod
    def deactivate_plugin(self, plugin_name: str) -> bool:
        """停用插件"""
        pass
    
    @abstractmethod
    def get_plugin_info(self, plugin_name: str) -> Optional[PluginInfo]:
        """获取插件信息"""
        pass
    
    @abstractmethod
    def get_all_plugins(self) -> Dict[str, PluginInfo]:
        """获取所有插件"""
        pass

# ============================================================================
# 钩子系统
# ============================================================================

class HookManager:
    """钩子管理器"""
    
    def __init__(self):
        self.hooks: Dict[str, List[Hook]] = defaultdict(list)
        self.logger = logging.getLogger(self.__class__.__name__)
        self._lock = threading.Lock()
    
    def add_hook(self, hook: Hook):
        """添加钩子"""
        with self._lock:
            self.hooks[hook.name].append(hook)
            # 按优先级排序
            self.hooks[hook.name].sort(key=lambda h: h.priority, reverse=True)
            
            self.logger.debug(f"Added hook: {hook.name} from plugin {hook.plugin_name}")
    
    def remove_hook(self, hook_name: str, plugin_name: str = None):
        """移除钩子"""
        with self._lock:
            if hook_name in self.hooks:
                if plugin_name:
                    # 移除特定插件的钩子
                    self.hooks[hook_name] = [
                        h for h in self.hooks[hook_name] 
                        if h.plugin_name != plugin_name
                    ]
                else:
                    # 移除所有钩子
                    del self.hooks[hook_name]
                
                self.logger.debug(f"Removed hook: {hook_name} from plugin {plugin_name or 'all'}")
    
    def remove_plugin_hooks(self, plugin_name: str):
        """移除插件的所有钩子"""
        with self._lock:
            for hook_name in list(self.hooks.keys()):
                self.hooks[hook_name] = [
                    h for h in self.hooks[hook_name] 
                    if h.plugin_name != plugin_name
                ]
                
                # 如果没有钩子了，删除键
                if not self.hooks[hook_name]:
                    del self.hooks[hook_name]
            
            self.logger.debug(f"Removed all hooks from plugin: {plugin_name}")
    
    def do_action(self, action_name: str, *args, **kwargs):
        """执行动作钩子"""
        if action_name in self.hooks:
            for hook in self.hooks[action_name]:
                if hook.hook_type == HookType.ACTION:
                    try:
                        hook(*args, **kwargs)
                    except Exception as e:
                        self.logger.error(f"Error in action hook {action_name} from {hook.plugin_name}: {e}")
    
    def apply_filters(self, filter_name: str, value: Any, *args, **kwargs) -> Any:
        """应用过滤器钩子"""
        if filter_name in self.hooks:
            for hook in self.hooks[filter_name]:
                if hook.hook_type == HookType.FILTER:
                    try:
                        value = hook(value, *args, **kwargs)
                    except Exception as e:
                        self.logger.error(f"Error in filter hook {filter_name} from {hook.plugin_name}: {e}")
        
        return value
    
    def trigger_event(self, event_name: str, *args, **kwargs):
        """触发事件钩子"""
        if event_name in self.hooks:
            for hook in self.hooks[event_name]:
                if hook.hook_type == HookType.EVENT:
                    try:
                        hook(*args, **kwargs)
                    except Exception as e:
                        self.logger.error(f"Error in event hook {event_name} from {hook.plugin_name}: {e}")
    
    def has_hook(self, hook_name: str) -> bool:
        """检查是否有钩子"""
        return hook_name in self.hooks and len(self.hooks[hook_name]) > 0
    
    def get_hooks(self, hook_name: str) -> List[Hook]:
        """获取钩子列表"""
        return self.hooks.get(hook_name, [])
    
    def get_all_hooks(self) -> Dict[str, List[Hook]]:
        """获取所有钩子"""
        return dict(self.hooks)
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        hook_counts = {}
        plugin_counts = defaultdict(int)
        
        for hook_name, hooks in self.hooks.items():
            hook_counts[hook_name] = len(hooks)
            for hook in hooks:
                plugin_counts[hook.plugin_name] += 1
        
        return {
            "total_hooks": sum(len(hooks) for hooks in self.hooks.values()),
            "hook_names": len(self.hooks),
            "hook_counts": hook_counts,
            "plugin_hook_counts": dict(plugin_counts)
        }

# ============================================================================
# 插件管理器实现
# ============================================================================

class PluginManager(IPluginManager):
    """插件管理器实现"""
    
    def __init__(self, plugin_dirs: List[str] = None):
        self.plugin_dirs = plugin_dirs or []
        self.plugins: Dict[str, PluginInfo] = {}
        self.hook_manager = HookManager()
        self.logger = logging.getLogger(self.__class__.__name__)
        self._lock = threading.Lock()
        
        # 插件设置存储
        self.settings_storage: Dict[str, Dict[str, Any]] = {}
        
        # 依赖关系图
        self.dependency_graph: Dict[str, Set[str]] = defaultdict(set)
        
        # 统计信息
        self.load_stats = {
            "total_loaded": 0,
            "total_activated": 0,
            "total_errors": 0,
            "load_times": []
        }
    
    def add_plugin_dir(self, plugin_dir: str):
        """添加插件目录"""
        if plugin_dir not in self.plugin_dirs:
            self.plugin_dirs.append(plugin_dir)
            self.logger.debug(f"Added plugin directory: {plugin_dir}")
    
    def discover_plugins(self) -> List[str]:
        """发现插件"""
        plugin_files = []
        
        for plugin_dir in self.plugin_dirs:
            if not os.path.exists(plugin_dir):
                continue
            
            for root, dirs, files in os.walk(plugin_dir):
                # 查找插件文件
                for file in files:
                    if file.endswith('.py') and not file.startswith('__'):
                        plugin_files.append(os.path.join(root, file))
                
                # 查找插件包
                for dir_name in dirs:
                    if not dir_name.startswith('__'):
                        plugin_init = os.path.join(root, dir_name, '__init__.py')
                        if os.path.exists(plugin_init):
                            plugin_files.append(plugin_init)
        
        self.logger.info(f"Discovered {len(plugin_files)} potential plugins")
        return plugin_files
    
    def load_plugin(self, plugin_path: str) -> bool:
        """加载插件"""
        start_time = time.time()
        
        try:
            # 获取模块名
            module_name = self._get_module_name(plugin_path)
            
            # 加载模块
            spec = importlib.util.spec_from_file_location(module_name, plugin_path)
            if spec is None or spec.loader is None:
                raise ImportError(f"Cannot load module from {plugin_path}")
            
            module = importlib.util.module_from_spec(spec)
            
            # 添加到sys.modules
            sys.modules[module_name] = module
            
            # 执行模块
            spec.loader.exec_module(module)
            
            # 查找插件类
            plugin_class = self._find_plugin_class(module)
            if plugin_class is None:
                raise ValueError(f"No plugin class found in {plugin_path}")
            
            # 创建插件实例
            plugin_instance = plugin_class()
            
            # 获取元数据
            metadata = plugin_instance.get_metadata()
            
            # 检查依赖
            if not self._check_dependencies(metadata):
                raise ValueError(f"Dependencies not satisfied for plugin {metadata.name}")
            
            # 创建插件信息
            plugin_info = PluginInfo(
                metadata=metadata,
                status=PluginStatus.LOADED,
                module_path=plugin_path,
                module=module,
                instance=plugin_instance,
                load_time=time.time() - start_time
            )
            
            with self._lock:
                self.plugins[metadata.name] = plugin_info
                
                # 更新依赖图
                self.dependency_graph[metadata.name] = set(metadata.dependencies)
                
                # 更新统计
                self.load_stats["total_loaded"] += 1
                self.load_stats["load_times"].append(plugin_info.load_time)
            
            # 调用加载回调
            plugin_instance.on_load()
            
            # 注册钩子
            self._register_plugin_hooks(plugin_instance, metadata.name)
            
            self.logger.info(f"Loaded plugin: {metadata.name} v{metadata.version} in {plugin_info.load_time:.3f}s")
            
            # 如果设置了自动激活，则激活插件
            if metadata.auto_activate:
                self.activate_plugin(metadata.name)
            
            return True
        
        except Exception as e:
            error_msg = f"Failed to load plugin from {plugin_path}: {str(e)}"
            self.logger.error(error_msg)
            self.logger.debug(traceback.format_exc())
            
            # 记录错误信息
            if 'metadata' in locals():
                plugin_info = PluginInfo(
                    metadata=metadata,
                    status=PluginStatus.ERROR,
                    module_path=plugin_path,
                    error_message=error_msg
                )
                with self._lock:
                    self.plugins[metadata.name] = plugin_info
                    self.load_stats["total_errors"] += 1
            
            return False
    
    def unload_plugin(self, plugin_name: str) -> bool:
        """卸载插件"""
        if plugin_name not in self.plugins:
            return False
        
        plugin_info = self.plugins[plugin_name]
        
        try:
            # 先停用插件
            if plugin_info.status == PluginStatus.ACTIVE:
                self.deactivate_plugin(plugin_name)
            
            # 调用卸载回调
            if plugin_info.instance:
                plugin_info.instance.on_unload()
            
            # 移除钩子
            self.hook_manager.remove_plugin_hooks(plugin_name)
            
            # 从sys.modules移除
            if plugin_info.module:
                module_name = plugin_info.module.__name__
                if module_name in sys.modules:
                    del sys.modules[module_name]
            
            with self._lock:
                # 移除插件
                del self.plugins[plugin_name]
                
                # 移除依赖图
                if plugin_name in self.dependency_graph:
                    del self.dependency_graph[plugin_name]
                
                # 更新统计
                self.load_stats["total_loaded"] -= 1
            
            self.logger.info(f"Unloaded plugin: {plugin_name}")
            return True
        
        except Exception as e:
            self.logger.error(f"Failed to unload plugin {plugin_name}: {str(e)}")
            return False
    
    def activate_plugin(self, plugin_name: str) -> bool:
        """激活插件"""
        if plugin_name not in self.plugins:
            return False
        
        plugin_info = self.plugins[plugin_name]
        
        if plugin_info.status != PluginStatus.LOADED:
            return False
        
        try:
            # 检查并激活依赖
            for dep_name in plugin_info.metadata.dependencies:
                if dep_name in self.plugins:
                    dep_info = self.plugins[dep_name]
                    if dep_info.status != PluginStatus.ACTIVE:
                        if not self.activate_plugin(dep_name):
                            raise ValueError(f"Failed to activate dependency: {dep_name}")
                else:
                    raise ValueError(f"Dependency not found: {dep_name}")
            
            # 调用激活回调
            if plugin_info.instance:
                plugin_info.instance.on_activate()
            
            # 更新状态
            plugin_info.status = PluginStatus.ACTIVE
            
            with self._lock:
                self.load_stats["total_activated"] += 1
            
            self.logger.info(f"Activated plugin: {plugin_name}")
            
            # 触发激活事件
            self.hook_manager.do_action("plugin_activated", plugin_name, plugin_info)
            
            return True
        
        except Exception as e:
            self.logger.error(f"Failed to activate plugin {plugin_name}: {str(e)}")
            plugin_info.status = PluginStatus.ERROR
            plugin_info.error_message = str(e)
            return False
    
    def deactivate_plugin(self, plugin_name: str) -> bool:
        """停用插件"""
        if plugin_name not in self.plugins:
            return False
        
        plugin_info = self.plugins[plugin_name]
        
        if plugin_info.status != PluginStatus.ACTIVE:
            return False
        
        try:
            # 检查是否有其他插件依赖此插件
            dependents = self._get_dependents(plugin_name)
            if dependents:
                # 先停用依赖此插件的其他插件
                for dependent in dependents:
                    if self.plugins[dependent].status == PluginStatus.ACTIVE:
                        self.deactivate_plugin(dependent)
            
            # 调用停用回调
            if plugin_info.instance:
                plugin_info.instance.on_deactivate()
            
            # 更新状态
            plugin_info.status = PluginStatus.LOADED
            
            with self._lock:
                self.load_stats["total_activated"] -= 1
            
            self.logger.info(f"Deactivated plugin: {plugin_name}")
            
            # 触发停用事件
            self.hook_manager.do_action("plugin_deactivated", plugin_name, plugin_info)
            
            return True
        
        except Exception as e:
            self.logger.error(f"Failed to deactivate plugin {plugin_name}: {str(e)}")
            return False
    
    def get_plugin_info(self, plugin_name: str) -> Optional[PluginInfo]:
        """获取插件信息"""
        return self.plugins.get(plugin_name)
    
    def get_all_plugins(self) -> Dict[str, PluginInfo]:
        """获取所有插件"""
        return self.plugins.copy()
    
    def get_active_plugins(self) -> Dict[str, PluginInfo]:
        """获取激活的插件"""
        return {
            name: info for name, info in self.plugins.items() 
            if info.status == PluginStatus.ACTIVE
        }
    
    def get_plugins_by_type(self, plugin_type: PluginType) -> Dict[str, PluginInfo]:
        """按类型获取插件"""
        return {
            name: info for name, info in self.plugins.items() 
            if info.metadata.plugin_type == plugin_type
        }
    
    def reload_plugin(self, plugin_name: str) -> bool:
        """重新加载插件"""
        if plugin_name not in self.plugins:
            return False
        
        plugin_info = self.plugins[plugin_name]
        plugin_path = plugin_info.module_path
        
        # 卸载插件
        if not self.unload_plugin(plugin_name):
            return False
        
        # 重新加载插件
        return self.load_plugin(plugin_path)
    
    def load_all_plugins(self) -> Dict[str, bool]:
        """加载所有发现的插件"""
        plugin_files = self.discover_plugins()
        results = {}
        
        for plugin_file in plugin_files:
            try:
                results[plugin_file] = self.load_plugin(plugin_file)
            except Exception as e:
                self.logger.error(f"Error loading plugin {plugin_file}: {e}")
                results[plugin_file] = False
        
        return results
    
    def get_plugin_settings(self, plugin_name: str) -> Dict[str, Any]:
        """获取插件设置"""
        return self.settings_storage.get(plugin_name, {})
    
    def set_plugin_settings(self, plugin_name: str, settings: Dict[str, Any]) -> bool:
        """设置插件设置"""
        if plugin_name not in self.plugins:
            return False
        
        plugin_info = self.plugins[plugin_name]
        
        # 验证设置
        if plugin_info.instance and hasattr(plugin_info.instance, 'validate_settings'):
            if not plugin_info.instance.validate_settings(settings):
                return False
        
        self.settings_storage[plugin_name] = settings
        plugin_info.settings = settings
        
        self.logger.debug(f"Updated settings for plugin: {plugin_name}")
        return True
    
    def _get_module_name(self, plugin_path: str) -> str:
        """获取模块名"""
        path = Path(plugin_path)
        if path.name == '__init__.py':
            return f"plugin_{path.parent.name}"
        else:
            return f"plugin_{path.stem}"
    
    def _find_plugin_class(self, module) -> Optional[Type[IPlugin]]:
        """查找插件类"""
        for name in dir(module):
            obj = getattr(module, name)
            if (inspect.isclass(obj) and 
                issubclass(obj, IPlugin) and 
                obj != IPlugin):
                return obj
        return None
    
    def _check_dependencies(self, metadata: PluginMetadata) -> bool:
        """检查依赖"""
        for dep_name in metadata.dependencies:
            if dep_name not in self.plugins:
                self.logger.warning(f"Dependency not found: {dep_name}")
                return False
            
            dep_info = self.plugins[dep_name]
            if dep_info.status == PluginStatus.ERROR:
                self.logger.warning(f"Dependency has error: {dep_name}")
                return False
        
        return True
    
    def _get_dependents(self, plugin_name: str) -> List[str]:
        """获取依赖此插件的其他插件"""
        dependents = []
        for name, deps in self.dependency_graph.items():
            if plugin_name in deps:
                dependents.append(name)
        return dependents
    
    def _register_plugin_hooks(self, plugin_instance: IPlugin, plugin_name: str):
        """注册插件钩子"""
        if hasattr(plugin_instance, 'get_hooks'):
            hooks = plugin_instance.get_hooks()
            for hook in hooks:
                hook.plugin_name = plugin_name
                self.hook_manager.add_hook(hook)
                
                # 记录钩子
                if plugin_name in self.plugins:
                    self.plugins[plugin_name].hooks.append(hook.name)
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        status_counts = defaultdict(int)
        type_counts = defaultdict(int)
        
        for plugin_info in self.plugins.values():
            status_counts[plugin_info.status.value] += 1
            type_counts[plugin_info.metadata.plugin_type.value] += 1
        
        avg_load_time = (
            sum(self.load_stats["load_times"]) / len(self.load_stats["load_times"])
            if self.load_stats["load_times"] else 0
        )
        
        return {
            "total_plugins": len(self.plugins),
            "status_distribution": dict(status_counts),
            "type_distribution": dict(type_counts),
            "load_stats": {
                **self.load_stats,
                "average_load_time": avg_load_time
            },
            "hook_stats": self.hook_manager.get_stats(),
            "plugin_directories": self.plugin_dirs
        }

# ============================================================================
# 插件装饰器
# ============================================================================

def plugin(name: str = None, version: str = "1.0.0", 
          plugin_type: PluginType = PluginType.CUSTOM,
          auto_activate: bool = False, **metadata_kwargs):
    """插件装饰器"""
    def decorator(cls: Type[IPlugin]) -> Type[IPlugin]:
        # 创建元数据
        plugin_name = name or cls.__name__
        
        # 保存原始的get_metadata方法
        original_get_metadata = getattr(cls, 'get_metadata', None)
        
        def get_metadata(self) -> PluginMetadata:
            if original_get_metadata:
                return original_get_metadata()
            else:
                return PluginMetadata(
                    name=plugin_name,
                    version=version,
                    plugin_type=plugin_type,
                    auto_activate=auto_activate,
                    **metadata_kwargs
                )
        
        # 替换get_metadata方法
        cls.get_metadata = get_metadata
        
        # 添加插件元数据属性
        cls._plugin_name = plugin_name
        cls._plugin_version = version
        cls._plugin_type = plugin_type
        
        return cls
    
    return decorator

def hook(hook_name: str, hook_type: HookType = HookType.ACTION, priority: int = 0):
    """钩子装饰器"""
    def decorator(func: Callable) -> Callable:
        # 添加钩子元数据
        func._hook_name = hook_name
        func._hook_type = hook_type
        func._hook_priority = priority
        
        return func
    
    return decorator

# ============================================================================
# 全局插件管理器
# ============================================================================

# 全局插件管理器
global_plugin_manager = PluginManager()

# 全局钩子管理器
global_hook_manager = global_plugin_manager.hook_manager

# 便捷函数
def load_plugin(plugin_path: str) -> bool:
    """加载插件的便捷函数"""
    return global_plugin_manager.load_plugin(plugin_path)

def activate_plugin(plugin_name: str) -> bool:
    """激活插件的便捷函数"""
    return global_plugin_manager.activate_plugin(plugin_name)

def deactivate_plugin(plugin_name: str) -> bool:
    """停用插件的便捷函数"""
    return global_plugin_manager.deactivate_plugin(plugin_name)

def do_action(action_name: str, *args, **kwargs):
    """执行动作的便捷函数"""
    global_hook_manager.do_action(action_name, *args, **kwargs)

def apply_filters(filter_name: str, value: Any, *args, **kwargs) -> Any:
    """应用过滤器的便捷函数"""
    return global_hook_manager.apply_filters(filter_name, value, *args, **kwargs)

def trigger_event(event_name: str, *args, **kwargs):
    """触发事件的便捷函数"""
    global_hook_manager.trigger_event(event_name, *args, **kwargs)

def add_plugin_dir(plugin_dir: str):
    """添加插件目录的便捷函数"""
    global_plugin_manager.add_plugin_dir(plugin_dir)

# ============================================================================
# 预定义插件目录设置
# ============================================================================

def setup_default_plugin_dirs():
    """设置默认插件目录"""
    # 添加默认插件目录
    current_dir = os.path.dirname(os.path.abspath(__file__))
    app_dir = os.path.dirname(current_dir)
    
    plugin_dirs = [
        os.path.join(app_dir, 'plugins'),
        os.path.join(app_dir, 'extensions'),
        os.path.expanduser('~/.cauldron/plugins')
    ]
    
    for plugin_dir in plugin_dirs:
        global_plugin_manager.add_plugin_dir(plugin_dir)
        
        # 创建目录（如果不存在）
        os.makedirs(plugin_dir, exist_ok=True)

# 自动设置默认插件目录
setup_default_plugin_dirs()