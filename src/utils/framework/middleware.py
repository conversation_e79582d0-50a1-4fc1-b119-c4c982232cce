from typing import Dict, Any, List, Optional, Callable, Union, Type, TypeVar, Awaitable
from dataclasses import dataclass, field
from abc import ABC, abstractmethod
from functools import wraps
import asyncio
import time
import logging
from enum import Enum
import inspect
from contextlib import contextmanager
import threading
import uuid
from collections import defaultdict

# ============================================================================
# 中间件相关类型和枚举
# ============================================================================

T = TypeVar('T')
NextFunction = Callable[[], Any]
AsyncNextFunction = Callable[[], Awaitable[Any]]

class MiddlewareType(Enum):
    """中间件类型"""
    REQUEST = "request"          # 请求中间件
    RESPONSE = "response"        # 响应中间件
    ERROR = "error"              # 错误处理中间件
    AUTHENTICATION = "auth"      # 认证中间件
    AUTHORIZATION = "authz"      # 授权中间件
    LOGGING = "logging"          # 日志中间件
    CACHING = "caching"          # 缓存中间件
    VALIDATION = "validation"    # 验证中间件
    TRANSFORMATION = "transform" # 数据转换中间件
    CUSTOM = "custom"            # 自定义中间件

class ExecutionOrder(Enum):
    """执行顺序"""
    BEFORE = "before"  # 在主处理之前
    AFTER = "after"    # 在主处理之后
    AROUND = "around"  # 包围主处理

@dataclass
class MiddlewareContext:
    """中间件上下文"""
    request_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    data: Dict[str, Any] = field(default_factory=dict)
    metadata: Dict[str, Any] = field(default_factory=dict)
    start_time: float = field(default_factory=time.time)
    user_id: Optional[str] = None
    session_id: Optional[str] = None
    trace_id: Optional[str] = None
    
    def get(self, key: str, default: Any = None) -> Any:
        """获取数据"""
        return self.data.get(key, default)
    
    def set(self, key: str, value: Any):
        """设置数据"""
        self.data[key] = value
    
    def has(self, key: str) -> bool:
        """检查是否有数据"""
        return key in self.data
    
    def remove(self, key: str) -> Any:
        """移除数据"""
        return self.data.pop(key, None)
    
    def get_elapsed_time(self) -> float:
        """获取经过的时间"""
        return time.time() - self.start_time
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "request_id": self.request_id,
            "data": self.data,
            "metadata": self.metadata,
            "start_time": self.start_time,
            "user_id": self.user_id,
            "session_id": self.session_id,
            "trace_id": self.trace_id,
            "elapsed_time": self.get_elapsed_time()
        }

@dataclass
class MiddlewareInfo:
    """中间件信息"""
    name: str
    middleware_type: MiddlewareType
    priority: int = 0
    enabled: bool = True
    execution_order: ExecutionOrder = ExecutionOrder.BEFORE
    conditions: List[Callable[[MiddlewareContext], bool]] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)
    execution_count: int = 0
    total_execution_time: float = 0.0
    error_count: int = 0
    
    def should_execute(self, context: MiddlewareContext) -> bool:
        """检查是否应该执行"""
        if not self.enabled:
            return False
        
        for condition in self.conditions:
            if not condition(context):
                return False
        
        return True
    
    def record_execution(self, execution_time: float, success: bool = True):
        """记录执行信息"""
        self.execution_count += 1
        self.total_execution_time += execution_time
        if not success:
            self.error_count += 1
    
    def get_average_execution_time(self) -> float:
        """获取平均执行时间"""
        return self.total_execution_time / self.execution_count if self.execution_count > 0 else 0.0
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "name": self.name,
            "type": self.middleware_type.value,
            "priority": self.priority,
            "enabled": self.enabled,
            "execution_order": self.execution_order.value,
            "execution_count": self.execution_count,
            "total_execution_time": self.total_execution_time,
            "average_execution_time": self.get_average_execution_time(),
            "error_count": self.error_count,
            "success_rate": (self.execution_count - self.error_count) / self.execution_count if self.execution_count > 0 else 0.0,
            "metadata": self.metadata
        }

# ============================================================================
# 中间件接口
# ============================================================================

class IMiddleware(ABC):
    """中间件接口"""
    
    @abstractmethod
    def execute(self, context: MiddlewareContext, next_func: NextFunction) -> Any:
        """执行中间件"""
        pass
    
    def get_name(self) -> str:
        """获取中间件名称"""
        return self.__class__.__name__
    
    def get_type(self) -> MiddlewareType:
        """获取中间件类型"""
        return MiddlewareType.CUSTOM
    
    def get_priority(self) -> int:
        """获取优先级"""
        return 0
    
    def is_enabled(self) -> bool:
        """是否启用"""
        return True

class IAsyncMiddleware(ABC):
    """异步中间件接口"""
    
    @abstractmethod
    async def execute(self, context: MiddlewareContext, next_func: AsyncNextFunction) -> Any:
        """执行异步中间件"""
        pass
    
    def get_name(self) -> str:
        """获取中间件名称"""
        return self.__class__.__name__
    
    def get_type(self) -> MiddlewareType:
        """获取中间件类型"""
        return MiddlewareType.CUSTOM
    
    def get_priority(self) -> int:
        """获取优先级"""
        return 0
    
    def is_enabled(self) -> bool:
        """是否启用"""
        return True

# ============================================================================
# 中间件管道
# ============================================================================

class MiddlewarePipeline:
    """中间件管道"""
    
    def __init__(self, name: str = "default"):
        self.name = name
        self.middlewares: List[IMiddleware] = []
        self.async_middlewares: List[IAsyncMiddleware] = []
        self.middleware_info: Dict[str, MiddlewareInfo] = {}
        self.logger = logging.getLogger(f"{self.__class__.__name__}[{name}]")
        self._lock = threading.Lock()
        self.global_context: Dict[str, Any] = {}
        
        # 性能统计
        self.total_executions = 0
        self.total_execution_time = 0.0
        self.error_count = 0
    
    def use(self, middleware: Union[IMiddleware, IAsyncMiddleware], 
           middleware_type: MiddlewareType = None,
           priority: int = 0,
           execution_order: ExecutionOrder = ExecutionOrder.BEFORE,
           conditions: List[Callable[[MiddlewareContext], bool]] = None,
           **metadata) -> 'MiddlewarePipeline':
        """添加中间件"""
        with self._lock:
            name = middleware.get_name()
            
            # 创建中间件信息
            info = MiddlewareInfo(
                name=name,
                middleware_type=middleware_type or middleware.get_type(),
                priority=priority or middleware.get_priority(),
                execution_order=execution_order,
                conditions=conditions or [],
                metadata=metadata
            )
            
            self.middleware_info[name] = info
            
            # 添加到对应列表
            if isinstance(middleware, IAsyncMiddleware):
                self.async_middlewares.append(middleware)
                # 按优先级排序
                self.async_middlewares.sort(key=lambda m: self.middleware_info[m.get_name()].priority, reverse=True)
            else:
                self.middlewares.append(middleware)
                # 按优先级排序
                self.middlewares.sort(key=lambda m: self.middleware_info[m.get_name()].priority, reverse=True)
            
            self.logger.debug(f"Added middleware: {name} (type: {info.middleware_type.value}, priority: {info.priority})")
            
            return self
    
    def remove(self, middleware_name: str) -> bool:
        """移除中间件"""
        with self._lock:
            # 从同步中间件列表移除
            for i, middleware in enumerate(self.middlewares):
                if middleware.get_name() == middleware_name:
                    del self.middlewares[i]
                    break
            
            # 从异步中间件列表移除
            for i, middleware in enumerate(self.async_middlewares):
                if middleware.get_name() == middleware_name:
                    del self.async_middlewares[i]
                    break
            
            # 移除中间件信息
            if middleware_name in self.middleware_info:
                del self.middleware_info[middleware_name]
                self.logger.debug(f"Removed middleware: {middleware_name}")
                return True
            
            return False
    
    def execute(self, handler: Callable, context: MiddlewareContext = None, *args, **kwargs) -> Any:
        """执行中间件管道"""
        if context is None:
            context = MiddlewareContext()
        
        start_time = time.time()
        
        try:
            # 过滤并排序中间件
            active_middlewares = [
                m for m in self.middlewares 
                if self.middleware_info[m.get_name()].should_execute(context)
            ]
            
            # 构建执行链
            def build_chain(middlewares: List[IMiddleware], final_handler: Callable) -> Callable:
                if not middlewares:
                    return lambda: final_handler(*args, **kwargs)
                
                middleware = middlewares[0]
                remaining = middlewares[1:]
                next_func = build_chain(remaining, final_handler)
                
                def execute_middleware():
                    middleware_start = time.time()
                    info = self.middleware_info[middleware.get_name()]
                    
                    try:
                        result = middleware.execute(context, next_func)
                        execution_time = time.time() - middleware_start
                        info.record_execution(execution_time, True)
                        return result
                    except Exception as e:
                        execution_time = time.time() - middleware_start
                        info.record_execution(execution_time, False)
                        self.logger.error(f"Error in middleware {middleware.get_name()}: {e}")
                        raise
                
                return execute_middleware
            
            # 执行链
            chain = build_chain(active_middlewares, handler)
            result = chain()
            
            # 记录统计信息
            execution_time = time.time() - start_time
            self.total_executions += 1
            self.total_execution_time += execution_time
            
            return result
        
        except Exception as e:
            self.error_count += 1
            self.logger.error(f"Error in pipeline execution: {e}")
            raise
    
    async def execute_async(self, handler: Callable, context: MiddlewareContext = None, *args, **kwargs) -> Any:
        """执行异步中间件管道"""
        if context is None:
            context = MiddlewareContext()
        
        start_time = time.time()
        
        try:
            # 过滤并排序中间件
            active_middlewares = [
                m for m in self.async_middlewares 
                if self.middleware_info[m.get_name()].should_execute(context)
            ]
            
            # 构建异步执行链
            def build_async_chain(middlewares: List[IAsyncMiddleware], final_handler: Callable) -> Callable:
                if not middlewares:
                    if asyncio.iscoroutinefunction(final_handler):
                        return lambda: final_handler(*args, **kwargs)
                    else:
                        return lambda: asyncio.create_task(asyncio.coroutine(lambda: final_handler(*args, **kwargs))())
                
                middleware = middlewares[0]
                remaining = middlewares[1:]
                next_func = build_async_chain(remaining, final_handler)
                
                async def execute_async_middleware():
                    middleware_start = time.time()
                    info = self.middleware_info[middleware.get_name()]
                    
                    try:
                        result = await middleware.execute(context, next_func)
                        execution_time = time.time() - middleware_start
                        info.record_execution(execution_time, True)
                        return result
                    except Exception as e:
                        execution_time = time.time() - middleware_start
                        info.record_execution(execution_time, False)
                        self.logger.error(f"Error in async middleware {middleware.get_name()}: {e}")
                        raise
                
                return execute_async_middleware
            
            # 执行异步链
            chain = build_async_chain(active_middlewares, handler)
            result = await chain()
            
            # 记录统计信息
            execution_time = time.time() - start_time
            self.total_executions += 1
            self.total_execution_time += execution_time
            
            return result
        
        except Exception as e:
            self.error_count += 1
            self.logger.error(f"Error in async pipeline execution: {e}")
            raise
    
    def enable_middleware(self, middleware_name: str) -> bool:
        """启用中间件"""
        if middleware_name in self.middleware_info:
            self.middleware_info[middleware_name].enabled = True
            self.logger.debug(f"Enabled middleware: {middleware_name}")
            return True
        return False
    
    def disable_middleware(self, middleware_name: str) -> bool:
        """禁用中间件"""
        if middleware_name in self.middleware_info:
            self.middleware_info[middleware_name].enabled = False
            self.logger.debug(f"Disabled middleware: {middleware_name}")
            return True
        return False
    
    def get_middleware_info(self, middleware_name: str) -> Optional[MiddlewareInfo]:
        """获取中间件信息"""
        return self.middleware_info.get(middleware_name)
    
    def get_all_middleware_info(self) -> Dict[str, MiddlewareInfo]:
        """获取所有中间件信息"""
        return self.middleware_info.copy()
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        middleware_stats = {}
        for name, info in self.middleware_info.items():
            middleware_stats[name] = info.to_dict()
        
        return {
            "pipeline_name": self.name,
            "total_middlewares": len(self.middlewares) + len(self.async_middlewares),
            "sync_middlewares": len(self.middlewares),
            "async_middlewares": len(self.async_middlewares),
            "total_executions": self.total_executions,
            "total_execution_time": self.total_execution_time,
            "average_execution_time": self.total_execution_time / self.total_executions if self.total_executions > 0 else 0.0,
            "error_count": self.error_count,
            "success_rate": (self.total_executions - self.error_count) / self.total_executions if self.total_executions > 0 else 0.0,
            "middleware_stats": middleware_stats
        }
    
    def clear(self):
        """清空管道"""
        with self._lock:
            self.middlewares.clear()
            self.async_middlewares.clear()
            self.middleware_info.clear()
            self.global_context.clear()
            
            # 重置统计
            self.total_executions = 0
            self.total_execution_time = 0.0
            self.error_count = 0
            
            self.logger.info("Pipeline cleared")

# ============================================================================
# 内置中间件
# ============================================================================

class LoggingMiddleware(IMiddleware):
    """日志中间件"""
    
    def __init__(self, logger: logging.Logger = None, log_level: int = logging.INFO):
        self.logger = logger or logging.getLogger(self.__class__.__name__)
        self.log_level = log_level
    
    def execute(self, context: MiddlewareContext, next_func: NextFunction) -> Any:
        start_time = time.time()
        
        self.logger.log(self.log_level, f"Request started: {context.request_id}")
        
        try:
            result = next_func()
            execution_time = time.time() - start_time
            
            self.logger.log(self.log_level, 
                          f"Request completed: {context.request_id} in {execution_time:.3f}s")
            
            return result
        
        except Exception as e:
            execution_time = time.time() - start_time
            
            self.logger.error(
                f"Request failed: {context.request_id} in {execution_time:.3f}s - {str(e)}"
            )
            
            raise
    
    def get_type(self) -> MiddlewareType:
        return MiddlewareType.LOGGING
    
    def get_priority(self) -> int:
        return 1000  # 高优先级，最先执行

class TimingMiddleware(IMiddleware):
    """计时中间件"""
    
    def execute(self, context: MiddlewareContext, next_func: NextFunction) -> Any:
        start_time = time.time()
        
        try:
            result = next_func()
            execution_time = time.time() - start_time
            
            context.set("execution_time", execution_time)
            context.metadata["timing"] = {
                "start_time": start_time,
                "end_time": time.time(),
                "execution_time": execution_time
            }
            
            return result
        
        except Exception as e:
            execution_time = time.time() - start_time
            context.set("execution_time", execution_time)
            context.set("error", str(e))
            raise
    
    def get_type(self) -> MiddlewareType:
        return MiddlewareType.CUSTOM
    
    def get_priority(self) -> int:
        return 900  # 高优先级

class ErrorHandlingMiddleware(IMiddleware):
    """错误处理中间件"""
    
    def __init__(self, error_handler: Callable[[Exception, MiddlewareContext], Any] = None):
        self.error_handler = error_handler
        self.logger = logging.getLogger(self.__class__.__name__)
    
    def execute(self, context: MiddlewareContext, next_func: NextFunction) -> Any:
        try:
            return next_func()
        
        except Exception as e:
            self.logger.error(f"Error in request {context.request_id}: {str(e)}")
            
            context.set("error", str(e))
            context.set("error_type", type(e).__name__)
            
            if self.error_handler:
                return self.error_handler(e, context)
            else:
                # 默认错误处理
                return {
                    "error": True,
                    "message": str(e),
                    "request_id": context.request_id
                }
    
    def get_type(self) -> MiddlewareType:
        return MiddlewareType.ERROR
    
    def get_priority(self) -> int:
        return 100  # 低优先级，最后执行

class CachingMiddleware(IMiddleware):
    """缓存中间件"""
    
    def __init__(self, cache_key_func: Callable[[MiddlewareContext], str] = None,
                 ttl: int = 300):
        self.cache: Dict[str, Any] = {}
        self.cache_times: Dict[str, float] = {}
        self.cache_key_func = cache_key_func or self._default_cache_key
        self.ttl = ttl
        self.logger = logging.getLogger(self.__class__.__name__)
    
    def _default_cache_key(self, context: MiddlewareContext) -> str:
        """默认缓存键生成"""
        return f"cache_{hash(str(context.data))}"
    
    def _is_cache_valid(self, key: str) -> bool:
        """检查缓存是否有效"""
        if key not in self.cache_times:
            return False
        
        return time.time() - self.cache_times[key] < self.ttl
    
    def execute(self, context: MiddlewareContext, next_func: NextFunction) -> Any:
        cache_key = self.cache_key_func(context)
        
        # 检查缓存
        if cache_key in self.cache and self._is_cache_valid(cache_key):
            self.logger.debug(f"Cache hit for key: {cache_key}")
            context.set("cache_hit", True)
            return self.cache[cache_key]
        
        # 执行并缓存结果
        result = next_func()
        
        self.cache[cache_key] = result
        self.cache_times[cache_key] = time.time()
        
        context.set("cache_hit", False)
        self.logger.debug(f"Cached result for key: {cache_key}")
        
        return result
    
    def get_type(self) -> MiddlewareType:
        return MiddlewareType.CACHING
    
    def get_priority(self) -> int:
        return 800  # 高优先级

class ValidationMiddleware(IMiddleware):
    """验证中间件"""
    
    def __init__(self, validators: List[Callable[[MiddlewareContext], bool]] = None):
        self.validators = validators or []
        self.logger = logging.getLogger(self.__class__.__name__)
    
    def execute(self, context: MiddlewareContext, next_func: NextFunction) -> Any:
        # 执行所有验证器
        for validator in self.validators:
            if not validator(context):
                error_msg = f"Validation failed for request {context.request_id}"
                self.logger.warning(error_msg)
                raise ValueError(error_msg)
        
        context.set("validation_passed", True)
        return next_func()
    
    def get_type(self) -> MiddlewareType:
        return MiddlewareType.VALIDATION
    
    def get_priority(self) -> int:
        return 700  # 较高优先级

# ============================================================================
# 中间件装饰器
# ============================================================================

def middleware(middleware_type: MiddlewareType = MiddlewareType.CUSTOM,
              priority: int = 0,
              execution_order: ExecutionOrder = ExecutionOrder.BEFORE,
              conditions: List[Callable[[MiddlewareContext], bool]] = None):
    """中间件装饰器"""
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs):
            # 创建上下文
            context = MiddlewareContext()
            
            # 创建临时管道
            pipeline = MiddlewarePipeline("temp")
            
            # 创建中间件包装器
            class FunctionMiddleware(IMiddleware):
                def execute(self, ctx: MiddlewareContext, next_func: NextFunction) -> Any:
                    return func(ctx, next_func, *args, **kwargs)
                
                def get_type(self) -> MiddlewareType:
                    return middleware_type
                
                def get_priority(self) -> int:
                    return priority
            
            # 添加中间件并执行
            pipeline.use(
                FunctionMiddleware(),
                middleware_type=middleware_type,
                priority=priority,
                execution_order=execution_order,
                conditions=conditions or []
            )
            
            return pipeline.execute(lambda: None, context)
        
        return wrapper
    
    return decorator

def use_middleware(*middlewares: IMiddleware):
    """使用中间件装饰器"""
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs):
            # 创建上下文
            context = MiddlewareContext()
            
            # 创建管道
            pipeline = MiddlewarePipeline("decorated")
            
            # 添加所有中间件
            for middleware in middlewares:
                pipeline.use(middleware)
            
            # 执行
            return pipeline.execute(lambda: func(*args, **kwargs), context)
        
        return wrapper
    
    return decorator

# ============================================================================
# 全局中间件管理器
# ============================================================================

class MiddlewareManager:
    """中间件管理器"""
    
    def __init__(self):
        self.pipelines: Dict[str, MiddlewarePipeline] = {}
        self.default_pipeline = MiddlewarePipeline("default")
        self.pipelines["default"] = self.default_pipeline
        self.logger = logging.getLogger(self.__class__.__name__)
    
    def create_pipeline(self, name: str) -> MiddlewarePipeline:
        """创建管道"""
        pipeline = MiddlewarePipeline(name)
        self.pipelines[name] = pipeline
        self.logger.debug(f"Created pipeline: {name}")
        return pipeline
    
    def get_pipeline(self, name: str) -> Optional[MiddlewarePipeline]:
        """获取管道"""
        return self.pipelines.get(name)
    
    def remove_pipeline(self, name: str) -> bool:
        """移除管道"""
        if name in self.pipelines and name != "default":
            del self.pipelines[name]
            self.logger.debug(f"Removed pipeline: {name}")
            return True
        return False
    
    def get_all_pipelines(self) -> Dict[str, MiddlewarePipeline]:
        """获取所有管道"""
        return self.pipelines.copy()
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        pipeline_stats = {}
        for name, pipeline in self.pipelines.items():
            pipeline_stats[name] = pipeline.get_stats()
        
        return {
            "total_pipelines": len(self.pipelines),
            "pipeline_stats": pipeline_stats
        }

# ============================================================================
# 全局实例和便捷函数
# ============================================================================

# 全局中间件管理器
global_middleware_manager = MiddlewareManager()

# 默认管道
default_pipeline = global_middleware_manager.default_pipeline

# 便捷函数
def use(middleware: Union[IMiddleware, IAsyncMiddleware], pipeline_name: str = "default", **kwargs) -> MiddlewarePipeline:
    """使用中间件的便捷函数"""
    pipeline = global_middleware_manager.get_pipeline(pipeline_name)
    if pipeline:
        return pipeline.use(middleware, **kwargs)
    else:
        raise ValueError(f"Pipeline '{pipeline_name}' not found")

def execute(handler: Callable, context: MiddlewareContext = None, pipeline_name: str = "default", *args, **kwargs) -> Any:
    """执行管道的便捷函数"""
    pipeline = global_middleware_manager.get_pipeline(pipeline_name)
    if pipeline:
        return pipeline.execute(handler, context, *args, **kwargs)
    else:
        raise ValueError(f"Pipeline '{pipeline_name}' not found")

async def execute_async(handler: Callable, context: MiddlewareContext = None, pipeline_name: str = "default", *args, **kwargs) -> Any:
    """执行异步管道的便捷函数"""
    pipeline = global_middleware_manager.get_pipeline(pipeline_name)
    if pipeline:
        return await pipeline.execute_async(handler, context, *args, **kwargs)
    else:
        raise ValueError(f"Pipeline '{pipeline_name}' not found")

def create_pipeline(name: str) -> MiddlewarePipeline:
    """创建管道的便捷函数"""
    return global_middleware_manager.create_pipeline(name)

# ============================================================================
# 预定义中间件设置
# ============================================================================

def setup_default_middlewares():
    """设置默认中间件"""
    # 添加基础中间件到默认管道
    default_pipeline.use(LoggingMiddleware(), priority=1000)
    default_pipeline.use(TimingMiddleware(), priority=900)
    default_pipeline.use(ErrorHandlingMiddleware(), priority=100)

# 自动设置默认中间件
setup_default_middlewares()