import glob
import os
from typing import cast

from ib_insync import Option, Stock, Ticker, Index, Contract, Trade
import numpy
import pandas
import math
from utils import DateUtils


# def get_option_list_from_chain(chain, center_price: float):
#     result = pandas.DataFrame()
#
#     for cur_diff in CALL_OPTION_DISTANCE:
#         strike_price = round(center_price + cur_diff)
#         option = get_option_with_price_from_chain(chain, strike_price, 'CALL')
#         if option is not None:
#             result = result.append(option)
#
#     for cur_diff in PUT_OPTION_DISTANCE:
#         strike_price = round(center_price - cur_diff)
#         option = get_option_with_price_from_chain(chain, strike_price, 'PUT')
#         if option is not None:
#             result = result.append(option)
#
#     return result


# def get_option_list_from_chain_in_pair_order(chain, center_price: float,
#                                              call_option_distance_list=CALL_OPTION_DISTANCE,
#                                              put_option_distance_list=PUT_OPTION_DISTANCE):
#     result = pandas.DataFrame()
#
#     frac, whole = math.modf(center_price)
#     if frac < 0.1:
#         center_price = whole
#     elif frac > 0.9:
#         center_price = whole+1
#
#     center_price_floor_for_call = math.ceil(center_price)
#     center_price_floor_for_put = math.floor(center_price)
#
#     for index, cur_diff in enumerate(call_option_distance_list):
#         call_strike_price = round(center_price_floor_for_call + cur_diff)
#         option = get_option_with_price_from_chain(chain, call_strike_price, 'CALL')
#         if option is not None:
#             result = result.append(option)
#
#         put_strike_price = round(center_price_floor_for_put - put_option_distance_list[index])
#         option = get_option_with_price_from_chain(chain, put_strike_price, 'PUT')
#         if option is not None:
#             result = result.append(option)
#
#     return result


def get_option_list_from_chain_with_interval(chain, center_price: float, number_of_options: int = 1,
                                             fist_price_offset: int = 4, option_step: int = 1):
	result = pandas.DataFrame()
	
	is_call_chain = chain.iloc[0].option_type == 'CALL'
	
	if is_call_chain:
		start_check_price = math.floor(center_price - fist_price_offset)
	else:
		start_check_price = math.ceil(center_price + fist_price_offset)
	
	if option_step != 0:
		for i in range(number_of_options):
			check_strike_price = option_step * i + start_check_price
			option = get_option_with_price_from_chain(chain, check_strike_price, is_call=is_call_chain)
			if option is not None:
				result = result.append(option, ignore_index=True)
	else:
		if is_call_chain:
			for index, row in chain.iterrows():
				if row.strike_price >= start_check_price:
					result = pandas.concat([result, row.to_frame().transpose()])
					
					if len(result) >= number_of_options:
						return result
		else:
			for index in reversed(chain.index):
				if chain.loc[index].strike_price <= start_check_price:
					result = pandas.concat([result, chain.loc[index].to_frame().transpose()])
					if len(result) >= number_of_options:
						return result
	
	return result


def pair_options_in_vertical_spread(option_list, space: float = None):
	result = pandas.DataFrame(columns={"first", "second"})
	
	option_list = option_list.reset_index(drop=True)
	
	second = None
	
	is_call = option_list.iloc[0].option_type == 'CALL'
	
	for index, row in option_list.iterrows():
		if space == 0:
			if index + 1 < len(option_list):
				second = option_list.loc[index + 1]
		else:
			second = get_option_with_price_from_chain(option_list, row.strike_price + space, is_call=is_call)
		if row is not None and second is not None:
			new_index = 0 if pandas.isnull(result.index.max()) else result.index.max() + 1
			result.loc[new_index] = [row, second]
	
	return result


def pair_options_in_double_oppsite(call_list, put_list, distance: float):
	result = pandas.DataFrame(columns={"first", "second"})
	
	# size = len(call_list)
	# if size != len(put_list):
	#     print("Error: call list and put list does not size the same!!!!!!!!")
	#     return None
	
	call_list = call_list.sort_values(by=['strike_price']).reset_index(drop=True)
	put_list = put_list.sort_values(by=['strike_price']).reset_index(drop=True)
	
	offset = get_index_offset_to_pair_option_list(call_list, put_list)
	
	for index, row in call_list.iterrows():
		if len(put_list) > index + offset:
			second = put_list.loc[index + offset]
			if row is not None and second is not None:
				new_index = 0 if pandas.isnull(result.index.max()) else result.index.max() + 1
				result.loc[new_index] = [row, second]
	
	return result


def get_index_offset_to_pair_option_list(call_list, put_list):
	last_diff = 0
	first_call_strike_price = call_list.loc[0]['strike_price']
	for index, row in put_list.iterrows():
		cur_diff = row['strike_price'] - first_call_strike_price
		if cur_diff == 0:
			return index
		elif last_diff == 0:
			last_diff = cur_diff
		elif last_diff * cur_diff < 0:
			if abs(last_diff) > abs(cur_diff):
				return index
			else:
				return index - 1
		else:
			if abs(last_diff) > abs(cur_diff):
				last_diff = cur_diff
			else:
				return index - 1


def get_option_with_price_from_chain(chain, price: float, option_type: str = 'CALL', is_call: bool = True):
	if not is_call:
		option_type = 'PUT'
	for index, row in chain.iterrows():
		if row.strike_price == price and row.option_type == option_type:
			return row
	return None


def get_straddle_price_by_distance(base_price, distance=0, step=5):
	nearest_multiple = round(base_price / step) * step
	
	# Check if the base price is within 1 unit of the nearest multiple
	if abs(base_price - nearest_multiple) <= 1:
		lower_price = nearest_multiple
		upper_price = nearest_multiple
	else:
		lower_price = nearest_multiple
		upper_price = nearest_multiple + step
	
	lower_price -= distance
	upper_price += distance
	
	return lower_price, upper_price


def get_closest_value(number, step):
	# Calculate the quotient and remainder when dividing by the step
	quotient, remainder = divmod(number, step)
	
	# Determine the closest value based on the remainder
	if remainder < step / 2:
		closest = quotient * step
	else:
		closest = (quotient + 1) * step
	
	return closest


def get_futu_option_code(stock_code, strike_price, date, c_or_p):
	return "US." + stock_code + DateUtils.get_6_digits_date_from_str(date.replace("-", "")) + c_or_p + str(
		int(strike_price)) + "000"


def get_futu_spx_option_code(strike_price, date, c_or_p):
	return get_futu_option_code("SPXW", strike_price, date, c_or_p)


def get_futu_spx_call_code(strike_price, date):
	return get_futu_option_code("SPXW", strike_price, date, "C")


def get_futu_spx_put_code(strike_price, date):
	return get_futu_option_code("SPXW", strike_price, date, "P")


def get_short_full_option_name(contract):
	return f"{contract.symbol} {short_pretty_option_name(contract)}"


def pretty_option_name(contract):
	return f"{contract.lastTradeDateOrContractMonth.replace('2024', '').replace('2025', '')}-{contract.right}{contract.strike:.1f}"


def short_option_name(contract):
	return f"{contract.right}{contract.strike:g}"

def short_option_name2(contract):
	return f"{format_strike_number(contract.strike)}{contract.right}"

def format_strike_number(num):
    s = f"{num:.10f}"  # Print with enough decimal places
    s = s.rstrip("0").rstrip(".") if "." in s else s  # Remove trailing .0
    return s

def pretty_option_name2(contract):
	if contract is not None and isinstance(contract, Option):
		return f"{contract.lastTradeDateOrContractMonth.replace('2024', '').replace('2025', '')}-{contract.strike:.1f}-{contract.right}"
	return "Not an option"


def short_pretty_option_name(contract):
	return f"{contract.right}{contract.strike:.1f}"


def are_ib_option_contracts_same(contract1, contract2):
	if contract1 is None or contract2 is None:
		return False
	return contract1.strike == contract2.strike and contract1.right == contract2.right and contract1.lastTradeDateOrContractMonth == contract2.lastTradeDateOrContractMonth


def filter_ib_data(src_data, threshold_datetime):
	result_data = filter_ib_data_by_time(src_data, threshold_datetime)
	result_data = result_data[['datetime', 'close']]
	result_data = result_data.reset_index(drop=True)
	return result_data


def filter_ib_data_by_time(src_data, threshold_datetime):
	result_data = src_data
	result_data['datetime'] = pandas.to_datetime(result_data['date'], utc=True).dt.tz_localize(None)
	dtime = DateUtils.get_datetime_from_unknown_type(threshold_datetime).replace(tzinfo=None)
	result_data = result_data[result_data['datetime'] >= dtime]
	result_data = result_data.reset_index(drop=True)
	return result_data


def filter_ib_data_to_market_close(src_data, selected_date, threshold_datetime):
	result_data = src_data
	if 'datetime' not in result_data.columns:
		result_data['datetime'] = result_data['date']
	result_data['datetime'] = pandas.to_datetime(result_data['datetime'])
	result_data['time'] = result_data['datetime'].dt.time
	result_data['date'] = result_data['datetime'].dt.date

	filter_date = pandas.Timestamp(selected_date).date()
	result_data = result_data[(result_data['date'] == filter_date)]
	
	start_time = pandas.Timestamp(threshold_datetime).time()
	end_time = pandas.Timestamp("16:00:05").time()
	
	# Filter the DataFrame based on the time range
	filtered_df = result_data[(result_data['time'] >= start_time) & (result_data['time'] <= end_time)]
	
	# Drop the helper 'time' column if needed
	filtered_df = filtered_df.drop(columns=['date'])
	filtered_df = filtered_df.drop(columns=['time'])
	
	return filtered_df


def get_spx_option_low_high(spx_data):
	low = spx_data["close"].min()
	high = spx_data["close"].max()
	low = int(low // 5 * 5 - 0)
	high = int(high // 5 * 5 + 5 + 5)
	return low, high


def get_spx_option_low_high_by_min_data(spx_high, spx_low, distance=2):
	low = int(spx_low // 5 * 5 - 0)
	high = int(spx_high // 5 * 5 + 5 + 5)
	low = low - distance * 5
	high = high + distance * 5
	return low, high


def round_down_by_unit(price, unit=5):
	return round_up_by_unit(price, unit) - unit


def round_up_by_unit(price, unit=5):
	return math.ceil(price / unit) * unit


def build_spxw_option_contract_with_bool(strike: int, is_call: bool = True, expiry_date: str = None):
	return build_ib_spxw_option_contract(strike=strike, c_or_p="C" if is_call else "P", expiry_date=expiry_date)


def build_ib_spxw_option_contract(strike: int, c_or_p: str = "C", expiry_date: str = None):
	if expiry_date is None:
		expiry_date = DateUtils.get_us_eastern_today_in_8_digits()
	return Option(symbol="SPX", lastTradeDateOrContractMonth=expiry_date, strike=strike, right=c_or_p, exchange='SMART', multiplier="100", currency='USD', tradingClass='SPXW')


def get_option_conid(strike: int, is_call: bool = True, expiry_date: str = None):
	contract = build_spxw_option_contract_with_bool(strike, is_call, expiry_date)
	

def get_stock_contract_from_option(option: Option) -> Stock:
	contract = Index('SPX', 'CBOE', 'USD') if option.symbol == "SPX" else Stock(symbol=option.symbol, exchange=option.exchange, currency=option.currency)
	
	if contract.exchange == "" or contract.exchange is None:
		contract.exchange = "SMART"
	
	return contract
	

def get_stock_contract_with_symbol(symbol: str) -> Stock:
	contract = Index('SPX', 'CBOE', 'USD') if symbol == "SPX" else Stock(symbol=symbol, exchange="SMART", currency="USD")
	
	return contract


def get_strike_from_ticker(ticker: Ticker) -> float:
	if isinstance(ticker.contract, Option):
		option = cast(Option, ticker.contract)
		return option.strike
	else:
		return -1


def get_delta_from_ticker(ticker: Ticker) -> float:
	if ticker is None:
		return 0
	if ticker.modelGreeks and ticker.modelGreeks.delta:
		return ticker.modelGreeks.delta
	return 0


def find_option_file_from_global_with_iscall(dir: str, strike: float, is_call: bool, prefix: str = ""):
	"""Find option file matching strike and type"""
	option_type = 'C' if is_call else 'P'
	return find_option_file_from_global(dir, strike, option_type, prefix)

def find_option_file_from_global(dir: str, strike: float, option_type: str, prefix: str = ""):
	"""Find option file matching strike and type"""

	pattern = f"{prefix}{strike:g}{option_type}.csv"
	files = glob.glob(os.path.join(dir, pattern))
	return files[0] if files else None


def load_and_dedupe_local_file_with_dir(dir: str, file_name: str):
	full_file_path = os.path.join(dir, file_name)
	if not full_file_path:
		available = [f.split('.')[0] for f in os.listdir(dir) if f.endswith('.csv')]
		raise FileNotFoundError(f"Couldn't find {file_name}. Available options: {available}")
	return load_and_dedupe_local_file(full_file_path)
    
def load_and_dedupe_local_file(file_path):
	if not file_path:
		raise FileNotFoundError(f"Couldn't find {file_path}.")

	"""Load CSV and handle duplicate timestamps"""
	df = pandas.read_csv(file_path)
	df['date'] = pandas.to_datetime(df['date'])
	
	if df['date'].duplicated().any():
		# print(f"Warning: Duplicate timestamps found in {os.path.basename(file_path)}, keeping last entries")
		df = df.drop_duplicates('date', keep='last')
	
	return df.set_index('date')

def get_intrinsic_value(stock_price: float, strike: float, is_call: bool):
	if is_call:
		return max(0, stock_price - strike)
	else:
		return max(0, strike - stock_price)


def is_call(right: str):
	return right in ["C", "c", "call", "CALL"]


def is_contract_call(contract: Contract):
	return is_right_call(contract.right)


def is_right_call(contract_right_letter: str):
	return contract_right_letter in ["C", "c", "call", "CALL"]

def get_smart_latest_price_with_check(ticker: Ticker, spread_threshold: float = 2) -> float:
	if ticker is None:
		# print(f"OptionUtils: get_smart_latest_price_with_check(): ticker is None")
		return None
	if numpy.isnan(ticker.ask) or numpy.isnan(ticker.bid) or (ticker.ask > 1 and ticker.ask / ticker.bid > spread_threshold):
		if numpy.isnan(ticker.last):
			# print(f"OptionUtils: get_smart_latest_price_with_check(): ticker.last is nan!")
			return None
		else:
			return ticker.last
	else:
		return (ticker.ask + ticker.bid) / 2

def group_the_trade_by_conid(trade_list: list[Trade]) -> dict[int, list[Trade]]:
	all_trades_by_conid_dict = {}
	for trade in trade_list:
		if trade.contract.conId not in all_trades_by_conid_dict:
			all_trades_by_conid_dict[trade.contract.conId] = []
		all_trades_by_conid_dict[trade.contract.conId].append(trade)
	return all_trades_by_conid_dict

def get_trade_pretty_str(trade: Trade) -> str:
	return f"{trade.contract.symbol} {trade.contract.right} {trade.contract.strike:.1f} {trade.order.action} {trade.order.tif} {trade.order.origin}"



