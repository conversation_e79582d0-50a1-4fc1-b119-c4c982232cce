import time
from datetime import datetime, timedelta, date, time as datetime_time

import pytz
from pandas.tseries.offsets import BDay
from pytz import timezone
import pandas

STANDARD_DATE_FORMAT = '%Y-%m-%d'
STANDARD_NUMBER_ONLY_FORMAT = '%Y%m%d'
STANDARD_NUMBER_ONLY_2_DIGITS_YEAR_FORMAT = '%y%m%d'
STANDARD_TIME_FORMAT = '%H:%M:%S'
HOUR_MIN_FORMAT = '%H:%M'
MIN_SEC_FORMAT = '%M:%S'
STANDARD_DATE_TIME_FORMAT = '%Y-%m-%d %H:%M:%S'
STANDARD_NUMBER_DATE_TIME_FORMAT = '%Y%m%d %H:%M:%S'
DATETIME_TIMEZONE_FORMAT = STANDARD_DATE_TIME_FORMAT + '%z'

DATA_FILE_PATH = "files/hk/"
DATA_US_FILE_PATH = "files/us/"
DATA_FILE_PATH_KDAY = "files/hk/k_day/"

US_MARKET_OPEN_TIME = "09:30:00"


def get_datetime_from_unknown_type(date_data):
	try:
		if type(date_data) is pandas.Timestamp:
			return date_data.to_pydatetime().replace(tzinfo=None)
		if type(date_data) is datetime:
			return date_data
		elif isinstance(date_data, date) and len(str(date_data)) == (len(STANDARD_DATE_FORMAT) + 2) and '-' in str(
				date_data):
			return datetime.strptime(str(date_data), STANDARD_DATE_FORMAT)
		elif len(str(date_data)) == (len(STANDARD_DATE_TIME_FORMAT) + 2):
			return datetime.strptime(date_data, STANDARD_DATE_TIME_FORMAT)
		elif len(str(date_data)) == (len(STANDARD_NUMBER_DATE_TIME_FORMAT) + 2):
			return datetime.strptime(date_data, STANDARD_NUMBER_DATE_TIME_FORMAT)
		elif len(str(date_data)) == len(DATETIME_TIMEZONE_FORMAT) + 6:
			return datetime.strptime(date_data, DATETIME_TIMEZONE_FORMAT)
		elif len(str(date_data)) == len(STANDARD_TIME_FORMAT) and ':' in str(date_data):
			return datetime.strptime(date_data, STANDARD_TIME_FORMAT)
		elif len(str(date_data)) == (len(STANDARD_DATE_FORMAT) + 2) and '-' in str(date_data):
			return datetime.strptime(date_data, STANDARD_DATE_FORMAT)
		elif len(str(date_data)) == 8 and '-' not in str(date_data) and ':' not in str(date_data) and str(
				date_data).startswith("20"):
			return datetime.strptime(date_data, STANDARD_NUMBER_ONLY_FORMAT)
		elif len(str(date_data)) == 6 and '-' not in str(date_data) and ':' not in str(date_data):
			return datetime.strptime(date_data, STANDARD_NUMBER_ONLY_2_DIGITS_YEAR_FORMAT)
		else:
			return datetime.strptime(date_data, STANDARD_DATE_TIME_FORMAT)
	except:
		return None


def get_today(return_format=STANDARD_DATE_FORMAT):
	return datetime.today().strftime(return_format)


def get_today_in_6_digits():
	return datetime.today().strftime(STANDARD_NUMBER_ONLY_2_DIGITS_YEAR_FORMAT)


def get_unknown_type_in_6_digits(date_data):
	return get_datetime_from_unknown_type(date_data).strftime(STANDARD_NUMBER_ONLY_2_DIGITS_YEAR_FORMAT)


def get_today_and_time(return_format=STANDARD_DATE_TIME_FORMAT):
	return datetime.today().strftime(return_format)


def get_today_and_time_in_us_eastern(return_format=STANDARD_DATE_TIME_FORMAT):
	current_us_time = datetime.now(timezone('US/Eastern'))
	return current_us_time.strftime(return_format)


def get_us_eastern_today_in_8_digits():
	current_us_time = datetime.now(timezone('US/Eastern'))
	return current_us_time.strftime(STANDARD_NUMBER_ONLY_FORMAT)


def get_us_eastern_today_in_6_digits():
	return get_us_eastern_today_in_8_digits()[2:]


def get_us_eastern_datetime(dtime):
	return dtime.astimezone(timezone('US/Eastern'))


def get_us_eastern_datetime_time_str(dtime):
	return get_us_eastern_datetime(dtime).time().strftime(STANDARD_TIME_FORMAT)


def get_next_day(return_format=STANDARD_DATE_FORMAT, current_day_in_str=get_today()):
	today = datetime.strptime(current_day_in_str, return_format)
	return (today + timedelta(days=1)).strftime(return_format)


def get_previous_business_day(return_format=STANDARD_DATE_FORMAT, current_day_in_str=get_today()):
	today = datetime.strptime(current_day_in_str, return_format)
	return (today - BDay(1)).strftime(return_format)


def get_next_business_day(return_format=STANDARD_DATE_FORMAT, current_day_in_str=get_today()):
	today = datetime.strptime(current_day_in_str, return_format)
	return (today + BDay(1)).strftime(return_format)


def add_seconds_to_str_time(time_in_str: str, add_seconds):
	original_time = get_datetime_from_unknown_type(time_in_str)
	new_time = original_time + timedelta(seconds=add_seconds)
	return new_time.strftime(STANDARD_TIME_FORMAT)


def add_seconds_to_datetime(datetime: datetime, add_seconds):
	new_time = datetime + timedelta(seconds=add_seconds)
	return new_time


def minus_seconds_to_str_time(time_in_str: str, minus_seconds):
	original_time = get_datetime_from_unknown_type(time_in_str)
	new_time = original_time + timedelta(seconds=minus_seconds)
	return new_time.strftime(STANDARD_TIME_FORMAT)


def get_hour_and_minute_from_str_time(time_in_str: str):
	return get_datetime_from_unknown_type(time_in_str).strftime("%H:%M")


def get_time_from_date_time_str(date_time_in_str: str):
	return datetime.strptime(date_time_in_str, STANDARD_DATE_TIME_FORMAT).time()


def get_time_from_date_time(date_time: datetime):
	return date_time.time().strftime(STANDARD_TIME_FORMAT)


def get_date_time_from_date_time(date_time_in_str: str):
	return datetime.strptime(date_time_in_str, STANDARD_DATE_TIME_FORMAT)


def get_date_from_date_time(date_time_in_str: str):
	return get_datetime_from_unknown_type(date_time_in_str).date()


def get_date_in_str_from_date_time(date_time_in_str: str):
	return get_datetime_from_unknown_type(date_time_in_str).date().strftime(STANDARD_DATE_FORMAT)


def get_date_from_timestamp(date_time: datetime):
	return date_time.date().strftime(STANDARD_DATE_FORMAT)


def get_yfinance_date_from_unknown_datetime(date_time_in_str: str):
	return get_datetime_from_unknown_type(date_time_in_str).date().strftime(STANDARD_DATE_FORMAT)


def get_time_from_time_str(time_in_str: str):
	return datetime.strptime(time_in_str, STANDARD_TIME_FORMAT).time()


def compare_dates_from_date_times(date_time_in_str1: str, date_time_in_str2: str):
	try:
		date1 = datetime.strptime(date_time_in_str1, STANDARD_DATE_TIME_FORMAT).date()
		date2 = datetime.strptime(date_time_in_str2, STANDARD_DATE_TIME_FORMAT).date()
	except:
		return False
	
	return date1 == date2


def compare_time_from_date_times_and_time(date_time_in_str1: str, time_in_str2: str):
	try:
		date1 = datetime.strptime(date_time_in_str1, STANDARD_DATE_TIME_FORMAT).time()
		date2 = datetime.strptime(time_in_str2, STANDARD_TIME_FORMAT).time()
	except:
		return False
	
	return date1 == date2


def is_time_equal(date_data1, date_data2):
	time1 = get_datetime_from_unknown_type(date_data1).time()
	time2 = get_datetime_from_unknown_type(date_data2).time()
	
	return time1 == time2


def is_time1_meeting_time2(date_data1, date_data2):
	date1 = get_datetime_from_unknown_type(date_data1)
	date2 = get_datetime_from_unknown_type(date_data2)
	if date1 is None or date2 is None:
		return False
	
	return date1 >= date2


def is_time1_later_or_equal_time2(date_data1, date_data2):
	time1 = get_datetime_from_unknown_type(date_data1).time()
	time2 = get_datetime_from_unknown_type(date_data2).time()
	if time1 is None or time2 is None:
		return False
	
	return time1 >= time2


def is_date1_later_or_equal_date2(date_data1, date_data2):
	date1 = get_datetime_from_unknown_type(date_data1).date()
	date2 = get_datetime_from_unknown_type(date_data2).date()
	if date1 is None or date2 is None:
		return False
	
	return date1 >= date2


def is_time1_later_or_equal_time2(date_data1, date_data2):
	time1 = date_data1 if type(date_data1) is datetime_time else get_datetime_from_unknown_type(date_data1).time()
	time2 = date_data2 if type(date_data2) is datetime_time else get_datetime_from_unknown_type(date_data2).time()
	if time1 is None or time2 is None:
		return False
	
	return time1 >= time2


def is_datetime1_later_or_equal_datetime2(datetime1, datetime2):
	dtime1 = get_datetime_from_unknown_type(datetime1).time()
	dtime2 = get_datetime_from_unknown_type(datetime2).time()
	if dtime1 is None or dtime2 is None:
		return False
	
	return dtime1 >= dtime2


def get_days_between_2_datetimes(date_time_in_str1: str, time_in_str2: str):
	date1 = datetime.strptime(date_time_in_str1, STANDARD_DATE_TIME_FORMAT).date()
	date2 = datetime.strptime(time_in_str2, STANDARD_DATE_TIME_FORMAT).date()
	return (date2 - date1).days


def get_days_between_2_dates(date_in_str1: str, date_in_str2: str):
	date1 = datetime.strptime(date_in_str1, STANDARD_DATE_FORMAT).date()
	date2 = datetime.strptime(date_in_str2, STANDARD_DATE_FORMAT).date()
	return (date2 - date1).days


def datetime_pretty_print(datetime: datetime):
	return datetime.strftime(STANDARD_DATE_TIME_FORMAT).replace(" 00:00:00", "").replace(":00-05:00", "")


def get_sub_list_by_time_period(data, start_time_in_str: str = None, end_time_in_str: str = None, ):
	# if start_time_in_str is None:
	#     start_time_in_str = STOCK_START_DATE + ' ' + OPEN_ORDER_TIME
	# if end_time_in_str is None:
	#     end_time_in_str = STOCK_END_DATE + ' ' + CLOSE_ORDER_TIME
	start_time = datetime.strptime(start_time_in_str, STANDARD_DATE_TIME_FORMAT)
	end_time = datetime.strptime(end_time_in_str, STANDARD_DATE_TIME_FORMAT)
	start_index = -1
	for index, row in data.iterrows():
		cur_time = datetime.strptime(row['time'], STANDARD_DATE_TIME_FORMAT)
		if start_index == -1:
			if cur_time >= start_time:
				start_index = index
		else:
			if cur_time > end_time:
				return data[start_index:index]


def get_polygon_time_info_from_default(time_period: str):
	multiplier = 0
	timespan = "minute"
	if time_period.replace("minute", "").isdigit():
		timespan = "minute"
		multiplier = int(time_period.replace("minute", ""))
	if time_period.replace("m", "").isdigit():
		timespan = "minute"
		multiplier = int(time_period.replace("m", ""))
	
	return multiplier, timespan


def replace_time_in_datetime(datetime: datetime, time_in_str: str):
	return


def get_local_date_time_str_from_us_eastern_str(time_in_str: str, use_next_day_if_earlier=True):
	if time_in_str is None:
		return None
	local_now = datetime.now()
	origianl_time = datetime.strptime(time_in_str, '%H:%M:%S').time()
	new_york_now = datetime.now(timezone('America/New_York'))
	new_york_time = pytz.timezone('America/New_York').localize(datetime.combine(new_york_now, origianl_time),
	                                                           is_dst=None)
	
	# Get local timezone
	local_timezone = datetime.now().astimezone().tzinfo
	local_time = new_york_time.astimezone(local_timezone)
	
	# compare two datetime, and if local_now > local_time, then it is the next day
	if use_next_day_if_earlier and local_now.timestamp() - local_time.timestamp() > 0:
		local_time += timedelta(days=1)
	return local_time.strftime(STANDARD_DATE_TIME_FORMAT)


def get_next_option_trade_date_from_us_eastern_str(time_in_str: str, use_next_day_if_earlier=True) -> str:
	if time_in_str is None:
		return None
	local_now = datetime.now()
	origianl_time = datetime.strptime(time_in_str, '%H:%M:%S').time()
	new_york_now = datetime.now(timezone('America/New_York'))
	new_york_time = pytz.timezone('America/New_York').localize(datetime.combine(new_york_now, origianl_time),
															   is_dst=None)

	# Get local timezone
	local_timezone = datetime.now().astimezone().tzinfo
	local_time = new_york_time.astimezone(local_timezone)

	# compare two datetime, and if local_now > local_time, then it is the next day
	if use_next_day_if_earlier and local_now.timestamp() - local_time.timestamp() > 0:
		local_time += timedelta(days=1)
	return local_time.astimezone(timezone('America/New_York')).strftime(STANDARD_NUMBER_ONLY_FORMAT)


def get_date_time_difference_from_now(date_time_in_str: str):
	now = datetime.now()
	check_time = datetime.strptime(date_time_in_str, STANDARD_DATE_TIME_FORMAT)
	return (check_time - now).total_seconds()


def check_if_time_is_up(date_time_in_str: str):
	return get_date_time_difference_from_now(date_time_in_str) <= 0


def wait_in_half_way_till_us_datetime(us_datetime_in_str: str):
	local_dt = get_local_date_time_str_from_us_eastern_str(us_datetime_in_str)
	wait_in_half_way_till_date_time(local_dt)


def wait_in_half_way_till_date_time(date_time_in_str: str):
	while True:
		time_difference = get_date_time_difference_from_now(date_time_in_str)
		if time_difference <= 5:
			if time_difference > 0:
				time.sleep(time_difference)
			print("(%s) Time is up ---- " % date_time_in_str)
			break
		else:
			wait_second = int(time_difference / 2)
			print("Got %s(%ds) left, will wait %s(%ds)" % (
				prettify_seconds_in_str(time_difference), time_difference,
				prettify_seconds_in_str(wait_second), wait_second
			))
			time.sleep(wait_second)


def prettify_seconds_in_str(seconds: int):
	hours = seconds // 3600
	minutes = (seconds % 3600) // 60
	seconds = seconds % 60
	
	return f"{hours:.0f}h {minutes:.0f}m {seconds:.0f}s"


def get_us_eastern_now():
	return datetime.now(timezone('US/Eastern'))


def get_us_eastern_now_time(s_format=STANDARD_TIME_FORMAT):
	now = datetime.now(timezone('US/Eastern'))
	milliseconds = int(now.microsecond / 1000)
	return f"{now.strftime(s_format)}.{milliseconds:03d}"


def get_us_eastern_now_time_plus_seconds(seconds: int):
	return get_us_eastern_now() + timedelta(seconds=seconds)


def get_us_eastern_now_time_plus_seconds_str(seconds: int):
	return get_us_eastern_now_time_plus_seconds(seconds).strftime(STANDARD_TIME_FORMAT)


def get_us_eastern_today(return_format=STANDARD_DATE_FORMAT):
	current_us_time = datetime.now(timezone('US/Eastern'))
	return current_us_time.strftime(return_format)


def get_us_eastern_current_trade_day(return_format=STANDARD_DATE_FORMAT):
	current_us_time = datetime.now(timezone('US/Eastern'))
	# if current_us_time.hour < 5:
	# should return the previous day
	if current_us_time.hour < 5:
		current_us_time -= timedelta(days=1)
	return current_us_time.strftime(return_format)


def get_us_next_sec_time(sec=45, return_format=HOUR_MIN_FORMAT):
	us_time = datetime.now(timezone('US/Eastern')) + timedelta(seconds=sec)
	return us_time.strftime(return_format)


def get_us_dt_by_string(date_str, time_str):
	result_datetime = datetime.strptime(date_str + " " + time_str, '%Y%m%d %H:%M:%S')
	result_datetime = pytz.timezone('US/Eastern').localize(result_datetime)
	return result_datetime


def is_after_market():
	now = datetime.now(pytz.timezone('US/Eastern'))
	return now.hour >= 16


def is_us_market_open_now():
	us_cur_time = datetime.now(timezone('US/Eastern'))
	if us_cur_time.weekday() == 5 or us_cur_time.weekday() == 6:
		return False
	
	if datetime_time(9, 30) < us_cur_time.time() < datetime_time(16, 0):
		return True
	
	return False


def is_us_market_last_N_mins(num: int):
	us_cur_time = datetime.now(timezone('US/Eastern'))
	if is_us_market_open_now() and us_cur_time.time() > datetime_time(15, 60 - num):
		return True
	
	return False


def is_us_time_meet(us_meet_time: str):
	return is_time1_later_or_equal_time2(get_us_eastern_now(), us_meet_time)


def is_us_time_meet_datetime(us_meet_time: str):
	return is_time1_later_or_equal_time2(get_us_eastern_now(), us_meet_time)


def wait_until_us_market_open():
	local_datetime = get_local_date_time_str_from_us_eastern_str(US_MARKET_OPEN_TIME)
	wait_in_half_way_till_date_time(local_datetime)


def get_current_us_sec():
	return get_us_eastern_now().second


def get_current_us_sec_and_microsec():
	now = get_us_eastern_now()
	return now.second + (now.microsecond / 1000000)


def get_current_us_min():
	return get_us_eastern_now().minute


def get_selected_sec_from_microsec(sec_with_micro: float, selected_secs) -> int:
	for i in range(len(selected_secs) - 1):
		if selected_secs[i] <= sec_with_micro < selected_secs[i + 1]:
			return selected_secs[i]
	if selected_secs[-1] <= sec_with_micro < selected_secs[-1] + 1:
		return selected_secs[-1]
	return -1


def get_2_date_time_difference(datetime1: datetime, datetime2: datetime):
	if datetime1 is None or datetime2 is None:
		return -1
	dt1 = get_datetime_from_unknown_type(datetime1)
	dt2 = get_datetime_from_unknown_type(datetime2)
	return (dt2 - dt1).total_seconds()

def get_us_secs_left_to_stop(stop_time_str: str) -> float:
	stop_time = get_local_date_time_str_from_us_eastern_str(stop_time_str)
	return get_date_time_difference_from_now(stop_time)
