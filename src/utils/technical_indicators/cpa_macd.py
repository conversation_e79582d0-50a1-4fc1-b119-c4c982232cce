import numpy as np
import pandas as pd
from typing import List, Dict, Tuple

class CPAMACDIndicator:
    """
    实现基于累计价格行为的CPA-MACD指标
    用于识别妖股特征和逆势交易机会
    """
    def __init__(self, m1: int = 12, m2: int = 26, m3: int = 9, smooth: int = 20):
        self.m1 = m1  # 快线周期
        self.m2 = m2  # 慢线周期
        self.m3 = m3  # DEA周期
        self.smooth = smooth  # 平滑参数
        self.dcp_sum_history = []  # 存储累计DCP_RATIO历史值

    def calculate_dcp_ratio(self, high: float, low: float, close: float, open_: float, prev_high: float, prev_low: float) -> float:
        """计算日内价格行为效率比率"""
        # 判断是否为涨跌停板
        is_limit_board = high == low
        if is_limit_board:
            is_up_limit = close > prev_high
            is_down_limit = close < prev_low
            return 1.0 if is_up_limit else (-1.0 if is_down_limit else 0.0)
        
        # 计算价格行为效率比率
        price_range = high - low
        if price_range == 0:
            return 0.0
        
        dcp_ratio = (close - open_) / price_range
        # 限制在[-1, 1]范围内
        return max(min(dcp_ratio, 1.0), -1.0)

    def update(self, high: float, low: float, close: float, open_: float, prev_high: float, prev_low: float) -> Tuple[float, float, float]:
        """更新指标并返回DIF, DEA, MACD值"""
        # 计算当前DCP比率
        dcp_ratio = self.calculate_dcp_ratio(high, low, close, open_, prev_high, prev_low)
        
        # 更新累计DCP和
        if not self.dcp_sum_history:
            self.dcp_sum_history.append(dcp_ratio)
        else:
            self.dcp_sum_history.append(self.dcp_sum_history[-1] + dcp_ratio)
        
        # 确保有足够的数据点
        if len(self.dcp_sum_history) < max(self.m1, self.m2, self.m3):
            return (0.0, 0.0, 0.0)
        
        # 计算EMA
        dcp_series = pd.Series(self.dcp_sum_history)
        ema1 = dcp_series.ewm(span=self.m1, adjust=False).mean().iloc[-1]
        ema2 = dcp_series.ewm(span=self.m2, adjust=False).mean().iloc[-1]
        
        # 计算DIF和DEA
        dif = ema1 - ema2
        dea = pd.Series([dif]).ewm(span=self.m3, adjust=False).mean().iloc[-1]
        macd = (dif - dea) * 2
        
        return (dif, dea, macd)

    def batch_calculate(self, price_data: List[Dict]) -> List[Dict]:
        """批量计算历史数据的CPA-MACD指标"""
        results = []
        self.dcp_sum_history = []  # 重置历史数据
        
        for i, data in enumerate(price_data):
            if i == 0:
                # 跳过第一个数据点，没有前一天数据
                results.append({
                    'datetime': data['datetime'],
                    'dcp_ratio': 0.0,
                    'dcp_sum': 0.0,
                    'dif': 0.0,
                    'dea': 0.0,
                    'macd': 0.0
                })
                continue
            
            prev_data = price_data[i-1]
            dcp_ratio = self.calculate_dcp_ratio(
                data['high'], data['low'], data['close'], data['open'],
                prev_data['high'], prev_data['low']
            )
            
            # 更新累计DCP和
            current_dcp_sum = self.dcp_sum_history[-1] + dcp_ratio if self.dcp_sum_history else dcp_ratio
            self.dcp_sum_history.append(current_dcp_sum)
            
            # 计算指标
            if len(self.dcp_sum_history) < max(self.m1, self.m2, self.m3):
                dif, dea, macd = 0.0, 0.0, 0.0
            else:
                dcp_series = pd.Series(self.dcp_sum_history)
                ema1 = dcp_series.ewm(span=self.m1, adjust=False).mean().iloc[-1]
                ema2 = dcp_series.ewm(span=self.m2, adjust=False).mean().iloc[-1]
                dif = ema1 - ema2
                dea = pd.Series([dif]).ewm(span=self.m3, adjust=False).mean().iloc[-1]
                macd = (dif - dea) * 2
            
            results.append({
                'datetime': data['datetime'],
                'dcp_ratio': dcp_ratio,
                'dcp_sum': current_dcp_sum,
                'dif': dif,
                'dea': dea,
                'macd': macd
            })
        
        return results