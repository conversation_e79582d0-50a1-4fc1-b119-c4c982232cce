#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🧠 智能API调度器
基于数据类型、时间、使用率、响应质量等因素智能选择最佳API

核心策略：
1. 数据类型优先级 - 不同数据用不同专长API
2. 时间窗口管理 - 交易时段vs非交易时段
3. 使用率均衡 - 避免单一API过载
4. 质量评分 - 基于历史表现选择
5. 故障转移 - 自动降级和恢复
"""

import time
import random
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum

class DataType(Enum):
    """数据类型枚举"""
    REALTIME_QUOTE = "realtime_quote"      # 实时报价
    HISTORICAL_DATA = "historical_data"    # 历史数据
    COMPANY_PROFILE = "company_profile"    # 公司档案
    MARKET_SCREENER = "market_screener"    # 市场筛选
    SEARCH_TRENDING = "search_trending"    # 搜索趋势
    NEWS_ANALYSIS = "news_analysis"        # 新闻分析
    TECHNICAL_INDICATORS = "technical"     # 技术指标
    BATCH_QUOTES = "batch_quotes"          # 批量报价

class TimeWindow(Enum):
    """时间窗口枚举"""
    MARKET_HOURS = "market_hours"          # 交易时段 (9:30-16:00 EST)
    PRE_MARKET = "pre_market"              # 盘前 (4:00-9:30 EST)
    AFTER_MARKET = "after_market"          # 盘后 (16:00-20:00 EST)
    OVERNIGHT = "overnight"                # 夜间 (20:00-4:00 EST)

@dataclass
class APIPerformance:
    """API性能指标"""
    success_rate: float = 100.0           # 成功率
    avg_response_time: float = 0.0        # 平均响应时间
    last_success: Optional[datetime] = None
    last_failure: Optional[datetime] = None
    consecutive_failures: int = 0
    quality_score: float = 100.0          # 综合质量评分

class SmartAPIScheduler:
    """智能API调度器"""
    
    def __init__(self):
        # Yahoo Finance API矩阵配置
        self.api_matrix = {
            'yahoo_finance_15': {
                'host': 'yahoo-finance15.p.rapidapi.com',
                'name': 'Yahoo Finance 经典版',
                'specialties': [DataType.REALTIME_QUOTE, DataType.MARKET_SCREENER, DataType.NEWS_ANALYSIS],
                'performance': APIPerformance(),
                'usage_limit': 500,  # 每小时限制
                'current_usage': 0,
                'cost_per_call': 0.001,
                'reliability': 0.95
            },
            'yh_finance_complete': {
                'host': 'yh-finance.p.rapidapi.com',
                'name': 'YH Finance 完整版',
                'specialties': [DataType.COMPANY_PROFILE, DataType.HISTORICAL_DATA, DataType.BATCH_QUOTES],
                'performance': APIPerformance(),
                'usage_limit': 1000,
                'current_usage': 0,
                'cost_per_call': 0.002,
                'reliability': 0.98
            },
            'yahoo_finance_api1': {
                'host': 'yahoo-finance-api1.p.rapidapi.com',
                'name': 'Yahoo Finance 搜索版',
                'specialties': [DataType.SEARCH_TRENDING, DataType.MARKET_SCREENER],
                'performance': APIPerformance(),
                'usage_limit': 300,
                'current_usage': 0,
                'cost_per_call': 0.0015,
                'reliability': 0.92
            },
            'yahoo_finance_low_latency': {
                'host': 'yahoo-finance-low-latency.p.rapidapi.com',
                'name': 'Yahoo Finance 实时版',
                'specialties': [DataType.REALTIME_QUOTE, DataType.TECHNICAL_INDICATORS],
                'performance': APIPerformance(),
                'usage_limit': 800,
                'current_usage': 0,
                'cost_per_call': 0.003,
                'reliability': 0.97
            },
            'yh_finance_enhanced': {
                'host': 'yh-finance-complete.p.rapidapi.com',
                'name': 'YH Finance 增强版',
                'specialties': [DataType.HISTORICAL_DATA, DataType.TECHNICAL_INDICATORS],
                'performance': APIPerformance(),
                'usage_limit': 600,
                'current_usage': 0,
                'cost_per_call': 0.0025,
                'reliability': 0.96
            },
            'yahoo_finance_127': {
                'host': 'yahoo-finance127.p.rapidapi.com',
                'name': 'Yahoo Finance 基础版',
                'specialties': [DataType.REALTIME_QUOTE, DataType.BATCH_QUOTES],
                'performance': APIPerformance(),
                'usage_limit': 400,
                'current_usage': 0,
                'cost_per_call': 0.001,
                'reliability': 0.94
            }
        }
        
        # 调度历史
        self.call_history = []
        self.last_reset = datetime.now()

    def get_current_time_window(self) -> TimeWindow:
        """获取当前时间窗口"""
        now = datetime.now()
        hour = now.hour
        
        # 简化版本，基于EST时间
        if 9 <= hour < 16:
            return TimeWindow.MARKET_HOURS
        elif 4 <= hour < 9:
            return TimeWindow.PRE_MARKET
        elif 16 <= hour < 20:
            return TimeWindow.AFTER_MARKET
        else:
            return TimeWindow.OVERNIGHT

    def calculate_api_score(self, api_id: str, data_type: DataType, time_window: TimeWindow) -> float:
        """计算API综合评分"""
        api = self.api_matrix[api_id]
        score = 0.0
        
        # 1. 专长匹配度 (40%)
        specialty_score = 100 if data_type in api['specialties'] else 50
        score += specialty_score * 0.4
        
        # 2. 使用率健康度 (25%)
        usage_ratio = api['current_usage'] / api['usage_limit']
        usage_score = max(0, 100 - usage_ratio * 100)
        score += usage_score * 0.25
        
        # 3. 性能质量 (20%)
        performance_score = api['performance'].quality_score
        score += performance_score * 0.2
        
        # 4. 可靠性 (10%)
        reliability_score = api['reliability'] * 100
        score += reliability_score * 0.1
        
        # 5. 成本效益 (5%)
        cost_score = max(0, 100 - api['cost_per_call'] * 1000)
        score += cost_score * 0.05
        
        # 时间窗口调整
        if time_window == TimeWindow.MARKET_HOURS:
            # 交易时段优先实时API
            if data_type == DataType.REALTIME_QUOTE:
                score *= 1.2
        elif time_window == TimeWindow.OVERNIGHT:
            # 夜间优先历史数据API
            if data_type == DataType.HISTORICAL_DATA:
                score *= 1.1
        
        return min(100, score)

    def select_optimal_api(self, data_type: DataType, exclude_apis: List[str] = None) -> Optional[str]:
        """选择最优API"""
        exclude_apis = exclude_apis or []
        time_window = self.get_current_time_window()
        
        # 计算所有可用API的评分
        api_scores = []
        for api_id, api_config in self.api_matrix.items():
            if api_id in exclude_apis:
                continue
                
            # 检查是否达到使用限制
            if api_config['current_usage'] >= api_config['usage_limit']:
                continue
                
            # 检查是否有连续失败
            if api_config['performance'].consecutive_failures >= 3:
                continue
            
            score = self.calculate_api_score(api_id, data_type, time_window)
            api_scores.append((api_id, score))
        
        if not api_scores:
            return None
        
        # 按评分排序，选择最高分的API
        api_scores.sort(key=lambda x: x[1], reverse=True)
        
        # 添加一些随机性，避免总是选择同一个API
        if len(api_scores) > 1 and random.random() < 0.2:
            # 20%概率选择第二高分的API
            return api_scores[1][0]
        
        return api_scores[0][0]

    def execute_api_call(self, api_id: str, data_type: DataType, params: Dict = None) -> Dict:
        """执行API调用并记录性能"""
        api = self.api_matrix[api_id]
        start_time = time.time()
        
        # 模拟API调用
        success = random.random() > (1 - api['reliability'])
        response_time = random.uniform(50, 300)  # 模拟响应时间
        
        # 更新使用统计
        api['current_usage'] += 1
        
        # 更新性能指标
        if success:
            api['performance'].last_success = datetime.now()
            api['performance'].consecutive_failures = 0
            api['performance'].success_rate = (
                api['performance'].success_rate * 0.9 + 100 * 0.1
            )
        else:
            api['performance'].last_failure = datetime.now()
            api['performance'].consecutive_failures += 1
            api['performance'].success_rate = (
                api['performance'].success_rate * 0.9 + 0 * 0.1
            )
        
        # 更新响应时间
        api['performance'].avg_response_time = (
            api['performance'].avg_response_time * 0.8 + response_time * 0.2
        )
        
        # 更新质量评分
        api['performance'].quality_score = (
            api['performance'].success_rate * 0.7 + 
            max(0, 100 - api['performance'].avg_response_time / 5) * 0.3
        )
        
        # 记录调用历史
        self.call_history.append({
            'timestamp': datetime.now(),
            'api_id': api_id,
            'data_type': data_type.value,
            'success': success,
            'response_time': response_time,
            'score': self.calculate_api_score(api_id, data_type, self.get_current_time_window())
        })
        
        return {
            'success': success,
            'api_used': api['name'],
            'response_time': response_time,
            'data_type': data_type.value,
            'quality_score': api['performance'].quality_score
        }

    def smart_get_data(self, data_type: DataType, params: Dict = None, max_retries: int = 3) -> Dict:
        """智能获取数据，自动故障转移"""
        tried_apis = []
        
        for attempt in range(max_retries):
            api_id = self.select_optimal_api(data_type, exclude_apis=tried_apis)
            
            if not api_id:
                return {
                    'success': False,
                    'error': 'No available APIs',
                    'tried_apis': tried_apis
                }
            
            result = self.execute_api_call(api_id, data_type, params)
            
            if result['success']:
                return result
            
            tried_apis.append(api_id)
            time.sleep(0.5)  # 避免过快重试
        
        return {
            'success': False,
            'error': 'All retries failed',
            'tried_apis': tried_apis
        }

    def get_scheduler_status(self) -> Dict:
        """获取调度器状态"""
        total_calls = len(self.call_history)
        successful_calls = sum(1 for call in self.call_history if call['success'])
        
        api_usage = {}
        for api_id, api_config in self.api_matrix.items():
            usage_ratio = api_config['current_usage'] / api_config['usage_limit']
            api_usage[api_id] = {
                'name': api_config['name'],
                'usage': api_config['current_usage'],
                'limit': api_config['usage_limit'],
                'usage_ratio': usage_ratio,
                'quality_score': api_config['performance'].quality_score,
                'success_rate': api_config['performance'].success_rate
            }
        
        return {
            'total_calls': total_calls,
            'success_rate': successful_calls / total_calls if total_calls > 0 else 0,
            'time_window': self.get_current_time_window().value,
            'api_usage': api_usage,
            'last_reset': self.last_reset
        }

    def reset_usage_counters(self):
        """重置使用计数器（每小时调用）"""
        for api_config in self.api_matrix.values():
            api_config['current_usage'] = 0
        self.last_reset = datetime.now()
        print(f"🔄 使用计数器已重置 - {self.last_reset.strftime('%H:%M:%S')}")

def demonstrate_smart_scheduler():
    """演示智能调度器"""
    print("🧠 智能API调度器演示")
    print("=" * 60)
    
    scheduler = SmartAPIScheduler()
    
    # 模拟不同类型的数据请求
    test_scenarios = [
        (DataType.REALTIME_QUOTE, "获取AAPL实时报价"),
        (DataType.HISTORICAL_DATA, "获取TSLA历史数据"),
        (DataType.COMPANY_PROFILE, "获取MSFT公司档案"),
        (DataType.SEARCH_TRENDING, "搜索热门股票"),
        (DataType.MARKET_SCREENER, "市场筛选器"),
        (DataType.TECHNICAL_INDICATORS, "技术指标分析"),
        (DataType.BATCH_QUOTES, "批量股票报价"),
        (DataType.NEWS_ANALYSIS, "新闻分析")
    ]
    
    print(f"📊 当前时间窗口: {scheduler.get_current_time_window().value}")
    print()
    
    # 执行测试场景
    for data_type, description in test_scenarios:
        print(f"🎯 {description}")
        result = scheduler.smart_get_data(data_type)
        
        if result['success']:
            print(f"   ✅ 成功！使用 {result['api_used']}")
            print(f"   📊 响应时间: {result['response_time']:.0f}ms")
            print(f"   🏆 质量评分: {result['quality_score']:.1f}")
        else:
            print(f"   ❌ 失败: {result['error']}")
            print(f"   🔄 尝试过的API: {result.get('tried_apis', [])}")
        print()
    
    # 显示调度器状态
    status = scheduler.get_scheduler_status()
    print("📈 调度器状态总结")
    print("=" * 60)
    print(f"总调用次数: {status['total_calls']}")
    print(f"成功率: {status['success_rate']:.1%}")
    print(f"当前时间窗口: {status['time_window']}")
    print()
    
    print("API使用情况:")
    for api_id, usage in status['api_usage'].items():
        print(f"  {usage['name']:<20}: {usage['usage']:>3}/{usage['limit']} "
              f"({usage['usage_ratio']:.1%}) 质量:{usage['quality_score']:.1f}")

if __name__ == "__main__":
    demonstrate_smart_scheduler()
