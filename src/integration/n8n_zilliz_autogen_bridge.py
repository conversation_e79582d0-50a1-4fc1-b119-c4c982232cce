#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
N8N → Zilliz → AutoGen → MCP 数据流桥接器
实现悬丝诊脉的智能数据流转
"""

import asyncio
import json
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
import requests
from pymilvus import connections, Collection, utility
import numpy as np

logger = logging.getLogger("N8NZillizAutoGenBridge")


class ZillizTruthSource:
    """Zilliz向量数据库作为唯一真理来源"""
    
    def __init__(self, config: Dict):
        self.config = config
        self.connection = None
        self.collections = {}
        self.connect_to_zilliz()
    
    def connect_to_zilliz(self):
        """连接到Zilliz Cloud"""
        try:
            connections.connect(
                alias="default",
                host=self.config['zilliz']['host'],
                port=self.config['zilliz']['port'],
                user=self.config['zilliz']['user'],
                password=self.config['zilliz']['password'],
                secure=True
            )
            logger.info("✅ Zilliz连接成功")
            self._load_collections()
        except Exception as e:
            logger.error(f"❌ Zilliz连接失败: {e}")
    
    def _load_collections(self):
        """加载数据集合"""
        collection_names = [
            "daily_market_intelligence",  # 每日市场情报
            "rss_sentiment_vectors",      # RSS情绪向量
            "debate_context_memory",      # 辩论上下文记忆
            "agent_knowledge_base"        # 分析师知识库
        ]
        
        for name in collection_names:
            if utility.has_collection(name):
                self.collections[name] = Collection(name)
                logger.info(f"📊 加载集合: {name}")
    
    def get_daily_intelligence(self, date: str = None) -> Dict:
        """获取指定日期的市场情报"""
        if not date:
            date = datetime.now().strftime("%Y-%m-%d")
        
        try:
            collection = self.collections["daily_market_intelligence"]
            
            # 查询当日情报
            search_params = {
                "metric_type": "COSINE",
                "params": {"nprobe": 10}
            }
            
            # 构建日期过滤条件
            expr = f'date == "{date}"'
            
            results = collection.query(
                expr=expr,
                output_fields=["content", "sentiment", "keywords", "source", "timestamp"]
            )
            
            if results:
                return {
                    "date": date,
                    "intelligence_count": len(results),
                    "data": results,
                    "summary": self._summarize_intelligence(results)
                }
            else:
                logger.warning(f"⚠️ 未找到 {date} 的市场情报")
                return {"date": date, "intelligence_count": 0, "data": []}
                
        except Exception as e:
            logger.error(f"❌ 获取市场情报失败: {e}")
            return {"error": str(e)}
    
    def _summarize_intelligence(self, data: List[Dict]) -> Dict:
        """总结市场情报"""
        if not data:
            return {}
        
        # 情绪分析统计
        sentiments = [item.get('sentiment', 0) for item in data]
        avg_sentiment = np.mean(sentiments) if sentiments else 0
        
        # 关键词统计
        all_keywords = []
        for item in data:
            keywords = item.get('keywords', [])
            if isinstance(keywords, list):
                all_keywords.extend(keywords)
        
        keyword_freq = {}
        for keyword in all_keywords:
            keyword_freq[keyword] = keyword_freq.get(keyword, 0) + 1
        
        top_keywords = sorted(keyword_freq.items(), key=lambda x: x[1], reverse=True)[:10]
        
        return {
            "average_sentiment": round(avg_sentiment, 3),
            "sentiment_distribution": {
                "positive": len([s for s in sentiments if s > 0.1]),
                "neutral": len([s for s in sentiments if -0.1 <= s <= 0.1]),
                "negative": len([s for s in sentiments if s < -0.1])
            },
            "top_keywords": top_keywords,
            "data_sources": list(set([item.get('source', 'unknown') for item in data]))
        }
    
    def search_relevant_context(self, query: str, collection_name: str, top_k: int = 5) -> List[Dict]:
        """搜索相关上下文"""
        try:
            if collection_name not in self.collections:
                logger.warning(f"⚠️ 集合 {collection_name} 不存在")
                return []
            
            collection = self.collections[collection_name]
            
            # 这里需要将query转换为向量
            # 实际实现中应该调用embedding模型
            query_vector = self._text_to_vector(query)
            
            search_params = {
                "metric_type": "COSINE",
                "params": {"nprobe": 10}
            }
            
            results = collection.search(
                data=[query_vector],
                anns_field="vector",
                param=search_params,
                limit=top_k,
                output_fields=["content", "metadata", "timestamp"]
            )
            
            return [
                {
                    "content": hit.entity.get("content"),
                    "metadata": hit.entity.get("metadata"),
                    "timestamp": hit.entity.get("timestamp"),
                    "score": hit.score
                }
                for hit in results[0]
            ]
            
        except Exception as e:
            logger.error(f"❌ 搜索相关上下文失败: {e}")
            return []
    
    def _text_to_vector(self, text: str) -> List[float]:
        """文本转向量（简化实现）"""
        # 实际应该调用BGE-M3或其他embedding模型
        # 这里返回随机向量作为示例
        return np.random.random(768).tolist()


class N8NDataCollector:
    """N8N数据收集器"""
    
    def __init__(self, n8n_webhook_url: str):
        self.webhook_url = n8n_webhook_url
        self.last_sync_time = None
    
    def trigger_daily_analysis(self) -> Dict:
        """触发N8N的每日分析流程"""
        try:
            payload = {
                "action": "daily_analysis",
                "timestamp": datetime.now().isoformat(),
                "request_id": f"daily_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            }
            
            response = requests.post(
                self.webhook_url,
                json=payload,
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                logger.info("✅ N8N每日分析触发成功")
                return result
            else:
                logger.error(f"❌ N8N触发失败: {response.status_code}")
                return {"error": f"HTTP {response.status_code}"}
                
        except Exception as e:
            logger.error(f"❌ N8N触发异常: {e}")
            return {"error": str(e)}
    
    def get_latest_rss_analysis(self) -> Dict:
        """获取最新的RSS分析结果"""
        try:
            # 调用N8N的RSS分析结果API
            response = requests.get(
                f"{self.webhook_url}/rss-analysis/latest",
                timeout=30
            )
            
            if response.status_code == 200:
                return response.json()
            else:
                return {"error": f"HTTP {response.status_code}"}
                
        except Exception as e:
            logger.error(f"❌ 获取RSS分析失败: {e}")
            return {"error": str(e)}


class AutoGenMCPBridge:
    """AutoGen与MCP的桥接器"""
    
    def __init__(self, zilliz_source: ZillizTruthSource):
        self.zilliz = zilliz_source
        self.autogen_agents = {}
        self.mcp_tools = {}
    
    def prepare_daily_context(self, date: str = None) -> Dict:
        """为AutoGen准备每日上下文"""
        # 1. 从Zilliz获取今日情报
        intelligence = self.zilliz.get_daily_intelligence(date)
        
        # 2. 构建AutoGen所需的上下文
        context = {
            "date": intelligence.get("date"),
            "market_summary": intelligence.get("summary", {}),
            "key_intelligence": intelligence.get("data", [])[:10],  # 取前10条
            "debate_topics": self._extract_debate_topics(intelligence),
            "agent_assignments": self._assign_agent_roles(intelligence)
        }
        
        return context
    
    def _extract_debate_topics(self, intelligence: Dict) -> List[Dict]:
        """从情报中提取辩论话题"""
        if not intelligence.get("summary"):
            return []
        
        summary = intelligence["summary"]
        topics = []
        
        # 基于关键词生成辩论话题
        top_keywords = summary.get("top_keywords", [])
        
        for keyword, freq in top_keywords[:3]:  # 取前3个热门关键词
            topic = {
                "keyword": keyword,
                "frequency": freq,
                "debate_type": self._classify_debate_type(keyword),
                "suggested_participants": self._suggest_participants(keyword)
            }
            topics.append(topic)
        
        return topics
    
    def _classify_debate_type(self, keyword: str) -> str:
        """分类辩论类型"""
        keyword_lower = keyword.lower()
        
        if any(word in keyword_lower for word in ["政策", "央行", "利率", "货币"]):
            return "policy_analysis"
        elif any(word in keyword_lower for word in ["科技", "AI", "新能源", "创新"]):
            return "sector_analysis"
        elif any(word in keyword_lower for word in ["风险", "波动", "调整", "下跌"]):
            return "risk_assessment"
        else:
            return "general_discussion"
    
    def _suggest_participants(self, keyword: str) -> List[str]:
        """建议参与者"""
        keyword_lower = keyword.lower()
        
        # 根据关键词建议合适的分析师
        if any(word in keyword_lower for word in ["政策", "宏观"]):
            return ["太上老君", "灵宝天尊"]
        elif any(word in keyword_lower for word in ["技术", "量化"]):
            return ["元始天尊", "蓝采和"]
        elif any(word in keyword_lower for word in ["成长", "科技"]):
            return ["吕洞宾", "何仙姑"]
        elif any(word in keyword_lower for word in ["价值", "传统"]):
            return ["张果老", "曹国舅"]
        elif any(word in keyword_lower for word in ["逆向", "风险"]):
            return ["铁拐李", "灵宝天尊"]
        else:
            return ["太上老君", "元始天尊", "铁拐李"]  # 默认三清
    
    def _assign_agent_roles(self, intelligence: Dict) -> Dict:
        """分配分析师角色"""
        summary = intelligence.get("summary", {})
        sentiment = summary.get("average_sentiment", 0)
        
        # 根据市场情绪分配角色
        if sentiment > 0.2:  # 乐观情绪
            primary_voice = "吕洞宾"  # 成长股专家
            supporting_voices = ["汉钟离", "何仙姑"]
            contrarian_voice = "铁拐李"
        elif sentiment < -0.2:  # 悲观情绪
            primary_voice = "灵宝天尊"  # 风险控制
            supporting_voices = ["张果老", "曹国舅"]
            contrarian_voice = "吕洞宾"
        else:  # 中性情绪
            primary_voice = "太上老君"  # 哲学分析
            supporting_voices = ["元始天尊", "张果老"]
            contrarian_voice = "铁拐李"
        
        return {
            "primary_voice": primary_voice,
            "supporting_voices": supporting_voices,
            "contrarian_voice": contrarian_voice,
            "moderator": "太上老君"  # 总是由太上老君主持
        }
    
    def create_mcp_tool_context(self, autogen_results: Dict) -> Dict:
        """为MCP工具创建上下文"""
        return {
            "daily_consensus": autogen_results.get("consensus", {}),
            "debate_highlights": autogen_results.get("key_debates", []),
            "agent_positions": autogen_results.get("agent_positions", {}),
            "market_outlook": autogen_results.get("market_outlook", {}),
            "risk_alerts": autogen_results.get("risk_alerts", []),
            "investment_recommendations": autogen_results.get("recommendations", [])
        }


class DailyWorkflowOrchestrator:
    """每日工作流编排器"""
    
    def __init__(self, config: Dict):
        self.config = config
        self.zilliz = ZillizTruthSource(config)
        self.n8n = N8NDataCollector(config['n8n']['webhook_url'])
        self.bridge = AutoGenMCPBridge(self.zilliz)
    
    async def run_daily_workflow(self):
        """运行每日工作流"""
        logger.info("🚀 开始每日工作流")
        
        try:
            # 1. 触发N8N收集和分析RSS数据
            logger.info("📡 触发N8N数据收集...")
            n8n_result = self.n8n.trigger_daily_analysis()
            
            if "error" in n8n_result:
                logger.error(f"❌ N8N数据收集失败: {n8n_result['error']}")
                return
            
            # 等待N8N处理完成（实际应该通过webhook回调）
            await asyncio.sleep(300)  # 等待5分钟
            
            # 2. 从Zilliz获取处理后的数据作为唯一真理来源
            logger.info("🔍 从Zilliz获取今日情报...")
            daily_context = self.bridge.prepare_daily_context()
            
            # 3. 启动AutoGen稷下学宫辩论
            logger.info("🏛️ 启动稷下学宫辩论...")
            autogen_results = await self._run_autogen_debate(daily_context)
            
            # 4. 为MCP工具准备上下文
            logger.info("🔧 准备MCP工具上下文...")
            mcp_context = self.bridge.create_mcp_tool_context(autogen_results)
            
            # 5. 更新MCP工具的知识库
            logger.info("📚 更新MCP工具知识库...")
            await self._update_mcp_knowledge(mcp_context)
            
            logger.info("✅ 每日工作流完成")
            
        except Exception as e:
            logger.error(f"❌ 每日工作流失败: {e}")
    
    async def _run_autogen_debate(self, context: Dict) -> Dict:
        """运行AutoGen辩论"""
        # 这里应该调用实际的AutoGen系统
        # 暂时返回模拟结果
        return {
            "consensus": {"market_direction": "neutral", "confidence": 0.7},
            "key_debates": context.get("debate_topics", []),
            "agent_positions": context.get("agent_assignments", {}),
            "market_outlook": {"short_term": "volatile", "long_term": "positive"},
            "risk_alerts": ["地缘政治风险", "流动性收紧"],
            "recommendations": ["关注科技股回调机会", "增加防御性配置"]
        }
    
    async def _update_mcp_knowledge(self, context: Dict):
        """更新MCP工具知识库"""
        # 将AutoGen的结果更新到MCP工具的知识库
        # 这样MCP工具就能基于最新的分析结果回答用户问题
        knowledge_update = {
            "timestamp": datetime.now().isoformat(),
            "source": "autogen_daily_debate",
            "context": context
        }
        
        # 保存到文件或数据库，供MCP工具调用
        with open("mcp_daily_knowledge.json", "w", encoding="utf-8") as f:
            json.dump(knowledge_update, f, ensure_ascii=False, indent=2)
        
        logger.info("📝 MCP知识库已更新")


# 配置示例
DEFAULT_CONFIG = {
    "zilliz": {
        "host": "your-zilliz-host",
        "port": 19530,
        "user": "your-username", 
        "password": "your-password"
    },
    "n8n": {
        "webhook_url": "https://your-n8n-instance.com/webhook/daily-analysis"
    },
    "autogen": {
        "config_file": "autogen_config.json"
    },
    "mcp": {
        "knowledge_file": "mcp_daily_knowledge.json"
    }
}


if __name__ == "__main__":
    # 运行每日工作流
    orchestrator = DailyWorkflowOrchestrator(DEFAULT_CONFIG)
    asyncio.run(orchestrator.run_daily_workflow())
