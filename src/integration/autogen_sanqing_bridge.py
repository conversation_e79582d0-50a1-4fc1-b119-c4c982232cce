#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AutoGen八仙论道与三清验证系统桥接器
连接八仙辩论系统和三清验证流程
"""

import asyncio
import json
import logging
from datetime import datetime
from typing import Dict, List, Any, Optional, Callable
from dataclasses import dataclass
import autogen

from ..core.sanqing_verification_orchestrator import (
    SanqingVerificationOrchestrator, 
    VerificationConfig,
    BaxianDebateResult
)

logger = logging.getLogger("AutoGenSanqingBridge")

@dataclass
class BaxianAgent:
    """八仙代理配置"""
    name: str
    role: str
    gua_position: str  # 八卦位置
    system_message: str
    model_config: Dict[str, Any]

class AutoGenSanqingBridge:
    """AutoGen与三清验证系统的桥接器"""
    
    def __init__(self, verification_config: VerificationConfig):
        self.verification_config = verification_config
        self.sanqing_orchestrator = SanqingVerificationOrchestrator(verification_config)
        self.debate_history = []
        self.verification_callbacks = []
        
        # 初始化八仙代理
        self.baxian_agents = self._initialize_baxian_agents()
        
    def _initialize_baxian_agents(self) -> Dict[str, autogen.AssistantAgent]:
        """初始化八仙AutoGen代理"""
        
        # 八仙配置
        baxian_configs = {
            "吕洞宾": BaxianAgent(
                name="吕洞宾",
                role="剑仙投资顾问",
                gua_position="乾☰",
                system_message="""
                你是吕洞宾，八仙之首，剑仙投资顾问。
                你的特点：
                - 以剑气纵横的气势分析市场
                - 善于识破市场迷雾，直击要害
                - 偏好高风险高收益的投资策略
                - 用古典诗词表达投资观点
                
                在辩论中，你要：
                1. 提出犀利的市场观点
                2. 用数据和逻辑支撑论断
                3. 挑战其他仙人的观点
                4. 保持仙风道骨的表达风格
                """,
                model_config={"model": "gpt-4", "temperature": 0.7}
            ),
            
            "何仙姑": BaxianAgent(
                name="何仙姑",
                role="慈悲风控专家", 
                gua_position="坤☷",
                system_message="""
                你是何仙姑，八仙中唯一的女仙，慈悲风控专家。
                你的特点：
                - 以母性的关怀关注投资风险
                - 善于发现市场中的隐藏危险
                - 偏好稳健保守的投资策略
                - 用温和但坚定的语气表达观点
                
                在辩论中，你要：
                1. 重点关注风险控制
                2. 提醒其他仙人注意潜在危险
                3. 提供稳健的投资建议
                4. 平衡激进与保守的观点
                """,
                model_config={"model": "gpt-4", "temperature": 0.5}
            ),
            
            "铁拐李": BaxianAgent(
                name="铁拐李",
                role="逆向思维大师",
                gua_position="震☳", 
                system_message="""
                你是铁拐李，八仙中的逆向思维大师。
                你的特点：
                - 总是从反面角度思考问题
                - 善于发现市场的反向机会
                - 不拘一格，敢于挑战主流观点
                - 用直率犀利的语言表达
                
                在辩论中，你要：
                1. 提出与众不同的观点
                2. 挑战市场共识
                3. 寻找逆向投资机会
                4. 用数据证明反向逻辑
                """,
                model_config={"model": "gpt-4", "temperature": 0.8}
            ),
            
            "蓝采和": BaxianAgent(
                name="蓝采和",
                role="情绪分析师",
                gua_position="巽☴",
                system_message="""
                你是蓝采和，八仙中的情绪分析师。
                你的特点：
                - 敏锐感知市场情绪变化
                - 善于分析投资者心理
                - 关注社交媒体和舆论趋势
                - 用轻松活泼的语调表达
                
                在辩论中，你要：
                1. 分析市场情绪指标
                2. 关注投资者情绪变化
                3. 提供情绪面的投资建议
                4. 用生动的比喻说明观点
                """,
                model_config={"model": "gpt-4", "temperature": 0.6}
            )
        }
        
        # 创建AutoGen代理
        agents = {}
        for name, config in baxian_configs.items():
            agent = autogen.AssistantAgent(
                name=config.name,
                system_message=config.system_message,
                llm_config=config.model_config
            )
            agents[name] = agent
            
        return agents
    
    async def conduct_baxian_debate(self, 
                                  topic: str, 
                                  context: Dict[str, Any],
                                  max_rounds: int = 6) -> BaxianDebateResult:
        """进行八仙论道"""
        logger.info(f"🎭 开始八仙论道: {topic}")
        
        # 创建群聊管理器
        groupchat = autogen.GroupChat(
            agents=list(self.baxian_agents.values()),
            messages=[],
            max_round=max_rounds,
            speaker_selection_method="round_robin"
        )
        
        manager = autogen.GroupChatManager(
            groupchat=groupchat,
            llm_config={"model": "gpt-4", "temperature": 0.3}
        )
        
        # 构建初始提示
        initial_prompt = self._build_debate_prompt(topic, context)
        
        # 开始辩论
        debate_messages = []
        try:
            # 启动辩论
            chat_result = await self._run_async_chat(
                self.baxian_agents["吕洞宾"], 
                manager, 
                initial_prompt
            )
            
            # 提取辩论消息
            debate_messages = groupchat.messages
            
        except Exception as e:
            logger.error(f"八仙论道过程中出错: {e}")
            raise
        
        # 分析辩论结果
        debate_analysis = self._analyze_debate_messages(debate_messages)
        
        # 生成辩论结果
        debate_result = BaxianDebateResult(
            debate_id=f"baxian_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
            topic=topic,
            participants=list(self.baxian_agents.keys()),
            conclusions=debate_analysis["conclusions"],
            confidence_score=debate_analysis["confidence_score"],
            timestamp=datetime.now(),
            key_claims=debate_analysis["key_claims"]
        )
        
        self.debate_history.append(debate_result)
        return debate_result
    
    async def _run_async_chat(self, initiator, manager, message):
        """异步运行聊天"""
        # 这里需要根据autogen的实际API调整
        # 目前autogen可能不直接支持async，需要在线程池中运行
        import concurrent.futures
        
        loop = asyncio.get_event_loop()
        with concurrent.futures.ThreadPoolExecutor() as executor:
            result = await loop.run_in_executor(
                executor,
                lambda: initiator.initiate_chat(manager, message=message)
            )
        return result
    
    def _build_debate_prompt(self, topic: str, context: Dict[str, Any]) -> str:
        """构建辩论提示"""
        prompt = f"""
        🎭 八仙论道开始！
        
        论道主题: {topic}
        
        背景信息:
        {json.dumps(context, indent=2, ensure_ascii=False)}
        
        论道规则:
        1. 每位仙人从自己的专业角度分析
        2. 必须提供具体的数据支撑
        3. 可以质疑其他仙人的观点
        4. 最终要形成一致的投资建议
        5. 保持仙风道骨的表达风格
        
        请吕洞宾仙长首先发言，其他仙人依次论道。
        """
        return prompt
    
    def _analyze_debate_messages(self, messages: List[Dict]) -> Dict[str, Any]:
        """分析辩论消息，提取关键信息"""
        
        # 提取关键论断
        key_claims = []
        participant_views = {}
        
        for msg in messages:
            speaker = msg.get("name", "unknown")
            content = msg.get("content", "")
            
            # 简单的关键信息提取（实际应该用更复杂的NLP）
            if "预测" in content or "认为" in content or "建议" in content:
                key_claims.append(content[:100] + "...")
            
            if speaker not in participant_views:
                participant_views[speaker] = []
            participant_views[speaker].append(content)
        
        # 计算置信度（基于参与度和一致性）
        confidence_score = min(0.9, len(key_claims) * 0.15 + 0.3)
        
        # 生成结论
        conclusions = {
            "investment_direction": "待定",  # 需要从消息中提取
            "risk_level": "中等",
            "time_horizon": "短期",
            "participant_consensus": len(participant_views) >= 3
        }
        
        return {
            "key_claims": key_claims[:5],  # 最多5个关键论断
            "conclusions": conclusions,
            "confidence_score": confidence_score,
            "participant_views": participant_views
        }
    
    async def conduct_full_verification_cycle(self, 
                                            topic: str,
                                            context: Dict[str, Any]) -> Dict[str, Any]:
        """执行完整的论道+验证周期"""
        logger.info(f"🌟 开始完整的八仙论道+三清验证周期")
        
        try:
            # 第一步：八仙论道
            debate_result = await self.conduct_baxian_debate(topic, context)
            logger.info(f"✅ 八仙论道完成，置信度: {debate_result.confidence_score:.2f}")
            
            # 第二步：三清验证
            verification_result = await self.sanqing_orchestrator.orchestrate_verification(
                debate_result
            )
            logger.info(f"✅ 三清验证完成，最终决策: {verification_result['final_assessment']['decision']}")
            
            # 第三步：生成综合报告
            comprehensive_report = self._generate_comprehensive_report(
                debate_result, verification_result
            )
            
            # 触发回调
            await self._trigger_verification_callbacks(comprehensive_report)
            
            return comprehensive_report
            
        except Exception as e:
            logger.error(f"完整验证周期失败: {e}")
            return {
                "status": "error",
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }
    
    def _generate_comprehensive_report(self, 
                                     debate_result: BaxianDebateResult,
                                     verification_result: Dict[str, Any]) -> Dict[str, Any]:
        """生成综合报告"""
        return {
            "report_id": f"comprehensive_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
            "executive_summary": {
                "topic": debate_result.topic,
                "debate_confidence": debate_result.confidence_score,
                "verification_confidence": verification_result["final_assessment"]["final_confidence"],
                "final_decision": verification_result["final_assessment"]["decision"],
                "recommendation": self._generate_final_recommendation(
                    debate_result, verification_result
                )
            },
            "baxian_debate": {
                "debate_id": debate_result.debate_id,
                "participants": debate_result.participants,
                "key_claims": debate_result.key_claims,
                "conclusions": debate_result.conclusions
            },
            "sanqing_verification": verification_result,
            "implementation_plan": self._generate_implementation_plan(
                verification_result["final_assessment"]
            ),
            "risk_assessment": self._generate_risk_assessment(
                debate_result, verification_result
            ),
            "monitoring_plan": self._generate_monitoring_plan(debate_result),
            "metadata": {
                "generation_timestamp": datetime.now().isoformat(),
                "system_version": "八仙论道+三清验证系统 v1.0",
                "confidence_evolution": {
                    "initial": debate_result.confidence_score,
                    "final": verification_result["final_assessment"]["final_confidence"],
                    "change": verification_result["final_assessment"]["confidence_change"]
                }
            }
        }
    
    def _generate_final_recommendation(self, 
                                     debate_result: BaxianDebateResult,
                                     verification_result: Dict[str, Any]) -> str:
        """生成最终建议"""
        decision = verification_result["final_assessment"]["decision"]
        confidence = verification_result["final_assessment"]["final_confidence"]
        
        if decision == "APPROVE" and confidence >= 0.8:
            return "强烈推荐：八仙论道结果经三清验证，可信度极高，建议积极实施"
        elif decision == "APPROVE":
            return "推荐：验证通过，建议谨慎实施并密切监控"
        elif decision == "CONDITIONAL_APPROVE":
            return "条件推荐：部分验证通过，建议小规模试验性实施"
        else:
            return "不推荐：验证未通过，建议重新论道或等待更多信息"
    
    def _generate_implementation_plan(self, final_assessment: Dict[str, Any]) -> List[str]:
        """生成实施计划"""
        return final_assessment.get("implementation_guidance", [
            "制定详细的实施方案",
            "设置风险控制措施", 
            "建立监控机制"
        ])
    
    def _generate_risk_assessment(self, 
                                debate_result: BaxianDebateResult,
                                verification_result: Dict[str, Any]) -> Dict[str, Any]:
        """生成风险评估"""
        return {
            "overall_risk": "中等",
            "key_risks": [
                "市场波动风险",
                "信息不确定性",
                "执行风险"
            ],
            "risk_mitigation": [
                "分散投资",
                "设置止损",
                "定期评估"
            ]
        }
    
    def _generate_monitoring_plan(self, debate_result: BaxianDebateResult) -> Dict[str, Any]:
        """生成监控计划"""
        return {
            "monitoring_frequency": "每日",
            "key_indicators": [
                "价格变化",
                "成交量",
                "市场情绪"
            ],
            "alert_thresholds": {
                "price_change": "±5%",
                "volume_change": "±50%"
            },
            "review_schedule": "每周回顾，每月深度分析"
        }
    
    def add_verification_callback(self, callback: Callable):
        """添加验证完成回调"""
        self.verification_callbacks.append(callback)
    
    async def _trigger_verification_callbacks(self, report: Dict[str, Any]):
        """触发验证回调"""
        for callback in self.verification_callbacks:
            try:
                if asyncio.iscoroutinefunction(callback):
                    await callback(report)
                else:
                    callback(report)
            except Exception as e:
                logger.error(f"回调执行失败: {e}")

# 使用示例
async def demonstrate_full_system():
    """演示完整系统"""
    
    # 配置
    verification_config = VerificationConfig(
        openmanus_url="https://your-openmanus-instance.com",
        openmanus_api_key="your-api-key",
        zilliz_config={
            "host": "your-zilliz-host",
            "port": 19530,
            "collection_name": "jixia_knowledge"
        },
        confidence_threshold=0.6
    )
    
    # 创建桥接器
    bridge = AutoGenSanqingBridge(verification_config)
    
    # 添加回调
    async def verification_complete_callback(report):
        print(f"🎉 验证完成通知: {report['executive_summary']['final_decision']}")
    
    bridge.add_verification_callback(verification_complete_callback)
    
    # 执行完整周期
    topic = "苹果公司Q1财报对股价影响分析"
    context = {
        "current_price": 150.0,
        "market_cap": "2.5T",
        "recent_news": ["iPhone销量超预期", "服务业务增长强劲"],
        "analyst_consensus": "买入"
    }
    
    comprehensive_report = await bridge.conduct_full_verification_cycle(topic, context)
    
    print("🌟 八仙论道+三清验证综合报告:")
    print(json.dumps(comprehensive_report, indent=2, ensure_ascii=False))

if __name__ == "__main__":
    asyncio.run(demonstrate_full_system())