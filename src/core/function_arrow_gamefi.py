# -*- coding: utf-8 -*-
"""
🏹 函数射箭GameFi系统
基于您的哲学洞察：道法、妖术、神迹的函数本质

核心理念：
- 函数 = 射箭过程 = 主观意图 → 客观结果
- 非对称加密 = 第一粒沙 → 聚沙成塔的神迹
- 倒行逆施，呵佛骂祖，方得正果
- 让gamer玩的有所获，有所不为

作者：太公心易BI系统  
版本：v5.0 Function Arrow Edition
"""

import asyncio
import json
import hashlib
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple, Callable
from dataclasses import dataclass, asdict
from enum import Enum
import random
import numpy as np

class ArrowType(Enum):
    """箭矢类型 - 不同的函数射箭方式"""
    MORTAL_ARROW = ("凡箭", "普通投资决策", 1.0)           # 传统认知
    MAGIC_ARROW = ("妖箭", "突破常规的策略", 2.0)          # 妖术
    DIVINE_ARROW = ("神箭", "颠覆性的洞察", 5.0)          # 神迹
    CHAOS_ARROW = ("乱箭", "看似疯狂的决策", 10.0)        # 醉话疯话
    
    def __init__(self, name: str, description: str, multiplier: float):
        self.arrow_name = name
        self.description = description
        self.multiplier = multiplier

class TargetType(Enum):
    """靶心类型 - 不同的市场目标"""
    BULL_EYE = ("正中靶心", "完美预测市场", 100)
    INNER_RING = ("内环", "基本正确的判断", 80)
    OUTER_RING = ("外环", "部分正确", 50)
    NEAR_MISS = ("擦边", "接近但未中", 20)
    COMPLETE_MISS = ("脱靶", "完全错误", 0)
    
    def __init__(self, name: str, description: str, score: int):
        self.target_name = name
        self.description = description
        self.score = score

@dataclass
class FunctionArrow:
    """函数射箭记录"""
    arrow_id: str
    shooter_id: str
    arrow_type: ArrowType
    
    # 射箭三要素（刑法论）
    subjective_intent: str      # 主观要射箭
    objective_result: str       # 客观箭头中靶
    causal_relationship: bool   # 因果关系
    
    # 射箭参数
    aim_direction: float        # 瞄准方向 (0-360度)
    force_applied: float        # 用力大小 (0-100)
    wind_factor: float          # 市场风向 (-50 to 50)
    
    # 结果
    hit_target: TargetType
    actual_position: Tuple[float, float]  # 实际落点
    distance_from_center: float          # 距离靶心距离
    
    # 奖励
    base_score: int
    multiplier_bonus: float
    final_score: float
    
    # 元数据
    timestamp: datetime
    market_context: str
    asymmetric_seed: str        # 非对称加密种子

@dataclass
class SandGrain:
    """沙粒 - 聚沙成塔的基础单元"""
    grain_id: str
    grain_type: str             # 知识、经验、洞察、疯狂
    value: float
    origin_arrow: str           # 来源于哪次射箭
    asymmetric_hash: str        # 非对称加密哈希
    accumulated_power: float    # 累积能量

class FunctionArrowGameFi:
    """函数射箭GameFi系统"""
    
    def __init__(self, player_id: str):
        self.player_id = player_id
        self.arrows_shot: List[FunctionArrow] = []
        self.sand_tower: List[SandGrain] = []
        self.total_score = 0
        self.enlightenment_level = 0
        
        # 射箭统计
        self.accuracy_rate = 0.0
        self.divine_shots = 0
        self.chaos_successes = 0
        
        # 非对称加密基础
        self.master_seed = self._generate_asymmetric_seed()
        
    def _generate_asymmetric_seed(self) -> str:
        """生成非对称加密种子 - 第一粒沙"""
        timestamp = datetime.now().isoformat()
        player_data = f"{self.player_id}_{timestamp}_{random.random()}"
        return hashlib.sha256(player_data.encode()).hexdigest()
    
    def shoot_arrow(self, 
                   intent: str,
                   arrow_type: ArrowType,
                   aim_direction: float,
                   force: float,
                   market_context: str) -> FunctionArrow:
        """射箭函数 - 核心GameFi机制"""
        
        # 生成箭矢ID
        arrow_id = f"arrow_{len(self.arrows_shot) + 1}_{int(datetime.now().timestamp())}"
        
        # 计算市场风向（随机因素）
        wind_factor = random.uniform(-50, 50)
        
        # 计算实际落点
        actual_position, hit_target, distance = self._calculate_arrow_trajectory(
            aim_direction, force, wind_factor, arrow_type
        )
        
        # 判断因果关系（刑法论核心）
        causal_relationship = self._determine_causality(intent, actual_position, hit_target)
        
        # 计算得分
        base_score = hit_target.score
        multiplier_bonus = arrow_type.multiplier
        final_score = base_score * multiplier_bonus
        
        # 如果是"疯话醉话"但命中，额外奖励
        if arrow_type == ArrowType.CHAOS_ARROW and hit_target.score > 50:
            final_score *= 2  # 倒行逆施，呵佛骂祖，方得正果
            self.chaos_successes += 1
        
        # 创建射箭记录
        arrow = FunctionArrow(
            arrow_id=arrow_id,
            shooter_id=self.player_id,
            arrow_type=arrow_type,
            subjective_intent=intent,
            objective_result=f"箭矢落在{actual_position}，{hit_target.target_name}",
            causal_relationship=causal_relationship,
            aim_direction=aim_direction,
            force_applied=force,
            wind_factor=wind_factor,
            hit_target=hit_target,
            actual_position=actual_position,
            distance_from_center=distance,
            base_score=base_score,
            multiplier_bonus=multiplier_bonus,
            final_score=final_score,
            timestamp=datetime.now(),
            market_context=market_context,
            asymmetric_seed=self._generate_asymmetric_seed()
        )
        
        # 记录射箭
        self.arrows_shot.append(arrow)
        self.total_score += final_score
        
        # 生成沙粒（聚沙成塔）
        sand_grain = self._create_sand_grain(arrow)
        self.sand_tower.append(sand_grain)
        
        # 更新统计
        self._update_statistics()
        
        return arrow
    
    def _calculate_arrow_trajectory(self, 
                                  aim_direction: float, 
                                  force: float, 
                                  wind_factor: float,
                                  arrow_type: ArrowType) -> Tuple[Tuple[float, float], TargetType, float]:
        """计算箭矢轨迹"""
        
        # 基础轨迹计算
        base_x = force * np.cos(np.radians(aim_direction))
        base_y = force * np.sin(np.radians(aim_direction))
        
        # 风向影响
        wind_x = wind_factor * 0.1
        wind_y = wind_factor * 0.05
        
        # 箭矢类型影响（不同类型有不同的随机性）
        if arrow_type == ArrowType.CHAOS_ARROW:
            # 疯话醉话：高随机性，但可能有神奇效果
            chaos_x = random.uniform(-30, 30)
            chaos_y = random.uniform(-30, 30)
            final_x = base_x + wind_x + chaos_x
            final_y = base_y + wind_y + chaos_y
        elif arrow_type == ArrowType.DIVINE_ARROW:
            # 神迹：减少随机性，更容易命中
            final_x = base_x + wind_x * 0.5
            final_y = base_y + wind_y * 0.5
        else:
            # 普通箭矢
            final_x = base_x + wind_x
            final_y = base_y + wind_y
        
        actual_position = (final_x, final_y)
        
        # 计算距离靶心的距离
        distance_from_center = np.sqrt(final_x**2 + final_y**2)
        
        # 判断命中区域
        if distance_from_center <= 10:
            hit_target = TargetType.BULL_EYE
        elif distance_from_center <= 25:
            hit_target = TargetType.INNER_RING
        elif distance_from_center <= 50:
            hit_target = TargetType.OUTER_RING
        elif distance_from_center <= 80:
            hit_target = TargetType.NEAR_MISS
        else:
            hit_target = TargetType.COMPLETE_MISS
        
        return actual_position, hit_target, distance_from_center
    
    def _determine_causality(self, intent: str, position: Tuple[float, float], hit_target: TargetType) -> bool:
        """判断因果关系 - 刑法论核心"""
        # 简化的因果关系判断
        # 如果主观意图明确且客观结果符合预期，则存在因果关系
        
        intent_keywords = ["盈利", "赚钱", "成功", "命中", "正确"]
        has_positive_intent = any(keyword in intent for keyword in intent_keywords)
        
        has_positive_result = hit_target.score > 50
        
        return has_positive_intent and has_positive_result
    
    def _create_sand_grain(self, arrow: FunctionArrow) -> SandGrain:
        """创建沙粒 - 聚沙成塔"""
        
        # 根据射箭结果确定沙粒类型和价值
        if arrow.hit_target == TargetType.BULL_EYE:
            grain_type = "智慧沙粒"
            value = 10.0
        elif arrow.arrow_type == ArrowType.CHAOS_ARROW and arrow.hit_target.score > 0:
            grain_type = "疯狂沙粒"  # 疯话成真
            value = 15.0
        elif arrow.causal_relationship:
            grain_type = "因果沙粒"
            value = 5.0
        else:
            grain_type = "经验沙粒"
            value = 1.0
        
        # 生成非对称哈希
        grain_data = f"{arrow.arrow_id}_{grain_type}_{value}_{arrow.asymmetric_seed}"
        asymmetric_hash = hashlib.sha256(grain_data.encode()).hexdigest()
        
        # 计算累积能量
        accumulated_power = sum(grain.value for grain in self.sand_tower) + value
        
        return SandGrain(
            grain_id=f"grain_{len(self.sand_tower) + 1}",
            grain_type=grain_type,
            value=value,
            origin_arrow=arrow.arrow_id,
            asymmetric_hash=asymmetric_hash,
            accumulated_power=accumulated_power
        )
    
    def _update_statistics(self):
        """更新射箭统计"""
        if not self.arrows_shot:
            return
        
        # 计算命中率
        hits = sum(1 for arrow in self.arrows_shot if arrow.hit_target.score > 0)
        self.accuracy_rate = hits / len(self.arrows_shot)
        
        # 计算神箭次数
        self.divine_shots = sum(1 for arrow in self.arrows_shot 
                               if arrow.arrow_type == ArrowType.DIVINE_ARROW and arrow.hit_target.score > 50)
        
        # 计算觉悟等级
        self.enlightenment_level = min(100, int(self.total_score / 100))
    
    def get_tower_status(self) -> Dict[str, Any]:
        """获取沙塔状态"""
        if not self.sand_tower:
            return {"message": "沙塔尚未建立"}
        
        total_grains = len(self.sand_tower)
        total_power = sum(grain.accumulated_power for grain in self.sand_tower)
        
        grain_types = {}
        for grain in self.sand_tower:
            grain_types[grain.grain_type] = grain_types.get(grain.grain_type, 0) + 1
        
        return {
            "total_grains": total_grains,
            "total_power": total_power,
            "grain_types": grain_types,
            "tower_height": total_grains * 0.1,  # 每粒沙0.1米
            "is_miracle": total_power > 1000,    # 超过1000能量就是神迹
            "asymmetric_foundation": self.master_seed[:16]
        }
    
    def analyze_shooting_pattern(self) -> Dict[str, Any]:
        """分析射箭模式 - 寻找函数规律"""
        if len(self.arrows_shot) < 3:
            return {"message": "射箭次数太少，无法分析模式"}
        
        # 分析射箭倾向
        arrow_type_counts = {}
        for arrow in self.arrows_shot:
            arrow_type_counts[arrow.arrow_type.arrow_name] = arrow_type_counts.get(arrow.arrow_type.arrow_name, 0) + 1
        
        # 分析成功模式
        successful_arrows = [arrow for arrow in self.arrows_shot if arrow.hit_target.score > 50]
        
        if successful_arrows:
            avg_force = sum(arrow.force_applied for arrow in successful_arrows) / len(successful_arrows)
            avg_direction = sum(arrow.aim_direction for arrow in successful_arrows) / len(successful_arrows)
        else:
            avg_force = 0
            avg_direction = 0
        
        return {
            "total_shots": len(self.arrows_shot),
            "accuracy_rate": f"{self.accuracy_rate:.2%}",
            "arrow_preferences": arrow_type_counts,
            "successful_shots": len(successful_arrows),
            "optimal_force": avg_force,
            "optimal_direction": avg_direction,
            "chaos_successes": self.chaos_successes,
            "divine_shots": self.divine_shots,
            "enlightenment_level": self.enlightenment_level,
            "pattern_insight": self._generate_pattern_insight()
        }
    
    def _generate_pattern_insight(self) -> str:
        """生成模式洞察"""
        if self.chaos_successes > 0:
            return "你已掌握'倒行逆施，呵佛骂祖'的精髓，疯话中蕴含真理"
        elif self.divine_shots > 2:
            return "你的神箭频出，已接近道法自然的境界"
        elif self.accuracy_rate > 0.7:
            return "你的射箭技艺日趋精进，函数规律渐显"
        else:
            return "继续练习，每一箭都是向真理的靠近"

# 演示函数
async def demo_function_arrow_gamefi():
    """演示函数射箭GameFi系统"""
    print("🏹 函数射箭GameFi系统演示")
    print("=" * 60)
    
    # 创建玩家
    player = FunctionArrowGameFi("archer_philosopher")
    
    # 模拟几次射箭
    shooting_scenarios = [
        {
            "intent": "我要通过价值投资获得长期收益",
            "arrow_type": ArrowType.MORTAL_ARROW,
            "aim": 45,
            "force": 70,
            "context": "牛市初期，基本面良好"
        },
        {
            "intent": "市场都说要跌，我偏要做多",
            "arrow_type": ArrowType.CHAOS_ARROW,
            "aim": 180,  # 反向
            "force": 90,
            "context": "市场极度悲观时"
        },
        {
            "intent": "洞察到了市场结构性变化",
            "arrow_type": ArrowType.DIVINE_ARROW,
            "aim": 0,    # 正中
            "force": 85,
            "context": "技术革命前夜"
        }
    ]
    
    for i, scenario in enumerate(shooting_scenarios, 1):
        print(f"\n🏹 第{i}箭:")
        print(f"意图: {scenario['intent']}")
        print(f"箭型: {scenario['arrow_type'].arrow_name}")
        
        arrow = player.shoot_arrow(
            intent=scenario['intent'],
            arrow_type=scenario['arrow_type'],
            aim_direction=scenario['aim'],
            force=scenario['force'],
            market_context=scenario['context']
        )
        
        print(f"结果: {arrow.hit_target.target_name} (得分: {arrow.final_score:.1f})")
        print(f"因果关系: {'✅' if arrow.causal_relationship else '❌'}")
        
        await asyncio.sleep(0.5)  # 模拟时间间隔
    
    # 显示沙塔状态
    print("\n🏗️ 沙塔状态:")
    tower_status = player.get_tower_status()
    for key, value in tower_status.items():
        print(f"  {key}: {value}")
    
    # 显示射箭分析
    print("\n📊 射箭模式分析:")
    analysis = player.analyze_shooting_pattern()
    for key, value in analysis.items():
        print(f"  {key}: {value}")

if __name__ == "__main__":
    asyncio.run(demo_function_arrow_gamefi())