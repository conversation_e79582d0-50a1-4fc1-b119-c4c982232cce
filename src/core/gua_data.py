#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
六十四卦完整数据字典
太公心易系统的核心数据模块

数据结构：
- 每卦包含完整的卦辞、象辞、解读、判词
- 模块化设计，便于维护和扩展
- 支持多种查询和匹配方式

作者：太公心易BI系统
版本：v1.0 GuaData
"""

from datetime import datetime
from .xinyi_system import GuaXiang, GuaLevel, GuaCategory


def get_complete_gua_dict():
    """获取完整的六十四卦字典"""
    gua_dict = {}
    
    # ==================== 乾坤系列 ====================
    
    # 第1卦：乾卦 ䷀
    gua_dict[1] = GuaXiang(
        number=1, name="乾", alias="飞龙在天",
        level=GuaLevel.SHANG_SHANG, category=GuaCategory.QIAN_KUN,
        upper_gua="乾", lower_gua="乾", yao_lines="111111",
        gua_ci="飞龙乘云腾九霄，风云际会正当时。天命所归终有应，金榜题名洞房时。",
        xiang_ci="天行健，君子以自强不息。",
        interpretation="抽得此签预示着人生正处于巅峰时刻，事业、感情、财运皆为最佳状态。宜把握时机，顺势而为，必能成就大业。",
        judgment="乾卦总纲：龙德正中，天道酬勤。一飞冲天志如虹，九五至尊显神通。天机已现莫迟疑，不见时机不出征。",
        suitable_for=["创业", "投资", "求职", "考试", "婚姻", "重大决策"],
        avoid_when=["犹豫不决", "缺乏准备", "逆势而为"],
        keywords=["成功", "领导", "创新", "突破", "巅峰", "权威", "主动"],
        action_advice="积极进取，把握机遇，发挥领导才能，勇于承担责任",
        timing_advice="当前正是最佳时机，宜立即行动，不可犹豫",
        risk_warning="成功在望，但需防止骄傲自满，居高思危",
        created_at=datetime.now().isoformat()
    )
    
    # 第2卦：坤卦 ䷁
    gua_dict[2] = GuaXiang(
        number=2, name="坤", alias="厚德载物",
        level=GuaLevel.SHANG_SHANG, category=GuaCategory.QIAN_KUN,
        upper_gua="坤", lower_gua="坤", yao_lines="000000",
        gua_ci="大地含章守本真，厚积薄发待春临。柔顺包容成大器，福泽绵延荫子孙。",
        xiang_ci="地势坤，君子以厚德载物。",
        interpretation="此签强调积累与包容。当前虽非高调之时，但正是夯实基础、积蓄力量的好机会。以柔克刚，以静制动，未来可期。",
        judgment="坤卦总纲：厚德载物，包容万象。一步一印稳如山，厚积薄发不争先。天机深藏待时发，不见根基不建园。",
        suitable_for=["积累", "学习", "团队合作", "长期投资", "培养关系", "支持他人"],
        avoid_when=["急功近利", "强行出头", "忽视基础", "独断专行"],
        keywords=["稳定", "包容", "积累", "支持", "基础", "柔顺", "承载"],
        action_advice="稳扎稳打，注重积累，发挥支持作用，以德服人",
        timing_advice="当前宜蓄势待发，不宜急进，耐心等待时机",
        risk_warning="过于保守可能错失良机，需要适时主动",
        created_at=datetime.now().isoformat()
    )
    
    # 第3卦：屯卦 ䷂
    gua_dict[3] = GuaXiang(
        number=3, name="屯", alias="草创维艰",
        level=GuaLevel.ZHONG_PING, category=GuaCategory.CHENG_BAI,
        upper_gua="坎", lower_gua="震", yao_lines="010001",
        gua_ci="草木初生根未稳，风霜雨雪皆考验。艰难困苦磨意志，破土而出始见天。",
        xiang_ci="云雷屯，君子以经纶。",
        interpretation="抽得此签表示你正处初创阶段，万事开头难。虽有阻碍，但只要坚持到底，终将突破困境，迎来成长。",
        judgment="屯卦总纲：草创维艰，坚韧不拔。一番寒彻骨，梅花扑鼻香。天机暗示需坚持，不见曙光不放弃。",
        suitable_for=["新项目启动", "学习新技能", "克服困难", "建立基础"],
        avoid_when=["轻易放弃", "缺乏耐心", "避免挑战"],
        keywords=["初创", "困难", "坚持", "成长", "突破", "磨练"],
        action_advice="坚持不懈，勇于面对困难，寻求帮助和支持",
        timing_advice="当前是艰难期，需要耐心等待和持续努力",
        risk_warning="初期困难较多，需要充分的心理准备",
        created_at=datetime.now().isoformat()
    )
    
    # ==================== 水火系列 ====================
    
    # 第60卦：节卦 ䷻ (你的示例)
    gua_dict[60] = GuaXiang(
        number=60, name="节", alias="节制有度",
        level=GuaLevel.ZHONG_PING, category=GuaCategory.JIN_TUI,
        upper_gua="坎", lower_gua="兑", yao_lines="010110",
        gua_ci="节而有度，道法自然。一日一卦默如金，日满月盈忘繁星。天机泄净终有时，不见兔子不撒鹰。",
        xiang_ci="泽上有水，节；君子以制数度，议德行。",
        interpretation="抽得此卦，示警吾人，万事万物皆须遵循节制之道，不可逾越其分寸。卜算亦然，如同每日功课，贵在精而不在多。当过度的指引如日月般耀眼时，反而会遮蔽细微的真相。须知天机有其泄露的限度，不可频繁触碰；如同狩猎，不见兔子，不应轻率出击。智慧的运用在于适时、适度、内敛，方能长久。",
        judgment="节卦总纲：节而有度，道法自然。一日一卦默如金，日满月盈忘繁星。天机泄净终有时，不见兔子不撒鹰。",
        suitable_for=["制定规则", "控制风险", "节约资源", "自我约束", "建立秩序"],
        avoid_when=["过度放纵", "频繁决策", "无节制消费", "贪婪无度"],
        keywords=["节制", "适度", "规律", "约束", "平衡", "秩序", "自律"],
        action_advice="保持节制，适度而为，建立良好规律和习惯",
        timing_advice="当前需要控制节奏，不宜过度，循序渐进",
        risk_warning="过度节制可能限制发展机会，需要把握平衡",
        created_at=datetime.now().isoformat()
    )
    
    # 第63卦：既济卦 ䷾
    gua_dict[63] = GuaXiang(
        number=63, name="既济", alias="功成身退",
        level=GuaLevel.SHANG_JI, category=GuaCategory.CHENG_BAI,
        upper_gua="坎", lower_gua="离", yao_lines="010101",
        gua_ci="渡河已过彼岸到，回首来路多险峻。功成名就当知足，居安思危保长久。",
        xiang_ci="水在火上，既济；君子以思患而豫防之。",
        interpretation="此卦象征事情已经成功完成，目标已经达成。但正因为成功，更需要保持警惕，居安思危，防止乐极生悲。",
        judgment="既济总纲：功成身退，居安思危。一朝得志莫忘形，盛极而衰是天理。天机示警需谨慎，不见危机不安宁。",
        suitable_for=["巩固成果", "总结经验", "防范风险", "适时退出"],
        avoid_when=["骄傲自满", "忽视危险", "过度扩张"],
        keywords=["成功", "完成", "警惕", "巩固", "退守", "谨慎"],
        action_advice="巩固已有成果，保持谦逊，做好风险防范",
        timing_advice="成功之时更需谨慎，宜守不宜攻",
        risk_warning="盛极必衰，需要时刻保持警惕",
        created_at=datetime.now().isoformat()
    )
    
    # 第64卦：未济卦 ䷿
    gua_dict[64] = GuaXiang(
        number=64, name="未济", alias="事未完成",
        level=GuaLevel.ZHONG_PING, category=GuaCategory.CHENG_BAI,
        upper_gua="离", lower_gua="坎", yao_lines="101010",
        gua_ci="渡河未到彼岸边，前路茫茫多险滩。虽然辛苦未成功，坚持到底见青天。",
        xiang_ci="火在水上，未济；君子以慎辨物居方。",
        interpretation="此卦表示事情尚未完成，仍在进行中。虽然目前看不到明确的结果，但只要坚持努力，终将获得成功。",
        judgment="未济总纲：事未完成，持续努力。一鼓作气莫松懈，胜利就在转角处。天机暗示需坚持，不见成功不停步。",
        suitable_for=["继续努力", "坚持不懈", "调整策略", "寻求帮助"],
        avoid_when=["半途而废", "急躁冒进", "失去信心"],
        keywords=["未完成", "坚持", "努力", "希望", "过程", "耐心"],
        action_advice="保持耐心，继续努力，适时调整方法",
        timing_advice="当前仍在过程中，需要持续投入",
        risk_warning="成功尚未到来，需要防止中途放弃",
        created_at=datetime.now().isoformat()
    )
    
    # ==================== 更多卦象 ====================
    # 这里可以继续添加其余的卦象...
    
    # 第11卦：泰卦 ䷊
    gua_dict[11] = GuaXiang(
        number=11, name="泰", alias="天地交泰",
        level=GuaLevel.SHANG_SHANG, category=GuaCategory.JI_XIONG,
        upper_gua="坤", lower_gua="乾", yao_lines="000111",
        gua_ci="天地交合万物生，阴阳调和气象新。上下相通无阻碍，春风得意马蹄疾。",
        xiang_ci="天地交，泰；后以财成天地之道，辅相天地之宜，以左右民。",
        interpretation="泰卦象征天地交合，阴阳调和，是大吉大利的卦象。表示当前运势极佳，各方面都很顺利，是实现目标的好时机。",
        judgment="泰卦总纲：天地交泰，万事亨通。一帆风顺正当时，春风得意展宏图。天机显现皆吉利，不见阻碍不停步。",
        suitable_for=["重大决策", "投资理财", "事业发展", "人际交往", "婚姻感情"],
        avoid_when=["过度保守", "错失良机", "妄自菲薄"],
        keywords=["顺利", "和谐", "成功", "交流", "合作", "繁荣"],
        action_advice="把握良机，积极行动，扩大合作，实现目标",
        timing_advice="当前是最佳时期，宜大胆进取",
        risk_warning="顺境中需保持清醒，防止乐极生悲",
        created_at=datetime.now().isoformat()
    )
    
    # 第12卦：否卦 ䷋
    gua_dict[12] = GuaXiang(
        number=12, name="否", alias="天地不交",
        level=GuaLevel.XIA_JI, category=GuaCategory.JI_XIONG,
        upper_gua="乾", lower_gua="坤", yao_lines="111000",
        gua_ci="天地不交气不通，阴阳隔绝路难行。暂时困顿莫心急，否极泰来有转机。",
        xiang_ci="天地不交，否；君子以俭德辟难，不可荣以禄。",
        interpretation="否卦与泰卦相反，表示天地不交，阴阳隔绝，运势低迷。但否极泰来，困难是暂时的，需要耐心等待转机。",
        judgment="否卦总纲：天地不交，暂时困顿。一时阴霾遮日月，否极泰来有转机。天机暗示需忍耐，不见曙光不放弃。",
        suitable_for=["韬光养晦", "积蓄力量", "反思总结", "等待时机"],
        avoid_when=["强行突破", "冒险投资", "急躁冒进"],
        keywords=["困难", "阻碍", "等待", "忍耐", "转机", "反思"],
        action_advice="保持低调，积蓄力量，等待时机，不可强求",
        timing_advice="当前不宜大动作，需要耐心等待",
        risk_warning="困难期容易做出错误决策，需要冷静",
        created_at=datetime.now().isoformat()
    )
    
    # 继续添加更多重要卦象...
    # 为了演示，这里先添加这些核心卦象
    
    return gua_dict


def get_gua_categories():
    """获取卦象分类说明"""
    return {
        GuaCategory.QIAN_KUN: {
            "name": "乾坤类",
            "description": "天地之道，阴阳之理",
            "characteristics": ["基础", "根本", "原理"]
        },
        GuaCategory.SHUI_HUO: {
            "name": "水火类", 
            "description": "水火既济，阴阳调和",
            "characteristics": ["平衡", "调和", "互补"]
        },
        GuaCategory.FENG_LEI: {
            "name": "风雷类",
            "description": "风雷激荡，变化万千",
            "characteristics": ["变化", "动荡", "激发"]
        },
        GuaCategory.SHAN_ZE: {
            "name": "山泽类",
            "description": "山泽通气，高低相应",
            "characteristics": ["稳定", "通达", "相应"]
        },
        GuaCategory.JIN_TUI: {
            "name": "进退类",
            "description": "进退有度，张弛有道",
            "characteristics": ["策略", "节奏", "时机"]
        },
        GuaCategory.CHENG_BAI: {
            "name": "成败类",
            "description": "成败得失，功过是非",
            "characteristics": ["结果", "评判", "总结"]
        },
        GuaCategory.JI_XIONG: {
            "name": "吉凶类",
            "description": "吉凶祸福，运势变化",
            "characteristics": ["运势", "福祸", "变化"]
        },
        GuaCategory.BIAN_HUA: {
            "name": "变化类",
            "description": "变化无常，适应调整",
            "characteristics": ["变化", "适应", "灵活"]
        }
    }


def get_level_descriptions():
    """获取卦象等级说明"""
    return {
        GuaLevel.SHANG_SHANG: {
            "name": "上上签",
            "description": "大吉大利，万事如意",
            "probability": "15%",
            "advice": "把握机遇，积极进取"
        },
        GuaLevel.SHANG_JI: {
            "name": "上吉签",
            "description": "吉利顺遂，前景光明", 
            "probability": "25%",
            "advice": "顺势而为，稳步前进"
        },
        GuaLevel.ZHONG_PING: {
            "name": "中平签",
            "description": "平稳中庸，需要努力",
            "probability": "40%", 
            "advice": "踏实努力，循序渐进"
        },
        GuaLevel.XIA_JI: {
            "name": "下吉签",
            "description": "小有困难，需要谨慎",
            "probability": "15%",
            "advice": "谨慎行事，化解困难"
        },
        GuaLevel.XIA_XIA: {
            "name": "下下签",
            "description": "困难重重，需要忍耐",
            "probability": "5%",
            "advice": "韬光养晦，等待转机"
        }
    }