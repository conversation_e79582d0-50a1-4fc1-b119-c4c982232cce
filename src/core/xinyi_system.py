#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
太公心易系统 - 六十四卦智慧决策模块
基于字典结构的模块化设计

核心功能：
1. 六十四卦完整字典数据
2. 智能卦象匹配与推荐
3. 情境化解读与应用
4. 决策支持与风险评估

作者：太公心易BI系统
版本：v1.0 XinYi
"""

from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
from enum import Enum
import random
import json
from datetime import datetime
import hashlib


class GuaLevel(Enum):
    """卦象吉凶等级"""
    SHANG_SHANG = "上上签"  # 大吉
    SHANG_JI = "上吉签"    # 吉
    ZHONG_PING = "中平签"  # 平
    XIA_JI = "下吉签"      # 小凶
    XIA_XIA = "下下签"     # 大凶


class GuaCategory(Enum):
    """卦象分类"""
    QIAN_KUN = "乾坤"      # 天地类
    SHUI_HUO = "水火"      # 水火类
    FENG_LEI = "风雷"      # 风雷类
    SHAN_ZE = "山泽"       # 山泽类
    JIN_TUI = "进退"       # 进退类
    CHENG_BAI = "成败"     # 成败类
    JI_XIONG = "吉凶"      # 吉凶类
    BIAN_HUA = "变化"      # 变化类


@dataclass
class GuaXiang:
    """卦象数据结构"""
    # 基本信息
    number: int                    # 卦序号 (1-64)
    name: str                     # 卦名
    alias: str                    # 别名/描述
    level: GuaLevel               # 吉凶等级
    category: GuaCategory         # 分类
    
    # 卦象构成
    upper_gua: str               # 上卦
    lower_gua: str               # 下卦
    yao_lines: str               # 爻辞 (如: "111010")
    
    # 核心内容
    gua_ci: str                  # 卦辞
    xiang_ci: str                # 象辞
    interpretation: str          # 现代解读
    judgment: str                # 太公判词
    
    # 应用指导
    suitable_for: List[str]      # 适用场景
    avoid_when: List[str]        # 避免场景
    keywords: List[str]          # 关键词
    
    # 决策建议
    action_advice: str           # 行动建议
    timing_advice: str           # 时机建议
    risk_warning: str            # 风险提示
    
    # 元数据
    created_at: str = None
    updated_at: str = None


class XinYiSystem:
    """太公心易系统核心类"""
    
    def __init__(self):
        self.gua_dict = self._initialize_gua_dict()
        self.category_mapping = self._build_category_mapping()
        self.keyword_index = self._build_keyword_index()
        
    def _initialize_gua_dict(self) -> Dict[int, GuaXiang]:
        """初始化六十四卦字典"""
        gua_dict = {}
        
        # 第1卦：乾卦
        gua_dict[1] = GuaXiang(
            number=1,
            name="乾",
            alias="飞龙在天",
            level=GuaLevel.SHANG_SHANG,
            category=GuaCategory.QIAN_KUN,
            upper_gua="乾",
            lower_gua="乾",
            yao_lines="111111",
            gua_ci="飞龙乘云腾九霄，风云际会正当时。天命所归终有应，金榜题名洞房时。",
            xiang_ci="天行健，君子以自强不息。",
            interpretation="抽得此签预示着人生正处于巅峰时刻，事业、感情、财运皆为最佳状态。宜把握时机，顺势而为，必能成就大业。",
            judgment="乾卦总纲：龙德正中，天道酬勤。一飞冲天志如虹，九五至尊显神通。天机已现莫迟疑，不见时机不出征。",
            suitable_for=["创业", "投资", "求职", "考试", "婚姻", "重大决策"],
            avoid_when=["犹豫不决", "缺乏准备", "逆势而为"],
            keywords=["成功", "领导", "创新", "突破", "巅峰"],
            action_advice="积极进取，把握机遇，发挥领导才能",
            timing_advice="当前正是最佳时机，宜立即行动",
            risk_warning="成功在望，但需防止骄傲自满",
            created_at=datetime.now().isoformat()
        )
        
        # 第2卦：坤卦
        gua_dict[2] = GuaXiang(
            number=2,
            name="坤",
            alias="厚德载物",
            level=GuaLevel.SHANG_SHANG,
            category=GuaCategory.QIAN_KUN,
            upper_gua="坤",
            lower_gua="坤",
            yao_lines="000000",
            gua_ci="大地含章守本真，厚积薄发待春临。柔顺包容成大器，福泽绵延荫子孙。",
            xiang_ci="地势坤，君子以厚德载物。",
            interpretation="此签强调积累与包容。当前虽非高调之时，但正是夯实基础、积蓄力量的好机会。以柔克刚，以静制动，未来可期。",
            judgment="坤卦总纲：厚德载物，包容万象。一步一印稳如山，厚积薄发不争先。天机深藏待时发，不见根基不建园。",
            suitable_for=["积累", "学习", "团队合作", "长期投资", "培养关系"],
            avoid_when=["急功近利", "强行出头", "忽视基础"],
            keywords=["稳定", "包容", "积累", "支持", "基础"],
            action_advice="稳扎稳打，注重积累，发挥支持作用",
            timing_advice="当前宜蓄势待发，不宜急进",
            risk_warning="过于保守可能错失良机",
            created_at=datetime.now().isoformat()
        )
        
        # 第60卦：节卦（你的示例）
        gua_dict[60] = GuaXiang(
            number=60,
            name="节",
            alias="节制有度",
            level=GuaLevel.ZHONG_PING,
            category=GuaCategory.JIN_TUI,
            upper_gua="坎",
            lower_gua="兑",
            yao_lines="010110",
            gua_ci="节而有度，道法自然。一日一卦默如金，日满月盈忘繁星。天机泄净终有时，不见兔子不撒鹰。",
            xiang_ci="泽上有水，节；君子以制数度，议德行。",
            interpretation="抽得此卦，示警吾人，万事万物皆须遵循节制之道，不可逾越其分寸。卜算亦然，如同每日功课，贵在精而不在多。当过度的指引如日月般耀眼时，反而会遮蔽细微的真相。须知天机有其泄露的限度，不可频繁触碰；如同狩猎，不见兔子，不应轻率出击。智慧的运用在于适时、适度、内敛，方能长久。",
            judgment="节卦总纲：节而有度，道法自然。一日一卦默如金，日满月盈忘繁星。天机泄净终有时，不见兔子不撒鹰。",
            suitable_for=["制定规则", "控制风险", "节约资源", "自我约束"],
            avoid_when=["过度放纵", "频繁决策", "无节制消费"],
            keywords=["节制", "适度", "规律", "约束", "平衡"],
            action_advice="保持节制，适度而为，建立良好规律",
            timing_advice="当前需要控制节奏，不宜过度",
            risk_warning="过度节制可能限制发展机会",
            created_at=datetime.now().isoformat()
        )
        
        # TODO: 添加其余61卦的完整数据
        # 这里先添加几个重要的卦象作为示例
        
        return gua_dict
    
    def _build_category_mapping(self) -> Dict[GuaCategory, List[int]]:
        """构建分类映射"""
        mapping = {}
        for category in GuaCategory:
            mapping[category] = []
        
        for number, gua in self.gua_dict.items():
            mapping[gua.category].append(number)
        
        return mapping
    
    def _build_keyword_index(self) -> Dict[str, List[int]]:
        """构建关键词索引"""
        index = {}
        
        for number, gua in self.gua_dict.items():
            for keyword in gua.keywords:
                if keyword not in index:
                    index[keyword] = []
                index[keyword].append(number)
        
        return index
    
    def get_gua_by_number(self, number: int) -> Optional[GuaXiang]:
        """根据卦序号获取卦象"""
        return self.gua_dict.get(number)
    
    def get_gua_by_name(self, name: str) -> Optional[GuaXiang]:
        """根据卦名获取卦象"""
        for gua in self.gua_dict.values():
            if gua.name == name or gua.alias == name:
                return gua
        return None
    
    def search_by_keywords(self, keywords: List[str]) -> List[GuaXiang]:
        """根据关键词搜索卦象"""
        matching_numbers = set()
        
        for keyword in keywords:
            if keyword in self.keyword_index:
                matching_numbers.update(self.keyword_index[keyword])
        
        return [self.gua_dict[num] for num in matching_numbers if num in self.gua_dict]
    
    def get_by_category(self, category: GuaCategory) -> List[GuaXiang]:
        """根据分类获取卦象"""
        numbers = self.category_mapping.get(category, [])
        return [self.gua_dict[num] for num in numbers if num in self.gua_dict]
    
    def random_gua(self) -> GuaXiang:
        """随机抽取一卦"""
        number = random.choice(list(self.gua_dict.keys()))
        return self.gua_dict[number]
    
    def divine_by_question(self, question: str, context: Dict[str, Any] = None) -> Tuple[GuaXiang, str]:
        """根据问题进行占卜"""
        # 基于问题内容和上下文智能匹配卦象
        question_lower = question.lower()
        context = context or {}
        
        # 关键词匹配
        keywords = []
        keyword_mapping = {
            "投资": ["成功", "风险", "财富"],
            "创业": ["创新", "突破", "领导"],
            "工作": ["稳定", "发展", "合作"],
            "感情": ["和谐", "包容", "变化"],
            "学习": ["积累", "进步", "坚持"],
            "决策": ["智慧", "时机", "平衡"]
        }
        
        for key, kws in keyword_mapping.items():
            if key in question_lower:
                keywords.extend(kws)
        
        # 如果有匹配的关键词，优先选择
        if keywords:
            matching_guas = self.search_by_keywords(keywords)
            if matching_guas:
                selected_gua = random.choice(matching_guas)
            else:
                selected_gua = self.random_gua()
        else:
            selected_gua = self.random_gua()
        
        # 生成个性化解读
        personalized_reading = self._generate_personalized_reading(
            selected_gua, question, context
        )
        
        return selected_gua, personalized_reading
    
    def _generate_personalized_reading(self, gua: GuaXiang, question: str, context: Dict[str, Any]) -> str:
        """生成个性化解读"""
        reading = f"针对您的问题「{question}」，太公心易为您解读{gua.name}卦：\n\n"
        reading += f"【卦象】{gua.alias} ({gua.level.value})\n"
        reading += f"【卦辞】{gua.gua_ci}\n\n"
        reading += f"【太公判词】{gua.judgment}\n\n"
        reading += f"【现代解读】{gua.interpretation}\n\n"
        reading += f"【行动建议】{gua.action_advice}\n"
        reading += f"【时机把握】{gua.timing_advice}\n"
        reading += f"【风险提示】{gua.risk_warning}\n"
        
        return reading
    
    def get_daily_gua(self, date: str = None) -> GuaXiang:
        """获取每日一卦"""
        if date is None:
            date = datetime.now().strftime("%Y-%m-%d")
        
        # 基于日期生成固定的卦象
        hash_object = hashlib.md5(date.encode())
        hash_hex = hash_object.hexdigest()
        gua_number = int(hash_hex[:8], 16) % len(self.gua_dict) + 1
        
        return self.gua_dict.get(gua_number, self.random_gua())
    
    def export_gua_dict(self, format: str = "json") -> str:
        """导出卦象字典"""
        if format.lower() == "json":
            # 转换为可序列化的格式
            export_data = {}
            for number, gua in self.gua_dict.items():
                gua_data = asdict(gua)
                # 转换枚举为字符串
                gua_data['level'] = gua.level.value
                gua_data['category'] = gua.category.value
                export_data[number] = gua_data
            
            return json.dumps(export_data, ensure_ascii=False, indent=2)
        
        # TODO: 支持其他格式 (yaml, xml等)
        return ""
    
    def import_gua_dict(self, data: str, format: str = "json"):
        """导入卦象字典"""
        if format.lower() == "json":
            import_data = json.loads(data)
            
            for number_str, gua_data in import_data.items():
                number = int(number_str)
                
                # 转换字符串回枚举
                gua_data['level'] = GuaLevel(gua_data['level'])
                gua_data['category'] = GuaCategory(gua_data['category'])
                
                # 创建GuaXiang对象
                gua = GuaXiang(**gua_data)
                self.gua_dict[number] = gua
            
            # 重建索引
            self.category_mapping = self._build_category_mapping()
            self.keyword_index = self._build_keyword_index()
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取系统统计信息"""
        stats = {
            "total_guas": len(self.gua_dict),
            "by_level": {},
            "by_category": {},
            "total_keywords": len(self.keyword_index)
        }
        
        # 按等级统计
        for level in GuaLevel:
            count = sum(1 for gua in self.gua_dict.values() if gua.level == level)
            stats["by_level"][level.value] = count
        
        # 按分类统计
        for category in GuaCategory:
            count = len(self.category_mapping.get(category, []))
            stats["by_category"][category.value] = count
        
        return stats


# 全局实例
xinyi_system = XinYiSystem()


def main():
    """演示函数"""
    print("🎯 太公心易系统演示")
    print("=" * 50)
    
    # 系统统计
    stats = xinyi_system.get_statistics()
    print(f"📊 系统统计: {stats}")
    
    # 随机抽卦
    print(f"\n🎲 随机抽卦:")
    random_gua = xinyi_system.random_gua()
    print(f"卦象: {random_gua.name}卦 - {random_gua.alias}")
    print(f"等级: {random_gua.level.value}")
    print(f"卦辞: {random_gua.gua_ci}")
    
    # 每日一卦
    print(f"\n📅 今日一卦:")
    daily_gua = xinyi_system.get_daily_gua()
    print(f"卦象: {daily_gua.name}卦 - {daily_gua.alias}")
    print(f"判词: {daily_gua.judgment}")
    
    # 问题占卜
    print(f"\n🔮 问题占卜:")
    question = "我应该投资股市吗？"
    gua, reading = xinyi_system.divine_by_question(question)
    print(reading)


if __name__ == "__main__":
    main()