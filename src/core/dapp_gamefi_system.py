# -*- coding: utf-8 -*-
"""
🐒 炼妖壶DApp GameFi系统 - 81难第一章节
真实时间线上的投资者修仙之路

核心理念：
- 金融系统本身就是game，GameFi就是同义反复
- 男人的勋章是伤疤 - 每个亏损都是真实的成长印记
- 同一时间线，同一fork，持续share真实体验
- 醉八仙系统 - 八种投资者的"醉态"映射

作者：太公心易BI系统
版本：v3.0 DApp Edition
"""

import asyncio
import json
import time
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
from enum import Enum
import hashlib
import uuid

class ScarType(Enum):
    """伤疤类型 - 男人的勋章"""
    FIRST_BLOOD = "初见血光"      # 第一次亏损
    MAJOR_LOSS = "刻骨之痛"       # 重大亏损 >20%
    BLOWN_ACCOUNT = "倾家荡产"    # 爆仓
    FOMO_TRAP = "追高被套"        # FOMO陷阱
    PANIC_SELL = "恐慌割肉"       # 恐慌性抛售
    LEVERAGE_BURN = "杠杆之殇"    # 杠杆爆仓
    TIMING_MISS = "错失良机"      # 踏空
    GREED_PUNISHMENT = "贪婪之罚" # 贪婪导致的损失

@dataclass
class TradingScar:
    """交易伤疤 - 真实的成长印记"""
    scar_id: str
    scar_type: ScarType
    timestamp: datetime
    loss_amount: float
    loss_percentage: float
    market_context: str
    emotional_state: str
    lesson_learned: str
    pain_level: int  # 1-10的痛苦程度
    wisdom_gained: int  # 获得的智慧点数
    
    # 区块链相关
    block_hash: str  # 记录在"区块链"上的哈希
    witnesses: List[str]  # 见证者（其他用户）
    verified: bool = False

@dataclass
class DrunkImmortal:
    """醉八仙 - 八种投资者醉态"""
    name: str
    drunk_style: str  # 醉态风格
    investment_bias: str  # 投资偏见
    typical_mistakes: List[str]  # 典型错误
    sobering_moments: List[str]  # 清醒时刻
    wisdom_quotes: List[str]  # 智慧语录

class EightDrunkImmortals:
    """醉八仙系统"""
    
    IMMORTALS = {
        "吕洞宾": DrunkImmortal(
            name="吕洞宾",
            drunk_style="理性醉",
            investment_bias="过度相信基本面分析",
            typical_mistakes=["忽视市场情绪", "持股过久", "错过技术面信号"],
            sobering_moments=["市场非理性暴跌时", "基本面良好但股价下跌"],
            wisdom_quotes=["市场可以保持非理性的时间，比你保持理性的时间更长"]
        ),
        "何仙姑": DrunkImmortal(
            name="何仙姑",
            drunk_style="保守醉",
            investment_bias="过度风险厌恶",
            typical_mistakes=["错失牛市机会", "现金比例过高", "过度分散"],
            sobering_moments=["通胀侵蚀现金价值时", "看到别人赚钱自己踏空"],
            wisdom_quotes=["不冒险本身就是最大的风险"]
        ),
        "张果老": DrunkImmortal(
            name="张果老",
            drunk_style="经验醉",
            investment_bias="过度依赖历史经验",
            typical_mistakes=["刻舟求剑", "忽视新趋势", "固守旧策略"],
            sobering_moments=["市场结构性变化时", "新兴行业崛起"],
            wisdom_quotes=["这次不一样，但人性永远一样"]
        ),
        "韩湘子": DrunkImmortal(
            name="韩湘子",
            drunk_style="技术醉",
            investment_bias="过度相信技术分析",
            typical_mistakes=["忽视基本面", "过度交易", "被假突破欺骗"],
            sobering_moments=["技术指标失效时", "基本面驱动的行情"],
            wisdom_quotes=["技术分析告诉你怎么做，基本面告诉你为什么做"]
        ),
        "汉钟离": DrunkImmortal(
            name="汉钟离",
            drunk_style="价值醉",
            investment_bias="过度相信价值投资",
            typical_mistakes=["价值陷阱", "忽视成长性", "买入时机不当"],
            sobering_moments=["价值股长期跑输时", "成长股暴涨"],
            wisdom_quotes=["价格是你付出的，价值是你得到的"]
        ),
        "蓝采和": DrunkImmortal(
            name="蓝采和",
            drunk_style="趋势醉",
            investment_bias="过度追逐趋势",
            typical_mistakes=["追高杀跌", "频繁换股", "忽视估值"],
            sobering_moments=["趋势反转时", "高位接盘"],
            wisdom_quotes=["趋势是你的朋友，直到它不是"]
        ),
        "曹国舅": DrunkImmortal(
            name="曹国舅",
            drunk_style="消息醉",
            investment_bias="过度相信内幕消息",
            typical_mistakes=["追逐小道消息", "忽视公开信息", "被割韭菜"],
            sobering_moments=["消息被证伪时", "公开信息更准确"],
            wisdom_quotes=["当消息传到你这里时，通常已经太晚了"]
        ),
        "铁拐李": DrunkImmortal(
            name="铁拐李",
            drunk_style="逆向醉",
            investment_bias="过度逆向思维",
            typical_mistakes=["过早抄底", "错判拐点", "逆势而为"],
            sobering_moments=["趋势持续超预期时", "抄底抄在半山腰"],
            wisdom_quotes=["市场保持疯狂的时间，可能比你想象的更长"]
        )
    }

class DAppGameFiSystem:
    """DApp GameFi系统 - 81难第一章节"""
    
    def __init__(self, user_id: str):
        self.user_id = user_id
        self.session_id = str(uuid.uuid4())
        self.start_time = datetime.now()
        
        # 用户状态
        self.current_level = 1
        self.total_scars = 0
        self.wisdom_points = 0
        self.pain_endured = 0
        
        # 伤疤收集
        self.scars: List[TradingScar] = []
        self.scar_collection = {scar_type: 0 for scar_type in ScarType}
        
        # 醉八仙状态
        self.drunk_immortals = EightDrunkImmortals.IMMORTALS
        self.current_drunk_state = None
        
        # DApp状态
        self.is_online = True
        self.last_heartbeat = datetime.now()
        self.shared_experiences = []
        
    def create_trading_scar(self, 
                          scar_type: ScarType,
                          loss_amount: float,
                          loss_percentage: float,
                          market_context: str,
                          emotional_state: str) -> TradingScar:
        """创建交易伤疤 - 真实的成长印记"""
        
        # 计算痛苦程度 (1-10)
        pain_level = min(10, max(1, int(abs(loss_percentage) * 20)))
        
        # 计算智慧获得 (痛苦越深，智慧越多)
        wisdom_gained = pain_level * 10 + (50 if loss_percentage > 0.2 else 0)
        
        # 生成区块哈希 (模拟区块链记录)
        block_data = f"{self.user_id}_{scar_type.value}_{loss_amount}_{datetime.now().isoformat()}"
        block_hash = hashlib.sha256(block_data.encode()).hexdigest()[:16]
        
        # 提取教训
        lesson_learned = self._extract_lesson(scar_type, loss_percentage, emotional_state)
        
        scar = TradingScar(
            scar_id=str(uuid.uuid4()),
            scar_type=scar_type,
            timestamp=datetime.now(),
            loss_amount=loss_amount,
            loss_percentage=loss_percentage,
            market_context=market_context,
            emotional_state=emotional_state,
            lesson_learned=lesson_learned,
            pain_level=pain_level,
            wisdom_gained=wisdom_gained,
            block_hash=block_hash,
            witnesses=[],
            verified=False
        )
        
        # 添加到收集
        self.scars.append(scar)
        self.scar_collection[scar_type] += 1
        self.total_scars += 1
        self.wisdom_points += wisdom_gained
        self.pain_endured += pain_level
        
        return scar
    
    def _extract_lesson(self, scar_type: ScarType, loss_percentage: float, emotional_state: str) -> str:
        """从伤疤中提取教训"""
        lessons = {
            ScarType.FIRST_BLOOD: "市场不会因为你是新手而手下留情",
            ScarType.MAJOR_LOSS: "永远不要把鸡蛋放在一个篮子里",
            ScarType.BLOWN_ACCOUNT: "杠杆是双刃剑，能让你飞得更高，也能让你摔得更惨",
            ScarType.FOMO_TRAP: "当所有人都在谈论某个投资时，通常已经太晚了",
            ScarType.PANIC_SELL: "恐慌是投资者最大的敌人",
            ScarType.LEVERAGE_BURN: "杠杆不是让你赚更多钱，而是让你亏得更快",
            ScarType.TIMING_MISS: "时机比选择更重要",
            ScarType.GREED_PUNISHMENT: "贪婪让人失去理智"
        }
        
        base_lesson = lessons.get(scar_type, "每一次失败都是成长的机会")
        
        if loss_percentage > 0.5:
            return f"{base_lesson} - 这次的代价尤其惨重，刻骨铭心"
        elif emotional_state in ["恐慌", "绝望", "愤怒"]:
            return f"{base_lesson} - 情绪管理是投资成功的关键"
        else:
            return base_lesson
    
    def identify_drunk_state(self, recent_trades: List[Dict]) -> Optional[str]:
        """识别当前的醉仙状态"""
        if not recent_trades:
            return None
            
        # 分析最近交易模式
        patterns = self._analyze_trading_patterns(recent_trades)
        
        # 匹配醉仙类型
        for immortal_name, immortal in self.drunk_immortals.items():
            if self._matches_drunk_pattern(patterns, immortal):
                self.current_drunk_state = immortal_name
                return immortal_name
        
        return None
    
    def _analyze_trading_patterns(self, trades: List[Dict]) -> Dict[str, Any]:
        """分析交易模式"""
        if not trades:
            return {}
            
        total_trades = len(trades)
        profitable_trades = sum(1 for t in trades if t.get('profit_loss', 0) > 0)
        
        return {
            "win_rate": profitable_trades / total_trades if total_trades > 0 else 0,
            "avg_holding_period": sum(t.get('holding_days', 0) for t in trades) / total_trades,
            "risk_level": sum(t.get('position_size', 0) for t in trades) / total_trades,
            "trading_frequency": total_trades,
            "emotion_volatility": self._calculate_emotion_volatility(trades)
        }
    
    def _matches_drunk_pattern(self, patterns: Dict[str, Any], immortal: DrunkImmortal) -> bool:
        """匹配醉仙模式"""
        # 简化的模式匹配逻辑
        if immortal.name == "吕洞宾" and patterns.get("avg_holding_period", 0) > 30:
            return True
        elif immortal.name == "何仙姑" and patterns.get("risk_level", 0) < 0.3:
            return True
        elif immortal.name == "蓝采和" and patterns.get("trading_frequency", 0) > 20:
            return True
        # ... 其他匹配逻辑
        
        return False
    
    def _calculate_emotion_volatility(self, trades: List[Dict]) -> float:
        """计算情绪波动性"""
        emotions = [t.get('emotional_state', 'neutral') for t in trades]
        negative_emotions = sum(1 for e in emotions if e in ['恐慌', '绝望', '愤怒', '贪婪'])
        return negative_emotions / len(emotions) if emotions else 0
    
    def share_experience_to_timeline(self, scar: TradingScar) -> Dict[str, Any]:
        """分享经历到时间线 - 同一fork上的真实share"""
        experience = {
            "user_id": self.user_id,
            "session_id": self.session_id,
            "timestamp": datetime.now().isoformat(),
            "scar_type": scar.scar_type.value,
            "pain_level": scar.pain_level,
            "lesson_learned": scar.lesson_learned,
            "block_hash": scar.block_hash,
            "wisdom_gained": scar.wisdom_gained,
            "market_context": scar.market_context
        }
        
        # 添加到共享时间线
        self.shared_experiences.append(experience)
        
        return experience
    
    def witness_others_pain(self, other_experiences: List[Dict]) -> List[str]:
        """见证他人的痛苦 - 获得间接智慧"""
        witnessed_wisdom = []
        
        for exp in other_experiences:
            if exp["user_id"] != self.user_id:  # 不是自己的经历
                # 从他人痛苦中学习
                indirect_wisdom = f"看到{exp['user_id'][:8]}...在{exp['scar_type']}中的教训：{exp['lesson_learned']}"
                witnessed_wisdom.append(indirect_wisdom)
                
                # 获得少量智慧点数
                self.wisdom_points += exp["pain_level"] // 2
        
        return witnessed_wisdom
    
    def get_gamefi_status(self) -> Dict[str, Any]:
        """获取GameFi状态"""
        return {
            "user_id": self.user_id,
            "session_id": self.session_id,
            "online_time": (datetime.now() - self.start_time).total_seconds(),
            
            # 修仙进度
            "current_level": self.current_level,
            "wisdom_points": self.wisdom_points,
            "total_scars": self.total_scars,
            "pain_endured": self.pain_endured,
            
            # 伤疤收集
            "scar_collection": {scar_type.value: count for scar_type, count in self.scar_collection.items()},
            "rarest_scar": self._get_rarest_scar(),
            
            # 醉仙状态
            "current_drunk_state": self.current_drunk_state,
            "drunk_immortal_info": self.drunk_immortals.get(self.current_drunk_state) if self.current_drunk_state else None,
            
            # DApp状态
            "is_online": self.is_online,
            "shared_experiences_count": len(self.shared_experiences),
            "last_heartbeat": self.last_heartbeat.isoformat()
        }
    
    def _get_rarest_scar(self) -> Optional[str]:
        """获取最稀有的伤疤"""
        if not self.scars:
            return None
            
        # 按痛苦程度排序，最痛的就是最稀有的
        sorted_scars = sorted(self.scars, key=lambda x: x.pain_level, reverse=True)
        return sorted_scars[0].scar_type.value if sorted_scars else None

# 使用示例
async def demo_dapp_gamefi():
    """演示DApp GameFi系统"""
    print("🚀 启动炼妖壶DApp GameFi系统")
    print("=" * 50)
    
    # 创建用户
    gamefi = DAppGameFiSystem("user_monkey_king")
    
    # 模拟第一次亏损 - 初见血光
    print("\n💀 第一次交易失败...")
    first_scar = gamefi.create_trading_scar(
        scar_type=ScarType.FIRST_BLOOD,
        loss_amount=1000,
        loss_percentage=0.1,
        market_context="新手买入热门股票，遇到市场调整",
        emotional_state="震惊"
    )
    
    print(f"获得伤疤: {first_scar.scar_type.value}")
    print(f"痛苦程度: {first_scar.pain_level}/10")
    print(f"智慧获得: +{first_scar.wisdom_gained}")
    print(f"教训: {first_scar.lesson_learned}")
    
    # 分享到时间线
    shared_exp = gamefi.share_experience_to_timeline(first_scar)
    print(f"\n📢 经历已分享到时间线，区块哈希: {shared_exp['block_hash']}")
    
    # 模拟重大亏损 - 刻骨之痛
    print("\n💔 遭遇重大亏损...")
    major_scar = gamefi.create_trading_scar(
        scar_type=ScarType.MAJOR_LOSS,
        loss_amount=10000,
        loss_percentage=0.3,
        market_context="杠杆交易遇到黑天鹅事件",
        emotional_state="绝望"
    )
    
    print(f"获得伤疤: {major_scar.scar_type.value}")
    print(f"痛苦程度: {major_scar.pain_level}/10")
    print(f"智慧获得: +{major_scar.wisdom_gained}")
    
    # 显示最终状态
    print("\n" + "=" * 50)
    print("🎯 当前GameFi状态:")
    status = gamefi.get_gamefi_status()
    print(f"智慧点数: {status['wisdom_points']}")
    print(f"总伤疤数: {status['total_scars']}")
    print(f"承受痛苦: {status['pain_endured']}")
    print(f"最稀有伤疤: {status['rarest_scar']}")
    
    print("\n🏆 伤疤收集:")
    for scar_type, count in status['scar_collection'].items():
        if count > 0:
            print(f"  {scar_type}: {count}个")

if __name__ == "__main__":
    asyncio.run(demo_dapp_gamefi())