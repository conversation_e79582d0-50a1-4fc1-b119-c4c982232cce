# -*- coding: utf-8 -*-
"""
🐒 散户修仙之路 - 猴王之旅
基于《临江仙·山下吟》的GameFi设计

水帘洞内见生死，舢板入海求道。得偿所望傲气扬，斜月三星洞，黄粱梦一场。
诏安饮马银河畔，仙桃玉液入嗓。金銮踏破终被擒，八卦炉中炼，五行山下吟。

春夏秋冬，十二等级，从傻逼到牛逼的完整修仙路径
"""

from enum import Enum
from dataclasses import dataclass
from typing import Dict, List, Optional
import json

class Season(Enum):
    SPRING = "春·求道"  # 花果山，见生死，求大道
    SUMMER = "夏·得道"  # 得所望，傲气扬，回老家  
    AUTUMN = "秋·失道"  # 受招安，喝玉液，砸金銮
    WINTER = "冬·悟道"  # 终被擒，八卦炉，五行山

@dataclass
class MonkeyLevel:
    """猴王等级"""
    level: int
    name: str
    poem: str
    description: str
    season: Season
    unlock_condition: str
    rewards: List[str]

class MonkeyKingJourney:
    """猴王修仙之路"""
    
    # 十二等级定义
    LEVELS = [
        # 春·求道篇 (1-3级)
        MonkeyLevel(1, "石猴出世", "混沌初开见天地", "初入股市，懵懂无知", 
                   Season.SPRING, "开户入金", ["新手保护符"]),
        MonkeyLevel(2, "水帘洞主", "洞中称王聚群猴", "发现投资机会，小有收获", 
                   Season.SPRING, "首次盈利", ["群猴追随"]),
        MonkeyLevel(3, "见生死", "生老病死心惊惧", "经历重大亏损，开始思考", 
                   Season.SPRING, "亏损>20%", ["生死智慧"]),
        
        # 夏·得道篇 (4-6级)  
        MonkeyLevel(4, "舢板求道", "漂洋过海寻仙师", "主动学习，寻找方法", 
                   Season.SUMMER, "学习投资理论", ["求道之心"]),
        MonkeyLevel(5, "斜月三星洞", "拜师学艺得真传", "找到投资体系", 
                   Season.SUMMER, "建立交易系统", ["七十二变"]),
        MonkeyLevel(6, "得偿所望", "筋斗云上任逍遥", "掌握技能，连续盈利", 
                   Season.SUMMER, "连续盈利3个月", ["如意金箍棒"]),
        
        # 秋·失道篇 (7-9级)
        MonkeyLevel(7, "傲气回山", "衣锦还乡显威风", "小有成就，开始自满", 
                   Season.AUTUMN, "年化收益>50%", ["齐天大圣"]),
        MonkeyLevel(8, "受招安", "天庭招安弼马温", "被市场诱惑，追逐热点", 
                   Season.AUTUMN, "偏离策略追热点", ["弼马温官印"]),
        MonkeyLevel(9, "蟠桃盛宴", "仙桃美酒醉心田", "享受牛市，忘记风险", 
                   Season.AUTUMN, "满仓操作", ["蟠桃仙果"]),
        
        # 冬·悟道篇 (10-12级)
        MonkeyLevel(10, "大闹天宫", "金箍棒打破南天门", "孤注一掷，对抗市场", 
                    Season.WINTER, "全仓做空/做多", ["大闹天宫"]),
        MonkeyLevel(11, "八卦炉炼", "太上老君炉中炼", "经历极端考验，痛苦蜕变", 
                    Season.WINTER, "回撤>70%", ["火眼金睛"]),
        MonkeyLevel(12, "五行山下", "压在山下五百年", "彻底觉悟，重新开始", 
                    Season.WINTER, "重新开始交易", ["斗战胜佛"])
    ]
    
    def __init__(self, user_id: str):
        self.user_id = user_id
        self.current_level = 1
        self.experience = 0
        self.achievements = []
        self.trading_stats = {
            "total_trades": 0,
            "win_rate": 0.0,
            "max_profit": 0.0,
            "max_loss": 0.0,
            "consecutive_wins": 0,
            "consecutive_losses": 0
        }
    
    def get_current_level_info(self) -> MonkeyLevel:
        """获取当前等级信息"""
        return self.LEVELS[self.current_level - 1]
    
    def check_level_up(self, trading_event: Dict) -> Optional[Dict]:
        """检查是否可以升级"""
        current = self.get_current_level_info()
        
        # 检查升级条件
        if self._check_unlock_condition(current.unlock_condition, trading_event):
            if self.current_level < 12:
                self.current_level += 1
                new_level = self.get_current_level_info()
                return {
                    "level_up": True,
                    "old_level": current,
                    "new_level": new_level,
                    "poem": new_level.poem,
                    "rewards": new_level.rewards
                }
        return None
    
    def _check_unlock_condition(self, condition: str, event: Dict) -> bool:
        """检查解锁条件"""
        # 这里可以根据实际交易数据判断
        # 简化版本，实际需要连接真实交易数据
        return True  # 暂时返回True用于演示
    
    def get_journey_progress(self) -> Dict:
        """获取修仙进度"""
        current = self.get_current_level_info()
        
        return {
            "user_id": self.user_id,
            "current_level": self.current_level,
            "level_name": current.name,
            "season": current.season.value,
            "poem": current.poem,
            "description": current.description,
            "progress_percent": (self.current_level / 12) * 100,
            "next_level": self.LEVELS[self.current_level] if self.current_level < 12 else None,
            "trading_stats": self.trading_stats,
            "achievements": self.achievements
        }
    
    def get_season_summary(self) -> Dict:
        """获取四季总结"""
        return {
            "春·求道": "花果山称王 → 水帘洞修行 → 见生死觉悟",
            "夏·得道": "舢板求仙师 → 三星洞学艺 → 得偿所望归",  
            "秋·失道": "傲气回花果 → 天庭受招安 → 蟠桃会醉心",
            "冬·悟道": "大闹破天宫 → 八卦炉重炼 → 五行山悟道"
        }

# 简化版UI展示
def create_journey_ui():
    """创建修仙之路UI"""
    return """
    🐒 散户修仙之路 - 猴王十二境界
    
    ═══════════════════════════════════════
    
    🌸 春·求道篇 (觉醒期)
    Lv.1 石猴出世 → Lv.2 水帘洞主 → Lv.3 见生死
    
    ☀️ 夏·得道篇 (学习期)  
    Lv.4 舢板求道 → Lv.5 斜月三星洞 → Lv.6 得偿所望
    
    🍂 秋·失道篇 (膨胀期)
    Lv.7 傲气回山 → Lv.8 受招安 → Lv.9 蟠桃盛宴
    
    ❄️ 冬·悟道篇 (觉悟期)
    Lv.10 大闹天宫 → Lv.11 八卦炉炼 → Lv.12 五行山下
    
    ═══════════════════════════════════════
    
    临江仙·山下吟
    
    水帘洞内见生死，舢板入海求道。
    得偿所望傲气扬，斜月三星洞，黄粱梦一场。
    
    诏安饮马银河畔，仙桃玉液入嗓。  
    金銮踏破终被擒，八卦炉中炼，五行山下吟。
    """

if __name__ == "__main__":
    # 演示
    journey = MonkeyKingJourney("user_123")
    print(create_journey_ui())
    print("\n当前进度:")
    progress = journey.get_journey_progress()
    print(f"等级: Lv.{progress['current_level']} {progress['level_name']}")
    print(f"季节: {progress['season']}")
    print(f"诗句: {progress['poem']}")