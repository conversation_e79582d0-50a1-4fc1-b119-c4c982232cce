#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
灵宝道君田野调查验证系统
在八仙论道完毕后，通过OpenManus进行实地验证
"""

import asyncio
import json
import logging
from datetime import datetime
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from enum import Enum
import aiohttp
from pydantic import BaseModel
from .openhands_integration import LingbaoOpenHandsVerifier

logger = logging.getLogger("LingbaoFieldVerification")

class VerificationStatus(Enum):
    PENDING = "pending"
    IN_PROGRESS = "in_progress" 
    COMPLETED = "completed"
    FAILED = "failed"
    REJECTED = "rejected"

class FieldTaskType(Enum):
    WEB_SCRAPING = "web_scraping"
    DATA_VALIDATION = "data_validation"
    MARKET_MONITORING = "market_monitoring"
    NEWS_VERIFICATION = "news_verification"
    SOCIAL_SENTIMENT = "social_sentiment"

@dataclass
class BaxianDebateResult:
    """八仙论道结果"""
    debate_id: str
    topic: str
    participants: List[str]  # 参与的八仙
    conclusions: Dict[str, Any]  # 辩论结论
    confidence_score: float
    timestamp: datetime
    key_claims: List[str]  # 需要验证的关键论断

@dataclass
class FieldTask:
    """田野调查任务"""
    task_id: str
    task_type: FieldTaskType
    target_url: str
    verification_points: List[str]
    expected_data: Dict[str, Any]
    timeout: int = 300
    retry_count: int = 3

class OpenManusClient:
    """OpenManus客户端接口"""
    
    def __init__(self, base_url: str, api_key: str):
        self.base_url = base_url
        self.api_key = api_key
        self.session = None
    
    async def __aenter__(self):
        self.session = aiohttp.ClientSession(
            headers={"Authorization": f"Bearer {self.api_key}"}
        )
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    async def create_scraping_task(self, task: FieldTask) -> str:
        """创建爬取任务"""
        payload = {
            "task_type": task.task_type.value,
            "target_url": task.target_url,
            "verification_points": task.verification_points,
            "timeout": task.timeout,
            "playwright_config": {
                "headless": True,
                "wait_for_selector": "body",
                "screenshot": True
            }
        }
        
        async with self.session.post(
            f"{self.base_url}/api/tasks/create",
            json=payload
        ) as response:
            result = await response.json()
            return result["task_id"]
    
    async def get_task_status(self, task_id: str) -> Dict[str, Any]:
        """获取任务状态"""
        async with self.session.get(
            f"{self.base_url}/api/tasks/{task_id}/status"
        ) as response:
            return await response.json()
    
    async def get_task_result(self, task_id: str) -> Dict[str, Any]:
        """获取任务结果"""
        async with self.session.get(
            f"{self.base_url}/api/tasks/{task_id}/result"
        ) as response:
            return await response.json()

class LingbaoFieldVerifier:
    """灵宝道君田野调查验证器"""
    
    def __init__(self, openmanus_url: str, api_key: str, openhands_api_key: str = None):
        self.openmanus_url = openmanus_url
        self.api_key = api_key
        self.openhands_api_key = openhands_api_key
        self.verification_history = []

        # 初始化OpenHands验证器（如果提供了API key）
        self.openhands_verifier = None
        if openhands_api_key:
            self.openhands_verifier = LingbaoOpenHandsVerifier(openhands_api_key)
    
    async def verify_debate_result(self, debate_result: BaxianDebateResult) -> Dict[str, Any]:
        """验证八仙论道结果"""
        logger.info(f"开始验证辩论结果: {debate_result.debate_id}")
        
        # 1. 分析论断，生成验证任务
        field_tasks = await self._generate_field_tasks(debate_result)
        
        # 2. 执行田野调查
        verification_results = []

        # 2a. 传统OpenManus验证
        async with OpenManusClient(self.openmanus_url, self.api_key) as client:
            for task in field_tasks:
                result = await self._execute_field_task(client, task)
                verification_results.append(result)

        # 2b. OpenHands Web验证（如果可用）
        openhands_results = None
        if self.openhands_verifier:
            logger.info("🌐 启动OpenHands Web验证...")
            try:
                # 转换辩论结果格式以适配OpenHands
                openhands_input = {
                    "debate_id": debate_result.debate_id,
                    "topic": debate_result.topic,
                    "conclusions": debate_result.conclusions,
                    "key_claims": debate_result.key_claims
                }
                openhands_results = await self.openhands_verifier.verify_debate_conclusions(openhands_input)
                logger.info("✅ OpenHands Web验证完成")
            except Exception as e:
                logger.error(f"❌ OpenHands验证失败: {e}")

        # 3. 综合分析验证结果
        final_report = await self._analyze_verification_results(
            debate_result, verification_results, openhands_results
        )
        
        # 4. 生成给元始天尊的报告
        tianzun_report = await self._generate_tianzun_report(
            debate_result, final_report
        )
        
        return tianzun_report
    
    async def _generate_field_tasks(self, debate_result: BaxianDebateResult) -> List[FieldTask]:
        """根据辩论结果生成田野调查任务"""
        tasks = []
        
        for i, claim in enumerate(debate_result.key_claims):
            # 根据论断类型生成不同的验证任务
            if "股价" in claim or "价格" in claim:
                tasks.append(FieldTask(
                    task_id=f"{debate_result.debate_id}_price_{i}",
                    task_type=FieldTaskType.MARKET_MONITORING,
                    target_url="https://finance.yahoo.com",
                    verification_points=[claim],
                    expected_data={"price_data": True}
                ))
            
            elif "新闻" in claim or "消息" in claim:
                tasks.append(FieldTask(
                    task_id=f"{debate_result.debate_id}_news_{i}",
                    task_type=FieldTaskType.NEWS_VERIFICATION,
                    target_url="https://www.reuters.com",
                    verification_points=[claim],
                    expected_data={"news_articles": True}
                ))
            
            elif "情绪" in claim or "sentiment" in claim.lower():
                tasks.append(FieldTask(
                    task_id=f"{debate_result.debate_id}_sentiment_{i}",
                    task_type=FieldTaskType.SOCIAL_SENTIMENT,
                    target_url="https://twitter.com",
                    verification_points=[claim],
                    expected_data={"sentiment_data": True}
                ))
        
        return tasks
    
    async def _execute_field_task(self, client: OpenManusClient, task: FieldTask) -> Dict[str, Any]:
        """执行单个田野调查任务"""
        logger.info(f"执行田野调查任务: {task.task_id}")
        
        try:
            # 创建任务
            remote_task_id = await client.create_scraping_task(task)
            
            # 等待任务完成
            max_wait_time = task.timeout
            wait_interval = 10
            elapsed_time = 0
            
            while elapsed_time < max_wait_time:
                status = await client.get_task_status(remote_task_id)
                
                if status["status"] == "completed":
                    result = await client.get_task_result(remote_task_id)
                    return {
                        "task_id": task.task_id,
                        "status": "success",
                        "data": result,
                        "verification_points": task.verification_points
                    }
                elif status["status"] == "failed":
                    return {
                        "task_id": task.task_id,
                        "status": "failed",
                        "error": status.get("error", "Unknown error"),
                        "verification_points": task.verification_points
                    }
                
                await asyncio.sleep(wait_interval)
                elapsed_time += wait_interval
            
            return {
                "task_id": task.task_id,
                "status": "timeout",
                "error": "Task execution timeout",
                "verification_points": task.verification_points
            }
            
        except Exception as e:
            logger.error(f"田野调查任务执行失败: {e}")
            return {
                "task_id": task.task_id,
                "status": "error",
                "error": str(e),
                "verification_points": task.verification_points
            }
    
    async def _analyze_verification_results(self,
                                          debate_result: BaxianDebateResult,
                                          verification_results: List[Dict[str, Any]],
                                          openhands_results: Dict[str, Any] = None) -> Dict[str, Any]:
        """分析验证结果"""
        
        verified_claims = []
        failed_verifications = []
        confidence_adjustments = {}
        
        for result in verification_results:
            if result["status"] == "success":
                # 分析验证数据与原论断的一致性
                consistency_score = await self._calculate_consistency(
                    result["verification_points"], 
                    result["data"]
                )
                
                verified_claims.append({
                    "claim": result["verification_points"][0],
                    "consistency_score": consistency_score,
                    "evidence": result["data"]
                })
                
                # 根据验证结果调整置信度
                if consistency_score > 0.8:
                    confidence_adjustments[result["task_id"]] = 0.1  # 提升置信度
                elif consistency_score < 0.3:
                    confidence_adjustments[result["task_id"]] = -0.2  # 降低置信度
            else:
                failed_verifications.append(result)
        
        # 整合OpenHands验证结果
        web_verification_summary = None
        if openhands_results:
            web_verification_summary = {
                "web_verification_enabled": True,
                "web_success_rate": openhands_results.get("verification_summary", {}).get("success_rate", 0),
                "web_confidence": openhands_results.get("verification_summary", {}).get("average_confidence", 0),
                "web_recommendation": openhands_results.get("final_recommendation", "UNKNOWN"),
                "web_evidence_count": sum(
                    r.get("evidence_count", 0)
                    for r in openhands_results.get("detailed_results", [])
                )
            }

            # 根据Web验证结果调整置信度
            web_confidence = openhands_results.get("verification_summary", {}).get("average_confidence", 0)
            if web_confidence > 0.7:
                adjustment += 0.15  # Web验证高置信度加分
            elif web_confidence < 0.3:
                adjustment -= 0.1   # Web验证低置信度减分
        else:
            web_verification_summary = {"web_verification_enabled": False}

        # 计算最终置信度
        original_confidence = debate_result.confidence_score
        if verification_results:
            adjustment = sum(confidence_adjustments.values()) / len(verification_results)
        else:
            adjustment = 0
        final_confidence = max(0.0, min(1.0, original_confidence + adjustment))
        
        return {
            "original_confidence": original_confidence,
            "final_confidence": final_confidence,
            "verified_claims": verified_claims,
            "failed_verifications": failed_verifications,
            "verification_summary": {
                "total_tasks": len(verification_results),
                "successful_verifications": len(verified_claims),
                "failed_verifications": len(failed_verifications)
            },
            "web_verification": web_verification_summary,
            "openhands_detailed_results": openhands_results.get("detailed_results", []) if openhands_results else []
        }
    
    async def _calculate_consistency(self, claims: List[str], evidence: Dict[str, Any]) -> float:
        """计算论断与证据的一致性分数"""
        # 这里应该实现更复杂的NLP分析
        # 暂时返回模拟分数
        if evidence and "error" not in evidence:
            return 0.75  # 模拟一致性分数
        return 0.1
    
    async def _generate_tianzun_report(self, 
                                     debate_result: BaxianDebateResult,
                                     verification_report: Dict[str, Any]) -> Dict[str, Any]:
        """生成给元始天尊的最终报告"""
        
        # 判断是否通过验证
        final_confidence = verification_report["final_confidence"]
        verification_passed = final_confidence >= 0.6
        
        recommendation = "APPROVE" if verification_passed else "REJECT"
        
        tianzun_report = {
            "report_id": f"tianzun_{debate_result.debate_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
            "debate_summary": {
                "debate_id": debate_result.debate_id,
                "topic": debate_result.topic,
                "participants": debate_result.participants,
                "original_confidence": debate_result.confidence_score
            },
            "verification_summary": verification_report["verification_summary"],
            "field_investigation_results": {
                "verified_claims": verification_report["verified_claims"],
                "failed_verifications": verification_report["failed_verifications"]
            },
            "lingbao_assessment": {
                "final_confidence": final_confidence,
                "confidence_change": final_confidence - debate_result.confidence_score,
                "verification_quality": self._assess_verification_quality(verification_report),
                "recommendation": recommendation
            },
            "tianzun_decision_points": [
                f"原始置信度: {debate_result.confidence_score:.2f}",
                f"验证后置信度: {final_confidence:.2f}",
                f"成功验证: {verification_report['verification_summary']['successful_verifications']}项",
                f"失败验证: {verification_report['verification_summary']['failed_verifications']}项",
                f"灵宝道君建议: {recommendation}"
            ],
            "timestamp": datetime.now().isoformat(),
            "signature": "灵宝道君 🔮"
        }
        
        return tianzun_report
    
    def _assess_verification_quality(self, verification_report: Dict[str, Any]) -> str:
        """评估验证质量"""
        total = verification_report["verification_summary"]["total_tasks"]
        successful = verification_report["verification_summary"]["successful_verifications"]
        
        if total == 0:
            return "无法评估"
        
        success_rate = successful / total
        
        if success_rate >= 0.8:
            return "优秀"
        elif success_rate >= 0.6:
            return "良好"
        elif success_rate >= 0.4:
            return "一般"
        else:
            return "较差"

# 使用示例
async def demonstrate_lingbao_verification():
    """演示灵宝道君验证流程"""
    
    # 模拟八仙论道结果
    debate_result = BaxianDebateResult(
        debate_id="debate_20250110_001",
        topic="特斯拉Q4财报对股价影响分析",
        participants=["吕洞宾", "何仙姑", "铁拐李", "蓝采和"],
        conclusions={
            "price_prediction": "上涨15%",
            "risk_level": "中等",
            "time_horizon": "1个月"
        },
        confidence_score=0.75,
        timestamp=datetime.now(),
        key_claims=[
            "特斯拉Q4交付量将超预期20%",
            "市场情绪对电动车板块转为乐观",
            "机构投资者增持特斯拉股票"
        ]
    )
    
    # 创建验证器
    verifier = LingbaoFieldVerifier(
        openmanus_url="https://your-openmanus-instance.com",
        api_key="your-api-key"
    )
    
    # 执行验证
    tianzun_report = await verifier.verify_debate_result(debate_result)
    
    print("🔮 灵宝道君田野调查报告:")
    print(json.dumps(tianzun_report, indent=2, ensure_ascii=False))

if __name__ == "__main__":
    asyncio.run(demonstrate_lingbao_verification())