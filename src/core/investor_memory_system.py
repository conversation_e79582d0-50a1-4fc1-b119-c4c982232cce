# -*- coding: utf-8 -*-
"""
九大主演记忆与学习系统
借鉴TradingAgents的向量化记忆机制，为散户投资者添加学习能力

核心功能：
1. 向量化记忆存储 - 使用ChromaDB存储投资经验
2. 相似度检索 - 基于事件相似性检索相关经验
3. 学习与反思 - 从成功和失败中学习
4. 情绪状态管理 - 跟踪投资者情绪变化
5. 个性化进化 - 根据经验调整投资风格

作者：太公心易BI系统
版本：v2.0
"""

import os
import json
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict
import hashlib

# 向量化存储
try:
    import chromadb
    from chromadb.config import Settings
    CHROMADB_AVAILABLE = True
except ImportError:
    CHROMADB_AVAILABLE = False
    logging.warning("ChromaDB未安装，将使用简化的内存存储")

# 嵌入模型
try:
    from openai import OpenAI
    OPENAI_AVAILABLE = True
except ImportError:
    OPENAI_AVAILABLE = False

try:
    import dashscope
    from dashscope import TextEmbedding
    DASHSCOPE_AVAILABLE = True
except ImportError:
    DASHSCOPE_AVAILABLE = False


@dataclass
class InvestmentExperience:
    """投资经验记录"""
    event_description: str
    investor_reaction: str
    market_outcome: str
    personal_outcome: str  # 盈利/亏损/持平
    lesson_learned: str
    confidence_change: float  # -1到1，信心变化
    emotion_before: str
    emotion_after: str
    timestamp: datetime
    event_hash: str


@dataclass
class EmotionalState:
    """情绪状态"""
    primary_emotion: str  # fear, greed, confidence, panic, euphoria, neutral
    intensity: float  # 0-1，情绪强度
    confidence_level: float  # 0-1，信心水平
    risk_appetite: float  # 0-1，风险偏好
    last_updated: datetime


class InvestorMemorySystem:
    """投资者记忆系统"""
    
    def __init__(self, investor_name: str, config: Dict[str, Any] = None):
        self.investor_name = investor_name
        self.config = config or {}
        self.logger = logging.getLogger(f'InvestorMemory_{investor_name}')
        
        # 初始化向量存储
        self._initialize_vector_store()
        
        # 初始化嵌入模型
        self._initialize_embedding_model()
        
        # 情绪状态
        self.emotional_state = EmotionalState(
            primary_emotion="neutral",
            intensity=0.5,
            confidence_level=0.5,
            risk_appetite=0.5,
            last_updated=datetime.now()
        )
        
        # 学习统计
        self.learning_stats = {
            "total_experiences": 0,
            "successful_predictions": 0,
            "failed_predictions": 0,
            "total_profit": 0.0,
            "total_loss": 0.0,
            "learning_rate": 0.1
        }
    
    def _initialize_vector_store(self):
        """初始化向量存储"""
        if CHROMADB_AVAILABLE:
            try:
                self.chroma_client = chromadb.Client(Settings(allow_reset=True))
                collection_name = f"investor_memory_{self.investor_name.lower()}"
                
                # 尝试获取现有集合
                try:
                    self.memory_collection = self.chroma_client.get_collection(name=collection_name)
                except:
                    # 创建新集合
                    self.memory_collection = self.chroma_client.create_collection(name=collection_name)
                
                self.vector_store_available = True
                self.logger.info(f"ChromaDB初始化成功: {collection_name}")
                
            except Exception as e:
                self.logger.error(f"ChromaDB初始化失败: {e}")
                self.vector_store_available = False
                self._initialize_fallback_storage()
        else:
            self.vector_store_available = False
            self._initialize_fallback_storage()
    
    def _initialize_fallback_storage(self):
        """初始化备用存储（简单的内存存储）"""
        self.memory_store = []
        self.logger.info("使用简化内存存储")
    
    def _initialize_embedding_model(self):
        """初始化嵌入模型"""
        # 优先使用DashScope（阿里百炼）
        if DASHSCOPE_AVAILABLE and os.getenv('DASHSCOPE_API_KEY'):
            self.embedding_provider = "dashscope"
            self.embedding_model = "text-embedding-v3"
            dashscope.api_key = os.getenv('DASHSCOPE_API_KEY')
            self.logger.info("使用DashScope嵌入模型")
            
        # 备用OpenAI
        elif OPENAI_AVAILABLE and os.getenv('OPENROUTER_API_KEY'):
            self.embedding_provider = "openai"
            self.embedding_model = "text-embedding-3-small"
            self.openai_client = OpenAI(
                api_key=os.getenv('OPENROUTER_API_KEY'),
                base_url="https://openrouter.ai/api/v1"
            )
            self.logger.info("使用OpenAI嵌入模型")
            
        else:
            self.embedding_provider = "none"
            self.logger.warning("无可用嵌入模型，使用关键词匹配")
    
    def get_embedding(self, text: str) -> List[float]:
        """获取文本嵌入向量"""
        try:
            if self.embedding_provider == "dashscope":
                response = TextEmbedding.call(
                    model=self.embedding_model,
                    input=text
                )
                if response.status_code == 200:
                    return response.output['embeddings'][0]['embedding']
                else:
                    raise Exception(f"DashScope错误: {response.code}")
                    
            elif self.embedding_provider == "openai":
                response = self.openai_client.embeddings.create(
                    model=self.embedding_model,
                    input=text
                )
                return response.data[0].embedding
                
            else:
                # 简化的向量化（使用哈希）
                return [float(hash(word) % 1000) / 1000 for word in text.split()[:100]]
                
        except Exception as e:
            self.logger.error(f"获取嵌入向量失败: {e}")
            # 返回简化向量
            return [float(hash(word) % 1000) / 1000 for word in text.split()[:100]]
    
    def add_experience(self, experience: InvestmentExperience):
        """添加投资经验"""
        try:
            if self.vector_store_available:
                # 使用ChromaDB存储
                embedding = self.get_embedding(experience.event_description)
                
                self.memory_collection.add(
                    documents=[experience.event_description],
                    metadatas=[{
                        "investor_reaction": experience.investor_reaction,
                        "market_outcome": experience.market_outcome,
                        "personal_outcome": experience.personal_outcome,
                        "lesson_learned": experience.lesson_learned,
                        "confidence_change": experience.confidence_change,
                        "emotion_before": experience.emotion_before,
                        "emotion_after": experience.emotion_after,
                        "timestamp": experience.timestamp.isoformat(),
                        "event_hash": experience.event_hash
                    }],
                    embeddings=[embedding],
                    ids=[experience.event_hash]
                )
            else:
                # 使用简化存储
                self.memory_store.append(asdict(experience))
            
            # 更新学习统计
            self._update_learning_stats(experience)
            
            # 更新情绪状态
            self._update_emotional_state(experience)
            
            self.logger.info(f"添加投资经验: {experience.event_description[:50]}...")
            
        except Exception as e:
            self.logger.error(f"添加经验失败: {e}")
    
    def get_relevant_experiences(self, current_event: str, n_matches: int = 3) -> List[Dict[str, Any]]:
        """获取相关投资经验"""
        try:
            if self.vector_store_available:
                # 使用向量相似度检索
                query_embedding = self.get_embedding(current_event)
                
                results = self.memory_collection.query(
                    query_embeddings=[query_embedding],
                    n_results=n_matches,
                    include=["metadatas", "documents", "distances"]
                )
                
                relevant_experiences = []
                for i in range(len(results["documents"][0])):
                    experience = {
                        "event_description": results["documents"][0][i],
                        "similarity_score": 1 - results["distances"][0][i],
                        **results["metadatas"][0][i]
                    }
                    relevant_experiences.append(experience)
                
                return relevant_experiences
                
            else:
                # 使用关键词匹配
                return self._keyword_based_matching(current_event, n_matches)
                
        except Exception as e:
            self.logger.error(f"检索相关经验失败: {e}")
            return []
    
    def _keyword_based_matching(self, current_event: str, n_matches: int) -> List[Dict[str, Any]]:
        """基于关键词的简化匹配"""
        event_keywords = set(current_event.lower().split())
        scored_experiences = []
        
        for exp in self.memory_store:
            exp_keywords = set(exp["event_description"].lower().split())
            overlap = len(event_keywords & exp_keywords)
            
            if overlap > 0:
                exp["similarity_score"] = overlap / len(event_keywords | exp_keywords)
                scored_experiences.append(exp)
        
        # 按相似度排序
        scored_experiences.sort(key=lambda x: x["similarity_score"], reverse=True)
        return scored_experiences[:n_matches]
    
    def _update_learning_stats(self, experience: InvestmentExperience):
        """更新学习统计"""
        self.learning_stats["total_experiences"] += 1
        
        if "盈利" in experience.personal_outcome:
            self.learning_stats["successful_predictions"] += 1
            # 提取盈利金额（简化处理）
            try:
                profit = float(''.join(filter(str.isdigit, experience.personal_outcome)))
                self.learning_stats["total_profit"] += profit
            except:
                pass
                
        elif "亏损" in experience.personal_outcome:
            self.learning_stats["failed_predictions"] += 1
            # 提取亏损金额
            try:
                loss = float(''.join(filter(str.isdigit, experience.personal_outcome)))
                self.learning_stats["total_loss"] += loss
            except:
                pass
    
    def _update_emotional_state(self, experience: InvestmentExperience):
        """更新情绪状态"""
        # 根据经验结果调整情绪
        if "盈利" in experience.personal_outcome:
            self.emotional_state.confidence_level = min(1.0, self.emotional_state.confidence_level + 0.1)
            self.emotional_state.primary_emotion = "confidence"
            self.emotional_state.risk_appetite = min(1.0, self.emotional_state.risk_appetite + 0.05)
            
        elif "亏损" in experience.personal_outcome:
            self.emotional_state.confidence_level = max(0.0, self.emotional_state.confidence_level - 0.15)
            self.emotional_state.primary_emotion = "fear"
            self.emotional_state.risk_appetite = max(0.0, self.emotional_state.risk_appetite - 0.1)
            
        else:
            self.emotional_state.primary_emotion = "neutral"
        
        # 调整情绪强度
        self.emotional_state.intensity = abs(experience.confidence_change)
        self.emotional_state.last_updated = datetime.now()
    
    def generate_learning_insights(self) -> Dict[str, Any]:
        """生成学习洞察"""
        total_exp = self.learning_stats["total_experiences"]
        if total_exp == 0:
            return {"message": "暂无投资经验数据"}
        
        success_rate = self.learning_stats["successful_predictions"] / total_exp
        net_profit = self.learning_stats["total_profit"] - self.learning_stats["total_loss"]
        
        insights = {
            "总经验数": total_exp,
            "成功率": f"{success_rate:.2%}",
            "净收益": f"{net_profit:.2f}",
            "当前情绪": self.emotional_state.primary_emotion,
            "信心水平": f"{self.emotional_state.confidence_level:.2%}",
            "风险偏好": f"{self.emotional_state.risk_appetite:.2%}",
            "学习建议": self._generate_learning_advice(success_rate, net_profit)
        }
        
        return insights
    
    def _generate_learning_advice(self, success_rate: float, net_profit: float) -> str:
        """生成学习建议"""
        if success_rate > 0.7 and net_profit > 0:
            return "表现优秀！继续保持当前策略，但要警惕过度自信。"
        elif success_rate > 0.5 and net_profit > 0:
            return "表现良好，可以适当增加仓位，但要控制风险。"
        elif success_rate < 0.4 or net_profit < 0:
            return "需要反思投资策略，建议降低仓位，多学习市场知识。"
        else:
            return "表现平平，建议保持谨慎，继续积累经验。"
    
    def export_memory_data(self) -> Dict[str, Any]:
        """导出记忆数据"""
        return {
            "investor_name": self.investor_name,
            "emotional_state": asdict(self.emotional_state),
            "learning_stats": self.learning_stats,
            "memory_count": len(self.memory_store) if not self.vector_store_available else self.memory_collection.count(),
            "export_time": datetime.now().isoformat()
        }


class MemorySystemManager:
    """记忆系统管理器 - 管理所有投资者的记忆"""
    
    def __init__(self, config: Dict[str, Any] = None):
        self.config = config or {}
        self.logger = logging.getLogger('MemorySystemManager')
        self.investor_memories: Dict[str, InvestorMemorySystem] = {}
    
    def get_investor_memory(self, investor_name: str) -> InvestorMemorySystem:
        """获取投资者记忆系统"""
        if investor_name not in self.investor_memories:
            self.investor_memories[investor_name] = InvestorMemorySystem(
                investor_name, self.config
            )
        return self.investor_memories[investor_name]
    
    def add_market_event_experience(self, event_title: str, event_description: str,
                                  investor_reactions: Dict[str, str],
                                  market_outcome: str = "待观察"):
        """为所有投资者添加市场事件经验"""
        event_hash = hashlib.md5(
            (event_title + event_description).encode()
        ).hexdigest()
        
        for investor_name, reaction in investor_reactions.items():
            memory_system = self.get_investor_memory(investor_name)
            
            experience = InvestmentExperience(
                event_description=f"{event_title}: {event_description}",
                investor_reaction=reaction,
                market_outcome=market_outcome,
                personal_outcome="待观察",
                lesson_learned="经验积累中...",
                confidence_change=0.0,
                emotion_before="neutral",
                emotion_after="neutral",
                timestamp=datetime.now(),
                event_hash=f"{event_hash}_{investor_name}"
            )
            
            memory_system.add_experience(experience)
    
    def get_collective_insights(self) -> Dict[str, Any]:
        """获取集体学习洞察"""
        insights = {}
        
        for investor_name, memory_system in self.investor_memories.items():
            insights[investor_name] = memory_system.generate_learning_insights()
        
        return {
            "individual_insights": insights,
            "collective_summary": self._generate_collective_summary(insights),
            "timestamp": datetime.now().isoformat()
        }
    
    def _generate_collective_summary(self, individual_insights: Dict) -> str:
        """生成集体总结"""
        total_investors = len(individual_insights)
        if total_investors == 0:
            return "暂无数据"
        
        # 统计情绪分布
        emotions = [insight.get("当前情绪", "neutral") for insight in individual_insights.values()]
        emotion_counts = {}
        for emotion in emotions:
            emotion_counts[emotion] = emotion_counts.get(emotion, 0) + 1
        
        dominant_emotion = max(emotion_counts, key=emotion_counts.get)
        
        return f"""
九大主演集体学习总结：
- 参与投资者: {total_investors}位
- 主导情绪: {dominant_emotion}
- 情绪分布: {emotion_counts}
- 集体智慧: 在市场的起伏中，每位投资者都在成长和学习
"""


# 使用示例
def main():
    """测试记忆系统"""
    # 创建记忆管理器
    manager = MemorySystemManager()
    
    # 模拟市场事件
    event_title = "美联储意外加息"
    event_description = "美联储宣布加息50个基点，超出市场预期"
    
    # 模拟投资者反应
    reactions = {
        "洪珏": "卧槽！要崩盘了！赶紧跑路！",
        "陈琉": "加息是利好银行股吧？我要抄底！",
        "黄琥": "技术指标显示超跌，可能反弹！",
        "陆珀": "群里大佬说要跌，我也跟着跑！",
        "兰琪": "这是预期内的，市场反应过度了！",
        "典瑛": "要跌了要跌了！还是等等再说...",
        "梓珂": "我的模型显示这是买入机会！",
        "白瑞": "又来收割韭菜了，我就看戏。",
        "贺珍": "散户恐慌，正好低价收筹。"
    }
    
    # 添加经验
    manager.add_market_event_experience(
        event_title, event_description, reactions, "市场下跌3%"
    )
    
    # 获取洞察
    insights = manager.get_collective_insights()
    print(json.dumps(insights, ensure_ascii=False, indent=2))


if __name__ == "__main__":
    main()