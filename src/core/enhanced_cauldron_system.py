# -*- coding: utf-8 -*-
"""
炼妖壶增强版统一系统
整合所有升级功能的主入口

核心组件：
1. 增强版RSS事件监控
2. 稷下学宫多智能体辩论
3. 九大主演记忆学习系统
4. N8N工作流集成
5. 实时数据处理

作者：太公心易BI系统
版本：v2.0 Enhanced
"""

import asyncio
import json
import logging
import os
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict
import schedule
import time
from pathlib import Path

# 导入增强组件
from .enhanced_rss_system import EnhancedEventProcessor
from .enhanced_jixia_academy import EnhancedJixiaAcademy
from .investor_memory_system import MemorySystemManager
from .simple_retail_theater import SimpleRetailTheater


@dataclass
class SystemConfig:
    """系统配置"""
    # API配置
    openrouter_api_key: str
    finnhub_api_key: Optional[str] = None
    alpha_vantage_key: Optional[str] = None
    newsapi_key: Optional[str] = None
    dashscope_api_key: Optional[str] = None
    
    # 监控配置
    monitoring_interval: int = 300  # 5分钟
    impact_threshold: float = 75.0
    max_events_per_cycle: int = 5
    
    # 存储配置
    data_dir: str = "./data"
    logs_dir: str = "./logs"
    
    # N8N配置
    n8n_webhook_url: Optional[str] = None
    n8n_enabled: bool = False
    
    # 调试配置
    debug_mode: bool = False
    log_level: str = "INFO"


class EnhancedCauldronSystem:
    """炼妖壶增强版统一系统"""
    
    def __init__(self, config: SystemConfig):
        self.config = config
        self.logger = self._setup_logging()
        
        # 初始化核心组件
        self.event_processor = EnhancedEventProcessor(config.openrouter_api_key)
        self.jixia_academy = EnhancedJixiaAcademy(config.openrouter_api_key)
        self.memory_manager = MemorySystemManager()
        self.retail_theater = SimpleRetailTheater()
        
        # 系统状态
        self.is_running = False
        self.last_cycle_time = None
        self.total_events_processed = 0
        self.system_stats = {
            "start_time": datetime.now(),
            "uptime": timedelta(0),
            "cycles_completed": 0,
            "events_processed": 0,
            "debates_conducted": 0,
            "errors_encountered": 0
        }
        
        # 创建必要目录
        self._create_directories()
        
        self.logger.info("🔥 炼妖壶增强版系统初始化完成")
    
    def _setup_logging(self) -> logging.Logger:
        """设置日志系统"""
        # 创建日志目录
        os.makedirs(self.config.logs_dir, exist_ok=True)
        
        # 配置日志格式
        log_format = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        
        # 文件日志
        file_handler = logging.FileHandler(
            os.path.join(self.config.logs_dir, f"cauldron_{datetime.now().strftime('%Y%m%d')}.log"),
            encoding='utf-8'
        )
        file_handler.setLevel(getattr(logging, self.config.log_level))
        file_handler.setFormatter(logging.Formatter(log_format))
        
        # 控制台日志
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)
        console_handler.setFormatter(logging.Formatter(log_format))
        
        # 配置根日志器
        logger = logging.getLogger('EnhancedCauldron')
        logger.setLevel(getattr(logging, self.config.log_level))
        logger.addHandler(file_handler)
        logger.addHandler(console_handler)
        
        return logger
    
    def _create_directories(self):
        """创建必要的目录结构"""
        directories = [
            self.config.data_dir,
            self.config.logs_dir,
            os.path.join(self.config.data_dir, "events"),
            os.path.join(self.config.data_dir, "debates"),
            os.path.join(self.config.data_dir, "memories"),
            os.path.join(self.config.data_dir, "exports")
        ]
        
        for directory in directories:
            os.makedirs(directory, exist_ok=True)
    
    async def start_monitoring(self):
        """启动监控系统"""
        if self.is_running:
            self.logger.warning("⚠️ 系统已在运行中")
            return
        
        self.is_running = True
        self.system_stats["start_time"] = datetime.now()
        
        self.logger.info("🚀 启动炼妖壶增强版监控系统")
        self.logger.info(f"📊 监控间隔: {self.config.monitoring_interval}秒")
        self.logger.info(f"🎯 影响力阈值: {self.config.impact_threshold}")
        
        try:
            while self.is_running:
                cycle_start = datetime.now()
                
                try:
                    # 执行监控周期
                    results = await self._run_monitoring_cycle()
                    
                    # 更新统计
                    self.system_stats["cycles_completed"] += 1
                    self.system_stats["events_processed"] += len(results)
                    self.system_stats["uptime"] = datetime.now() - self.system_stats["start_time"]
                    
                    # 保存结果
                    if results:
                        await self._save_cycle_results(results, cycle_start)
                    
                    self.last_cycle_time = cycle_start
                    
                    # 计算下次执行时间
                    cycle_duration = (datetime.now() - cycle_start).total_seconds()
                    sleep_time = max(0, self.config.monitoring_interval - cycle_duration)
                    
                    if sleep_time > 0:
                        self.logger.info(f"😴 监控周期完成，等待 {sleep_time:.1f} 秒后开始下一轮")
                        await asyncio.sleep(sleep_time)
                    
                except Exception as e:
                    self.system_stats["errors_encountered"] += 1
                    self.logger.error(f"❌ 监控周期异常: {e}")
                    await asyncio.sleep(60)  # 异常后等待1分钟
                    
        except KeyboardInterrupt:
            self.logger.info("⏹️ 收到停止信号")
        finally:
            self.is_running = False
            self.logger.info("🛑 监控系统已停止")
    
    async def _run_monitoring_cycle(self) -> List[Dict[str, Any]]:
        """运行单次监控周期"""
        self.logger.info("🔍 开始新的监控周期")
        
        # 1. 监控RSS和API新闻源
        results = await self.event_processor.run_monitoring_cycle()
        
        if not results:
            self.logger.info("📭 本轮未发现重大事件")
            return []
        
        # 2. 限制处理数量
        if len(results) > self.config.max_events_per_cycle:
            self.logger.info(f"⚡ 事件数量过多，限制处理前 {self.config.max_events_per_cycle} 个")
            results = results[:self.config.max_events_per_cycle]
        
        # 3. 增强处理每个事件
        enhanced_results = []
        for i, result in enumerate(results):
            try:
                enhanced_result = await self._enhance_event_processing(result, i)
                enhanced_results.append(enhanced_result)
                
                # 更新辩论统计
                self.system_stats["debates_conducted"] += 1
                
            except Exception as e:
                self.logger.error(f"❌ 增强处理事件失败: {e}")
                enhanced_results.append(result)  # 使用原始结果
        
        # 4. 触发N8N工作流（如果启用）
        if self.config.n8n_enabled and self.config.n8n_webhook_url:
            await self._trigger_n8n_workflow(enhanced_results)
        
        self.logger.info(f"✅ 监控周期完成，处理了 {len(enhanced_results)} 个重大事件")
        return enhanced_results
    
    async def _enhance_event_processing(self, result: Dict[str, Any], index: int) -> Dict[str, Any]:
        """增强事件处理"""
        event = result["event"]
        
        # 1. 生成简化版韭菜小剧场（作为对比）
        simple_theater_result = await self.retail_theater.run_quick_simulation(
            event["title"], event["description"]
        )
        
        # 2. 分析情绪变化趋势
        emotion_analysis = self._analyze_emotion_trends(result)
        
        # 3. 生成投资建议
        investment_advice = self._generate_investment_advice(result, emotion_analysis)
        
        # 4. 计算风险评级
        risk_rating = self._calculate_risk_rating(event, emotion_analysis)
        
        # 增强结果
        enhanced_result = {
            **result,
            "simple_theater": simple_theater_result,
            "emotion_analysis": emotion_analysis,
            "investment_advice": investment_advice,
            "risk_rating": risk_rating,
            "processing_index": index,
            "enhancement_timestamp": datetime.now().isoformat()
        }
        
        return enhanced_result
    
    def _analyze_emotion_trends(self, result: Dict[str, Any]) -> Dict[str, Any]:
        """分析情绪趋势"""
        debate_messages = result.get("debate_result", {}).get("debate_messages", [])
        
        if not debate_messages:
            return {"trend": "neutral", "intensity": 0.5, "consensus": "unknown"}
        
        # 统计正负方情绪
        positive_emotions = 0
        negative_emotions = 0
        total_intensity = 0
        
        for message in debate_messages:
            risk_level = getattr(message, 'risk_level', 50)
            
            if risk_level < 50:  # 低风险感知 = 乐观
                positive_emotions += 1
                total_intensity += (50 - risk_level) / 50
            else:  # 高风险感知 = 悲观
                negative_emotions += 1
                total_intensity += (risk_level - 50) / 50
        
        total_speakers = len(debate_messages)
        if total_speakers == 0:
            return {"trend": "neutral", "intensity": 0.5, "consensus": "unknown"}
        
        # 计算趋势
        if positive_emotions > negative_emotions:
            trend = "optimistic"
        elif negative_emotions > positive_emotions:
            trend = "pessimistic"
        else:
            trend = "neutral"
        
        # 计算强度
        intensity = total_intensity / total_speakers if total_speakers > 0 else 0.5
        
        # 计算共识度
        majority = max(positive_emotions, negative_emotions)
        consensus_ratio = majority / total_speakers
        
        if consensus_ratio > 0.7:
            consensus = "strong"
        elif consensus_ratio > 0.5:
            consensus = "moderate"
        else:
            consensus = "divided"
        
        return {
            "trend": trend,
            "intensity": intensity,
            "consensus": consensus,
            "positive_count": positive_emotions,
            "negative_count": negative_emotions,
            "consensus_ratio": consensus_ratio
        }
    
    def _generate_investment_advice(self, result: Dict[str, Any], emotion_analysis: Dict) -> Dict[str, Any]:
        """生成投资建议"""
        event = result["event"]
        impact_score = event.get("impact_score", 0)
        urgency = event.get("urgency_level", "low")
        sentiment = event.get("sentiment_score", 0)
        
        # 基础风险评估
        if impact_score >= 90:
            base_risk = "very_high"
        elif impact_score >= 75:
            base_risk = "high"
        elif impact_score >= 50:
            base_risk = "medium"
        else:
            base_risk = "low"
        
        # 情绪调整
        emotion_trend = emotion_analysis.get("trend", "neutral")
        consensus = emotion_analysis.get("consensus", "unknown")
        
        # 生成建议
        if base_risk in ["very_high", "high"] and emotion_trend == "pessimistic":
            action = "defensive"
            advice = "市场情绪悲观，建议降低仓位，关注防御性资产"
        elif base_risk == "low" and emotion_trend == "optimistic" and consensus == "strong":
            action = "cautious_bullish"
            advice = "市场情绪乐观但需谨慎，可适当增加仓位但控制风险"
        elif consensus == "divided":
            action = "wait_and_see"
            advice = "市场分歧较大，建议观望等待更明确信号"
        else:
            action = "neutral"
            advice = "保持当前策略，密切关注后续发展"
        
        return {
            "action": action,
            "advice": advice,
            "confidence": emotion_analysis.get("consensus_ratio", 0.5),
            "time_horizon": "short_term" if urgency == "high" else "medium_term"
        }
    
    def _calculate_risk_rating(self, event: Dict, emotion_analysis: Dict) -> Dict[str, Any]:
        """计算风险评级"""
        impact_score = event.get("impact_score", 0)
        urgency = event.get("urgency_level", "low")
        sentiment = event.get("sentiment_score", 0)
        
        # 基础评分
        risk_score = impact_score
        
        # 紧急度调整
        if urgency == "high":
            risk_score += 10
        elif urgency == "medium":
            risk_score += 5
        
        # 情绪调整
        emotion_intensity = emotion_analysis.get("intensity", 0.5)
        if emotion_analysis.get("trend") == "pessimistic":
            risk_score += emotion_intensity * 15
        
        # 共识度调整
        if emotion_analysis.get("consensus") == "strong":
            risk_score += 5  # 强共识增加风险
        
        # 限制在0-100范围
        risk_score = max(0, min(100, risk_score))
        
        # 评级分类
        if risk_score >= 90:
            rating = "CRITICAL"
            color = "🔴"
        elif risk_score >= 75:
            rating = "HIGH"
            color = "🟠"
        elif risk_score >= 50:
            rating = "MEDIUM"
            color = "🟡"
        elif risk_score >= 25:
            rating = "LOW"
            color = "🟢"
        else:
            rating = "MINIMAL"
            color = "🔵"
        
        return {
            "score": risk_score,
            "rating": rating,
            "color": color,
            "description": f"{color} {rating} - 风险评分: {risk_score:.1f}/100"
        }
    
    async def _trigger_n8n_workflow(self, results: List[Dict[str, Any]]):
        """触发N8N工作流"""
        try:
            import aiohttp
            
            payload = {
                "timestamp": datetime.now().isoformat(),
                "system": "enhanced_cauldron",
                "events_count": len(results),
                "events": [
                    {
                        "title": result["event"]["title"],
                        "impact_score": result["event"]["impact_score"],
                        "risk_rating": result["risk_rating"]["rating"],
                        "investment_advice": result["investment_advice"]["action"]
                    }
                    for result in results
                ]
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    self.config.n8n_webhook_url,
                    json=payload,
                    timeout=10
                ) as response:
                    if response.status == 200:
                        self.logger.info("✅ N8N工作流触发成功")
                    else:
                        self.logger.warning(f"⚠️ N8N工作流触发失败: {response.status}")
                        
        except Exception as e:
            self.logger.error(f"❌ N8N工作流触发异常: {e}")
    
    async def _save_cycle_results(self, results: List[Dict[str, Any]], cycle_time: datetime):
        """保存周期结果"""
        try:
            # 生成文件名
            filename = f"cycle_{cycle_time.strftime('%Y%m%d_%H%M%S')}.json"
            filepath = os.path.join(self.config.data_dir, "events", filename)
            
            # 准备保存数据
            save_data = {
                "cycle_time": cycle_time.isoformat(),
                "events_count": len(results),
                "system_stats": self.system_stats.copy(),
                "results": results
            }
            
            # 保存到文件
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(save_data, f, ensure_ascii=False, indent=2, default=str)
            
            self.logger.info(f"💾 周期结果已保存: {filename}")
            
        except Exception as e:
            self.logger.error(f"❌ 保存周期结果失败: {e}")
    
    def stop_monitoring(self):
        """停止监控"""
        self.is_running = False
        self.logger.info("🛑 正在停止监控系统...")
    
    def get_system_status(self) -> Dict[str, Any]:
        """获取系统状态"""
        return {
            "is_running": self.is_running,
            "last_cycle_time": self.last_cycle_time.isoformat() if self.last_cycle_time else None,
            "system_stats": {
                **self.system_stats,
                "start_time": self.system_stats["start_time"].isoformat(),
                "uptime_seconds": self.system_stats["uptime"].total_seconds()
            },
            "config": {
                "monitoring_interval": self.config.monitoring_interval,
                "impact_threshold": self.config.impact_threshold,
                "n8n_enabled": self.config.n8n_enabled,
                "debug_mode": self.config.debug_mode
            },
            "components_status": {
                "event_processor": "active",
                "jixia_academy": "active",
                "memory_manager": "active",
                "retail_theater": "active"
            }
        }
    
    async def manual_trigger(self, event_title: str, event_description: str) -> Dict[str, Any]:
        """手动触发事件处理"""
        self.logger.info(f"🎯 手动触发事件: {event_title}")
        
        # 创建模拟事件
        mock_event = {
            "title": event_title,
            "description": event_description,
            "impact_score": 80,  # 手动事件默认高影响
            "urgency_level": "high",
            "keywords": event_title.split(),
            "sentiment_score": 0.0,
            "source": "manual_trigger",
            "category": "手动触发"
        }
        
        # 触发稷下学宫辩论
        debate_result = await self.jixia_academy.start_debate(
            topic=f"如何看待：{event_title}",
            event_context=mock_event
        )
        
        # 生成简化版韭菜小剧场
        simple_theater = await self.retail_theater.run_quick_simulation(
            event_title, event_description
        )
        
        result = {
            "event": mock_event,
            "debate_result": debate_result,
            "simple_theater": simple_theater,
            "trigger_type": "manual",
            "timestamp": datetime.now().isoformat()
        }
        
        return result


# 配置加载函数
def load_config_from_env() -> SystemConfig:
    """从环境变量加载配置"""
    return SystemConfig(
        openrouter_api_key=os.getenv('OPENROUTER_API_KEY', ''),
        finnhub_api_key=os.getenv('FINNHUB_API_KEY'),
        alpha_vantage_key=os.getenv('ALPHA_VANTAGE_API_KEY'),
        newsapi_key=os.getenv('NEWSAPI_KEY'),
        dashscope_api_key=os.getenv('DASHSCOPE_API_KEY'),
        
        monitoring_interval=int(os.getenv('MONITORING_INTERVAL', '300')),
        impact_threshold=float(os.getenv('IMPACT_THRESHOLD', '75.0')),
        max_events_per_cycle=int(os.getenv('MAX_EVENTS_PER_CYCLE', '5')),
        
        data_dir=os.getenv('DATA_DIR', './data'),
        logs_dir=os.getenv('LOGS_DIR', './logs'),
        
        n8n_webhook_url=os.getenv('N8N_WEBHOOK_URL'),
        n8n_enabled=os.getenv('N8N_ENABLED', 'false').lower() == 'true',
        
        debug_mode=os.getenv('DEBUG_MODE', 'false').lower() == 'true',
        log_level=os.getenv('LOG_LEVEL', 'INFO')
    )


# 主函数
async def main():
    """主函数 - 启动增强版炼妖壶系统"""
    print("🔥 炼妖壶增强版系统启动中...")
    
    # 加载配置
    config = load_config_from_env()
    
    if not config.openrouter_api_key:
        print("❌ 错误: 未设置 OPENROUTER_API_KEY 环境变量")
        return
    
    # 创建系统实例
    system = EnhancedCauldronSystem(config)
    
    try:
        # 启动监控
        await system.start_monitoring()
    except KeyboardInterrupt:
        print("\n⏹️ 收到停止信号")
    finally:
        system.stop_monitoring()
        print("🛑 系统已停止")


if __name__ == "__main__":
    asyncio.run(main())