#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
太公心易API接口模块
提供标准化的心易系统调用接口

功能模块：
1. 占卜接口 - 各种占卜方式
2. 查询接口 - 卦象查询和搜索  
3. 分析接口 - 深度分析和解读
4. 管理接口 - 系统管理和配置

作者：太公心易BI系统
版本：v1.0 XinYi API
"""

from typing import Dict, List, Any, Optional, Union
from datetime import datetime, timedelta
import json
import logging
from pathlib import Path

from .xinyi_system import XinYiSystem, GuaXiang, GuaLevel, GuaCategory
from .gua_data import get_complete_gua_dict, get_gua_categories, get_level_descriptions


class XinYiAPI:
    """太公心易API类"""
    
    def __init__(self, data_dir: str = "data/xinyi"):
        self.system = XinYiSystem()
        self.data_dir = Path(data_dir)
        self.data_dir.mkdir(parents=True, exist_ok=True)
        self.logger = logging.getLogger('XinYiAPI')
        
        # 加载完整卦象数据
        self._load_complete_data()
        
        # 历史记录
        self.history_file = self.data_dir / "divination_history.json"
        self.daily_gua_file = self.data_dir / "daily_gua.json"
    
    def _load_complete_data(self):
        """加载完整的六十四卦数据"""
        complete_gua_dict = get_complete_gua_dict()
        self.system.gua_dict.update(complete_gua_dict)
        
        # 重建索引
        self.system.category_mapping = self.system._build_category_mapping()
        self.system.keyword_index = self.system._build_keyword_index()
        
        self.logger.info(f"已加载 {len(self.system.gua_dict)} 个卦象")
    
    # ==================== 占卜接口 ====================
    
    def divine_random(self, user_id: str = None) -> Dict[str, Any]:
        """随机占卜"""
        gua = self.system.random_gua()
        result = self._format_divination_result(gua, "随机占卜", user_id)
        self._save_history(result)
        return result
    
    def divine_by_question(self, question: str, user_id: str = None, 
                          context: Dict[str, Any] = None) -> Dict[str, Any]:
        """问题占卜"""
        gua, reading = self.system.divine_by_question(question, context)
        result = self._format_divination_result(
            gua, f"问题占卜: {question}", user_id, reading
        )
        self._save_history(result)
        return result
    
    def divine_daily(self, date: str = None, user_id: str = None) -> Dict[str, Any]:
        """每日一卦"""
        if date is None:
            date = datetime.now().strftime("%Y-%m-%d")
        
        # 检查是否已有今日卦象
        daily_gua = self._get_daily_gua_cache(date)
        if daily_gua:
            return daily_gua
        
        gua = self.system.get_daily_gua(date)
        result = self._format_divination_result(gua, f"每日一卦: {date}", user_id)
        
        # 缓存今日卦象
        self._save_daily_gua_cache(date, result)
        return result
    
    def divine_by_numbers(self, numbers: List[int], user_id: str = None) -> Dict[str, Any]:
        """数字占卜（根据提供的数字计算卦象）"""
        # 简单的数字转卦象算法
        total = sum(numbers) if numbers else 1
        gua_number = (total % 64) + 1
        
        gua = self.system.get_gua_by_number(gua_number)
        if not gua:
            gua = self.system.random_gua()
        
        result = self._format_divination_result(
            gua, f"数字占卜: {numbers}", user_id
        )
        self._save_history(result)
        return result
    
    def divine_by_time(self, user_id: str = None) -> Dict[str, Any]:
        """时间占卜（根据当前时间计算卦象）"""
        now = datetime.now()
        # 使用时分秒计算
        numbers = [now.hour, now.minute, now.second]
        return self.divine_by_numbers(numbers, user_id)
    
    # ==================== 查询接口 ====================
    
    def get_gua_by_number(self, number: int) -> Optional[Dict[str, Any]]:
        """根据卦序号查询卦象"""
        gua = self.system.get_gua_by_number(number)
        return self._format_gua_info(gua) if gua else None
    
    def get_gua_by_name(self, name: str) -> Optional[Dict[str, Any]]:
        """根据卦名查询卦象"""
        gua = self.system.get_gua_by_name(name)
        return self._format_gua_info(gua) if gua else None
    
    def search_guas(self, keywords: List[str] = None, 
                   category: str = None, level: str = None) -> List[Dict[str, Any]]:
        """搜索卦象"""
        results = []
        
        if keywords:
            guas = self.system.search_by_keywords(keywords)
            results.extend(guas)
        
        if category:
            try:
                cat = GuaCategory(category)
                guas = self.system.get_by_category(cat)
                results.extend(guas)
            except ValueError:
                pass
        
        if level:
            try:
                lv = GuaLevel(level)
                guas = [gua for gua in self.system.gua_dict.values() if gua.level == lv]
                results.extend(guas)
            except ValueError:
                pass
        
        # 去重
        unique_guas = {}
        for gua in results:
            unique_guas[gua.number] = gua
        
        return [self._format_gua_info(gua) for gua in unique_guas.values()]
    
    def get_all_guas(self) -> List[Dict[str, Any]]:
        """获取所有卦象"""
        return [self._format_gua_info(gua) for gua in self.system.gua_dict.values()]
    
    def get_categories(self) -> Dict[str, Any]:
        """获取卦象分类信息"""
        return get_gua_categories()
    
    def get_levels(self) -> Dict[str, Any]:
        """获取卦象等级信息"""
        return get_level_descriptions()
    
    # ==================== 分析接口 ====================
    
    def analyze_gua_combination(self, gua_numbers: List[int]) -> Dict[str, Any]:
        """分析多个卦象的组合含义"""
        guas = []
        for number in gua_numbers:
            gua = self.system.get_gua_by_number(number)
            if gua:
                guas.append(gua)
        
        if not guas:
            return {"error": "未找到有效卦象"}
        
        # 分析组合
        analysis = {
            "guas": [self._format_gua_info(gua) for gua in guas],
            "combination_analysis": self._analyze_combination(guas),
            "overall_advice": self._generate_combination_advice(guas),
            "generated_at": datetime.now().isoformat()
        }
        
        return analysis
    
    def get_fortune_trend(self, days: int = 7, user_id: str = None) -> Dict[str, Any]:
        """获取运势趋势（连续多日的卦象）"""
        trends = []
        base_date = datetime.now()
        
        for i in range(days):
            date = (base_date - timedelta(days=i)).strftime("%Y-%m-%d")
            gua = self.system.get_daily_gua(date)
            trends.append({
                "date": date,
                "gua": self._format_gua_info(gua),
                "score": self._calculate_fortune_score(gua)
            })
        
        # 分析趋势
        trend_analysis = self._analyze_fortune_trend(trends)
        
        return {
            "trends": trends,
            "analysis": trend_analysis,
            "generated_at": datetime.now().isoformat()
        }
    
    def get_compatibility(self, gua1_number: int, gua2_number: int) -> Dict[str, Any]:
        """分析两个卦象的相容性"""
        gua1 = self.system.get_gua_by_number(gua1_number)
        gua2 = self.system.get_gua_by_number(gua2_number)
        
        if not gua1 or not gua2:
            return {"error": "卦象不存在"}
        
        compatibility = self._calculate_compatibility(gua1, gua2)
        
        return {
            "gua1": self._format_gua_info(gua1),
            "gua2": self._format_gua_info(gua2),
            "compatibility_score": compatibility["score"],
            "compatibility_level": compatibility["level"],
            "analysis": compatibility["analysis"],
            "advice": compatibility["advice"],
            "generated_at": datetime.now().isoformat()
        }
    
    # ==================== 管理接口 ====================
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取系统统计信息"""
        stats = self.system.get_statistics()
        
        # 添加使用统计
        history = self._load_history()
        stats["usage_stats"] = {
            "total_divinations": len(history),
            "recent_divinations": len([
                h for h in history 
                if datetime.fromisoformat(h["timestamp"]) > datetime.now() - timedelta(days=30)
            ])
        }
        
        return stats
    
    def get_history(self, user_id: str = None, limit: int = 50) -> List[Dict[str, Any]]:
        """获取占卜历史"""
        history = self._load_history()
        
        if user_id:
            history = [h for h in history if h.get("user_id") == user_id]
        
        # 按时间倒序排列
        history.sort(key=lambda x: x["timestamp"], reverse=True)
        
        return history[:limit]
    
    def clear_history(self, user_id: str = None) -> Dict[str, Any]:
        """清除历史记录"""
        if user_id:
            history = self._load_history()
            history = [h for h in history if h.get("user_id") != user_id]
            self._save_history_list(history)
            return {"message": f"已清除用户 {user_id} 的历史记录"}
        else:
            if self.history_file.exists():
                self.history_file.unlink()
            return {"message": "已清除所有历史记录"}
    
    # ==================== 私有方法 ====================
    
    def _format_divination_result(self, gua: GuaXiang, method: str, 
                                user_id: str = None, custom_reading: str = None) -> Dict[str, Any]:
        """格式化占卜结果"""
        return {
            "id": self._generate_id(),
            "method": method,
            "user_id": user_id,
            "gua": self._format_gua_info(gua),
            "reading": custom_reading or gua.interpretation,
            "timestamp": datetime.now().isoformat()
        }
    
    def _format_gua_info(self, gua: GuaXiang) -> Dict[str, Any]:
        """格式化卦象信息"""
        return {
            "number": gua.number,
            "name": gua.name,
            "alias": gua.alias,
            "level": gua.level.value,
            "category": gua.category.value,
            "upper_gua": gua.upper_gua,
            "lower_gua": gua.lower_gua,
            "yao_lines": gua.yao_lines,
            "gua_ci": gua.gua_ci,
            "xiang_ci": gua.xiang_ci,
            "interpretation": gua.interpretation,
            "judgment": gua.judgment,
            "suitable_for": gua.suitable_for,
            "avoid_when": gua.avoid_when,
            "keywords": gua.keywords,
            "action_advice": gua.action_advice,
            "timing_advice": gua.timing_advice,
            "risk_warning": gua.risk_warning
        }
    
    def _generate_id(self) -> str:
        """生成唯一ID"""
        import uuid
        return str(uuid.uuid4())[:8]
    
    def _save_history(self, result: Dict[str, Any]):
        """保存占卜历史"""
        history = self._load_history()
        history.append(result)
        
        # 限制历史记录数量
        if len(history) > 1000:
            history = history[-1000:]
        
        self._save_history_list(history)
    
    def _load_history(self) -> List[Dict[str, Any]]:
        """加载历史记录"""
        if not self.history_file.exists():
            return []
        
        try:
            with open(self.history_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            self.logger.error(f"加载历史记录失败: {e}")
            return []
    
    def _save_history_list(self, history: List[Dict[str, Any]]):
        """保存历史记录列表"""
        try:
            with open(self.history_file, 'w', encoding='utf-8') as f:
                json.dump(history, f, ensure_ascii=False, indent=2)
        except Exception as e:
            self.logger.error(f"保存历史记录失败: {e}")
    
    def _get_daily_gua_cache(self, date: str) -> Optional[Dict[str, Any]]:
        """获取每日卦象缓存"""
        if not self.daily_gua_file.exists():
            return None
        
        try:
            with open(self.daily_gua_file, 'r', encoding='utf-8') as f:
                cache = json.load(f)
                return cache.get(date)
        except Exception:
            return None
    
    def _save_daily_gua_cache(self, date: str, result: Dict[str, Any]):
        """保存每日卦象缓存"""
        cache = {}
        if self.daily_gua_file.exists():
            try:
                with open(self.daily_gua_file, 'r', encoding='utf-8') as f:
                    cache = json.load(f)
            except Exception:
                pass
        
        cache[date] = result
        
        # 只保留最近30天的缓存
        cutoff_date = (datetime.now() - timedelta(days=30)).strftime("%Y-%m-%d")
        cache = {k: v for k, v in cache.items() if k >= cutoff_date}
        
        try:
            with open(self.daily_gua_file, 'w', encoding='utf-8') as f:
                json.dump(cache, f, ensure_ascii=False, indent=2)
        except Exception as e:
            self.logger.error(f"保存每日卦象缓存失败: {e}")
    
    def _analyze_combination(self, guas: List[GuaXiang]) -> str:
        """分析卦象组合"""
        if len(guas) == 1:
            return "单卦分析，重点关注卦象本身的含义。"
        
        levels = [gua.level for gua in guas]
        categories = [gua.category for gua in guas]
        
        analysis = "组合分析：\n"
        
        # 等级分析
        good_count = sum(1 for level in levels if level in [GuaLevel.SHANG_SHANG, GuaLevel.SHANG_JI])
        bad_count = sum(1 for level in levels if level in [GuaLevel.XIA_JI, GuaLevel.XIA_XIA])
        
        if good_count > bad_count:
            analysis += "整体运势偏好，多数卦象显示积极信号。\n"
        elif bad_count > good_count:
            analysis += "整体运势偏弱，需要谨慎应对挑战。\n"
        else:
            analysis += "运势平衡，需要把握时机，稳步前进。\n"
        
        # 分类分析
        unique_categories = set(categories)
        if len(unique_categories) == 1:
            analysis += f"卦象集中在{list(unique_categories)[0].value}类，主题明确。\n"
        else:
            analysis += f"涉及多个类别：{', '.join([cat.value for cat in unique_categories])}，需要综合考虑。\n"
        
        return analysis
    
    def _generate_combination_advice(self, guas: List[GuaXiang]) -> str:
        """生成组合建议"""
        all_keywords = []
        for gua in guas:
            all_keywords.extend(gua.keywords)
        
        # 统计关键词频率
        from collections import Counter
        keyword_freq = Counter(all_keywords)
        top_keywords = [kw for kw, _ in keyword_freq.most_common(3)]
        
        advice = f"综合建议：基于卦象组合，当前应重点关注：{', '.join(top_keywords)}。"
        
        # 根据主要关键词给出具体建议
        if "成功" in top_keywords or "突破" in top_keywords:
            advice += "把握机遇，积极进取。"
        elif "稳定" in top_keywords or "积累" in top_keywords:
            advice += "稳扎稳打，注重基础。"
        elif "变化" in top_keywords or "适应" in top_keywords:
            advice += "灵活应变，顺势而为。"
        else:
            advice += "保持平常心，循序渐进。"
        
        return advice
    
    def _analyze_fortune_trend(self, trends: List[Dict[str, Any]]) -> Dict[str, Any]:
        """分析运势趋势"""
        scores = [trend["score"] for trend in trends]
        
        # 计算趋势
        if len(scores) >= 2:
            recent_avg = sum(scores[:3]) / min(3, len(scores))
            earlier_avg = sum(scores[3:]) / max(1, len(scores) - 3)
            
            if recent_avg > earlier_avg + 10:
                trend_direction = "上升"
            elif recent_avg < earlier_avg - 10:
                trend_direction = "下降"
            else:
                trend_direction = "平稳"
        else:
            trend_direction = "平稳"
        
        return {
            "trend_direction": trend_direction,
            "average_score": sum(scores) / len(scores),
            "highest_score": max(scores),
            "lowest_score": min(scores),
            "stability": "稳定" if max(scores) - min(scores) < 30 else "波动较大"
        }
    
    def _calculate_fortune_score(self, gua: GuaXiang) -> int:
        """计算运势分数"""
        level_scores = {
            GuaLevel.SHANG_SHANG: 90,
            GuaLevel.SHANG_JI: 75,
            GuaLevel.ZHONG_PING: 60,
            GuaLevel.XIA_JI: 40,
            GuaLevel.XIA_XIA: 25
        }
        return level_scores.get(gua.level, 60)
    
    def _calculate_compatibility(self, gua1: GuaXiang, gua2: GuaXiang) -> Dict[str, Any]:
        """计算两个卦象的相容性"""
        score = 50  # 基础分数
        
        # 等级相容性
        level_diff = abs(
            list(GuaLevel).index(gua1.level) - 
            list(GuaLevel).index(gua2.level)
        )
        score += (4 - level_diff) * 10
        
        # 分类相容性
        if gua1.category == gua2.category:
            score += 20
        
        # 关键词相容性
        common_keywords = set(gua1.keywords) & set(gua2.keywords)
        score += len(common_keywords) * 5
        
        # 确保分数在合理范围内
        score = max(0, min(100, score))
        
        # 确定相容性等级
        if score >= 80:
            level = "高度相容"
        elif score >= 60:
            level = "较为相容"
        elif score >= 40:
            level = "一般相容"
        else:
            level = "相容性较低"
        
        analysis = f"两卦相容性分析：{gua1.name}卦与{gua2.name}卦的相容性为{level}。"
        
        if score >= 70:
            advice = "两卦相互促进，可以同时关注两个方面的发展。"
        elif score >= 50:
            advice = "两卦基本协调，需要平衡两者的要求。"
        else:
            advice = "两卦存在冲突，需要优先选择其中一个方向。"
        
        return {
            "score": score,
            "level": level,
            "analysis": analysis,
            "advice": advice
        }


# 全局API实例
xinyi_api = XinYiAPI()


def main():
    """演示API功能"""
    print("🎯 太公心易API演示")
    print("=" * 50)
    
    # 随机占卜
    print("\n🎲 随机占卜:")
    result = xinyi_api.divine_random("demo_user")
    print(f"卦象: {result['gua']['name']}卦 - {result['gua']['alias']}")
    print(f"等级: {result['gua']['level']}")
    
    # 问题占卜
    print("\n🔮 问题占卜:")
    result = xinyi_api.divine_by_question("我的事业发展如何？", "demo_user")
    print(f"卦象: {result['gua']['name']}卦")
    print(f"解读: {result['reading'][:100]}...")
    
    # 每日一卦
    print("\n📅 每日一卦:")
    result = xinyi_api.divine_daily()
    print(f"今日卦象: {result['gua']['name']}卦 - {result['gua']['alias']}")
    
    # 系统统计
    print("\n📊 系统统计:")
    stats = xinyi_api.get_statistics()
    print(f"总卦象数: {stats['total_guas']}")
    print(f"使用次数: {stats['usage_stats']['total_divinations']}")


if __name__ == "__main__":
    main()