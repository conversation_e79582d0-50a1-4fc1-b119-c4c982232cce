# -*- coding: utf-8 -*-
"""
OpenHands集成模块 - 灵宝道君的Web验证工具
通过OpenHands云服务进行实时web验证和数据分析

作者：太公心易BI系统
版本：v1.0
"""

import asyncio
import json
import logging
import aiohttp
from datetime import datetime
from typing import Dict, List, Any, Optional
from dataclasses import dataclass

logger = logging.getLogger(__name__)

@dataclass
class OpenHandsTask:
    """OpenHands验证任务"""
    task_id: str
    task_type: str  # web_search, data_analysis, fact_check
    description: str
    target_claims: List[str]
    verification_urls: List[str]
    expected_outcome: str
    priority: int = 1

@dataclass
class OpenHandsResult:
    """OpenHands验证结果"""
    task_id: str
    success: bool
    findings: Dict[str, Any]
    evidence: List[str]
    confidence_score: float
    execution_time: float
    raw_output: str

class OpenHandsClient:
    """OpenHands云服务客户端"""
    
    def __init__(self, api_key: str, base_url: str = "https://app.all-hands.dev"):
        self.api_key = api_key
        self.base_url = base_url
        self.session = None
        
    async def __aenter__(self):
        self.session = aiohttp.ClientSession()
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    async def create_verification_session(self, task: OpenHandsTask) -> str:
        """创建验证会话"""
        try:
            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }
            
            payload = {
                "task_type": task.task_type,
                "description": task.description,
                "claims_to_verify": task.target_claims,
                "search_urls": task.verification_urls,
                "priority": task.priority
            }
            
            async with self.session.post(
                f"{self.base_url}/api/v1/sessions",
                headers=headers,
                json=payload
            ) as response:
                if response.status == 200:
                    result = await response.json()
                    return result.get("session_id")
                else:
                    logger.error(f"创建OpenHands会话失败: {response.status}")
                    return None
                    
        except Exception as e:
            logger.error(f"OpenHands会话创建异常: {e}")
            return None
    
    async def execute_web_verification(self, session_id: str, task: OpenHandsTask) -> OpenHandsResult:
        """执行web验证任务"""
        start_time = datetime.now()
        
        try:
            # 构建验证指令
            verification_prompt = self._build_verification_prompt(task)
            
            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }
            
            payload = {
                "session_id": session_id,
                "command": verification_prompt,
                "timeout": 300  # 5分钟超时
            }
            
            async with self.session.post(
                f"{self.base_url}/api/v1/execute",
                headers=headers,
                json=payload
            ) as response:
                
                execution_time = (datetime.now() - start_time).total_seconds()
                
                if response.status == 200:
                    result = await response.json()
                    return self._parse_verification_result(task, result, execution_time)
                else:
                    logger.error(f"OpenHands执行失败: {response.status}")
                    return OpenHandsResult(
                        task_id=task.task_id,
                        success=False,
                        findings={},
                        evidence=[],
                        confidence_score=0.0,
                        execution_time=execution_time,
                        raw_output=f"执行失败: {response.status}"
                    )
                    
        except Exception as e:
            execution_time = (datetime.now() - start_time).total_seconds()
            logger.error(f"OpenHands执行异常: {e}")
            return OpenHandsResult(
                task_id=task.task_id,
                success=False,
                findings={},
                evidence=[],
                confidence_score=0.0,
                execution_time=execution_time,
                raw_output=f"执行异常: {str(e)}"
            )
    
    def _build_verification_prompt(self, task: OpenHandsTask) -> str:
        """构建验证提示词"""
        if task.task_type == "web_search":
            return f"""
作为灵宝道君的田野调查助手，请验证以下投资论断：

待验证论断：
{chr(10).join(f"- {claim}" for claim in task.target_claims)}

验证任务：
1. 搜索相关的最新新闻和数据
2. 访问权威财经网站获取信息
3. 分析数据的真实性和时效性
4. 总结验证结果

请使用Python代码进行web搜索和数据分析，并提供详细的验证报告。
"""
        elif task.task_type == "data_analysis":
            return f"""
请分析以下投资数据的准确性：

分析目标：
{task.description}

数据来源：
{chr(10).join(f"- {url}" for url in task.verification_urls)}

请编写Python代码：
1. 获取相关数据
2. 进行统计分析
3. 验证数据的合理性
4. 生成分析报告
"""
        else:  # fact_check
            return f"""
请对以下投资建议进行事实核查：

核查内容：
{chr(10).join(f"- {claim}" for claim in task.target_claims)}

核查要求：
1. 验证相关公司的基本面数据
2. 检查市场数据的准确性
3. 核实新闻事件的真实性
4. 评估预测的合理性

请提供详细的事实核查报告。
"""
    
    def _parse_verification_result(self, task: OpenHandsTask, result: Dict, execution_time: float) -> OpenHandsResult:
        """解析验证结果"""
        try:
            # 提取关键信息
            output = result.get("output", "")
            success = result.get("success", False)
            
            # 分析输出内容，提取证据和发现
            findings = self._extract_findings(output)
            evidence = self._extract_evidence(output)
            confidence = self._calculate_confidence(findings, evidence)
            
            return OpenHandsResult(
                task_id=task.task_id,
                success=success,
                findings=findings,
                evidence=evidence,
                confidence_score=confidence,
                execution_time=execution_time,
                raw_output=output
            )
            
        except Exception as e:
            logger.error(f"解析验证结果失败: {e}")
            return OpenHandsResult(
                task_id=task.task_id,
                success=False,
                findings={},
                evidence=[],
                confidence_score=0.0,
                execution_time=execution_time,
                raw_output=str(result)
            )
    
    def _extract_findings(self, output: str) -> Dict[str, Any]:
        """从输出中提取关键发现"""
        findings = {}
        
        # 简单的关键词提取（实际应用中可以使用更复杂的NLP）
        if "上涨" in output or "增长" in output:
            findings["trend"] = "positive"
        elif "下跌" in output or "下降" in output:
            findings["trend"] = "negative"
        else:
            findings["trend"] = "neutral"
            
        # 提取数字信息
        import re
        numbers = re.findall(r'\d+\.?\d*%', output)
        if numbers:
            findings["percentages"] = numbers
            
        return findings
    
    def _extract_evidence(self, output: str) -> List[str]:
        """提取证据链接和引用"""
        evidence = []
        
        # 提取URL
        import re
        urls = re.findall(r'http[s]?://(?:[a-zA-Z]|[0-9]|[$-_@.&+]|[!*\\(\\),]|(?:%[0-9a-fA-F][0-9a-fA-F]))+', output)
        evidence.extend(urls)
        
        # 提取引用
        quotes = re.findall(r'"([^"]*)"', output)
        evidence.extend(quotes[:3])  # 最多3个引用
        
        return evidence
    
    def _calculate_confidence(self, findings: Dict, evidence: List[str]) -> float:
        """计算置信度分数"""
        base_score = 0.5
        
        # 有发现加分
        if findings:
            base_score += 0.2
            
        # 有证据加分
        if evidence:
            base_score += min(len(evidence) * 0.1, 0.3)
            
        return min(base_score, 1.0)

class LingbaoOpenHandsVerifier:
    """灵宝道君OpenHands验证器"""
    
    def __init__(self, api_key: str):
        self.api_key = api_key
        self.client = None
        
    async def verify_debate_conclusions(self, debate_result: Dict[str, Any]) -> Dict[str, Any]:
        """验证辩论结论"""
        logger.info("🔮 灵宝道君启动OpenHands田野调查...")
        
        # 生成验证任务
        tasks = self._generate_verification_tasks(debate_result)
        
        # 执行验证
        verification_results = []
        async with OpenHandsClient(self.api_key) as client:
            for task in tasks:
                session_id = await client.create_verification_session(task)
                if session_id:
                    result = await client.execute_web_verification(session_id, task)
                    verification_results.append(result)
        
        # 综合分析
        final_report = self._analyze_verification_results(debate_result, verification_results)
        
        logger.info(f"✅ 田野调查完成，验证了{len(verification_results)}个任务")
        return final_report
    
    def _generate_verification_tasks(self, debate_result: Dict[str, Any]) -> List[OpenHandsTask]:
        """生成验证任务"""
        tasks = []
        
        # 基于辩论结论生成不同类型的验证任务
        if "price_prediction" in debate_result.get("conclusions", {}):
            tasks.append(OpenHandsTask(
                task_id=f"price_verify_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                task_type="web_search",
                description="验证价格预测的合理性",
                target_claims=[debate_result["conclusions"]["price_prediction"]],
                verification_urls=[
                    "https://finance.yahoo.com",
                    "https://www.bloomberg.com",
                    "https://cn.investing.com"
                ],
                expected_outcome="价格预测验证报告"
            ))
        
        if "key_claims" in debate_result:
            tasks.append(OpenHandsTask(
                task_id=f"claims_verify_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                task_type="fact_check",
                description="核查关键论断的事实依据",
                target_claims=debate_result["key_claims"],
                verification_urls=[],
                expected_outcome="事实核查报告"
            ))
        
        return tasks
    
    def _analyze_verification_results(self, debate_result: Dict, verification_results: List[OpenHandsResult]) -> Dict[str, Any]:
        """分析验证结果"""
        total_confidence = sum(r.confidence_score for r in verification_results)
        avg_confidence = total_confidence / len(verification_results) if verification_results else 0
        
        successful_verifications = [r for r in verification_results if r.success]
        success_rate = len(successful_verifications) / len(verification_results) if verification_results else 0
        
        return {
            "verification_summary": {
                "total_tasks": len(verification_results),
                "successful_tasks": len(successful_verifications),
                "success_rate": success_rate,
                "average_confidence": avg_confidence
            },
            "detailed_results": [
                {
                    "task_id": r.task_id,
                    "success": r.success,
                    "confidence": r.confidence_score,
                    "key_findings": r.findings,
                    "evidence_count": len(r.evidence)
                }
                for r in verification_results
            ],
            "final_recommendation": "APPROVE" if avg_confidence >= 0.7 and success_rate >= 0.8 else "REVIEW_REQUIRED",
            "verification_timestamp": datetime.now().isoformat(),
            "original_debate": debate_result
        }

# 使用示例
async def demo_openhands_verification():
    """演示OpenHands验证功能"""
    
    # 模拟辩论结果
    debate_result = {
        "debate_id": "debate_20250113_001",
        "topic": "特斯拉Q4财报影响分析",
        "conclusions": {
            "price_prediction": "上涨15%",
            "risk_level": "中等",
            "time_horizon": "1个月"
        },
        "key_claims": [
            "特斯拉Q4交付量将超预期20%",
            "电动车市场需求强劲",
            "机构投资者看好特斯拉前景"
        ]
    }
    
    # 创建验证器
    verifier = LingbaoOpenHandsVerifier(api_key="hA04ZDQbdKUbBCqmN5ZPFkcdK0xsKLwX")
    
    # 执行验证
    verification_report = await verifier.verify_debate_conclusions(debate_result)
    
    print("🔮 灵宝道君OpenHands验证报告:")
    print(json.dumps(verification_report, indent=2, ensure_ascii=False))

if __name__ == "__main__":
    asyncio.run(demo_openhands_verification())
