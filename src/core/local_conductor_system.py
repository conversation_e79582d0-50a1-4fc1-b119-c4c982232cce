# -*- coding: utf-8 -*-
"""
本地指挥系统 (Local Conductor System)
使用Ollama本地模型作为"指挥"，协调整个炼妖壶系统

核心理念：
- 指挥 (Ollama本地模型): 持续运行，实时决策，成本极低
- 交响乐团 (OpenRouter): 精彩演出，深度分析，按需调用
- RAG知识库: 历史经验，策略积累，持续学习

架构思想：
四梁八柱 (OpenRouter) + 本地指挥 (Ollama) + RAG知识库 = 完美的AI交响乐

作者：太公心易BI系统
版本：v2.1 Conductor
"""

import asyncio
import json
import logging
import os
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
import aiohttp
from pathlib import Path

# RAG相关
try:
    import chromadb
    from chromadb.config import Settings
    CHROMADB_AVAILABLE = True
except ImportError:
    CHROMADB_AVAILABLE = False

# Ollama客户端
try:
    import ollama
    OLLAMA_AVAILABLE = True
except ImportError:
    OLLAMA_AVAILABLE = False


@dataclass
class ConductorDecision:
    """指挥决策"""
    decision_type: str  # filter, trigger, escalate, ignore
    confidence: float  # 0-1
    reasoning: str
    suggested_action: str
    priority: int  # 1-5
    estimated_cost: float  # 预估的API调用成本
    timestamp: datetime


@dataclass
class EventAssessment:
    """事件评估"""
    event_id: str
    title: str
    description: str
    initial_score: float
    conductor_score: float
    decision: ConductorDecision
    rag_context: List[str]  # 相关历史经验
    processing_time: float


class LocalRAGSystem:
    """本地RAG知识库系统"""
    
    def __init__(self, data_dir: str = "./data/rag"):
        self.data_dir = Path(data_dir)
        self.data_dir.mkdir(parents=True, exist_ok=True)
        self.logger = logging.getLogger('LocalRAG')
        
        # 初始化向量数据库
        if CHROMADB_AVAILABLE:
            self.chroma_client = chromadb.PersistentClient(path=str(self.data_dir / "chroma"))
            
            # 创建不同类型的知识库
            self.collections = {
                "market_events": self._get_or_create_collection("market_events"),
                "decision_patterns": self._get_or_create_collection("decision_patterns"),
                "strategy_knowledge": self._get_or_create_collection("strategy_knowledge"),
                "risk_cases": self._get_or_create_collection("risk_cases")
            }
        else:
            self.logger.warning("ChromaDB不可用，使用简化存储")
            self.collections = {}
    
    def _get_or_create_collection(self, name: str):
        """获取或创建集合"""
        try:
            return self.chroma_client.get_collection(name)
        except:
            return self.chroma_client.create_collection(name)
    
    def add_market_event(self, event_title: str, event_desc: str, 
                        decision: str, outcome: str, lessons: str):
        """添加市场事件经验"""
        if not CHROMADB_AVAILABLE:
            return
        
        try:
            doc_id = f"event_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            
            self.collections["market_events"].add(
                documents=[f"{event_title}: {event_desc}"],
                metadatas=[{
                    "decision": decision,
                    "outcome": outcome,
                    "lessons": lessons,
                    "timestamp": datetime.now().isoformat()
                }],
                ids=[doc_id]
            )
            
            self.logger.info(f"添加市场事件经验: {event_title[:30]}...")
            
        except Exception as e:
            self.logger.error(f"添加市场事件失败: {e}")
    
    def query_similar_events(self, query: str, n_results: int = 3) -> List[Dict]:
        """查询相似事件"""
        if not CHROMADB_AVAILABLE:
            return []
        
        try:
            results = self.collections["market_events"].query(
                query_texts=[query],
                n_results=n_results,
                include=["documents", "metadatas", "distances"]
            )
            
            similar_events = []
            for i in range(len(results["documents"][0])):
                similar_events.append({
                    "document": results["documents"][0][i],
                    "decision": results["metadatas"][0][i]["decision"],
                    "outcome": results["metadatas"][0][i]["outcome"],
                    "lessons": results["metadatas"][0][i]["lessons"],
                    "similarity": 1 - results["distances"][0][i]
                })
            
            return similar_events
            
        except Exception as e:
            self.logger.error(f"查询相似事件失败: {e}")
            return []
    
    def add_decision_pattern(self, pattern_name: str, conditions: str, 
                           action: str, success_rate: float):
        """添加决策模式"""
        if not CHROMADB_AVAILABLE:
            return
        
        try:
            doc_id = f"pattern_{pattern_name}_{datetime.now().strftime('%Y%m%d')}"
            
            self.collections["decision_patterns"].add(
                documents=[f"{pattern_name}: {conditions}"],
                metadatas=[{
                    "action": action,
                    "success_rate": success_rate,
                    "timestamp": datetime.now().isoformat()
                }],
                ids=[doc_id]
            )
            
        except Exception as e:
            self.logger.error(f"添加决策模式失败: {e}")
    
    def get_decision_context(self, event_desc: str) -> str:
        """获取决策上下文"""
        similar_events = self.query_similar_events(event_desc, 3)
        
        if not similar_events:
            return "无相关历史经验"
        
        context = "相关历史经验:\n"
        for i, event in enumerate(similar_events, 1):
            context += f"{i}. {event['document'][:100]}...\n"
            context += f"   决策: {event['decision']}\n"
            context += f"   结果: {event['outcome']}\n"
            context += f"   教训: {event['lessons']}\n\n"
        
        return context


class LocalConductor:
    """本地指挥系统 - 使用Ollama实现零停机架构"""

    def __init__(self, model_name: str = "llama3.2:3b", rag_system: LocalRAGSystem = None):
        self.model_name = model_name
        self.rag_system = rag_system or LocalRAGSystem()
        self.logger = logging.getLogger('LocalConductor')

        # 检查Ollama可用性
        self.ollama_available = self._check_ollama()

        # 零停机配置
        self.zero_downtime_config = {
            "heroku_endpoints": [
                "https://cauldron-main.herokuapp.com",
                "https://cauldron-backup.herokuapp.com"
            ],
            "health_check_interval": 30,  # 秒
            "failover_threshold": 3,      # 连续失败次数
            "circuit_breaker_timeout": 300  # 熔断器超时（秒）
        }

        # 云端服务状态
        self.cloud_services = {
            "primary": {"url": self.zero_downtime_config["heroku_endpoints"][0], "status": "healthy", "failures": 0},
            "backup": {"url": self.zero_downtime_config["heroku_endpoints"][1], "status": "healthy", "failures": 0}
        }

        # 决策统计
        self.decision_stats = {
            "total_decisions": 0,
            "filtered_events": 0,
            "triggered_debates": 0,
            "escalated_events": 0,
            "ignored_events": 0,
            "avg_processing_time": 0.0,
            "cost_saved": 0.0,  # 通过本地过滤节省的API成本
            "uptime_percentage": 100.0,  # 系统可用性
            "local_decisions": 0,        # 本地处理的决策数
            "cloud_decisions": 0         # 云端处理的决策数
        }

        # 成本估算 (美元)
        self.api_costs = {
            "openrouter_call": 0.002,  # 每次调用约0.002美元
            "full_debate": 0.05,       # 完整辩论约0.05美元
            "simple_analysis": 0.01,   # 简单分析约0.01美元
            "local_processing": 0.0    # 本地处理完全免费
        }
    
    def _check_ollama(self) -> bool:
        """检查Ollama是否可用"""
        if not OLLAMA_AVAILABLE:
            self.logger.warning("Ollama库未安装")
            return False
        
        try:
            # 尝试连接Ollama
            models = ollama.list()
            available_models = [model['name'] for model in models['models']]
            
            if self.model_name not in available_models:
                self.logger.warning(f"模型 {self.model_name} 不可用，可用模型: {available_models}")
                # 尝试使用第一个可用模型
                if available_models:
                    self.model_name = available_models[0]
                    self.logger.info(f"切换到模型: {self.model_name}")
                else:
                    return False
            
            self.logger.info(f"Ollama可用，使用模型: {self.model_name}")
            return True
            
        except Exception as e:
            self.logger.error(f"Ollama连接失败: {e}")
            return False
    
    async def assess_event(self, event_title: str, event_desc: str, 
                          initial_score: float) -> EventAssessment:
        """评估事件是否需要触发完整分析"""
        start_time = datetime.now()
        
        # 获取RAG上下文
        rag_context_str = self.rag_system.get_decision_context(event_desc)
        rag_context = rag_context_str.split('\n')[:10]  # 限制上下文长度
        
        # 生成决策
        decision = await self._make_conductor_decision(
            event_title, event_desc, initial_score, rag_context_str
        )
        
        # 计算处理时间
        processing_time = (datetime.now() - start_time).total_seconds()
        
        # 更新统计
        self._update_stats(decision, processing_time)
        
        # 创建评估结果
        assessment = EventAssessment(
            event_id=f"event_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
            title=event_title,
            description=event_desc,
            initial_score=initial_score,
            conductor_score=decision.confidence * 100,
            decision=decision,
            rag_context=rag_context,
            processing_time=processing_time
        )
        
        return assessment
    
    async def _make_conductor_decision(self, title: str, desc: str, 
                                     initial_score: float, rag_context: str) -> ConductorDecision:
        """使用Ollama做出指挥决策"""
        
        if not self.ollama_available:
            # 备用决策逻辑
            return self._fallback_decision(initial_score)
        
        try:
            prompt = f"""你是炼妖壶系统的本地指挥，负责决定是否触发昂贵的云端AI分析。

事件信息:
标题: {title}
描述: {desc}
初始评分: {initial_score}/100

历史经验:
{rag_context}

你的任务是决定如何处理这个事件:
1. FILTER - 过滤掉，不值得分析 (评分<60)
2. TRIGGER - 触发完整的稷下学宫辩论 (评分>80)
3. ESCALATE - 升级处理，需要特别关注 (评分>90)
4. IGNORE - 暂时忽略，等待更多信息

考虑因素:
- API调用成本 (完整辩论约$0.05)
- 事件的真实影响力
- 历史相似事件的结果
- 当前市场环境

请用JSON格式回复:
{
    "decision_type": "FILTER/TRIGGER/ESCALATE/IGNORE",
    "confidence": 0.85,
    "reasoning": "详细理由",
    "suggested_action": "具体建议",
    "priority": 3,
    "estimated_cost": 0.05
}"""

            response = ollama.generate(
                model=self.model_name,
                prompt=prompt,
                options={
                    "temperature": 0.3,
                    "top_p": 0.9,
                    "num_predict": 200
                }
            )
            
            # 解析响应
            response_text = response['response']
            
            # 尝试提取JSON
            try:
                # 查找JSON部分
                json_start = response_text.find('{')
                json_end = response_text.rfind('}') + 1
                
                if json_start >= 0 and json_end > json_start:
                    json_str = response_text[json_start:json_end]
                    decision_data = json.loads(json_str)
                    
                    return ConductorDecision(
                        decision_type=decision_data.get("decision_type", "FILTER"),
                        confidence=decision_data.get("confidence", 0.5),
                        reasoning=decision_data.get("reasoning", "Ollama分析"),
                        suggested_action=decision_data.get("suggested_action", "标准处理"),
                        priority=decision_data.get("priority", 3),
                        estimated_cost=decision_data.get("estimated_cost", 0.01),
                        timestamp=datetime.now()
                    )
                else:
                    raise ValueError("无法找到JSON格式")
                    
            except (json.JSONDecodeError, ValueError):
                # JSON解析失败，使用文本分析
                return self._parse_text_decision(response_text, initial_score)
                
        except Exception as e:
            self.logger.error(f"Ollama决策失败: {e}")
            return self._fallback_decision(initial_score)
    
    def _parse_text_decision(self, text: str, initial_score: float) -> ConductorDecision:
        """从文本中解析决策"""
        text_lower = text.lower()
        
        if "trigger" in text_lower or "触发" in text_lower:
            decision_type = "TRIGGER"
            confidence = 0.7
        elif "escalate" in text_lower or "升级" in text_lower:
            decision_type = "ESCALATE"
            confidence = 0.8
        elif "ignore" in text_lower or "忽略" in text_lower:
            decision_type = "IGNORE"
            confidence = 0.6
        else:
            decision_type = "FILTER"
            confidence = 0.5
        
        return ConductorDecision(
            decision_type=decision_type,
            confidence=confidence,
            reasoning=f"基于文本分析: {text[:100]}...",
            suggested_action="根据决策类型处理",
            priority=3,
            estimated_cost=self.api_costs.get("simple_analysis", 0.01),
            timestamp=datetime.now()
        )
    
    def _fallback_decision(self, initial_score: float) -> ConductorDecision:
        """备用决策逻辑"""
        if initial_score >= 90:
            decision_type = "ESCALATE"
            confidence = 0.9
            cost = self.api_costs["full_debate"]
        elif initial_score >= 75:
            decision_type = "TRIGGER"
            confidence = 0.8
            cost = self.api_costs["full_debate"]
        elif initial_score >= 50:
            decision_type = "IGNORE"
            confidence = 0.6
            cost = 0
        else:
            decision_type = "FILTER"
            confidence = 0.7
            cost = 0
        
        return ConductorDecision(
            decision_type=decision_type,
            confidence=confidence,
            reasoning=f"基于评分{initial_score}的备用决策",
            suggested_action="标准处理流程",
            priority=3,
            estimated_cost=cost,
            timestamp=datetime.now()
        )
    
    def _update_stats(self, decision: ConductorDecision, processing_time: float):
        """更新统计信息"""
        self.decision_stats["total_decisions"] += 1
        
        if decision.decision_type == "FILTER":
            self.decision_stats["filtered_events"] += 1
            # 过滤掉的事件节省了API成本
            self.decision_stats["cost_saved"] += self.api_costs["full_debate"]
        elif decision.decision_type == "TRIGGER":
            self.decision_stats["triggered_debates"] += 1
        elif decision.decision_type == "ESCALATE":
            self.decision_stats["escalated_events"] += 1
        else:
            self.decision_stats["ignored_events"] += 1
        
        # 更新平均处理时间
        total = self.decision_stats["total_decisions"]
        current_avg = self.decision_stats["avg_processing_time"]
        self.decision_stats["avg_processing_time"] = (
            (current_avg * (total - 1) + processing_time) / total
        )
    
    def get_performance_report(self) -> Dict[str, Any]:
        """获取性能报告"""
        total = self.decision_stats["total_decisions"]
        if total == 0:
            return {"message": "暂无决策数据"}
        
        return {
            "总决策数": total,
            "过滤事件": f"{self.decision_stats['filtered_events']} ({self.decision_stats['filtered_events']/total:.1%})",
            "触发辩论": f"{self.decision_stats['triggered_debates']} ({self.decision_stats['triggered_debates']/total:.1%})",
            "升级处理": f"{self.decision_stats['escalated_events']} ({self.decision_stats['escalated_events']/total:.1%})",
            "暂时忽略": f"{self.decision_stats['ignored_events']} ({self.decision_stats['ignored_events']/total:.1%})",
            "平均处理时间": f"{self.decision_stats['avg_processing_time']:.3f}秒",
            "节省成本": f"${self.decision_stats['cost_saved']:.3f}",
            "Ollama状态": "可用" if self.ollama_available else "不可用"
        }
    
    async def learn_from_outcome(self, assessment: EventAssessment,
                               actual_outcome: str, market_reaction: str):
        """从结果中学习"""
        # 添加到RAG知识库
        self.rag_system.add_market_event(
            assessment.title,
            assessment.description,
            assessment.decision.decision_type,
            actual_outcome,
            f"市场反应: {market_reaction}"
        )

        self.logger.info(f"学习完成: {assessment.title[:30]}... -> {actual_outcome}")

    async def health_check_cloud_services(self):
        """健康检查云端服务"""
        for service_name, service_info in self.cloud_services.items():
            try:
                async with aiohttp.ClientSession() as session:
                    async with session.get(
                        f"{service_info['url']}/health",
                        timeout=aiohttp.ClientTimeout(total=10)
                    ) as response:
                        if response.status == 200:
                            service_info['status'] = 'healthy'
                            service_info['failures'] = 0
                            self.logger.debug(f"✅ {service_name} 服务健康")
                        else:
                            service_info['failures'] += 1
                            if service_info['failures'] >= self.zero_downtime_config['failover_threshold']:
                                service_info['status'] = 'unhealthy'
                            self.logger.warning(f"⚠️ {service_name} 服务响应异常: {response.status}")
            except Exception as e:
                service_info['failures'] += 1
                if service_info['failures'] >= self.zero_downtime_config['failover_threshold']:
                    service_info['status'] = 'unhealthy'
                self.logger.error(f"❌ {service_name} 服务健康检查失败: {e}")

    async def get_healthy_cloud_endpoint(self) -> Optional[str]:
        """获取健康的云端端点"""
        # 优先使用主服务
        if self.cloud_services['primary']['status'] == 'healthy':
            return self.cloud_services['primary']['url']

        # 备用服务
        if self.cloud_services['backup']['status'] == 'healthy':
            self.logger.info("🔄 切换到备用服务")
            return self.cloud_services['backup']['url']

        # 都不健康，返回None，完全依赖本地处理
        self.logger.warning("⚠️ 所有云端服务不可用，启用纯本地模式")
        return None

    async def trigger_cloud_analysis(self, assessment: EventAssessment) -> Optional[Dict]:
        """触发云端分析（带故障转移）"""
        endpoint = await self.get_healthy_cloud_endpoint()

        if not endpoint:
            # 云端服务不可用，使用本地增强分析
            return await self._enhanced_local_analysis(assessment)

        try:
            payload = {
                "event_title": assessment.title,
                "event_description": assessment.description,
                "impact_score": assessment.conductor_score,
                "rag_context": assessment.rag_context,
                "analysis_type": assessment.decision.decision_type
            }

            async with aiohttp.ClientSession() as session:
                async with session.post(
                    f"{endpoint}/api/analyze",
                    json=payload,
                    timeout=aiohttp.ClientTimeout(total=60)
                ) as response:
                    if response.status == 200:
                        result = await response.json()
                        self.decision_stats['cloud_decisions'] += 1
                        return result
                    else:
                        self.logger.error(f"云端分析失败: {response.status}")
                        return await self._enhanced_local_analysis(assessment)

        except Exception as e:
            self.logger.error(f"云端分析异常: {e}")
            return await self._enhanced_local_analysis(assessment)

    async def _enhanced_local_analysis(self, assessment: EventAssessment) -> Dict:
        """增强的本地分析（云端服务不可用时）"""
        self.decision_stats['local_decisions'] += 1

        if not self.ollama_available:
            return {
                "analysis": "云端服务不可用，本地模型也不可用，使用基础规则分析",
                "recommendation": "建议手动关注此事件",
                "confidence": 0.3,
                "source": "fallback_rules"
            }

        try:
            prompt = f"""作为炼妖壶系统的本地分析师，请对以下事件进行深度分析：

事件标题: {assessment.title}
事件描述: {assessment.description}
影响力评分: {assessment.conductor_score}

历史经验参考:
{chr(10).join(assessment.rag_context[:5])}

请提供:
1. 事件分析
2. 投资建议
3. 风险评估
4. 置信度(0-1)

请用中文回答，保持专业和客观。"""

            response = ollama.generate(
                model=self.model_name,
                prompt=prompt,
                options={'temperature': 0.3}
            )

            return {
                "analysis": response['response'],
                "recommendation": "基于本地模型分析",
                "confidence": 0.7,
                "source": "local_ollama"
            }

        except Exception as e:
            self.logger.error(f"本地分析失败: {e}")
            return {
                "analysis": f"本地分析异常: {e}",
                "recommendation": "建议手动分析",
                "confidence": 0.2,
                "source": "error_fallback"
            }


class ConductorIntegratedSystem:
    """指挥集成系统 - 将本地指挥与现有系统集成"""
    
    def __init__(self, enhanced_cauldron_system, conductor: LocalConductor = None):
        self.enhanced_system = enhanced_cauldron_system
        self.conductor = conductor or LocalConductor()
        self.logger = logging.getLogger('ConductorIntegrated')
        
        # 集成统计
        self.integration_stats = {
            "events_assessed": 0,
            "debates_triggered": 0,
            "events_filtered": 0,
            "cost_efficiency": 0.0
        }
    
    async def enhanced_monitoring_cycle(self) -> List[Dict[str, Any]]:
        """增强的监控周期 - 加入本地指挥"""
        self.logger.info("🎼 开始指挥增强监控周期")
        
        # 1. 获取原始事件
        raw_events = await self.enhanced_system.event_processor.rss_monitor.monitor_all_sources()
        
        if not raw_events:
            return []
        
        # 2. 本地指挥评估每个事件
        assessed_events = []
        for event in raw_events:
            assessment = await self.conductor.assess_event(
                event.title,
                event.description,
                event.impact_score
            )
            
            self.integration_stats["events_assessed"] += 1
            
            # 根据指挥决策处理
            if assessment.decision.decision_type in ["TRIGGER", "ESCALATE"]:
                assessed_events.append((event, assessment))
                self.integration_stats["debates_triggered"] += 1
            else:
                self.integration_stats["events_filtered"] += 1
                self.logger.info(f"🎯 指挥决策过滤事件: {event.title[:50]}...")
        
        # 3. 只对通过指挥筛选的事件进行完整处理
        results = []
        for event, assessment in assessed_events:
            try:
                # 使用原有系统处理
                result = await self.enhanced_system._process_single_event({
                    "event": event.__dict__
                })
                
                # 添加指挥信息
                result["conductor_assessment"] = {
                    "decision": assessment.decision.__dict__,
                    "processing_time": assessment.processing_time,
                    "rag_context_count": len(assessment.rag_context)
                }
                
                results.append(result)
                
            except Exception as e:
                self.logger.error(f"处理事件失败: {e}")
        
        # 4. 计算成本效率
        total_events = len(raw_events)
        processed_events = len(results)
        if total_events > 0:
            self.integration_stats["cost_efficiency"] = 1 - (processed_events / total_events)
        
        self.logger.info(f"🎼 指挥周期完成: {total_events}个事件 -> {processed_events}个处理")
        return results
    
    def get_conductor_status(self) -> Dict[str, Any]:
        """获取指挥系统状态"""
        return {
            "conductor_performance": self.conductor.get_performance_report(),
            "integration_stats": self.integration_stats,
            "ollama_available": self.conductor.ollama_available,
            "rag_collections": len(self.conductor.rag_system.collections),
            "cost_efficiency": f"{self.integration_stats['cost_efficiency']:.1%}"
        }


# 使用示例
async def main():
    """测试本地指挥系统"""
    print("🎼 测试本地指挥系统")
    
    # 创建RAG系统
    rag_system = LocalRAGSystem()
    
    # 添加一些示例经验
    rag_system.add_market_event(
        "美联储加息",
        "美联储宣布加息50个基点",
        "TRIGGER",
        "市场下跌3%",
        "加息事件通常导致短期下跌，但需要关注后续政策"
    )
    
    # 创建指挥系统
    conductor = LocalConductor(rag_system=rag_system)
    
    # 测试事件评估
    test_events = [
        ("美联储意外加息75个基点", "美联储在今日会议上意外宣布加息75个基点，远超市场预期的50个基点", 95),
        ("苹果发布新iPhone", "苹果公司今日发布了新款iPhone 16，搭载最新A18芯片", 60),
        ("某小公司财报", "XYZ公司发布Q3财报，营收同比增长5%", 30)
    ]
    
    for title, desc, score in test_events:
        print(f"\n--- 评估事件: {title} ---")
        assessment = await conductor.assess_event(title, desc, score)
        
        print(f"决策: {assessment.decision.decision_type}")
        print(f"信心: {assessment.decision.confidence:.2f}")
        print(f"理由: {assessment.decision.reasoning}")
        print(f"处理时间: {assessment.processing_time:.3f}秒")
    
    # 显示性能报告
    print("\n--- 性能报告 ---")
    report = conductor.get_performance_report()
    for key, value in report.items():
        print(f"{key}: {value}")


if __name__ == "__main__":
    asyncio.run(main())