# -*- coding: utf-8 -*-
"""
太公心易BI系统 - 股票分析引擎
集成八仙论道作为智能分析模块，对IB导入的股票数据进行AI分析
"""

import asyncio
import json
import os
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from pathlib import Path
from dataclasses import dataclass

# 导入八仙论道核心模块
try:
    # 检查是否在Streamlit环境中运行
    import streamlit as st
    # 在Streamlit环境中跳过jixia_academy导入以避免asyncio冲突
    JIXIA_AVAILABLE = False
    print("ℹ️  在Streamlit环境中运行，跳过八仙论道模块导入")
except ImportError:
    # 不在Streamlit环境中，尝试导入jixia_academy
    try:
        from jixia_academy.handoff_debate_enhanced import HandoffDebateSystem
        JIXIA_AVAILABLE = True
    except ImportError:
        JIXIA_AVAILABLE = False
        print("⚠️  八仙论道模块未找到，将使用模拟分析")

# 导入配置管理器和妖股识别器
from .config_manager import ConfigManager
from .demon_stock_detector import DemonStockDetector, DemonStockSignal, PriceAction

@dataclass
class StockData:
    """股票数据结构"""
    symbol: str
    price: float
    volume: int
    change: float
    change_percent: float
    timestamp: datetime
    market_cap: Optional[float] = None
    pe_ratio: Optional[float] = None
    technical_indicators: Optional[Dict] = None
    news_sentiment: Optional[Dict] = None
    # 妖股识别所需的历史数据
    historical_prices: Optional[List[Dict]] = None  # [{'open': x, 'high': x, 'low': x, 'close': x, 'volume': x, 'date': x}]
    fundamental_data: Optional[Dict] = None
    short_interest_data: Optional[Dict] = None

@dataclass
class MarketAtmosphere:
    """市场气氛数据结构"""
    overall_sentiment: str  # 'bullish', 'bearish', 'neutral'
    volatility_level: str   # 'high', 'medium', 'low'
    sector_rotation: Dict[str, float]
    fear_greed_index: float
    volume_trend: str
    technical_signals: Dict[str, Any]
    news_impact: Dict[str, Any]

@dataclass
class InvestmentAdvice:
    """投资建议结构"""
    recommendation: str  # 'buy', 'sell', 'hold'
    confidence_level: float  # 0-1
    target_price: Optional[float]
    stop_loss: Optional[float]
    time_horizon: str  # 'short', 'medium', 'long'
    reasoning: str
    risk_assessment: str
    debate_summary: str
    participants: List[str]
    timestamp: datetime
    # 妖股识别结果
    demon_signal: Optional[DemonStockSignal] = None

class StockAnalysisEngine:
    """股票分析引擎 - 集成八仙论道AI分析"""
    
    def __init__(self):
        self.config_manager = ConfigManager()
        self.config = self.config_manager.get_all_config()
        self.logger = self._setup_logger()
        
        # 初始化八仙论道系统
        if JIXIA_AVAILABLE and self.config['jixia_academy']['enabled']:
            self.debate_system = HandoffDebateSystem()
            self.ai_analysis_enabled = True
            self.logger.info("✅ 八仙论道AI分析引擎已启用")
        else:
            self.debate_system = None
            self.ai_analysis_enabled = False
            self.logger.warning("⚠️  八仙论道AI分析引擎未启用，将使用基础分析")
        
        # 初始化妖股识别器
        self.demon_detector = DemonStockDetector()
        self.logger.info("🔮 妖股识别引擎已启用")
    
    def _setup_logger(self) -> logging.Logger:
        """设置日志记录器"""
        logger = logging.getLogger('StockAnalysisEngine')
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        
        return logger
    
    async def analyze_stock(self, stock_data: StockData) -> InvestmentAdvice:
        """分析单只股票"""
        self.logger.info(f"🔍 开始分析股票: {stock_data.symbol}")
        
        # 1. 计算市场气氛
        market_atmosphere = await self._calculate_market_atmosphere(stock_data)
        
        # 2. 妖股识别分析
        demon_signal = await self._analyze_demon_stock(stock_data)
        
        # 3. 如果启用AI分析，触发八仙论道
        if self.ai_analysis_enabled:
            advice = await self._ai_debate_analysis(stock_data, market_atmosphere)
        else:
            advice = await self._basic_analysis(stock_data, market_atmosphere)
        
        # 4. 将妖股识别结果添加到投资建议中
        advice.demon_signal = demon_signal
        
        # 5. 根据妖股识别结果调整建议
        advice = self._adjust_advice_for_demon_stock(advice, demon_signal)
        
        self.logger.info(f"✅ 股票分析完成: {stock_data.symbol} - {advice.recommendation} (妖股等级: {demon_signal.level.value})")
        return advice
    
    async def analyze_market_batch(self, stocks: List[StockData]) -> Dict[str, InvestmentAdvice]:
        """批量分析市场股票"""
        self.logger.info(f"📊 开始批量分析 {len(stocks)} 只股票")
        
        results = {}
        for stock in stocks:
            try:
                advice = await self.analyze_stock(stock)
                results[stock.symbol] = advice
            except Exception as e:
                self.logger.error(f"❌ 分析股票 {stock.symbol} 时出错: {e}")
                continue
        
        self.logger.info(f"✅ 批量分析完成，成功分析 {len(results)} 只股票")
        return results
    
    async def _calculate_market_atmosphere(self, stock_data: StockData) -> MarketAtmosphere:
        """计算市场气氛"""
        # 基于股票数据计算市场情绪
        sentiment = 'neutral'
        if stock_data.change_percent > 2:
            sentiment = 'bullish'
        elif stock_data.change_percent < -2:
            sentiment = 'bearish'
        
        # 计算波动性
        volatility = 'medium'
        if abs(stock_data.change_percent) > 5:
            volatility = 'high'
        elif abs(stock_data.change_percent) < 1:
            volatility = 'low'
        
        # 模拟恐惧贪婪指数
        fear_greed = 50 + (stock_data.change_percent * 5)
        fear_greed = max(0, min(100, fear_greed))
        
        return MarketAtmosphere(
            overall_sentiment=sentiment,
            volatility_level=volatility,
            sector_rotation={},
            fear_greed_index=fear_greed,
            volume_trend='normal',
            technical_signals={},
            news_impact={}
        )
    
    async def _ai_debate_analysis(self, stock_data: StockData, atmosphere: MarketAtmosphere) -> InvestmentAdvice:
        """使用八仙论道进行AI分析"""
        self.logger.info(f"🎭 启动八仙论道分析: {stock_data.symbol}")
        
        # 构建分析主题
        topic = f"""请分析股票 {stock_data.symbol} 的投资价值：
        
当前价格: ${stock_data.price:.2f}
涨跌幅: {stock_data.change_percent:.2f}%
成交量: {stock_data.volume:,}
市场气氛: {atmosphere.overall_sentiment}
波动性: {atmosphere.volatility_level}
恐惧贪婪指数: {atmosphere.fear_greed_index:.1f}

请从技术分析、基本面分析、市场情绪、风险评估等角度进行深入讨论，
最终给出明确的投资建议（买入/卖出/持有）及理由。"""
        
        try:
            # 启动八仙论道辩论
            debate_result = await self.debate_system.start_debate(
                topic=topic,
                max_rounds=6,  # 适合股票分析的轮数
                context={"stock_data": stock_data.__dict__, "atmosphere": atmosphere.__dict__}
            )
            
            # 解析辩论结果
            recommendation = self._parse_debate_recommendation(debate_result)
            
            return InvestmentAdvice(
                recommendation=recommendation['action'],
                confidence_level=recommendation['confidence'],
                target_price=recommendation.get('target_price'),
                stop_loss=recommendation.get('stop_loss'),
                time_horizon=recommendation.get('time_horizon', 'medium'),
                reasoning=recommendation['reasoning'],
                risk_assessment=recommendation['risk_assessment'],
                debate_summary=debate_result.get('summary', ''),
                participants=debate_result.get('participants', []),
                timestamp=datetime.now()
            )
            
        except Exception as e:
            self.logger.error(f"❌ AI分析失败: {e}")
            # 降级到基础分析
            return await self._basic_analysis(stock_data, atmosphere)
    
    async def _basic_analysis(self, stock_data: StockData, atmosphere: MarketAtmosphere) -> InvestmentAdvice:
        """基础分析（当AI不可用时）"""
        self.logger.info(f"📈 使用基础分析: {stock_data.symbol}")
        
        # 简单的技术分析逻辑
        if stock_data.change_percent > 3 and atmosphere.overall_sentiment == 'bullish':
            recommendation = 'buy'
            confidence = 0.7
            reasoning = "股价强势上涨，市场情绪乐观"
        elif stock_data.change_percent < -3 and atmosphere.overall_sentiment == 'bearish':
            recommendation = 'sell'
            confidence = 0.6
            reasoning = "股价大幅下跌，市场情绪悲观"
        else:
            recommendation = 'hold'
            confidence = 0.5
            reasoning = "股价波动正常，建议观望"
        
        return InvestmentAdvice(
            recommendation=recommendation,
            confidence_level=confidence,
            target_price=None,
            stop_loss=None,
            time_horizon='short',
            reasoning=reasoning,
            risk_assessment='中等风险',
            debate_summary='基础技术分析',
            participants=['技术分析模块'],
            timestamp=datetime.now()
        )
    
    def _parse_debate_recommendation(self, debate_result: Dict) -> Dict:
        """解析八仙论道的辩论结果"""
        # 从辩论结果中提取投资建议
        summary = debate_result.get('summary', '')
        
        # 简单的关键词匹配（实际应用中可以使用更复杂的NLP）
        if '买入' in summary or 'buy' in summary.lower():
            action = 'buy'
        elif '卖出' in summary or 'sell' in summary.lower():
            action = 'sell'
        else:
            action = 'hold'
        
        # 提取置信度（模拟）
        confidence = 0.8 if '强烈' in summary else 0.6
        
        return {
            'action': action,
            'confidence': confidence,
            'reasoning': summary,
            'risk_assessment': '基于AI辩论的风险评估'
        }
    
    async def generate_daily_report(self, stocks: List[StockData]) -> Dict[str, Any]:
        """生成每日投资晨会报告"""
        self.logger.info("📋 生成每日投资晨会报告")
        
        # 批量分析股票
        analysis_results = await self.analyze_market_batch(stocks)
        
        # 统计分析结果
        buy_count = sum(1 for advice in analysis_results.values() if advice.recommendation == 'buy')
        sell_count = sum(1 for advice in analysis_results.values() if advice.recommendation == 'sell')
        hold_count = sum(1 for advice in analysis_results.values() if advice.recommendation == 'hold')
        
        # 找出高置信度推荐
        high_confidence = [
            (symbol, advice) for symbol, advice in analysis_results.items() 
            if advice.confidence_level > 0.7
        ]
        
        report = {
            'date': datetime.now().strftime('%Y-%m-%d'),
            'total_analyzed': len(stocks),
            'recommendations': {
                'buy': buy_count,
                'sell': sell_count,
                'hold': hold_count
            },
            'high_confidence_picks': high_confidence,
            'market_overview': self._generate_market_overview(analysis_results),
            'ai_analysis_enabled': self.ai_analysis_enabled,
            'generated_at': datetime.now().isoformat()
        }
        
        # 保存报告
        await self._save_daily_report(report)
        
        return report
    
    def _generate_market_overview(self, analysis_results: Dict[str, InvestmentAdvice]) -> str:
        """生成市场概览"""
        if not analysis_results:
            return "今日无分析数据"
        
        buy_ratio = sum(1 for advice in analysis_results.values() if advice.recommendation == 'buy') / len(analysis_results)
        
        if buy_ratio > 0.6:
            return "市场整体偏乐观，多数股票获得买入建议"
        elif buy_ratio < 0.3:
            return "市场整体偏谨慎，建议控制风险"
        else:
            return "市场情绪中性，建议精选个股"
    
    async def _save_daily_report(self, report: Dict[str, Any]):
        """保存每日报告"""
        reports_dir = Path(__file__).parent / "reports"
        reports_dir.mkdir(exist_ok=True)
        
        filename = f"daily_report_{report['date']}.json"
        filepath = reports_dir / filename
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False, default=str)
        
        self.logger.info(f"📄 每日报告已保存: {filepath}")
    
    async def _analyze_demon_stock(self, stock_data: StockData) -> DemonStockSignal:
        """分析妖股潜力"""
        try:
            # 转换历史价格数据为PriceAction格式
            price_actions = []
            if stock_data.historical_prices:
                for price_data in stock_data.historical_prices:
                    action = PriceAction(
                        open_price=price_data.get('open', stock_data.price),
                        high_price=price_data.get('high', stock_data.price),
                        low_price=price_data.get('low', stock_data.price),
                        close_price=price_data.get('close', stock_data.price),
                        volume=price_data.get('volume', stock_data.volume),
                        timestamp=price_data.get('date', stock_data.timestamp)
                    )
                    price_actions.append(action)
            else:
                # 如果没有历史数据，创建单个数据点
                action = PriceAction(
                    open_price=stock_data.price * 0.99,  # 模拟开盘价
                    high_price=stock_data.price * 1.02,  # 模拟最高价
                    low_price=stock_data.price * 0.98,   # 模拟最低价
                    close_price=stock_data.price,
                    volume=stock_data.volume,
                    timestamp=stock_data.timestamp
                )
                price_actions = [action] * 30  # 创建30天的模拟数据
            
            # 调用妖股识别器
            demon_signal = self.demon_detector.analyze_demon_potential(
                symbol=stock_data.symbol,
                price_actions=price_actions,
                fundamental_data=stock_data.fundamental_data,
                short_interest_data=stock_data.short_interest_data
            )
            
            return demon_signal
            
        except Exception as e:
            self.logger.error(f"❌ 妖股识别失败 {stock_data.symbol}: {e}")
            # 返回默认的正常股票信号
            from .demon_stock_detector import DemonStockLevel
            return DemonStockSignal(
                symbol=stock_data.symbol,
                level=DemonStockLevel.NORMAL,
                confidence=0.0,
                cpa_macd_value=0.0,
                dcp_ratio=0.0,
                short_interest_trend='unknown',
                fundamental_score=0.0,
                technical_score=0.0,
                reasoning="妖股识别失败",
                timestamp=datetime.now(),
                risk_warning="数据不足"
            )
    
    def _adjust_advice_for_demon_stock(self, advice: InvestmentAdvice, demon_signal: DemonStockSignal) -> InvestmentAdvice:
        """根据妖股识别结果调整投资建议"""
        from .demon_stock_detector import DemonStockLevel
        
        # 根据妖股等级调整建议
        if demon_signal.level == DemonStockLevel.SUPER_DEMON:
            # 超级妖股：高风险高收益，建议小仓位参与
            if advice.recommendation == 'buy':
                advice.reasoning += f" | 🔮 超级妖股识别：{demon_signal.reasoning}"
                advice.risk_assessment = "极高风险：超级妖股波动巨大，建议1-2%仓位试探"
                advice.confidence_level = min(advice.confidence_level, 0.7)  # 降低置信度
            else:
                advice.reasoning += f" | 🔮 超级妖股但技术面不佳，建议观望"
        
        elif demon_signal.level == DemonStockLevel.CONFIRMED:
            # 确认妖股：谨慎参与
            advice.reasoning += f" | 🔮 确认妖股：{demon_signal.reasoning}"
            advice.risk_assessment = "高风险：确认妖股，建议严格止损"
            if advice.recommendation == 'buy':
                advice.confidence_level = min(advice.confidence_level, 0.8)
        
        elif demon_signal.level == DemonStockLevel.EMERGING:
            # 新兴妖股：密切关注
            advice.reasoning += f" | 🔮 新兴妖股：{demon_signal.reasoning}"
            advice.risk_assessment = "中高风险：新兴妖股特征，建议小仓位关注"
        
        elif demon_signal.level == DemonStockLevel.POTENTIAL:
            # 潜在妖股：保持关注
            advice.reasoning += f" | 🔮 潜在妖股：{demon_signal.reasoning}"
        
        # 添加妖股风险提示
        if demon_signal.level != DemonStockLevel.NORMAL:
            advice.risk_assessment += f" | {demon_signal.risk_warning}"
        
        return advice

# 示例使用
async def main():
    """示例主函数"""
    engine = StockAnalysisEngine()
    
    # 模拟股票数据
    sample_stocks = [
        StockData(
            symbol="AAPL",
            price=150.25,
            volume=1000000,
            change=2.50,
            change_percent=1.69,
            timestamp=datetime.now()
        ),
        StockData(
            symbol="TSLA",
            price=200.80,
            volume=2000000,
            change=-5.20,
            change_percent=-2.52,
            timestamp=datetime.now()
        )
    ]
    
    # 生成每日报告
    report = await engine.generate_daily_report(sample_stocks)
    print("📊 每日投资晨会报告:")
    print(json.dumps(report, indent=2, ensure_ascii=False))

if __name__ == "__main__":
    asyncio.run(main())