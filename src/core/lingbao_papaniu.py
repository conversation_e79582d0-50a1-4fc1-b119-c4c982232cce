# -*- coding: utf-8 -*-
"""
灵宝道君的爬爬牛验证系统
基于十二龙子类的具体实现

作者：太公心易BI系统  
版本：v1.0 - 爬爬牛实例
"""

import asyncio
import logging
from typing import Dict, List, Any, Optional
from datetime import datetime
from dataclasses import dataclass

from ..dragons.dragon_base import DragonFactory, DragonResult, DragonBase

logger = logging.getLogger(__name__)

@dataclass
class VerificationTask:
    """验证任务"""
    task_id: str
    claim: str
    keywords: List[str]
    priority: int = 1
    timestamp: datetime = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now()

@dataclass 
class VerificationReport:
    """验证报告"""
    task_id: str
    original_claim: str
    dragon_results: Dict[str, DragonResult]
    final_confidence: float
    recommendation: str
    risk_level: str
    summary: str
    timestamp: datetime
    execution_time: float

class LingbaoPaPaNiu:
    """灵宝道君的爬爬牛验证系统
    
    基于十二龙子类架构的具体实现：
    - 从dragon_base.py的类定义派生具体对象
    - 协调各个龙子实例完成验证任务
    - 生成综合验证报告
    """
    
    def __init__(self, n8n_webhook_url: str = None):
        self.n8n_webhook_url = n8n_webhook_url
        self.dragons = {}
        self.verification_history = []
        
        # 初始化十二龙子实例
        self._initialize_dragons()
        
        logger.info("🔮 灵宝道君的爬爬牛验证系统初始化完成")
        logger.info(f"🐉 已加载{len(self.dragons)}个龙子实例")
    
    def _initialize_dragons(self):
        """初始化十二龙子实例"""
        try:
            # 从工厂类创建所有可用的龙子实例
            self.dragons = DragonFactory.create_all_dragons()
            
            logger.info("🐉 十二龙子实例化完成:")
            for name, dragon in self.dragons.items():
                logger.info(f"  {name}: {dragon.role} - {dragon.specialty}")
                
        except Exception as e:
            logger.error(f"❌ 龙子初始化失败: {e}")
            self.dragons = {}
    
    async def verify_investment_claim(self, claim: str, keywords: List[str] = None) -> VerificationReport:
        """验证投资论断
        
        Args:
            claim: 要验证的投资论断
            keywords: 关键词列表（可选）
            
        Returns:
            VerificationReport: 验证报告
        """
        start_time = datetime.now()
        task_id = f"verify_{int(start_time.timestamp())}"
        
        logger.info(f"🔮 灵宝道君开始验证: {claim}")
        
        # 创建验证任务
        task = VerificationTask(
            task_id=task_id,
            claim=claim,
            keywords=keywords or self._extract_keywords(claim),
            timestamp=start_time
        )
        
        # 执行龙子验证
        dragon_results = await self._execute_dragon_verification(task)
        
        # 计算最终置信度
        final_confidence = self._calculate_final_confidence(dragon_results)
        
        # 生成建议和风险评估
        recommendation, risk_level = self._generate_recommendation(final_confidence, dragon_results)
        
        # 生成摘要
        summary = self._generate_summary(dragon_results, final_confidence)
        
        # 创建验证报告
        execution_time = (datetime.now() - start_time).total_seconds()
        
        report = VerificationReport(
            task_id=task_id,
            original_claim=claim,
            dragon_results=dragon_results,
            final_confidence=final_confidence,
            recommendation=recommendation,
            risk_level=risk_level,
            summary=summary,
            timestamp=datetime.now(),
            execution_time=execution_time
        )
        
        # 保存到历史记录
        self.verification_history.append(report)
        
        logger.info(f"✅ 验证完成，置信度: {final_confidence:.2f}, 建议: {recommendation}")
        return report
    
    async def _execute_dragon_verification(self, task: VerificationTask) -> Dict[str, DragonResult]:
        """执行龙子验证"""
        dragon_results = {}
        
        # 准备任务数据
        task_data = {
            "claim": task.claim,
            "keywords": task.keywords,
            "task_id": task.task_id
        }
        
        # 第一阶段：信息收集龙子（并行执行）
        collection_dragons = ["囚牛", "睚眦", "狻猊"]
        collection_results = await self._execute_dragons_parallel(collection_dragons, task_data)
        dragon_results.update(collection_results)
        
        # 第二阶段：数据处理龙子（基于第一阶段结果）
        # 注意：这里需要等待其他龙子类的实现
        # processing_dragons = ["蒲牢", "嘲风", "狴犴"]
        # 暂时跳过，等待其他龙子类实现
        
        return dragon_results
    
    async def _execute_dragons_parallel(self, dragon_names: List[str], task_data: Dict[str, Any]) -> Dict[str, DragonResult]:
        """并行执行指定的龙子"""
        results = {}
        
        # 创建异步任务
        tasks = []
        for dragon_name in dragon_names:
            if dragon_name in self.dragons:
                dragon = self.dragons[dragon_name]
                task = asyncio.create_task(self._execute_single_dragon(dragon, task_data))
                tasks.append((dragon_name, task))
        
        # 等待所有任务完成
        for dragon_name, task in tasks:
            try:
                result = await task
                results[dragon_name] = result
                logger.info(f"🐉 {dragon_name}执行完成")
            except Exception as e:
                logger.error(f"❌ {dragon_name}执行失败: {e}")
                # 创建失败结果
                results[dragon_name] = DragonResult(
                    dragon_name=dragon_name,
                    role="未知",
                    specialty="未知", 
                    confidence=0.0,
                    data={},
                    timestamp=datetime.now(),
                    success=False,
                    error_message=str(e)
                )
        
        return results
    
    async def _execute_single_dragon(self, dragon: DragonBase, task_data: Dict[str, Any]) -> DragonResult:
        """执行单个龙子任务"""
        # 在异步环境中执行同步的龙子方法
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(None, dragon.execute, task_data)
    
    def _extract_keywords(self, claim: str) -> List[str]:
        """从论断中提取关键词"""
        # 简单的关键词提取
        import re
        
        # 移除标点符号并分词
        words = re.findall(r'[\u4e00-\u9fff]+|[a-zA-Z]+', claim)
        
        # 过滤短词和常见词
        stop_words = {'的', '是', '在', '和', '与', '将', '会', '可能', '应该'}
        keywords = [word for word in words if len(word) > 1 and word not in stop_words]
        
        return keywords[:5]  # 返回前5个关键词
    
    def _calculate_final_confidence(self, dragon_results: Dict[str, DragonResult]) -> float:
        """计算最终置信度"""
        if not dragon_results:
            return 0.0
        
        # 龙子权重配置
        dragon_weights = {
            "囚牛": 0.3,   # 基础搜索权重
            "睚眦": 0.4,   # 深度挖掘权重  
            "狻猊": 0.3,   # 权威验证权重
            # 其他龙子权重待添加
        }
        
        total_weighted_confidence = 0.0
        total_weight = 0.0
        
        for dragon_name, result in dragon_results.items():
            if result.success:
                weight = dragon_weights.get(dragon_name, 0.1)
                total_weighted_confidence += result.confidence * weight
                total_weight += weight
        
        if total_weight == 0:
            return 0.0
        
        return total_weighted_confidence / total_weight
    
    def _generate_recommendation(self, confidence: float, dragon_results: Dict[str, DragonResult]) -> tuple[str, str]:
        """生成建议和风险评估"""
        
        # 基于置信度生成建议
        if confidence >= 0.8:
            recommendation = "STRONG_APPROVE"
            risk_level = "LOW"
        elif confidence >= 0.6:
            recommendation = "APPROVE" 
            risk_level = "MEDIUM"
        elif confidence >= 0.4:
            recommendation = "REVIEW_REQUIRED"
            risk_level = "MEDIUM"
        else:
            recommendation = "REJECT"
            risk_level = "HIGH"
        
        # 检查龙子执行失败情况
        failed_dragons = [name for name, result in dragon_results.items() if not result.success]
        if len(failed_dragons) > len(dragon_results) / 2:
            risk_level = "HIGH"
            if recommendation in ["STRONG_APPROVE", "APPROVE"]:
                recommendation = "REVIEW_REQUIRED"
        
        return recommendation, risk_level
    
    def _generate_summary(self, dragon_results: Dict[str, DragonResult], confidence: float) -> str:
        """生成验证摘要"""
        successful_dragons = [name for name, result in dragon_results.items() if result.success]
        failed_dragons = [name for name, result in dragon_results.items() if not result.success]
        
        summary_parts = [
            f"十二龙子协同验证完成",
            f"成功执行: {', '.join(successful_dragons)}" if successful_dragons else "无成功执行",
            f"执行失败: {', '.join(failed_dragons)}" if failed_dragons else "全部成功",
            f"最终置信度: {confidence:.2f}"
        ]
        
        return " | ".join(summary_parts)
    
    def get_dragon_status(self) -> Dict[str, Dict[str, Any]]:
        """获取所有龙子状态"""
        status = {}
        for name, dragon in self.dragons.items():
            status[name] = dragon.get_status()
        return status
    
    def get_verification_history(self, limit: int = 10) -> List[VerificationReport]:
        """获取验证历史"""
        return self.verification_history[-limit:]

# 使用示例
async def demo_papaniu_verification():
    """演示爬爬牛验证功能"""
    
    print("🔮 灵宝道君爬爬牛验证系统演示")
    print("=" * 60)
    
    # 创建爬爬牛实例
    papaniu = LingbaoPaPaNiu()
    
    # 测试验证任务
    test_claims = [
        "特斯拉Q4财报将超预期20%",
        "AI芯片市场需求在2025年将翻倍",
        "英伟达数据中心业务将保持高增长"
    ]
    
    for claim in test_claims:
        print(f"\n🎯 验证论断: {claim}")
        print("-" * 40)
        
        # 执行验证
        report = await papaniu.verify_investment_claim(claim)
        
        print(f"📊 验证结果:")
        print(f"  置信度: {report.final_confidence:.2f}")
        print(f"  建议: {report.recommendation}")
        print(f"  风险等级: {report.risk_level}")
        print(f"  执行时间: {report.execution_time:.2f}秒")
        print(f"  摘要: {report.summary}")
        
        # 显示龙子执行情况
        print(f"\n🐉 龙子执行情况:")
        for dragon_name, result in report.dragon_results.items():
            status = "✅" if result.success else "❌"
            print(f"  {status} {dragon_name}: {result.confidence:.2f}")
    
    # 显示龙子状态
    print(f"\n📋 龙子状态总览:")
    dragon_status = papaniu.get_dragon_status()
    for name, status in dragon_status.items():
        print(f"  {name}: {status['role']} - 执行{status['execution_count']}次")

if __name__ == "__main__":
    asyncio.run(demo_papaniu_verification())
