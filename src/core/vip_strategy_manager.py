# -*- coding: utf-8 -*-
"""
至尊会员策略源码管理系统
提供策略源码访问、版本控制、个性化定制等服务
"""

import os
import json
import hashlib
import zipfile
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
from enum import Enum
import logging


class MembershipLevel(Enum):
    """会员等级"""
    FREE = "free"           # 六壬观心 - 免费
    PREMIUM = "premium"     # 遁甲择时 - $39/月
    VIP = "vip"            # 太乙观澜 - $128/月


class StrategyCategory(Enum):
    """策略分类"""
    MOMENTUM = "momentum"           # 动量策略
    MEAN_REVERSION = "mean_reversion"  # 均值回归
    ARBITRAGE = "arbitrage"         # 套利策略
    OPTIONS = "options"             # 期权策略
    FUTURES = "futures"             # 期货策略
    DEMON_STOCK = "demon_stock"     # 妖股识别
    MARKET_MAKING = "market_making" # 做市策略
    PAIRS_TRADING = "pairs_trading" # 配对交易


@dataclass
class StrategyInfo:
    """策略信息"""
    id: str
    name: str
    category: StrategyCategory
    description: str
    author: str
    version: str
    created_at: datetime
    updated_at: datetime
    min_membership: MembershipLevel
    performance_metrics: Dict[str, float]
    risk_level: int  # 1-5级风险
    source_files: List[str]
    dependencies: List[str]
    documentation: str
    is_active: bool = True


@dataclass
class UserAccess:
    """用户访问记录"""
    user_id: str
    membership_level: MembershipLevel
    access_time: datetime
    strategy_id: str
    action: str  # download, view, customize
    ip_address: str


class VIPStrategyManager:
    """至尊会员策略管理器"""
    
    def __init__(self, strategies_dir: str = "strategies", data_dir: str = "data/vip"):
        self.strategies_dir = Path(strategies_dir)
        self.data_dir = Path(data_dir)
        self.strategies_dir.mkdir(exist_ok=True)
        self.data_dir.mkdir(exist_ok=True, parents=True)
        
        self.logger = self._setup_logger()
        self.strategies: Dict[str, StrategyInfo] = {}
        self.user_access_log: List[UserAccess] = []
        
        # 加载策略信息
        self._load_strategies()
    
    def _setup_logger(self) -> logging.Logger:
        """设置日志"""
        logger = logging.getLogger('VIPStrategyManager')
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        
        return logger
    
    def _load_strategies(self):
        """加载策略信息"""
        strategies_file = self.data_dir / "strategies.json"
        if strategies_file.exists():
            try:
                with open(strategies_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    for strategy_data in data:
                        strategy = StrategyInfo(
                            **{k: v if k not in ['created_at', 'updated_at'] else datetime.fromisoformat(v) 
                               for k, v in strategy_data.items()}
                        )
                        strategy.category = StrategyCategory(strategy_data['category'])
                        strategy.min_membership = MembershipLevel(strategy_data['min_membership'])
                        self.strategies[strategy.id] = strategy
                        
                self.logger.info(f"✅ 加载了 {len(self.strategies)} 个策略")
            except Exception as e:
                self.logger.error(f"❌ 加载策略失败: {e}")
        else:
            # 初始化默认策略
            self._initialize_default_strategies()
    
    def _initialize_default_strategies(self):
        """初始化默认策略"""
        default_strategies = [
            {
                "id": "cpa_macd_v2",
                "name": "CPA-MACD增强版",
                "category": StrategyCategory.MOMENTUM,
                "description": "基于CPA指标和MACD的动量策略，适用于日内交易",
                "author": "太公心易团队",
                "version": "2.1.0",
                "min_membership": MembershipLevel.VIP,
                "performance_metrics": {
                    "annual_return": 0.285,
                    "sharpe_ratio": 1.85,
                    "max_drawdown": 0.12,
                    "win_rate": 0.68
                },
                "risk_level": 3,
                "source_files": ["cpa_macd_strategy.py", "indicators/cpa_indicator.py"],
                "dependencies": ["numpy", "pandas", "ta-lib"],
                "documentation": "CPA-MACD策略详细说明文档"
            },
            {
                "id": "demon_stock_detector",
                "name": "妖股识别系统",
                "category": StrategyCategory.DEMON_STOCK,
                "description": "基于多维度指标识别潜在妖股的AI系统",
                "author": "太公心易团队",
                "version": "1.5.0",
                "min_membership": MembershipLevel.PREMIUM,
                "performance_metrics": {
                    "precision": 0.72,
                    "recall": 0.65,
                    "f1_score": 0.68,
                    "accuracy": 0.78
                },
                "risk_level": 4,
                "source_files": ["demon_detector.py", "features/volume_analysis.py"],
                "dependencies": ["scikit-learn", "xgboost", "pandas"],
                "documentation": "妖股识别算法原理与使用指南"
            },
            {
                "id": "options_wheel_strategy",
                "name": "期权轮动策略",
                "category": StrategyCategory.OPTIONS,
                "description": "基于波动率的期权轮动策略，适合稳健收益",
                "author": "太公心易团队",
                "version": "1.3.0",
                "min_membership": MembershipLevel.VIP,
                "performance_metrics": {
                    "annual_return": 0.18,
                    "sharpe_ratio": 2.1,
                    "max_drawdown": 0.08,
                    "win_rate": 0.82
                },
                "risk_level": 2,
                "source_files": ["options_wheel.py", "volatility/iv_calculator.py"],
                "dependencies": ["ib-insync", "py_vollib", "scipy"],
                "documentation": "期权轮动策略实施指南"
            }
        ]
        
        for strategy_data in default_strategies:
            strategy = StrategyInfo(
                **strategy_data,
                created_at=datetime.now(),
                updated_at=datetime.now()
            )
            self.strategies[strategy.id] = strategy
        
        self._save_strategies()
        self.logger.info("✅ 初始化默认策略完成")
    
    def _save_strategies(self):
        """保存策略信息"""
        strategies_file = self.data_dir / "strategies.json"
        try:
            strategies_data = []
            for strategy in self.strategies.values():
                data = asdict(strategy)
                data['category'] = strategy.category.value
                data['min_membership'] = strategy.min_membership.value
                data['created_at'] = strategy.created_at.isoformat()
                data['updated_at'] = strategy.updated_at.isoformat()
                strategies_data.append(data)
            
            with open(strategies_file, 'w', encoding='utf-8') as f:
                json.dump(strategies_data, f, ensure_ascii=False, indent=2)
                
        except Exception as e:
            self.logger.error(f"❌ 保存策略失败: {e}")
    
    def get_available_strategies(self, membership_level: MembershipLevel) -> List[StrategyInfo]:
        """获取用户可访问的策略列表"""
        available = []
        
        # 定义会员等级权限
        level_hierarchy = {
            MembershipLevel.FREE: 0,
            MembershipLevel.PREMIUM: 1,
            MembershipLevel.VIP: 2
        }
        
        user_level = level_hierarchy[membership_level]
        
        for strategy in self.strategies.values():
            if not strategy.is_active:
                continue
                
            required_level = level_hierarchy[strategy.min_membership]
            if user_level >= required_level:
                available.append(strategy)
        
        return sorted(available, key=lambda x: x.updated_at, reverse=True)
    
    def get_strategy_source(self, strategy_id: str, user_id: str, 
                          membership_level: MembershipLevel, ip_address: str) -> Optional[Tuple[bytes, str]]:
        """获取策略源码"""
        if strategy_id not in self.strategies:
            return None
        
        strategy = self.strategies[strategy_id]
        
        # 检查权限
        if not self._check_access_permission(strategy, membership_level):
            self.logger.warning(f"用户 {user_id} 权限不足，无法访问策略 {strategy_id}")
            return None
        
        # 记录访问日志
        self._log_user_access(user_id, membership_level, strategy_id, "download", ip_address)
        
        try:
            # 创建策略源码包
            zip_buffer = self._create_strategy_package(strategy)
            filename = f"{strategy.name}_v{strategy.version}_{datetime.now().strftime('%Y%m%d')}.zip"
            
            self.logger.info(f"✅ 用户 {user_id} 下载策略源码: {strategy_id}")
            return zip_buffer, filename
            
        except Exception as e:
            self.logger.error(f"❌ 创建策略包失败: {e}")
            return None
    
    def _check_access_permission(self, strategy: StrategyInfo, user_level: MembershipLevel) -> bool:
        """检查访问权限"""
        level_hierarchy = {
            MembershipLevel.FREE: 0,
            MembershipLevel.PREMIUM: 1,
            MembershipLevel.VIP: 2
        }
        
        return level_hierarchy[user_level] >= level_hierarchy[strategy.min_membership]
    
    def _create_strategy_package(self, strategy: StrategyInfo) -> bytes:
        """创建策略源码包"""
        import io
        
        zip_buffer = io.BytesIO()
        
        with zipfile.ZipFile(zip_buffer, 'w', zipfile.ZIP_DEFLATED) as zip_file:
            # 添加策略信息文件
            strategy_info = {
                "name": strategy.name,
                "version": strategy.version,
                "description": strategy.description,
                "author": strategy.author,
                "risk_level": strategy.risk_level,
                "performance_metrics": strategy.performance_metrics,
                "dependencies": strategy.dependencies,
                "usage_instructions": strategy.documentation
            }
            
            zip_file.writestr("strategy_info.json", 
                            json.dumps(strategy_info, ensure_ascii=False, indent=2))
            
            # 添加源码文件
            for source_file in strategy.source_files:
                source_path = self.strategies_dir / strategy.id / source_file
                if source_path.exists():
                    zip_file.write(source_path, source_file)
                else:
                    # 如果文件不存在，创建模板文件
                    template_content = self._generate_template_code(source_file, strategy)
                    zip_file.writestr(source_file, template_content)
            
            # 添加README文件
            readme_content = self._generate_readme(strategy)
            zip_file.writestr("README.md", readme_content)
            
            # 添加requirements.txt
            requirements = "\n".join(strategy.dependencies)
            zip_file.writestr("requirements.txt", requirements)
        
        zip_buffer.seek(0)
        return zip_buffer.getvalue()
    
    def _generate_template_code(self, filename: str, strategy: StrategyInfo) -> str:
        """生成模板代码"""
        if filename.endswith('.py'):
            return f'''# -*- coding: utf-8 -*-
"""
{strategy.name} - {filename}
版本: {strategy.version}
作者: {strategy.author}
风险等级: {strategy.risk_level}/5

{strategy.description}
"""

import pandas as pd
import numpy as np
from datetime import datetime
from typing import Dict, List, Any, Optional


class {strategy.name.replace(' ', '').replace('-', '')}:
    """
    {strategy.description}
    """
    
    def __init__(self, config: Dict[str, Any] = None):
        self.config = config or {{}}
        self.name = "{strategy.name}"
        self.version = "{strategy.version}"
    
    def initialize(self):
        """初始化策略"""
        pass
    
    def on_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """处理市场数据"""
        # TODO: 实现策略逻辑
        return {{"action": "hold", "quantity": 0}}
    
    def on_order_update(self, order: Dict[str, Any]):
        """处理订单更新"""
        pass
    
    def get_performance_metrics(self) -> Dict[str, float]:
        """获取策略表现指标"""
        return {strategy.performance_metrics}


# 使用示例
if __name__ == "__main__":
    strategy = {strategy.name.replace(' ', '').replace('-', '')}()
    strategy.initialize()
    print(f"策略 {{strategy.name}} v{{strategy.version}} 已初始化")
'''
        else:
            return f"# {strategy.name} - {filename}\n# 配置文件或数据文件\n"
    
    def _generate_readme(self, strategy: StrategyInfo) -> str:
        """生成README文档"""
        return f"""# {strategy.name}

## 策略概述
{strategy.description}

## 基本信息
- **版本**: {strategy.version}
- **作者**: {strategy.author}
- **风险等级**: {strategy.risk_level}/5
- **最低会员等级**: {strategy.min_membership.value}

## 性能指标
{chr(10).join([f"- **{k}**: {v}" for k, v in strategy.performance_metrics.items()])}

## 依赖包
```bash
pip install {' '.join(strategy.dependencies)}
```

## 使用说明
{strategy.documentation}

## 文件结构
{chr(10).join([f"- {file}" for file in strategy.source_files])}

## 免责声明
本策略仅供学习和研究使用，不构成投资建议。使用本策略进行实盘交易的风险由用户自行承担。

---
*太公心易BI - 至尊会员专享策略*
"""
    
    def _log_user_access(self, user_id: str, membership_level: MembershipLevel, 
                        strategy_id: str, action: str, ip_address: str):
        """记录用户访问日志"""
        access_record = UserAccess(
            user_id=user_id,
            membership_level=membership_level,
            access_time=datetime.now(),
            strategy_id=strategy_id,
            action=action,
            ip_address=ip_address
        )
        
        self.user_access_log.append(access_record)
        
        # 定期清理日志（保留30天）
        cutoff_date = datetime.now() - timedelta(days=30)
        self.user_access_log = [
            log for log in self.user_access_log 
            if log.access_time > cutoff_date
        ]
    
    def get_user_access_stats(self, user_id: str) -> Dict[str, Any]:
        """获取用户访问统计"""
        user_logs = [log for log in self.user_access_log if log.user_id == user_id]
        
        if not user_logs:
            return {"total_access": 0, "strategies_accessed": [], "last_access": None}
        
        return {
            "total_access": len(user_logs),
            "strategies_accessed": list(set(log.strategy_id for log in user_logs)),
            "last_access": max(log.access_time for log in user_logs).isoformat(),
            "access_by_action": {
                action: len([log for log in user_logs if log.action == action])
                for action in set(log.action for log in user_logs)
            }
        }
    
    def add_custom_strategy(self, strategy_info: Dict[str, Any], source_files: Dict[str, str]) -> str:
        """添加自定义策略（VIP专享）"""
        strategy_id = f"custom_{hashlib.md5(strategy_info['name'].encode()).hexdigest()[:8]}"
        
        # 创建策略目录
        strategy_dir = self.strategies_dir / strategy_id
        strategy_dir.mkdir(exist_ok=True)
        
        # 保存源码文件
        for filename, content in source_files.items():
            file_path = strategy_dir / filename
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
        
        # 创建策略信息
        strategy = StrategyInfo(
            id=strategy_id,
            name=strategy_info['name'],
            category=StrategyCategory(strategy_info.get('category', 'momentum')),
            description=strategy_info.get('description', ''),
            author=strategy_info.get('author', '用户自定义'),
            version=strategy_info.get('version', '1.0.0'),
            created_at=datetime.now(),
            updated_at=datetime.now(),
            min_membership=MembershipLevel.VIP,
            performance_metrics=strategy_info.get('performance_metrics', {}),
            risk_level=strategy_info.get('risk_level', 3),
            source_files=list(source_files.keys()),
            dependencies=strategy_info.get('dependencies', []),
            documentation=strategy_info.get('documentation', ''),
            is_active=True
        )
        
        self.strategies[strategy_id] = strategy
        self._save_strategies()
        
        self.logger.info(f"✅ 添加自定义策略: {strategy_id}")
        return strategy_id
