# -*- coding: utf-8 -*-
"""
增强版稷下学宫 - 融合TradingAgents多智能体架构
事件驱动的结构化AI辩论系统

核心创新：
1. RSS事件驱动 + 多智能体辩论
2. 九大主演风险感知光谱
3. 三清论道，八仙过海
4. 向量化记忆与学习

作者：太公心易BI系统
版本：v2.0 Enhanced
"""

import asyncio
import json
import logging
from datetime import datetime
from typing import Dict, List, Any, Optional, TypedDict
from dataclasses import dataclass
from enum import Enum
import aiohttp
import os

# 借鉴TradingAgents的状态管理思想
class DebateState(TypedDict):
    """辩论状态管理"""
    topic: str
    current_round: int
    max_rounds: int
    speakers_history: Dict[str, List[str]]
    debate_history: List[Dict[str, Any]]
    current_speaker: str
    debate_conclusion: str
    is_active: bool


class SpeakerRole(Enum):
    """发言者角色"""
    MODERATOR = "主持人"
    POSITIVE = "正方"
    NEGATIVE = "反方"
    OBSERVER = "观察者"


@dataclass
class DebateMessage:
    """辩论消息"""
    speaker: str
    role: SpeakerRole
    content: str
    timestamp: datetime
    round_number: int
    risk_level: int


class EnhancedRetailInvestor:
    """增强版散户投资者 - 添加记忆和学习能力"""
    
    def __init__(self, name: str, risk_level: int, personality: str, 
                 background: str, trading_style: str, capital: int):
        self.name = name
        self.risk_level = risk_level  # 0-100的风险感知水平
        self.personality = personality
        self.background = background
        self.trading_style = trading_style
        self.capital = capital
        
        # 新增：记忆和学习能力
        self.memory = []  # 历史经验记忆
        self.learning_history = []  # 学习历史
        self.emotional_state = "neutral"  # 当前情绪状态
        self.confidence_level = 50  # 信心水平 0-100
        
    def add_memory(self, event: str, reaction: str, outcome: str):
        """添加记忆"""
        memory_entry = {
            "event": event,
            "reaction": reaction,
            "outcome": outcome,
            "timestamp": datetime.now().isoformat(),
            "lesson_learned": self._extract_lesson(event, reaction, outcome)
        }
        self.memory.append(memory_entry)
        
        # 只保留最近50条记忆
        if len(self.memory) > 50:
            self.memory = self.memory[-50:]
    
    def _extract_lesson(self, event: str, reaction: str, outcome: str) -> str:
        """从经验中提取教训"""
        # 简化的教训提取逻辑
        if "亏损" in outcome:
            return f"在{event}时{reaction}导致了损失，下次应该更谨慎"
        elif "盈利" in outcome:
            return f"在{event}时{reaction}获得了收益，可以继续这种策略"
        else:
            return f"在{event}时{reaction}，结果平平，需要观察更多数据"
    
    def get_relevant_memories(self, current_event: str, n_matches: int = 3) -> List[Dict]:
        """获取相关记忆（简化版向量匹配）"""
        # 简化的关键词匹配，实际应该使用向量相似度
        relevant_memories = []
        event_keywords = current_event.lower().split()
        
        for memory in self.memory:
            memory_keywords = memory["event"].lower().split()
            # 计算关键词重叠度
            overlap = len(set(event_keywords) & set(memory_keywords))
            if overlap > 0:
                memory["relevance_score"] = overlap
                relevant_memories.append(memory)
        
        # 按相关性排序并返回前n个
        relevant_memories.sort(key=lambda x: x["relevance_score"], reverse=True)
        return relevant_memories[:n_matches]


class EnhancedJixiaAcademy:
    """增强版稷下学宫 - 多智能体辩论系统"""
    
    def __init__(self, openrouter_api_key: str = None):
        self.logger = logging.getLogger('EnhancedJixiaAcademy')
        self.api_key = openrouter_api_key
        self.base_url = "https://openrouter.ai/api/v1/chat/completions"
        self.model = "deepseek/deepseek-chat"
        
        # 辩论状态
        self.debate_state: DebateState = {
            "topic": "",
            "current_round": 0,
            "max_rounds": 3,
            "speakers_history": {},
            "debate_history": [],
            "current_speaker": "",
            "debate_conclusion": "",
            "is_active": False
        }
        
        # 初始化九大主演（增强版）
        self._initialize_enhanced_investors()
        
        # 兜率宫八卦炉辩论流程 - 按先天八卦顺序
        self.bagua_debate_flow = {
            "round_1": {
                "name": "八卦炉第一轮",
                "description": "按先天八卦顺序完整发言",
                "speakers": [
                    "吕洞宾",    # 乾☰ - 天
                    "张果老",    # 兑☱ - 泽
                    "汉钟离",    # 离☲ - 火
                    "曹国舅",    # 震☳ - 雷
                    "铁拐李",    # 巽☴ - 风
                    "蓝采和",    # 坎☵ - 水
                    "韩湘子",    # 艮☶ - 山
                    "何仙姑"     # 坤☷ - 地
                ]
            },
            "round_2": {
                "name": "长毛象互动轮",
                "description": "有话则多，无话则免",
                "speakers": "dynamic",  # 根据长毛象热度动态决定
                "trigger": "mastodon_engagement"
            }
        }

        # 三清职责分工
        self.sanqing_roles = {
            "太上老君": {
                "role": "debate_moderator",
                "description": "斗蛐蛐式撩拨，激发观点碰撞",
                "trigger_timing": "between_speakers",
                "style": "provocative_questioning"
            },
            "灵宝道君": {
                "role": "data_validator",
                "description": "MCP工具调用，RSS数据核实总结",
                "trigger_timing": "after_round_1",
                "tools": ["rss_query", "market_data", "fact_check"]
            },
            "元始天尊": {
                "role": "final_judge",
                "description": "把厚报告读薄，最终多空裁决",
                "trigger_timing": "conclusion",
                "output_format": "bull_bear_verdict"
            }
        }
    
    def _initialize_enhanced_investors(self):
        """初始化增强版九大主演"""
        self.investors = {
            # 三清（主持和观察）
            "灵宝道君": EnhancedRetailInvestor(
                name="灵宝道君", risk_level=50, 
                personality="公正严明，善于引导讨论，维持辩论秩序",
                background="稷下学宫主持人，中立观察者",
                trading_style="不参与交易，专注于分析和总结",
                capital=0
            ),
            
            # 八仙（辩论参与者）
            "吕洞宾": EnhancedRetailInvestor(
                name="吕洞宾", risk_level=25,
                personality="智慧深邃，逻辑严密，善于开场立论",
                background="正方领袖，理性投资倡导者",
                trading_style="价值投资，长期持有",
                capital=500000
            ),
            
            "何仙姑": EnhancedRetailInvestor(
                name="何仙姑", risk_level=75,
                personality="温和理性，善于发现问题，逻辑清晰",
                background="反方领袖，风险控制专家",
                trading_style="保守投资，注重风险管理",
                capital=300000
            ),
            
            "张果老": EnhancedRetailInvestor(
                name="张果老", risk_level=35,
                personality="经验丰富，善于举例论证，说服力强",
                background="正方二辩，实战经验丰富的老股民",
                trading_style="技术分析结合基本面",
                capital=800000
            ),
            
            "韩湘子": EnhancedRetailInvestor(
                name="韩湘子", risk_level=65,
                personality="年轻敏锐，善于质疑，思维活跃",
                background="反方二辩，新生代理性投资者",
                trading_style="量化投资，数据驱动",
                capital=200000
            ),
            
            "汉钟离": EnhancedRetailInvestor(
                name="汉钟离", risk_level=40,
                personality="稳重大气，善于总结归纳，论证有力",
                background="正方三辩，机构投资背景",
                trading_style="组合投资，分散风险",
                capital=1000000
            ),
            
            "蓝采和": EnhancedRetailInvestor(
                name="蓝采和", risk_level=80,
                personality="灵活机智，善于反驳，角度独特",
                background="反方三辩，独立投资研究员",
                trading_style="逆向投资，价值发现",
                capital=400000
            ),
            
            "曹国舅": EnhancedRetailInvestor(
                name="曹国舅", risk_level=30,
                personality="庄重威严，善于总结陈词，气势磅礴",
                background="正方四辩，投资基金管理人",
                trading_style="成长投资，趋势跟踪",
                capital=2000000
            ),
            
            "铁拐李": EnhancedRetailInvestor(
                name="铁拐李", risk_level=90,
                personality="直率犀利，善于最后反击，一针见血",
                background="反方四辩，市场风险警示者",
                trading_style="空头策略，危机预警",
                capital=600000
            )
        }
    
    async def start_debate(self, topic: str, event_context: Dict[str, Any]) -> Dict[str, Any]:
        """开始稷下学宫辩论"""
        self.logger.info(f"🎭 稷下学宫开始辩论: {topic}")
        
        # 初始化辩论状态
        self.debate_state = {
            "topic": topic,
            "current_round": 0,
            "max_rounds": 3,
            "speakers_history": {},
            "debate_history": [],
            "current_speaker": "",
            "debate_conclusion": "",
            "is_active": True
        }
        
        # 执行辩论流程
        debate_messages = []
        
        for i, speaker_name in enumerate(self.debate_flow):
            if not self.debate_state["is_active"]:
                break
                
            try:
                # 生成发言
                message = await self._generate_speaker_message(
                    speaker_name, topic, event_context, i
                )
                debate_messages.append(message)
                
                # 更新辩论状态
                self._update_debate_state(message)
                
                # 添加延迟避免API限制
                await asyncio.sleep(0.5)
                
            except Exception as e:
                self.logger.error(f"生成{speaker_name}发言失败: {e}")
                # 使用备用发言
                fallback_message = self._get_fallback_message(speaker_name, topic, i)
                debate_messages.append(fallback_message)
        
        # 生成辩论总结
        conclusion = await self._generate_debate_conclusion(topic, debate_messages, event_context)
        
        return {
            "topic": topic,
            "event_context": event_context,
            "debate_messages": debate_messages,
            "conclusion": conclusion,
            "timestamp": datetime.now().isoformat(),
            "participants": len(self.debate_flow),
            "total_rounds": len(debate_messages)
        }
    
    async def _generate_speaker_message(self, speaker_name: str, topic: str, 
                                      event_context: Dict, position: int) -> DebateMessage:
        """生成发言者消息"""
        investor = self.investors[speaker_name]
        
        # 确定角色
        if speaker_name == "灵宝道君":
            role = SpeakerRole.MODERATOR
        elif speaker_name in ["吕洞宾", "张果老", "汉钟离", "曹国舅"]:
            role = SpeakerRole.POSITIVE
        elif speaker_name in ["何仙姑", "韩湘子", "蓝采和", "铁拐李"]:
            role = SpeakerRole.NEGATIVE
        else:
            role = SpeakerRole.OBSERVER
        
        # 获取相关记忆
        relevant_memories = investor.get_relevant_memories(topic)
        
        # 构建提示词
        prompt = self._build_debate_prompt(
            investor, role, topic, event_context, 
            self.debate_state["debate_history"], relevant_memories, position
        )
        
        # 调用AI生成回应
        content = await self._call_ai_api(prompt)
        
        # 创建消息对象
        message = DebateMessage(
            speaker=speaker_name,
            role=role,
            content=content,
            timestamp=datetime.now(),
            round_number=position // 2 + 1,
            risk_level=investor.risk_level
        )
        
        return message
    
    def _build_debate_prompt(self, investor: EnhancedRetailInvestor, role: SpeakerRole,
                           topic: str, event_context: Dict, debate_history: List,
                           memories: List, position: int) -> str:
        """构建辩论提示词"""
        
        # 基础角色设定
        role_instruction = {
            SpeakerRole.MODERATOR: "你是稷下学宫的主持人，负责引导辩论和总结观点",
            SpeakerRole.POSITIVE: "你是正方辩手，需要支持和论证正面观点",
            SpeakerRole.NEGATIVE: "你是反方辩手，需要质疑和反驳正面观点",
            SpeakerRole.OBSERVER: "你是观察者，提供中立的分析和见解"
        }
        
        # 构建记忆上下文
        memory_context = ""
        if memories:
            memory_context = "\n你的相关经验：\n"
            for memory in memories:
                memory_context += f"- {memory['lesson_learned']}\n"
        
        # 构建辩论历史
        history_context = ""
        if debate_history:
            history_context = "\n前面的辩论内容：\n"
            for msg in debate_history[-3:]:  # 只显示最近3条
                history_context += f"{msg['speaker']}: {msg['content']}\n"
        
        prompt = f"""你是{investor.name}，{role_instruction[role]}。

你的特征：
- 风险感知水平: {investor.risk_level}%
- 性格特点: {investor.personality}
- 投资背景: {investor.background}
- 交易风格: {investor.trading_style}

当前辩论主题：{topic}

事件背景：
{event_context.get('description', '')}
关键词：{', '.join(event_context.get('keywords', []))}
影响力评分：{event_context.get('impact_score', 0)}/100

{memory_context}

{history_context}

请以{investor.name}的身份发言，要求：
1. 体现你的角色定位和性格特点
2. 结合你的投资经验和风险感知水平
3. 针对主题进行有理有据的论述
4. 语言要符合角色身份，既有文化底蕴又贴近现实
5. 控制在200字以内

直接回复发言内容，不要加前缀："""
        
        return prompt
    
    async def _call_ai_api(self, prompt: str) -> str:
        """调用AI API"""
        if not self.api_key:
            return "API密钥未配置，使用备用回复。"
        
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
        
        data = {
            "model": self.model,
            "messages": [{"role": "user", "content": prompt}],
            "max_tokens": 300,
            "temperature": 0.8
        }
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.post(self.base_url, headers=headers, json=data) as response:
                    if response.status == 200:
                        result = await response.json()
                        return result['choices'][0]['message']['content'].strip()
                    else:
                        error_text = await response.text()
                        self.logger.error(f"AI API调用失败: {response.status}, {error_text}")
                        return "AI服务暂时不可用，使用备用回复。"
        except Exception as e:
            self.logger.error(f"AI API调用异常: {e}")
            return "网络连接异常，使用备用回复。"
    
    def _update_debate_state(self, message: DebateMessage):
        """更新辩论状态"""
        # 添加到历史记录
        self.debate_state["debate_history"].append({
            "speaker": message.speaker,
            "role": message.role.value,
            "content": message.content,
            "timestamp": message.timestamp.isoformat(),
            "round": message.round_number
        })
        
        # 更新发言者历史
        if message.speaker not in self.debate_state["speakers_history"]:
            self.debate_state["speakers_history"][message.speaker] = []
        self.debate_state["speakers_history"][message.speaker].append(message.content)
        
        # 更新当前发言者
        self.debate_state["current_speaker"] = message.speaker
    
    def _get_fallback_message(self, speaker_name: str, topic: str, position: int) -> DebateMessage:
        """获取备用消息"""
        investor = self.investors[speaker_name]
        
        # 简单的备用回复
        fallback_content = f"作为{speaker_name}，我认为关于{topic}这个话题需要从多个角度来分析..."
        
        return DebateMessage(
            speaker=speaker_name,
            role=SpeakerRole.OBSERVER,
            content=fallback_content,
            timestamp=datetime.now(),
            round_number=position // 2 + 1,
            risk_level=investor.risk_level
        )
    
    async def _generate_debate_conclusion(self, topic: str, messages: List[DebateMessage], 
                                        event_context: Dict) -> str:
        """生成辩论总结"""
        # 统计正反方观点
        positive_points = [msg.content for msg in messages if msg.role == SpeakerRole.POSITIVE]
        negative_points = [msg.content for msg in messages if msg.role == SpeakerRole.NEGATIVE]
        
        conclusion = f"""
🎯 稷下学宫辩论总结

📋 辩论主题：{topic}
📊 事件影响力：{event_context.get('impact_score', 0)}/100
🔑 关键词：{', '.join(event_context.get('keywords', []))}

🟢 正方观点总结：
{len(positive_points)}位正方辩手从不同角度论证了积极观点...

🔴 反方观点总结：
{len(negative_points)}位反方辩手提出了谨慎和风险控制的观点...

💡 太公心易智慧：
在市场的喧嚣中，智者能听到不同的声音。
正如稷下学宫百家争鸣，投资决策也需要兼听则明。
真正的智慧不在于预测未来，而在于理解当下。

⚖️ 综合建议：
基于本次辩论，建议投资者：
1. 保持理性，避免情绪化决策
2. 分散风险，不要孤注一掷  
3. 持续学习，适应市场变化
4. 量力而行，控制仓位规模

---
*稷下学宫 - 三清论道，八仙过海*
"""
        
        return conclusion


# 使用示例
async def main():
    """测试增强版稷下学宫"""
    # 初始化系统
    academy = EnhancedJixiaAcademy(openrouter_api_key=os.getenv('OPENROUTER_API_KEY'))
    
    # 模拟事件
    event_context = {
        "title": "美联储意外加息50个基点",
        "description": "美联储在今日的议息会议上意外宣布加息50个基点，超出市场预期的25个基点",
        "keywords": ["美联储", "加息", "货币政策"],
        "impact_score": 85,
        "source": "财经新闻"
    }
    
    # 开始辩论
    result = await academy.start_debate("美联储加息对市场的影响", event_context)
    
    # 输出结果
    print(json.dumps(result, ensure_ascii=False, indent=2))


if __name__ == "__main__":
    asyncio.run(main())