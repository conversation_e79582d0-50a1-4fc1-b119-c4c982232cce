# -*- coding: utf-8 -*-
"""
太公心易BI系统 - 妖股识别引擎
基于累计价格行为的CPA-MACD指标识别妖股

妖股定义：
- IB基本面：晨星无评级，分析师懒得研究，但在板块中不垫底
- 做空数据：明显增加，但下跌效率逐渐下降
- 技术特征：使用CPA-MACD捕捉日内行棋效率，缩小跳空缺口权重
"""

import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
import logging
from enum import Enum

class DemonStockLevel(Enum):
    """妖股等级"""
    NORMAL = "正常股票"
    POTENTIAL = "潜在妖股"
    EMERGING = "新兴妖股"
    CONFIRMED = "确认妖股"
    SUPER_DEMON = "超级妖股"

@dataclass
class PriceAction:
    """价格行为数据"""
    open_price: float
    high_price: float
    low_price: float
    close_price: float
    volume: int
    timestamp: datetime
    
    @property
    def is_limit_board(self) -> bool:
        """是否涨跌停板"""
        return abs(self.high_price - self.low_price) < 0.001
    
    @property
    def is_up_limit(self) -> bool:
        """是否涨停"""
        return self.is_limit_board and self.close_price > self.open_price
    
    @property
    def is_down_limit(self) -> bool:
        """是否跌停"""
        return self.is_limit_board and self.close_price < self.open_price

@dataclass
class DemonStockSignal:
    """妖股信号"""
    symbol: str
    level: DemonStockLevel
    confidence: float  # 0-1
    cpa_macd_value: float
    dcp_ratio: float
    short_interest_trend: str  # 'increasing', 'decreasing', 'stable'
    fundamental_score: float  # 基本面评分
    technical_score: float   # 技术面评分
    reasoning: str
    timestamp: datetime
    risk_warning: str

class CPAMACDCalculator:
    """累计价格行为MACD计算器"""
    
    def __init__(self, m1: int = 12, m2: int = 26, m3: int = 9, smooth: int = 20):
        """
        初始化CPA-MACD参数
        
        Args:
            m1: 快线周期
            m2: 慢线周期  
            m3: DEA周期
            smooth: 平滑周期
        """
        self.m1 = m1
        self.m2 = m2
        self.m3 = m3
        self.smooth = smooth
        self.logger = logging.getLogger('CPAMACDCalculator')
    
    def calculate_dcp_ratio(self, price_action: PriceAction, prev_high: float, prev_low: float) -> float:
        """
        计算日内价格行为比率(DCP_RATIO)
        
        核心算法：
        - 涨跌停板：涨停=1，跌停=-1，其他=0
        - 正常交易：(收盘-开盘)/(最高-最低)
        - 限制范围：[-1, 1]
        """
        if price_action.is_limit_board:
            if price_action.is_up_limit:
                return 1.0
            elif price_action.is_down_limit:
                return -1.0
            else:
                return 0.0
        
        # 正常交易日计算
        price_range = price_action.high_price - price_action.low_price
        if price_range == 0:
            return 0.0
        
        dcp_ratio = (price_action.close_price - price_action.open_price) / price_range
        
        # 限制在[-1, 1]范围内
        return max(-1.0, min(1.0, dcp_ratio))
    
    def calculate_cpa_macd(self, price_actions: List[PriceAction]) -> Tuple[List[float], List[float], List[float]]:
        """
        计算累计价格行为MACD
        
        Returns:
            Tuple[DIF, DEA, MACD]
        """
        if len(price_actions) < max(self.m1, self.m2, self.m3):
            raise ValueError(f"数据长度不足，至少需要{max(self.m1, self.m2, self.m3)}个交易日")
        
        # 计算DCP_RATIO序列
        dcp_ratios = []
        for i, action in enumerate(price_actions):
            if i == 0:
                prev_high = action.high_price
                prev_low = action.low_price
            else:
                prev_high = price_actions[i-1].high_price
                prev_low = price_actions[i-1].low_price
            
            dcp_ratio = self.calculate_dcp_ratio(action, prev_high, prev_low)
            dcp_ratios.append(dcp_ratio)
        
        # 计算累计DCP_SUM
        dcp_sum = np.cumsum(dcp_ratios)
        
        # 计算EMA
        def ema(data, period):
            alpha = 2.0 / (period + 1)
            ema_values = [data[0]]
            for i in range(1, len(data)):
                ema_values.append(alpha * data[i] + (1 - alpha) * ema_values[-1])
            return ema_values
        
        # 计算DIF和DEA
        ema_fast = ema(dcp_sum, self.m1)
        ema_slow = ema(dcp_sum, self.m2)
        
        dif = [fast - slow for fast, slow in zip(ema_fast, ema_slow)]
        dea = ema(dif, self.m3)
        
        # 计算MACD
        macd = [(d - dea_val) * 2 for d, dea_val in zip(dif, dea)]
        
        return dif, dea, macd

class DemonStockDetector:
    """妖股识别引擎"""
    
    def __init__(self):
        self.cpa_calculator = CPAMACDCalculator()
        self.logger = logging.getLogger('DemonStockDetector')
        
        # 妖股识别阈值
        self.thresholds = {
            'cpa_macd_min': 0.1,      # CPA-MACD最小值
            'confidence_min': 0.6,     # 最小置信度
            'volume_spike': 2.0,       # 成交量放大倍数
            'short_interest_increase': 0.2,  # 做空增加阈值
        }
    
    def analyze_demon_potential(self, 
                              symbol: str,
                              price_actions: List[PriceAction],
                              fundamental_data: Optional[Dict] = None,
                              short_interest_data: Optional[Dict] = None) -> DemonStockSignal:
        """
        分析股票的妖股潜力
        
        Args:
            symbol: 股票代码
            price_actions: 价格行为数据
            fundamental_data: 基本面数据
            short_interest_data: 做空数据
        """
        self.logger.info(f"🔍 开始分析妖股潜力: {symbol}")
        
        try:
            # 1. 计算CPA-MACD
            dif, dea, macd = self.cpa_calculator.calculate_cpa_macd(price_actions)
            current_macd = macd[-1] if macd else 0
            current_dcp = self.cpa_calculator.calculate_dcp_ratio(
                price_actions[-1], 
                price_actions[-2].high_price if len(price_actions) > 1 else price_actions[-1].high_price,
                price_actions[-2].low_price if len(price_actions) > 1 else price_actions[-1].low_price
            )
            
            # 2. 基本面评分
            fundamental_score = self._evaluate_fundamentals(fundamental_data)
            
            # 3. 技术面评分
            technical_score = self._evaluate_technicals(price_actions, macd)
            
            # 4. 做空数据分析
            short_trend = self._analyze_short_interest(short_interest_data)
            
            # 5. 综合评估
            level, confidence = self._determine_demon_level(
                current_macd, fundamental_score, technical_score, short_trend
            )
            
            # 6. 生成推理说明
            reasoning = self._generate_reasoning(
                symbol, current_macd, fundamental_score, technical_score, short_trend
            )
            
            # 7. 风险提示
            risk_warning = self._generate_risk_warning(level, confidence)
            
            signal = DemonStockSignal(
                symbol=symbol,
                level=level,
                confidence=confidence,
                cpa_macd_value=current_macd,
                dcp_ratio=current_dcp,
                short_interest_trend=short_trend,
                fundamental_score=fundamental_score,
                technical_score=technical_score,
                reasoning=reasoning,
                timestamp=datetime.now(),
                risk_warning=risk_warning
            )
            
            self.logger.info(f"✅ 妖股分析完成: {symbol} - {level.value} (置信度: {confidence:.2f})")
            return signal
            
        except Exception as e:
            self.logger.error(f"❌ 妖股分析失败 {symbol}: {e}")
            return DemonStockSignal(
                symbol=symbol,
                level=DemonStockLevel.NORMAL,
                confidence=0.0,
                cpa_macd_value=0.0,
                dcp_ratio=0.0,
                short_interest_trend='unknown',
                fundamental_score=0.0,
                technical_score=0.0,
                reasoning=f"分析失败: {str(e)}",
                timestamp=datetime.now(),
                risk_warning="数据不足，无法评估"
            )
    
    def _evaluate_fundamentals(self, fundamental_data: Optional[Dict]) -> float:
        """
        评估基本面 - 妖股特征：晨星无评级，分析师懒得研究，但不垫底
        """
        if not fundamental_data:
            return 0.5  # 无数据默认中性
        
        score = 0.0
        
        # 晨星评级 - 无评级得分更高
        morningstar_rating = fundamental_data.get('morningstar_rating')
        if morningstar_rating is None or morningstar_rating == 'N/A':
            score += 0.3
        
        # 分析师覆盖度 - 覆盖少得分更高
        analyst_coverage = fundamental_data.get('analyst_count', 0)
        if analyst_coverage < 3:
            score += 0.3
        
        # 行业排名 - 不垫底
        sector_rank = fundamental_data.get('sector_rank_percentile', 50)
        if 20 < sector_rank < 80:  # 不在前20%也不在后20%
            score += 0.4
        
        return min(1.0, score)
    
    def _evaluate_technicals(self, price_actions: List[PriceAction], macd: List[float]) -> float:
        """
        评估技术面
        """
        if len(price_actions) < 5 or not macd:
            return 0.0
        
        score = 0.0
        
        # MACD趋势
        if len(macd) >= 3:
            recent_macd = macd[-3:]
            if all(recent_macd[i] > recent_macd[i-1] for i in range(1, len(recent_macd))):
                score += 0.4  # MACD上升趋势
        
        # 成交量放大
        recent_volumes = [action.volume for action in price_actions[-5:]]
        avg_volume = sum(recent_volumes[:-1]) / len(recent_volumes[:-1])
        if recent_volumes[-1] > avg_volume * self.thresholds['volume_spike']:
            score += 0.3
        
        # 价格波动效率
        recent_actions = price_actions[-5:]
        efficiency_scores = []
        for action in recent_actions:
            if action.high_price != action.low_price:
                efficiency = abs(action.close_price - action.open_price) / (action.high_price - action.low_price)
                efficiency_scores.append(efficiency)
        
        if efficiency_scores and sum(efficiency_scores) / len(efficiency_scores) > 0.6:
            score += 0.3
        
        return min(1.0, score)
    
    def _analyze_short_interest(self, short_data: Optional[Dict]) -> str:
        """
        分析做空数据趋势
        """
        if not short_data:
            return 'unknown'
        
        current_short = short_data.get('current_short_interest', 0)
        previous_short = short_data.get('previous_short_interest', 0)
        
        if current_short > previous_short * (1 + self.thresholds['short_interest_increase']):
            return 'increasing'
        elif current_short < previous_short * (1 - self.thresholds['short_interest_increase']):
            return 'decreasing'
        else:
            return 'stable'
    
    def _determine_demon_level(self, macd_value: float, fundamental_score: float, 
                             technical_score: float, short_trend: str) -> Tuple[DemonStockLevel, float]:
        """
        确定妖股等级和置信度
        """
        # 综合评分
        total_score = (fundamental_score * 0.3 + technical_score * 0.4 + 
                      (0.3 if macd_value > self.thresholds['cpa_macd_min'] else 0) * 0.3)
        
        # 做空数据加权
        if short_trend == 'increasing':
            total_score += 0.2  # 做空增加但下跌效率下降是妖股特征
        
        confidence = total_score
        
        if total_score >= 0.8:
            level = DemonStockLevel.SUPER_DEMON
        elif total_score >= 0.7:
            level = DemonStockLevel.CONFIRMED
        elif total_score >= 0.6:
            level = DemonStockLevel.EMERGING
        elif total_score >= 0.4:
            level = DemonStockLevel.POTENTIAL
        else:
            level = DemonStockLevel.NORMAL
        
        return level, confidence
    
    def _generate_reasoning(self, symbol: str, macd_value: float, 
                          fundamental_score: float, technical_score: float, 
                          short_trend: str) -> str:
        """
        生成推理说明
        """
        reasoning_parts = []
        
        reasoning_parts.append(f"CPA-MACD值: {macd_value:.4f}")
        
        if fundamental_score > 0.6:
            reasoning_parts.append("基本面符合妖股特征：缺乏主流关注但基础不差")
        
        if technical_score > 0.6:
            reasoning_parts.append("技术面显示异常活跃：成交量放大，价格效率提升")
        
        if short_trend == 'increasing':
            reasoning_parts.append("做空数据增加但下跌效率下降，符合妖股逆转特征")
        
        return "; ".join(reasoning_parts)
    
    def _generate_risk_warning(self, level: DemonStockLevel, confidence: float) -> str:
        """
        生成风险提示
        """
        if level in [DemonStockLevel.SUPER_DEMON, DemonStockLevel.CONFIRMED]:
            return "⚠️ 高风险：妖股波动极大，请严格控制仓位，设置止损"
        elif level == DemonStockLevel.EMERGING:
            return "⚠️ 中高风险：新兴妖股不确定性较大，建议小仓位试探"
        elif level == DemonStockLevel.POTENTIAL:
            return "⚠️ 中等风险：潜在妖股需密切观察，谨慎参与"
        else:
            return "ℹ️ 正常风险：未发现明显妖股特征"

# 示例使用
if __name__ == "__main__":
    # 创建示例数据
    detector = DemonStockDetector()
    
    # 模拟价格行为数据
    sample_actions = [
        PriceAction(100, 105, 98, 103, 1000000, datetime.now() - timedelta(days=i))
        for i in range(30, 0, -1)
    ]
    
    # 模拟基本面数据
    sample_fundamentals = {
        'morningstar_rating': None,
        'analyst_count': 2,
        'sector_rank_percentile': 45
    }
    
    # 模拟做空数据
    sample_short_data = {
        'current_short_interest': 15.5,
        'previous_short_interest': 12.0
    }
    
    # 分析妖股潜力
    signal = detector.analyze_demon_potential(
        "TEST", sample_actions, sample_fundamentals, sample_short_data
    )
    
    print(f"妖股分析结果: {signal.level.value}")
    print(f"置信度: {signal.confidence:.2f}")
    print(f"推理: {signal.reasoning}")
    print(f"风险提示: {signal.risk_warning}")