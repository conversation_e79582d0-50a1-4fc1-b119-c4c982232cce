-- 稷下学宫PostgreSQL数据库架构
-- 简化版设计，专注于核心功能

-- 市场情报表
CREATE TABLE IF NOT EXISTS market_intelligence (
    id SERIAL PRIMARY KEY,
    title VARCHAR(500) NOT NULL,
    content TEXT NOT NULL,
    source VARCHAR(50) NOT NULL,
    sentiment DECIMAL(3,2) DEFAULT 0.00, -- -1.00 到 1.00
    keywords TEXT[], -- PostgreSQL数组类型
    url VARCHAR(1000),
    author <PERSON><PERSON><PERSON><PERSON>(100),
    published_at TIMESTAMP WITH TIME ZONE,
    processed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    date DATE DEFAULT CURRENT_DATE,
    
    -- 索引字段
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_market_intelligence_date ON market_intelligence(date);
CREATE INDEX IF NOT EXISTS idx_market_intelligence_source ON market_intelligence(source);
CREATE INDEX IF NOT EXISTS idx_market_intelligence_sentiment ON market_intelligence(sentiment);
CREATE INDEX IF NOT EXISTS idx_market_intelligence_keywords ON market_intelligence USING GIN(keywords);

-- AI分析师记忆表
CREATE TABLE IF NOT EXISTS agent_memories (
    id SERIAL PRIMARY KEY,
    agent_name VARCHAR(50) NOT NULL,
    memory_type VARCHAR(50) NOT NULL, -- 'post', 'reply', 'debate', 'analysis'
    content TEXT NOT NULL,
    context JSONB, -- 存储上下文信息
    sentiment DECIMAL(3,2),
    keywords TEXT[],
    related_intelligence_ids INTEGER[], -- 关联的情报ID
    
    -- 时间字段
    memory_date DATE DEFAULT CURRENT_DATE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_agent_memories_agent ON agent_memories(agent_name);
CREATE INDEX IF NOT EXISTS idx_agent_memories_type ON agent_memories(memory_type);
CREATE INDEX IF NOT EXISTS idx_agent_memories_date ON agent_memories(memory_date);
CREATE INDEX IF NOT EXISTS idx_agent_memories_context ON agent_memories USING GIN(context);

-- 辩论记录表
CREATE TABLE IF NOT EXISTS debate_records (
    id SERIAL PRIMARY KEY,
    debate_id VARCHAR(100) NOT NULL,
    topic VARCHAR(500) NOT NULL,
    agent_name VARCHAR(50) NOT NULL,
    position VARCHAR(200) NOT NULL,
    arguments TEXT[] NOT NULL,
    argument_type VARCHAR(50) DEFAULT 'main', -- 'main', 'counter', 'support'
    
    -- 关联信息
    related_intelligence_ids INTEGER[],
    parent_debate_id VARCHAR(100), -- 如果是回应其他辩论
    
    -- 统计信息
    confidence_score DECIMAL(3,2) DEFAULT 0.50,
    impact_score DECIMAL(3,2) DEFAULT 0.00,
    
    -- 时间字段
    debate_date DATE DEFAULT CURRENT_DATE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_debate_records_debate_id ON debate_records(debate_id);
CREATE INDEX IF NOT EXISTS idx_debate_records_agent ON debate_records(agent_name);
CREATE INDEX IF NOT EXISTS idx_debate_records_topic ON debate_records(topic);
CREATE INDEX IF NOT EXISTS idx_debate_records_date ON debate_records(debate_date);

-- 每日共识表
CREATE TABLE IF NOT EXISTS daily_consensus (
    id SERIAL PRIMARY KEY,
    consensus_date DATE DEFAULT CURRENT_DATE,
    topic VARCHAR(500) NOT NULL,
    
    -- 共识内容
    market_direction VARCHAR(50), -- 'bullish', 'bearish', 'neutral'
    confidence_level DECIMAL(3,2) DEFAULT 0.50,
    key_factors TEXT[],
    risk_alerts TEXT[],
    opportunities TEXT[],
    
    -- 参与分析师
    participating_agents TEXT[] NOT NULL,
    debate_ids TEXT[], -- 相关辩论ID
    
    -- 详细共识
    consensus_details JSONB,
    
    -- 时间字段
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- 确保每日每个话题只有一个共识
    UNIQUE(consensus_date, topic)
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_daily_consensus_date ON daily_consensus(consensus_date);
CREATE INDEX IF NOT EXISTS idx_daily_consensus_direction ON daily_consensus(market_direction);
CREATE INDEX IF NOT EXISTS idx_daily_consensus_details ON daily_consensus USING GIN(consensus_details);

-- 分析师配置表
CREATE TABLE IF NOT EXISTS agent_configs (
    id SERIAL PRIMARY KEY,
    agent_name VARCHAR(50) UNIQUE NOT NULL,
    role VARCHAR(100) NOT NULL,
    personality TEXT,
    expertise TEXT[],
    posting_style TEXT,
    
    -- 专长关键词（用于过滤相关情报）
    focus_keywords TEXT[],
    
    -- 统计信息
    total_posts INTEGER DEFAULT 0,
    total_debates INTEGER DEFAULT 0,
    accuracy_score DECIMAL(3,2) DEFAULT 0.50,
    reputation_score DECIMAL(5,2) DEFAULT 0.00,
    
    -- 配置信息
    config JSONB,
    
    -- 时间字段
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 插入默认分析师配置
INSERT INTO agent_configs (agent_name, role, personality, expertise, focus_keywords) VALUES
('太上老君', '宏观战略分析师', '深谋远虑，哲学思辨', ARRAY['宏观经济', '政策分析', '长期趋势'], ARRAY['政策', '宏观', '央行', '利率', '经济', 'GDP', '通胀']),
('元始天尊', '技术分析大师', '精准理性，数据驱动', ARRAY['技术分析', '量化交易', '程序化'], ARRAY['技术分析', '量化', '指标', 'MACD', 'RSI', '成交量', '突破']),
('灵宝天尊', '风险控制专家', '谨慎保守，防范未然', ARRAY['风险管理', '资产配置', '避险策略'], ARRAY['风险', '波动', '下跌', '调整', '避险', '防御', '稳健']),
('铁拐李', '逆向投资专家', '独立思考，逆向而行', ARRAY['逆向投资', '价值发现', '反转交易'], ARRAY['逆向', '反转', '超跌', '低估', '冷门', '被忽视']),
('汉钟离', '趋势跟踪大师', '顺势而为，把握节奏', ARRAY['趋势分析', '动量交易', '周期研究'], ARRAY['趋势', '上涨', '突破', '动量', '强势', '领涨']),
('张果老', '价值投资老炮', '经验丰富，价值导向', ARRAY['价值投资', '基本面分析', '长期持有'], ARRAY['价值', '基本面', '业绩', '估值', '分红', '长期']),
('吕洞宾', '成长股猎手', '前瞻视野，成长导向', ARRAY['成长投资', '新兴机会', '高成长潜力'], ARRAY['成长', '科技', '创新', '新兴', 'AI', '新能源']),
('何仙姑', 'ESG投资专家', '可持续发展，社会责任', ARRAY['ESG投资', '可持续发展', '社会责任'], ARRAY['ESG', '环保', '可持续', '社会责任', '绿色']),
('蓝采和', '量化交易高手', '算法策略，系统化交易', ARRAY['量化交易', '算法策略', '系统化交易'], ARRAY['量化', '算法', '程序化', '高频', '套利']),
('曹国舅', '固收专家', '稳健收益，风险控制', ARRAY['固收投资', '债券分析', '利率研究'], ARRAY['固收', '债券', '利率', '信用', '收益率'])
ON CONFLICT (agent_name) DO NOTHING;

-- 用户互动表（用于记录用户与AI分析师的互动）
CREATE TABLE IF NOT EXISTS user_interactions (
    id SERIAL PRIMARY KEY,
    user_id VARCHAR(100), -- 可以是用户名或ID
    agent_name VARCHAR(50) NOT NULL,
    interaction_type VARCHAR(50) NOT NULL, -- 'question', 'mention', 'reply'
    user_content TEXT NOT NULL,
    agent_response TEXT,
    
    -- 上下文信息
    context JSONB,
    related_debate_ids TEXT[],
    
    -- 满意度评分（用户可选）
    user_rating INTEGER CHECK (user_rating >= 1 AND user_rating <= 5),
    
    -- 时间字段
    interaction_date DATE DEFAULT CURRENT_DATE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_user_interactions_agent ON user_interactions(agent_name);
CREATE INDEX IF NOT EXISTS idx_user_interactions_type ON user_interactions(interaction_type);
CREATE INDEX IF NOT EXISTS idx_user_interactions_date ON user_interactions(interaction_date);
CREATE INDEX IF NOT EXISTS idx_user_interactions_rating ON user_interactions(user_rating);

-- 创建更新时间触发器函数
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 为所有表添加更新时间触发器
CREATE TRIGGER update_market_intelligence_updated_at BEFORE UPDATE ON market_intelligence FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_agent_memories_updated_at BEFORE UPDATE ON agent_memories FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_debate_records_updated_at BEFORE UPDATE ON debate_records FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_daily_consensus_updated_at BEFORE UPDATE ON daily_consensus FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_agent_configs_updated_at BEFORE UPDATE ON agent_configs FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_user_interactions_updated_at BEFORE UPDATE ON user_interactions FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 创建一些有用的视图

-- 每日情报摘要视图
CREATE OR REPLACE VIEW daily_intelligence_summary AS
SELECT 
    date,
    COUNT(*) as total_articles,
    AVG(sentiment) as avg_sentiment,
    COUNT(CASE WHEN sentiment > 0.1 THEN 1 END) as positive_count,
    COUNT(CASE WHEN sentiment BETWEEN -0.1 AND 0.1 THEN 1 END) as neutral_count,
    COUNT(CASE WHEN sentiment < -0.1 THEN 1 END) as negative_count,
    array_agg(DISTINCT source) as sources,
    array_agg(DISTINCT unnest(keywords)) as all_keywords
FROM market_intelligence 
GROUP BY date
ORDER BY date DESC;

-- 分析师活跃度视图
CREATE OR REPLACE VIEW agent_activity_summary AS
SELECT 
    agent_name,
    COUNT(*) as total_memories,
    COUNT(CASE WHEN memory_type = 'debate' THEN 1 END) as debate_count,
    COUNT(CASE WHEN memory_type = 'analysis' THEN 1 END) as analysis_count,
    COUNT(CASE WHEN memory_type = 'reply' THEN 1 END) as reply_count,
    AVG(sentiment) as avg_sentiment,
    MAX(created_at) as last_activity
FROM agent_memories 
GROUP BY agent_name
ORDER BY total_memories DESC;

-- 热门辩论话题视图
CREATE OR REPLACE VIEW popular_debate_topics AS
SELECT 
    topic,
    COUNT(DISTINCT agent_name) as participant_count,
    COUNT(*) as total_arguments,
    AVG(confidence_score) as avg_confidence,
    MAX(created_at) as latest_debate
FROM debate_records 
WHERE debate_date >= CURRENT_DATE - INTERVAL '7 days'
GROUP BY topic
ORDER BY participant_count DESC, total_arguments DESC;

-- 添加注释
COMMENT ON TABLE market_intelligence IS '市场情报表 - 存储从RSS等渠道收集的市场信息';
COMMENT ON TABLE agent_memories IS 'AI分析师记忆表 - 存储各分析师的发言、分析和互动记录';
COMMENT ON TABLE debate_records IS '辩论记录表 - 存储稷下学宫的辩论内容和观点';
COMMENT ON TABLE daily_consensus IS '每日共识表 - 存储AI分析师团队达成的每日市场共识';
COMMENT ON TABLE agent_configs IS '分析师配置表 - 存储各AI分析师的人设和配置信息';
COMMENT ON TABLE user_interactions IS '用户互动表 - 存储用户与AI分析师的互动记录';
