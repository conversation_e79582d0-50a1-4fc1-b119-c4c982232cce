# -*- coding: utf-8 -*-
"""
至尊会员策略API
提供策略列表、源码下载、个性化定制等接口
"""

from fastapi import APIRouter, HTTPException, Depends, Request, Response
from fastapi.responses import StreamingResponse
from pydantic import BaseModel
from typing import List, Dict, Any, Optional
from datetime import datetime
import io
import logging

from ..core.vip_strategy_manager import VIPStrategyManager, MembershipLevel, StrategyCategory
from ..core.member_system import MemberSystem, get_current_user


# 请求/响应模型
class StrategyListRequest(BaseModel):
    membership_level: str
    category: Optional[str] = None
    search_keyword: Optional[str] = None


class StrategyInfoResponse(BaseModel):
    id: str
    name: str
    category: str
    description: str
    author: str
    version: str
    created_at: str
    updated_at: str
    min_membership: str
    performance_metrics: Dict[str, float]
    risk_level: int
    dependencies: List[str]
    is_available: bool


class StrategyDownloadRequest(BaseModel):
    strategy_id: str
    user_id: str
    customization_params: Optional[Dict[str, Any]] = None


class CustomStrategyRequest(BaseModel):
    name: str
    description: str
    category: str
    source_files: Dict[str, str]
    dependencies: List[str]
    risk_level: int = 3
    documentation: str = ""


# 创建路由器
router = APIRouter(prefix="/api/vip/strategies", tags=["VIP策略"])

# 初始化管理器
strategy_manager = VIPStrategyManager()
member_system = MemberSystem()
logger = logging.getLogger("VIPStrategyAPI")


@router.get("/list", response_model=List[StrategyInfoResponse])
async def get_strategy_list(
    membership_level: str,
    category: Optional[str] = None,
    search_keyword: Optional[str] = None,
    current_user: Dict = Depends(get_current_user)
):
    """
    获取策略列表
    
    - **membership_level**: 会员等级 (free/premium/vip)
    - **category**: 策略分类筛选 (可选)
    - **search_keyword**: 搜索关键词 (可选)
    """
    try:
        # 验证会员等级
        try:
            level = MembershipLevel(membership_level)
        except ValueError:
            raise HTTPException(status_code=400, detail="无效的会员等级")
        
        # 获取可用策略
        strategies = strategy_manager.get_available_strategies(level)
        
        # 分类筛选
        if category:
            try:
                cat_filter = StrategyCategory(category)
                strategies = [s for s in strategies if s.category == cat_filter]
            except ValueError:
                raise HTTPException(status_code=400, detail="无效的策略分类")
        
        # 关键词搜索
        if search_keyword:
            keyword = search_keyword.lower()
            strategies = [
                s for s in strategies 
                if keyword in s.name.lower() or keyword in s.description.lower()
            ]
        
        # 转换为响应格式
        response_data = []
        for strategy in strategies:
            response_data.append(StrategyInfoResponse(
                id=strategy.id,
                name=strategy.name,
                category=strategy.category.value,
                description=strategy.description,
                author=strategy.author,
                version=strategy.version,
                created_at=strategy.created_at.isoformat(),
                updated_at=strategy.updated_at.isoformat(),
                min_membership=strategy.min_membership.value,
                performance_metrics=strategy.performance_metrics,
                risk_level=strategy.risk_level,
                dependencies=strategy.dependencies,
                is_available=True
            ))
        
        logger.info(f"用户 {current_user.get('user_id')} 查询策略列表: {len(response_data)} 个结果")
        return response_data
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取策略列表失败: {e}")
        raise HTTPException(status_code=500, detail="服务器内部错误")


@router.get("/download/{strategy_id}")
async def download_strategy(
    strategy_id: str,
    request: Request,
    current_user: Dict = Depends(get_current_user)
):
    """
    下载策略源码
    
    - **strategy_id**: 策略ID
    """
    try:
        # 获取用户信息
        user_id = current_user.get('user_id')
        membership_level = MembershipLevel(current_user.get('membership_level', 'free'))
        ip_address = request.client.host
        
        # 获取策略源码
        result = strategy_manager.get_strategy_source(
            strategy_id=strategy_id,
            user_id=user_id,
            membership_level=membership_level,
            ip_address=ip_address
        )
        
        if not result:
            raise HTTPException(status_code=404, detail="策略不存在或权限不足")
        
        zip_data, filename = result
        
        # 返回文件流
        return StreamingResponse(
            io.BytesIO(zip_data),
            media_type="application/zip",
            headers={"Content-Disposition": f"attachment; filename={filename}"}
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"下载策略失败: {e}")
        raise HTTPException(status_code=500, detail="下载失败")


@router.get("/categories")
async def get_strategy_categories():
    """获取策略分类列表"""
    categories = [
        {
            "value": cat.value,
            "label": {
                "momentum": "动量策略",
                "mean_reversion": "均值回归",
                "arbitrage": "套利策略",
                "options": "期权策略",
                "futures": "期货策略",
                "demon_stock": "妖股识别",
                "market_making": "做市策略",
                "pairs_trading": "配对交易"
            }.get(cat.value, cat.value)
        }
        for cat in StrategyCategory
    ]
    
    return {"categories": categories}


@router.get("/performance/{strategy_id}")
async def get_strategy_performance(
    strategy_id: str,
    current_user: Dict = Depends(get_current_user)
):
    """
    获取策略详细性能数据
    
    - **strategy_id**: 策略ID
    """
    try:
        if strategy_id not in strategy_manager.strategies:
            raise HTTPException(status_code=404, detail="策略不存在")
        
        strategy = strategy_manager.strategies[strategy_id]
        
        # 检查权限
        user_level = MembershipLevel(current_user.get('membership_level', 'free'))
        if not strategy_manager._check_access_permission(strategy, user_level):
            raise HTTPException(status_code=403, detail="权限不足")
        
        # 返回详细性能数据
        performance_data = {
            "basic_metrics": strategy.performance_metrics,
            "risk_metrics": {
                "risk_level": strategy.risk_level,
                "volatility": strategy.performance_metrics.get("volatility", 0.15),
                "beta": strategy.performance_metrics.get("beta", 1.0),
                "var_95": strategy.performance_metrics.get("var_95", -0.05)
            },
            "backtest_period": {
                "start_date": "2023-01-01",
                "end_date": "2024-12-31",
                "total_trades": strategy.performance_metrics.get("total_trades", 1000),
                "win_rate": strategy.performance_metrics.get("win_rate", 0.65)
            },
            "monthly_returns": [
                # 模拟月度收益数据
                {"month": f"2024-{i:02d}", "return": 0.02 + (i % 3 - 1) * 0.01}
                for i in range(1, 13)
            ]
        }
        
        return performance_data
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取策略性能失败: {e}")
        raise HTTPException(status_code=500, detail="服务器内部错误")


@router.post("/custom")
async def create_custom_strategy(
    request: CustomStrategyRequest,
    current_user: Dict = Depends(get_current_user)
):
    """
    创建自定义策略（VIP专享）
    
    - **name**: 策略名称
    - **description**: 策略描述
    - **category**: 策略分类
    - **source_files**: 源码文件字典 {filename: content}
    - **dependencies**: 依赖包列表
    - **risk_level**: 风险等级 (1-5)
    - **documentation**: 文档说明
    """
    try:
        # 检查VIP权限
        user_level = MembershipLevel(current_user.get('membership_level', 'free'))
        if user_level != MembershipLevel.VIP:
            raise HTTPException(status_code=403, detail="仅限至尊会员使用此功能")
        
        # 验证分类
        try:
            StrategyCategory(request.category)
        except ValueError:
            raise HTTPException(status_code=400, detail="无效的策略分类")
        
        # 创建策略
        strategy_info = {
            "name": request.name,
            "description": request.description,
            "category": request.category,
            "author": current_user.get('username', '用户自定义'),
            "dependencies": request.dependencies,
            "risk_level": request.risk_level,
            "documentation": request.documentation,
            "performance_metrics": {}  # 初始为空，需要回测后填充
        }
        
        strategy_id = strategy_manager.add_custom_strategy(
            strategy_info=strategy_info,
            source_files=request.source_files
        )
        
        logger.info(f"用户 {current_user.get('user_id')} 创建自定义策略: {strategy_id}")
        
        return {
            "strategy_id": strategy_id,
            "message": "自定义策略创建成功",
            "next_steps": [
                "请进行策略回测验证",
                "完善策略文档说明",
                "设置风险控制参数"
            ]
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"创建自定义策略失败: {e}")
        raise HTTPException(status_code=500, detail="创建失败")


@router.get("/user/stats")
async def get_user_strategy_stats(
    current_user: Dict = Depends(get_current_user)
):
    """获取用户策略使用统计"""
    try:
        user_id = current_user.get('user_id')
        stats = strategy_manager.get_user_access_stats(user_id)
        
        # 添加会员等级信息
        membership_info = {
            "current_level": current_user.get('membership_level', 'free'),
            "available_strategies": len(strategy_manager.get_available_strategies(
                MembershipLevel(current_user.get('membership_level', 'free'))
            )),
            "total_strategies": len(strategy_manager.strategies)
        }
        
        return {
            "access_stats": stats,
            "membership_info": membership_info,
            "recommendations": [
                "升级至至尊会员获取完整策略源码",
                "参与策略讨论社区",
                "定制个人专属策略"
            ]
        }
        
    except Exception as e:
        logger.error(f"获取用户统计失败: {e}")
        raise HTTPException(status_code=500, detail="服务器内部错误")


@router.get("/health")
async def health_check():
    """健康检查接口"""
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "total_strategies": len(strategy_manager.strategies),
        "active_strategies": len([s for s in strategy_manager.strategies.values() if s.is_active])
    }
