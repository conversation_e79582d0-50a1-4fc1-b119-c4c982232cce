# -*- coding: utf-8 -*-
"""
炼妖壶MCP工具API
为Heroku MCP Agent提供可调用的工具接口
"""

from fastapi import APIRouter, HTTPException, Request
from fastapi.responses import JSONResponse
from pydantic import BaseModel
from typing import Dict, Any, List, Optional
from datetime import datetime
import logging
import asyncio
import os
import json

# 配置日志
logger = logging.getLogger("MCPToolsAPI")

# 创建路由器
router = APIRouter(prefix="/api/mcp", tags=["MCP工具"])


# 请求/响应模型
class MCPToolRequest(BaseModel):
    tool_name: str
    parameters: Dict[str, Any] = {}
    user_context: Optional[Dict[str, Any]] = None


class MCPToolResponse(BaseModel):
    success: bool
    data: Any
    message: str
    timestamp: str
    tool_name: str


class MarketSentimentRequest(BaseModel):
    source: Optional[str] = "all"  # all, rss, social, news
    timeframe: Optional[str] = "1d"  # 1h, 4h, 1d, 1w
    symbols: Optional[List[str]] = None


class TradingSignalRequest(BaseModel):
    symbol: str
    strategy: Optional[str] = "default"
    timeframe: Optional[str] = "1d"
    risk_level: Optional[int] = 3


# MCP工具注册表
MCP_TOOLS = {
    "market_sentiment": {
        "name": "market_sentiment",
        "description": "分析当前市场情绪和趋势",
        "parameters": {
            "source": {"type": "string", "description": "数据源: all, rss, social, news"},
            "timeframe": {"type": "string", "description": "时间范围: 1h, 4h, 1d, 1w"},
            "symbols": {"type": "array", "description": "股票代码列表（可选）"}
        },
        "required": [],
        "category": "analysis"
    },
    "trading_signal": {
        "name": "trading_signal", 
        "description": "生成交易信号和建议",
        "parameters": {
            "symbol": {"type": "string", "description": "股票代码"},
            "strategy": {"type": "string", "description": "策略类型"},
            "timeframe": {"type": "string", "description": "时间周期"},
            "risk_level": {"type": "integer", "description": "风险等级 1-5"}
        },
        "required": ["symbol"],
        "category": "trading"
    },
    "portfolio_analysis": {
        "name": "portfolio_analysis",
        "description": "分析投资组合风险和收益",
        "parameters": {
            "holdings": {"type": "array", "description": "持仓列表"},
            "benchmark": {"type": "string", "description": "基准指数"}
        },
        "required": ["holdings"],
        "category": "portfolio"
    },
    "risk_assessment": {
        "name": "risk_assessment",
        "description": "评估投资风险和建议",
        "parameters": {
            "symbol": {"type": "string", "description": "股票代码"},
            "position_size": {"type": "number", "description": "仓位大小"},
            "time_horizon": {"type": "string", "description": "投资期限"}
        },
        "required": ["symbol"],
        "category": "risk"
    },
    "news_impact": {
        "name": "news_impact",
        "description": "分析新闻对市场的影响",
        "parameters": {
            "news_text": {"type": "string", "description": "新闻内容"},
            "symbols": {"type": "array", "description": "相关股票代码"}
        },
        "required": ["news_text"],
        "category": "analysis"
    }
}


@router.get("/tools")
async def list_mcp_tools():
    """列出所有可用的MCP工具"""
    return {
        "tools": list(MCP_TOOLS.values()),
        "total_count": len(MCP_TOOLS),
        "categories": list(set(tool["category"] for tool in MCP_TOOLS.values())),
        "timestamp": datetime.now().isoformat()
    }


@router.get("/tools/{tool_name}")
async def get_tool_info(tool_name: str):
    """获取特定工具的详细信息"""
    if tool_name not in MCP_TOOLS:
        raise HTTPException(status_code=404, detail=f"工具 {tool_name} 不存在")
    
    return {
        "tool": MCP_TOOLS[tool_name],
        "timestamp": datetime.now().isoformat()
    }


@router.post("/tools/{tool_name}/call")
async def call_mcp_tool(tool_name: str, request: MCPToolRequest):
    """调用指定的MCP工具"""
    try:
        if tool_name not in MCP_TOOLS:
            raise HTTPException(status_code=404, detail=f"工具 {tool_name} 不存在")
        
        # 根据工具名称调用相应的处理函数
        if tool_name == "market_sentiment":
            result = await handle_market_sentiment(request.parameters)
        elif tool_name == "trading_signal":
            result = await handle_trading_signal(request.parameters)
        elif tool_name == "portfolio_analysis":
            result = await handle_portfolio_analysis(request.parameters)
        elif tool_name == "risk_assessment":
            result = await handle_risk_assessment(request.parameters)
        elif tool_name == "news_impact":
            result = await handle_news_impact(request.parameters)
        else:
            raise HTTPException(status_code=501, detail=f"工具 {tool_name} 尚未实现")
        
        return MCPToolResponse(
            success=True,
            data=result,
            message=f"工具 {tool_name} 执行成功",
            timestamp=datetime.now().isoformat(),
            tool_name=tool_name
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"调用工具 {tool_name} 失败: {e}")
        return MCPToolResponse(
            success=False,
            data=None,
            message=f"工具执行失败: {str(e)}",
            timestamp=datetime.now().isoformat(),
            tool_name=tool_name
        )


# 工具处理函数
async def handle_market_sentiment(params: Dict[str, Any]) -> Dict[str, Any]:
    """处理市场情绪分析"""
    source = params.get("source", "all")
    timeframe = params.get("timeframe", "1d")
    symbols = params.get("symbols", [])

    # 从Zilliz唯一真理来源获取数据
    zilliz_data = await get_zilliz_market_intelligence()

    if zilliz_data and "summary" in zilliz_data:
        # 基于真实数据构建情绪分析
        summary = zilliz_data["summary"]
        sentiment_data = {
            "overall_sentiment": _classify_sentiment(summary.get("average_sentiment", 0)),
            "sentiment_score": summary.get("average_sentiment", 0),
            "confidence": 0.85,  # 基于Zilliz数据的高置信度
            "key_factors": _extract_key_factors(zilliz_data.get("data", [])),
            "sector_sentiment": _analyze_sector_sentiment(zilliz_data.get("data", [])),
            "timeframe": timeframe,
            "data_sources": summary.get("data_sources", ["rss"]),
            "last_updated": datetime.now().isoformat(),
            "intelligence_count": zilliz_data.get("intelligence_count", 0),
            "autogen_consensus": await get_autogen_consensus()
        }
    else:
        # 降级到模拟数据
        sentiment_data = {
            "overall_sentiment": "neutral",
            "sentiment_score": 0.15,
            "confidence": 0.75,
            "key_factors": [
                "宏观经济数据平稳",
                "市场流动性充足",
                "地缘政治风险可控"
            ],
            "sector_sentiment": {
                "technology": 0.3,
                "finance": -0.1,
                "healthcare": 0.2,
                "energy": -0.2
            },
            "timeframe": timeframe,
            "data_sources": [source] if source != "all" else ["rss", "social", "news"],
            "last_updated": datetime.now().isoformat(),
            "data_source": "fallback"
        }

    if symbols:
        sentiment_data["symbol_sentiment"] = {
            symbol: {"score": 0.1, "trend": "neutral"} for symbol in symbols
        }

    return sentiment_data


async def get_zilliz_market_intelligence() -> Optional[Dict]:
    """从Zilliz获取市场情报"""
    try:
        # 这里应该调用Zilliz API或直接连接
        # 暂时从文件读取（实际部署时应该是实时连接）
        import os
        intelligence_file = "zilliz_daily_intelligence.json"

        if os.path.exists(intelligence_file):
            with open(intelligence_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        else:
            logger.warning("⚠️ Zilliz情报文件不存在，使用模拟数据")
            return None

    except Exception as e:
        logger.error(f"❌ 获取Zilliz数据失败: {e}")
        return None


async def get_autogen_consensus() -> Dict:
    """获取AutoGen稷下学宫的共识"""
    try:
        consensus_file = "mcp_daily_knowledge.json"

        if os.path.exists(consensus_file):
            with open(consensus_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
                return data.get("context", {}).get("daily_consensus", {})
        else:
            return {"status": "no_consensus", "message": "稷下学宫今日尚未形成共识"}

    except Exception as e:
        logger.error(f"❌ 获取AutoGen共识失败: {e}")
        return {"error": str(e)}


def _classify_sentiment(score: float) -> str:
    """分类情绪"""
    if score > 0.2:
        return "positive"
    elif score < -0.2:
        return "negative"
    else:
        return "neutral"


def _extract_key_factors(intelligence_data: List[Dict]) -> List[str]:
    """提取关键因素"""
    factors = []

    for item in intelligence_data[:5]:  # 取前5条情报
        content = item.get("content", "")
        if len(content) > 20:  # 过滤太短的内容
            # 简化提取，实际应该用NLP
            factors.append(content[:50] + "..." if len(content) > 50 else content)

    return factors if factors else ["市场数据分析中", "等待更多情报"]


def _analyze_sector_sentiment(intelligence_data: List[Dict]) -> Dict[str, float]:
    """分析板块情绪"""
    sector_keywords = {
        "technology": ["科技", "AI", "人工智能", "芯片", "软件"],
        "finance": ["银行", "保险", "证券", "金融"],
        "healthcare": ["医药", "生物", "医疗", "健康"],
        "energy": ["能源", "石油", "新能源", "电力"],
        "consumer": ["消费", "零售", "食品", "服装"]
    }

    sector_sentiment = {}

    for sector, keywords in sector_keywords.items():
        scores = []
        for item in intelligence_data:
            content = item.get("content", "").lower()
            if any(keyword in content for keyword in keywords):
                scores.append(item.get("sentiment", 0))

        if scores:
            sector_sentiment[sector] = round(sum(scores) / len(scores), 2)
        else:
            sector_sentiment[sector] = 0.0

    return sector_sentiment


async def handle_trading_signal(params: Dict[str, Any]) -> Dict[str, Any]:
    """处理交易信号生成"""
    symbol = params.get("symbol")
    strategy = params.get("strategy", "default")
    timeframe = params.get("timeframe", "1d")
    risk_level = params.get("risk_level", 3)
    
    if not symbol:
        raise ValueError("股票代码不能为空")
    
    # 模拟交易信号生成
    signal_data = {
        "symbol": symbol,
        "signal": "hold",  # buy, sell, hold
        "strength": 0.6,  # 0-1
        "confidence": 0.8,
        "entry_price": 100.0,
        "target_price": 105.0,
        "stop_loss": 95.0,
        "risk_reward_ratio": 2.0,
        "strategy": strategy,
        "timeframe": timeframe,
        "risk_level": risk_level,
        "reasoning": [
            "技术指标显示横盘整理",
            "成交量相对平稳",
            "基本面无重大变化"
        ],
        "generated_at": datetime.now().isoformat()
    }
    
    return signal_data


async def handle_portfolio_analysis(params: Dict[str, Any]) -> Dict[str, Any]:
    """处理投资组合分析"""
    holdings = params.get("holdings", [])
    benchmark = params.get("benchmark", "000300.SH")
    
    if not holdings:
        raise ValueError("持仓列表不能为空")
    
    # 模拟组合分析结果
    analysis_data = {
        "total_value": 1000000.0,
        "total_return": 0.08,
        "annual_return": 0.12,
        "volatility": 0.15,
        "sharpe_ratio": 0.8,
        "max_drawdown": -0.05,
        "beta": 1.1,
        "alpha": 0.02,
        "sector_allocation": {
            "technology": 0.3,
            "finance": 0.25,
            "healthcare": 0.2,
            "consumer": 0.15,
            "others": 0.1
        },
        "risk_metrics": {
            "var_95": -0.03,
            "cvar_95": -0.045,
            "correlation_with_benchmark": 0.85
        },
        "recommendations": [
            "建议适当降低科技股比重",
            "增加防御性资产配置",
            "关注流动性风险"
        ],
        "benchmark": benchmark,
        "analysis_date": datetime.now().isoformat()
    }
    
    return analysis_data


async def handle_risk_assessment(params: Dict[str, Any]) -> Dict[str, Any]:
    """处理风险评估"""
    symbol = params.get("symbol")
    position_size = params.get("position_size", 0.1)
    time_horizon = params.get("time_horizon", "medium")
    
    if not symbol:
        raise ValueError("股票代码不能为空")
    
    # 模拟风险评估结果
    risk_data = {
        "symbol": symbol,
        "overall_risk": "medium",
        "risk_score": 3.2,  # 1-5
        "position_size": position_size,
        "time_horizon": time_horizon,
        "risk_factors": {
            "market_risk": 3.0,
            "liquidity_risk": 2.5,
            "credit_risk": 1.5,
            "operational_risk": 2.0,
            "regulatory_risk": 2.5
        },
        "var_estimates": {
            "1_day": -0.02,
            "1_week": -0.05,
            "1_month": -0.08
        },
        "stress_scenarios": {
            "market_crash": -0.15,
            "sector_rotation": -0.08,
            "liquidity_crisis": -0.12
        },
        "recommendations": [
            "建议设置止损位于-5%",
            "关注市场流动性变化",
            "定期重新评估风险"
        ],
        "assessment_date": datetime.now().isoformat()
    }
    
    return risk_data


async def handle_news_impact(params: Dict[str, Any]) -> Dict[str, Any]:
    """处理新闻影响分析"""
    news_text = params.get("news_text")
    symbols = params.get("symbols", [])
    
    if not news_text:
        raise ValueError("新闻内容不能为空")
    
    # 模拟新闻影响分析
    impact_data = {
        "news_summary": news_text[:200] + "..." if len(news_text) > 200 else news_text,
        "sentiment": "neutral",
        "impact_score": 0.3,  # -1 到 1
        "urgency": "medium",  # low, medium, high
        "affected_sectors": ["technology", "finance"],
        "market_impact": {
            "short_term": "neutral",
            "medium_term": "slightly_positive",
            "long_term": "positive"
        },
        "key_themes": [
            "政策变化",
            "行业发展",
            "公司业绩"
        ],
        "trading_implications": [
            "短期可能出现波动",
            "关注相关板块表现",
            "建议谨慎操作"
        ],
        "analysis_timestamp": datetime.now().isoformat()
    }
    
    if symbols:
        impact_data["symbol_impact"] = {
            symbol: {
                "impact_score": 0.2,
                "direction": "positive",
                "confidence": 0.7
            } for symbol in symbols
        }
    
    return impact_data


@router.get("/health")
async def mcp_health_check():
    """MCP工具健康检查"""
    return {
        "status": "healthy",
        "available_tools": len(MCP_TOOLS),
        "timestamp": datetime.now().isoformat(),
        "version": "1.0.0"
    }
