#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AutoGen Zilliz知识检索器
让稷下学宫的AI分析师能够从Zilliz向量数据库获取今日情报
"""

import json
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
import numpy as np
from pymilvus import connections, Collection, utility

logger = logging.getLogger("ZillizKnowledgeRetriever")


class ZillizKnowledgeRetriever:
    """Zilliz知识检索器 - AutoGen的唯一真理来源"""
    
    def __init__(self, config: Dict):
        self.config = config
        self.connection = None
        self.collections = {}
        self.daily_cache = {}
        self.connect()
    
    def connect(self):
        """连接到Zilliz Cloud"""
        try:
            connections.connect(
                alias="autogen_retriever",
                host=self.config['host'],
                port=self.config['port'],
                user=self.config['user'],
                password=self.config['password'],
                secure=True
            )
            logger.info("✅ AutoGen Zilliz连接成功")
            self._load_collections()
        except Exception as e:
            logger.error(f"❌ Zilliz连接失败: {e}")
            # 降级到本地文件模式
            self._fallback_to_local()
    
    def _load_collections(self):
        """加载向量集合"""
        collection_names = [
            "daily_market_intelligence",
            "rss_sentiment_vectors", 
            "debate_context_memory",
            "agent_knowledge_base"
        ]
        
        for name in collection_names:
            try:
                if utility.has_collection(name):
                    self.collections[name] = Collection(name)
                    logger.info(f"📊 加载集合: {name}")
                else:
                    logger.warning(f"⚠️ 集合 {name} 不存在")
            except Exception as e:
                logger.error(f"❌ 加载集合 {name} 失败: {e}")
    
    def _fallback_to_local(self):
        """降级到本地文件模式"""
        logger.warning("⚠️ 降级到本地文件模式")
        self.connection = None
    
    def get_daily_intelligence_for_agent(self, agent_name: str, date: str = None) -> Dict:
        """为特定分析师获取今日情报"""
        if not date:
            date = datetime.now().strftime("%Y-%m-%d")
        
        # 检查缓存
        cache_key = f"{agent_name}_{date}"
        if cache_key in self.daily_cache:
            return self.daily_cache[cache_key]
        
        try:
            if self.connection:
                intelligence = self._get_from_zilliz(agent_name, date)
            else:
                intelligence = self._get_from_local_file(agent_name, date)
            
            # 根据分析师专长过滤和排序
            filtered_intelligence = self._filter_by_agent_expertise(intelligence, agent_name)
            
            # 缓存结果
            self.daily_cache[cache_key] = filtered_intelligence
            
            return filtered_intelligence
            
        except Exception as e:
            logger.error(f"❌ 获取 {agent_name} 的情报失败: {e}")
            return self._get_fallback_intelligence(agent_name)
    
    def _get_from_zilliz(self, agent_name: str, date: str) -> Dict:
        """从Zilliz获取数据"""
        if "daily_market_intelligence" not in self.collections:
            return {"data": [], "summary": {}}
        
        collection = self.collections["daily_market_intelligence"]
        
        # 查询当日数据
        expr = f'date == "{date}"'
        results = collection.query(
            expr=expr,
            output_fields=["content", "title", "sentiment", "keywords", "source", "timestamp"],
            limit=100
        )
        
        if not results:
            logger.warning(f"⚠️ 未找到 {date} 的数据")
            return {"data": [], "summary": {}}
        
        # 生成摘要
        summary = self._generate_summary(results)
        
        return {
            "date": date,
            "data": results,
            "summary": summary,
            "agent": agent_name,
            "source": "zilliz"
        }
    
    def _get_from_local_file(self, agent_name: str, date: str) -> Dict:
        """从本地文件获取数据"""
        try:
            filename = f"zilliz_daily_intelligence.json"
            with open(filename, 'r', encoding='utf-8') as f:
                data = json.load(f)
                
            if data.get("date") == date:
                return data
            else:
                logger.warning(f"⚠️ 本地文件日期不匹配: {data.get('date')} != {date}")
                return {"data": [], "summary": {}}
                
        except FileNotFoundError:
            logger.warning("⚠️ 本地情报文件不存在")
            return {"data": [], "summary": {}}
        except Exception as e:
            logger.error(f"❌ 读取本地文件失败: {e}")
            return {"data": [], "summary": {}}
    
    def _filter_by_agent_expertise(self, intelligence: Dict, agent_name: str) -> Dict:
        """根据分析师专长过滤情报"""
        if not intelligence.get("data"):
            return intelligence
        
        # 定义各分析师的关注领域
        agent_keywords = {
            "太上老君": ["政策", "宏观", "央行", "利率", "经济", "GDP", "通胀"],
            "元始天尊": ["技术分析", "量化", "指标", "MACD", "RSI", "成交量", "突破"],
            "灵宝天尊": ["风险", "波动", "下跌", "调整", "避险", "防御", "稳健"],
            "铁拐李": ["逆向", "反转", "超跌", "低估", "冷门", "被忽视"],
            "汉钟离": ["趋势", "上涨", "突破", "动量", "强势", "领涨"],
            "张果老": ["价值", "基本面", "业绩", "估值", "分红", "长期"],
            "吕洞宾": ["成长", "科技", "创新", "新兴", "AI", "新能源"],
            "何仙姑": ["ESG", "环保", "可持续", "社会责任", "绿色"],
            "蓝采和": ["量化", "算法", "程序化", "高频", "套利"],
            "曹国舅": ["固收", "债券", "利率", "信用", "收益率"]
        }
        
        keywords = agent_keywords.get(agent_name, [])
        if not keywords:
            return intelligence
        
        # 过滤相关数据
        filtered_data = []
        for item in intelligence["data"]:
            content = item.get("content", "").lower()
            title = item.get("title", "").lower()
            item_keywords = item.get("keywords", [])
            
            # 计算相关性得分
            relevance_score = 0
            for keyword in keywords:
                if keyword.lower() in content or keyword.lower() in title:
                    relevance_score += 2
                if keyword in item_keywords:
                    relevance_score += 3
            
            if relevance_score > 0:
                item["relevance_score"] = relevance_score
                filtered_data.append(item)
        
        # 按相关性排序
        filtered_data.sort(key=lambda x: x.get("relevance_score", 0), reverse=True)
        
        # 更新情报
        filtered_intelligence = intelligence.copy()
        filtered_intelligence["data"] = filtered_data[:20]  # 取前20条最相关的
        filtered_intelligence["filtered_for"] = agent_name
        filtered_intelligence["total_relevant"] = len(filtered_data)
        
        return filtered_intelligence
    
    def _generate_summary(self, data: List[Dict]) -> Dict:
        """生成数据摘要"""
        if not data:
            return {}
        
        # 情绪统计
        sentiments = [item.get("sentiment", 0) for item in data if "sentiment" in item]
        avg_sentiment = np.mean(sentiments) if sentiments else 0
        
        # 关键词统计
        keyword_count = {}
        for item in data:
            keywords = item.get("keywords", [])
            for keyword in keywords:
                keyword_count[keyword] = keyword_count.get(keyword, 0) + 1
        
        top_keywords = sorted(keyword_count.items(), key=lambda x: x[1], reverse=True)[:10]
        
        # 来源统计
        sources = {}
        for item in data:
            source = item.get("source", "unknown")
            sources[source] = sources.get(source, 0) + 1
        
        return {
            "total_items": len(data),
            "average_sentiment": round(avg_sentiment, 3),
            "sentiment_distribution": {
                "positive": len([s for s in sentiments if s > 0.1]),
                "neutral": len([s for s in sentiments if -0.1 <= s <= 0.1]),
                "negative": len([s for s in sentiments if s < -0.1])
            },
            "top_keywords": top_keywords,
            "sources": sources,
            "generated_at": datetime.now().isoformat()
        }
    
    def _get_fallback_intelligence(self, agent_name: str) -> Dict:
        """获取降级情报"""
        return {
            "date": datetime.now().strftime("%Y-%m-%d"),
            "data": [],
            "summary": {
                "total_items": 0,
                "average_sentiment": 0.0,
                "message": f"暂无 {agent_name} 相关的市场情报"
            },
            "agent": agent_name,
            "source": "fallback"
        }
    
    def search_historical_debates(self, topic: str, agent_name: str = None, limit: int = 5) -> List[Dict]:
        """搜索历史辩论记录"""
        try:
            if "debate_context_memory" not in self.collections:
                return []
            
            collection = self.collections["debate_context_memory"]
            
            # 构建查询条件
            expr_parts = [f'topic like "%{topic}%"']
            if agent_name:
                expr_parts.append(f'agent_name == "{agent_name}"')
            
            expr = " and ".join(expr_parts)
            
            results = collection.query(
                expr=expr,
                output_fields=["topic", "position", "arguments", "timestamp", "agent_name"],
                limit=limit
            )
            
            return results
            
        except Exception as e:
            logger.error(f"❌ 搜索历史辩论失败: {e}")
            return []
    
    def get_agent_knowledge_context(self, agent_name: str, query: str) -> Dict:
        """获取分析师知识上下文"""
        try:
            # 获取今日情报
            daily_intel = self.get_daily_intelligence_for_agent(agent_name)
            
            # 搜索相关历史辩论
            historical_debates = self.search_historical_debates(query, agent_name)
            
            # 构建上下文
            context = {
                "agent_name": agent_name,
                "query": query,
                "daily_intelligence": daily_intel,
                "historical_debates": historical_debates,
                "context_generated_at": datetime.now().isoformat()
            }
            
            return context
            
        except Exception as e:
            logger.error(f"❌ 获取 {agent_name} 知识上下文失败: {e}")
            return {
                "agent_name": agent_name,
                "query": query,
                "error": str(e)
            }
    
    def update_debate_memory(self, debate_record: Dict):
        """更新辩论记忆"""
        try:
            if "debate_context_memory" not in self.collections:
                logger.warning("⚠️ 辩论记忆集合不存在")
                return False
            
            collection = self.collections["debate_context_memory"]
            
            # 插入辩论记录
            collection.insert([debate_record])
            collection.flush()
            
            logger.info(f"✅ 辩论记忆已更新: {debate_record.get('topic', 'unknown')}")
            return True
            
        except Exception as e:
            logger.error(f"❌ 更新辩论记忆失败: {e}")
            return False
    
    def clear_daily_cache(self):
        """清理每日缓存"""
        self.daily_cache.clear()
        logger.info("🧹 每日缓存已清理")


# AutoGen集成函数
def create_zilliz_retriever_for_autogen(config: Dict) -> ZillizKnowledgeRetriever:
    """为AutoGen创建Zilliz检索器"""
    return ZillizKnowledgeRetriever(config)


def get_daily_context_for_debate(retriever: ZillizKnowledgeRetriever, 
                                participants: List[str]) -> Dict:
    """为辩论获取每日上下文"""
    context = {
        "date": datetime.now().strftime("%Y-%m-%d"),
        "participants": participants,
        "intelligence_by_agent": {},
        "common_topics": [],
        "debate_suggestions": []
    }
    
    # 为每个参与者获取情报
    all_keywords = []
    for agent in participants:
        intel = retriever.get_daily_intelligence_for_agent(agent)
        context["intelligence_by_agent"][agent] = intel
        
        # 收集关键词
        if intel.get("summary", {}).get("top_keywords"):
            all_keywords.extend([kw[0] for kw in intel["summary"]["top_keywords"][:5]])
    
    # 找出共同话题
    keyword_count = {}
    for keyword in all_keywords:
        keyword_count[keyword] = keyword_count.get(keyword, 0) + 1
    
    common_topics = [kw for kw, count in keyword_count.items() if count >= 2]
    context["common_topics"] = common_topics[:5]
    
    # 生成辩论建议
    if common_topics:
        context["debate_suggestions"] = [
            f"关于{topic}的市场影响分析" for topic in common_topics[:3]
        ]
    
    return context


# 配置示例
ZILLIZ_CONFIG = {
    "host": "your-zilliz-host",
    "port": 19530,
    "user": "your-username",
    "password": "your-password"
}
