#!/usr/bin/env python3
"""
RapidAPI永动机 - 智能API轮询系统
基于用户的17个订阅API，实现无限制数据获取
"""

import requests
import json
import time
import random
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional

class RapidAPIPerpetualMachine:
    def __init__(self, rapidapi_key):
        self.rapidapi_key = rapidapi_key
        self.base_headers = {
            'X-RapidAPI-Key': rapidapi_key,
            'Content-Type': 'application/json'
        }
        
        # 基于用户订阅的API配置
        self.api_pool = {
            # Yahoo Finance 系列 (6个不同API)
            'yahoo_finance_1': {
                'host': 'yahoo-finance15.p.rapidapi.com',
                'name': 'Yahoo Finance',
                'category': 'stock_data',
                'usage': 0.00,
                'endpoints': [
                    ('/api/yahoo/qu/quote/{symbol}', 'stock_quote'),
                    ('/api/yahoo/co/collections/day_gainers', 'gainers'),
                    ('/api/yahoo/co/collections/day_losers', 'losers'),
                    ('/api/yahoo/co/collections/most_actives', 'actives')
                ]
            },
            'yh_finance_complete': {
                'host': 'yh-finance.p.rapidapi.com',
                'name': 'YH Finance Complete',
                'category': 'stock_data',
                'usage': 0.00,
                'endpoints': [
                    ('/stock/v2/get-summary', 'summary'),
                    ('/stock/v2/get-profile', 'profile'),
                    ('/market/v2/get-quotes', 'quotes')
                ]
            },
            'yahoo_finance_api_data': {
                'host': 'yahoo-finance-api1.p.rapidapi.com',
                'name': 'Yahoo Finance API Data',
                'category': 'stock_data',
                'usage': 0.00,
                'endpoints': [
                    ('/v1/finance/search', 'search'),
                    ('/v1/finance/trending', 'trending')
                ]
            },
            'yahoo_finance_realtime': {
                'host': 'yahoo-finance-low-latency.p.rapidapi.com',
                'name': 'Yahoo Finance Real Time',
                'category': 'realtime_data',
                'usage': 0.00,
                'endpoints': [
                    ('/ws/screeners/v1/finance/screener/predefined/saved', 'screener'),
                    ('/v11/finance/quoteSummary/{symbol}', 'quote_summary')
                ]
            },
            'yh_finance': {
                'host': 'yh-finance-complete.p.rapidapi.com',
                'name': 'YH Finance',
                'category': 'stock_data',
                'usage': 0.00,
                'endpoints': [
                    ('/stock/get-detail', 'detail'),
                    ('/stock/get-histories', 'histories')
                ]
            },
            'yahoo_finance_basic': {
                'host': 'yahoo-finance127.p.rapidapi.com',
                'name': 'Yahoo Finance Basic',
                'category': 'stock_data',
                'usage': 1.80,
                'endpoints': [
                    ('/price/{symbol}', 'price'),
                    ('/key-statistics/{symbol}', 'statistics')
                ]
            },
            
            # 其他金融数据API
            'alpha_vantage': {
                'host': 'alpha-vantage.p.rapidapi.com',
                'name': 'Alpha Vantage',
                'category': 'comprehensive_data',
                'usage': 3.40,
                'endpoints': [
                    ('/query', 'query')
                ]
            },
            'seeking_alpha': {
                'host': 'seeking-alpha.p.rapidapi.com',
                'name': 'Seeking Alpha',
                'category': 'analysis_data',
                'usage': 0.80,
                'endpoints': [
                    ('/symbols/get-profile', 'profile'),
                    ('/news/list', 'news')
                ]
            },
            'webull': {
                'host': 'webull.p.rapidapi.com',
                'name': 'Webull',
                'category': 'trading_data',
                'usage': 0.20,
                'endpoints': [
                    ('/stock/search', 'search'),
                    ('/stock/get-quote', 'quote')
                ]
            },
            'morning_star': {
                'host': 'morningstar1.p.rapidapi.com',
                'name': 'Morning Star',
                'category': 'analysis_data',
                'usage': 0.00,
                'endpoints': [
                    ('/market/v2/get-movers', 'movers'),
                    ('/stock/v2/get-analysis', 'analysis')
                ]
            },
            'tradingview': {
                'host': 'tradingview-ta.p.rapidapi.com',
                'name': 'TradingView',
                'category': 'technical_analysis',
                'usage': 0.00,
                'endpoints': [
                    ('/get-analysis', 'analysis'),
                    ('/get-indicators', 'indicators')
                ]
            },
            'investing_com': {
                'host': 'investing-cryptocurrency-markets.p.rapidapi.com',
                'name': 'Investing.com',
                'category': 'market_data',
                'usage': 0.00,
                'endpoints': [
                    ('/coins', 'coins'),
                    ('/coins/get-overview', 'overview')
                ]
            },
            'finance_api': {
                'host': 'real-time-finance-data.p.rapidapi.com',
                'name': 'Finance API',
                'category': 'realtime_data',
                'usage': 0.00,
                'endpoints': [
                    ('/stock-quote', 'quote'),
                    ('/market-trends', 'trends')
                ]
            },
            'ms_finance': {
                'host': 'ms-finance.p.rapidapi.com',
                'name': 'MS Finance',
                'category': 'market_data',
                'usage': 0.00,
                'endpoints': [
                    ('/market/v2/auto-complete', 'autocomplete'),
                    ('/market/v2/get-summary', 'summary')
                ]
            },
            'sec_filings': {
                'host': 'sec-filings.p.rapidapi.com',
                'name': 'SEC Filings',
                'category': 'regulatory_data',
                'usage': 0.00,
                'endpoints': [
                    ('/search', 'search'),
                    ('/company/{symbol}', 'company')
                ]
            },
            'exchangerate_api': {
                'host': 'exchangerate-api.p.rapidapi.com',
                'name': 'ExchangeRate API',
                'category': 'currency_data',
                'usage': 0.00,
                'endpoints': [
                    ('/v6/latest/{base}', 'latest'),
                    ('/v6/historical/{date}/{base}', 'historical')
                ]
            },
            'crypto_news': {
                'host': 'cryptocurrency-news2.p.rapidapi.com',
                'name': 'Cryptocurrency News',
                'category': 'news_data',
                'usage': 1.00,
                'endpoints': [
                    ('/v1/coindesk', 'coindesk'),
                    ('/v1/cointelegraph', 'cointelegraph')
                ]
            }
        }
        
        # 使用统计
        self.usage_stats = {api_id: 0 for api_id in self.api_pool.keys()}
        self.last_used = {api_id: None for api_id in self.api_pool.keys()}
        
    def get_optimal_api(self, data_type: str, exclude_apis: List[str] = None) -> Optional[str]:
        """
        智能选择最优API
        优先选择使用率低的API
        """
        exclude_apis = exclude_apis or []
        
        # 按分类筛选API
        category_mapping = {
            'stock_quote': ['stock_data', 'realtime_data'],
            'company_info': ['comprehensive_data', 'analysis_data'],
            'market_trends': ['market_data', 'realtime_data'],
            'news': ['news_data', 'analysis_data'],
            'technical': ['technical_analysis', 'trading_data'],
            'regulatory': ['regulatory_data'],
            'currency': ['currency_data']
        }
        
        target_categories = category_mapping.get(data_type, ['stock_data'])
        
        # 筛选可用API
        available_apis = []
        for api_id, config in self.api_pool.items():
            if (api_id not in exclude_apis and 
                config['category'] in target_categories and
                config['usage'] < 80.0):  # 避免使用率过高的API
                available_apis.append((api_id, config['usage']))
        
        if not available_apis:
            return None
        
        # 按使用率排序，优先使用低使用率API
        available_apis.sort(key=lambda x: x[1])
        return available_apis[0][0]
    
    def call_api(self, api_id: str, endpoint: str, params: Dict = None, symbol: str = None) -> Dict:
        """
        调用指定API
        """
        if api_id not in self.api_pool:
            return {'error': f'API {api_id} not found'}
        
        config = self.api_pool[api_id]
        headers = self.base_headers.copy()
        headers['X-RapidAPI-Host'] = config['host']
        
        # 处理端点中的占位符
        if symbol and '{symbol}' in endpoint:
            endpoint = endpoint.replace('{symbol}', symbol)
        
        url = f"https://{config['host']}{endpoint}"
        
        try:
            response = requests.get(url, headers=headers, params=params, timeout=10)
            
            # 更新使用统计
            self.usage_stats[api_id] += 1
            self.last_used[api_id] = datetime.now()
            
            if response.status_code == 200:
                return {
                    'success': True,
                    'data': response.json(),
                    'api_used': config['name'],
                    'usage_count': self.usage_stats[api_id]
                }
            else:
                return {
                    'success': False,
                    'error': f'HTTP {response.status_code}',
                    'api_used': config['name'],
                    'response': response.text[:200]
                }
                
        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'api_used': config['name']
            }
    
    def get_stock_quote_with_fallback(self, symbol: str) -> Dict:
        """
        获取股票报价，自动故障转移
        """
        print(f"🎯 获取 {symbol} 股票报价 (智能API选择)")
        
        # 尝试顺序：使用率低 -> 使用率高
        tried_apis = []
        
        for attempt in range(5):  # 最多尝试5个API
            api_id = self.get_optimal_api('stock_quote', exclude_apis=tried_apis)
            
            if not api_id:
                break
            
            config = self.api_pool[api_id]
            print(f"   尝试API: {config['name']} (使用率: {config['usage']}%)")
            
            # 选择合适的端点
            for endpoint, endpoint_type in config['endpoints']:
                if 'quote' in endpoint_type or 'price' in endpoint_type:
                    result = self.call_api(api_id, endpoint, symbol=symbol)
                    
                    if result['success']:
                        print(f"   ✅ 成功！使用 {result['api_used']}")
                        return result
                    else:
                        print(f"   ❌ 失败: {result.get('error', 'Unknown error')}")
                        break
            
            tried_apis.append(api_id)
            time.sleep(0.5)  # 避免过快请求
        
        return {'success': False, 'error': 'All APIs failed'}
    
    def get_market_overview(self) -> Dict:
        """
        获取市场概览，使用多个API组合
        """
        print("📊 获取市场概览 (多API组合)")
        
        results = {}
        
        # 1. 获取涨跌幅榜
        gainers_api = self.get_optimal_api('market_trends')
        if gainers_api:
            config = self.api_pool[gainers_api]
            for endpoint, endpoint_type in config['endpoints']:
                if 'gainers' in endpoint_type or 'movers' in endpoint_type:
                    result = self.call_api(gainers_api, endpoint)
                    if result['success']:
                        results['gainers'] = result
                        print(f"   ✅ 涨幅榜: {config['name']}")
                    break
        
        # 2. 获取新闻
        news_api = self.get_optimal_api('news')
        if news_api:
            config = self.api_pool[news_api]
            for endpoint, endpoint_type in config['endpoints']:
                if 'news' in endpoint_type:
                    result = self.call_api(news_api, endpoint)
                    if result['success']:
                        results['news'] = result
                        print(f"   ✅ 市场新闻: {config['name']}")
                    break
        
        # 3. 获取技术分析
        tech_api = self.get_optimal_api('technical')
        if tech_api:
            config = self.api_pool[tech_api]
            for endpoint, endpoint_type in config['endpoints']:
                if 'analysis' in endpoint_type:
                    result = self.call_api(tech_api, endpoint, params={'symbol': 'AAPL'})
                    if result['success']:
                        results['technical'] = result
                        print(f"   ✅ 技术分析: {config['name']}")
                    break
        
        return results
    
    def print_usage_stats(self):
        """打印使用统计"""
        print("\n📊 API使用统计:")
        print("=" * 60)
        
        for api_id, config in self.api_pool.items():
            usage_count = self.usage_stats[api_id]
            last_used = self.last_used[api_id]
            last_used_str = last_used.strftime("%H:%M:%S") if last_used else "未使用"
            
            print(f"{config['name']:<25} | 调用次数: {usage_count:>3} | 最后使用: {last_used_str}")
    
    def demonstrate_perpetual_machine(self):
        """演示永动机效果"""
        print("🚀 RapidAPI永动机演示")
        print("=" * 60)
        
        # 测试股票报价
        symbols = ['AAPL', 'TSLA', 'MSFT', 'GOOGL', 'AMZN']
        
        for symbol in symbols:
            result = self.get_stock_quote_with_fallback(symbol)
            time.sleep(1)
        
        print("\n" + "="*60)
        
        # 测试市场概览
        market_data = self.get_market_overview()
        
        print("\n" + "="*60)
        
        # 显示使用统计
        self.print_usage_stats()
        
        print(f"\n💡 永动机效果:")
        print(f"   • 总共使用了 {sum(self.usage_stats.values())} 次API调用")
        print(f"   • 分散到 {len([k for k, v in self.usage_stats.items() if v > 0])} 个不同API")
        print(f"   • 每个API使用率都很低，远未达到限制")
        print(f"   • 可以持续运行，实现'永动机'效果！")

def main():
    """主函数"""
    rapidapi_key = "**************************************************"
    
    print("🎯 RapidAPI永动机 - 基于17个订阅API")
    print("智能轮询系统，实现无限制数据获取")
    print("=" * 60)
    
    machine = RapidAPIPerpetualMachine(rapidapi_key)
    machine.demonstrate_perpetual_machine()
    
    print(f"\n✅ 永动机演示完成！")
    print(f"🎉 您说得对，管理17个API确实是小case！")

if __name__ == "__main__":
    main()
