#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
韭菜小剧场安装脚本
确保所有依赖正确安装
"""

import subprocess
import sys
import os
from pathlib import Path

def install_package(package):
    """安装Python包"""
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        print(f"✅ {package} 安装成功")
        return True
    except subprocess.CalledProcessError:
        print(f"❌ {package} 安装失败")
        return False

def check_package(package):
    """检查包是否已安装"""
    try:
        __import__(package)
        print(f"✅ {package} 已安装")
        return True
    except ImportError:
        print(f"⚠️ {package} 未安装")
        return False

def main():
    """主安装流程"""
    print("🎭 韭菜小剧场安装程序")
    print("=" * 50)
    
    # 必需的包
    required_packages = [
        "streamlit",
        "aiohttp",
        "asyncio"  # Python内置，但检查一下
    ]
    
    # 可选的包（用于增强功能）
    optional_packages = [
        "plotly",
        "pandas"
    ]
    
    print("📦 检查必需依赖...")
    
    missing_packages = []
    
    for package in required_packages:
        if package == "asyncio":
            # asyncio是Python内置模块
            try:
                import asyncio
                print(f"✅ asyncio 可用")
            except ImportError:
                print(f"❌ asyncio 不可用（这很奇怪，应该是Python内置的）")
        else:
            if not check_package(package):
                missing_packages.append(package)
    
    # 安装缺失的包
    if missing_packages:
        print(f"\n📥 安装缺失的包: {', '.join(missing_packages)}")
        
        for package in missing_packages:
            install_package(package)
    else:
        print("\n✅ 所有必需依赖都已安装")
    
    # 检查可选包
    print(f"\n📦 检查可选依赖...")
    
    for package in optional_packages:
        if not check_package(package):
            print(f"💡 建议安装 {package} 以获得更好的体验")
    
    # 检查OpenRouter API Key
    print(f"\n🔑 检查API配置...")
    
    api_keys = [
        os.getenv('OPENROUTER_API_KEY'),
        os.getenv('OPENROUTER_API_KEY_1'),
        os.getenv('OPENROUTER_API_KEY_2'),
        os.getenv('OPENROUTER_API_KEY_3'),
        os.getenv('OPENROUTER_API_KEY_4')
    ]
    
    available_keys = [key for key in api_keys if key]
    
    if available_keys:
        print(f"✅ 检测到 {len(available_keys)} 个OpenRouter API Key")
    else:
        print("⚠️ 未检测到OpenRouter API Key")
        print("💡 设置环境变量以启用真实AI模拟:")
        print("   export OPENROUTER_API_KEY='your_api_key_here'")
        print("   或在 .env 文件中添加: OPENROUTER_API_KEY=your_api_key_here")
    
    # 检查项目文件
    print(f"\n📁 检查项目文件...")
    
    required_files = [
        "src/core/simple_retail_theater.py",
        "src/ui/simple_theater_ui.py",
        "test_theater.py"
    ]
    
    all_files_exist = True
    
    for file_path in required_files:
        if Path(file_path).exists():
            print(f"✅ {file_path}")
        else:
            print(f"❌ {file_path} 不存在")
            all_files_exist = False
    
    # 创建必要的目录
    directories = [
        "src",
        "src/core", 
        "src/ui"
    ]
    
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
        
        # 创建__init__.py文件
        init_file = Path(directory) / "__init__.py"
        if not init_file.exists():
            init_file.touch()
            print(f"✅ 创建 {init_file}")
    
    # 安装总结
    print(f"\n" + "=" * 50)
    print("📋 安装总结:")
    
    if all_files_exist:
        print("✅ 所有项目文件都存在")
    else:
        print("⚠️ 部分项目文件缺失，请检查项目结构")
    
    if not missing_packages:
        print("✅ 所有必需依赖都已安装")
    else:
        print("⚠️ 部分依赖可能安装失败，请手动检查")
    
    if available_keys:
        print("✅ API配置正常")
    else:
        print("⚠️ 需要配置OpenRouter API Key")
    
    print(f"\n🚀 安装完成！运行以下命令启动韭菜小剧场:")
    print("   streamlit run test_theater.py")
    print("   或")
    print("   streamlit run cauldron_app.py")

if __name__ == "__main__":
    main()
