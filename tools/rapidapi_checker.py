#!/usr/bin/env python3
"""
RapidAPI订阅检查器
测试常用的RapidAPI服务来确定订阅状态
"""

import requests
import json
import time

class RapidAPIChecker:
    def __init__(self, api_key):
        self.api_key = api_key
        self.headers = {
            'X-RapidAPI-Key': api_key,
            'X-RapidAPI-Host': '',  # 会根据API动态设置
            'Content-Type': 'application/json'
        }
    
    def test_api(self, host, endpoint, params=None, method='GET'):
        """测试特定的RapidAPI服务"""
        self.headers['X-RapidAPI-Host'] = host
        url = f"https://{host}{endpoint}"
        
        try:
            if method == 'GET':
                response = requests.get(url, headers=self.headers, params=params, timeout=10)
            else:
                response = requests.post(url, headers=self.headers, json=params, timeout=10)
            
            return {
                'status_code': response.status_code,
                'success': response.status_code == 200,
                'response': response.text[:500],  # 限制响应长度
                'headers': dict(response.headers)
            }
        except Exception as e:
            return {
                'status_code': None,
                'success': False,
                'error': str(e),
                'response': None
            }
    
    def check_common_apis(self):
        """检查常用的RapidAPI服务"""
        print("🔍 检查RapidAPI订阅状态")
        print("=" * 60)
        
        # 常用的RapidAPI服务列表
        apis_to_test = [
            {
                'name': 'Alpha Vantage (股票数据)',
                'host': 'alpha-vantage.p.rapidapi.com',
                'endpoint': '/query',
                'params': {'function': 'GLOBAL_QUOTE', 'symbol': 'AAPL'}
            },
            {
                'name': 'Yahoo Finance (财经数据)',
                'host': 'yahoo-finance15.p.rapidapi.com',
                'endpoint': '/api/yahoo/qu/quote/AAPL'
            },
            {
                'name': 'Financial Modeling Prep',
                'host': 'financialmodelingprep.com',
                'endpoint': '/api/v3/profile/AAPL'
            },
            {
                'name': 'News API',
                'host': 'newsapi.org',
                'endpoint': '/v2/everything',
                'params': {'q': 'tesla', 'pageSize': 1}
            },
            {
                'name': 'Polygon.io (股票数据)',
                'host': 'polygon-io.p.rapidapi.com',
                'endpoint': '/v2/aggs/ticker/AAPL/prev'
            },
            {
                'name': 'Twelve Data (金融数据)',
                'host': 'twelve-data1.p.rapidapi.com',
                'endpoint': '/quote',
                'params': {'symbol': 'AAPL'}
            },
            {
                'name': 'IEX Cloud (股票数据)',
                'host': 'cloud.iexapis.com',
                'endpoint': '/stable/stock/aapl/quote'
            },
            {
                'name': 'Finnhub (股票数据)',
                'host': 'finnhub.io',
                'endpoint': '/api/v1/quote',
                'params': {'symbol': 'AAPL'}
            },
            {
                'name': 'Quandl (金融数据)',
                'host': 'www.quandl.com',
                'endpoint': '/api/v3/datasets/WIKI/AAPL.json',
                'params': {'rows': 1}
            },
            {
                'name': 'CoinGecko (加密货币)',
                'host': 'api.coingecko.com',
                'endpoint': '/api/v3/simple/price',
                'params': {'ids': 'bitcoin', 'vs_currencies': 'usd'}
            }
        ]
        
        results = []
        
        for api in apis_to_test:
            print(f"\n📊 测试: {api['name']}")
            print(f"   主机: {api['host']}")
            
            result = self.test_api(
                host=api['host'],
                endpoint=api['endpoint'],
                params=api.get('params')
            )
            
            if result['success']:
                print(f"   ✅ 可访问 (状态码: {result['status_code']})")
                if 'X-RateLimit' in str(result['headers']):
                    print(f"   📈 检测到速率限制信息")
            elif result['status_code'] == 401:
                print(f"   🔑 需要认证 (可能已订阅但需要正确配置)")
            elif result['status_code'] == 403:
                print(f"   🚫 访问被拒绝 (可能未订阅)")
            elif result['status_code'] == 429:
                print(f"   ⏰ 速率限制 (已订阅但达到限制)")
            else:
                print(f"   ❌ 无法访问 (状态码: {result.get('status_code', 'N/A')})")
                if result.get('error'):
                    print(f"   错误: {result['error']}")
            
            # 显示部分响应内容
            if result['response'] and len(result['response']) > 10:
                response_preview = result['response'][:100] + "..." if len(result['response']) > 100 else result['response']
                print(f"   响应预览: {response_preview}")
            
            results.append({
                'api': api['name'],
                'result': result
            })
            
            # 避免过快请求
            time.sleep(1)
        
        return results
    
    def analyze_results(self, results):
        """分析测试结果"""
        print("\n" + "=" * 60)
        print("📊 RapidAPI订阅分析结果")
        print("=" * 60)
        
        accessible = []
        needs_auth = []
        forbidden = []
        rate_limited = []
        errors = []
        
        for result in results:
            api_name = result['api']
            api_result = result['result']
            
            if api_result['success']:
                accessible.append(api_name)
            elif api_result.get('status_code') == 401:
                needs_auth.append(api_name)
            elif api_result.get('status_code') == 403:
                forbidden.append(api_name)
            elif api_result.get('status_code') == 429:
                rate_limited.append(api_name)
            else:
                errors.append(api_name)
        
        print(f"\n✅ 可直接访问的API ({len(accessible)}):")
        for api in accessible:
            print(f"   • {api}")
        
        print(f"\n🔑 需要认证的API ({len(needs_auth)}):")
        for api in needs_auth:
            print(f"   • {api}")
        
        print(f"\n🚫 访问被拒绝的API ({len(forbidden)}):")
        for api in forbidden:
            print(f"   • {api}")
        
        print(f"\n⏰ 速率限制的API ({len(rate_limited)}):")
        for api in rate_limited:
            print(f"   • {api}")
        
        print(f"\n❌ 连接错误的API ({len(errors)}):")
        for api in errors:
            print(f"   • {api}")
        
        # 总结建议
        print(f"\n💡 建议:")
        if accessible:
            print("   • 可直接使用的API可以立即集成到项目中")
        if needs_auth:
            print("   • 需要认证的API可能已订阅，检查API密钥配置")
        if rate_limited:
            print("   • 速率限制的API表明已订阅但达到使用限制")
        if forbidden:
            print("   • 被拒绝的API可能需要订阅或升级套餐")

def main():
    """主函数"""
    api_key = "6731900a13msh816fbe854209ac2p1bded2jsn1538144d52a4"
    
    print("🎯 RapidAPI订阅检查工具")
    print(f"API Key: {api_key[:20]}...")
    print("=" * 60)
    
    checker = RapidAPIChecker(api_key)
    results = checker.check_common_apis()
    checker.analyze_results(results)
    
    print(f"\n✅ 检查完成！")
    print(f"📝 建议查看RapidAPI控制台获取详细的订阅信息：")
    print(f"   https://rapidapi.com/developer/dashboard")

if __name__ == "__main__":
    main()
