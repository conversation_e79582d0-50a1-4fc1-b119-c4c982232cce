/**
 * N8N GraphRAG集成节点
 * 用于在N8N工作流中调用GraphRAG服务
 */

// GraphRAG查询节点
const graphragQueryNode = {
    name: "GraphRAG Query",
    description: "查询GraphRAG知识图谱",
    
    async execute() {
        const items = $input.all();
        const processedItems = [];
        
        // GraphRAG服务配置
        const graphragConfig = {
            baseUrl: process.env.GRAPHRAG_API_URL || 'http://graphrag-server:8080',
            timeout: 30000
        };
        
        for (const item of items) {
            try {
                const data = item.json;
                
                // 构建查询请求
                const queryRequest = {
                    question: data.question || data.title || data.content,
                    method: data.method || 'global',
                    max_tokens: data.max_tokens || 8000,
                    context: {
                        source: data.source || 'rss',
                        timestamp: data.published_date || new Date().toISOString(),
                        article_id: data.article_id
                    }
                };
                
                // 调用GraphRAG API
                const response = await $http.request({
                    method: 'POST',
                    url: `${graphragConfig.baseUrl}/query`,
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: queryRequest,
                    timeout: graphragConfig.timeout
                });
                
                // 处理响应
                const graphragResult = response.body;
                
                // 构建增强的文档
                const enhancedDocument = {
                    ...data,
                    graphrag_analysis: {
                        question: graphragResult.question,
                        method: graphragResult.method,
                        response: graphragResult.response,
                        context_data: graphragResult.context_data,
                        timestamp: graphragResult.timestamp
                    },
                    enhanced_metadata: {
                        has_graphrag_analysis: true,
                        analysis_quality: this.assessAnalysisQuality(graphragResult),
                        key_insights: this.extractKeyInsights(graphragResult.response)
                    }
                };
                
                processedItems.push({
                    json: enhancedDocument,
                    pairedItem: { item: items.indexOf(item) }
                });
                
                console.log(`✅ GraphRAG分析完成: ${data.title?.substring(0, 50)}...`);
                
            } catch (error) {
                console.error(`❌ GraphRAG分析失败: ${error.message}`);
                
                // 错误处理：返回原始数据
                processedItems.push({
                    json: {
                        ...data,
                        graphrag_error: {
                            message: error.message,
                            timestamp: new Date().toISOString()
                        }
                    },
                    pairedItem: { item: items.indexOf(item) }
                });
            }
        }
        
        return processedItems;
    },
    
    // 评估分析质量
    assessAnalysisQuality(result) {
        const response = result.response || '';
        const contextData = result.context_data || {};
        
        let quality = 'low';
        
        if (response.length > 500 && Object.keys(contextData).length > 0) {
            quality = 'high';
        } else if (response.length > 200) {
            quality = 'medium';
        }
        
        return quality;
    },
    
    // 提取关键洞察
    extractKeyInsights(response) {
        if (!response) return [];
        
        // 简单的关键词提取
        const insights = [];
        const sentences = response.split(/[.!?]+/);
        
        for (const sentence of sentences) {
            if (sentence.length > 50 && 
                (sentence.includes('趋势') || 
                 sentence.includes('影响') || 
                 sentence.includes('建议') ||
                 sentence.includes('风险'))) {
                insights.push(sentence.trim());
            }
        }
        
        return insights.slice(0, 3); // 最多返回3个洞察
    }
};

// GraphRAG索引构建节点
const graphragIndexNode = {
    name: "GraphRAG Index Builder",
    description: "构建GraphRAG知识图谱索引",
    
    async execute() {
        const items = $input.all();
        
        // 收集所有文档
        const documents = [];
        const sources = new Set();
        
        for (const item of items) {
            const data = item.json;
            if (data.content || data.title) {
                documents.push(data.content || data.title);
                sources.add(data.source || 'unknown');
            }
        }
        
        if (documents.length === 0) {
            throw new Error('没有找到可索引的文档');
        }
        
        try {
            // GraphRAG服务配置
            const graphragConfig = {
                baseUrl: process.env.GRAPHRAG_API_URL || 'http://graphrag-server:8080',
                timeout: 300000 // 5分钟超时
            };
            
            // 构建索引请求
            const indexRequest = {
                documents: documents,
                source: Array.from(sources).join(','),
                rebuild: false
            };
            
            // 调用GraphRAG索引API
            const response = await $http.request({
                method: 'POST',
                url: `${graphragConfig.baseUrl}/index`,
                headers: {
                    'Content-Type': 'application/json'
                },
                body: indexRequest,
                timeout: graphragConfig.timeout
            });
            
            console.log(`✅ GraphRAG索引构建启动: ${documents.length}个文档`);
            
            return [{
                json: {
                    status: 'success',
                    message: '索引构建任务已启动',
                    document_count: documents.length,
                    sources: Array.from(sources),
                    timestamp: new Date().toISOString(),
                    response: response.body
                }
            }];
            
        } catch (error) {
            console.error(`❌ GraphRAG索引构建失败: ${error.message}`);
            throw error;
        }
    }
};

// GraphRAG状态检查节点
const graphragStatusNode = {
    name: "GraphRAG Status Check",
    description: "检查GraphRAG服务状态",
    
    async execute() {
        try {
            const graphragConfig = {
                baseUrl: process.env.GRAPHRAG_API_URL || 'http://graphrag-server:8080',
                timeout: 10000
            };
            
            // 检查健康状态
            const healthResponse = await $http.request({
                method: 'GET',
                url: `${graphragConfig.baseUrl}/health`,
                timeout: graphragConfig.timeout
            });
            
            // 检查服务状态
            const statusResponse = await $http.request({
                method: 'GET',
                url: `${graphragConfig.baseUrl}/status`,
                timeout: graphragConfig.timeout
            });
            
            return [{
                json: {
                    health: healthResponse.body,
                    status: statusResponse.body,
                    timestamp: new Date().toISOString(),
                    service_ready: healthResponse.body.status === 'healthy' && 
                                  statusResponse.body.initialized === true
                }
            }];
            
        } catch (error) {
            console.error(`❌ GraphRAG状态检查失败: ${error.message}`);
            
            return [{
                json: {
                    error: error.message,
                    timestamp: new Date().toISOString(),
                    service_ready: false
                }
            }];
        }
    }
};

// 导出节点配置
module.exports = {
    graphragQueryNode,
    graphragIndexNode,
    graphragStatusNode
};
