#!/usr/bin/env python3
"""
RapidAPI详细功能探索器
深度测试每个已订阅API的具体功能和端点
"""

import requests
import json
import time

class RapidAPIDetailedExplorer:
    def __init__(self, api_key):
        self.api_key = api_key
        self.base_headers = {
            'X-RapidAPI-Key': api_key,
            'Content-Type': 'application/json'
        }
        
        # 详细的API端点映射
        self.api_endpoints = {
            'alpha-vantage.p.rapidapi.com': {
                'name': 'Alpha Vantage (股票数据)',
                'category': '股票/金融数据',
                'endpoints': [
                    ('/query', {'function': 'GLOBAL_QUOTE', 'symbol': 'AAPL'}, '实时股票报价'),
                    ('/query', {'function': 'OVERVIEW', 'symbol': 'AAPL'}, '公司概览'),
                    ('/query', {'function': 'INCOME_STATEMENT', 'symbol': 'AAPL'}, '损益表'),
                    ('/query', {'function': 'BALANCE_SHEET', 'symbol': 'AAPL'}, '资产负债表'),
                    ('/query', {'function': 'CASH_FLOW', 'symbol': 'AAPL'}, '现金流量表'),
                    ('/query', {'function': 'EARNINGS', 'symbol': 'AAPL'}, '财报数据'),
                    ('/query', {'function': 'TIME_SERIES_DAILY', 'symbol': 'AAPL'}, '日线数据'),
                    ('/query', {'function': 'NEWS_SENTIMENT', 'tickers': 'AAPL'}, '新闻情绪'),
                ]
            },
            'yahoo-finance15.p.rapidapi.com': {
                'name': 'Yahoo Finance (财经数据)',
                'category': '股票/金融数据',
                'endpoints': [
                    ('/api/yahoo/qu/quote/AAPL', {}, '股票报价'),
                    ('/api/yahoo/co/collections/day_gainers', {}, '当日涨幅榜'),
                    ('/api/yahoo/co/collections/day_losers', {}, '当日跌幅榜'),
                    ('/api/yahoo/co/collections/most_actives', {}, '最活跃股票'),
                    ('/api/yahoo/ne/news', {'symbols': 'AAPL'}, '股票新闻'),
                    ('/api/yahoo/hi/history/AAPL', {'period1': '1640995200', 'period2': '1672531200'}, '历史数据'),
                ]
            },
            'seeking-alpha.p.rapidapi.com': {
                'name': 'Seeking Alpha (投资分析)',
                'category': '投资分析/新闻',
                'endpoints': [
                    ('/symbols/get-profile', {'symbols': 'AAPL'}, '公司档案'),
                    ('/symbols/get-earnings', {'symbols': 'AAPL'}, '财报数据'),
                    ('/symbols/get-dividends', {'symbols': 'AAPL'}, '股息信息'),
                    ('/news/list', {'category': 'market-news'}, '市场新闻'),
                    ('/symbols/get-ratings', {'symbols': 'AAPL'}, '分析师评级'),
                ]
            },
            'twelve-data1.p.rapidapi.com': {
                'name': 'Twelve Data (金融数据)',
                'category': '股票/金融数据',
                'endpoints': [
                    ('/quote', {'symbol': 'AAPL'}, '实时报价'),
                    ('/time_series', {'symbol': 'AAPL', 'interval': '1day'}, '时间序列'),
                    ('/earnings', {'symbol': 'AAPL'}, '财报数据'),
                    ('/profile', {'symbol': 'AAPL'}, '公司档案'),
                    ('/statistics', {'symbol': 'AAPL'}, '统计数据'),
                ]
            },
            'polygon-io.p.rapidapi.com': {
                'name': 'Polygon.io (股票数据)',
                'category': '股票/金融数据',
                'endpoints': [
                    ('/v2/aggs/ticker/AAPL/prev', {}, '前一交易日数据'),
                    ('/v1/meta/symbols/AAPL/company', {}, '公司信息'),
                    ('/v2/snapshot/locale/us/markets/stocks/tickers', {}, '市场快照'),
                    ('/v1/meta/symbols/AAPL/news', {}, '公司新闻'),
                ]
            },
            'webull.p.rapidapi.com': {
                'name': 'Webull (股票数据)',
                'category': '股票/金融数据',
                'endpoints': [
                    ('/stock/search', {'keyword': 'AAPL'}, '股票搜索'),
                    ('/stock/get-quote', {'tickerId': '*********'}, '股票报价'),
                    ('/stock/get-analysis', {'tickerId': '*********'}, '技术分析'),
                ]
            },
            'sec-filings.p.rapidapi.com': {
                'name': 'SEC Filings (监管文件)',
                'category': 'SEC/监管数据',
                'endpoints': [
                    ('/search', {'query': 'AAPL'}, 'SEC文件搜索'),
                    ('/company/AAPL', {}, '公司SEC信息'),
                    ('/filings/10-K', {'symbol': 'AAPL'}, '10-K年报'),
                    ('/filings/10-Q', {'symbol': 'AAPL'}, '10-Q季报'),
                ]
            },
            'coinranking1.p.rapidapi.com': {
                'name': 'Coinranking (加密货币)',
                'category': '加密货币',
                'endpoints': [
                    ('/coins', {}, '加密货币列表'),
                    ('/coin/Qwsogvtv82FCd/price', {}, 'Bitcoin价格'),
                    ('/coin/razxDUgYGNAdQ/price', {}, 'Ethereum价格'),
                    ('/stats', {}, '市场统计'),
                ]
            },
            'news-api14.p.rapidapi.com': {
                'name': 'News API (新闻)',
                'category': '新闻/媒体',
                'endpoints': [
                    ('/everything', {'q': 'tesla', 'pageSize': 5}, '全部新闻'),
                    ('/top-headlines', {'country': 'us', 'pageSize': 5}, '头条新闻'),
                    ('/sources', {}, '新闻源'),
                ]
            }
        }
    
    def test_api_endpoint(self, host, endpoint, params, description):
        """测试单个API端点"""
        headers = self.base_headers.copy()
        headers['X-RapidAPI-Host'] = host
        
        url = f"https://{host}{endpoint}"
        
        try:
            response = requests.get(url, headers=headers, params=params, timeout=10)
            
            # 解析响应
            try:
                json_data = response.json()
                data_preview = json.dumps(json_data, indent=2)[:300] + "..." if len(str(json_data)) > 300 else json.dumps(json_data, indent=2)
            except:
                data_preview = response.text[:300] + "..." if len(response.text) > 300 else response.text
            
            return {
                'endpoint': endpoint,
                'params': params,
                'description': description,
                'status_code': response.status_code,
                'success': response.status_code == 200,
                'data_preview': data_preview,
                'response_size': len(response.text),
                'content_type': response.headers.get('content-type', ''),
                'rate_limit_info': {k: v for k, v in response.headers.items() if 'rate' in k.lower() or 'limit' in k.lower()}
            }
            
        except Exception as e:
            return {
                'endpoint': endpoint,
                'params': params,
                'description': description,
                'status_code': None,
                'success': False,
                'error': str(e),
                'data_preview': '',
                'response_size': 0,
                'content_type': '',
                'rate_limit_info': {}
            }
    
    def explore_api(self, host):
        """深度探索单个API"""
        if host not in self.api_endpoints:
            return None
        
        api_info = self.api_endpoints[host]
        print(f"\n🎯 {api_info['name']}")
        print(f"   分类: {api_info['category']}")
        print(f"   主机: {host}")
        print("-" * 50)
        
        results = []
        
        for endpoint, params, description in api_info['endpoints']:
            print(f"\n📊 测试: {description}")
            print(f"   端点: {endpoint}")
            if params:
                print(f"   参数: {params}")
            
            result = self.test_api_endpoint(host, endpoint, params, description)
            results.append(result)
            
            if result['success']:
                print(f"   ✅ 成功 (状态码: {result['status_code']})")
                print(f"   📄 响应大小: {result['response_size']} 字节")
                print(f"   📋 内容类型: {result['content_type']}")
                
                if result['rate_limit_info']:
                    remaining = result['rate_limit_info'].get('X-RateLimit-Requests-Remaining', 'N/A')
                    limit = result['rate_limit_info'].get('X-RateLimit-Requests-Limit', 'N/A')
                    print(f"   ⏱️ 速率限制: {remaining}/{limit}")
                
                # 显示数据预览
                if result['data_preview']:
                    print(f"   📊 数据预览:")
                    preview_lines = result['data_preview'].split('\n')[:5]
                    for line in preview_lines:
                        print(f"      {line}")
                        
            elif result['status_code'] == 429:
                print(f"   ⏰ 速率限制 (状态码: {result['status_code']})")
            elif result['status_code'] == 403:
                print(f"   🚫 访问被拒绝 (状态码: {result['status_code']})")
            elif result['status_code'] == 404:
                print(f"   ❓ 端点不存在 (状态码: {result['status_code']})")
            else:
                print(f"   ❌ 失败 (状态码: {result.get('status_code', 'N/A')})")
                if result.get('error'):
                    print(f"   错误: {result['error']}")
            
            # 避免过快请求
            time.sleep(0.5)
        
        return results
    
    def generate_api_summary(self, all_results):
        """生成API功能总结"""
        print("\n" + "=" * 60)
        print("📊 RapidAPI订阅功能总结")
        print("=" * 60)
        
        categories = {}
        working_apis = []
        
        for host, results in all_results.items():
            if host in self.api_endpoints:
                api_info = self.api_endpoints[host]
                category = api_info['category']
                
                if category not in categories:
                    categories[category] = []
                
                working_endpoints = [r for r in results if r['success']]
                if working_endpoints:
                    working_apis.append({
                        'name': api_info['name'],
                        'host': host,
                        'category': category,
                        'working_endpoints': len(working_endpoints),
                        'total_endpoints': len(results)
                    })
                    categories[category].append(api_info['name'])
        
        print(f"\n📈 按分类统计:")
        for category, apis in categories.items():
            print(f"   {category}: {len(apis)} 个API")
            for api in apis:
                print(f"     • {api}")
        
        print(f"\n✅ 可用API详情:")
        for api in working_apis:
            print(f"   • {api['name']}")
            print(f"     可用端点: {api['working_endpoints']}/{api['total_endpoints']}")
            print(f"     主机: {api['host']}")
        
        # 保存详细配置
        config = {
            'rapidapi_key': self.api_key,
            'working_apis': working_apis,
            'detailed_results': all_results
        }
        
        with open('rapidapi_detailed_config.json', 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=2, ensure_ascii=False)
        
        print(f"\n📄 详细配置已保存: rapidapi_detailed_config.json")

def main():
    """主函数"""
    api_key = "6731900a13msh816fbe854209ac2p1bded2jsn1538144d52a4"
    
    print("🎯 RapidAPI详细功能探索器")
    print(f"API Key: {api_key[:20]}...")
    print("深度测试每个已订阅API的具体功能")
    print("=" * 60)
    
    explorer = RapidAPIDetailedExplorer(api_key)
    
    # 重点测试已确认订阅的API
    priority_hosts = [
        'alpha-vantage.p.rapidapi.com',
        'yahoo-finance15.p.rapidapi.com', 
        'seeking-alpha.p.rapidapi.com',
        'twelve-data1.p.rapidapi.com',
        'polygon-io.p.rapidapi.com',
        'webull.p.rapidapi.com',
        'sec-filings.p.rapidapi.com',
        'coinranking1.p.rapidapi.com',
        'news-api14.p.rapidapi.com'
    ]
    
    all_results = {}
    
    for host in priority_hosts:
        try:
            results = explorer.explore_api(host)
            if results:
                all_results[host] = results
        except Exception as e:
            print(f"❌ 探索 {host} 时出错: {e}")
    
    explorer.generate_api_summary(all_results)
    
    print(f"\n✅ 探索完成！")
    print(f"💡 现在您可以:")
    print(f"   1. 查看 rapidapi_detailed_config.json 获取完整配置")
    print(f"   2. 根据可用端点设计稷下学宫的数据流")
    print(f"   3. 为八仙论道分配不同的数据源")

if __name__ == "__main__":
    main()
