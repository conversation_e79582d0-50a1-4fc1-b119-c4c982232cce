#!/usr/bin/env python3
"""
RapidAPI订阅扫描器
自动发现和测试所有已订阅的API服务
"""

import requests
import json
import time
from concurrent.futures import ThreadPoolExecutor, as_completed

class RapidAPISubscriptionScanner:
    def __init__(self, api_key):
        self.api_key = api_key
        self.base_headers = {
            'X-RapidAPI-Key': api_key,
            'Content-Type': 'application/json'
        }
        
        # 常见的RapidAPI服务主机列表（基于金融/数据类别）
        self.known_hosts = [
            # 股票和金融数据
            'alpha-vantage.p.rapidapi.com',
            'yahoo-finance15.p.rapidapi.com',
            'yahoo-finance-api1.p.rapidapi.com',
            'yahoo-finance-low-latency.p.rapidapi.com',
            'yh-finance.p.rapidapi.com',
            'twelve-data1.p.rapidapi.com',
            'polygon-io.p.rapidapi.com',
            'finnhub.io',
            'financialmodelingprep.com',
            'seeking-alpha.p.rapidapi.com',
            'morningstar1.p.rapidapi.com',
            'webull.p.rapidapi.com',
            'tradingview-ta.p.rapidapi.com',
            
            # SEC和监管数据
            'sec-filings.p.rapidapi.com',
            'sec-api.p.rapidapi.com',
            'edgar-sec-filings.p.rapidapi.com',
            
            # 加密货币
            'coinranking1.p.rapidapi.com',
            'coinbase-api.p.rapidapi.com',
            'binance43.p.rapidapi.com',
            'crypto-news16.p.rapidapi.com',
            
            # 新闻和情绪分析
            'news-api14.p.rapidapi.com',
            'contextualwebsearch-websearch-v1.p.rapidapi.com',
            'google-news13.p.rapidapi.com',
            'financial-news.p.rapidapi.com',
            
            # 经济数据
            'economic-indicators.p.rapidapi.com',
            'fred-api.p.rapidapi.com',
            'world-bank-data.p.rapidapi.com',
            
            # 其他金融工具
            'currency-exchange.p.rapidapi.com',
            'exchangerate-api.p.rapidapi.com',
            'real-time-finance-data.p.rapidapi.com'
        ]
        
        # 常见的测试端点
        self.test_endpoints = {
            'alpha-vantage.p.rapidapi.com': [
                ('/query', {'function': 'GLOBAL_QUOTE', 'symbol': 'AAPL'}),
                ('/query', {'function': 'OVERVIEW', 'symbol': 'AAPL'})
            ],
            'yahoo-finance15.p.rapidapi.com': [
                ('/api/yahoo/qu/quote/AAPL', {}),
                ('/api/yahoo/co/collections/day_gainers', {})
            ],
            'twelve-data1.p.rapidapi.com': [
                ('/quote', {'symbol': 'AAPL'}),
                ('/time_series', {'symbol': 'AAPL', 'interval': '1day'})
            ],
            'polygon-io.p.rapidapi.com': [
                ('/v2/aggs/ticker/AAPL/prev', {}),
                ('/v1/meta/symbols/AAPL/company', {})
            ],
            'seeking-alpha.p.rapidapi.com': [
                ('/symbols/get-profile', {'symbols': 'AAPL'}),
                ('/news/list', {'category': 'market-news'})
            ],
            'sec-filings.p.rapidapi.com': [
                ('/search', {'query': 'AAPL'}),
                ('/company/AAPL', {})
            ],
            'coinranking1.p.rapidapi.com': [
                ('/coins', {}),
                ('/coin/Qwsogvtv82FCd/price', {})  # Bitcoin
            ],
            'news-api14.p.rapidapi.com': [
                ('/everything', {'q': 'tesla', 'pageSize': 1}),
                ('/top-headlines', {'country': 'us', 'pageSize': 1})
            ]
        }
    
    def test_api_endpoint(self, host, endpoint, params):
        """测试单个API端点"""
        headers = self.base_headers.copy()
        headers['X-RapidAPI-Host'] = host
        
        url = f"https://{host}{endpoint}"
        
        try:
            response = requests.get(url, headers=headers, params=params, timeout=8)
            
            # 检查响应头中的速率限制信息
            rate_limit_info = {}
            for key, value in response.headers.items():
                if any(term in key.lower() for term in ['rate', 'limit', 'remaining', 'reset']):
                    rate_limit_info[key] = value
            
            return {
                'host': host,
                'endpoint': endpoint,
                'params': params,
                'status_code': response.status_code,
                'success': response.status_code == 200,
                'subscribed': response.status_code != 403,  # 403通常表示未订阅
                'rate_limit_info': rate_limit_info,
                'response_size': len(response.text),
                'response_preview': response.text[:200] if response.text else '',
                'error_message': None
            }
            
        except Exception as e:
            return {
                'host': host,
                'endpoint': endpoint,
                'params': params,
                'status_code': None,
                'success': False,
                'subscribed': False,
                'rate_limit_info': {},
                'response_size': 0,
                'response_preview': '',
                'error_message': str(e)
            }
    
    def scan_subscriptions(self, max_workers=5):
        """并发扫描所有可能的订阅"""
        print("🔍 扫描RapidAPI订阅服务...")
        print(f"📊 预计扫描 {len(self.known_hosts)} 个服务主机")
        print("=" * 60)
        
        all_tests = []
        
        # 准备所有测试任务
        for host in self.known_hosts:
            if host in self.test_endpoints:
                for endpoint, params in self.test_endpoints[host]:
                    all_tests.append((host, endpoint, params))
            else:
                # 对于没有预定义端点的主机，尝试通用端点
                all_tests.append((host, '/', {}))
        
        results = []
        subscribed_services = {}
        
        # 并发执行测试
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            future_to_test = {
                executor.submit(self.test_api_endpoint, host, endpoint, params): (host, endpoint, params)
                for host, endpoint, params in all_tests
            }
            
            completed = 0
            for future in as_completed(future_to_test):
                result = future.result()
                results.append(result)
                completed += 1
                
                # 实时显示进度
                if completed % 5 == 0 or completed == len(all_tests):
                    print(f"⏳ 进度: {completed}/{len(all_tests)} ({completed/len(all_tests)*100:.1f}%)")
                
                # 如果发现订阅的服务，记录下来
                if result['subscribed']:
                    host = result['host']
                    if host not in subscribed_services:
                        subscribed_services[host] = []
                    subscribed_services[host].append(result)
                
                # 避免过快请求
                time.sleep(0.1)
        
        return results, subscribed_services
    
    def analyze_results(self, results, subscribed_services):
        """分析扫描结果"""
        print("\n" + "=" * 60)
        print("📊 RapidAPI订阅分析结果")
        print("=" * 60)
        
        # 统计信息
        total_tests = len(results)
        successful_tests = sum(1 for r in results if r['success'])
        subscribed_hosts = len(subscribed_services)
        
        print(f"\n📈 扫描统计:")
        print(f"   总测试数: {total_tests}")
        print(f"   成功请求: {successful_tests}")
        print(f"   已订阅服务: {subscribed_hosts}")
        
        # 详细的订阅服务信息
        print(f"\n✅ 已确认订阅的API服务 ({subscribed_hosts}):")
        
        for host, host_results in subscribed_services.items():
            print(f"\n🎯 {host}")
            
            # 找到成功的测试
            successful_endpoints = [r for r in host_results if r['success']]
            failed_endpoints = [r for r in host_results if (not r['success']) and r['subscribed']]
            
            if successful_endpoints:
                print(f"   ✅ 可用端点 ({len(successful_endpoints)}):")
                for result in successful_endpoints:
                    endpoint_desc = f"{result['endpoint']}"
                    if result['params']:
                        endpoint_desc += f" (参数: {result['params']})"
                    print(f"     • {endpoint_desc}")
                    
                    # 显示速率限制信息
                    if result['rate_limit_info']:
                        print(f"       速率限制: {result['rate_limit_info']}")
            
            if failed_endpoints:
                print(f"   ⚠️ 需要配置的端点 ({len(failed_endpoints)}):")
                for result in failed_endpoints:
                    print(f"     • {result['endpoint']} (状态码: {result['status_code']})")
        
        # 生成API配置建议
        self.generate_api_config(subscribed_services)
    
    def generate_api_config(self, subscribed_services):
        """生成API配置文件"""
        print(f"\n🔧 生成API配置建议:")
        
        config = {
            'rapidapi_key': self.api_key,
            'subscribed_apis': {}
        }
        
        for host, host_results in subscribed_services.items():
            successful_endpoints = [r for r in host_results if r['success']]
            
            if successful_endpoints:
                api_name = host.replace('.p.rapidapi.com', '').replace('.rapidapi.com', '')
                config['subscribed_apis'][api_name] = {
                    'host': host,
                    'base_url': f'https://{host}',
                    'endpoints': []
                }
                
                for result in successful_endpoints:
                    endpoint_config = {
                        'path': result['endpoint'],
                        'method': 'GET',
                        'params': result['params'],
                        'rate_limit': result['rate_limit_info']
                    }
                    config['subscribed_apis'][api_name]['endpoints'].append(endpoint_config)
        
        # 保存配置文件
        with open('rapidapi_config.json', 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=2, ensure_ascii=False)
        
        print(f"   📄 配置文件已保存: rapidapi_config.json")
        print(f"   📋 包含 {len(config['subscribed_apis'])} 个可用API服务")
        
        return config

def main():
    """主函数"""
    api_key = "6731900a13msh816fbe854209ac2p1bded2jsn1538144d52a4"
    
    print("🎯 RapidAPI订阅自动扫描器")
    print(f"API Key: {api_key[:20]}...")
    print("根据您的控制台显示：16个订阅，9个24小时调用")
    print("=" * 60)
    
    scanner = RapidAPISubscriptionScanner(api_key)
    results, subscribed_services = scanner.scan_subscriptions(max_workers=3)
    scanner.analyze_results(results, subscribed_services)
    
    print(f"\n✅ 扫描完成！")
    print(f"💡 建议：")
    print(f"   1. 查看生成的 rapidapi_config.json 文件")
    print(f"   2. 根据配置文件集成到您的项目中")
    print(f"   3. 为稷下学宫八仙论道配置数据源")

if __name__ == "__main__":
    main()
