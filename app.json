{"name": "MongoDB GraphRAG", "description": "智能知识图谱问答系统 - 连接MongoDB Atlas，构建智能知识网络", "keywords": ["mongodb", "graphrag", "ai", "knowledge-graph", "nlp"], "website": "https://github.com/jingminzhang/cauldron", "repository": "https://github.com/jingminzhang/cauldron", "logo": "https://cdn.jsdelivr.net/npm/simple-icons@v9/icons/mongodb.svg", "success_url": "/", "env": {"MONGODB_URI": {"description": "MongoDB Atlas连接字符串", "value": "mongodb+srv://jingminzhang:<EMAIL>/cauldron_rss?retryWrites=true&w=majority", "required": true}, "FLASK_ENV": {"description": "Flask环境", "value": "production"}}, "formation": {"web": {"quantity": 1, "size": "basic"}}, "buildpacks": [{"url": "heroku/python"}], "stack": "heroku-22", "addons": []}