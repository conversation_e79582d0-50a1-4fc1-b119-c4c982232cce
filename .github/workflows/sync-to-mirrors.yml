name: Sync to Mirror<PERSON>

on:
  workflow_dispatch:
  schedule:
    - cron: '0 21 * * *' # Beijing time 5:00 AM

jobs:
  sync:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Check for recent commits
        id: check_commits
        run: |
          # Check if there were any commits in the last 24 hours
          # The cron job runs daily, so we check for commits in that window.
          if git log --since="24 hours ago" --pretty=format:"%H" | grep . > /dev/null; then
            echo "commits_found=true"
            echo "commits_found=true" >> $GITHUB_OUTPUT
          else
            echo "commits_found=false"
            echo "commits_found=false" >> $GITHUB_OUTPUT
          fi

      - name: Push to GitLab
        if: steps.check_commits.outputs.commits_found == 'true' || github.event_name == 'workflow_dispatch'
        run: |
          echo "Pushing to GitLab..."
          git remote add gitlab https://oauth2:${{ secrets.GITLAB_TOKEN }}@gitlab.com/seekkey/cauldron.git
          git push --all gitlab
          git push --tags gitlab
        continue-on-error: true

      - name: Push to Gitee
        if: steps.check_commits.outputs.commits_found == 'true' || github.event_name == 'workflow_dispatch'
        run: |
          echo "Pushing to Gitee..."
          git remote add gitee https://${{ secrets.GITEE_USERNAME }}:${{ secrets.GITEE_TOKEN }}@gitee.com/seekkey/cauldron.git
          git push --all gitee
          git push --tags gitee
        continue-on-error: true