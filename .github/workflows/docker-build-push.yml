name: Docker Build and Push

on:
  workflow_dispatch:
  schedule:
    - cron: '0 20 * * *' # 4:00 AM Beijing Time (UTC+8)

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0 # Fetch all history for all tags and branches

      - name: Check for recent commits on release branches
        id: check_changes
        run: |
          # Check if there were any commits to any release/** branch in the last 24 hours
          # The command exits with 0 if commits are found, 1 otherwise.
          if git log origin/releases/** --since="24 hours ago" -n 1 | grep -q commit; then
            echo "Found recent commits. Proceeding with build."
            echo "has_changes=true" >> $GITHUB_OUTPUT
          else
            echo "No recent commits found on release branches. Skipping build."
            echo "has_changes=false" >> $GITHUB_OUTPUT
          fi

      - name: Login to Docker Hub
        if: steps.check_changes.outputs.has_changes == 'true' || github.event_name == 'workflow_dispatch'
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_TOKEN }}

      - name: Build and push
        if: steps.check_changes.outputs.has_changes == 'true' || github.event_name == 'workflow_dispatch'
        uses: docker/build-push-action@v5
        with:
          context: .
          push: true
          tags: |
            ${{ secrets.DOCKERHUB_USERNAME }}/cauldron:latest
            ${{ secrets.DOCKERHUB_USERNAME }}/cauldron:${{ github.sha }}