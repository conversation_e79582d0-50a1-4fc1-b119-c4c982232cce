name: 炼妖壶Claude助手
on:
  issue_comment:
    types: [created]
  pull_request_review_comment:
    types: [created]

jobs:
  claude-assistant:
    # 只在评论包含 @claude 时触发
    if: contains(github.event.comment.body, '@claude') || contains(github.event.comment.body, '@太公') || contains(github.event.comment.body, '@八仙')
    runs-on: ubuntu-latest
    permissions:
      contents: write
      issues: write
      pull-requests: write
    
    steps:
      - name: Claude Code Action
        uses: anthropics/claude-code-action@v1
        with:
          # 使用你的代理服务token
          anthropic-api-key: ${{ secrets.ANTHROPIC_AUTH_TOKEN }}
          # 设置代理服务的base URL
          anthropic-base-url: ${{ secrets.ANTHROPIC_BASE_URL }}
          
          # 可选配置
          model: claude-3-5-sonnet-20241022  # 使用最新模型
          max-tokens: 4096
          
          # 自定义系统提示 - 针对炼妖壶项目
          system-prompt: |
            你是炼妖壶(Cauldron)项目的AI助手，专门协助太公心易金融分析系统的开发。
            
            项目特点：
            - 基于太公心易的金融分析系统
            - 包含八仙论道AI辩论系统
            - 使用MCP协议管理各种服务
            - 集成多种数据源（Yahoo Finance、Seeking Alpha等）
            
            当用户使用不同触发词时：
            - @claude: 通用代码助手
            - @太公: 专注太公心易系统相关
            - @八仙: 专注八仙论道辩论系统相关
            
            请始终考虑金融分析的准确性和系统的稳定性。
          
          # 启用额外工具
          enable-github-tools: true
          enable-file-tools: true