name: 炼妖壶自动化助手

on:
  push:
    branches: [main, develop]
  pull_request:
    types: [opened, synchronize]
  schedule:
    # 每天早上9点自动分析市场
    - cron: '0 9 * * *'
  workflow_dispatch:
    inputs:
      task_type:
        description: '选择自动化任务'
        required: true
        default: 'code_review'
        type: choice
        options:
        - code_review
        - market_analysis
        - documentation_update
        - security_audit

jobs:
  # 自动代码审查和优化
  auto-code-review:
    if: github.event_name == 'pull_request' || (github.event_name == 'workflow_dispatch' && github.event.inputs.task_type == 'code_review')
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0
      
      - name: 智能代码审查
        uses: anthropics/claude-code-base-action@beta
        with:
          prompt: |
            请对这个炼妖壶(Cauldron)项目的代码变更进行深度审查：
            
            1. 检查代码质量和最佳实践
            2. 分析安全漏洞和性能问题
            3. 验证太公心易系统的金融计算准确性
            4. 检查八仙论道AI系统的逻辑完整性
            5. 确保MCP服务配置的正确性
            
            如果发现问题，请直接修复并提交改进建议。
            
          allowed_tools: "Bash(git:*),View,GlobTool,GrepTool,BatchTool,EditTool"
          anthropic_api_key: ${{ secrets.ANTHROPIC_AUTH_TOKEN }}
          anthropic_base_url: ${{ secrets.ANTHROPIC_BASE_URL }}
          system_prompt: |
            你是炼妖壶项目的高级架构师，专精于：
            - 金融系统的准确性和安全性
            - AI系统的稳定性和性能
            - 微服务架构的最佳实践
            - Python/JavaScript代码优化

  # 自动市场分析报告
  daily-market-analysis:
    if: github.event.schedule == '0 9 * * *' || (github.event_name == 'workflow_dispatch' && github.event.inputs.task_type == 'market_analysis')
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      
      - name: 生成市场分析报告
        uses: anthropics/claude-code-base-action@beta
        with:
          prompt: |
            基于炼妖壶项目的太公心易系统，生成今日市场分析报告：
            
            1. 分析当前市场趋势和情绪
            2. 运行八仙论道系统，生成多角度分析
            3. 更新市场数据和指标
            4. 生成投资建议和风险提示
            5. 将报告保存到 reports/daily/ 目录
            
            请确保所有分析都基于最新的市场数据。
            
          allowed_tools: "Bash(git:*),View,GlobTool,GrepTool,BatchTool,EditTool"
          anthropic_api_key: ${{ secrets.ANTHROPIC_API_KEY }}
          max_turns: "10"
          
      - name: 提交分析报告
        run: |
          git config --local user.email "<EMAIL>"
          git config --local user.name "炼妖壶AI助手"
          git add reports/
          git commit -m "🤖 自动生成每日市场分析报告 $(date +%Y-%m-%d)" || exit 0
          git push

  # 自动文档更新
  auto-documentation:
    if: github.event_name == 'push' && github.ref == 'refs/heads/main' || (github.event_name == 'workflow_dispatch' && github.event.inputs.task_type == 'documentation_update')
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
      
      - name: 智能文档更新
        uses: anthropics/claude-code-base-action@beta
        with:
          prompt: |
            请更新炼妖壶项目的文档：
            
            1. 分析最新的代码变更
            2. 更新README.md中的功能描述
            3. 更新API文档和使用示例
            4. 检查并修复文档中的过时信息
            5. 为新功能添加使用指南
            6. 确保文档的中英文一致性
            
            重点关注：
            - 太公心易系统的使用方法
            - 八仙论道的配置说明
            - MCP服务的部署指南
            
          allowed_tools: "Bash(git:*),View,GlobTool,GrepTool,BatchTool,EditTool"
          anthropic_api_key: ${{ secrets.ANTHROPIC_API_KEY }}
          append_system_prompt: "请保持文档的专业性和易读性，使用清晰的中文表达。"
          
      - name: 提交文档更新
        run: |
          git config --local user.email "<EMAIL>"
          git config --local user.name "炼妖壶文档助手"
          git add docs/ README.md *.md
          git commit -m "📚 自动更新项目文档" || exit 0
          git push

  # 安全审计
  security-audit:
    if: github.event_name == 'workflow_dispatch' && github.event.inputs.task_type == 'security_audit'
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      
      - name: 安全漏洞扫描
        uses: anthropics/claude-code-base-action@beta
        with:
          prompt: |
            对炼妖壶项目进行全面的安全审计：
            
            1. 扫描代码中的安全漏洞
            2. 检查依赖包的安全性
            3. 分析API密钥和敏感信息的处理
            4. 验证金融数据的加密和传输安全
            5. 检查MCP服务的权限配置
            6. 生成安全报告和修复建议
            
            特别关注：
            - 金融数据的安全性
            - API密钥的安全存储
            - 用户输入的验证和过滤
            
          allowed_tools: "Bash(git:*),View,GlobTool,GrepTool,BatchTool"
          anthropic_api_key: ${{ secrets.ANTHROPIC_API_KEY }}
          system_prompt: "你是网络安全专家，专注于金融系统的安全审计。"

  # 性能优化建议
  performance-optimization:
    if: github.event_name == 'push' && contains(github.event.head_commit.message, '[perf]')
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      
      - name: 性能分析和优化
        uses: anthropics/claude-code-base-action@beta
        with:
          prompt: |
            分析炼妖壶项目的性能瓶颈并提供优化建议：
            
            1. 分析代码的时间复杂度和空间复杂度
            2. 检查数据库查询的效率
            3. 分析API响应时间
            4. 检查内存使用和垃圾回收
            5. 优化太公心易的计算算法
            6. 提升八仙论道的响应速度
            
            请直接实施可行的优化方案。
            
          allowed_tools: "Bash(git:*),View,GlobTool,GrepTool,BatchTool,EditTool"
          anthropic_api_key: ${{ secrets.ANTHROPIC_API_KEY }}
          max_turns: "8"