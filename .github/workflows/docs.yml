name: 部署 MkDocs 文档到 GitHub Pages

on:
  push:
    branches:
      - main
  workflow_dispatch:

# 设置 GITHUB_TOKEN 权限以允许部署到 GitHub Pages
permissions:
  contents: read
  pages: write
  id-token: write

# 只允许一个并发部署，跳过正在进行的运行和最新排队的运行之间的运行
concurrency:
  group: "pages"
  cancel-in-progress: false

jobs:
  # 构建作业
  build:
    runs-on: ubuntu-latest
    steps:
      - name: 检出代码
        uses: actions/checkout@v4
        with:
          fetch-depth: 0  # 获取完整历史记录用于 git-revision-date-localized 插件

      - name: 设置 Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.11'

      - name: 安装依赖
        run: |
          pip install -r requirements.txt
          pip install mkdocs>=1.5.0 mkdocs-material>=9.0.0 mkdocs-minify-plugin>=0.7.0 mkdocs-git-revision-date-localized-plugin>=1.2.0

      - name: 构建 MkDocs 站点
        run: mkdocs build --clean --verbose

      - name: 设置 Pages
        uses: actions/configure-pages@v4

      - name: 上传构建产物
        uses: actions/upload-pages-artifact@v3
        with:
          path: ./site

  # 部署作业
  deploy:
    environment:
      name: github-pages
      url: ${{ steps.deployment.outputs.page_url }}
    runs-on: ubuntu-latest
    needs: build
    steps:
      - name: 部署到 GitHub Pages
        id: deployment
        uses: actions/deploy-pages@v4