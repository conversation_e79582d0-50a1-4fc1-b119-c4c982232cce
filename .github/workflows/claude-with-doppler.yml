name: 炼妖壶Claude助手 (Doppler版)

on:
  issue_comment:
    types: [created]
  pull_request_review_comment:
    types: [created]

jobs:
  claude-assistant:
    # 限制访问权限，防止滥用
    if: |
      (contains(github.event.comment.body, '@claude') || 
       contains(github.event.comment.body, '@太公') || 
       contains(github.event.comment.body, '@八仙')) &&
      (github.event.comment.author_association == 'OWNER' ||
       github.event.comment.author_association == 'COLLABORATOR' ||
       github.event.comment.author_association == 'MEMBER')
    
    runs-on: ubuntu-latest
    permissions:
      contents: write
      issues: write
      pull-requests: write
    
    steps:
      # 使用Doppler获取所有密钥
      - name: Setup Doppler CLI
        uses: dopplerhq/cli-action@v3
        with:
          cli-version: latest
      
      - name: Load secrets from Dopp<PERSON>
        run: |
          # 从Doppler加载所有环境变量
          doppler secrets download --no-file --format env >> $GITHUB_ENV
        env:
          DOPPLER_TOKEN: ${{ secrets.DOPPLER_TOKEN }}
          DOPPLER_PROJECT: ${{ secrets.DOPPLER_PROJECT }}
          DOPPLER_CONFIG: ${{ secrets.DOPPLER_CONFIG }}
      
      - name: Claude Code Action
        uses: anthropics/claude-code-action@v1
        with:
          # 使用从Doppler加载的密钥
          anthropic-api-key: ${{ env.ANTHROPIC_AUTH_TOKEN }}
          anthropic-base-url: ${{ env.ANTHROPIC_BASE_URL }}
          
          model: claude-3-5-sonnet-20241022
          max-tokens: 4096
          
          # 炼妖壶专用系统提示
          system-prompt: |
            你是炼妖壶(Cauldron)项目的AI助手，专门协助太公心易金融分析系统的开发。
            
            项目特点：
            - 基于太公心易的金融分析系统
            - 包含八仙论道AI辩论系统
            - 使用MCP协议管理各种服务
            - 集成多种数据源和API服务
            
            当前可用的服务密钥：
            - OpenRouter API (AI模型调用)
            - Zilliz向量数据库
            - 数据库连接
            - Mastodon社交媒体
            
            触发词含义：
            - @claude: 通用代码助手和技术咨询
            - @太公: 专注太公心易系统和金融分析
            - @八仙: 专注八仙论道辩论系统和AI协作
            
            请始终考虑金融分析的准确性、系统安全性和代码质量。
            
          enable-github-tools: true
          enable-file-tools: true
      
      # 记录使用情况，便于监控
      - name: Log Claude usage
        run: |
          echo "🤖 Claude调用记录:"
          echo "用户: ${{ github.event.comment.user.login }}"
          echo "仓库: ${{ github.repository }}"
          echo "时间: $(date)"
          echo "触发内容: ${{ github.event.comment.body }}"
          echo "关联: ${{ github.event.comment.html_url }}"
        continue-on-error: true