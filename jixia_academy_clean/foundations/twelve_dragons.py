from enum import Enum
from typing import List, Dict, Optional
from pydantic import BaseModel
from .mythology import BaseMythicalBeing, MythologyEngine

class DragonName(str, Enum):
    """Enumeration for the names of the Twelve Dragon Children."""
    QIU_NIU = "囚牛"
    YA_ZI = "睚眦"
    SUAN_NI = "狻猊"
    PU_LAO = "蒲牢"
    CHAO_FENG = "嘲风"
    BI_AN = "狴犴"
    BI_XI = "贔屓"
    FU_XI = "负屃"
    CHI_WEN = "螭吻"
    GONG_FU = "蚣蝮"
    PI_XIU = "貔貅"
    TAO_TIE = "饕餮"

class DragonChild(BaseMythicalBeing):
    """
    Represents one of the Twelve Dragon Children, serving as a foundational 'class'
    for various system functions, from data processing to AI agent roles.
    """
    id: int
    name: DragonName  # 将name字段限定为DragonName枚举，更具体
    mother: str
    function: str
    description: str


# The canonical list of the Twelve Dragon Children, based on the table in docs/12dragon.md
_TWELVE_DRAGONS_DATA: List[DragonChild] = [
    DragonChild(id=1, name=DragonName.QIU_NIU, mother="牛", function="礼乐戎祀", description="建立秩序，凝聚人心，是系统的根基。"),
    DragonChild(id=2, name=DragonName.YA_ZI, mother="豺", function="虽远必诛", description="确立法度，赏罚分明，是规则的守护者。"),
    DragonChild(id=3, name=DragonName.SUAN_NI, mother="狮", function="讲经说法", description="内化智慧，破除我执，是系统的思想建设。"),
    DragonChild(id=4, name=DragonName.PU_LAO, mother="鲲", function="声如洪钟", description="宣告理念，融合思想，是系统的外部宣言。"),
    DragonChild(id=5, name=DragonName.CHAO_FENG, mother="凤", function="千里听风", description="风险预警，洞察危机，是系统的风控情报中心。"),
    DragonChild(id=6, name=DragonName.BI_AN, mother="虎", function="天下为公", description="司法审判，揭示内部矛盾，考验系统的公正性。"),
    DragonChild(id=7, name=DragonName.BI_XI, mother="龟", function="文以载道", description="文化重建，弥合创伤，用文明覆盖野蛮。"),
    DragonChild(id=8, name=DragonName.FU_XI, mother="龙", function="东西一通", description="对外开拓，经略外部，汲取核心利益。"),
    DragonChild(id=9, name=DragonName.CHI_WEN, mother="鱼", function="吐故纳新", description="内部净化，新陈代谢，是系统的免疫系统。"),
    DragonChild(id=10, name=DragonName.GONG_FU, mother="虬", function="镇守九宫", description="守成反思，追求平衡，是系统的成熟稳定期。"),
    DragonChild(id=11, name=DragonName.PI_XIU, mother="罴", function="颗粒归仓", description="战略储备，只进不出，为未来积蓄能量。"),
    DragonChild(id=12, name=DragonName.TAO_TIE, mother="熊", function="乃成富翁", description="综合决策，整合所有资源，达成最终目标。"),
]

# 创建一个默认的、预配置好的“十二龙子”神话引擎实例
TWELVE_DRAGONS_ENGINE = MythologyEngine[DragonChild](
    name="十二龙子",
    beings=_TWELVE_DRAGONS_DATA
)

# 为了方便和向后兼容，可以保留便捷的查找函数
def get_dragon_by_name(name: DragonName) -> Optional[DragonChild]:
    """Retrieves a dragon's data by its name."""
    return TWELVE_DRAGONS_ENGINE.get_by_name(name.value)

def get_dragon_by_id(dragon_id: int) -> Optional[DragonChild]:
    """Retrieves a dragon's data by its ID."""
    for dragon in TWELVE_DRAGONS_ENGINE.get_all():
        if dragon.id == dragon_id:
            return dragon
    return None