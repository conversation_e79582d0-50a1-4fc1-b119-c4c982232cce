#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Zilliz唯一真理来源
AutoGen智能体通过MCP直接访问的数据源
"""

import os
import json
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
import uuid
from dataclasses import dataclass, asdict
from pymilvus import connections, Collection, utility
from sentence_transformers import SentenceTransformer

logger = logging.getLogger(__name__)


@dataclass
class MarketEvent:
    """市场事件数据结构"""
    id: str
    timestamp: datetime
    title: str
    content: str
    source: str
    sentiment: float
    impact_score: float
    keywords: List[str]
    embedding: List[float] = None


@dataclass
class AgentMemory:
    """智能体记忆数据结构"""
    agent_name: str
    topic: str
    historical_view: str
    prediction_accuracy: float
    timestamp: datetime
    confidence_score: float


class ZillizTruthSource:
    """Zilliz唯一真理来源 - AutoGen的数据基础"""
    
    def __init__(self, config: Optional[Dict] = None):
        self.config = config or self._load_config()
        self.collection_name = "market_intelligence_v2"
        self.agent_memory_collection = "agent_memories_v2"
        self.embedding_model = None
        self.collection = None
        self.memory_collection = None
        
        self._initialize()
    
    def _load_config(self) -> Dict:
        """加载配置"""
        return {
            "zilliz_endpoint": os.getenv("ZILLIZ_ENDPOINT"),
            "zilliz_token": os.getenv("ZILLIZ_TOKEN"),
            "embedding_model": "sentence-transformers/all-MiniLM-L6-v2"
        }
    
    def _initialize(self):
        """初始化连接和模型"""
        try:
            # 连接Zilliz
            connections.connect(
                "default",
                uri=self.config["zilliz_endpoint"],
                token=self.config["zilliz_token"]
            )
            
            # 加载嵌入模型
            self.embedding_model = SentenceTransformer(self.config["embedding_model"])
            
            # 初始化集合
            self._initialize_collections()
            
            logger.info("✅ Zilliz真理来源初始化成功")
            
        except Exception as e:
            logger.error(f"❌ Zilliz真理来源初始化失败: {e}")
            raise
    
    def _initialize_collections(self):
        """初始化向量集合"""
        # 主要市场情报集合
        if utility.has_collection(self.collection_name):
            self.collection = Collection(self.collection_name)
            self.collection.load()
        else:
            logger.warning(f"⚠️ 集合 {self.collection_name} 不存在")
        
        # 智能体记忆集合
        if utility.has_collection(self.agent_memory_collection):
            self.memory_collection = Collection(self.agent_memory_collection)
            self.memory_collection.load()
        else:
            logger.warning(f"⚠️ 集合 {self.agent_memory_collection} 不存在")
    
    async def query_market_context(self, topic: str, days: int = 7) -> Dict:
        """查询市场上下文 - AutoGen的主要数据接口"""
        try:
            # 1. 生成查询向量
            query_vector = self.embedding_model.encode([topic])[0].tolist()
            
            # 2. 时间过滤条件
            end_time = datetime.now()
            start_time = end_time - timedelta(days=days)
            time_filter = f"timestamp >= {int(start_time.timestamp())} and timestamp <= {int(end_time.timestamp())}"
            
            # 3. 向量搜索
            search_params = {"metric_type": "L2", "params": {"nprobe": 10}}
            
            results = self.collection.search(
                data=[query_vector],
                anns_field="embedding",
                param=search_params,
                limit=50,
                expr=time_filter,
                output_fields=["title", "content", "source", "sentiment", "impact_score", "keywords", "timestamp"]
            )
            
            # 4. 处理结果
            events = []
            sentiments = []
            impact_scores = []
            
            for hit in results[0]:
                event_data = hit.entity
                events.append({
                    "title": event_data.get("title", ""),
                    "content": event_data.get("content", "")[:200] + "...",
                    "source": event_data.get("source", ""),
                    "sentiment": event_data.get("sentiment", 0.0),
                    "impact_score": event_data.get("impact_score", 0.0),
                    "keywords": event_data.get("keywords", []),
                    "timestamp": datetime.fromtimestamp(event_data.get("timestamp", 0)),
                    "relevance_score": 1.0 - hit.distance  # 转换为相似度分数
                })
                
                sentiments.append(event_data.get("sentiment", 0.0))
                impact_scores.append(event_data.get("impact_score", 0.0))
            
            # 5. 生成上下文摘要
            context_summary = self._generate_context_summary(events, sentiments, impact_scores)
            
            return {
                "topic": topic,
                "timeframe": f"过去{days}天",
                "total_events": len(events),
                "relevant_events": events[:10],  # 返回最相关的10个事件
                "sentiment_trend": context_summary["sentiment_trend"],
                "impact_distribution": context_summary["impact_distribution"],
                "key_insights": context_summary["key_insights"],
                "data_source": "zilliz_direct",
                "query_timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"❌ 查询市场上下文失败: {e}")
            return self._get_fallback_context(topic, days)
    
    async def get_agent_memory(self, agent_name: str, topic: str) -> Dict:
        """获取智能体历史记忆"""
        try:
            if not self.memory_collection:
                return self._get_empty_memory(agent_name)
            
            # 查询智能体历史观点
            expr = f'agent_name == "{agent_name}"'
            
            results = self.memory_collection.query(
                expr=expr,
                output_fields=["topic", "historical_view", "prediction_accuracy", "timestamp", "confidence_score"],
                limit=20
            )
            
            # 过滤相关主题
            relevant_memories = []
            for result in results:
                if self._is_topic_relevant(result.get("topic", ""), topic):
                    relevant_memories.append({
                        "topic": result.get("topic", ""),
                        "historical_view": result.get("historical_view", ""),
                        "prediction_accuracy": result.get("prediction_accuracy", 0.0),
                        "timestamp": datetime.fromtimestamp(result.get("timestamp", 0)),
                        "confidence_score": result.get("confidence_score", 0.0)
                    })
            
            # 计算一致性和准确率
            consistency_score = self._calculate_consistency(relevant_memories)
            accuracy_trend = self._calculate_accuracy_trend(relevant_memories)
            
            return {
                "agent": agent_name,
                "topic": topic,
                "historical_views": relevant_memories[:5],  # 最近5个相关观点
                "consistency_score": consistency_score,
                "accuracy_trend": accuracy_trend,
                "total_memories": len(relevant_memories),
                "data_source": "zilliz_agent_memory"
            }
            
        except Exception as e:
            logger.error(f"❌ 获取智能体记忆失败: {e}")
            return self._get_empty_memory(agent_name)
    
    async def store_debate_outcome(self, debate_record: Dict) -> str:
        """存储辩论结果"""
        try:
            # 为每个智能体的观点创建记忆记录
            for agent_name, view_data in debate_record["agents_views"].items():
                memory_record = {
                    "id": str(uuid.uuid4()),
                    "agent_name": agent_name,
                    "topic": debate_record["topic"],
                    "historical_view": view_data.get("view", ""),
                    "prediction_accuracy": view_data.get("confidence", 0.5),
                    "timestamp": int(debate_record["timestamp"].timestamp()),
                    "confidence_score": view_data.get("confidence", 0.5),
                    "debate_id": debate_record["debate_id"]
                }
                
                # 存储到智能体记忆集合
                if self.memory_collection:
                    self.memory_collection.insert([memory_record])
            
            logger.info(f"✅ 辩论结果已存储: {debate_record['debate_id']}")
            return debate_record["debate_id"]
            
        except Exception as e:
            logger.error(f"❌ 存储辩论结果失败: {e}")
            return ""
    
    def _generate_context_summary(self, events: List[Dict], sentiments: List[float], impact_scores: List[float]) -> Dict:
        """生成上下文摘要"""
        if not events:
            return {
                "sentiment_trend": "中性",
                "impact_distribution": "低",
                "key_insights": ["暂无相关数据"]
            }
        
        # 情绪趋势
        avg_sentiment = sum(sentiments) / len(sentiments) if sentiments else 0.0
        sentiment_trend = "积极" if avg_sentiment > 0.1 else "消极" if avg_sentiment < -0.1 else "中性"
        
        # 影响力分布
        avg_impact = sum(impact_scores) / len(impact_scores) if impact_scores else 0.0
        impact_distribution = "高" if avg_impact > 7.0 else "中" if avg_impact > 4.0 else "低"
        
        # 关键洞察
        key_insights = [
            f"共发现{len(events)}个相关事件",
            f"平均情绪倾向：{sentiment_trend}（{avg_sentiment:.2f}）",
            f"平均影响力：{impact_distribution}（{avg_impact:.1f}）"
        ]
        
        # 添加热门关键词
        all_keywords = []
        for event in events:
            all_keywords.extend(event.get("keywords", []))
        
        if all_keywords:
            keyword_freq = {}
            for keyword in all_keywords:
                keyword_freq[keyword] = keyword_freq.get(keyword, 0) + 1
            
            top_keywords = sorted(keyword_freq.items(), key=lambda x: x[1], reverse=True)[:3]
            key_insights.append(f"热门关键词：{', '.join([kw[0] for kw in top_keywords])}")
        
        return {
            "sentiment_trend": sentiment_trend,
            "impact_distribution": impact_distribution,
            "key_insights": key_insights
        }
    
    def _is_topic_relevant(self, stored_topic: str, query_topic: str) -> bool:
        """判断主题相关性"""
        # 简单的关键词匹配，可以改进为语义相似度
        stored_words = set(stored_topic.lower().split())
        query_words = set(query_topic.lower().split())
        
        # 如果有共同关键词，认为相关
        return len(stored_words & query_words) > 0
    
    def _calculate_consistency(self, memories: List[Dict]) -> float:
        """计算观点一致性"""
        if len(memories) < 2:
            return 1.0
        
        # 简化的一致性计算：基于置信度的标准差
        confidences = [m.get("confidence_score", 0.5) for m in memories]
        if not confidences:
            return 0.5
        
        avg_confidence = sum(confidences) / len(confidences)
        variance = sum((c - avg_confidence) ** 2 for c in confidences) / len(confidences)
        
        # 转换为0-1的一致性分数
        consistency = max(0.0, 1.0 - variance)
        return round(consistency, 2)
    
    def _calculate_accuracy_trend(self, memories: List[Dict]) -> str:
        """计算准确率趋势"""
        if len(memories) < 2:
            return "数据不足"
        
        # 按时间排序
        sorted_memories = sorted(memories, key=lambda x: x.get("timestamp", datetime.min))
        
        # 计算前半部分和后半部分的平均准确率
        mid_point = len(sorted_memories) // 2
        early_accuracy = sum(m.get("prediction_accuracy", 0.5) for m in sorted_memories[:mid_point]) / mid_point
        recent_accuracy = sum(m.get("prediction_accuracy", 0.5) for m in sorted_memories[mid_point:]) / (len(sorted_memories) - mid_point)
        
        if recent_accuracy > early_accuracy + 0.1:
            return "上升"
        elif recent_accuracy < early_accuracy - 0.1:
            return "下降"
        else:
            return "稳定"
    
    def _get_fallback_context(self, topic: str, days: int) -> Dict:
        """获取降级上下文"""
        return {
            "topic": topic,
            "timeframe": f"过去{days}天",
            "total_events": 0,
            "relevant_events": [],
            "sentiment_trend": "中性",
            "impact_distribution": "低",
            "key_insights": ["数据暂时不可用，使用历史经验进行分析"],
            "data_source": "fallback",
            "query_timestamp": datetime.now().isoformat()
        }
    
    def _get_empty_memory(self, agent_name: str) -> Dict:
        """获取空记忆"""
        return {
            "agent": agent_name,
            "topic": "",
            "historical_views": [],
            "consistency_score": 0.5,
            "accuracy_trend": "未知",
            "total_memories": 0,
            "data_source": "empty"
        }
