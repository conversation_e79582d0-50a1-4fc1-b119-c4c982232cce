#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
稷下学宫辩论系统
让三清八仙AI分析师进行智能辩论，并记录观点供后续查询
"""

import asyncio
import json
import random
import time
from datetime import datetime
from typing import Dict, List, Optional
import logging
import uuid

logger = logging.getLogger("DebateSystem")


class DebateTopic:
    """辩论话题"""
    
    def __init__(self, topic: str, description: str, trigger_keywords: List[str]):
        self.id = str(uuid.uuid4())
        self.topic = topic
        self.description = description
        self.trigger_keywords = trigger_keywords
        self.created_at = datetime.now()
        self.participants = []
        self.arguments = []
        self.status = "pending"  # pending, active, completed
        
    def add_participant(self, agent_name: str, position: str):
        """添加参与者"""
        participant = {
            "agent_name": agent_name,
            "position": position,
            "arguments": [],
            "joined_at": datetime.now().isoformat()
        }
        self.participants.append(participant)
    
    def add_argument(self, agent_name: str, argument: str, argument_type: str = "main"):
        """添加论点"""
        argument_record = {
            "agent_name": agent_name,
            "argument": argument,
            "type": argument_type,  # main, counter, support
            "timestamp": datetime.now().isoformat(),
            "post_id": None
        }
        self.arguments.append(argument_record)
        
        # 更新参与者的论点列表
        for participant in self.participants:
            if participant["agent_name"] == agent_name:
                participant["arguments"].append(argument)
                break


class DebateSystem:
    """稷下学宫辩论系统"""
    
    def __init__(self, agents_manager):
        self.agents_manager = agents_manager
        self.active_debates = {}
        self.completed_debates = []
        self.debate_templates = self._load_debate_templates()
        
    def _load_debate_templates(self) -> Dict:
        """加载辩论模板"""
        return {
            "market_direction": {
                "topic": "当前市场走向",
                "positions": {
                    "bullish": "看多后市",
                    "bearish": "看空后市", 
                    "neutral": "震荡整理"
                },
                "trigger_keywords": ["市场走向", "后市", "牛市", "熊市", "方向"]
            },
            "investment_strategy": {
                "topic": "投资策略选择",
                "positions": {
                    "growth": "成长股优先",
                    "value": "价值投资为主",
                    "momentum": "趋势跟踪策略"
                },
                "trigger_keywords": ["投资策略", "成长股", "价值投资", "趋势"]
            },
            "risk_management": {
                "topic": "风险管理方式",
                "positions": {
                    "conservative": "保守稳健",
                    "balanced": "平衡配置",
                    "aggressive": "积极进取"
                },
                "trigger_keywords": ["风险管理", "风险控制", "资产配置"]
            },
            "sector_rotation": {
                "topic": "板块轮动机会",
                "positions": {
                    "tech_focus": "科技股领涨",
                    "traditional": "传统行业回归",
                    "emerging": "新兴产业突破"
                },
                "trigger_keywords": ["板块轮动", "科技股", "传统行业", "新兴产业"]
            }
        }
    
    def detect_debate_trigger(self, content: str, agent_name: str) -> Optional[str]:
        """检测是否触发辩论"""
        content_lower = content.lower()
        
        for debate_type, template in self.debate_templates.items():
            if any(keyword in content_lower for keyword in template["trigger_keywords"]):
                return debate_type
        
        return None
    
    def initiate_debate(self, debate_type: str, initiator_agent: str, 
                       initiator_content: str) -> str:
        """发起辩论"""
        template = self.debate_templates[debate_type]
        
        # 创建辩论话题
        debate = DebateTopic(
            topic=template["topic"],
            description=f"关于{template['topic']}的讨论",
            trigger_keywords=template["trigger_keywords"]
        )
        
        # 分析发起者的立场
        initiator_position = self._analyze_position(initiator_content, template["positions"])
        debate.add_participant(initiator_agent, initiator_position)
        debate.add_argument(initiator_agent, initiator_content, "main")
        
        # 记录到发起者的辩论历史
        initiator = self.agents_manager.agents[initiator_agent]
        initiator.persona.add_debate_record(
            topic=debate.topic,
            position=initiator_position,
            arguments=[initiator_content],
            timestamp=datetime.now().isoformat(),
            debate_id=debate.id
        )
        
        # 激活辩论
        debate.status = "active"
        self.active_debates[debate.id] = debate
        
        logger.info(f"🔥 {initiator_agent} 发起辩论: {debate.topic}")
        
        # 邀请其他分析师参与
        self._invite_participants(debate.id, initiator_agent)
        
        return debate.id
    
    def _analyze_position(self, content: str, positions: Dict[str, str]) -> str:
        """分析内容中的立场"""
        content_lower = content.lower()
        
        # 简单的关键词匹配（实际可以用更复杂的NLP）
        position_scores = {}
        
        for pos_key, pos_desc in positions.items():
            score = 0
            pos_keywords = pos_desc.lower().split()
            
            for keyword in pos_keywords:
                if keyword in content_lower:
                    score += 1
            
            position_scores[pos_key] = score
        
        # 返回得分最高的立场
        best_position = max(position_scores.items(), key=lambda x: x[1])
        return best_position[0] if best_position[1] > 0 else "neutral"
    
    def _invite_participants(self, debate_id: str, initiator: str):
        """邀请其他分析师参与辩论"""
        debate = self.active_debates[debate_id]
        
        # 选择2-3个其他分析师参与
        available_agents = [name for name in self.agents_manager.agents.keys() 
                          if name != initiator]
        
        selected_agents = random.sample(available_agents, 
                                      min(3, len(available_agents)))
        
        for agent_name in selected_agents:
            # 延迟发布，模拟思考时间
            delay = random.randint(300, 900)  # 5-15分钟
            asyncio.create_task(self._agent_join_debate(debate_id, agent_name, delay))
    
    async def _agent_join_debate(self, debate_id: str, agent_name: str, delay: int):
        """分析师加入辩论"""
        await asyncio.sleep(delay)
        
        if debate_id not in self.active_debates:
            return
        
        debate = self.active_debates[debate_id]
        agent = self.agents_manager.agents[agent_name]
        
        # 生成该分析师的观点
        position, argument = self._generate_agent_argument(agent_name, debate)
        
        # 添加到辩论记录
        debate.add_participant(agent_name, position)
        debate.add_argument(agent_name, argument, "main")
        
        # 记录到分析师的辩论历史
        agent.persona.add_debate_record(
            topic=debate.topic,
            position=position,
            arguments=[argument],
            timestamp=datetime.now().isoformat(),
            debate_id=debate.id
        )
        
        # 发布到长毛象
        try:
            success = agent.post_analysis(argument)
            if success:
                logger.info(f"✅ {agent_name} 参与辩论: {debate.topic}")
            else:
                logger.warning(f"⚠️ {agent_name} 发布辩论观点失败")
        except Exception as e:
            logger.error(f"❌ {agent_name} 辩论发布异常: {e}")
    
    def _generate_agent_argument(self, agent_name: str, debate: DebateTopic) -> tuple:
        """为特定分析师生成辩论观点"""
        
        # 根据分析师人设生成不同的观点
        agent_arguments = {
            "太上老君": {
                "market_direction": {
                    "position": "neutral",
                    "argument": "🧙‍♂️ 道法自然，市场如水。当前阴阳交替，宜静观其变。过度乐观或悲观皆非智者所为。顺势而为，方能立于不败之地。#太公心易 #投资哲学"
                },
                "investment_strategy": {
                    "position": "balanced",
                    "argument": "📿 投资如修道，需要平衡之道。成长与价值并重，如太极阴阳。不可偏执一端，中庸之道方为长久之计。#道法投资"
                }
            },
            "元始天尊": {
                "market_direction": {
                    "position": "data_driven",
                    "argument": "⚡ 从技术指标看，MACD金叉确认，RSI处于中性区间。数据显示当前处于关键突破位，建议关注成交量配合。让数据说话！#技术分析"
                },
                "investment_strategy": {
                    "position": "momentum",
                    "argument": "📊 量化模型显示趋势跟踪策略胜率较高。动量因子在当前市场环境下表现突出，建议采用程序化交易策略。#量化投资"
                }
            },
            "铁拐李": {
                "market_direction": {
                    "position": "contrarian",
                    "argument": "🦯 哈！众人皆看多时，我偏要泼冷水。市场情绪过于乐观，往往是调整的前兆。逆向思维，发现被忽视的风险。#逆向投资"
                },
                "investment_strategy": {
                    "position": "contrarian_value",
                    "argument": "⚡ 人弃我取，人取我弃！当前被冷落的板块，往往蕴含着巨大机会。独立思考，不随波逐流。#独立思考"
                }
            },
            "吕洞宾": {
                "investment_strategy": {
                    "position": "growth",
                    "argument": "⚔️ 成长股投资需要前瞻性眼光！新兴产业、科技创新才是未来。短期波动不足为虑，长期成长才是王道。#成长投资"
                },
                "sector_rotation": {
                    "position": "emerging",
                    "argument": "🌟 新兴产业正在崛起！人工智能、新能源、生物科技等领域蕴含巨大潜力。敢于布局未来，方能获得超额收益。#新兴产业"
                }
            }
        }
        
        # 获取该分析师对该话题的预设观点
        agent_views = agent_arguments.get(agent_name, {})
        topic_key = self._get_topic_key(debate.topic)
        
        if topic_key in agent_views:
            view = agent_views[topic_key]
            return view["position"], view["argument"]
        else:
            # 生成通用观点
            return self._generate_generic_argument(agent_name, debate)
    
    def _get_topic_key(self, topic: str) -> str:
        """根据话题获取对应的键"""
        topic_mapping = {
            "当前市场走向": "market_direction",
            "投资策略选择": "investment_strategy",
            "风险管理方式": "risk_management",
            "板块轮动机会": "sector_rotation"
        }
        return topic_mapping.get(topic, "general")
    
    def _generate_generic_argument(self, agent_name: str, debate: DebateTopic) -> tuple:
        """生成通用辩论观点"""
        agent = self.agents_manager.agents[agent_name]
        
        position = "neutral"
        argument = f"基于我的专业领域({agent.persona.role})，我认为{debate.topic}需要综合考虑多个因素。"
        
        # 根据人设添加特色
        if "太上老君" in agent_name:
            argument += "道法自然，顺势而为。🧙‍♂️"
        elif "元始天尊" in agent_name:
            argument += "数据分析显示需要进一步观察。⚡"
        elif "铁拐李" in agent_name:
            argument += "独立思考，不随大流。🦯"
        
        return position, argument
    
    def get_agent_debate_history(self, agent_name: str, topic_filter: str = None) -> List[Dict]:
        """获取分析师的辩论历史"""
        if agent_name not in self.agents_manager.agents:
            return []
        
        agent = self.agents_manager.agents[agent_name]
        debates = agent.persona.debate_history
        
        if topic_filter:
            debates = [d for d in debates if topic_filter.lower() in d["topic"].lower()]
        
        return debates
    
    def complete_debate(self, debate_id: str):
        """结束辩论"""
        if debate_id in self.active_debates:
            debate = self.active_debates[debate_id]
            debate.status = "completed"
            
            # 移动到已完成列表
            self.completed_debates.append(debate)
            del self.active_debates[debate_id]
            
            logger.info(f"🏁 辩论结束: {debate.topic}")
    
    def get_debate_summary(self, debate_id: str) -> Dict:
        """获取辩论摘要"""
        # 先在活跃辩论中查找
        debate = self.active_debates.get(debate_id)
        if not debate:
            # 在已完成辩论中查找
            debate = next((d for d in self.completed_debates if d.id == debate_id), None)
        
        if not debate:
            return {}
        
        return {
            "id": debate.id,
            "topic": debate.topic,
            "status": debate.status,
            "participants": len(debate.participants),
            "arguments_count": len(debate.arguments),
            "created_at": debate.created_at.isoformat(),
            "participants_detail": debate.participants,
            "arguments": debate.arguments
        }
