# -*- coding: utf-8 -*-
"""
远程MCP客户端
连接韩国服务器上的N8N + MCP生态系统，为本地太公心易提供远程数据支持
"""

import os
import json
import asyncio
import logging
from datetime import datetime
from typing import Dict, List, Any, Optional
import httpx
from dataclasses import dataclass
from dotenv import load_dotenv

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 加载环境变量
load_dotenv(dotenv_path='.env')

@dataclass
class RemoteMCPConfig:
    """远程MCP配置"""
    server_url: str
    api_key: Optional[str] = None
    timeout: int = 30
    retry_count: int = 3

class RemoteMCPClient:
    """远程MCP客户端"""
    
    def __init__(self, config: RemoteMCPConfig):
        self.config = config
        self.session = None
        
        # 远程MCP服务端点
        self.endpoints = {
            'crypto_data': '/api/mcp/crypto-data',
            'news_feed': '/api/mcp/news-feed',
            'market_analysis': '/api/mcp/market-analysis',
            'trigger_analysis': '/api/mcp/trigger-analysis',
            'health_check': '/api/health'
        }
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        self.session = httpx.AsyncClient(
            timeout=self.config.timeout,
            headers=self._get_headers()
        )
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        if self.session:
            await self.session.aclose()
    
    def _get_headers(self) -> Dict[str, str]:
        """获取请求头"""
        headers = {
            'Content-Type': 'application/json',
            'User-Agent': 'TaigongXinyi-MCP-Client/1.0'
        }
        
        if self.config.api_key:
            headers['Authorization'] = f'Bearer {self.config.api_key}'
        
        return headers
    
    async def health_check(self) -> bool:
        """健康检查"""
        try:
            url = f"{self.config.server_url}{self.endpoints['health_check']}"
            response = await self.session.get(url)
            
            if response.status_code == 200:
                logger.info("✅ 远程MCP服务器连接正常")
                return True
            else:
                logger.warning(f"⚠️ 远程MCP服务器响应异常: {response.status_code}")
                return False
                
        except Exception as e:
            logger.error(f"❌ 远程MCP服务器连接失败: {e}")
            return False
    
    async def get_crypto_data(self, symbols: Optional[List[str]] = None) -> Dict[str, Any]:
        """获取加密货币数据"""
        try:
            url = f"{self.config.server_url}{self.endpoints['crypto_data']}"
            params = {}
            
            if symbols:
                params['symbols'] = ','.join(symbols)
            
            response = await self.session.get(url, params=params)
            response.raise_for_status()
            
            data = response.json()
            logger.info(f"📊 获取到 {len(data.get('events', []))} 个加密货币事件")
            
            return data
            
        except Exception as e:
            logger.error(f"❌ 获取加密货币数据失败: {e}")
            return {}
    
    async def get_news_feed(self, categories: Optional[List[str]] = None, limit: int = 20) -> Dict[str, Any]:
        """获取新闻流"""
        try:
            url = f"{self.config.server_url}{self.endpoints['news_feed']}"
            params = {'limit': limit}
            
            if categories:
                params['categories'] = ','.join(categories)
            
            response = await self.session.get(url, params=params)
            response.raise_for_status()
            
            data = response.json()
            logger.info(f"📰 获取到 {len(data.get('news', []))} 条新闻")
            
            return data
            
        except Exception as e:
            logger.error(f"❌ 获取新闻流失败: {e}")
            return {}
    
    async def request_market_analysis(self, query: str, analysis_type: str = 'comprehensive') -> Dict[str, Any]:
        """请求市场分析"""
        try:
            url = f"{self.config.server_url}{self.endpoints['market_analysis']}"
            payload = {
                'query': query,
                'analysis_type': analysis_type,
                'timestamp': datetime.now().isoformat()
            }
            
            response = await self.session.post(url, json=payload)
            response.raise_for_status()
            
            data = response.json()
            logger.info(f"🔮 市场分析请求完成: {query}")
            
            return data
            
        except Exception as e:
            logger.error(f"❌ 市场分析请求失败: {e}")
            return {}
    
    async def trigger_remote_analysis(self, trigger_data: Dict[str, Any]) -> Dict[str, Any]:
        """触发远程分析"""
        try:
            url = f"{self.config.server_url}{self.endpoints['trigger_analysis']}"
            
            response = await self.session.post(url, json=trigger_data)
            response.raise_for_status()
            
            data = response.json()
            logger.info("🚀 远程分析触发成功")
            
            return data
            
        except Exception as e:
            logger.error(f"❌ 触发远程分析失败: {e}")
            return {}

class TaigongRemoteIntegration:
    """太公心易远程集成"""
    
    def __init__(self, server_url: str, api_key: Optional[str] = None):
        self.config = RemoteMCPConfig(
            server_url=server_url,
            api_key=api_key
        )
    
    async def get_enhanced_market_intelligence(self, query: str) -> Dict[str, Any]:
        """获取增强的市场情报（本地+远程）"""
        
        intelligence = {
            'query': query,
            'timestamp': datetime.now().isoformat(),
            'local_data': {},
            'remote_data': {},
            'combined_analysis': {}
        }
        
        try:
            # 1. 获取本地数据
            local_data = await self._get_local_intelligence(query)
            intelligence['local_data'] = local_data
            
            # 2. 获取远程数据
            async with RemoteMCPClient(self.config) as client:
                # 健康检查
                if await client.health_check():
                    
                    # 获取远程加密货币数据
                    crypto_data = await client.get_crypto_data()
                    
                    # 获取远程新闻数据
                    news_data = await client.get_news_feed(
                        categories=['crypto', 'finance', 'technology']
                    )
                    
                    # 请求远程分析
                    analysis_data = await client.request_market_analysis(query)
                    
                    intelligence['remote_data'] = {
                        'crypto': crypto_data,
                        'news': news_data,
                        'analysis': analysis_data
                    }
                    
                    # 3. 融合分析
                    combined_analysis = self._combine_local_remote_analysis(
                        local_data, intelligence['remote_data']
                    )
                    intelligence['combined_analysis'] = combined_analysis
                    
                else:
                    logger.warning("⚠️ 远程服务不可用，仅使用本地数据")
                    intelligence['combined_analysis'] = local_data
            
            return intelligence
            
        except Exception as e:
            logger.error(f"❌ 获取增强市场情报失败: {e}")
            intelligence['combined_analysis'] = intelligence['local_data']
            return intelligence
    
    async def _get_local_intelligence(self, query: str) -> Dict[str, Any]:
        """获取本地情报"""
        try:
            # 这里集成本地的免费数据源
            from free_crypto_newsflow import FreeCryptoNewsFlow
            
            local_system = FreeCryptoNewsFlow()
            local_report = await local_system.generate_free_newsflow_report()
            
            return {
                'source': 'local',
                'data': local_report,
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"❌ 获取本地情报失败: {e}")
            return {}
    
    def _combine_local_remote_analysis(self, local_data: Dict[str, Any], remote_data: Dict[str, Any]) -> Dict[str, Any]:
        """融合本地和远程分析"""
        
        combined = {
            'data_sources': [],
            'market_events_total': 0,
            'news_total': 0,
            'analysis_summary': '',
            'confidence_level': 0.5,
            'recommendations': []
        }
        
        try:
            # 统计数据源
            if local_data:
                combined['data_sources'].append('local_free_apis')
                local_events = local_data.get('data', {}).get('market_events_count', 0)
                combined['market_events_total'] += local_events
            
            if remote_data.get('crypto'):
                combined['data_sources'].append('remote_crypto_mcp')
                remote_events = len(remote_data['crypto'].get('events', []))
                combined['market_events_total'] += remote_events
            
            if remote_data.get('news'):
                combined['data_sources'].append('remote_news_mcp')
                combined['news_total'] = len(remote_data['news'].get('news', []))
            
            # 生成综合分析摘要
            if combined['market_events_total'] > 0 or combined['news_total'] > 0:
                combined['analysis_summary'] = f"综合分析发现 {combined['market_events_total']} 个市场事件和 {combined['news_total']} 条相关新闻。"
                combined['confidence_level'] = 0.8
                combined['recommendations'] = [
                    "基于多源数据分析，建议关注市场动态",
                    "远程和本地数据显示一致性较好",
                    "建议结合技术分析进行决策"
                ]
            else:
                combined['analysis_summary'] = "当前市场相对平静，暂无重大事件。"
                combined['recommendations'] = ["保持观望，等待更明确的信号"]
            
            return combined
            
        except Exception as e:
            logger.error(f"❌ 融合分析失败: {e}")
            return combined

# 配置示例
def create_korean_server_config():
    """创建韩国服务器配置示例"""
    
    # 从环境变量读取配置
    server_url = os.getenv('KOREAN_MCP_SERVER_URL', 'https://your-korean-server.com')
    api_key = os.getenv('KOREAN_MCP_API_KEY')
    
    return RemoteMCPConfig(
        server_url=server_url,
        api_key=api_key,
        timeout=30,
        retry_count=3
    )

# 使用示例
async def test_remote_integration():
    """测试远程集成"""
    try:
        print("🌏 测试太公心易远程集成...")
        
        # 配置韩国服务器连接
        server_url = "https://your-korean-server.com"  # 替换为实际URL
        api_key = None  # 如果需要认证，设置API密钥
        
        integration = TaigongRemoteIntegration(server_url, api_key)
        
        # 获取增强市场情报
        intelligence = await integration.get_enhanced_market_intelligence(
            query="比特币价格走势分析"
        )
        
        print(f"📊 情报获取完成:")
        print(f"  数据源: {', '.join(intelligence['combined_analysis']['data_sources'])}")
        print(f"  市场事件: {intelligence['combined_analysis']['market_events_total']}")
        print(f"  新闻数量: {intelligence['combined_analysis']['news_total']}")
        print(f"  置信度: {intelligence['combined_analysis']['confidence_level']:.2f}")
        print(f"  分析摘要: {intelligence['combined_analysis']['analysis_summary']}")
        
        print("\n✅ 远程集成测试完成！")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")

if __name__ == "__main__":
    asyncio.run(test_remote_integration())
