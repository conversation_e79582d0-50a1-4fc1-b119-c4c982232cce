"""基于Streamlit的AI辩论系统
使用AutoGen实现多智能体辩论，完全移除Chainlit依赖
"""

import asyncio
import json
from datetime import datetime
from typing import Dict, List, Optional, Any
import streamlit as st
from pathlib import Path
import os

# AutoGen imports - 兼容不同版本
try:
    from autogen_ext.models.openai import OpenAIChatCompletionClient
    AUTOGEN_AVAILABLE = True
except ImportError:
    AUTOGEN_AVAILABLE = False
    
# 其他AutoGen组件（可选）
try:
    from autogen_core.base import MessageContext
    AUTOGEN_CORE_AVAILABLE = True
except ImportError:
    AUTOGEN_CORE_AVAILABLE = False

class StreamlitDebateAgent:
    """Streamlit版本的辩论智能体"""
    
    def __init__(self, name: str, role: str, position: str, personality: str, api_key: str = None):
        self.name = name
        self.role = role
        self.position = position  # "正方" 或 "反方"
        self.personality = personality
        self.api_key = api_key
        self.model_client = None
        
        if AUTOGEN_AVAILABLE and api_key:
            try:
                # 导入ModelInfo
                from autogen_core.models import ModelInfo
                
                model_info = ModelInfo(
                    model="deepseek/deepseek-chat-v3-0324:free",
                    api_type="openai",
                    api_version=None,
                    vision=False,
                    function_calling=False,
                    json_output=False,
                    family="八仙family"
                )
                
                self.model_client = OpenAIChatCompletionClient(
                    model="deepseek/deepseek-chat-v3-0324:free",
                    model_info=model_info,
                    api_key=api_key,
                    base_url="https://openrouter.ai/api/v1"
                )
                print(f"✅ {name} 模型客户端初始化成功")
            except Exception as e:
                print(f"❌ 初始化{name}的模型失败: {e}")
                self.model_client = None
    
    async def generate_response(self, topic: str, context: List[Dict], round_num: int) -> str:
        """生成辩论回应"""
        if not self.model_client:
            return f"[{self.name}] 模型未初始化，无法生成回应。"
        
        try:
            # 构建提示词
            system_prompt = f"""
你是{self.name}，{self.role}。你的性格特点：{self.personality}
你代表{self.position}，正在参与关于"{topic}"的辩论。

辩论规则：
1. 保持角色特色，体现你的性格
2. 逻辑清晰，论据充分
3. 尊重对手，文明辩论
4. 每次发言控制在200字以内
5. 针对前面的发言进行回应或反驳

当前是第{round_num}轮辩论。
"""
            
            # 构建对话历史
            messages = [{"role": "system", "content": system_prompt}]
            
            # 添加辩论历史
            for msg in context[-5:]:  # 只取最近5条消息
                messages.append({
                    "role": "user",
                    "content": f"{msg['speaker']}: {msg['content']}"
                })
            
            messages.append({
                "role": "user",
                "content": f"现在轮到你发言了，请针对以上内容进行{self.position}的论述。"
            })
            
            # 调用模型
            response = await self.model_client.create(
                messages=messages,
                max_tokens=300,
                temperature=0.8
            )
            
            return response.content
            
        except Exception as e:
            return f"[{self.name}] 生成回应时出错: {str(e)}"

class StreamlitDebateSystem:
    """基于Streamlit的辩论系统"""
    
    def __init__(self):
        self.agents = {}
        self.debate_history = []
        self.current_round = 0
        self.max_rounds = 3
        self.topic = ""
        self.is_running = False
        
        # 初始化八仙辩论阵容
        self._initialize_agents()
    
    def _initialize_agents(self):
        """初始化八仙辩论阵容"""
        # 从环境变量获取API密钥（使用现有的密钥配置）
        base_keys = [
            os.getenv("OPENROUTER_API_KEY_1"),
            os.getenv("OPENROUTER_API_KEY_2"), 
            os.getenv("OPENROUTER_API_KEY_3"),
            os.getenv("OPENROUTER_API_KEY_4")
        ]
        
        # 分配密钥给不同角色（负载均衡）
        api_keys = {
            "moderator": base_keys[0],  # 主持人用第1个密钥
            "pro_1": base_keys[0],      # 正方一辩用第1个密钥
            "con_1": base_keys[1],      # 反方一辩用第2个密钥
            "pro_2": base_keys[1],      # 正方二辩用第2个密钥
            "con_2": base_keys[2],      # 反方二辩用第3个密钥
            "pro_3": base_keys[2],      # 正方三辩用第3个密钥
            "con_3": base_keys[3],      # 反方三辩用第4个密钥
            "pro_4": base_keys[3],      # 正方四辩用第4个密钥
            "con_4": base_keys[0]       # 反方四辩用第1个密钥
        }
        
        # 定义八仙角色
        agents_config = [
            {
                "name": "灵宝道君",
                "role": "主持人",
                "position": "中立",
                "personality": "公正严明，善于引导讨论，维持辩论秩序",
                "key": "moderator"
            },
            {
                "name": "吕洞宾",
                "role": "正方一辩",
                "position": "正方",
                "personality": "智慧深邃，逻辑严密，善于开场立论",
                "key": "pro_1"
            },
            {
                "name": "何仙姑",
                "role": "反方一辩",
                "position": "反方",
                "personality": "温和理性，善于发现问题，逻辑清晰",
                "key": "con_1"
            },
            {
                "name": "张果老",
                "role": "正方二辩",
                "position": "正方",
                "personality": "经验丰富，善于举例论证，说服力强",
                "key": "pro_2"
            },
            {
                "name": "韩湘子",
                "role": "反方二辩",
                "position": "反方",
                "personality": "年轻敏锐，善于质疑，思维活跃",
                "key": "con_2"
            },
            {
                "name": "汉钟离",
                "role": "正方三辩",
                "position": "正方",
                "personality": "稳重大气，善于总结归纳，论证有力",
                "key": "pro_3"
            },
            {
                "name": "蓝采和",
                "role": "反方三辩",
                "position": "反方",
                "personality": "灵活机智，善于反驳，角度独特",
                "key": "con_3"
            },
            {
                "name": "曹国舅",
                "role": "正方四辩",
                "position": "正方",
                "personality": "庄重威严，善于总结陈词，气势磅礴",
                "key": "pro_4"
            },
            {
                "name": "铁拐李",
                "role": "反方四辩",
                "position": "反方",
                "personality": "直率犀利，善于最后反击，一针见血",
                "key": "con_4"
            }
        ]
        
        # 创建智能体
        for config in agents_config:
            api_key = api_keys.get(config["key"])
            agent = StreamlitDebateAgent(
                name=config["name"],
                role=config["role"],
                position=config["position"],
                personality=config["personality"],
                api_key=api_key
            )
            self.agents[config["name"]] = agent
    
    def start_debate(self, topic: str, max_rounds: int = 3):
        """开始辩论"""
        self.topic = topic
        self.max_rounds = max_rounds
        self.current_round = 0
        self.debate_history = []
        self.is_running = True
        
        # 主持人开场
        opening_message = {
            "speaker": "🎤 灵宝道君",
            "content": f"欢迎各位仙人参与今日辩论。今天的主题是：{topic}。现在有请正方一辩吕洞宾开始立论。",
            "timestamp": datetime.now().strftime("%H:%M:%S"),
            "round": 0,
            "position": "中立"
        }
        self.debate_history.append(opening_message)
    
    async def next_speaker(self) -> Optional[Dict]:
        """获取下一个发言者的回应"""
        if not self.is_running or self.current_round >= self.max_rounds:
            return None
        
        # 确定发言顺序
        speaking_order = [
            "吕洞宾", "何仙姑", "张果老", "韩湘子",
            "汉钟离", "蓝采和", "曹国舅", "铁拐李"
        ]
        
        current_speaker_index = len(self.debate_history) - 1  # 减去主持人开场
        
        if current_speaker_index >= len(speaking_order):
            self.current_round += 1
            if self.current_round >= self.max_rounds:
                return await self._generate_closing_statement()
            current_speaker_index = 0
        
        speaker_name = speaking_order[current_speaker_index]
        agent = self.agents[speaker_name]
        
        # 生成回应
        response = await agent.generate_response(
            self.topic, 
            self.debate_history, 
            self.current_round + 1
        )
        
        # 添加到历史记录
        message = {
            "speaker": f"{self._get_agent_emoji(speaker_name)} {speaker_name}",
            "content": response,
            "timestamp": datetime.now().strftime("%H:%M:%S"),
            "round": self.current_round + 1,
            "position": agent.position
        }
        
        self.debate_history.append(message)
        return message
    
    async def _generate_closing_statement(self) -> Dict:
        """生成主持人总结"""
        moderator = self.agents["灵宝道君"]
        
        closing_content = f"""经过{self.current_round}轮精彩辩论，各位仙人都展现了深厚的智慧。
        
本次辩论主题：{self.topic}
        
正方观点总结：支持该观点的仙人们提出了有力的论据...
反方观点总结：反对该观点的仙人们也展现了深刻的洞察...
        
辩论到此结束，感谢各位仙人的精彩表现！"""
        
        message = {
            "speaker": "🎤 灵宝道君",
            "content": closing_content,
            "timestamp": datetime.now().strftime("%H:%M:%S"),
            "round": self.current_round,
            "position": "中立"
        }
        
        self.debate_history.append(message)
        self.is_running = False
        return message
    
    def _get_agent_emoji(self, name: str) -> str:
        """获取智能体对应的emoji"""
        emoji_map = {
            "灵宝道君": "🎤",
            "吕洞宾": "⚡",
            "何仙姑": "🌍",
            "张果老": "🌊",
            "韩湘子": "⛰️",
            "汉钟离": "🔥",
            "蓝采和": "💧",
            "曹国舅": "⚡",
            "铁拐李": "🌪️"
        }
        return emoji_map.get(name, "🎭")
    
    def get_debate_stats(self) -> Dict:
        """获取辩论统计信息"""
        if not self.debate_history:
            return {}
        
        pro_count = sum(1 for msg in self.debate_history if msg.get("position") == "正方")
        con_count = sum(1 for msg in self.debate_history if msg.get("position") == "反方")
        
        return {
            "total_messages": len(self.debate_history),
            "current_round": self.current_round,
            "max_rounds": self.max_rounds,
            "pro_statements": pro_count,
            "con_statements": con_count,
            "is_running": self.is_running,
            "duration": "计算中..."
        }
    
    def stop_debate(self):
        """停止辩论"""
        self.is_running = False