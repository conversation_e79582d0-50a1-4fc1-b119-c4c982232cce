#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
兜率宫 (Tusita Palace) MCP服务器
太上老君的炼丹工坊 - 通过N8N工作流实现智能数据处理
"""

import os
import json
import asyncio
import logging
from datetime import datetime
from typing import Dict, List, Any, Optional
import httpx
from dataclasses import dataclass
from dotenv import load_dotenv

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 加载环境变量
load_dotenv()

@dataclass
class AlchemyRecipe:
    """炼丹配方"""
    recipe_id: str
    name: str
    description: str
    workflow_id: str
    ingredients: List[str]  # 所需原料
    output_type: str       # 产出类型
    processing_time: int   # 炼制时间(秒)

@dataclass
class AlchemyResult:
    """炼丹结果"""
    execution_id: str
    recipe_id: str
    status: str           # pending, running, completed, failed
    start_time: datetime
    end_time: Optional[datetime]
    result_data: Optional[Dict[str, Any]]
    error_message: Optional[str]

class TusitaPalaceMCP:
    """兜率宫MCP服务器 - 太上老君的炼丹工坊"""
    
    def __init__(self):
        # N8N连接配置
        self.n8n_base_url = os.getenv('N8N_BASE_URL', 'http://localhost:5678')
        self.n8n_api_key = os.getenv('N8N_API_KEY')
        self.webhook_base_url = f"{self.n8n_base_url}/webhook"
        
        # 炼丹配方库
        self.alchemy_recipes = {
            'rss_elixir': AlchemyRecipe(
                recipe_id='rss_elixir',
                name='RSS仙丹',
                description='将RSS新闻炼制成智能情报',
                workflow_id='rss_processing_workflow',
                ingredients=['rss_feeds', 'sentiment_analyzer', 'keyword_extractor'],
                output_type='intelligence_document',
                processing_time=60
            ),
            'market_pill': AlchemyRecipe(
                recipe_id='market_pill',
                name='市场灵丹',
                description='将市场数据炼制成投资建议',
                workflow_id='market_analysis_workflow',
                ingredients=['price_data', 'volume_data', 'taigong_analyzer'],
                output_type='investment_advice',
                processing_time=120
            ),
            'debate_potion': AlchemyRecipe(
                recipe_id='debate_potion',
                name='辩论仙液',
                description='触发稷下学宫八仙辩论',
                workflow_id='jixia_debate_workflow',
                ingredients=['market_event', 'historical_data', 'eight_immortals'],
                output_type='debate_conclusion',
                processing_time=180
            ),
            'wisdom_essence': AlchemyRecipe(
                recipe_id='wisdom_essence',
                name='智慧精华',
                description='综合分析生成太公心易报告',
                workflow_id='taigong_analysis_workflow',
                ingredients=['all_data_sources', 'three_brain_architecture'],
                output_type='taigong_report',
                processing_time=300
            )
        }
        
        # 炼丹记录
        self.active_alchemy = {}  # execution_id -> AlchemyResult
    
    async def list_alchemy_recipes(self) -> List[Dict[str, Any]]:
        """列出所有炼丹配方"""
        recipes = []
        for recipe in self.alchemy_recipes.values():
            recipes.append({
                'recipe_id': recipe.recipe_id,
                'name': recipe.name,
                'description': recipe.description,
                'ingredients': recipe.ingredients,
                'output_type': recipe.output_type,
                'processing_time': recipe.processing_time
            })
        return recipes
    
    async def start_alchemy(self, recipe_id: str, ingredients: Dict[str, Any]) -> AlchemyResult:
        """开始炼丹过程"""
        try:
            if recipe_id not in self.alchemy_recipes:
                raise ValueError(f"未知的炼丹配方: {recipe_id}")
            
            recipe = self.alchemy_recipes[recipe_id]
            
            logger.info(f"🔥 太上老君开始炼制 {recipe.name}...")
            
            # 通过webhook触发N8N工作流
            webhook_url = f"{self.webhook_base_url}/tusita-palace/{recipe_id}"
            
            payload = {
                'recipe_id': recipe_id,
                'recipe_name': recipe.name,
                'ingredients': ingredients,
                'start_time': datetime.now().isoformat(),
                'alchemist': '太上老君'
            }
            
            async with httpx.AsyncClient() as client:
                response = await client.post(webhook_url, json=payload, timeout=30)
                response.raise_for_status()
                
                execution_data = response.json()
                execution_id = execution_data.get('executionId', f"exec_{datetime.now().timestamp()}")
            
            # 创建炼丹记录
            alchemy_result = AlchemyResult(
                execution_id=execution_id,
                recipe_id=recipe_id,
                status='running',
                start_time=datetime.now(),
                end_time=None,
                result_data=None,
                error_message=None
            )
            
            self.active_alchemy[execution_id] = alchemy_result
            
            logger.info(f"✅ 炼丹过程已启动: {execution_id}")
            return alchemy_result
            
        except Exception as e:
            logger.error(f"❌ 炼丹启动失败: {e}")
            raise
    
    async def check_alchemy_status(self, execution_id: str) -> AlchemyResult:
        """检查炼丹进度"""
        if execution_id not in self.active_alchemy:
            raise ValueError(f"未找到炼丹记录: {execution_id}")
        
        alchemy_result = self.active_alchemy[execution_id]
        
        try:
            # 通过N8N API查询执行状态
            if self.n8n_api_key:
                api_url = f"{self.n8n_base_url}/api/v1/executions/{execution_id}"
                headers = {"X-N8N-API-KEY": self.n8n_api_key}
                
                async with httpx.AsyncClient() as client:
                    response = await client.get(api_url, headers=headers)
                    if response.status_code == 200:
                        execution_data = response.json()
                        
                        # 更新状态
                        if execution_data.get('finished'):
                            alchemy_result.status = 'completed' if execution_data.get('success') else 'failed'
                            alchemy_result.end_time = datetime.now()
                            alchemy_result.result_data = execution_data.get('data')
                            
                            if not execution_data.get('success'):
                                alchemy_result.error_message = execution_data.get('error', '炼丹失败')
            
            return alchemy_result
            
        except Exception as e:
            logger.error(f"❌ 查询炼丹状态失败: {e}")
            alchemy_result.status = 'failed'
            alchemy_result.error_message = str(e)
            return alchemy_result
    
    async def get_alchemy_result(self, execution_id: str) -> Dict[str, Any]:
        """获取炼丹结果"""
        alchemy_result = await self.check_alchemy_status(execution_id)
        
        if alchemy_result.status == 'completed':
            recipe = self.alchemy_recipes[alchemy_result.recipe_id]
            
            return {
                'execution_id': execution_id,
                'recipe_name': recipe.name,
                'status': 'completed',
                'start_time': alchemy_result.start_time.isoformat(),
                'end_time': alchemy_result.end_time.isoformat() if alchemy_result.end_time else None,
                'processing_duration': (alchemy_result.end_time - alchemy_result.start_time).total_seconds() if alchemy_result.end_time else None,
                'result_data': alchemy_result.result_data,
                'output_type': recipe.output_type
            }
        else:
            return {
                'execution_id': execution_id,
                'status': alchemy_result.status,
                'error_message': alchemy_result.error_message
            }
    
    async def trigger_emergency_alchemy(self, event_type: str, event_data: Dict[str, Any]) -> AlchemyResult:
        """紧急炼丹 - 响应突发市场事件"""
        logger.info(f"🚨 紧急炼丹触发: {event_type}")
        
        # 根据事件类型选择炼丹配方
        recipe_mapping = {
            'market_crash': 'market_pill',
            'breaking_news': 'rss_elixir',
            'price_surge': 'debate_potion',
            'major_event': 'wisdom_essence'
        }
        
        recipe_id = recipe_mapping.get(event_type, 'rss_elixir')
        
        # 准备紧急炼丹原料
        emergency_ingredients = {
            'event_type': event_type,
            'event_data': event_data,
            'priority': 'emergency',
            'timestamp': datetime.now().isoformat()
        }
        
        return await self.start_alchemy(recipe_id, emergency_ingredients)
    
    async def get_palace_status(self) -> Dict[str, Any]:
        """获取兜率宫状态"""
        active_count = len([a for a in self.active_alchemy.values() if a.status == 'running'])
        completed_count = len([a for a in self.active_alchemy.values() if a.status == 'completed'])
        failed_count = len([a for a in self.active_alchemy.values() if a.status == 'failed'])
        
        return {
            'palace_name': '兜率宫',
            'alchemist': '太上老君',
            'status': 'active',
            'available_recipes': len(self.alchemy_recipes),
            'active_alchemy': active_count,
            'completed_alchemy': completed_count,
            'failed_alchemy': failed_count,
            'total_alchemy_sessions': len(self.active_alchemy),
            'last_update': datetime.now().isoformat()
        }

# N8N工作流配置示例
def generate_n8n_workflows():
    """生成N8N工作流配置"""
    
    workflows = {
        'rss_processing_workflow': {
            'name': 'RSS仙丹炼制工作流',
            'description': '将RSS新闻炼制成智能情报',
            'nodes': [
                {
                    'name': 'Webhook触发',
                    'type': 'n8n-nodes-base.webhook',
                    'parameters': {'path': 'tusita-palace/rss_elixir'}
                },
                {
                    'name': 'RSS采集',
                    'type': 'n8n-nodes-base.rss',
                    'parameters': {'url': '={{$json.ingredients.rss_url}}'}
                },
                {
                    'name': '情感分析',
                    'type': 'n8n-nodes-base.httpRequest',
                    'parameters': {
                        'url': 'http://taigong-sentiment-api/analyze',
                        'method': 'POST'
                    }
                },
                {
                    'name': '存储到MongoDB',
                    'type': 'n8n-nodes-base.mongoDb',
                    'parameters': {
                        'collection': 'intelligence_documents',
                        'operation': 'insert'
                    }
                },
                {
                    'name': '向量化存储',
                    'type': 'n8n-nodes-base.httpRequest',
                    'parameters': {
                        'url': 'http://zilliz-api/vectorize',
                        'method': 'POST'
                    }
                }
            ]
        },
        
        'jixia_debate_workflow': {
            'name': '稷下学宫辩论工作流',
            'description': '触发八仙过海辩论',
            'nodes': [
                {
                    'name': 'Webhook触发',
                    'type': 'n8n-nodes-base.webhook',
                    'parameters': {'path': 'tusita-palace/debate_potion'}
                },
                {
                    'name': '八仙智能体调用',
                    'type': 'n8n-nodes-base.httpRequest',
                    'parameters': {
                        'url': 'http://jixia-academy/eight-immortals-debate',
                        'method': 'POST'
                    }
                },
                {
                    'name': '三清论道总结',
                    'type': 'n8n-nodes-base.httpRequest',
                    'parameters': {
                        'url': 'http://jixia-academy/sanqing-summary',
                        'method': 'POST'
                    }
                },
                {
                    'name': '结果通知',
                    'type': 'n8n-nodes-base.webhook',
                    'parameters': {'method': 'POST'}
                }
            ]
        }
    }
    
    return workflows

# 使用示例
async def demo_tusita_palace():
    """兜率宫演示"""
    palace = TusitaPalaceMCP()
    
    print("🏰 欢迎来到兜率宫!")
    print("太上老君的炼丹工坊已准备就绪")
    
    # 列出可用配方
    recipes = await palace.list_alchemy_recipes()
    print(f"\n📜 可用炼丹配方 ({len(recipes)}个):")
    for recipe in recipes:
        print(f"  🧪 {recipe['name']}: {recipe['description']}")
    
    # 开始炼制RSS仙丹
    print(f"\n🔥 开始炼制RSS仙丹...")
    ingredients = {
        'rss_url': 'https://feeds.finance.yahoo.com/rss/2.0/headline',
        'analysis_depth': 'deep',
        'target_sentiment': 'all'
    }
    
    try:
        alchemy_result = await palace.start_alchemy('rss_elixir', ingredients)
        print(f"✅ 炼丹已启动: {alchemy_result.execution_id}")
        
        # 检查状态
        status = await palace.check_alchemy_status(alchemy_result.execution_id)
        print(f"📊 当前状态: {status.status}")
        
        # 获取兜率宫状态
        palace_status = await palace.get_palace_status()
        print(f"\n🏰 兜率宫状态:")
        print(f"  炼丹师: {palace_status['alchemist']}")
        print(f"  活跃炼丹: {palace_status['active_alchemy']}")
        print(f"  可用配方: {palace_status['available_recipes']}")
        
    except Exception as e:
        print(f"❌ 炼丹失败: {e}")

if __name__ == "__main__":
    asyncio.run(demo_tusita_palace())
