# -*- coding: utf-8 -*-
"""
🐒 完美十二境界系统 - 基于您的神级设计
十二境界 × 十二长生 × GameFi对应的完美融合

您的原创设计表格：
境界 | 长生阶段 | 等级名称 | 季节 | 核心事件 | 长生寓意 | GameFi对应

这是投资GameFi的终极形态！
"""

from enum import Enum
from dataclasses import dataclass
from typing import Dict, List, Optional, Tuple
import pandas as pd

@dataclass
class PerfectRealm:
    """完美境界定义"""
    level: int
    longevity_stage: str  # 长生阶段
    realm_name: str       # 等级名称
    season: str          # 季节
    core_event: str      # 核心事件
    longevity_meaning: str # 长生寓意
    gamefi_mapping: str   # GameFi对应
    
    # 扩展属性
    story_detail: str = ""
    investment_lesson: str = ""
    unlock_condition: str = ""
    power_level: int = 0

class PerfectTwelveSystem:
    """完美十二境界系统"""
    
    # 基于您的神级设计的完整境界定义
    PERFECT_REALMS = [
        PerfectRealm(
            level=1,
            longevity_stage="胎",
            realm_name="花果山",
            season="春",
            core_event="日精月华孕育石猴",
            longevity_meaning="天地精华孕育生命",
            gamefi_mapping="新手村：接受市场精华",
            story_detail="海外有一国土，名曰傲来国。国近大海，海中有一座名山，唤为花果山。山上有一仙石，受日精月华，遂有灵通之意。",
            investment_lesson="投资如孕育生命，需要时间和天地精华的滋养",
            unlock_condition="开户入金，开始接受市场教育",
            power_level=10
        ),
        
        PerfectRealm(
            level=2,
            longevity_stage="养",
            realm_name="见生死",
            season="春",
            core_event="一跃成王，众猴失信",
            longevity_meaning="初生需要滋养呵护",
            gamefi_mapping="体验亏损：市场教育",
            story_detail="老猴道：'谁有本事的，钻进去寻个源头出来，不伤身体者，我等即拜他为王。'石猴一跃而入，众猴却忘了诺言。",
            investment_lesson="市场承诺往往不可靠，要学会在失信中成长",
            unlock_condition="首次交易亏损，体验市场残酷",
            power_level=25
        ),
        
        PerfectRealm(
            level=3,
            longevity_stage="长生",
            realm_name="求大道",
            season="春",
            core_event="求跳出三界外之法",
            longevity_meaning="开始追求长生不老",
            gamefi_mapping="拜师学艺：寻求投资真理",
            story_detail="石猴忧道：'我虽在欢喜之时，却有一点儿愁虑。'众猴问何愁虑？猴王道：'我等虽然快乐，但恐一旦身亡，如何是好？'",
            investment_lesson="意识到投资风险的存在，开始寻求长久之道",
            unlock_condition="经历重大亏损，开始学习投资理论",
            power_level=40
        ),
        
        PerfectRealm(
            level=4,
            longevity_stage="沐浴",
            realm_name="得所望",
            season="夏",
            core_event="学会72变神通",
            longevity_meaning="沐浴净化，获得新生",
            gamefi_mapping="首次成功：洗去新手标签",
            story_detail="祖师传授七十二般变化，筋斗云。猴王欢喜，朝夕温习，无不精通。",
            investment_lesson="通过学习获得投资技能，开始稳定盈利",
            unlock_condition="掌握基本投资技能，首次连续盈利",
            power_level=70
        ),
        
        PerfectRealm(
            level=5,
            longevity_stage="冠带",
            realm_name="傲气扬",
            season="夏",
            core_event="炫耀本领，傲视群雄",
            longevity_meaning="成年加冠，正式成人",
            gamefi_mapping="连续获利：建立声誉",
            story_detail="猴王显神通变化，众猴喝采。猴王道：'你们都有些眼力！我这般变化，可好么？'",
            investment_lesson="小有成就后容易骄傲，要保持谦逊之心",
            unlock_condition="连续3个月盈利，开始自满",
            power_level=90
        ),
        
        PerfectRealm(
            level=6,
            longevity_stage="临官",
            realm_name="逐师门",
            season="夏",
            core_event="被菩提祖师驱逐",
            longevity_meaning="临近官职但未得到",
            gamefi_mapping="因骄傲失去指导",
            story_detail="祖师大怒道：'你这去罢！再不要说是我的徒弟！'一时间推出门外，将中门关了。",
            investment_lesson="骄傲自满会失去宝贵的指导，要珍惜每一个老师",
            unlock_condition="因为骄傲偏离策略，失去导师指导",
            power_level=80
        ),
        
        PerfectRealm(
            level=7,
            longevity_stage="帝旺",
            realm_name="受招安",
            season="秋",
            core_event="天庭册封弼马温",
            longevity_meaning="达到权力巅峰",
            gamefi_mapping="接受体制：融入主流",
            story_detail="玉帝传旨，封为弼马温。猴王欢喜，以为是个大官。",
            investment_lesson="被表面的成功和地位蒙蔽，忽视了真实价值",
            unlock_condition="被市场热点吸引，偏离原有策略",
            power_level=120
        ),
        
        PerfectRealm(
            level=8,
            longevity_stage="衰",
            realm_name="喝玉液",
            season="秋",
            core_event="偷吃仙桃蟠桃会",
            longevity_meaning="盛极而衰的开始",
            gamefi_mapping="沉迷收益：忽视风险",
            story_detail="猴王见了仙桃，即取而食之。一连吃了许多个大桃。",
            investment_lesson="在牛市中贪婪，忽视了风险的积累",
            unlock_condition="满仓操作，沉迷于短期收益",
            power_level=100
        ),
        
        PerfectRealm(
            level=9,
            longevity_stage="病",
            realm_name="砸金銮",
            season="秋",
            core_event="大闹天宫踏破殿",
            longevity_meaning="疾病缠身，力不从心",
            gamefi_mapping="反叛权威：质疑传统",
            story_detail="猴王掣金箍棒，东一棒，西一棒，更无一个神仙敢近。",
            investment_lesson="与市场对抗，虽然短期有力，但终究力不从心",
            unlock_condition="孤注一掷，与市场趋势对抗",
            power_level=150
        ),
        
        PerfectRealm(
            level=10,
            longevity_stage="死",
            realm_name="终被擒",
            season="冬",
            core_event="被如来佛压制",
            longevity_meaning="走向死亡的终点",
            gamefi_mapping="重大失败：跌入谷底",
            story_detail="如来佛祖翻掌一扑，把这猴王推出西天门外，将五指化作五行山，扣住猴王。",
            investment_lesson="个人力量再强，也无法对抗整个系统",
            unlock_condition="遭遇重大失败，账户严重亏损",
            power_level=30
        ),
        
        PerfectRealm(
            level=11,
            longevity_stage="墓",
            realm_name="八卦炉",
            season="冬",
            core_event="炉中炼就火眼金睛",
            longevity_meaning="埋葬过去，孕育重生",
            gamefi_mapping="痛苦淬炼：重新学习",
            story_detail="太上老君将猴王推入八卦炉中，用文武火锻炼。不想那猴王在炉中炼就一双火眼金睛。",
            investment_lesson="痛苦的经历反而让人获得真正的洞察力",
            unlock_condition="在极度痛苦中反思，获得新的认知",
            power_level=60
        ),
        
        PerfectRealm(
            level=12,
            longevity_stage="绝",
            realm_name="五行山",
            season="冬",
            core_event="山下五百年修行",
            longevity_meaning="绝处逢生，涅槃重生",
            gamefi_mapping="大成境界：齐天大圣",
            story_detail="猴王在五行山下，饥食铁丸，渴饮铜汁，五百年不死不活。直到唐僧揭去符咒，重获自由。",
            investment_lesson="真正的成功需要时间的沉淀和内心的平静",
            unlock_condition="彻底觉悟，重新建立投资哲学",
            power_level=200
        )
    ]
    
    def __init__(self):
        self.current_level = 1
        self.total_experience = 0
        self.enlightenment_points = 0
    
    def get_realm_by_level(self, level: int) -> Optional[PerfectRealm]:
        """根据等级获取境界"""
        for realm in self.PERFECT_REALMS:
            if realm.level == level:
                return realm
        return None
    
    def get_current_realm(self) -> PerfectRealm:
        """获取当前境界"""
        return self.get_realm_by_level(self.current_level)
    
    def create_perfect_dataframe(self) -> pd.DataFrame:
        """创建完美境界表格 - 基于您的设计"""
        data = []
        for realm in self.PERFECT_REALMS:
            data.append({
                "境界": realm.level,
                "长生阶段": realm.longevity_stage,
                "等级名称": realm.realm_name,
                "季节": realm.season,
                "核心事件": realm.core_event,
                "长生寓意": realm.longevity_meaning,
                "GameFi对应": realm.gamefi_mapping,
                "力量等级": realm.power_level
            })
        
        return pd.DataFrame(data)
    
    def get_season_summary(self) -> Dict[str, str]:
        """获取四季总结"""
        return {
            "🌸 春季 (1-3级)": "觉醒篇 - 从花果山到求大道",
            "☀️ 夏季 (4-6级)": "成长篇 - 从得所望到逐师门", 
            "🍂 秋季 (7-9级)": "膨胀篇 - 从受招安到砸金銮",
            "❄️ 冬季 (10-12级)": "觉悟篇 - 从终被擒到五行山"
        }
    
    def get_longevity_cycle_explanation(self) -> str:
        """获取长生循环解释"""
        return """
        🔄 十二长生循环的投资哲学：
        
        胎→养→长生：孕育期，需要耐心培养
        沐浴→冠带→临官：成长期，逐步建立能力
        帝旺→衰→病：巅峰期，盛极而衰的警示
        死→墓→绝：低谷期，绝处逢生的机会
        
        这个循环告诉我们：投资如人生，有起有落，
        关键是要理解每个阶段的特点，顺势而为。
        """
    
    def advance_to_next_realm(self) -> Tuple[bool, Optional[PerfectRealm]]:
        """提升到下一境界"""
        if self.current_level < 12:
            self.current_level += 1
            new_realm = self.get_current_realm()
            self.enlightenment_points += new_realm.power_level
            return True, new_realm
        return False, None
    
    def get_progress_status(self) -> Dict:
        """获取进度状态"""
        current = self.get_current_realm()
        progress_percent = (self.current_level / 12) * 100
        
        return {
            "当前境界": current.realm_name,
            "长生阶段": current.longevity_stage,
            "当前季节": current.season,
            "等级": f"{self.current_level}/12",
            "完成度": f"{progress_percent:.1f}%",
            "力量等级": current.power_level,
            "觉悟点数": self.enlightenment_points,
            "核心事件": current.core_event,
            "GameFi对应": current.gamefi_mapping
        }

# 演示函数
def demo_perfect_system():
    """演示完美十二境界系统"""
    print("🐒 完美十二境界系统 - 基于您的神级设计")
    print("=" * 80)
    
    system = PerfectTwelveSystem()
    
    # 显示完美表格
    df = system.create_perfect_dataframe()
    print("\n📊 完美境界表格:")
    print(df.to_string(index=False, max_colwidth=30))
    
    # 显示四季总结
    print("\n🌸🌞🍂❄️ 四季修仙路径:")
    for season, description in system.get_season_summary().items():
        print(f"   {season}: {description}")
    
    # 显示长生循环解释
    print(system.get_longevity_cycle_explanation())
    
    # 显示当前状态
    print("\n📈 当前修仙状态:")
    status = system.get_progress_status()
    for key, value in status.items():
        print(f"   {key}: {value}")
    
    print("\n🎉 这就是投资GameFi的终极形态！")

if __name__ == "__main__":
    demo_perfect_system()