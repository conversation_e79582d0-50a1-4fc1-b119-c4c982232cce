#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
稷下学宫辩论结果提纯系统
将辩论原始数据提纯后存储到Zilliz向量数据库

核心流程：
1. 语义提纯：使用嵌入模型进行语义聚类和去重
2. 质量提纯：使用Rerank模型进行质量排序和筛选
3. 向量化存储：将提纯结果存储到Zilliz

作者：太公心易系统
版本：v1.0
"""

import asyncio
import json
import logging
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
import numpy as np
from sklearn.cluster import KMeans
from sklearn.metrics.pairwise import cosine_similarity
import openai
import os
from pymilvus import connections, Collection, FieldSchema, CollectionSchema, DataType
import hashlib
import aiohttp
from abc import ABC, abstractmethod

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# ============= 模型服务抽象接口 =============

class ModelService(ABC):
    """模型服务抽象基类"""

    @abstractmethod
    async def get_embeddings(self, texts: List[str]) -> List[List[float]]:
        """获取文本嵌入向量"""
        pass

    @abstractmethod
    async def rerank(self, query: str, documents: List[str]) -> List[float]:
        """文档重排序"""
        pass

    @abstractmethod
    async def chat_completion(self, messages: List[Dict[str, str]], max_tokens: int = 100) -> str:
        """聊天补全"""
        pass

class DirectAPIService(ModelService):
    """直接API调用服务"""

    def __init__(self, openai_api_key: str = None):
        self.openai_client = openai.OpenAI(api_key=openai_api_key or os.getenv('OPENAI_API_KEY'))
        self.embedding_model = "text-embedding-3-large"

    async def get_embeddings(self, texts: List[str]) -> List[List[float]]:
        """直接调用OpenAI嵌入API"""
        try:
            response = await asyncio.to_thread(
                self.openai_client.embeddings.create,
                model=self.embedding_model,
                input=texts
            )
            return [data.embedding for data in response.data]
        except Exception as e:
            logger.error(f"获取嵌入向量失败: {e}")
            return [[0.0] * 3072 for _ in texts]  # fallback

    async def rerank(self, query: str, documents: List[str]) -> List[float]:
        """使用GPT进行重排序"""
        scores = []
        for doc in documents:
            try:
                prompt = f"评估以下文档与查询的相关性(0-1):\n查询: {query}\n文档: {doc}\n只返回数字:"
                response = await asyncio.to_thread(
                    self.openai_client.chat.completions.create,
                    model="gpt-4o-mini",
                    messages=[{"role": "user", "content": prompt}],
                    max_tokens=10
                )
                score = float(response.choices[0].message.content.strip())
                scores.append(score)
            except:
                scores.append(0.5)  # 默认分数
        return scores

    async def chat_completion(self, messages: List[Dict[str, str]], max_tokens: int = 100) -> str:
        """聊天补全"""
        try:
            response = await asyncio.to_thread(
                self.openai_client.chat.completions.create,
                model="gpt-4o-mini",
                messages=messages,
                max_tokens=max_tokens
            )
            return response.choices[0].message.content.strip()
        except Exception as e:
            logger.error(f"聊天补全失败: {e}")
            return "处理失败，请稍后重试。"

class DifyService(ModelService):
    """Dify服务调用"""

    def __init__(self, dify_api_key: str = None, dify_base_url: str = None):
        self.api_key = dify_api_key or os.getenv('DIFY_API_KEY')
        self.base_url = dify_base_url or os.getenv('DIFY_BASE_URL', 'https://api.dify.ai/v1')
        self.session = None

    async def _get_session(self):
        """获取HTTP会话"""
        if not self.session:
            self.session = aiohttp.ClientSession(
                headers={'Authorization': f'Bearer {self.api_key}'}
            )
        return self.session

    async def get_embeddings(self, texts: List[str]) -> List[List[float]]:
        """通过Dify获取嵌入向量"""
        try:
            session = await self._get_session()
            async with session.post(
                f"{self.base_url}/embeddings",
                json={
                    "model": "text-embedding-3-large",
                    "input": texts
                }
            ) as response:
                data = await response.json()
                return [item['embedding'] for item in data['data']]
        except Exception as e:
            logger.error(f"Dify嵌入向量获取失败: {e}")
            return [[0.0] * 3072 for _ in texts]

    async def rerank(self, query: str, documents: List[str]) -> List[float]:
        """通过Dify进行重排序"""
        try:
            session = await self._get_session()
            async with session.post(
                f"{self.base_url}/rerank",
                json={
                    "model": "jina-reranker-v2-base-multilingual",
                    "query": query,
                    "documents": documents
                }
            ) as response:
                data = await response.json()
                return [item['relevance_score'] for item in data['results']]
        except Exception as e:
            logger.error(f"Dify重排序失败: {e}")
            return [0.5] * len(documents)

    async def chat_completion(self, messages: List[Dict[str, str]], max_tokens: int = 100) -> str:
        """通过Dify进行聊天补全"""
        try:
            session = await self._get_session()
            async with session.post(
                f"{self.base_url}/chat/completions",
                json={
                    "model": "gpt-4o-mini",
                    "messages": messages,
                    "max_tokens": max_tokens
                }
            ) as response:
                data = await response.json()
                return data['choices'][0]['message']['content']
        except Exception as e:
            logger.error(f"Dify聊天补全失败: {e}")
            return "处理失败，请稍后重试。"

    async def close(self):
        """关闭会话"""
        if self.session:
            await self.session.close()

# ============= 数据结构定义 =============

@dataclass
class DebateInsight:
    """辩论洞察结构"""
    content: str
    speaker: str
    stance: str  # 'positive', 'negative', 'neutral'
    confidence: float
    relevance_score: float
    logic_score: float
    embedding: Optional[List[float]] = None
    cluster_id: Optional[int] = None
    
@dataclass
class PurifiedResult:
    """提纯结果结构"""
    original_topic: str
    event_context: Dict[str, Any]
    core_insights: List[DebateInsight]
    consensus_points: List[str]
    divergent_points: List[str]
    final_recommendation: str
    purification_metadata: Dict[str, Any]

class DebatePurificationEngine:
    """辩论结果提纯引擎"""

    def __init__(self,
                 model_service: ModelService = None,
                 zilliz_endpoint: str = None,
                 zilliz_token: str = None,
                 use_dify: bool = False):

        # 模型服务配置
        if model_service:
            self.model_service = model_service
        elif use_dify:
            self.model_service = DifyService()
            logger.info("🚀 使用Dify模型服务")
        else:
            self.model_service = DirectAPIService()
            logger.info("🔧 使用直接API调用")

        # Zilliz配置
        self.zilliz_endpoint = zilliz_endpoint or os.getenv('ZILLIZ_ENDPOINT')
        self.zilliz_token = zilliz_token or os.getenv('ZILLIZ_TOKEN')

        # 提纯参数
        self.embedding_dim = 3072
        self.rerank_threshold = 0.7
        self.cluster_threshold = 0.8

        # 初始化Zilliz连接
        self._init_zilliz_connection()
        
    def _init_zilliz_connection(self):
        """初始化Zilliz连接"""
        try:
            connections.connect(
                alias="default",
                uri=self.zilliz_endpoint,
                token=self.zilliz_token
            )
            logger.info("✅ Zilliz连接成功")
        except Exception as e:
            logger.error(f"❌ Zilliz连接失败: {e}")
    
    async def purify_debate_results(self, debate_results: Dict[str, Any]) -> PurifiedResult:
        """主要提纯流程"""
        logger.info("🧪 开始辩论结果提纯...")
        
        # 1. 提取原始洞察
        raw_insights = self._extract_raw_insights(debate_results)
        logger.info(f"📝 提取到 {len(raw_insights)} 个原始洞察")
        
        # 2. 语义提纯
        semantic_insights = await self._semantic_purification(raw_insights)
        logger.info(f"🔍 语义提纯后剩余 {len(semantic_insights)} 个洞察")
        
        # 3. 质量提纯
        quality_insights = await self._quality_purification(
            semantic_insights, debate_results.get('topic', ''), 
            debate_results.get('event_context', {})
        )
        logger.info(f"⭐ 质量提纯后剩余 {len(quality_insights)} 个高质量洞察")
        
        # 4. 生成共识和分歧点
        consensus_points, divergent_points = self._analyze_consensus_divergence(quality_insights)
        
        # 5. 生成最终建议
        final_recommendation = await self._generate_final_recommendation(
            quality_insights, consensus_points, divergent_points
        )
        
        # 6. 构建提纯结果
        purified_result = PurifiedResult(
            original_topic=debate_results.get('topic', ''),
            event_context=debate_results.get('event_context', {}),
            core_insights=quality_insights,
            consensus_points=consensus_points,
            divergent_points=divergent_points,
            final_recommendation=final_recommendation,
            purification_metadata={
                'purification_time': datetime.now().isoformat(),
                'original_insights_count': len(raw_insights),
                'final_insights_count': len(quality_insights),
                'purification_ratio': len(quality_insights) / len(raw_insights) if raw_insights else 0
            }
        )
        
        logger.info("✅ 辩论结果提纯完成")
        return purified_result
    
    def _extract_raw_insights(self, debate_results: Dict[str, Any]) -> List[DebateInsight]:
        """从辩论结果中提取原始洞察"""
        insights = []
        
        # 从辩论消息中提取
        for message in debate_results.get('debate_messages', []):
            insight = DebateInsight(
                content=message.get('content', ''),
                speaker=message.get('speaker', ''),
                stance=self._determine_stance(message.get('speaker', '')),
                confidence=message.get('confidence', 0.5),
                relevance_score=0.0,  # 待计算
                logic_score=0.0       # 待计算
            )
            insights.append(insight)
        
        # 从总结中提取
        if 'summary' in debate_results:
            summary_insight = DebateInsight(
                content=debate_results['summary'],
                speaker='三清论道',
                stance='neutral',
                confidence=0.9,
                relevance_score=0.0,
                logic_score=0.0
            )
            insights.append(summary_insight)
        
        return insights
    
    def _determine_stance(self, speaker: str) -> str:
        """根据发言者确定立场"""
        positive_speakers = ["吕洞宾", "张果老", "汉钟离", "曹国舅"]
        negative_speakers = ["何仙姑", "韩湘子", "蓝采和", "铁拐李"]
        
        if speaker in positive_speakers:
            return 'positive'
        elif speaker in negative_speakers:
            return 'negative'
        else:
            return 'neutral'
    
    async def _semantic_purification(self, insights: List[DebateInsight]) -> List[DebateInsight]:
        """语义提纯：去重和聚类"""
        if not insights:
            return insights
        
        # 1. 生成嵌入向量
        contents = [insight.content for insight in insights]
        embeddings = await self._get_embeddings(contents)
        
        for i, insight in enumerate(insights):
            insight.embedding = embeddings[i]
        
        # 2. 语义去重
        unique_insights = self._semantic_deduplication(insights)
        
        # 3. 语义聚类
        clustered_insights = self._semantic_clustering(unique_insights)
        
        return clustered_insights
    
    async def _get_embeddings(self, texts: List[str]) -> List[List[float]]:
        """获取文本嵌入向量"""
        return await self.model_service.get_embeddings(texts)
    
    def _semantic_deduplication(self, insights: List[DebateInsight]) -> List[DebateInsight]:
        """语义去重"""
        if len(insights) <= 1:
            return insights
        
        unique_insights = []
        embeddings = np.array([insight.embedding for insight in insights])
        
        for i, insight in enumerate(insights):
            is_duplicate = False
            for j in range(len(unique_insights)):
                similarity = cosine_similarity(
                    [embeddings[i]], 
                    [embeddings[unique_insights[j]]]
                )[0][0]
                
                if similarity > self.cluster_threshold:
                    is_duplicate = True
                    # 保留置信度更高的洞察
                    if insight.confidence > insights[unique_insights[j]].confidence:
                        unique_insights[j] = i
                    break
            
            if not is_duplicate:
                unique_insights.append(i)
        
        return [insights[i] for i in unique_insights]
    
    def _semantic_clustering(self, insights: List[DebateInsight]) -> List[DebateInsight]:
        """语义聚类"""
        if len(insights) <= 2:
            return insights
        
        embeddings = np.array([insight.embedding for insight in insights])
        
        # 使用KMeans聚类
        n_clusters = min(len(insights) // 2, 5)  # 最多5个聚类
        kmeans = KMeans(n_clusters=n_clusters, random_state=42)
        cluster_labels = kmeans.fit_predict(embeddings)
        
        for i, insight in enumerate(insights):
            insight.cluster_id = int(cluster_labels[i])
        
        return insights
    
    async def _quality_purification(self, insights: List[DebateInsight], 
                                  topic: str, context: Dict[str, Any]) -> List[DebateInsight]:
        """质量提纯：相关性和逻辑性评分"""
        
        # 1. 计算相关性分数
        for insight in insights:
            insight.relevance_score = await self._calculate_relevance_score(
                insight.content, topic, context
            )
        
        # 2. 计算逻辑一致性分数
        for insight in insights:
            insight.logic_score = await self._calculate_logic_score(insight.content)
        
        # 3. 综合质量筛选
        quality_insights = [
            insight for insight in insights
            if (insight.relevance_score * 0.6 + insight.logic_score * 0.4) >= self.rerank_threshold
        ]
        
        # 4. 按质量排序
        quality_insights.sort(
            key=lambda x: x.relevance_score * 0.6 + x.logic_score * 0.4,
            reverse=True
        )
        
        return quality_insights
    
    async def _calculate_relevance_score(self, content: str, topic: str, context: Dict[str, Any]) -> float:
        """计算相关性分数"""
        try:
            prompt = f"""
            请评估以下内容与主题的相关性（0-1分）：

            主题：{topic}
            背景：{json.dumps(context, ensure_ascii=False)}
            内容：{content}

            只返回数字分数，不要解释。
            """

            response = await self.model_service.chat_completion(
                [{"role": "user", "content": prompt}],
                max_tokens=10
            )

            return float(response.strip())
        except:
            return 0.5  # 默认分数
    
    async def _calculate_logic_score(self, content: str) -> float:
        """计算逻辑一致性分数"""
        try:
            prompt = f"""
            请评估以下内容的逻辑一致性（0-1分）：
            
            内容：{content}
            
            评估标准：
            - 论证是否有逻辑
            - 结论是否合理
            - 是否有明显矛盾
            
            只返回数字分数，不要解释。
            """
            
            response = await asyncio.to_thread(
                self.openai_client.chat.completions.create,
                model="gpt-4o-mini",
                messages=[{"role": "user", "content": prompt}],
                max_tokens=10
            )
            
            score_text = response.choices[0].message.content.strip()
            return float(score_text)
        except:
            return 0.5  # 默认分数
    
    def _analyze_consensus_divergence(self, insights: List[DebateInsight]) -> Tuple[List[str], List[str]]:
        """分析共识点和分歧点"""
        positive_insights = [i for i in insights if i.stance == 'positive']
        negative_insights = [i for i in insights if i.stance == 'negative']
        neutral_insights = [i for i in insights if i.stance == 'neutral']
        
        # 共识点：中性观点和高质量洞察
        consensus_points = [
            insight.content for insight in neutral_insights
            if insight.relevance_score > 0.8
        ]
        
        # 分歧点：正反方的主要观点
        divergent_points = []
        if positive_insights:
            best_positive = max(positive_insights, key=lambda x: x.relevance_score)
            divergent_points.append(f"正方观点：{best_positive.content}")
        
        if negative_insights:
            best_negative = max(negative_insights, key=lambda x: x.relevance_score)
            divergent_points.append(f"反方观点：{best_negative.content}")
        
        return consensus_points, divergent_points
    
    async def _generate_final_recommendation(self, insights: List[DebateInsight], 
                                           consensus: List[str], 
                                           divergence: List[str]) -> str:
        """生成最终建议"""
        try:
            prompt = f"""
            基于稷下学宫辩论结果，生成最终投资建议：
            
            核心洞察：
            {json.dumps([i.content for i in insights[:5]], ensure_ascii=False, indent=2)}
            
            共识点：
            {json.dumps(consensus, ensure_ascii=False, indent=2)}
            
            分歧点：
            {json.dumps(divergence, ensure_ascii=False, indent=2)}
            
            请生成一个简洁、实用的投资建议（100字以内）。
            """
            
            response = await asyncio.to_thread(
                self.openai_client.chat.completions.create,
                model="gpt-4o-mini",
                messages=[{"role": "user", "content": prompt}],
                max_tokens=200
            )
            
            return response.choices[0].message.content.strip()
        except Exception as e:
            logger.error(f"生成最终建议失败: {e}")
            return "建议保持谨慎，综合考虑各方因素后做出决策。"
    
    async def store_to_zilliz(self, purified_result: PurifiedResult) -> bool:
        """将提纯结果存储到Zilliz"""
        try:
            # 创建或获取集合
            collection = self._get_or_create_collection("debate_insights")
            
            # 准备数据
            data_to_insert = []
            for insight in purified_result.core_insights:
                data_to_insert.append({
                    'id': self._generate_id(insight.content),
                    'content': insight.content,
                    'speaker': insight.speaker,
                    'stance': insight.stance,
                    'topic': purified_result.original_topic,
                    'relevance_score': insight.relevance_score,
                    'logic_score': insight.logic_score,
                    'embedding': insight.embedding,
                    'timestamp': datetime.now().isoformat()
                })
            
            # 插入数据
            collection.insert(data_to_insert)
            collection.flush()
            
            logger.info(f"✅ 成功存储 {len(data_to_insert)} 条提纯结果到Zilliz")
            return True
            
        except Exception as e:
            logger.error(f"❌ 存储到Zilliz失败: {e}")
            return False
    
    def _get_or_create_collection(self, collection_name: str):
        """获取或创建Zilliz集合"""
        # 定义字段
        fields = [
            FieldSchema(name="id", dtype=DataType.VARCHAR, max_length=64, is_primary=True),
            FieldSchema(name="content", dtype=DataType.VARCHAR, max_length=2000),
            FieldSchema(name="speaker", dtype=DataType.VARCHAR, max_length=50),
            FieldSchema(name="stance", dtype=DataType.VARCHAR, max_length=20),
            FieldSchema(name="topic", dtype=DataType.VARCHAR, max_length=200),
            FieldSchema(name="relevance_score", dtype=DataType.FLOAT),
            FieldSchema(name="logic_score", dtype=DataType.FLOAT),
            FieldSchema(name="embedding", dtype=DataType.FLOAT_VECTOR, dim=self.embedding_dim),
            FieldSchema(name="timestamp", dtype=DataType.VARCHAR, max_length=50)
        ]
        
        schema = CollectionSchema(fields, "稷下学宫辩论洞察集合")
        
        try:
            collection = Collection(collection_name, schema)
            logger.info(f"✅ 创建新集合: {collection_name}")
        except:
            collection = Collection(collection_name)
            logger.info(f"✅ 使用现有集合: {collection_name}")
        
        return collection
    
    def _generate_id(self, content: str) -> str:
        """生成内容ID"""
        return hashlib.md5(content.encode('utf-8')).hexdigest()[:16]

# 使用示例
async def main():
    """测试提纯系统"""
    engine = DebatePurificationEngine()
    
    # 模拟辩论结果
    mock_debate_results = {
        'topic': '美联储加息对市场的影响',
        'event_context': {'source': 'Fed News', 'impact': 'high'},
        'debate_messages': [
            {'speaker': '吕洞宾', 'content': '加息有利于控制通胀，长期利好市场'},
            {'speaker': '何仙姑', 'content': '加息会抑制经济增长，短期利空股市'},
            {'speaker': '张果老', 'content': '历史数据显示加息后科技股表现较好'},
            {'speaker': '韩湘子', 'content': '当前经济环境下加息风险较大'}
        ],
        'summary': '综合分析认为需要关注加息的时机和幅度'
    }
    
    # 执行提纯
    result = await engine.purify_debate_results(mock_debate_results)
    
    # 存储到Zilliz
    success = await engine.store_to_zilliz(result)
    
    print(f"提纯完成，存储状态: {success}")

if __name__ == "__main__":
    asyncio.run(main())
