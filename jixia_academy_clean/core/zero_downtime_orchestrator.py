# -*- coding: utf-8 -*-
"""
零停机编排器 (Zero Downtime Orchestrator)
实现交响乐团指挥模式的零停机架构

核心理念：
🎼 指挥永不停歇 (本地Ollama+RAG)
🎭 乐团热切换 (Heroku蓝绿部署)
🎯 智能路由 (健康检查+故障转移)
🔄 持续学习 (RAG知识库更新)

架构优势：
- 本地指挥：0成本，永不停机
- 云端乐团：按需调用，热切换
- 智能降级：云端故障时本地兜底
- 最小化SLA：实现99.9%+可用性

作者：太公心易BI系统
版本：v3.0 ZeroDowntime
"""

import asyncio
import json
import logging
import time
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
from pathlib import Path
import aiohttp
import yaml

# 本地模型
try:
    import ollama
    OLLAMA_AVAILABLE = True
except ImportError:
    OLLAMA_AVAILABLE = False

# RAG系统
from .local_conductor_system import LocalConductor, LocalRAGSystem, EventAssessment


@dataclass
class DeploymentConfig:
    """部署配置"""
    name: str
    url: str
    version: str
    status: str = "unknown"  # healthy, unhealthy, deploying, unknown
    last_health_check: Optional[datetime] = None
    consecutive_failures: int = 0
    response_time_ms: float = 0.0
    deployment_time: Optional[datetime] = None


@dataclass
class ZeroDowntimeMetrics:
    """零停机指标"""
    total_requests: int = 0
    successful_requests: int = 0
    failed_requests: int = 0
    local_handled: int = 0
    cloud_handled: int = 0
    average_response_time: float = 0.0
    uptime_percentage: float = 100.0
    cost_saved_usd: float = 0.0
    last_downtime: Optional[datetime] = None
    downtime_duration_seconds: float = 0.0


class ZeroDowntimeOrchestrator:
    """零停机编排器"""
    
    def __init__(self, config_path: str = "config/zero_downtime.yaml"):
        self.config_path = Path(config_path)
        self.logger = logging.getLogger('ZeroDowntimeOrchestrator')
        
        # 加载配置
        self.config = self._load_config()
        
        # 初始化组件
        self.rag_system = LocalRAGSystem()
        self.conductor = LocalConductor(rag_system=self.rag_system)
        
        # 部署配置
        self.deployments: Dict[str, DeploymentConfig] = {}
        self._init_deployments()
        
        # 指标
        self.metrics = ZeroDowntimeMetrics()
        
        # 健康检查任务
        self.health_check_task = None
        self.is_running = False
        
        # 蓝绿部署状态
        self.active_deployment = "blue"
        self.deployment_in_progress = False
        
    def _load_config(self) -> Dict:
        """加载配置文件"""
        default_config = {
            "health_check_interval": 30,
            "failover_threshold": 3,
            "response_timeout": 10,
            "deployments": {
                "blue": {
                    "url": "https://cauldron-blue.herokuapp.com",
                    "version": "v1.0.0"
                },
                "green": {
                    "url": "https://cauldron-green.herokuapp.com", 
                    "version": "v1.0.0"
                }
            },
            "local_fallback": {
                "enabled": True,
                "confidence_threshold": 0.6
            },
            "cost_optimization": {
                "local_first": True,
                "cloud_threshold_score": 80
            }
        }
        
        if self.config_path.exists():
            try:
                with open(self.config_path, 'r', encoding='utf-8') as f:
                    config = yaml.safe_load(f)
                    # 合并默认配置
                    default_config.update(config)
                    return default_config
            except Exception as e:
                self.logger.error(f"配置文件加载失败: {e}")
                
        return default_config
    
    def _init_deployments(self):
        """初始化部署配置"""
        for name, deploy_config in self.config["deployments"].items():
            self.deployments[name] = DeploymentConfig(
                name=name,
                url=deploy_config["url"],
                version=deploy_config["version"]
            )
    
    async def start(self):
        """启动零停机编排器"""
        self.logger.info("🎼 启动零停机编排器...")
        self.is_running = True
        
        # 启动健康检查
        self.health_check_task = asyncio.create_task(self._health_check_loop())
        
        # 初始健康检查
        await self._check_all_deployments()
        
        self.logger.info("✅ 零停机编排器启动完成")
    
    async def stop(self):
        """停止编排器"""
        self.logger.info("🛑 停止零停机编排器...")
        self.is_running = False
        
        if self.health_check_task:
            self.health_check_task.cancel()
            try:
                await self.health_check_task
            except asyncio.CancelledError:
                pass
        
        self.logger.info("✅ 零停机编排器已停止")
    
    async def _health_check_loop(self):
        """健康检查循环"""
        while self.is_running:
            try:
                await self._check_all_deployments()
                await asyncio.sleep(self.config["health_check_interval"])
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error(f"健康检查异常: {e}")
                await asyncio.sleep(5)
    
    async def _check_all_deployments(self):
        """检查所有部署的健康状态"""
        tasks = []
        for deployment in self.deployments.values():
            tasks.append(self._check_deployment_health(deployment))
        
        await asyncio.gather(*tasks, return_exceptions=True)
    
    async def _check_deployment_health(self, deployment: DeploymentConfig):
        """检查单个部署的健康状态"""
        start_time = time.time()
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(
                    f"{deployment.url}/health",
                    timeout=aiohttp.ClientTimeout(total=self.config["response_timeout"])
                ) as response:
                    response_time = (time.time() - start_time) * 1000
                    deployment.response_time_ms = response_time
                    deployment.last_health_check = datetime.now()
                    
                    if response.status == 200:
                        deployment.status = "healthy"
                        deployment.consecutive_failures = 0
                        self.logger.debug(f"✅ {deployment.name} 健康 ({response_time:.1f}ms)")
                    else:
                        deployment.consecutive_failures += 1
                        if deployment.consecutive_failures >= self.config["failover_threshold"]:
                            deployment.status = "unhealthy"
                        self.logger.warning(f"⚠️ {deployment.name} 响应异常: {response.status}")
                        
        except Exception as e:
            deployment.consecutive_failures += 1
            deployment.last_health_check = datetime.now()
            
            if deployment.consecutive_failures >= self.config["failover_threshold"]:
                deployment.status = "unhealthy"
                self.logger.error(f"❌ {deployment.name} 健康检查失败: {e}")
    
    async def get_active_deployment(self) -> Optional[DeploymentConfig]:
        """获取当前活跃的部署"""
        # 优先返回当前活跃的部署
        active = self.deployments.get(self.active_deployment)
        if active and active.status == "healthy":
            return active
        
        # 如果活跃部署不健康，尝试其他部署
        for deployment in self.deployments.values():
            if deployment.status == "healthy":
                if deployment.name != self.active_deployment:
                    self.logger.info(f"🔄 故障转移: {self.active_deployment} -> {deployment.name}")
                    self.active_deployment = deployment.name
                return deployment
        
        # 所有部署都不健康
        self.logger.warning("⚠️ 所有云端部署不可用，启用纯本地模式")
        return None
    
    async def process_event(self, event_title: str, event_desc: str, 
                          initial_score: float) -> Dict[str, Any]:
        """处理事件（零停机模式）"""
        start_time = time.time()
        self.metrics.total_requests += 1
        
        try:
            # 1. 本地指挥评估
            assessment = await self.conductor.assess_event(
                event_title, event_desc, initial_score
            )
            
            # 2. 决定处理策略
            if self._should_use_local_only(assessment):
                # 本地处理
                result = await self._process_locally(assessment)
                self.metrics.local_handled += 1
                self.metrics.cost_saved_usd += 0.05  # 节省云端调用成本
            else:
                # 云端处理（带故障转移）
                result = await self._process_with_cloud(assessment)
                if result.get("source") == "local_fallback":
                    self.metrics.local_handled += 1
                else:
                    self.metrics.cloud_handled += 1
            
            # 3. 更新指标
            processing_time = (time.time() - start_time) * 1000
            self._update_metrics(processing_time, success=True)
            
            result["processing_time_ms"] = processing_time
            result["handled_by"] = "local" if result.get("source", "").startswith("local") else "cloud"
            
            return result
            
        except Exception as e:
            self.logger.error(f"事件处理失败: {e}")
            self.metrics.failed_requests += 1
            self._update_metrics((time.time() - start_time) * 1000, success=False)
            
            return {
                "error": str(e),
                "fallback_analysis": "系统异常，建议手动关注此事件",
                "handled_by": "error_fallback"
            }
    
    def _should_use_local_only(self, assessment: EventAssessment) -> bool:
        """判断是否应该仅使用本地处理"""
        # 成本优化：低影响事件本地处理
        if (self.config["cost_optimization"]["local_first"] and 
            assessment.conductor_score < self.config["cost_optimization"]["cloud_threshold_score"]):
            return True
        
        # 云端服务不可用
        active_deployment = asyncio.create_task(self.get_active_deployment())
        if not active_deployment:
            return True
        
        return False
    
    async def _process_locally(self, assessment: EventAssessment) -> Dict[str, Any]:
        """本地处理事件"""
        return await self.conductor._enhanced_local_analysis(assessment)
    
    async def _process_with_cloud(self, assessment: EventAssessment) -> Dict[str, Any]:
        """云端处理事件（带故障转移）"""
        deployment = await self.get_active_deployment()
        
        if not deployment:
            # 云端不可用，本地兜底
            result = await self._process_locally(assessment)
            result["source"] = "local_fallback"
            return result
        
        try:
            # 调用云端服务
            result = await self.conductor.trigger_cloud_analysis(assessment)
            return result or await self._process_locally(assessment)
            
        except Exception as e:
            self.logger.error(f"云端处理失败: {e}")
            result = await self._process_locally(assessment)
            result["source"] = "local_fallback"
            return result
    
    def _update_metrics(self, processing_time: float, success: bool):
        """更新指标"""
        if success:
            self.metrics.successful_requests += 1
        
        # 更新平均响应时间
        total_requests = self.metrics.successful_requests + self.metrics.failed_requests
        if total_requests > 0:
            self.metrics.average_response_time = (
                (self.metrics.average_response_time * (total_requests - 1) + processing_time) / 
                total_requests
            )
        
        # 更新可用性
        if total_requests > 0:
            self.metrics.uptime_percentage = (
                self.metrics.successful_requests / total_requests * 100
            )
    
    def get_system_status(self) -> Dict[str, Any]:
        """获取系统状态"""
        return {
            "orchestrator": {
                "active_deployment": self.active_deployment,
                "deployment_in_progress": self.deployment_in_progress,
                "is_running": self.is_running
            },
            "deployments": {
                name: asdict(deployment) 
                for name, deployment in self.deployments.items()
            },
            "metrics": asdict(self.metrics),
            "conductor": {
                "ollama_available": self.conductor.ollama_available,
                "model_name": self.conductor.model_name,
                "decision_stats": self.conductor.decision_stats
            }
        }


# 使用示例
async def main():
    """测试零停机编排器"""
    print("🎼 测试零停机编排器")
    
    orchestrator = ZeroDowntimeOrchestrator()
    await orchestrator.start()
    
    try:
        # 测试事件处理
        test_events = [
            ("美联储加息", "美联储宣布加息75个基点", 95),
            ("苹果财报", "苹果发布Q4财报，营收超预期", 70),
            ("小公司新闻", "某小公司获得A轮融资", 30)
        ]
        
        for title, desc, score in test_events:
            print(f"\n📊 处理事件: {title}")
            result = await orchestrator.process_event(title, desc, score)
            print(f"   处理方式: {result.get('handled_by', 'unknown')}")
            print(f"   处理时间: {result.get('processing_time_ms', 0):.1f}ms")
        
        # 显示系统状态
        print(f"\n📈 系统状态:")
        status = orchestrator.get_system_status()
        print(f"   可用性: {status['metrics']['uptime_percentage']:.2f}%")
        print(f"   本地处理: {status['metrics']['local_handled']}")
        print(f"   云端处理: {status['metrics']['cloud_handled']}")
        print(f"   节省成本: ${status['metrics']['cost_saved_usd']:.3f}")
        
    finally:
        await orchestrator.stop()


if __name__ == "__main__":
    asyncio.run(main())
