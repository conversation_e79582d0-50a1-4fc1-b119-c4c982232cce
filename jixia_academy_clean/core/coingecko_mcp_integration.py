# -*- coding: utf-8 -*-
"""
CoinGecko MCP集成模块
将CoinGecko的实时加密货币数据集成到太公心易情报系统中
"""

import os
import json
import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
import httpx
from dataclasses import dataclass
from dotenv import load_dotenv

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 加载环境变量
load_dotenv(dotenv_path='.env')

@dataclass
class CryptoNewsEvent:
    """加密货币新闻事件数据类"""
    symbol: str
    name: str
    price_usd: float
    price_change_24h: float
    volume_24h: float
    market_cap: float
    market_cap_rank: int
    sentiment_score: float  # 基于价格变化计算的情绪分数
    volatility: float
    timestamp: datetime
    event_type: str  # 'price_surge', 'volume_spike', 'new_listing', 'trend_change'
    significance: str  # 'low', 'medium', 'high', 'critical'

@dataclass
class CryptoMarketInsight:
    """加密货币市场洞察"""
    total_market_cap: float
    total_volume_24h: float
    btc_dominance: float
    eth_dominance: float
    market_sentiment: str
    trending_coins: List[str]
    top_gainers: List[Dict[str, Any]]
    top_losers: List[Dict[str, Any]]
    fear_greed_index: float
    timestamp: datetime

class CoinGeckoMCPIntegration:
    """CoinGecko MCP集成类"""
    
    def __init__(self, use_pro_api: bool = False):
        self.use_pro_api = use_pro_api
        self.api_key = os.getenv('COINGECKO_PRO_API_KEY') if use_pro_api else None
        self.base_url = "https://api.coingecko.com/api/v3"
        self.pro_base_url = "https://pro-api.coingecko.com/api/v3"
        
        # 监控的重点币种（七仙女概念扩展到加密货币）
        self.stellar_cryptos = [
            'bitcoin', 'ethereum', 'binancecoin', 'solana', 
            'cardano', 'avalanche-2', 'polygon', 'chainlink',
            'polkadot', 'uniswap', 'litecoin', 'bitcoin-cash'
        ]
        
        # 事件阈值配置
        self.thresholds = {
            'price_surge': 0.15,      # 15%涨幅触发
            'price_crash': -0.15,     # 15%跌幅触发
            'volume_spike': 2.0,      # 2倍成交量触发
            'market_cap_change': 0.1   # 10%市值变化触发
        }
        
    async def get_market_overview(self) -> CryptoMarketInsight:
        """获取市场概览"""
        try:
            url = f"{self._get_base_url()}/global"
            headers = self._get_headers()
            
            async with httpx.AsyncClient() as client:
                response = await client.get(url, headers=headers)
                response.raise_for_status()
                data = response.json()
                
                global_data = data['data']
                
                # 获取热门币种
                trending_coins = await self._get_trending_coins()
                
                # 获取涨跌幅榜
                gainers_losers = await self._get_gainers_losers()
                
                # 计算市场情绪
                market_sentiment = self._calculate_market_sentiment(global_data, gainers_losers)
                
                # 计算恐惧贪婪指数（简化版）
                fear_greed_index = self._calculate_fear_greed_index(global_data, gainers_losers)
                
                return CryptoMarketInsight(
                    total_market_cap=global_data.get('total_market_cap', {}).get('usd', 0),
                    total_volume_24h=global_data.get('total_volume', {}).get('usd', 0),
                    btc_dominance=global_data.get('market_cap_percentage', {}).get('btc', 0),
                    eth_dominance=global_data.get('market_cap_percentage', {}).get('eth', 0),
                    market_sentiment=market_sentiment,
                    trending_coins=trending_coins,
                    top_gainers=gainers_losers.get('gainers', []),
                    top_losers=gainers_losers.get('losers', []),
                    fear_greed_index=fear_greed_index,
                    timestamp=datetime.now()
                )
                
        except Exception as e:
            logger.error(f"❌ 获取市场概览失败: {e}")
            return self._create_empty_market_insight()
    
    async def monitor_crypto_events(self) -> List[CryptoNewsEvent]:
        """监控加密货币事件"""
        events = []
        
        try:
            # 获取重点币种数据
            stellar_data = await self._get_coins_data(self.stellar_cryptos)
            
            for coin_data in stellar_data:
                event = self._analyze_coin_for_events(coin_data)
                if event:
                    events.append(event)
            
            # 获取新上线币种
            new_listings = await self._get_new_listings()
            for coin_data in new_listings:
                event = self._create_new_listing_event(coin_data)
                if event:
                    events.append(event)
            
            # 按重要性排序
            events.sort(key=lambda x: self._get_significance_score(x.significance), reverse=True)
            
            logger.info(f"🔍 检测到 {len(events)} 个加密货币事件")
            return events
            
        except Exception as e:
            logger.error(f"❌ 监控加密货币事件失败: {e}")
            return []
    
    async def get_crypto_news_flow(self, hours: int = 24) -> List[Dict[str, Any]]:
        """生成加密货币新闻流"""
        news_flow = []
        
        try:
            # 1. 市场概览
            market_insight = await self.get_market_overview()
            news_flow.append({
                'type': 'market_overview',
                'title': f'加密货币市场概览',
                'content': f'总市值: ${market_insight.total_market_cap:,.0f}, BTC占比: {market_insight.btc_dominance:.1f}%, 市场情绪: {market_sentiment}',
                'timestamp': market_insight.timestamp,
                'importance': 'medium'
            })
            
            # 2. 重要事件
            events = await self.monitor_crypto_events()
            for event in events[:10]:  # 取前10个重要事件
                news_flow.append({
                    'type': 'crypto_event',
                    'title': f'{event.name} ({event.symbol.upper()}) {event.event_type}',
                    'content': f'价格: ${event.price_usd:.4f}, 24h变化: {event.price_change_24h:+.2f}%, 成交量: ${event.volume_24h:,.0f}',
                    'timestamp': event.timestamp,
                    'importance': event.significance,
                    'symbol': event.symbol,
                    'sentiment_score': event.sentiment_score
                })
            
            # 3. 热门币种动态
            for coin in market_insight.trending_coins[:5]:
                news_flow.append({
                    'type': 'trending_coin',
                    'title': f'热门币种: {coin}',
                    'content': f'{coin} 正在热门搜索榜上',
                    'timestamp': datetime.now(),
                    'importance': 'low'
                })
            
            # 按时间和重要性排序
            news_flow.sort(key=lambda x: (
                self._get_importance_score(x['importance']),
                x['timestamp']
            ), reverse=True)
            
            return news_flow
            
        except Exception as e:
            logger.error(f"❌ 生成加密货币新闻流失败: {e}")
            return []
    
    async def _get_trending_coins(self) -> List[str]:
        """获取热门币种"""
        try:
            url = f"{self._get_base_url()}/search/trending"
            headers = self._get_headers()
            
            async with httpx.AsyncClient() as client:
                response = await client.get(url, headers=headers)
                response.raise_for_status()
                data = response.json()
                
                trending = []
                for coin in data.get('coins', [])[:10]:
                    trending.append(coin['item']['name'])
                
                return trending
                
        except Exception as e:
            logger.error(f"❌ 获取热门币种失败: {e}")
            return []
    
    async def _get_gainers_losers(self) -> Dict[str, List[Dict[str, Any]]]:
        """获取涨跌幅榜"""
        try:
            if self.use_pro_api:
                url = f"{self._get_base_url()}/coins/top_gainers_losers"
                headers = self._get_headers()
                
                async with httpx.AsyncClient() as client:
                    response = await client.get(url, headers=headers)
                    response.raise_for_status()
                    data = response.json()
                    
                    return {
                        'gainers': data.get('top_gainers', [])[:5],
                        'losers': data.get('top_losers', [])[:5]
                    }
            else:
                # 免费API的替代方案
                url = f"{self._get_base_url()}/coins/markets"
                params = {
                    'vs_currency': 'usd',
                    'order': 'price_change_percentage_24h_desc',
                    'per_page': 10,
                    'page': 1
                }
                headers = self._get_headers()
                
                async with httpx.AsyncClient() as client:
                    response = await client.get(url, params=params, headers=headers)
                    response.raise_for_status()
                    gainers = response.json()
                    
                    # 获取跌幅榜
                    params['order'] = 'price_change_percentage_24h_asc'
                    response = await client.get(url, params=params, headers=headers)
                    response.raise_for_status()
                    losers = response.json()
                    
                    return {
                        'gainers': gainers[:5],
                        'losers': losers[:5]
                    }
                    
        except Exception as e:
            logger.error(f"❌ 获取涨跌幅榜失败: {e}")
            return {'gainers': [], 'losers': []}
    
    async def _get_coins_data(self, coin_ids: List[str]) -> List[Dict[str, Any]]:
        """获取指定币种数据"""
        try:
            url = f"{self._get_base_url()}/coins/markets"
            params = {
                'vs_currency': 'usd',
                'ids': ','.join(coin_ids),
                'order': 'market_cap_desc',
                'per_page': len(coin_ids),
                'page': 1,
                'sparkline': False,
                'price_change_percentage': '24h'
            }
            headers = self._get_headers()
            
            async with httpx.AsyncClient() as client:
                response = await client.get(url, params=params, headers=headers)
                response.raise_for_status()
                return response.json()
                
        except Exception as e:
            logger.error(f"❌ 获取币种数据失败: {e}")
            return []
    
    async def _get_new_listings(self) -> List[Dict[str, Any]]:
        """获取新上线币种"""
        try:
            if self.use_pro_api:
                url = f"{self._get_base_url()}/coins/list/new"
                headers = self._get_headers()
                
                async with httpx.AsyncClient() as client:
                    response = await client.get(url, headers=headers)
                    response.raise_for_status()
                    return response.json()[:5]  # 取前5个新币
            else:
                # 免费API的替代方案：获取最近添加的币种
                url = f"{self._get_base_url()}/coins/markets"
                params = {
                    'vs_currency': 'usd',
                    'order': 'id_asc',  # 按ID升序，新币ID通常较大
                    'per_page': 10,
                    'page': 1
                }
                headers = self._get_headers()
                
                async with httpx.AsyncClient() as client:
                    response = await client.get(url, params=params, headers=headers)
                    response.raise_for_status()
                    data = response.json()
                    
                    # 过滤出最近24小时内的新币（简化判断）
                    new_coins = []
                    for coin in data:
                        if coin.get('ath_date'):
                            ath_date = datetime.fromisoformat(coin['ath_date'].replace('Z', '+00:00'))
                            if (datetime.now() - ath_date.replace(tzinfo=None)).days <= 7:
                                new_coins.append(coin)
                    
                    return new_coins[:5]
                    
        except Exception as e:
            logger.error(f"❌ 获取新上线币种失败: {e}")
            return []
    
    def _analyze_coin_for_events(self, coin_data: Dict[str, Any]) -> Optional[CryptoNewsEvent]:
        """分析币种数据，检测事件"""
        try:
            price_change_24h = coin_data.get('price_change_percentage_24h', 0)
            volume_24h = coin_data.get('total_volume', 0)
            market_cap = coin_data.get('market_cap', 0)
            
            # 检测价格异动
            event_type = None
            significance = 'low'
            
            if abs(price_change_24h) >= self.thresholds['price_surge']:
                if price_change_24h > 0:
                    event_type = 'price_surge'
                    significance = 'high' if price_change_24h > 30 else 'medium'
                else:
                    event_type = 'price_crash'
                    significance = 'critical' if price_change_24h < -30 else 'high'
            
            # 检测成交量异动（需要历史数据对比，这里简化处理）
            elif volume_24h > market_cap * 0.5:  # 成交量超过市值50%
                event_type = 'volume_spike'
                significance = 'medium'
            
            if event_type:
                return CryptoNewsEvent(
                    symbol=coin_data.get('symbol', ''),
                    name=coin_data.get('name', ''),
                    price_usd=coin_data.get('current_price', 0),
                    price_change_24h=price_change_24h,
                    volume_24h=volume_24h,
                    market_cap=market_cap,
                    market_cap_rank=coin_data.get('market_cap_rank', 0),
                    sentiment_score=self._calculate_sentiment_score(price_change_24h),
                    volatility=abs(price_change_24h),
                    timestamp=datetime.now(),
                    event_type=event_type,
                    significance=significance
                )
            
            return None

        except Exception as e:
            logger.error(f"❌ 分析币种事件失败: {e}")
            return None

    def _create_new_listing_event(self, coin_data: Dict[str, Any]) -> Optional[CryptoNewsEvent]:
        """创建新上线事件"""
        try:
            return CryptoNewsEvent(
                symbol=coin_data.get('symbol', ''),
                name=coin_data.get('name', ''),
                price_usd=coin_data.get('current_price', 0),
                price_change_24h=coin_data.get('price_change_percentage_24h', 0),
                volume_24h=coin_data.get('total_volume', 0),
                market_cap=coin_data.get('market_cap', 0),
                market_cap_rank=coin_data.get('market_cap_rank', 0),
                sentiment_score=0.5,  # 新币中性情绪
                volatility=abs(coin_data.get('price_change_percentage_24h', 0)),
                timestamp=datetime.now(),
                event_type='new_listing',
                significance='medium'
            )
        except Exception as e:
            logger.error(f"❌ 创建新上线事件失败: {e}")
            return None

    def _calculate_market_sentiment(self, global_data: Dict[str, Any], gainers_losers: Dict[str, List]) -> str:
        """计算市场情绪"""
        try:
            # 基于涨跌比例计算
            gainers_count = len(gainers_losers.get('gainers', []))
            losers_count = len(gainers_losers.get('losers', []))

            if gainers_count > losers_count * 1.5:
                return "极度乐观"
            elif gainers_count > losers_count:
                return "乐观"
            elif losers_count > gainers_count * 1.5:
                return "极度悲观"
            elif losers_count > gainers_count:
                return "悲观"
            else:
                return "中性"

        except Exception:
            return "中性"

    def _calculate_fear_greed_index(self, global_data: Dict[str, Any], gainers_losers: Dict[str, List]) -> float:
        """计算恐惧贪婪指数（简化版）"""
        try:
            # 基于多个因子计算
            factors = []

            # 1. BTC占比因子（占比高=恐惧，占比低=贪婪）
            btc_dominance = global_data.get('market_cap_percentage', {}).get('btc', 50)
            btc_factor = max(0, min(100, 100 - btc_dominance))  # 反向
            factors.append(btc_factor)

            # 2. 涨跌比例因子
            gainers = gainers_losers.get('gainers', [])
            losers = gainers_losers.get('losers', [])

            if gainers and losers:
                avg_gain = sum(coin.get('price_change_percentage_24h', 0) for coin in gainers) / len(gainers)
                avg_loss = sum(coin.get('price_change_percentage_24h', 0) for coin in losers) / len(losers)

                # 转换为0-100分数
                sentiment_factor = max(0, min(100, 50 + (avg_gain + abs(avg_loss)) * 2))
                factors.append(sentiment_factor)

            # 3. 成交量因子（高成交量=高情绪）
            total_volume = global_data.get('total_volume', {}).get('usd', 0)
            total_market_cap = global_data.get('total_market_cap', {}).get('usd', 1)

            if total_market_cap > 0:
                volume_ratio = (total_volume / total_market_cap) * 100
                volume_factor = min(100, volume_ratio * 10)  # 放大10倍
                factors.append(volume_factor)

            # 计算平均值
            return sum(factors) / len(factors) if factors else 50.0

        except Exception:
            return 50.0  # 默认中性

    def _calculate_sentiment_score(self, price_change_24h: float) -> float:
        """基于价格变化计算情绪分数"""
        # 将价格变化转换为-1到1的情绪分数
        return max(-1, min(1, price_change_24h / 50))  # 50%变化对应满分

    def _get_significance_score(self, significance: str) -> int:
        """获取重要性分数"""
        scores = {'low': 1, 'medium': 2, 'high': 3, 'critical': 4}
        return scores.get(significance, 1)

    def _get_importance_score(self, importance: str) -> int:
        """获取重要性分数"""
        scores = {'low': 1, 'medium': 2, 'high': 3, 'critical': 4}
        return scores.get(importance, 1)

    def _get_base_url(self) -> str:
        """获取基础URL"""
        return self.pro_base_url if self.use_pro_api else self.base_url

    def _get_headers(self) -> Dict[str, str]:
        """获取请求头"""
        headers = {'User-Agent': 'TaigongXinyi/1.0'}
        if self.use_pro_api and self.api_key:
            headers['x-cg-pro-api-key'] = self.api_key
        return headers

    def _create_empty_market_insight(self) -> CryptoMarketInsight:
        """创建空的市场洞察"""
        return CryptoMarketInsight(
            total_market_cap=0,
            total_volume_24h=0,
            btc_dominance=0,
            eth_dominance=0,
            market_sentiment="数据不可用",
            trending_coins=[],
            top_gainers=[],
            top_losers=[],
            fear_greed_index=50.0,
            timestamp=datetime.now()
        )


# 集成到太公心易RSS RAG系统
class CryptoNewsFlowGenerator:
    """加密货币新闻流生成器"""

    def __init__(self, use_pro_api: bool = False):
        self.coingecko = CoinGeckoMCPIntegration(use_pro_api)

    async def generate_crypto_rss_feed(self) -> List[Dict[str, Any]]:
        """生成加密货币RSS风格的新闻流"""
        try:
            # 获取加密货币新闻流
            crypto_news = await self.coingecko.get_crypto_news_flow(hours=24)

            # 转换为RSS格式
            rss_items = []
            for news in crypto_news:
                rss_item = {
                    'title': news['title'],
                    'description': news['content'],
                    'pubDate': news['timestamp'].isoformat(),
                    'category': 'cryptocurrency',
                    'source': 'CoinGecko MCP',
                    'sentiment': news.get('sentiment_score', 0),
                    'importance': news['importance'],
                    'guid': f"crypto_{news['type']}_{int(news['timestamp'].timestamp())}"
                }

                # 添加特定字段
                if 'symbol' in news:
                    rss_item['symbol'] = news['symbol']

                rss_items.append(rss_item)

            return rss_items

        except Exception as e:
            logger.error(f"❌ 生成加密货币RSS流失败: {e}")
            return []

    async def get_crypto_market_summary(self) -> str:
        """获取加密货币市场摘要"""
        try:
            market_insight = await self.coingecko.get_market_overview()

            summary = f"""
🔮 太公心易加密货币市场洞察

📊 市场概览:
- 总市值: ${market_insight.total_market_cap:,.0f}
- 24h成交量: ${market_insight.total_volume_24h:,.0f}
- BTC占比: {market_insight.btc_dominance:.1f}%
- ETH占比: {market_insight.eth_dominance:.1f}%

😊 市场情绪: {market_insight.market_sentiment}
📈 恐惧贪婪指数: {market_insight.fear_greed_index:.0f}/100

🔥 热门币种: {', '.join(market_insight.trending_coins[:5])}

📈 今日涨幅榜:
"""

            for i, gainer in enumerate(market_insight.top_gainers[:3], 1):
                name = gainer.get('name', gainer.get('symbol', 'Unknown'))
                change = gainer.get('price_change_percentage_24h', 0)
                summary += f"{i}. {name}: +{change:.1f}%\n"

            summary += "\n📉 今日跌幅榜:\n"
            for i, loser in enumerate(market_insight.top_losers[:3], 1):
                name = loser.get('name', loser.get('symbol', 'Unknown'))
                change = loser.get('price_change_percentage_24h', 0)
                summary += f"{i}. {name}: {change:.1f}%\n"

            return summary

        except Exception as e:
            logger.error(f"❌ 获取市场摘要失败: {e}")
            return "暂时无法获取加密货币市场数据"


# 使用示例和测试
async def test_coingecko_integration():
    """测试CoinGecko集成"""
    try:
        print("🚀 测试CoinGecko MCP集成...")

        # 初始化（使用免费API）
        crypto_generator = CryptoNewsFlowGenerator(use_pro_api=False)

        # 1. 测试市场概览
        print("\n📊 获取市场概览...")
        market_insight = await crypto_generator.coingecko.get_market_overview()
        print(f"总市值: ${market_insight.total_market_cap:,.0f}")
        print(f"BTC占比: {market_insight.btc_dominance:.1f}%")
        print(f"市场情绪: {market_insight.market_sentiment}")
        print(f"恐惧贪婪指数: {market_insight.fear_greed_index:.0f}")

        # 2. 测试事件监控
        print("\n🔍 监控加密货币事件...")
        events = await crypto_generator.coingecko.monitor_crypto_events()
        print(f"检测到 {len(events)} 个事件")

        for event in events[:3]:
            print(f"- {event.name} ({event.symbol}): {event.event_type}, 变化: {event.price_change_24h:+.2f}%")

        # 3. 测试新闻流生成
        print("\n📰 生成加密货币新闻流...")
        rss_feed = await crypto_generator.generate_crypto_rss_feed()
        print(f"生成 {len(rss_feed)} 条新闻")

        for item in rss_feed[:3]:
            print(f"- {item['title']}")
            print(f"  {item['description'][:100]}...")

        # 4. 测试市场摘要
        print("\n📋 生成市场摘要...")
        summary = await crypto_generator.get_crypto_market_summary()
        print(summary[:500] + "..." if len(summary) > 500 else summary)

        print("\n✅ CoinGecko MCP集成测试完成！")

    except Exception as e:
        print(f"❌ 测试失败: {e}")


if __name__ == "__main__":
    asyncio.run(test_coingecko_integration())
