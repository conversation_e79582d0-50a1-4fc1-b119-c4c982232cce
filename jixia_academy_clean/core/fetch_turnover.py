import sys
import json
import argparse
import pandas as pd
from pathlib import Path

# 将项目根目录添加到sys.path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from src.core.turnover_strategy import get_top_turnover_stocks

def main():
    parser = argparse.ArgumentParser(description='Fetch top turnover stocks.')
    parser.add_argument('--count', type=int, default=8, help='Number of stocks to fetch')
    args = parser.parse_args()

    try:
        df = get_top_turnover_stocks(args.count)
        # 将DataFrame转换为JSON格式的字符串
        result_json = df.to_json(orient='records')
        print(result_json)
    except Exception as e:
        print(json.dumps({'error': str(e)}), file=sys.stderr)
        sys.exit(1)

if __name__ == "__main__":
    main()