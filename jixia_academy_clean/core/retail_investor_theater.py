# -*- coding: utf-8 -*-
"""
散户小剧场 - 基于TinyTroupe的散户行为模拟系统
模拟真实散户在重大事件冲击下的心理和行为反应
"""

import asyncio
import json
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from enum import Enum
import random

try:
    from tinytroupe import TinyPerson, TinyWorld, TinyStory
    TINYTROUPE_AVAILABLE = True
except ImportError:
    TINYTROUPE_AVAILABLE = False


class InvestorType(Enum):
    """投资者类型"""
    NEWBIE = "新手小白"           # 刚入市的新手
    FOMO_CHASER = "追涨杀跌侠"    # FOMO情绪严重
    LEVERAGED_GAMBLER = "杠杆赌徒"  # 喜欢高杠杆
    CONSPIRACY_THEORIST = "阴谋论者"  # 相信各种阴谋论
    TECHNICAL_BELIEVER = "技术派信徒"  # 迷信技术分析
    NEWS_FOLLOWER = "消息面跟风者"    # 跟着新闻走
    CONTRARIAN_WANNABE = "伪逆向者"   # 自以为是逆向投资
    PANIC_SELLER = "恐慌抛售者"      # 一有风吹草动就卖


@dataclass
class MarketEvent:
    """市场事件"""
    title: str
    description: str
    impact_level: int  # 1-10级影响
    affected_sectors: List[str]
    event_type: str  # "geopolitical", "economic", "corporate", "natural"
    timestamp: datetime
    duration_days: int = 1


@dataclass
class RetailReaction:
    """散户反应"""
    investor_name: str
    investor_type: InvestorType
    initial_thought: str
    action_taken: str
    reasoning: str
    confidence_level: float  # 0-1
    leverage_used: float = 1.0
    outcome_prediction: str
    actual_outcome: Optional[str] = None
    pnl_result: Optional[float] = None


class RetailInvestorTheater:
    """散户小剧场主控制器"""
    
    def __init__(self):
        self.logger = logging.getLogger('RetailTheater')
        self.world = None
        self.investors = {}
        self.current_event = None
        self.reaction_history = []
        
        # 初始化散户角色
        self._initialize_retail_investors()
    
    def _initialize_retail_investors(self):
        """初始化散户角色"""
        
        # 定义典型散户人设
        investor_profiles = {
            "小韭菜张三": {
                "type": InvestorType.NEWBIE,
                "age": 25,
                "background": "刚毕业的程序员，用工资炒股，看B站学投资",
                "personality": "容易激动，喜欢跟风，风险意识薄弱",
                "capital": 50000,
                "favorite_stocks": ["茅台", "宁德时代", "比亚迪"],
                "trading_style": "看热搜买股票，涨了就加仓，跌了就割肉"
            },
            
            "FOMO李四": {
                "type": InvestorType.FOMO_CHASER,
                "age": 35,
                "background": "中层管理，有点闲钱，总觉得错过了发财机会",
                "personality": "FOMO情绪严重，看到涨停就想买，害怕错过任何机会",
                "capital": 200000,
                "favorite_stocks": ["热门概念股", "涨停板"],
                "trading_style": "追涨杀跌，永远在高点买入，低点卖出"
            },
            
            "杠杆王五": {
                "type": InvestorType.LEVERAGED_GAMBLER,
                "age": 40,
                "background": "小生意人，喜欢以小博大，认为不加杠杆赚不到钱",
                "personality": "胆大心细，但容易上头，喜欢重仓梭哈",
                "capital": 100000,
                "favorite_stocks": ["期货", "期权", "融资融券"],
                "trading_style": "高杠杆，重仓，快进快出"
            },
            
            "阴谋论赵六": {
                "type": InvestorType.CONSPIRACY_THEORIST,
                "age": 50,
                "background": "老股民，经历过多轮牛熊，相信一切都是庄家操控",
                "personality": "疑神疑鬼，认为所有消息都是假的，市场都是操控的",
                "capital": 300000,
                "favorite_stocks": ["黄金", "军工", "资源股"],
                "trading_style": "逆向思维，但经常逆向过头"
            },
            
            "技术流孙七": {
                "type": InvestorType.TECHNICAL_BELIEVER,
                "age": 30,
                "background": "工程师，迷信技术分析，认为K线包含一切",
                "personality": "理性分析，但容易钻牛角尖，过度相信指标",
                "capital": 150000,
                "favorite_stocks": ["根据技术形态选择"],
                "trading_style": "严格按照技术指标操作，止损止盈明确"
            },
            
            "消息面周八": {
                "type": InvestorType.NEWS_FOLLOWER,
                "age": 45,
                "background": "媒体从业者，消息灵通，喜欢根据新闻炒股",
                "personality": "信息敏感，反应迅速，但容易被假消息误导",
                "capital": 180000,
                "favorite_stocks": ["热点题材股"],
                "trading_style": "消息驱动，快进快出，追热点"
            }
        }
        
        # 如果TinyTroupe可用，创建AI角色
        if TINYTROUPE_AVAILABLE:
            self.world = TinyWorld("散户交易大厅")
            
            for name, profile in investor_profiles.items():
                person = TinyPerson(name)
                person.define_background(profile["background"])
                person.define_personality(profile["personality"])
                person.define_interests(profile["favorite_stocks"])
                
                # 添加投资相关属性
                person.set_attribute("capital", profile["capital"])
                person.set_attribute("investor_type", profile["type"])
                person.set_attribute("trading_style", profile["trading_style"])
                
                self.world.add_person(person)
                self.investors[name] = person
        else:
            # 如果TinyTroupe不可用，使用模拟数据
            self.investors = investor_profiles
            self.logger.warning("TinyTroupe不可用，使用模拟散户行为")
    
    async def simulate_event_reaction(self, event: MarketEvent) -> List[RetailReaction]:
        """模拟散户对事件的反应"""
        self.current_event = event
        reactions = []
        
        self.logger.info(f"🎭 开始模拟散户对事件的反应: {event.title}")
        
        if TINYTROUPE_AVAILABLE and self.world:
            # 使用TinyTroupe进行真实AI模拟
            reactions = await self._simulate_with_tinytroupe(event)
        else:
            # 使用规则引擎模拟
            reactions = await self._simulate_with_rules(event)
        
        self.reaction_history.extend(reactions)
        return reactions
    
    async def _simulate_with_tinytroupe(self, event: MarketEvent) -> List[RetailReaction]:
        """使用TinyTroupe进行AI模拟"""
        reactions = []
        
        # 创建事件情境
        story = TinyStory(f"重大市场事件: {event.title}")
        story.set_context(f"""
        市场突发事件: {event.description}
        影响等级: {event.impact_level}/10
        涉及板块: {', '.join(event.affected_sectors)}
        
        现在是交易时间，各位散户投资者看到这个消息后会如何反应？
        请每个人说出自己的第一反应、分析思路和具体行动计划。
        """)
        
        # 让每个散户角色发表看法
        for name, person in self.investors.items():
            response = await person.think_and_speak(
                f"看到'{event.title}'这个消息，你的第一反应是什么？你会采取什么行动？"
            )
            
            # 解析AI回复，提取关键信息
            reaction = self._parse_ai_response(name, person, response, event)
            reactions.append(reaction)
        
        return reactions
    
    async def _simulate_with_rules(self, event: MarketEvent) -> List[RetailReaction]:
        """使用规则引擎模拟散户行为"""
        reactions = []
        
        # 以伊朗以色列冲突为例
        if "伊朗" in event.title or "以色列" in event.title or "中东" in event.title:
            reactions = self._simulate_middle_east_conflict_reactions(event)
        else:
            # 通用事件反应模拟
            reactions = self._simulate_generic_event_reactions(event)
        
        return reactions
    
    def _simulate_middle_east_conflict_reactions(self, event: MarketEvent) -> List[RetailReaction]:
        """模拟中东冲突事件的散户反应"""
        reactions = []
        
        # 小韭菜张三的反应
        reactions.append(RetailReaction(
            investor_name="小韭菜张三",
            investor_type=InvestorType.NEWBIE,
            initial_thought="卧槽！中东打仗了！原油要涨！赶紧买原油基金！",
            action_taken="梭哈中海油、中石油，买了10万原油基金",
            reasoning="战争肯定影响石油供应，原油必涨！这是千载难逢的机会！",
            confidence_level=0.9,
            leverage_used=1.0,
            outcome_prediction="原油涨到天上去，我要发财了！",
            actual_outcome="买在最高点，三天后原油回落，亏损15%"
        ))
        
        # FOMO李四的反应
        reactions.append(RetailReaction(
            investor_name="FOMO李四",
            investor_type=InvestorType.FOMO_CHASER,
            initial_thought="完了完了！我还没买原油！大家都在买，我也得赶紧上车！",
            action_taken="融资买入石油股，还买了原油期货",
            reasoning="看群里大家都在买，涨停榜全是石油股，不买就错过了！",
            confidence_level=0.7,
            leverage_used=2.0,
            outcome_prediction="跟着大家买总没错，至少能喝点汤",
            actual_outcome="高位接盘，杠杆放大亏损，被强制平仓"
        ))
        
        # 杠杆王五的反应
        reactions.append(RetailReaction(
            investor_name="杠杆王五",
            investor_type=InvestorType.LEVERAGED_GAMBLER,
            initial_thought="机会来了！这种地缘政治事件，不加杠杆怎么赚大钱？",
            action_taken="10倍杠杆做多原油期货，重仓军工股",
            reasoning="地缘政治冲突，原油和军工是最直接的受益板块",
            confidence_level=0.8,
            leverage_used=10.0,
            outcome_prediction="这波至少赚50%，小赌怡情，大赌发财！",
            actual_outcome="原油期货波动剧烈，隔夜跳空爆仓"
        ))
        
        # 阴谋论赵六的反应
        reactions.append(RetailReaction(
            investor_name="阴谋论赵六",
            investor_type=InvestorType.CONSPIRACY_THEORIST,
            initial_thought="又是美国在背后搞鬼！这是转移国内矛盾的老套路！",
            action_taken="反向操作，做空原油，买入黄金",
            reasoning="这种冲突都是演戏，真正的大佬早就布局好了，散户进去就是送钱",
            confidence_level=0.6,
            leverage_used=1.0,
            outcome_prediction="等散户都进去了，庄家就开始收割",
            actual_outcome="逆向思维过头，错过了短期上涨机会"
        ))
        
        # 技术流孙七的反应
        reactions.append(RetailReaction(
            investor_name="技术流孙七",
            investor_type=InvestorType.TECHNICAL_BELIEVER,
            initial_thought="从技术面看，原油确实突破了关键阻力位",
            action_taken="根据技术指标，小仓位试探性买入",
            reasoning="MACD金叉，RSI未超买，技术面支持上涨",
            confidence_level=0.5,
            leverage_used=1.0,
            outcome_prediction="技术面良好，但要严格止损",
            actual_outcome="技术分析在突发事件面前失效，小亏出局"
        ))
        
        # 消息面周八的反应
        reactions.append(RetailReaction(
            investor_name="消息面周八",
            investor_type=InvestorType.NEWS_FOLLOWER,
            initial_thought="这个消息很重要！得赶紧分析影响面",
            action_taken="买入石油股和军工股，同时关注后续消息",
            reasoning="地缘政治事件通常有持续性，要跟踪消息面变化",
            confidence_level=0.7,
            leverage_used=1.5,
            outcome_prediction="消息面驱动的行情，关键看事态发展",
            actual_outcome="消息面快速降温，及时止损小幅亏损"
        ))
        
        return reactions
    
    def _simulate_generic_event_reactions(self, event: MarketEvent) -> List[RetailReaction]:
        """模拟通用事件的散户反应"""
        # 这里可以根据不同事件类型实现不同的反应模式
        return []
    
    def _parse_ai_response(self, name: str, person, response: str, event: MarketEvent) -> RetailReaction:
        """解析AI回复，提取结构化信息"""
        # 这里需要实现NLP解析逻辑，提取关键信息
        # 暂时返回模拟数据
        return RetailReaction(
            investor_name=name,
            investor_type=person.get_attribute("investor_type"),
            initial_thought=response[:100],
            action_taken="AI模拟行动",
            reasoning=response,
            confidence_level=random.uniform(0.3, 0.9),
            leverage_used=random.uniform(1.0, 3.0),
            outcome_prediction="AI预测结果"
        )
    
    def generate_theater_script(self, reactions: List[RetailReaction]) -> str:
        """生成散户小剧场剧本"""
        script = f"""
# 🎭 散户小剧场：{self.current_event.title}

## 📰 事件背景
{self.current_event.description}

## 🎬 散户众生相

"""
        
        for reaction in reactions:
            script += f"""
### {reaction.investor_name} ({reaction.investor_type.value})

**第一反应**: {reaction.initial_thought}

**具体行动**: {reaction.action_taken}

**内心独白**: "{reaction.reasoning}"

**信心指数**: {'⭐' * int(reaction.confidence_level * 5)}

**杠杆倍数**: {reaction.leverage_used}x

**预期结果**: {reaction.outcome_prediction}

"""
            if reaction.actual_outcome:
                script += f"**实际结果**: {reaction.actual_outcome}\n\n"
        
        script += """
## 🎯 太公心易点评

看完这出散户小剧场，是不是很熟悉？这就是市场的真实写照：

- **情绪驱动**: 大部分散户都是被情绪主导，而不是理性分析
- **从众心理**: 看到别人买就跟着买，缺乏独立思考
- **杠杆成瘾**: 总想着以小博大，忽视了风险控制
- **消息滞后**: 等散户都知道的消息，往往已经被市场消化

**太公心易的智慧**: 在众人向左时，智者向右。真正的机会往往在恐慌中诞生，在狂欢中消失。

---
*免责声明：以上内容仅为娱乐和教育目的，不构成投资建议*
"""
        
        return script
    
    async def run_live_simulation(self, event: MarketEvent) -> Dict[str, Any]:
        """运行实时模拟"""
        reactions = await self.simulate_event_reaction(event)
        script = self.generate_theater_script(reactions)
        
        return {
            "event": event.__dict__,
            "reactions": [r.__dict__ for r in reactions],
            "script": script,
            "timestamp": datetime.now().isoformat(),
            "entertainment_value": self._calculate_entertainment_value(reactions),
            "educational_insights": self._extract_educational_insights(reactions)
        }
    
    def _calculate_entertainment_value(self, reactions: List[RetailReaction]) -> float:
        """计算娱乐价值分数"""
        # 基于反应的戏剧性和多样性计算娱乐价值
        diversity_score = len(set(r.investor_type for r in reactions)) / len(InvestorType)
        drama_score = sum(r.leverage_used for r in reactions) / len(reactions) / 10
        confidence_variance = sum((r.confidence_level - 0.5) ** 2 for r in reactions) / len(reactions)
        
        return min(1.0, (diversity_score + drama_score + confidence_variance) / 3)
    
    def _extract_educational_insights(self, reactions: List[RetailReaction]) -> List[str]:
        """提取教育洞察"""
        insights = []
        
        # 分析常见错误模式
        high_leverage_count = sum(1 for r in reactions if r.leverage_used > 2.0)
        if high_leverage_count > len(reactions) / 2:
            insights.append("⚠️ 过度使用杠杆是散户的常见陷阱")
        
        fomo_count = sum(1 for r in reactions if r.investor_type == InvestorType.FOMO_CHASER)
        if fomo_count > 0:
            insights.append("😱 FOMO情绪往往导致高位接盘")
        
        high_confidence_count = sum(1 for r in reactions if r.confidence_level > 0.8)
        if high_confidence_count > len(reactions) / 2:
            insights.append("🎯 过度自信是投资的大敌")
        
        return insights
