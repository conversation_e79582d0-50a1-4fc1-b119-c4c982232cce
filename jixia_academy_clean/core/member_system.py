# -*- coding: utf-8 -*-
"""
太公心易BI系统 - 会员系统
管理会员权限和每日晨会报告的访问
"""

import json
import hashlib
import os
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from pathlib import Path
from dataclasses import dataclass, asdict
from enum import Enum
from urllib.parse import urlparse

import psycopg2
from psycopg2.extras import RealDictCursor

class MembershipLevel(Enum):
    """会员等级"""
    FREE = "六壬观心(免费)"
    ADVANCED = "遁甲择时(展示)"
    SUPREME = "太乙观澜(至尊)"

@dataclass
class Member:
    """会员信息"""
    id: int
    username: str
    email: str
    password_hash: str
    membership_level: MembershipLevel
    created_at: datetime
    last_login: Optional[datetime] = None
    is_active: bool = True
    subscription_expires: Optional[datetime] = None

@dataclass
class ReportAccess:
    """报告访问记录"""
    member_id: int
    report_date: str
    access_time: datetime
    report_type: str

class MemberSystem:
    """会员系统管理器"""
    
    def __init__(self):
        self.database_url = os.getenv('DATABASE_URL')
        if not self.database_url:
            raise ValueError("DATABASE_URL environment variable not set. This application requires a PostgreSQL database.")
        
        self.db_config = self._parse_database_url(self.database_url)
        
        # 初始化数据库
        self._init_database()
        
        # 创建默认guest用户
        self._create_default_users()
    
    def _parse_database_url(self, url: str) -> Dict[str, str]:
        """解析数据库URL"""
        parsed = urlparse(url)
        return {
            'host': parsed.hostname,
            'port': parsed.port or 5432,
            'database': parsed.path[1:],  # 去掉开头的/
            'user': parsed.username,
            'password': parsed.password
        }
    
    def _get_connection(self):
        """获取数据库连接"""
        return psycopg2.connect(
            host=self.db_config['host'],
            port=self.db_config['port'],
            database=self.db_config['database'],
            user=self.db_config['user'],
            password=self.db_config['password'],
            cursor_factory=RealDictCursor
        )
    
    def _init_database(self):
        """初始化数据库"""
        with self._get_connection() as conn:
            cursor = conn.cursor()
            

            # PostgreSQL建表语句
            cursor.execute("""
    CREATE TABLE IF NOT EXISTS members (
        id SERIAL PRIMARY KEY,
        username VARCHAR(255) UNIQUE NOT NULL,
        email VARCHAR(255) UNIQUE NOT NULL,
        password_hash VARCHAR(255) NOT NULL,
        membership_level VARCHAR(50) NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        last_login TIMESTAMP,
        is_active BOOLEAN DEFAULT TRUE,
        subscription_expires TIMESTAMP
    )
""")
            
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS report_access (
                    id SERIAL PRIMARY KEY,
                    member_id INTEGER NOT NULL,
                    report_date VARCHAR(20) NOT NULL,
                    access_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    report_type VARCHAR(50) NOT NULL,
                    FOREIGN KEY (member_id) REFERENCES members (id)
                )
            """)
            
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS daily_reports (
                    id SERIAL PRIMARY KEY,
                    report_date VARCHAR(20) UNIQUE NOT NULL,
                    report_data TEXT NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            conn.commit()
    
    def _create_default_users(self):
        """创建默认用户"""
        try:
            # 检查guest用户是否已存在
            with self._get_connection() as conn:
                cursor = conn.cursor()
                
                cursor.execute("SELECT id FROM members WHERE username = %s", ('guest',))
                
                if not cursor.fetchone():
                    # 创建guest用户
                    guest_member = self.register_member(
                        username='guest',
                        email='<EMAIL>',
                        password='guest',
                        membership_level=MembershipLevel.FREE
                    )
                    if guest_member:
                        print("✅ 默认guest用户创建成功 (用户名: guest, 密码: guest)")
                    else:
                        print("❌ 默认guest用户创建失败")
                else:
                    print("ℹ️ 默认guest用户已存在")

                # 创建其他默认用户
                default_users = {
                    'supreme': MembershipLevel.SUPREME,
                    'advanced': MembershipLevel.ADVANCED,
                    'freemium': MembershipLevel.FREE
                }

                for username, level in default_users.items():
                    cursor.execute("SELECT id FROM members WHERE username = %s", (username,))
                    if not cursor.fetchone():
                        self.register_member(
                            username=username,
                            email=f"{username}@example.com",
                            password='niubi666',
                            membership_level=level
                        )
                        print(f"✅ 默认用户 {username} 创建成功")
                    else:
                        print(f"ℹ️ 默认用户 {username} 已存在")
                    
        except Exception as e:
            print(f"创建默认用户失败: {e}")
    
    def _hash_password(self, password: str) -> str:
        """密码哈希"""
        return hashlib.sha256(password.encode()).hexdigest()
    
    def register_member(self, username: str, email: str, password: str, 
                       membership_level: MembershipLevel = MembershipLevel.FREE) -> Optional[Member]:
        """注册新会员"""
        try:
            with self._get_connection() as conn:
                cursor = conn.cursor()
                
                # 检查用户名和邮箱是否已存在
                cursor.execute("SELECT id FROM members WHERE username = %s OR email = %s", 
                             (username, email))
                if cursor.fetchone():
                    return None  # 用户已存在
                
                # 创建新会员
                password_hash = self._hash_password(password)
                created_at = datetime.now()
                
                # 设置订阅过期时间
                subscription_expires = None
                if membership_level != MembershipLevel.FREE:
                    subscription_expires = datetime.now() + timedelta(days=365)
                
                cursor.execute("""
                    INSERT INTO members (username, email, password_hash, membership_level, 
                                       created_at, subscription_expires)
                    VALUES (%s, %s, %s, %s, %s, %s) RETURNING id
                """, (username, email, password_hash, membership_level.value, 
                      created_at, subscription_expires))
                member_id = cursor.fetchone()['id']
                
                conn.commit()
                
                return Member(
                    id=member_id,
                    username=username,
                    email=email,
                    password_hash=password_hash,
                    membership_level=membership_level,
                    created_at=created_at,
                    subscription_expires=subscription_expires
                )
                
        except Exception as e:
            print(f"注册会员失败: {e}")
            return None
    
    def authenticate_member(self, username: str, password: str) -> Optional[Member]:
        """会员认证"""
        try:
            with self._get_connection() as conn:
                cursor = conn.cursor()
                
                cursor.execute("""
                    SELECT id, username, email, password_hash, membership_level, 
                           created_at, last_login, is_active, subscription_expires
                    FROM members 
                    WHERE username = %s AND is_active = TRUE
                """, (username,))
                
                row = cursor.fetchone()
                if not row:
                    return None
                
                # 验证密码
                password_hash = row['password_hash']
                if self._hash_password(password) != password_hash:
                    return None
                
                # 更新最后登录时间
                last_login = datetime.now()
                member_id = row['id']
                
                cursor.execute("""
                    UPDATE members SET last_login = %s WHERE id = %s
                """, (last_login, member_id))
                
                conn.commit()
                
                # 构建Member对象
                return Member(
                    id=row['id'],
                    username=row['username'],
                    email=row['email'],
                    password_hash=row['password_hash'],
                    membership_level=MembershipLevel(row['membership_level']),
                    created_at=row['created_at'],
                    last_login=last_login,
                    is_active=row['is_active'],
                    subscription_expires=row['subscription_expires']
                )
                
        except Exception as e:
            print(f"会员认证失败: {e}")
            return None
    
    def get_member_permissions(self, member: Member) -> Dict[str, Any]:
        """获取会员权限"""
        # 检查订阅是否过期
        is_subscription_valid = True
        if member.subscription_expires:
            is_subscription_valid = datetime.now() < member.subscription_expires
        
        # 如果付费订阅过期，降级为免费会员权限
        effective_level = member.membership_level
        if not is_subscription_valid and member.membership_level != MembershipLevel.FREE:
            effective_level = MembershipLevel.FREE
        
        # 基于offering.md的权限定义
        permissions = {
            'fundamental_data': True,  # 基本面数据
            'sentiment_analysis': True,  # 舆情分析
            'intraday_signals': 'none',  # 日内交易信号: none, limited, full
            'swing_strategy': False,  # 波段策略
            'contrarian_report': False,  # 逆向思维研报
            'dedicated_vps': False,  # 独立VPS
            'private_wechat': False,  # 私人微信
            'risk_proxy': 'basic',  # 风险代理: basic, advanced, full
            'api_access': False,  # API访问
            'technical_support': 'community',  # 技术支持: community, dedicated
            # UI权限定义
            'view_daily_report': True,  # 查看日报
            'view_ai_analysis': False,  # AI分析
            'view_detailed_reasoning': False,  # 详细推理
            'access_historical_reports': False,  # 历史报告
            'export_reports': False,  # 导出报告
            'real_time_alerts': False,  # 实时提醒
            'premium_stocks': False,  # 优质股票
            # 稷下学宫辩论系统权限
            'jixia_debate_access': True,  # 稷下学宫AI辩论系统访问权限
        }
        
        if effective_level == MembershipLevel.ADVANCED:
            permissions.update({
                'intraday_signals': 'limited',
                'risk_proxy': 'advanced',
                'view_ai_analysis': True,
                'access_historical_reports': True,
                'real_time_alerts': True,
            })
        elif effective_level == MembershipLevel.SUPREME:
            permissions.update({
                'intraday_signals': 'full',
                'swing_strategy': True,
                'contrarian_report': True,
                'dedicated_vps': True,
                'private_wechat': True,
                'risk_proxy': 'full',
                'api_access': True,
                'technical_support': 'dedicated',
                'view_ai_analysis': True,
                'view_detailed_reasoning': True,
                'access_historical_reports': True,
                'export_reports': True,
                'real_time_alerts': True,
                'premium_stocks': True,
            })
        
        return permissions
    
    def save_daily_report(self, report_date: str, report_data: Dict[str, Any]):
        """保存每日报告"""
        try:
            with self._get_connection() as conn:
                cursor = conn.cursor()
                
                report_json = json.dumps(report_data, ensure_ascii=False, default=str)
                created_at = datetime.now()
                
                cursor.execute("""
                    INSERT INTO daily_reports (report_date, report_data, created_at)
                    VALUES (%s, %s, %s)
                    ON CONFLICT (report_date) 
                    DO UPDATE SET report_data = EXCLUDED.report_data, created_at = EXCLUDED.created_at
                """, (report_date, report_json, created_at))
                
                conn.commit()
                
        except Exception as e:
            print(f"保存每日报告失败: {e}")
    
    def get_daily_report(self, member: Member, report_date: str) -> Optional[Dict[str, Any]]:
        """获取每日报告（根据会员权限过滤）"""
        try:
            with self._get_connection() as conn:
                cursor = conn.cursor()
                
                cursor.execute("""
                    SELECT report_data FROM daily_reports WHERE report_date = %s
                """, (report_date,))
                
                row = cursor.fetchone()
                if not row:
                    return None
                
                report_data_str = row['report_data']
                report_data = json.loads(report_data_str)
                
                # 根据会员权限过滤报告内容
                filtered_report = self._filter_report_by_permissions(report_data, member)
                
                # 记录访问
                self._log_report_access(member.id, report_date, "daily_report")
                
                return filtered_report
                
        except Exception as e:
            print(f"获取每日报告失败: {e}")
            return None
    
    def _filter_report_by_permissions(self, report_data: Dict[str, Any], member: Member) -> Dict[str, Any]:
        """根据会员权限过滤报告内容"""
        permissions = self.get_member_permissions(member)
        filtered_report = report_data.copy()

        # 根据日内交易信号权限过滤
        if permissions['intraday_signals'] == 'none':
            if 'intraday_signals' in filtered_report:
                filtered_report['intraday_signals'] = {
                    'summary': '升级至[遁甲择时]以查看日内信号',
                    'signals': []
                }
        elif permissions['intraday_signals'] == 'limited':
            if 'intraday_signals' in filtered_report and 'signals' in filtered_report['intraday_signals']:
                # 只显示信号的一部分作为展示
                signals = filtered_report['intraday_signals']['signals']
                if len(signals) > 1:
                    filtered_report['intraday_signals']['signals'] = signals[:1]
                    filtered_report['intraday_signals']['summary'] = f"展示部分信号(1/{len(signals)})。升级至[太乙观澜]查看全部。 "

        # 根据波段策略权限过滤
        if not permissions['swing_strategy']:
            if 'swing_strategy' in filtered_report:
                del filtered_report['swing_strategy']

        # 根据逆向思维研报权限过滤
        if not permissions['contrarian_report']:
            if 'contrarian_report' in filtered_report:
                del filtered_report['contrarian_report']

        # 添加会员信息
        filtered_report['member_info'] = {
            'username': member.username,
            'membership_level': member.membership_level.value,
            'permissions': permissions
        }
        
        return filtered_report
    
    def _log_report_access(self, member_id: int, report_date: str, report_type: str):
        """记录报告访问"""
        try:
            with self._get_connection() as conn:
                cursor = conn.cursor()
                
                access_time = datetime.now()
                
                cursor.execute("""
                    INSERT INTO report_access (member_id, report_date, access_time, report_type)
                    VALUES (%s, %s, %s, %s)
                """, (member_id, report_date, access_time, report_type))
                
                conn.commit()
                
        except Exception as e:
            print(f"记录访问日志失败: {e}")
    
    def get_historical_reports(self, member: Member, days: int = 7) -> List[Dict[str, Any]]:
        """获取历史报告"""
        permissions = self.get_member_permissions(member)
        
        # 所有等级都可以访问历史报告，但深度不同
        effective_level = member.membership_level
        if member.subscription_expires and datetime.now() > member.subscription_expires:
            effective_level = MembershipLevel.FREE

        if effective_level == MembershipLevel.FREE:
            days = min(days, 7)
        elif effective_level == MembershipLevel.ADVANCED:
            days = min(days, 30)
        # SUPREME会员无限制
        
        try:
            with self._get_connection() as conn:
                cursor = conn.cursor()
                
                start_date = (datetime.now() - timedelta(days=days)).strftime('%Y-%m-%d')
                
                cursor.execute("""
                    SELECT report_date, report_data FROM daily_reports 
                    WHERE report_date >= %s 
                    ORDER BY report_date DESC
                """, (start_date,))
                
                reports = []
                for row in cursor.fetchall():
                    report_date = row['report_date']
                    report_data_str = row['report_data']
                    
                    report_data = json.loads(report_data_str)
                    filtered_report = self._filter_report_by_permissions(report_data, member)
                    reports.append({
                        'date': report_date,
                        'data': filtered_report
                    })
                
                return reports
                
        except Exception as e:
            print(f"获取历史报告失败: {e}")
            return []
    
    def upgrade_member(self, username: str, new_level: MembershipLevel) -> bool:
        """升级会员等级"""
        try:
            with self._get_connection() as conn:
                cursor = conn.cursor()
                
                # 设置订阅过期时间
                subscription_expires = None
                if new_level != MembershipLevel.FREE:
                    subscription_expires = datetime.now() + timedelta(days=365)
                
                cursor.execute("""
                    UPDATE members 
                    SET membership_level = %s, subscription_expires = %s
                    WHERE username = %s AND is_active = TRUE
                """, (new_level.value, subscription_expires, username))
                
                if cursor.rowcount > 0:
                    conn.commit()
                    print(f"✅ 用户 {username} 已升级为 {new_level.value}")
                    return True
                else:
                    print(f"❌ 用户 {username} 不存在或已被禁用")
                    return False
                    
        except Exception as e:
            print(f"升级会员失败: {e}")
            return False
    
    def get_member_stats(self) -> Dict[str, Any]:
        """获取会员统计信息"""
        try:
            with self._get_connection() as conn:
                cursor = conn.cursor()
                
                # 总会员数
                cursor.execute("SELECT COUNT(*) FROM members WHERE is_active = TRUE")
                total_members = cursor.fetchone()['count']
                
                # 各等级会员数
                cursor.execute("""
                    SELECT membership_level, COUNT(*) 
                    FROM members 
                    WHERE is_active = TRUE 
                    GROUP BY membership_level
                """)
                level_stats = {}
                for row in cursor.fetchall():
                    level_stats[row['membership_level']] = row['count']
                
                # 今日活跃用户
                today = datetime.now().date()
                cursor.execute("""
                    SELECT COUNT(DISTINCT member_id) 
                    FROM report_access 
                    WHERE DATE(access_time) = %s
                """, (today,))
                daily_active = cursor.fetchone()['count']
                
                return {
                    'total_members': total_members,
                    'level_distribution': level_stats,
                    'daily_active_users': daily_active,
                    'generated_at': datetime.now().isoformat()
                }
                
        except Exception as e:
            print(f"获取会员统计失败: {e}")
            return {}

# 示例使用
def main():
    """示例主函数"""
    member_system = MemberSystem()
    
    # 注册示例会员
    free_member = member_system.register_member(
        "demo_user", "<EMAIL>", "password123", MembershipLevel.FREE
    )
    
    advanced_member = member_system.register_member(
        "advanced_user", "<EMAIL>", "password456", MembershipLevel.ADVANCED
    )

    supreme_member = member_system.register_member(
        "supreme_user", "<EMAIL>", "password789", MembershipLevel.SUPREME
    )
    
    if free_member:
        print(f"✅ 免费会员注册成功: {free_member.username}")
        permissions = member_system.get_member_permissions(free_member)
        print(f"权限: {permissions}")

    if advanced_member:
        print(f"✅ 高级会员注册成功: {advanced_member.username}")
        permissions = member_system.get_member_permissions(advanced_member)
        print(f"权限: {permissions}")

    if supreme_member:
        print(f"✅ 至尊会员注册成功: {supreme_member.username}")
        permissions = member_system.get_member_permissions(supreme_member)
        print(f"权限: {permissions}")
    
    # 保存示例报告
    sample_report = {
        'date': datetime.now().strftime('%Y-%m-%d'),
        'market_summary': {'total_analyzed': 10},
        'ai_recommendations': {'buy_signals': 3},
        'featured_analysis': [{
            'symbol': 'AAPL',
            'recommendation': 'buy',
            'reasoning': '基于技术分析和基本面分析，该股票具有良好的投资价值...'
        }]
    }
    
    member_system.save_daily_report(datetime.now().strftime('%Y-%m-%d'), sample_report)
    print("✅ 示例报告已保存")
    
    # 获取统计信息
    stats = member_system.get_member_stats()
    print(f"📊 会员统计: {stats}")

if __name__ == "__main__":
    main()