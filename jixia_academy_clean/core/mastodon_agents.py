#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
稷下学宫长毛象版 - 三清八仙AI分析师团队
让AI分析师们在长毛象上建立人设和声誉
"""

import asyncio
import json
import os
import random
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional
import logging

from mastodon import Mastodon
import schedule
from debate_system import DebateSystem

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("JixiaAcademyMastodon")


class AgentPersona:
    """AI分析师人设配置"""

    def __init__(self, name: str, role: str, personality: str,
                 expertise: List[str], posting_style: str):
        self.name = name
        self.role = role
        self.personality = personality
        self.expertise = expertise
        self.posting_style = posting_style
        self.memory = []  # 历史发言记录
        self.debate_history = []  # 辩论历史记录
        self.pending_replies = []  # 待回复的提及
        self.context_memory = {}  # 上下文记忆库
        self.reputation_score = 0.0
        self.followers_count = 0
        self.accuracy_rate = 0.0

    def add_debate_record(self, topic: str, position: str, arguments: List[str],
                         timestamp: str, debate_id: str):
        """添加辩论记录"""
        debate_record = {
            "debate_id": debate_id,
            "topic": topic,
            "position": position,
            "arguments": arguments,
            "timestamp": timestamp,
            "related_posts": []
        }
        self.debate_history.append(debate_record)

        # 更新上下文记忆
        self.context_memory[f"debate_{debate_id}"] = {
            "topic": topic,
            "my_stance": position,
            "key_points": arguments,
            "timestamp": timestamp
        }

    def find_relevant_debate(self, query: str) -> Optional[Dict]:
        """根据查询找到相关的辩论记录"""
        query_lower = query.lower()

        for debate in self.debate_history:
            # 检查话题匹配
            if any(keyword in query_lower for keyword in debate["topic"].lower().split()):
                return debate

            # 检查论点匹配
            for arg in debate["arguments"]:
                if any(keyword in query_lower for keyword in arg.lower().split()):
                    return debate

        return None


class MastodonAgent:
    """单个长毛象AI分析师"""
    
    def __init__(self, persona: AgentPersona, mastodon_config: Dict):
        self.persona = persona
        self.mastodon = None
        self.config = mastodon_config
        self.last_post_time = None
        self.daily_post_count = 0
        self.max_daily_posts = 5
        
        # 初始化长毛象连接
        self._init_mastodon()
    
    def _init_mastodon(self):
        """初始化长毛象API连接"""
        try:
            self.mastodon = Mastodon(
                client_id=self.config['client_id'],
                client_secret=self.config['client_secret'],
                access_token=self.config['access_token'],
                api_base_url=self.config['api_base_url']
            )
            logger.info(f"✅ {self.persona.name} 长毛象连接已建立")
        except Exception as e:
            logger.error(f"❌ {self.persona.name} 长毛象连接失败: {e}")
    
    def generate_market_analysis(self) -> str:
        """生成市场分析内容"""
        # 这里应该调用真实的分析API
        # 暂时使用模拟数据
        
        analysis_templates = {
            "太上老君": [
                "🧙‍♂️ 观今日市场，如太极阴阳转换。{market_trend}之势已现，智者当顺势而为。#太公心易 #市场哲学",
                "📿 以道观市，{sector}板块呈{sentiment}之象。古云：知止而后有定，定而后能静。#投资智慧",
                "🌟 市场如流水，{analysis}。老君曰：上善若水，水善利万物而不争。#道法自然"
            ],
            "元始天尊": [
                "⚡ 技术分析显示：{symbol} {signal}信号确认，{confidence}%置信度。数据不会说谎。#量化分析",
                "📊 {timeframe}级别突破确认，{indicator}指标共振。精准如剑，快如闪电。#技术分析",
                "🎯 算法检测到{pattern}形态，预期{target}。让数据指引方向。#程序化交易"
            ],
            "铁拐李": [
                "🦯 众人恐惧时我贪婪！{contrarian_view}。逆向思维，往往能发现被忽视的机会。#逆向投资",
                "⚖️ 市场情绪过于{emotion}，理性投资者应该{action}。铁拐李从不随波逐流。#独立思考",
                "🔄 {reversal_signal}出现，可能是趋势转折点。敢于逆流而上，方显英雄本色。#反转交易"
            ]
        }
        
        # 根据人设选择模板
        templates = analysis_templates.get(self.persona.name, analysis_templates["太上老君"])
        template = random.choice(templates)
        
        # 填充模板变量（实际应该从真实数据获取）
        variables = {
            "market_trend": random.choice(["上涨", "下跌", "震荡"]),
            "sector": random.choice(["科技", "金融", "医药", "新能源"]),
            "sentiment": random.choice(["强势", "弱势", "平衡"]),
            "analysis": "当前处于关键支撑位，需密切关注成交量变化",
            "symbol": "000001.SZ",
            "signal": random.choice(["买入", "卖出", "观望"]),
            "confidence": random.randint(70, 95),
            "timeframe": random.choice(["日线", "周线", "月线"]),
            "indicator": "MACD",
            "pattern": "双底",
            "target": "上涨10%",
            "contrarian_view": "此时正是布局良机",
            "emotion": random.choice(["乐观", "悲观", "谨慎"]),
            "action": random.choice(["逢低买入", "获利了结", "保持观望"]),
            "reversal_signal": "背离信号"
        }
        
        return template.format(**variables)
    
    def post_analysis(self, content: str, visibility: str = "public",
                     check_debate: bool = True) -> bool:
        """发布分析内容到长毛象"""
        try:
            if not self.mastodon:
                logger.error(f"❌ {self.persona.name} 长毛象连接未建立")
                return False

            # 检查发布频率限制
            if not self._can_post():
                logger.warning(f"⚠️ {self.persona.name} 达到每日发布限制")
                return False

            # 发布内容
            status = self.mastodon.status_post(
                status=content,
                visibility=visibility,
                sensitive=False
            )

            # 更新统计
            self.last_post_time = datetime.now()
            self.daily_post_count += 1

            # 保存到记忆
            memory_record = {
                "content": content,
                "timestamp": datetime.now().isoformat(),
                "status_id": status["id"],
                "url": status["url"],
                "type": "analysis"
            }
            self.persona.memory.append(memory_record)

            # 检查是否触发辩论（如果启用）
            if check_debate and hasattr(self, '_debate_system_ref'):
                debate_type = self._debate_system_ref.detect_debate_trigger(content, self.persona.name)
                if debate_type:
                    debate_id = self._debate_system_ref.initiate_debate(
                        debate_type, self.persona.name, content
                    )
                    memory_record["debate_id"] = debate_id
                    logger.info(f"🔥 {self.persona.name} 触发辩论: {debate_type}")

            logger.info(f"✅ {self.persona.name} 发布成功: {content[:50]}...")
            return True

        except Exception as e:
            logger.error(f"❌ {self.persona.name} 发布失败: {e}")
            return False

    def set_debate_system_ref(self, debate_system):
        """设置辩论系统引用"""
        self._debate_system_ref = debate_system
    
    def _can_post(self) -> bool:
        """检查是否可以发布"""
        # 检查每日限制
        if self.daily_post_count >= self.max_daily_posts:
            return False
        
        # 检查时间间隔（至少间隔1小时）
        if self.last_post_time:
            time_diff = datetime.now() - self.last_post_time
            if time_diff < timedelta(hours=1):
                return False
        
        return True
    
    def reply_to_mention(self, mention_id: str, reply_content: str):
        """回复提及"""
        try:
            self.mastodon.status_reply(
                to_status=mention_id,
                status=reply_content
            )
            logger.info(f"✅ {self.persona.name} 回复成功")
        except Exception as e:
            logger.error(f"❌ {self.persona.name} 回复失败: {e}")
    
    def get_mentions(self) -> List[Dict]:
        """获取提及"""
        try:
            notifications = self.mastodon.notifications(types=['mention'])
            return [n for n in notifications if not n.get('dismissed', False)]
        except Exception as e:
            logger.error(f"❌ {self.persona.name} 获取提及失败: {e}")
            return []

    def analyze_mention_intent(self, mention_text: str, username: str) -> Dict:
        """分析提及的意图和内容"""
        mention_lower = mention_text.lower()

        # 移除@用户名
        clean_text = mention_text.replace(f"@{self.persona.name.lower()}", "").strip()

        intent_analysis = {
            "type": "general",
            "topic": None,
            "requires_debate_context": False,
            "urgency": "normal",
            "sentiment": "neutral",
            "keywords": []
        }

        # 检测辩论相关询问
        debate_keywords = ["辩论", "观点", "论点", "立场", "看法", "意见", "怎么看", "认为"]
        if any(keyword in mention_lower for keyword in debate_keywords):
            intent_analysis["type"] = "debate_inquiry"
            intent_analysis["requires_debate_context"] = True

        # 检测市场询问
        market_keywords = ["市场", "股票", "投资", "买入", "卖出", "分析", "预测"]
        if any(keyword in mention_lower for keyword in market_keywords):
            intent_analysis["type"] = "market_inquiry"

        # 检测个人询问
        personal_keywords = ["你是", "介绍", "专长", "擅长", "经验"]
        if any(keyword in mention_lower for keyword in personal_keywords):
            intent_analysis["type"] = "personal_inquiry"

        # 提取关键词
        intent_analysis["keywords"] = [word for word in clean_text.split()
                                     if len(word) > 1 and word not in ["的", "了", "是", "在", "有"]]

        return intent_analysis

    def generate_contextual_reply(self, mention: Dict) -> str:
        """生成上下文相关的回复"""
        mention_text = mention['status']['content']
        username = mention['account']['username']

        # 分析意图
        intent = self.analyze_mention_intent(mention_text, username)

        if intent["requires_debate_context"]:
            return self._generate_debate_context_reply(mention_text, username, intent)
        elif intent["type"] == "market_inquiry":
            return self._generate_market_reply(mention_text, username, intent)
        elif intent["type"] == "personal_inquiry":
            return self._generate_personal_reply(mention_text, username, intent)
        else:
            return self._generate_general_reply(mention_text, username, intent)

    def _generate_debate_context_reply(self, mention_text: str, username: str, intent: Dict) -> str:
        """生成基于辩论上下文的回复"""
        # 查找相关辩论记录
        relevant_debate = self.persona.find_relevant_debate(mention_text)

        if relevant_debate:
            topic = relevant_debate["topic"]
            position = relevant_debate["position"]
            key_arguments = relevant_debate["arguments"]

            # 根据人设生成回复
            if self.persona.name == "吕洞宾":
                reply = f"@{username} 关于{topic}的辩论，我的立场是{position}。"
                reply += f"主要论点包括：{'、'.join(key_arguments[:2])}。"
                reply += "成长股投资需要前瞻性思维，不能只看当下。⚔️ #成长投资"

            elif self.persona.name == "太上老君":
                reply = f"@{username} 道法自然，关于{topic}，吾之观点为{position}。"
                reply += f"正如老子所言：{'、'.join(key_arguments[:2])}。"
                reply += "市场如水，顺势而为方为上策。🧙‍♂️ #投资哲学"

            elif self.persona.name == "铁拐李":
                reply = f"@{username} 哈！{topic}这事儿，我当时就说{position}！"
                reply += f"理由很简单：{'、'.join(key_arguments[:2])}。"
                reply += "逆向思维，往往能看到别人看不到的机会。🦯 #逆向投资"

            else:
                reply = f"@{username} 在{topic}的讨论中，我认为{position}。"
                reply += f"主要基于：{'、'.join(key_arguments[:2])}。"
        else:
            # 没有找到相关辩论记录
            reply = f"@{username} 感谢提及！我最近没有参与相关话题的辩论，"
            reply += f"不过基于我的专业领域({self.persona.role})，我认为..."

            # 根据人设生成通用观点
            if self.persona.name == "吕洞宾":
                reply += "成长股投资要看长远，短期波动不足为虑。⚔️"
            elif self.persona.name == "太上老君":
                reply += "投资如修道，需要耐心和智慧。🧙‍♂️"
            elif self.persona.name == "铁拐李":
                reply += "市场总是错的，独立思考最重要。🦯"

        return reply

    def _generate_market_reply(self, mention_text: str, username: str, intent: Dict) -> str:
        """生成市场相关回复"""
        keywords = intent["keywords"]

        # 根据人设和关键词生成回复
        if self.persona.name == "元始天尊":
            reply = f"@{username} 从技术分析角度来看，"
            if "买入" in keywords or "卖出" in keywords:
                reply += "需要结合多个技术指标确认信号。当前MACD、RSI等指标显示..."
            else:
                reply += "市场数据显示当前处于关键位置，建议关注成交量变化。"
            reply += "⚡ #技术分析"

        elif self.persona.name == "灵宝天尊":
            reply = f"@{username} 投资需谨慎，"
            reply += "建议先评估风险承受能力，合理配置资产。"
            reply += "记住：保本第一，收益第二。🛡️ #风险管理"

        else:
            reply = f"@{username} 基于我的分析，"
            reply += "建议关注基本面变化和市场情绪。具体操作需要结合个人情况。"

        return reply

    def _generate_personal_reply(self, mention_text: str, username: str, intent: Dict) -> str:
        """生成个人介绍回复"""
        reply = f"@{username} 我是{self.persona.name}，{self.persona.role}。"
        reply += f"专长领域：{', '.join(self.persona.expertise)}。"
        reply += f"投资理念：{self.persona.personality}。"

        # 添加个性化结尾
        if self.persona.name == "吕洞宾":
            reply += "愿与君共探成长投资之道！⚔️"
        elif self.persona.name == "太上老君":
            reply += "道法自然，投资亦然。🧙‍♂️"
        elif self.persona.name == "铁拐李":
            reply += "独立思考，逆向而行！🦯"

        return reply

    def _generate_general_reply(self, mention_text: str, username: str, intent: Dict) -> str:
        """生成通用回复"""
        greetings = [
            f"@{username} 感谢关注！",
            f"@{username} 很高兴与你交流！",
            f"@{username} 谢谢提及！"
        ]

        reply = random.choice(greetings)

        # 根据人设添加特色回复
        if self.persona.name == "太上老君":
            reply += "有什么投资问题，老君愿意分享一些心得。🧙‍♂️"
        elif self.persona.name == "元始天尊":
            reply += "数据和技术分析方面的问题，我很乐意解答。⚡"
        elif self.persona.name == "铁拐李":
            reply += "逆向投资的思路，咱们可以聊聊。🦯"
        else:
            reply += f"关于{self.persona.role}的问题，我很乐意分享经验。"

        return reply
    
    def reset_daily_count(self):
        """重置每日发布计数"""
        self.daily_post_count = 0
        logger.info(f"🔄 {self.persona.name} 每日计数已重置")


class JixiaAcademyMastodon:
    """稷下学宫长毛象版主控制器"""
    
    def __init__(self, config_file: str = "mastodon_config.json"):
        self.agents: Dict[str, MastodonAgent] = {}
        self.config = self._load_config(config_file)
        self.running = False

        # 初始化所有AI分析师
        self._init_agents()

        # 初始化辩论系统
        self.debate_system = DebateSystem(self)

        # 设置定时任务
        self._setup_schedule()
    
    def _load_config(self, config_file: str) -> Dict:
        """加载配置文件"""
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except FileNotFoundError:
            logger.error(f"❌ 配置文件 {config_file} 不存在")
            return {}
        except Exception as e:
            logger.error(f"❌ 加载配置失败: {e}")
            return {}
    
    def _init_agents(self):
        """初始化所有AI分析师"""
        # 三清论道
        sanqing_personas = [
            AgentPersona(
                name="太上老君",
                role="宏观战略分析师",
                personality="深谋远虑，哲学思辨",
                expertise=["宏观经济", "政策分析", "长期趋势"],
                posting_style="引经据典，富含哲理"
            ),
            AgentPersona(
                name="元始天尊", 
                role="技术分析大师",
                personality="精准理性，数据驱动",
                expertise=["技术分析", "量化交易", "程序化"],
                posting_style="数据说话，逻辑清晰"
            ),
            AgentPersona(
                name="灵宝天尊",
                role="风险控制专家", 
                personality="谨慎保守，防范未然",
                expertise=["风险管理", "资产配置", "避险策略"],
                posting_style="稳健务实，风险提示"
            )
        ]
        
        # 八仙过海
        baxian_personas = [
            AgentPersona(
                name="铁拐李",
                role="逆向投资专家",
                personality="独立思考，逆向而行",
                expertise=["逆向投资", "价值发现", "反转交易"],
                posting_style="观点独特，敢于逆流"
            ),
            AgentPersona(
                name="汉钟离",
                role="趋势跟踪大师",
                personality="顺势而为，把握节奏",
                expertise=["趋势分析", "动量交易", "周期研究"],
                posting_style="顺势而为，节奏把控"
            ),
            AgentPersona(
                name="张果老",
                role="价值投资老炮",
                personality="经验丰富，价值导向",
                expertise=["价值投资", "基本面分析", "长期持有"],
                posting_style="经验之谈，价值为王"
            )
        ]
        
        # 创建Agent实例
        all_personas = sanqing_personas + baxian_personas

        for persona in all_personas:
            if persona.name in self.config.get('agents', {}):
                agent_config = self.config['agents'][persona.name]
                agent = MastodonAgent(persona, agent_config)
                self.agents[persona.name] = agent
                logger.info(f"✅ {persona.name} 初始化完成")
            else:
                logger.warning(f"⚠️ {persona.name} 配置缺失，跳过初始化")

        # 为所有Agent设置辩论系统引用
        if hasattr(self, 'debate_system'):
            for agent in self.agents.values():
                agent.set_debate_system_ref(self.debate_system)
    
    def _setup_schedule(self):
        """设置定时任务"""
        # 每天早上8点重置发布计数
        schedule.every().day.at("08:00").do(self._reset_all_daily_counts)
        
        # 工作日定时发布
        schedule.every().monday.at("09:30").do(self._morning_market_analysis)
        schedule.every().tuesday.at("09:30").do(self._morning_market_analysis)
        schedule.every().wednesday.at("09:30").do(self._morning_market_analysis)
        schedule.every().thursday.at("09:30").do(self._morning_market_analysis)
        schedule.every().friday.at("09:30").do(self._morning_market_analysis)
        
        # 收盘后分析
        schedule.every().monday.at("15:30").do(self._closing_market_analysis)
        schedule.every().tuesday.at("15:30").do(self._closing_market_analysis)
        schedule.every().wednesday.at("15:30").do(self._closing_market_analysis)
        schedule.every().thursday.at("15:30").do(self._closing_market_analysis)
        schedule.every().friday.at("15:30").do(self._closing_market_analysis)
        
        # 每小时检查提及和回复
        schedule.every().hour.do(self._check_mentions)
        
        logger.info("⏰ 定时任务设置完成")
    
    def _reset_all_daily_counts(self):
        """重置所有Agent的每日计数"""
        for agent in self.agents.values():
            agent.reset_daily_count()
    
    def _morning_market_analysis(self):
        """早盘分析"""
        # 随机选择2-3个分析师发布早盘观点
        selected_agents = random.sample(list(self.agents.values()), 
                                      min(3, len(self.agents)))
        
        for agent in selected_agents:
            content = agent.generate_market_analysis()
            agent.post_analysis(content)
            time.sleep(random.randint(300, 900))  # 随机间隔5-15分钟
    
    def _closing_market_analysis(self):
        """收盘分析"""
        # 收盘后的总结分析
        selected_agents = random.sample(list(self.agents.values()), 
                                      min(2, len(self.agents)))
        
        for agent in selected_agents:
            content = agent.generate_market_analysis()
            agent.post_analysis(content)
            time.sleep(random.randint(600, 1800))  # 随机间隔10-30分钟
    
    def _check_mentions(self):
        """检查并回复提及"""
        for agent in self.agents.values():
            mentions = agent.get_mentions()
            for mention in mentions[:3]:  # 限制每次处理3个提及
                try:
                    # 生成智能回复
                    reply = agent.generate_contextual_reply(mention)

                    # 发送回复
                    agent.reply_to_mention(mention['status']['id'], reply)

                    # 记录到记忆中
                    agent.persona.memory.append({
                        "type": "reply",
                        "content": reply,
                        "original_mention": mention['status']['content'],
                        "username": mention['account']['username'],
                        "timestamp": datetime.now().isoformat()
                    })

                    logger.info(f"✅ {agent.persona.name} 智能回复: {reply[:50]}...")
                    time.sleep(60)  # 间隔1分钟

                except Exception as e:
                    logger.error(f"❌ {agent.persona.name} 回复失败: {e}")
                    # 降级到简单回复
                    simple_reply = f"@{mention['account']['username']} 感谢关注！{agent.persona.name}正在分析中... 🤔"
                    try:
                        agent.reply_to_mention(mention['status']['id'], simple_reply)
                    except:
                        pass
    
    async def start(self):
        """启动稷下学宫长毛象版"""
        self.running = True
        logger.info("🚀 稷下学宫长毛象版启动")
        
        while self.running:
            try:
                schedule.run_pending()
                await asyncio.sleep(60)  # 每分钟检查一次
            except KeyboardInterrupt:
                logger.info("⏹️ 收到停止信号")
                break
            except Exception as e:
                logger.error(f"❌ 运行错误: {e}")
                await asyncio.sleep(300)  # 错误后等待5分钟
    
    def stop(self):
        """停止运行"""
        self.running = False
        logger.info("⏹️ 稷下学宫长毛象版已停止")
    
    def get_agent_stats(self) -> Dict:
        """获取所有Agent统计信息"""
        stats = {}
        for name, agent in self.agents.items():
            stats[name] = {
                "daily_posts": agent.daily_post_count,
                "total_posts": len(agent.persona.memory),
                "last_post": agent.last_post_time.isoformat() if agent.last_post_time else None,
                "reputation": agent.persona.reputation_score
            }
        return stats


if __name__ == "__main__":
    # 启动稷下学宫长毛象版
    academy = JixiaAcademyMastodon()
    
    try:
        asyncio.run(academy.start())
    except KeyboardInterrupt:
        academy.stop()
        logger.info("👋 稷下学宫长毛象版已退出")
