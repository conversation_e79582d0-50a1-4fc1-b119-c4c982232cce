# -*- coding: utf-8 -*-
"""
🐒 散户修仙之路 - 英雄之旅系统
基于西游记四季轮回的十二等级进化体系

春（花果山时期）- 初入江湖
夏（取经路上）- 历练成长  
秋（功成名就）- 巅峰时刻
冬（劫难重重）- 涅槃重生

作者：太公心易BI系统
版本：v2.0 GameFi Enhanced
"""

import json
import logging
from datetime import datetime
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict
from enum import Enum
import asyncio

class Season(Enum):
    """四季轮回"""
    SPRING = "春"  # 花果山时期
    SUMMER = "夏"  # 取经路上
    AUTUMN = "秋"  # 功成名就
    WINTER = "冬"  # 劫难重生

class HeroLevel(Enum):
    """十二等级英雄之旅"""
    # 春季 - 花果山时期（1-3级）
    STONE_MONKEY = 1      # 石猴初醒 - 刚入市场的小白
    MONKEY_KING = 2       # 美猴王 - 初尝甜头的散户
    SEEKING_IMMORTAL = 3  # 求仙问道 - 开始学习技术分析
    
    # 夏季 - 取经路上（4-6级）
    DISCIPLE = 4          # 拜师学艺 - 跟随大V学习
    DEMON_FIGHTER = 5     # 降妖除魔 - 能识别一些套路
    PROTECTOR = 6         # 护法金刚 - 有了自己的交易体系
    
    # 秋季 - 功成名就（7-9级）
    GREAT_SAGE = 7        # 齐天大圣 - 市场中的高手
    JADE_EMPEROR = 8      # 玉皇大帝 - 呼风唤雨的大佬
    GOLDEN_PALACE = 9     # 金銮殿主 - 财富自由的巅峰
    
    # 冬季 - 劫难重生（10-12级）
    CAPTURED = 10         # 被擒入狱 - 遭遇重大亏损
    FURNACE_TRIAL = 11    # 八卦炉炼 - 在痛苦中反思
    MOUNTAIN_PRESSED = 12 # 五行山下 - 彻底觉悟，重新开始

@dataclass
class HeroStatus:
    """英雄状态"""
    level: HeroLevel
    season: Season
    experience: int
    total_pnl: float
    win_rate: float
    max_drawdown: float
    trading_days: int
    achievements: List[str]
    current_trial: Optional[str]
    next_level_requirement: Dict[str, Any]
    
    def to_dict(self) -> Dict:
        return {
            'level': self.level.value,
            'level_name': self.get_level_name(),
            'season': self.season.value,
            'experience': self.experience,
            'total_pnl': self.total_pnl,
            'win_rate': self.win_rate,
            'max_drawdown': self.max_drawdown,
            'trading_days': self.trading_days,
            'achievements': self.achievements,
            'current_trial': self.current_trial,
            'next_level_requirement': self.next_level_requirement
        }
    
    def get_level_name(self) -> str:
        """获取等级名称"""
        level_names = {
            1: "石猴初醒",
            2: "美猴王",
            3: "求仙问道",
            4: "拜师学艺", 
            5: "降妖除魔",
            6: "护法金刚",
            7: "齐天大圣",
            8: "玉皇大帝",
            9: "金銮殿主",
            10: "被擒入狱",
            11: "八卦炉炼",
            12: "五行山下"
        }
        return level_names.get(self.level.value, "未知等级")

class HeroJourneySystem:
    """散户修仙之路系统"""
    
    def __init__(self):
        self.logger = logging.getLogger('HeroJourneySystem')
        self.level_requirements = self._initialize_level_requirements()
        self.season_stories = self._initialize_season_stories()
        self.achievements_catalog = self._initialize_achievements()
    
    def _initialize_level_requirements(self) -> Dict[int, Dict]:
        """初始化等级要求"""
        return {
            1: {  # 石猴初醒
                "experience": 0,
                "min_trades": 0,
                "description": "刚刚踏入股市，对一切都充满好奇",
                "trial": "完成第一笔交易",
                "reward": "获得新手保护期，小额资金练手"
            },
            2: {  # 美猴王
                "experience": 100,
                "min_trades": 10,
                "win_rate": 0.3,
                "description": "初尝甜头，以为自己是天选之子",
                "trial": "连续盈利3天",
                "reward": "解锁基础技术指标"
            },
            3: {  # 求仙问道
                "experience": 500,
                "min_trades": 50,
                "max_single_loss": -0.05,
                "description": "开始意识到需要学习，寻找交易圣杯",
                "trial": "学会止损，单次亏损不超过5%",
                "reward": "获得稷下学宫辩论观摩权"
            },
            4: {  # 拜师学艺
                "experience": 1500,
                "min_trades": 100,
                "win_rate": 0.45,
                "description": "找到了师父，开始系统学习",
                "trial": "胜率达到45%以上",
                "reward": "解锁RSS事件监控系统"
            },
            5: {  # 降妖除魔
                "experience": 3000,
                "min_trades": 200,
                "max_drawdown": -0.15,
                "description": "能够识别市场陷阱，避开大部分套路",
                "trial": "最大回撤控制在15%以内",
                "reward": "获得AI辩论系统参与权"
            },
            6: {  # 护法金刚
                "experience": 6000,
                "min_trades": 500,
                "win_rate": 0.55,
                "sharpe_ratio": 1.0,
                "description": "形成了自己的交易体系，能够稳定盈利",
                "trial": "夏普比率达到1.0以上",
                "reward": "解锁高级策略和VIP功能"
            },
            7: {  # 齐天大圣
                "experience": 10000,
                "total_pnl": 1.0,
                "win_rate": 0.6,
                "description": "市场中的高手，开始有了名气",
                "trial": "总收益率达到100%",
                "reward": "获得导师资格，可以收徒弟"
            },
            8: {  # 玉皇大帝
                "experience": 20000,
                "total_pnl": 3.0,
                "max_consecutive_wins": 10,
                "description": "呼风唤雨的大佬，影响力巨大",
                "trial": "连续盈利10次以上",
                "reward": "解锁量化策略工厂"
            },
            9: {  # 金銮殿主
                "experience": 50000,
                "total_pnl": 10.0,
                "assets_under_management": 10000000,
                "description": "财富自由的巅峰，坐拥金山银山",
                "trial": "管理资金达到千万级别",
                "reward": "获得炼妖壶终极权限"
            },
            10: {  # 被擒入狱
                "trigger": "major_loss",
                "loss_threshold": -0.5,
                "description": "遭遇重大亏损，从云端跌落",
                "trial": "承认错误，开始反思",
                "reward": "获得重生机会"
            },
            11: {  # 八卦炉炼
                "experience": 1000,
                "reflection_days": 30,
                "description": "在痛苦中反思，重新审视交易哲学",
                "trial": "完成30天的交易反思",
                "reward": "获得火眼金睛技能"
            },
            12: {  # 五行山下
                "experience": 5000,
                "enlightenment_score": 90,
                "description": "彻底觉悟，明白了市场的本质",
                "trial": "通过最终觉悟测试",
                "reward": "重新开始，但保留所有经验和智慧"
            }
        }
    
    def _initialize_season_stories(self) -> Dict[Season, Dict]:
        """初始化四季故事"""
        return {
            Season.SPRING: {
                "title": "花果山时期 - 初入江湖",
                "description": "见生死，求大道，春风得意马蹄疾",
                "levels": [1, 2, 3],
                "theme": "好奇、冲动、学习",
                "color": "#4CAF50",
                "story": """
                在花果山的日子里，你就像那只刚刚从石头里蹦出来的猴子，
                对这个股市世界充满了好奇。每一个K线都像是新的发现，
                每一次交易都让你兴奋不已。虽然偶尔会被套牢，
                但你总是能很快忘记痛苦，继续你的冒险之旅。
                """
            },
            Season.SUMMER: {
                "title": "取经路上 - 历练成长",
                "description": "得所望，傲气扬，一路降妖又除魔",
                "levels": [4, 5, 6],
                "theme": "成长、历练、建立体系",
                "color": "#FF9800",
                "story": """
                踏上了取经之路，你开始真正的修行。每一次交易都是一次历练，
                每一个亏损都是一个妖怪需要降服。你学会了止损，学会了风控，
                慢慢建立起自己的交易体系。虽然路途艰辛，但你的内功日渐深厚。
                """
            },
            Season.AUTUMN: {
                "title": "功成名就 - 巅峰时刻",
                "description": "受招安，喝玉液，砸金銮，秋高气爽",
                "levels": [7, 8, 9],
                "theme": "成功、荣耀、巅峰",
                "color": "#FFD700",
                "story": """
                功成名就的时刻到了！你已经是市场中的传奇人物，
                每一次操作都被人津津乐道。财富如秋天的果实一样丰硕，
                你坐在金銮殿上，俯视着整个市场。但是，巅峰往往意味着...
                """
            },
            Season.WINTER: {
                "title": "劫难重生 - 涅槃重生",
                "description": "终被擒，八卦炉，五行山，冬藏待春",
                "levels": [10, 11, 12],
                "theme": "挫折、反思、重生",
                "color": "#607D8B",
                "story": """
                冬天来了，劫难也来了。巨大的亏损让你从云端跌落，
                就像被压在五行山下的孙悟空。但这不是结束，而是新的开始。
                在痛苦中反思，在挫折中成长，最终你将获得真正的智慧，
                迎来新的春天。
                """
            }
        }
    
    def _initialize_achievements(self) -> Dict[str, Dict]:
        """初始化成就系统"""
        return {
            "first_trade": {
                "name": "初出茅庐",
                "description": "完成第一笔交易",
                "icon": "🐒",
                "rarity": "common"
            },
            "first_profit": {
                "name": "初尝甜头", 
                "description": "获得第一次盈利",
                "icon": "🍑",
                "rarity": "common"
            },
            "win_streak_5": {
                "name": "连胜将军",
                "description": "连续盈利5次",
                "icon": "🏆",
                "rarity": "rare"
            },
            "loss_control": {
                "name": "止损大师",
                "description": "单次亏损控制在5%以内",
                "icon": "🛡️",
                "rarity": "rare"
            },
            "market_crash_survivor": {
                "name": "股灾幸存者",
                "description": "在市场大跌中保持盈利",
                "icon": "🦅",
                "rarity": "legendary"
            },
            "mentor": {
                "name": "一代宗师",
                "description": "成为其他玩家的导师",
                "icon": "👨‍🏫",
                "rarity": "legendary"
            },
            "phoenix_reborn": {
                "name": "浴火重生",
                "description": "从重大亏损中完全恢复",
                "icon": "🔥",
                "rarity": "mythical"
            }
        }
    
    def calculate_hero_status(self, trading_data: Dict[str, Any]) -> HeroStatus:
        """计算英雄状态"""
        # 基础数据提取
        total_trades = trading_data.get('total_trades', 0)
        total_pnl = trading_data.get('total_pnl', 0.0)
        win_rate = trading_data.get('win_rate', 0.0)
        max_drawdown = trading_data.get('max_drawdown', 0.0)
        trading_days = trading_data.get('trading_days', 0)
        
        # 计算经验值
        experience = self._calculate_experience(trading_data)
        
        # 确定等级
        current_level = self._determine_level(experience, trading_data)
        
        # 确定季节
        current_season = self._determine_season(current_level)
        
        # 获取成就
        achievements = self._calculate_achievements(trading_data)
        
        # 获取当前试炼
        current_trial = self._get_current_trial(current_level, trading_data)
        
        # 获取下一等级要求
        next_requirement = self._get_next_level_requirement(current_level)
        
        return HeroStatus(
            level=current_level,
            season=current_season,
            experience=experience,
            total_pnl=total_pnl,
            win_rate=win_rate,
            max_drawdown=max_drawdown,
            trading_days=trading_days,
            achievements=achievements,
            current_trial=current_trial,
            next_level_requirement=next_requirement
        )
    
    def _calculate_experience(self, trading_data: Dict) -> int:
        """计算经验值"""
        base_exp = trading_data.get('total_trades', 0) * 10
        profit_bonus = max(0, trading_data.get('total_pnl', 0)) * 100
        time_bonus = trading_data.get('trading_days', 0) * 5
        
        return int(base_exp + profit_bonus + time_bonus)
    
    def _determine_level(self, experience: int, trading_data: Dict) -> HeroLevel:
        """确定当前等级"""
        # 检查是否触发冬季劫难
        if trading_data.get('major_loss_event', False):
            if trading_data.get('reflection_completed', False):
                if trading_data.get('enlightenment_score', 0) >= 90:
                    return HeroLevel.MOUNTAIN_PRESSED
                else:
                    return HeroLevel.FURNACE_TRIAL
            else:
                return HeroLevel.CAPTURED
        
        # 正常等级判断
        for level in range(12, 0, -1):
            if level in self.level_requirements:
                req = self.level_requirements[level]
                if self._meets_requirements(experience, trading_data, req):
                    return HeroLevel(level)
        
        return HeroLevel.STONE_MONKEY
    
    def _meets_requirements(self, experience: int, trading_data: Dict, requirements: Dict) -> bool:
        """检查是否满足等级要求"""
        if experience < requirements.get('experience', 0):
            return False
        
        if trading_data.get('total_trades', 0) < requirements.get('min_trades', 0):
            return False
        
        if 'win_rate' in requirements:
            if trading_data.get('win_rate', 0) < requirements['win_rate']:
                return False
        
        if 'total_pnl' in requirements:
            if trading_data.get('total_pnl', 0) < requirements['total_pnl']:
                return False
        
        return True
    
    def _determine_season(self, level: HeroLevel) -> Season:
        """确定当前季节"""
        if level.value <= 3:
            return Season.SPRING
        elif level.value <= 6:
            return Season.SUMMER
        elif level.value <= 9:
            return Season.AUTUMN
        else:
            return Season.WINTER
    
    def _calculate_achievements(self, trading_data: Dict) -> List[str]:
        """计算已获得的成就"""
        achievements = []
        
        if trading_data.get('total_trades', 0) > 0:
            achievements.append('first_trade')
        
        if trading_data.get('total_pnl', 0) > 0:
            achievements.append('first_profit')
        
        if trading_data.get('max_consecutive_wins', 0) >= 5:
            achievements.append('win_streak_5')
        
        # 更多成就判断逻辑...
        
        return achievements
    
    def _get_current_trial(self, level: HeroLevel, trading_data: Dict) -> Optional[str]:
        """获取当前试炼"""
        if level.value in self.level_requirements:
            return self.level_requirements[level.value].get('trial')
        return None
    
    def _get_next_level_requirement(self, level: HeroLevel) -> Dict[str, Any]:
        """获取下一等级要求"""
        next_level = level.value + 1
        if next_level <= 12 and next_level in self.level_requirements:
            return self.level_requirements[next_level]
        return {}
    
    def generate_hero_card(self, hero_status: HeroStatus) -> str:
        """生成英雄卡片"""
        season_info = self.season_stories[hero_status.season]
        
        card = f"""
╭─────────────────────────────────────╮
│  🐒 散户修仙之路 - 英雄档案 🐒        │
├─────────────────────────────────────┤
│                                     │
│  🏷️  {hero_status.get_level_name()}                │
│  🌸  {season_info['title']}          │
│  ⭐  等级: {hero_status.level.value}/12              │
│  🎯  经验: {hero_status.experience:,}              │
│                                     │
│  📊  交易数据:                       │
│  💰  总收益: {hero_status.total_pnl:.2%}           │
│  🎯  胜率: {hero_status.win_rate:.2%}             │
│  📉  最大回撤: {hero_status.max_drawdown:.2%}      │
│  📅  交易天数: {hero_status.trading_days}          │
│                                     │
│  🏆  成就: {len(hero_status.achievements)}个        │
│  🎯  当前试炼: {hero_status.current_trial or '无'}  │
│                                     │
│  {season_info['description']}        │
╰─────────────────────────────────────╯
        """
        
        return card.strip()

# 使用示例
async def demo_hero_journey():
    """演示英雄之旅系统"""
    system = HeroJourneySystem()
    
    # 模拟交易数据
    trading_data = {
        'total_trades': 150,
        'total_pnl': 0.25,  # 25%收益
        'win_rate': 0.52,   # 52%胜率
        'max_drawdown': -0.12,  # 12%最大回撤
        'trading_days': 90,
        'max_consecutive_wins': 7
    }
    
    # 计算英雄状态
    hero_status = system.calculate_hero_status(trading_data)
    
    # 生成英雄卡片
    hero_card = system.generate_hero_card(hero_status)
    print(hero_card)
    
    # 输出JSON格式数据
    print("\n" + "="*50)
    print("英雄状态JSON:")
    print(json.dumps(hero_status.to_dict(), ensure_ascii=False, indent=2))

if __name__ == "__main__":
    asyncio.run(demo_hero_journey())