#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
太公心易BI系统 - 配置管理器
统一管理炼股葫芦和八仙论道系统的配置
"""

import os
import json
import yaml
from pathlib import Path
from typing import Dict, Any, Optional
from dotenv import load_dotenv

class ConfigManager:
    """统一配置管理器"""
    
    def __init__(self):
        self.base_dir = Path(__file__).parent
        self.jixia_dir = self.base_dir / "jixia_academy"
        
        # 加载环境变量
        load_dotenv(self.base_dir / ".env")
        
        # 如果jixia_academy有独立的.env文件，也加载它
        jixia_env = self.jixia_dir / ".env"
        if jixia_env.exists():
            load_dotenv(jixia_env)
    
    def _safe_int(self, value: str, default: int) -> int:
        """安全的整数转换，处理带注释的配置值"""
        if not value:
            return default
        try:
            # 移除注释部分
            clean_value = value.split('#')[0].strip()
            return int(clean_value) if clean_value else default
        except (ValueError, AttributeError):
            return default
    
    def get_cauldron_config(self) -> Dict[str, Any]:
        """获取炼妖壶配置"""
        return {
            "database": {
                "host": os.getenv("DB_HOST", "localhost"),
                "port": self._safe_int(os.getenv("DB_PORT", "5432"), 5432),
                "name": os.getenv("DB_NAME", "cauldron_db"),
                "user": os.getenv("DB_USER", "postgres"),
                "password": os.getenv("DB_PASSWORD", "")
            },
            "ib_api": {
                "host": os.getenv("IB_HOST", "localhost"),
                "port": self._safe_int(os.getenv("IB_PORT", "4000"), 4000),
                "client_id": self._safe_int(os.getenv("IB_CLIENT_ID", "1"), 1),
                "timeout": self._safe_int(os.getenv("IB_TIMEOUT", "30"), 30),
                "retry_count": self._safe_int(os.getenv("IB_RETRY_COUNT", "3"), 3),
                "market_data_type": self._safe_int(os.getenv("IB_MARKET_DATA_TYPE", "1"), 1)
            },
            "external_apis": {
                "tushare": os.getenv("tushare", ""),
                "blockchair": os.getenv("blockchair", ""),
                "blockchair_base_url": os.getenv("BLOCKCHAIR_BASE_URL", "")
            },
            "n8n": {
                "webhook_url": os.getenv("N8N_WEBHOOK_URL", ""),
                "api_key": os.getenv("N8N_API_KEY", "")
            },
            "analysis_engine": {
                "enabled": os.getenv("ANALYSIS_ENGINE_ENABLED", "True").lower() == "true",
                "daily_analysis_time": os.getenv("DAILY_ANALYSIS_TIME", "09:30"),
                "default_symbols": os.getenv("DEFAULT_SYMBOLS", "AAPL,GOOGL,MSFT,TSLA,AMZN,NVDA,META,NFLX").split(",")
            },
            "member_system": {
                "enabled": os.getenv("MEMBER_SYSTEM_ENABLED", "True").lower() == "true",
                "db_path": os.getenv("MEMBER_DB_PATH", "data/members.db")
            },
            "app": {
                "debug": os.getenv("DEBUG", "True").lower() == "true",
                "log_level": os.getenv("LOG_LEVEL", "INFO")
            }
        }
    
    def get_jixia_academy_config(self) -> Dict[str, Any]:
        """获取八仙论道配置"""
        config = {
            "enabled": os.getenv("JIXIA_ACADEMY_ENABLED", "True").lower() == "true",
            "server": {
                "host": os.getenv("CHAINLIT_HOST", "0.0.0.0"),
                "port": int(os.getenv("CHAINLIT_PORT", "8000")),
                "debug": os.getenv("CHAINLIT_DEBUG", "True").lower() == "true"
            },
            "database": {
                "host": os.getenv("JIXIA_DB_HOST", "localhost"),
                "port": int(os.getenv("JIXIA_DB_PORT", "5432")),
                "name": os.getenv("JIXIA_DB_NAME", "jixia_academy"),
                "user": os.getenv("JIXIA_DB_USER", "postgres"),
                "password": os.getenv("JIXIA_DB_PASSWORD", "")
            },
            "debate": {
                "max_rounds": int(os.getenv("DEBATE_MAX_ROUNDS", "8")),
                "time_limit": int(os.getenv("DEBATE_TIME_LIMIT", "300")),
                "auto_start": os.getenv("DEBATE_AUTO_START", "False").lower() == "true"
            },
            "openrouter_api_keys": {
                "account_1": os.getenv("OPENROUTER_API_KEY_1", ""),
                "account_2": os.getenv("OPENROUTER_API_KEY_2", ""),
                "account_3": os.getenv("OPENROUTER_API_KEY_3", ""),
                "account_4": os.getenv("OPENROUTER_API_KEY_4", "")
            },
            "litellm": {
                "base_url": os.getenv("LITELLM_BASE_URL", ""),
                "api_key": os.getenv("LITELLM_API_KEY", "")
            }
        }
        
        # 加载模型配置
        model_config_file = self.jixia_dir / "config" / "model_config.yaml"
        if model_config_file.exists():
            try:
                with open(model_config_file, 'r', encoding='utf-8') as f:
                    config["models"] = yaml.safe_load(f)
            except Exception as e:
                print(f"警告: 无法加载模型配置文件: {e}")
        
        # 加载团队配置
        team_config_file = self.jixia_dir / "config" / "team-config.json"
        if team_config_file.exists():
            try:
                with open(team_config_file, 'r', encoding='utf-8') as f:
                    config["team"] = json.load(f)
            except Exception as e:
                print(f"警告: 无法加载团队配置文件: {e}")
        
        return config
    
    def get_all_config(self) -> Dict[str, Any]:
        """获取所有系统配置"""
        return {
            "cauldron": self.get_cauldron_config(),
            "jixia_academy": self.get_jixia_academy_config()
        }
    
    def validate_config(self) -> Dict[str, list]:
        """验证配置完整性"""
        issues = {
            "errors": [],
            "warnings": []
        }
        
        # 验证炼股葫芦配置
        penny_config = self.get_cauldron_config()
        
        if not penny_config["database"]["password"]:
            issues["warnings"].append("炼股葫芦数据库密码未设置")
        
        if not penny_config["ib_api"]["host"]:
            issues["errors"].append("IB API主机地址未设置")
        
        # 验证八仙论道配置
        jixia_config = self.get_jixia_academy_config()
        
        if jixia_config["enabled"]:
            api_keys = jixia_config["openrouter_api_keys"]
            empty_keys = [k for k, v in api_keys.items() if not v]
            
            if len(empty_keys) == 4:
                issues["errors"].append("所有OpenRouter API密钥都未设置")
            elif empty_keys:
                issues["warnings"].append(f"部分OpenRouter API密钥未设置: {', '.join(empty_keys)}")
            
            if not jixia_config["litellm"]["api_key"]:
                issues["warnings"].append("LiteLLM API密钥未设置")
        
        return issues
    
    def export_config(self, output_file: Optional[str] = None) -> str:
        """导出配置到文件"""
        config = self.get_all_config()
        
        if output_file is None:
            output_file = self.base_dir / "config_export.json"
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=2, ensure_ascii=False)
        
        return str(output_file)
    
    def print_config_summary(self):
        """打印配置摘要"""
        print("🔧 太公心易BI系统配置摘要")
        print("=" * 50)
        
        # 炼股葫芦配置
        penny_config = self.get_cauldron_config()
        print("\n📊 炼股葫芦配置:")
        print(f"  数据库: {penny_config['database']['host']}:{penny_config['database']['port']}")
        print(f"  IB API: {penny_config['ib_api']['host']}:{penny_config['ib_api']['port']}")
        print(f"  调试模式: {penny_config['app']['debug']}")
        
        # 八仙论道配置
        jixia_config = self.get_jixia_academy_config()
        print("\n🎭 八仙论道配置:")
        print(f"  启用状态: {jixia_config['enabled']}")
        
        if jixia_config['enabled']:
            print(f"  服务器: {jixia_config['server']['host']}:{jixia_config['server']['port']}")
            print(f"  辩论轮数: {jixia_config['debate']['max_rounds']}")
            print(f"  时间限制: {jixia_config['debate']['time_limit']}秒")
            
            # API密钥状态
            api_keys = jixia_config['openrouter_api_keys']
            configured_keys = sum(1 for v in api_keys.values() if v)
            print(f"  API密钥: {configured_keys}/4 已配置")
        
        # 配置验证
        issues = self.validate_config()
        if issues['errors']:
            print("\n❌ 配置错误:")
            for error in issues['errors']:
                print(f"  - {error}")
        
        if issues['warnings']:
            print("\n⚠️  配置警告:")
            for warning in issues['warnings']:
                print(f"  - {warning}")
        
        if not issues['errors'] and not issues['warnings']:
            print("\n✅ 配置检查通过")

def main():
    """主函数 - 用于测试配置管理器"""
    manager = ConfigManager()
    manager.print_config_summary()
    
    # 导出配置
    export_file = manager.export_config()
    print(f"\n📄 配置已导出到: {export_file}")

if __name__ == "__main__":
    main()