import pandas as pd
import random

def get_top_turnover_stocks(count: int = 8) -> pd.DataFrame:
    """
    获取模拟的换手率最高的美国正股列表。

    :param count: 需要获取的股票数量
    :return: 一个包含股票代码、公司名称、昨日成交量和换手率的DataFrame
    """
    # 模拟数据
    symbols = ['AAPL', 'MSFT', 'GOOG', 'AMZN', 'NVDA', 'TSLA', 'META', 'JPM', 'V', 'JNJ']
    results = []

    for symbol in symbols:
        results.append({
            'Symbol': symbol,
            'Volume': random.randint(1000000, 100000000),
            'SharesOutstanding': random.randint(50000000, 1000000000),
            'TurnoverRate': random.uniform(1, 15)
        })

    df = pd.DataFrame(results)
    df_sorted = df.sort_values(by='TurnoverRate', ascending=False)

    return df_sorted.head(count)