#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
稷下学宫辩论结果提纯工作流
MongoDB原始存储 → N8N提纯处理 → Zilliz向量存储
"""

import os
import json
import asyncio
import logging
from datetime import datetime
from typing import Dict, List, Any, Optional
import httpx
from dataclasses import dataclass, asdict
from dotenv import load_dotenv

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 加载环境变量
load_dotenv()

@dataclass
class DebateRecord:
    """辩论记录数据结构"""
    debate_id: str
    topic: str
    participants: List[str]  # 八仙参与者
    debate_rounds: List[Dict[str, Any]]  # 辩论轮次
    sanqing_summary: str  # 三清论道总结
    conclusion: str  # 最终结论
    confidence_score: float  # 置信度
    created_at: datetime
    metadata: Dict[str, Any]

@dataclass
class PurifiedDebateEssence:
    """提纯后的辩论精华"""
    essence_id: str
    original_debate_id: str
    topic_vector: List[float]  # 主题向量
    conclusion_vector: List[float]  # 结论向量
    participant_consensus_vector: List[float]  # 参与者共识向量
    semantic_keywords: List[str]  # 语义关键词
    debate_sentiment: float  # 辩论情感倾向
    wisdom_level: str  # 智慧等级: bronze, silver, gold, platinum
    purification_timestamp: datetime

class DebatePurificationWorkflow:
    """辩论结果提纯工作流管理器"""
    
    def __init__(self):
        self.n8n_base_url = os.getenv('N8N_BASE_URL', 'http://localhost:5678')
        self.webhook_base_url = f"{self.n8n_base_url}/webhook"
        
        # 提纯工作流配置
        self.purification_workflows = {
            'debate_essence_extraction': {
                'webhook_path': 'jixia-academy/debate-purification',
                'description': '辩论精华提取工作流',
                'processing_stages': [
                    'raw_debate_analysis',
                    'semantic_vectorization', 
                    'consensus_extraction',
                    'wisdom_distillation',
                    'zilliz_storage'
                ]
            },
            'batch_purification': {
                'webhook_path': 'jixia-academy/batch-purification',
                'description': '批量辩论结果提纯',
                'processing_stages': [
                    'batch_retrieval',
                    'parallel_processing',
                    'quality_filtering',
                    'bulk_vectorization'
                ]
            }
        }
    
    async def store_raw_debate_to_mongo(self, debate_record: DebateRecord) -> bool:
        """第一步：将原始辩论结果存储到MongoDB"""
        try:
            logger.info(f"📚 存储原始辩论到MongoDB: {debate_record.debate_id}")
            
            # 这里集成MongoDB存储逻辑
            mongo_document = {
                'debate_id': debate_record.debate_id,
                'topic': debate_record.topic,
                'participants': debate_record.participants,
                'debate_rounds': debate_record.debate_rounds,
                'sanqing_summary': debate_record.sanqing_summary,
                'conclusion': debate_record.conclusion,
                'confidence_score': debate_record.confidence_score,
                'created_at': debate_record.created_at.isoformat(),
                'metadata': debate_record.metadata,
                'purification_status': 'pending',  # 等待提纯
                'storage_stage': 'raw_mongo'
            }
            
            # 实际的MongoDB插入操作
            # collection.insert_one(mongo_document)
            
            logger.info(f"✅ 原始辩论已存储到MongoDB")
            return True
            
        except Exception as e:
            logger.error(f"❌ MongoDB存储失败: {e}")
            return False
    
    async def trigger_purification_workflow(self, debate_id: str) -> Dict[str, Any]:
        """第二步：触发N8N提纯工作流"""
        try:
            logger.info(f"🔥 触发辩论提纯工作流: {debate_id}")
            
            webhook_url = f"{self.webhook_base_url}/jixia-academy/debate-purification"
            
            payload = {
                'debate_id': debate_id,
                'purification_type': 'debate_essence_extraction',
                'trigger_time': datetime.now().isoformat(),
                'processing_priority': 'normal',
                'quality_threshold': 0.7,
                'target_vector_dimensions': 384
            }
            
            async with httpx.AsyncClient() as client:
                response = await client.post(webhook_url, json=payload, timeout=60)
                response.raise_for_status()
                
                workflow_result = response.json()
                execution_id = workflow_result.get('executionId', f"purify_{datetime.now().timestamp()}")
            
            logger.info(f"✅ 提纯工作流已启动: {execution_id}")
            
            return {
                'execution_id': execution_id,
                'debate_id': debate_id,
                'workflow_status': 'started',
                'estimated_completion': '2-3分钟'
            }
            
        except Exception as e:
            logger.error(f"❌ 提纯工作流触发失败: {e}")
            raise
    
    async def monitor_purification_progress(self, execution_id: str) -> Dict[str, Any]:
        """监控提纯进度"""
        try:
            # 通过N8N API查询执行状态
            api_url = f"{self.n8n_base_url}/api/v1/executions/{execution_id}"
            headers = {"X-N8N-API-KEY": os.getenv('N8N_API_KEY')}
            
            async with httpx.AsyncClient() as client:
                response = await client.get(api_url, headers=headers)
                
                if response.status_code == 200:
                    execution_data = response.json()
                    
                    return {
                        'execution_id': execution_id,
                        'status': execution_data.get('status', 'unknown'),
                        'progress': execution_data.get('progress', {}),
                        'current_stage': execution_data.get('currentNode', 'unknown'),
                        'completion_percentage': execution_data.get('completionPercentage', 0)
                    }
                else:
                    return {'execution_id': execution_id, 'status': 'query_failed'}
                    
        except Exception as e:
            logger.error(f"❌ 查询提纯进度失败: {e}")
            return {'execution_id': execution_id, 'status': 'error', 'error': str(e)}
    
    async def complete_debate_purification_cycle(self, debate_record: DebateRecord) -> PurifiedDebateEssence:
        """完整的辩论提纯周期"""
        try:
            logger.info(f"🌟 开始完整辩论提纯周期: {debate_record.topic}")
            
            # 第一步：存储原始辩论到MongoDB
            mongo_success = await self.store_raw_debate_to_mongo(debate_record)
            if not mongo_success:
                raise Exception("MongoDB存储失败")
            
            # 第二步：触发N8N提纯工作流
            workflow_result = await self.trigger_purification_workflow(debate_record.debate_id)
            execution_id = workflow_result['execution_id']
            
            # 第三步：监控提纯进度
            max_wait_time = 300  # 最多等待5分钟
            wait_interval = 10   # 每10秒检查一次
            waited_time = 0
            
            while waited_time < max_wait_time:
                progress = await self.monitor_purification_progress(execution_id)
                
                if progress['status'] == 'completed':
                    logger.info("✅ 辩论提纯完成")
                    break
                elif progress['status'] == 'failed':
                    raise Exception(f"提纯工作流失败: {progress.get('error', 'Unknown error')}")
                
                logger.info(f"⏳ 提纯进行中... {progress.get('completion_percentage', 0)}%")
                await asyncio.sleep(wait_interval)
                waited_time += wait_interval
            
            # 第四步：获取提纯结果
            purified_essence = await self.get_purification_result(execution_id, debate_record)
            
            logger.info(f"🎉 辩论提纯周期完成: {purified_essence.essence_id}")
            return purified_essence
            
        except Exception as e:
            logger.error(f"❌ 辩论提纯周期失败: {e}")
            raise
    
    async def get_purification_result(self, execution_id: str, original_debate: DebateRecord) -> PurifiedDebateEssence:
        """获取提纯结果"""
        try:
            # 从N8N工作流结果中提取提纯数据
            # 这里模拟提纯结果，实际应该从N8N执行结果中获取
            
            purified_essence = PurifiedDebateEssence(
                essence_id=f"essence_{original_debate.debate_id}",
                original_debate_id=original_debate.debate_id,
                topic_vector=[0.1] * 384,  # 实际应该是真实的向量
                conclusion_vector=[0.2] * 384,
                participant_consensus_vector=[0.3] * 384,
                semantic_keywords=self._extract_semantic_keywords(original_debate),
                debate_sentiment=original_debate.confidence_score,
                wisdom_level=self._determine_wisdom_level(original_debate.confidence_score),
                purification_timestamp=datetime.now()
            )
            
            return purified_essence
            
        except Exception as e:
            logger.error(f"❌ 获取提纯结果失败: {e}")
            raise
    
    def _extract_semantic_keywords(self, debate: DebateRecord) -> List[str]:
        """提取语义关键词"""
        # 简化实现，实际应该使用NLP技术
        keywords = []
        
        # 从主题中提取
        topic_words = debate.topic.split()
        keywords.extend(topic_words)
        
        # 从结论中提取
        conclusion_words = debate.conclusion.split()[:5]  # 取前5个词
        keywords.extend(conclusion_words)
        
        # 去重并返回
        return list(set(keywords))
    
    def _determine_wisdom_level(self, confidence_score: float) -> str:
        """确定智慧等级"""
        if confidence_score >= 0.9:
            return 'platinum'  # 白金级智慧
        elif confidence_score >= 0.8:
            return 'gold'      # 黄金级智慧
        elif confidence_score >= 0.7:
            return 'silver'    # 白银级智慧
        else:
            return 'bronze'    # 青铜级智慧

# N8N工作流配置
def generate_debate_purification_n8n_workflow():
    """生成辩论提纯N8N工作流配置"""
    
    workflow_config = {
        "name": "稷下学宫辩论提纯工作流",
        "description": "将MongoDB中的原始辩论结果提纯为Zilliz向量",
        "nodes": [
            {
                "name": "Webhook触发",
                "type": "n8n-nodes-base.webhook",
                "parameters": {
                    "path": "jixia-academy/debate-purification",
                    "httpMethod": "POST"
                }
            },
            {
                "name": "从MongoDB获取辩论数据",
                "type": "n8n-nodes-base.mongoDb",
                "parameters": {
                    "operation": "find",
                    "collection": "debate_records",
                    "query": "={\"debate_id\": \"{{$json.debate_id}}\"}"
                }
            },
            {
                "name": "辩论内容预处理",
                "type": "n8n-nodes-base.function",
                "parameters": {
                    "functionCode": """
                    // 提取和清理辩论内容
                    const debate = items[0].json;
                    
                    // 合并所有辩论轮次的内容
                    let fullDebateText = debate.topic + ' ';
                    debate.debate_rounds.forEach(round => {
                        fullDebateText += round.content + ' ';
                    });
                    fullDebateText += debate.sanqing_summary + ' ' + debate.conclusion;
                    
                    // 清理和标准化文本
                    const cleanedText = fullDebateText
                        .replace(/[^\u4e00-\u9fa5\w\s]/g, ' ')  // 保留中文、英文、数字
                        .replace(/\s+/g, ' ')  // 合并多个空格
                        .trim();
                    
                    return [{
                        json: {
                            debate_id: debate.debate_id,
                            topic: debate.topic,
                            full_content: cleanedText,
                            conclusion: debate.conclusion,
                            confidence_score: debate.confidence_score,
                            participants: debate.participants
                        }
                    }];
                    """
                }
            },
            {
                "name": "生成主题向量",
                "type": "n8n-nodes-base.httpRequest",
                "parameters": {
                    "url": "http://sentence-transformer-api:8000/encode",
                    "method": "POST",
                    "body": {
                        "text": "={{$json.topic}}",
                        "model": "sentence-transformers/paraphrase-multilingual-MiniLM-L12-v2"
                    }
                }
            },
            {
                "name": "生成结论向量",
                "type": "n8n-nodes-base.httpRequest",
                "parameters": {
                    "url": "http://sentence-transformer-api:8000/encode",
                    "method": "POST",
                    "body": {
                        "text": "={{$json.conclusion}}",
                        "model": "sentence-transformers/paraphrase-multilingual-MiniLM-L12-v2"
                    }
                }
            },
            {
                "name": "生成全文向量",
                "type": "n8n-nodes-base.httpRequest",
                "parameters": {
                    "url": "http://sentence-transformer-api:8000/encode",
                    "method": "POST",
                    "body": {
                        "text": "={{$json.full_content}}",
                        "model": "sentence-transformers/paraphrase-multilingual-MiniLM-L12-v2"
                    }
                }
            },
            {
                "name": "合并向量数据",
                "type": "n8n-nodes-base.function",
                "parameters": {
                    "functionCode": """
                    // 合并所有向量和元数据
                    const debateData = items[0].json;
                    const topicVector = items[1].json.embedding;
                    const conclusionVector = items[2].json.embedding;
                    const fullContentVector = items[3].json.embedding;
                    
                    return [{
                        json: {
                            essence_id: `essence_${debateData.debate_id}`,
                            original_debate_id: debateData.debate_id,
                            topic: debateData.topic,
                            conclusion: debateData.conclusion,
                            topic_vector: topicVector,
                            conclusion_vector: conclusionVector,
                            full_content_vector: fullContentVector,
                            confidence_score: debateData.confidence_score,
                            participants: debateData.participants,
                            purification_timestamp: new Date().toISOString(),
                            wisdom_level: debateData.confidence_score >= 0.9 ? 'platinum' : 
                                         debateData.confidence_score >= 0.8 ? 'gold' :
                                         debateData.confidence_score >= 0.7 ? 'silver' : 'bronze'
                        }
                    }];
                    """
                }
            },
            {
                "name": "存储到Zilliz",
                "type": "n8n-nodes-base.httpRequest",
                "parameters": {
                    "url": "http://zilliz-api:8000/collections/debate_essences/insert",
                    "method": "POST",
                    "body": "={{$json}}",
                    "headers": {
                        "Authorization": "Bearer {{$env.ZILLIZ_TOKEN}}"
                    }
                }
            },
            {
                "name": "更新MongoDB状态",
                "type": "n8n-nodes-base.mongoDb",
                "parameters": {
                    "operation": "update",
                    "collection": "debate_records",
                    "query": "={\"debate_id\": \"{{$json.original_debate_id}}\"}",
                    "update": "={\"$set\": {\"purification_status\": \"completed\", \"essence_id\": \"{{$json.essence_id}}\", \"purified_at\": \"{{$json.purification_timestamp}}\"}}"
                }
            },
            {
                "name": "返回提纯结果",
                "type": "n8n-nodes-base.function",
                "parameters": {
                    "functionCode": """
                    return [{
                        json: {
                            success: true,
                            message: '辩论提纯完成',
                            essence_id: items[0].json.essence_id,
                            wisdom_level: items[0].json.wisdom_level,
                            processing_time: new Date().toISOString()
                        }
                    }];
                    """
                }
            }
        ]
    }
    
    return workflow_config

# 使用示例
async def demo_debate_purification():
    """演示辩论提纯流程"""
    
    # 创建示例辩论记录
    sample_debate = DebateRecord(
        debate_id="debate_20250103_001",
        topic="比特币价格突破10万美元的市场影响分析",
        participants=["铁拐李", "汉钟离", "张果老", "蓝采和"],
        debate_rounds=[
            {
                "round": 1,
                "participant": "铁拐李",
                "viewpoint": "技术分析显示突破关键阻力位",
                "content": "从技术图表看，比特币突破10万美元是长期上升趋势的延续..."
            },
            {
                "round": 2, 
                "participant": "汉钟离",
                "viewpoint": "宏观经济环境支持",
                "content": "当前通胀预期和货币政策为比特币提供了良好的宏观环境..."
            }
        ],
        sanqing_summary="三清论道认为此次突破具有重要意义，但需关注回调风险",
        conclusion="综合分析认为比特币突破10万美元是多重因素共同作用的结果，短期内可能继续上涨，但投资者应注意风险管理",
        confidence_score=0.85,
        created_at=datetime.now(),
        metadata={"market_condition": "bullish", "volatility": "high"}
    )
    
    # 执行提纯流程
    purification_workflow = DebatePurificationWorkflow()
    
    try:
        purified_essence = await purification_workflow.complete_debate_purification_cycle(sample_debate)
        
        print("🎉 辩论提纯完成!")
        print(f"原始辩论ID: {purified_essence.original_debate_id}")
        print(f"提纯精华ID: {purified_essence.essence_id}")
        print(f"智慧等级: {purified_essence.wisdom_level}")
        print(f"语义关键词: {', '.join(purified_essence.semantic_keywords)}")
        
    except Exception as e:
        print(f"❌ 辩论提纯失败: {e}")

if __name__ == "__main__":
    asyncio.run(demo_debate_purification())
