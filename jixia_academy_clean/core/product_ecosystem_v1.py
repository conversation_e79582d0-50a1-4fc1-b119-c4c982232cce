#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
炼妖壶产品生态系统 v1.0 - iPhone级别的产品矩阵
构建从免费版到至尊版的完整商业闭环

产品线设计：
1. 免费版-炼妖壶(Cauldron) - 基础AI助手，限制使用次数
2. 高级版-降魔杵(Hammer) - 专业分析工具，无限使用
3. 至尊版-打神鞭(Loom) - 全功能AI投资顾问，个性化服务

商业模式：
- 免费增值模式 (Freemium)
- 订阅制服务 (SaaS)
- 企业级定制 (Enterprise)
- API服务收费 (API-as-a-Service)

作者：太公心易BI系统
版本：v1.0 Product Ecosystem
"""

import json
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Union
from dataclasses import dataclass, asdict
from enum import Enum
import hashlib
import uuid

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class ProductTier(Enum):
    """产品层级"""
    FREE = "免费版-炼妖壶"
    PREMIUM = "高级版-降魔杵"
    ULTIMATE = "至尊版-打神鞭"
    ENTERPRISE = "企业版-九鼎神器"


class SubscriptionStatus(Enum):
    """订阅状态"""
    ACTIVE = "活跃"
    EXPIRED = "过期"
    CANCELLED = "已取消"
    TRIAL = "试用"


class FeatureCategory(Enum):
    """功能类别"""
    AI_ANALYSIS = "AI分析"
    DATA_ACCESS = "数据访问"
    VISUALIZATION = "数据可视化"
    ALERTS = "智能提醒"
    SUPPORT = "客户支持"
    API_ACCESS = "API访问"
    CUSTOMIZATION = "个性化定制"


@dataclass
class ProductFeature:
    """产品功能"""
    name: str
    category: FeatureCategory
    description: str
    free_limit: Optional[int] = None  # 免费版限制
    premium_limit: Optional[int] = None  # 高级版限制
    ultimate_unlimited: bool = True  # 至尊版是否无限制
    enterprise_custom: bool = True  # 企业版是否可定制


@dataclass
class UserSubscription:
    """用户订阅信息"""
    user_id: str
    tier: ProductTier
    status: SubscriptionStatus
    start_date: datetime
    end_date: datetime
    usage_stats: Dict[str, int]
    payment_history: List[Dict]
    
    def is_active(self) -> bool:
        """检查订阅是否有效"""
        return (self.status == SubscriptionStatus.ACTIVE and 
                datetime.now() <= self.end_date)
    
    def days_remaining(self) -> int:
        """剩余天数"""
        if not self.is_active():
            return 0
        return (self.end_date - datetime.now()).days


@dataclass
class PricingPlan:
    """定价方案"""
    tier: ProductTier
    monthly_price: float
    yearly_price: float
    features: List[str]
    limits: Dict[str, int]
    popular: bool = False
    
    def get_yearly_discount(self) -> float:
        """计算年付折扣"""
        if self.yearly_price == 0:
            return 0
        monthly_total = self.monthly_price * 12
        return (monthly_total - self.yearly_price) / monthly_total


class ProductEcosystem:
    """炼妖壶产品生态系统"""
    
    def __init__(self):
        self.features = self._initialize_features()
        self.pricing_plans = self._initialize_pricing()
        self.user_subscriptions = {}
        
        logger.info("🏭 炼妖壶产品生态系统初始化完成")
    
    def _initialize_features(self) -> List[ProductFeature]:
        """初始化产品功能矩阵"""
        return [
            # AI分析功能
            ProductFeature(
                name="智能问答",
                category=FeatureCategory.AI_ANALYSIS,
                description="基础AI投资问答服务",
                free_limit=10,  # 每日10次
                premium_limit=100,  # 每日100次
                ultimate_unlimited=True
            ),
            ProductFeature(
                name="深度分析报告",
                category=FeatureCategory.AI_ANALYSIS,
                description="专业的投资分析报告生成",
                free_limit=0,  # 免费版不提供
                premium_limit=5,  # 每月5份
                ultimate_unlimited=True
            ),
            ProductFeature(
                name="八仙辩论",
                category=FeatureCategory.AI_ANALYSIS,
                description="多智能体投资决策辩论",
                free_limit=0,
                premium_limit=10,  # 每月10次
                ultimate_unlimited=True
            ),
            
            # 数据访问功能
            ProductFeature(
                name="实时市场数据",
                category=FeatureCategory.DATA_ACCESS,
                description="实时股票、基金、期货数据",
                free_limit=50,  # 每日50次查询
                premium_limit=1000,
                ultimate_unlimited=True
            ),
            ProductFeature(
                name="历史数据回测",
                category=FeatureCategory.DATA_ACCESS,
                description="历史数据分析和策略回测",
                free_limit=0,
                premium_limit=20,  # 每月20次
                ultimate_unlimited=True
            ),
            ProductFeature(
                name="新闻情报分析",
                category=FeatureCategory.DATA_ACCESS,
                description="RSS新闻流智能分析",
                free_limit=20,  # 每日20条
                premium_limit=200,
                ultimate_unlimited=True
            ),
            
            # 可视化功能
            ProductFeature(
                name="基础图表",
                category=FeatureCategory.VISUALIZATION,
                description="基本的数据可视化图表",
                free_limit=10,  # 每日10个图表
                premium_limit=100,
                ultimate_unlimited=True
            ),
            ProductFeature(
                name="高级仪表板",
                category=FeatureCategory.VISUALIZATION,
                description="专业的投资仪表板",
                free_limit=0,
                premium_limit=5,  # 5个自定义仪表板
                ultimate_unlimited=True
            ),
            
            # 智能提醒
            ProductFeature(
                name="价格提醒",
                category=FeatureCategory.ALERTS,
                description="股票价格变动提醒",
                free_limit=5,  # 5个提醒
                premium_limit=50,
                ultimate_unlimited=True
            ),
            ProductFeature(
                name="AI智能预警",
                category=FeatureCategory.ALERTS,
                description="基于AI分析的投资预警",
                free_limit=0,
                premium_limit=20,
                ultimate_unlimited=True
            ),
            
            # 客户支持
            ProductFeature(
                name="社区支持",
                category=FeatureCategory.SUPPORT,
                description="用户社区和基础支持",
                free_limit=1,  # 有限支持
                premium_limit=1,
                ultimate_unlimited=True
            ),
            ProductFeature(
                name="专属客服",
                category=FeatureCategory.SUPPORT,
                description="一对一专属客服支持",
                free_limit=0,
                premium_limit=0,
                ultimate_unlimited=True
            ),
            
            # API访问
            ProductFeature(
                name="API调用",
                category=FeatureCategory.API_ACCESS,
                description="程序化API接口调用",
                free_limit=100,  # 每日100次
                premium_limit=10000,
                ultimate_unlimited=True
            ),
            
            # 个性化定制
            ProductFeature(
                name="个性化推荐",
                category=FeatureCategory.CUSTOMIZATION,
                description="基于用户偏好的个性化推荐",
                free_limit=0,
                premium_limit=1,  # 基础个性化
                ultimate_unlimited=True
            ),
            ProductFeature(
                name="专属投资顾问",
                category=FeatureCategory.CUSTOMIZATION,
                description="AI投资顾问个性化服务",
                free_limit=0,
                premium_limit=0,
                ultimate_unlimited=True
            )
        ]
    
    def _initialize_pricing(self) -> List[PricingPlan]:
        """初始化定价方案"""
        return [
            PricingPlan(
                tier=ProductTier.FREE,
                monthly_price=0,
                yearly_price=0,
                features=[
                    "每日10次智能问答",
                    "每日50次市场数据查询",
                    "每日20条新闻分析",
                    "基础图表功能",
                    "5个价格提醒",
                    "社区支持",
                    "每日100次API调用"
                ],
                limits={
                    "daily_questions": 10,
                    "daily_data_queries": 50,
                    "daily_news": 20,
                    "price_alerts": 5,
                    "daily_api_calls": 100
                }
            ),
            PricingPlan(
                tier=ProductTier.PREMIUM,
                monthly_price=99,
                yearly_price=999,  # 年付8.5折
                features=[
                    "每日100次智能问答",
                    "每月5份深度分析报告",
                    "每月10次八仙辩论",
                    "每日1000次市场数据查询",
                    "每月20次历史数据回测",
                    "每日200条新闻分析",
                    "高级仪表板(5个)",
                    "50个价格提醒",
                    "每月20次AI智能预警",
                    "基础个性化推荐",
                    "每日10000次API调用"
                ],
                limits={
                    "daily_questions": 100,
                    "monthly_reports": 5,
                    "monthly_debates": 10,
                    "daily_data_queries": 1000,
                    "monthly_backtests": 20,
                    "daily_news": 200,
                    "dashboards": 5,
                    "price_alerts": 50,
                    "monthly_ai_alerts": 20,
                    "daily_api_calls": 10000
                },
                popular=True
            ),
            PricingPlan(
                tier=ProductTier.ULTIMATE,
                monthly_price=299,
                yearly_price=2999,  # 年付8.3折
                features=[
                    "无限智能问答",
                    "无限深度分析报告",
                    "无限八仙辩论",
                    "无限市场数据访问",
                    "无限历史数据回测",
                    "无限新闻情报分析",
                    "无限高级仪表板",
                    "无限价格提醒",
                    "无限AI智能预警",
                    "专属客服支持",
                    "无限API调用",
                    "高级个性化推荐",
                    "专属AI投资顾问",
                    "优先功能更新"
                ],
                limits={},  # 无限制
            ),
            PricingPlan(
                tier=ProductTier.ENTERPRISE,
                monthly_price=999,
                yearly_price=9999,
                features=[
                    "所有至尊版功能",
                    "企业级数据安全",
                    "私有化部署选项",
                    "定制化功能开发",
                    "专属技术支持",
                    "SLA服务保障",
                    "多用户管理",
                    "企业级API配额",
                    "定制化报告",
                    "专属培训服务"
                ],
                limits={}  # 可定制
            )
        ]
    
    def get_user_tier_features(self, user_id: str) -> Dict[str, Any]:
        """获取用户当前层级的功能权限"""
        subscription = self.user_subscriptions.get(user_id)
        if not subscription or not subscription.is_active():
            tier = ProductTier.FREE
        else:
            tier = subscription.tier
        
        # 获取对应层级的功能限制
        tier_features = {}
        for feature in self.features:
            if tier == ProductTier.FREE:
                limit = feature.free_limit
            elif tier == ProductTier.PREMIUM:
                limit = feature.premium_limit
            elif tier == ProductTier.ULTIMATE:
                limit = None if feature.ultimate_unlimited else feature.premium_limit
            else:  # ENTERPRISE
                limit = None if feature.enterprise_custom else feature.premium_limit
            
            tier_features[feature.name] = {
                "description": feature.description,
                "category": feature.category.value,
                "limit": limit,
                "unlimited": limit is None
            }
        
        return {
            "tier": tier.value,
            "features": tier_features,
            "subscription_info": asdict(subscription) if subscription else None
        }
    
    def check_feature_access(self, user_id: str, feature_name: str, usage_count: int = 1) -> Dict[str, Any]:
        """检查用户是否可以使用某个功能"""
        subscription = self.user_subscriptions.get(user_id)
        if not subscription or not subscription.is_active():
            tier = ProductTier.FREE
            current_usage = 0  # 简化处理
        else:
            tier = subscription.tier
            current_usage = subscription.usage_stats.get(feature_name, 0)
        
        # 查找功能配置
        feature = next((f for f in self.features if f.name == feature_name), None)
        if not feature:
            return {"allowed": False, "reason": "功能不存在"}
        
        # 确定限制
        if tier == ProductTier.FREE:
            limit = feature.free_limit
        elif tier == ProductTier.PREMIUM:
            limit = feature.premium_limit
        elif tier == ProductTier.ULTIMATE:
            limit = None if feature.ultimate_unlimited else feature.premium_limit
        else:  # ENTERPRISE
            limit = None if feature.enterprise_custom else feature.premium_limit
        
        # 检查访问权限
        if limit is None:  # 无限制
            return {
                "allowed": True,
                "remaining": "无限",
                "tier": tier.value
            }
        elif limit == 0:  # 不允许使用
            return {
                "allowed": False,
                "reason": f"该功能需要升级到更高版本",
                "upgrade_suggestion": self._get_upgrade_suggestion(tier)
            }
        elif current_usage + usage_count <= limit:  # 在限制内
            return {
                "allowed": True,
                "remaining": limit - current_usage - usage_count,
                "tier": tier.value
            }
        else:  # 超出限制
            return {
                "allowed": False,
                "reason": f"已达到{tier.value}的使用限制({limit}次)",
                "upgrade_suggestion": self._get_upgrade_suggestion(tier)
            }
    
    def _get_upgrade_suggestion(self, current_tier: ProductTier) -> Dict[str, Any]:
        """获取升级建议"""
        if current_tier == ProductTier.FREE:
            suggested_tier = ProductTier.PREMIUM
        elif current_tier == ProductTier.PREMIUM:
            suggested_tier = ProductTier.ULTIMATE
        else:
            return {"message": "您已是最高级用户"}
        
        suggested_plan = next(p for p in self.pricing_plans if p.tier == suggested_tier)
        
        return {
            "suggested_tier": suggested_tier.value,
            "monthly_price": suggested_plan.monthly_price,
            "yearly_price": suggested_plan.yearly_price,
            "key_benefits": suggested_plan.features[:5],  # 前5个主要功能
            "discount": f"{suggested_plan.get_yearly_discount():.1%}" if suggested_plan.get_yearly_discount() > 0 else "0%"
        }
    
    def create_subscription(self, user_id: str, tier: ProductTier, duration_months: int = 1) -> UserSubscription:
        """创建用户订阅"""
        start_date = datetime.now()
        end_date = start_date + timedelta(days=duration_months * 30)
        
        subscription = UserSubscription(
            user_id=user_id,
            tier=tier,
            status=SubscriptionStatus.ACTIVE,
            start_date=start_date,
            end_date=end_date,
            usage_stats={},
            payment_history=[]
        )
        
        self.user_subscriptions[user_id] = subscription
        logger.info(f"✅ 用户 {user_id} 订阅 {tier.value} 成功")
        
        return subscription
    
    def get_pricing_comparison(self) -> List[Dict[str, Any]]:
        """获取定价对比表"""
        comparison = []
        
        for plan in self.pricing_plans:
            plan_info = {
                "tier": plan.tier.value,
                "monthly_price": plan.monthly_price,
                "yearly_price": plan.yearly_price,
                "yearly_discount": f"{plan.get_yearly_discount():.1%}",
                "features": plan.features,
                "popular": plan.popular,
                "limits": plan.limits
            }
            comparison.append(plan_info)
        
        return comparison
    
    def get_ecosystem_stats(self) -> Dict[str, Any]:
        """获取生态系统统计信息"""
        total_users = len(self.user_subscriptions)
        active_subscriptions = sum(1 for s in self.user_subscriptions.values() if s.is_active())
        
        tier_distribution = {}
        for subscription in self.user_subscriptions.values():
            if subscription.is_active():
                tier = subscription.tier.value
                tier_distribution[tier] = tier_distribution.get(tier, 0) + 1
        
        return {
            "total_users": total_users,
            "active_subscriptions": active_subscriptions,
            "tier_distribution": tier_distribution,
            "total_features": len(self.features),
            "pricing_plans": len(self.pricing_plans)
        }


def main():
    """演示产品生态系统"""
    print("🏭 炼妖壶产品生态系统演示")
    print("=" * 50)
    
    ecosystem = ProductEcosystem()
    
    # 显示定价对比
    print("\n💰 定价方案对比:")
    for plan in ecosystem.get_pricing_comparison():
        print(f"\n{plan['tier']}:")
        print(f"  月费: ¥{plan['monthly_price']}")
        print(f"  年费: ¥{plan['yearly_price']} (优惠{plan['yearly_discount']})")
        print(f"  主要功能: {', '.join(plan['features'][:3])}")
    
    # 创建测试用户
    user_id = "test_user_001"
    ecosystem.create_subscription(user_id, ProductTier.PREMIUM, 12)
    
    # 检查功能访问
    print(f"\n🔍 用户功能权限检查:")
    features_to_check = ["智能问答", "深度分析报告", "八仙辩论"]
    
    for feature in features_to_check:
        access = ecosystem.check_feature_access(user_id, feature)
        print(f"  {feature}: {'✅ 允许' if access['allowed'] else '❌ 拒绝'}")
        if access['allowed']:
            print(f"    剩余次数: {access.get('remaining', '无限')}")
        else:
            print(f"    原因: {access.get('reason', '未知')}")
    
    # 显示生态系统统计
    print(f"\n📊 生态系统统计:")
    stats = ecosystem.get_ecosystem_stats()
    for key, value in stats.items():
        print(f"  {key}: {value}")


if __name__ == "__main__":
    main()
