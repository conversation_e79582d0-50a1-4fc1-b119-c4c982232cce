# -*- coding: utf-8 -*-
"""
N8N + MCP集成模块
通过N8N工作流自动触发太公心易MCP服务器，实现事件驱动的智能分析
"""

import os
import json
import asyncio
import logging
from datetime import datetime
from typing import Dict, List, Any, Optional
from flask import Flask, request, jsonify
import requests
from free_crypto_newsflow import FreeCryptoNewsFlow

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class N8NMCPBridge:
    """N8N与MCP服务器的桥接器"""
    
    def __init__(self, port: int = 5000):
        self.app = Flask(__name__)
        self.port = port
        self.crypto_newsflow = FreeCryptoNewsFlow()
        
        # N8N webhook配置
        self.n8n_webhooks = {
            'market_alert': '/webhook/market-alert',
            'news_trigger': '/webhook/news-trigger', 
            'analysis_request': '/webhook/analysis-request'
        }
        
        # 设置路由
        self._setup_routes()
    
    def _setup_routes(self):
        """设置Flask路由"""
        
        @self.app.route('/health', methods=['GET'])
        def health_check():
            """健康检查"""
            return jsonify({
                'status': 'healthy',
                'timestamp': datetime.now().isoformat(),
                'service': 'N8N-MCP Bridge'
            })
        
        @self.app.route('/webhook/market-alert', methods=['POST'])
        def market_alert_webhook():
            """市场预警webhook"""
            try:
                data = request.get_json()
                logger.info(f"📢 收到N8N市场预警: {data}")
                
                # 处理市场预警
                result = asyncio.run(self._handle_market_alert(data))
                
                return jsonify({
                    'success': True,
                    'message': '市场预警处理完成',
                    'result': result
                })
                
            except Exception as e:
                logger.error(f"❌ 处理市场预警失败: {e}")
                return jsonify({
                    'success': False,
                    'error': str(e)
                }), 500
        
        @self.app.route('/webhook/news-trigger', methods=['POST'])
        def news_trigger_webhook():
            """新闻触发webhook"""
            try:
                data = request.get_json()
                logger.info(f"📰 收到N8N新闻触发: {data}")
                
                # 处理新闻触发
                result = asyncio.run(self._handle_news_trigger(data))
                
                return jsonify({
                    'success': True,
                    'message': '新闻触发处理完成',
                    'result': result
                })
                
            except Exception as e:
                logger.error(f"❌ 处理新闻触发失败: {e}")
                return jsonify({
                    'success': False,
                    'error': str(e)
                }), 500
        
        @self.app.route('/webhook/analysis-request', methods=['POST'])
        def analysis_request_webhook():
            """分析请求webhook"""
            try:
                data = request.get_json()
                logger.info(f"🔮 收到N8N分析请求: {data}")
                
                # 处理分析请求
                result = asyncio.run(self._handle_analysis_request(data))
                
                return jsonify({
                    'success': True,
                    'message': '分析请求处理完成',
                    'result': result
                })
                
            except Exception as e:
                logger.error(f"❌ 处理分析请求失败: {e}")
                return jsonify({
                    'success': False,
                    'error': str(e)
                }), 500
        
        @self.app.route('/mcp/crypto-data', methods=['GET'])
        def get_crypto_data():
            """获取加密货币数据的MCP端点"""
            try:
                # 获取免费加密货币数据
                result = asyncio.run(self.crypto_newsflow.generate_free_newsflow_report())
                
                return jsonify({
                    'success': True,
                    'data': result,
                    'timestamp': datetime.now().isoformat()
                })
                
            except Exception as e:
                logger.error(f"❌ 获取加密货币数据失败: {e}")
                return jsonify({
                    'success': False,
                    'error': str(e)
                }), 500
        
        @self.app.route('/mcp/trigger-jixia-debate', methods=['POST'])
        def trigger_jixia_debate():
            """触发稷下学宫辩论的MCP端点"""
            try:
                data = request.get_json()
                topic = data.get('topic', '市场分析')
                context = data.get('context', {})
                
                # 模拟稷下学宫辩论
                debate_result = self._simulate_jixia_debate(topic, context)
                
                return jsonify({
                    'success': True,
                    'debate_result': debate_result,
                    'timestamp': datetime.now().isoformat()
                })
                
            except Exception as e:
                logger.error(f"❌ 触发稷下学宫辩论失败: {e}")
                return jsonify({
                    'success': False,
                    'error': str(e)
                }), 500
    
    async def _handle_market_alert(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """处理市场预警"""
        try:
            alert_type = data.get('type', 'unknown')
            symbol = data.get('symbol', '')
            change = data.get('change', 0)
            
            logger.info(f"🚨 处理市场预警: {alert_type} - {symbol} - {change}%")
            
            # 获取相关加密货币数据
            crypto_report = await self.crypto_newsflow.generate_free_newsflow_report()
            
            # 生成预警响应
            response = {
                'alert_type': alert_type,
                'symbol': symbol,
                'change_percent': change,
                'analysis_time': datetime.now().isoformat(),
                'crypto_context': crypto_report.get('summary', ''),
                'recommendation': self._generate_alert_recommendation(alert_type, change),
                'next_actions': [
                    '监控相关资产价格变化',
                    '分析市场情绪变化',
                    '评估风险敞口'
                ]
            }
            
            # 如果变化幅度大，触发稷下学宫辩论
            if abs(change) > 10:
                debate_topic = f"{symbol}价格异动{change:+.1f}%的市场影响分析"
                debate_result = self._simulate_jixia_debate(debate_topic, data)
                response['jixia_debate'] = debate_result
            
            return response
            
        except Exception as e:
            logger.error(f"❌ 处理市场预警失败: {e}")
            return {'error': str(e)}
    
    async def _handle_news_trigger(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """处理新闻触发"""
        try:
            news_title = data.get('title', '')
            news_content = data.get('content', '')
            source = data.get('source', '')
            
            logger.info(f"📰 处理新闻触发: {news_title[:50]}...")
            
            # 分析新闻重要性
            importance = self._analyze_news_importance(news_title, news_content)
            
            # 获取相关市场数据
            market_data = await self.crypto_newsflow.get_free_market_data()
            
            response = {
                'news_title': news_title,
                'source': source,
                'importance_level': importance,
                'analysis_time': datetime.now().isoformat(),
                'market_context': self._extract_market_context(market_data),
                'potential_impact': self._assess_news_impact(news_title, news_content)
            }
            
            # 如果新闻重要性高，触发深度分析
            if importance == 'high':
                debate_topic = f"重大新闻事件分析: {news_title}"
                debate_result = self._simulate_jixia_debate(debate_topic, {
                    'news': news_content,
                    'market_data': market_data
                })
                response['jixia_debate'] = debate_result
            
            return response
            
        except Exception as e:
            logger.error(f"❌ 处理新闻触发失败: {e}")
            return {'error': str(e)}
    
    async def _handle_analysis_request(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """处理分析请求"""
        try:
            query = data.get('query', '市场分析')
            analysis_type = data.get('type', 'general')
            
            logger.info(f"🔮 处理分析请求: {query}")
            
            # 获取综合数据
            crypto_report = await self.crypto_newsflow.generate_free_newsflow_report()
            
            # 执行太公心易分析（简化版）
            xinyi_analysis = self._perform_simplified_xinyi_analysis(query, crypto_report)
            
            response = {
                'query': query,
                'analysis_type': analysis_type,
                'analysis_time': datetime.now().isoformat(),
                'xinyi_analysis': xinyi_analysis,
                'market_summary': crypto_report.get('summary', ''),
                'data_sources': crypto_report.get('data_sources', []),
                'confidence_level': 0.75  # 简化的置信度
            }
            
            return response
            
        except Exception as e:
            logger.error(f"❌ 处理分析请求失败: {e}")
            return {'error': str(e)}
    
    def _simulate_jixia_debate(self, topic: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """模拟稷下学宫辩论"""
        
        # 八仙过海辩论模拟
        debate_participants = [
            '铁拐李', '汉钟离', '张果老', '蓝采和',
            '何仙姑', '吕洞宾', '韩湘子', '曹国舅'
        ]
        
        # 生成辩论观点
        debate_views = []
        for participant in debate_participants[:4]:  # 简化为4个观点
            view = self._generate_debate_view(participant, topic, context)
            debate_views.append({
                'participant': participant,
                'viewpoint': view,
                'stance': 'positive' if len(debate_views) % 2 == 0 else 'negative'
            })
        
        # 三清论道总结
        sanqing_summary = self._generate_sanqing_summary(topic, debate_views)
        
        return {
            'topic': topic,
            'debate_time': datetime.now().isoformat(),
            'participants': debate_participants[:4],
            'debate_views': debate_views,
            'sanqing_summary': sanqing_summary,
            'conclusion': self._generate_debate_conclusion(debate_views)
        }
    
    def _generate_debate_view(self, participant: str, topic: str, context: Dict[str, Any]) -> str:
        """生成辩论观点"""
        participant_styles = {
            '铁拐李': '从技术分析角度',
            '汉钟离': '从宏观经济角度', 
            '张果老': '从历史经验角度',
            '蓝采和': '从市场情绪角度',
            '何仙姑': '从风险管理角度',
            '吕洞宾': '从价值投资角度',
            '韩湘子': '从量化分析角度',
            '曹国舅': '从政策影响角度'
        }
        
        style = participant_styles.get(participant, '从综合角度')
        return f"{style}，{participant}认为{topic}需要谨慎分析市场变化，建议关注相关风险因素。"
    
    def _generate_sanqing_summary(self, topic: str, debate_views: List[Dict[str, Any]]) -> str:
        """生成三清论道总结"""
        positive_views = [v for v in debate_views if v['stance'] == 'positive']
        negative_views = [v for v in debate_views if v['stance'] == 'negative']
        
        return f"三清论道总结：关于{topic}，正方观点{len(positive_views)}个，反方观点{len(negative_views)}个。综合分析认为需要平衡考虑各方因素，建议采取稳健策略。"
    
    def _generate_debate_conclusion(self, debate_views: List[Dict[str, Any]]) -> str:
        """生成辩论结论"""
        return "经过八仙辩论和三清论道，建议投资者保持理性，根据个人风险承受能力做出决策。"
    
    def _analyze_news_importance(self, title: str, content: str) -> str:
        """分析新闻重要性"""
        high_keywords = ['央行', '利率', '政策', '监管', '危机', '暴跌', '暴涨']
        medium_keywords = ['财报', '业绩', '合并', '收购', 'IPO']
        
        text = (title + ' ' + content).lower()
        
        if any(keyword in text for keyword in high_keywords):
            return 'high'
        elif any(keyword in text for keyword in medium_keywords):
            return 'medium'
        else:
            return 'low'
    
    def _extract_market_context(self, market_data: Dict[str, Any]) -> str:
        """提取市场上下文"""
        if not market_data:
            return "市场数据暂时不可用"
        
        sources = list(market_data.keys())
        return f"当前市场数据来源: {', '.join(sources)}，整体市场相对稳定。"
    
    def _assess_news_impact(self, title: str, content: str) -> str:
        """评估新闻影响"""
        return "该新闻可能对相关资产产生短期影响，建议密切关注后续发展。"
    
    def _perform_simplified_xinyi_analysis(self, query: str, crypto_report: Dict[str, Any]) -> Dict[str, Any]:
        """执行简化的太公心易分析"""
        return {
            'taiyi_guan_lan': '基于当前市场数据，趋势相对稳定',
            'dunja_ze_shi': '建议在市场波动较小时适度参与',
            'liuren_cha_xin': '市场情绪整体中性，无明显恐慌或贪婪',
            'overall_recommendation': '建议保持观望，等待更明确的信号'
        }
    
    def _generate_alert_recommendation(self, alert_type: str, change: float) -> str:
        """生成预警建议"""
        if abs(change) > 15:
            return "重大价格变动，建议立即评估风险敞口"
        elif abs(change) > 10:
            return "显著价格变动，建议密切监控"
        else:
            return "正常价格波动，保持关注即可"
    
    def run(self):
        """运行N8N-MCP桥接服务"""
        logger.info(f"🚀 启动N8N-MCP桥接服务，端口: {self.port}")
        logger.info(f"📡 Webhook端点:")
        for name, path in self.n8n_webhooks.items():
            logger.info(f"  - {name}: http://localhost:{self.port}{path}")
        
        self.app.run(host='0.0.0.0', port=self.port, debug=False)


# N8N工作流配置示例
def generate_n8n_workflow_config():
    """生成N8N工作流配置示例"""
    workflow_config = {
        "name": "太公心易智能分析工作流",
        "nodes": [
            {
                "name": "定时触发器",
                "type": "n8n-nodes-base.cron",
                "parameters": {
                    "rule": {
                        "interval": [{"field": "minute", "value": 30}]
                    }
                }
            },
            {
                "name": "获取加密货币数据",
                "type": "n8n-nodes-base.httpRequest",
                "parameters": {
                    "url": "http://localhost:5000/mcp/crypto-data",
                    "method": "GET"
                }
            },
            {
                "name": "判断是否需要预警",
                "type": "n8n-nodes-base.if",
                "parameters": {
                    "conditions": {
                        "number": [
                            {
                                "value1": "={{$json.data.market_events_count}}",
                                "operation": "larger",
                                "value2": 0
                            }
                        ]
                    }
                }
            },
            {
                "name": "触发稷下学宫辩论",
                "type": "n8n-nodes-base.httpRequest",
                "parameters": {
                    "url": "http://localhost:5000/mcp/trigger-jixia-debate",
                    "method": "POST",
                    "body": {
                        "topic": "市场异动分析",
                        "context": "={{$json}}"
                    }
                }
            },
            {
                "name": "发送通知",
                "type": "n8n-nodes-base.emailSend",
                "parameters": {
                    "subject": "太公心易市场分析报告",
                    "text": "={{$json.debate_result.conclusion}}"
                }
            }
        ]
    }
    
    return workflow_config


# 使用示例
if __name__ == "__main__":
    # 启动N8N-MCP桥接服务
    bridge = N8NMCPBridge(port=5000)
    
    print("🎯 N8N-MCP集成服务配置:")
    print("1. 启动服务: python n8n_mcp_integration.py")
    print("2. 在N8N中配置webhook指向: http://localhost:5000/webhook/*")
    print("3. 设置定时任务调用MCP端点")
    print("4. 配置通知渠道接收分析结果")
    
    # 生成N8N工作流配置
    workflow_config = generate_n8n_workflow_config()
    with open('n8n_workflow_config.json', 'w', encoding='utf-8') as f:
        json.dump(workflow_config, f, ensure_ascii=False, indent=2)
    
    print("✅ N8N工作流配置已生成: n8n_workflow_config.json")
    
    # 启动服务
    bridge.run()
