#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
稷下学宫辩论控制器
统一管理三种输出模式：Console、Streamlit、Mastodon
"""

import asyncio
import logging
from enum import Enum
from typing import Dict, List, Any, Optional
import autogen
import streamlit as st
from mastodon import Mastodon

logger = logging.getLogger(__name__)

class DebateOutputMode(Enum):
    """辩论输出模式"""
    CONSOLE = "console"      # 终端输出
    STREAMLIT = "streamlit"  # 网页展示
    MASTODON = "mastodon"    # 社交发布
    ALL = "all"              # 全部模式

class JixiaDebateController:
    """稷下学宫辩论控制器"""
    
    def __init__(self):
        self.agents = self.setup_agents()
        self.mastodon_clients = self.setup_mastodon_clients()
        
    def setup_agents(self) -> Dict[str, autogen.AssistantAgent]:
        """设置AutoGen代理"""
        
        config_list = [{
            "model": "gpt-4",
            "api_key": os.getenv("OPENAI_API_KEY")
        }]
        
        # 三清
        taishang_laojun = autogen.AssistantAgent(
            name="太上老君",
            system_message="""
            你是太上老君，稷下学宫的主持人和宏观经济大师。
            你的职责是主持辩论、维持秩序、提供宏观分析、综合各方观点。
            """,
            llm_config={"config_list": config_list}
        )
        
        lu_dongbin = autogen.AssistantAgent(
            name="吕洞宾",
            system_message="""
            你是吕洞宾，价值投资专家。
            特点：坚持长期价值投资理念，以剑仙之名发誓的口头禅，注重基本面分析。
            """,
            llm_config={"config_list": config_list}
        )
        
        zhang_guolao = autogen.AssistantAgent(
            name="张果老", 
            system_message="""
            你是张果老，量化交易大师。
            特点：倒骑驴看市场的独特视角，依赖数据和模型分析，技术分析专家。
            """,
            llm_config={"config_list": config_list}
        )
        
        return {
            "taishang_laojun": taishang_laojun,
            "lu_dongbin": lu_dongbin,
            "zhang_guolao": zhang_guolao
        }
    
    def setup_mastodon_clients(self) -> Dict[str, Mastodon]:
        """设置Mastodon客户端"""
        clients = {}
        
        for agent_name in self.agents.keys():
            try:
                client = Mastodon(
                    client_id=os.getenv(f'MASTODON_{agent_name.upper()}_CLIENT_ID'),
                    client_secret=os.getenv(f'MASTODON_{agent_name.upper()}_CLIENT_SECRET'),
                    access_token=os.getenv(f'MASTODON_{agent_name.upper()}_ACCESS_TOKEN'),
                    api_base_url=os.getenv('MASTODON_INSTANCE_URL')
                )
                clients[agent_name] = client
            except Exception as e:
                logger.warning(f"无法创建{agent_name}的Mastodon客户端: {e}")
        
        return clients
    
    async def start_debate(self, topic: str, trigger_data: Dict[str, Any], 
                          output_mode: DebateOutputMode = DebateOutputMode.CONSOLE):
        """启动稷下学宫辩论"""
        
        logger.info(f"🎭 稷下学宫辩论开始: {topic}")
        logger.info(f"📺 输出模式: {output_mode.value}")
        
        # 创建群聊
        groupchat = autogen.GroupChat(
            agents=list(self.agents.values()),
            messages=[],
            max_round=8
        )
        
        manager = autogen.GroupChatManager(groupchat=groupchat)
        
        # 构建初始消息
        initial_message = f"""
🎭 稷下学宫紧急会议

📝 辩题: {topic}
📊 触发数据: {trigger_data}

各位仙友，请基于你们的专业领域发表观点。
        """.strip()
        
        # 根据输出模式选择处理方式
        if output_mode == DebateOutputMode.CONSOLE:
            return await self.debate_to_console(manager, initial_message)
            
        elif output_mode == DebateOutputMode.STREAMLIT:
            return await self.debate_to_streamlit(manager, initial_message)
            
        elif output_mode == DebateOutputMode.MASTODON:
            return await self.debate_to_mastodon(manager, initial_message, topic)
            
        elif output_mode == DebateOutputMode.ALL:
            # 先在console进行辩论
            debate_result = await self.debate_to_console(manager, initial_message)
            
            # 然后发布到其他平台
            await self.post_result_to_streamlit(debate_result)
            await self.post_result_to_mastodon(debate_result, topic)
            
            return debate_result
    
    async def debate_to_console(self, manager, initial_message):
        """模式1: Console辩论（开发调试）"""
        print("🖥️ 稷下学宫Console辩论模式")
        print("=" * 50)
        
        # 启动AutoGen对话
        chat_result = await manager.a_initiate_chat(
            self.agents["taishang_laojun"],
            message=initial_message
        )
        
        print("=" * 50)
        print("✅ Console辩论完成")
        
        return self.extract_debate_result(chat_result)
    
    async def debate_to_streamlit(self, manager, initial_message):
        """模式2: Streamlit辩论（网页展示）"""
        
        if 'st' not in globals():
            logger.error("Streamlit环境未初始化")
            return None
        
        st.title("🎭 稷下学宫实时辩论")
        
        # 创建消息容器
        messages_container = st.container()
        status_placeholder = st.empty()
        
        with status_placeholder:
            st.info("🎭 仙人们正在激烈辩论中...")
        
        # 启动辩论（这里需要实现流式输出）
        chat_result = await manager.a_initiate_chat(
            self.agents["taishang_laojun"],
            message=initial_message
        )
        
        # 显示辩论结果
        with messages_container:
            for message in chat_result.chat_history:
                agent_name = message.get('name', 'System')
                content = message.get('content', '')
                
                st.chat_message(agent_name).write(content)
        
        with status_placeholder:
            st.success("✅ 辩论完成！")
        
        return self.extract_debate_result(chat_result)
    
    async def debate_to_mastodon(self, manager, initial_message, topic):
        """模式3: Mastodon发布（社交传播）"""
        print("🐘 稷下学宫Mastodon发布模式")
        
        # 先进行内部辩论
        chat_result = await manager.a_initiate_chat(
            self.agents["taishang_laojun"],
            message=initial_message
        )
        
        debate_result = self.extract_debate_result(chat_result)
        
        # 发布到Mastodon
        await self.post_result_to_mastodon(debate_result, topic)
        
        return debate_result
    
    async def post_result_to_mastodon(self, debate_result, topic):
        """发布辩论结果到Mastodon"""
        
        # 太上老君发布总结
        if "taishang_laojun" in self.mastodon_clients:
            summary_post = f"""
🎭 稷下学宫最新辩论

📝 话题: {topic}
🎯 结论: {debate_result.get('conclusion', '仙人们观点各异')}

#稷下学宫 #太公心易 #AI辩论
            """.strip()
            
            try:
                self.mastodon_clients["taishang_laojun"].status_post(summary_post)
                print("✅ 太上老君总结已发布")
            except Exception as e:
                logger.error(f"发布总结失败: {e}")
        
        # 各仙人发布个人观点
        for agent_name, view in debate_result.get('individual_views', {}).items():
            if agent_name in self.mastodon_clients:
                try:
                    personal_post = self.format_agent_post(agent_name, view, topic)
                    self.mastodon_clients[agent_name].status_post(personal_post)
                    print(f"✅ {agent_name}观点已发布")
                    
                    # 间隔发布，避免刷屏
                    await asyncio.sleep(30)
                    
                except Exception as e:
                    logger.error(f"{agent_name}发布失败: {e}")
    
    def format_agent_post(self, agent_name: str, view: str, topic: str) -> str:
        """格式化Agent的Mastodon帖子"""
        
        templates = {
            "taishang_laojun": "🧙‍♂️ 太上老君观点：{view}\n\n#宏观经济 #稷下学宫",
            "lu_dongbin": "⚔️ 以剑仙之名发誓，{view}\n\n#价值投资 #稷下学宫",
            "zhang_guolao": "🐴 倒骑驴看市场，{view}\n\n#量化分析 #稷下学宫"
        }
        
        template = templates.get(agent_name, "{view}\n\n#稷下学宫")
        return template.format(view=view)
    
    def extract_debate_result(self, chat_result) -> Dict[str, Any]:
        """提取辩论结果"""
        
        # 这里需要根据实际的chat_result结构来提取信息
        messages = getattr(chat_result, 'chat_history', [])
        
        individual_views = {}
        conclusion = "辩论已完成"
        
        # 提取各Agent的观点
        for message in messages:
            agent_name = message.get('name', '')
            content = message.get('content', '')
            
            if agent_name in self.agents:
                individual_views[agent_name] = content
        
        return {
            'topic': '市场分析',
            'conclusion': conclusion,
            'individual_views': individual_views,
            'chat_history': messages
        }
    
    async def post_result_to_streamlit(self, debate_result):
        """将结果展示到Streamlit（如果在Streamlit环境中）"""
        if 'st' in globals():
            st.success("🎉 辩论结果已生成！")
            st.json(debate_result)

# 使用示例
async def demo_debate_modes():
    """演示三种辩论模式"""
    
    controller = JixiaDebateController()
    
    topic = "美联储加息对市场的影响"
    trigger_data = {
        "impact_score": 8.5,
        "sentiment": -0.3,
        "source": "Fed News"
    }
    
    # 模式1: Console辩论
    print("🖥️ 测试Console模式...")
    result1 = await controller.start_debate(topic, trigger_data, DebateOutputMode.CONSOLE)
    
    # 模式2: Mastodon发布
    print("🐘 测试Mastodon模式...")
    result2 = await controller.start_debate(topic, trigger_data, DebateOutputMode.MASTODON)
    
    # 模式3: 全部模式
    print("🌟 测试全部模式...")
    result3 = await controller.start_debate(topic, trigger_data, DebateOutputMode.ALL)

if __name__ == "__main__":
    asyncio.run(demo_debate_modes())