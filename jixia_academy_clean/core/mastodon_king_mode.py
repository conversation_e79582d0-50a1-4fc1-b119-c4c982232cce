#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
稷下学宫Mastodon国王模式
在拉斯阿拉莫斯rock！无限制创建AI仙人军团！
"""

import asyncio
import json
import os
import requests
from typing import Dict, List, Any
from mastodon import Mastodon
from dotenv import load_dotenv

class MastodonKingMode:
    """国王模式：无限制创建和管理AI仙人"""
    
    def __init__(self):
        load_dotenv()
        self.instance_url = os.getenv('MASTODON_INSTANCE_URL')
        self.admin_token = os.getenv('MASTODON_ADMIN_TOKEN')  # 您的管理员token
        
        # 十一仙配置
        self.immortals = {
            # 三清
            "taishang_laojun": {
                "display_name": "太上老君",
                "username": "taishang_laojun", 
                "email": "<EMAIL>",
                "bio": "🧙‍♂️ 太上老君，稷下学宫宗师\n📊 宏观经济大师，深谋远虑\n🎭 在拉斯阿拉莫斯rock！\n#宏观经济 #稷下学宫",
                "avatar": "🧙‍♂️",
                "personality": "权威稳重，善于总结大局"
            },
            "yuanshi_tianzun": {
                "display_name": "元始天尊",
                "username": "yuanshi_tianzun",
                "email": "<EMAIL>", 
                "bio": "⚡ 元始天尊，技术分析宗师\n📈 图表和数据的终极解读者\n🎭 在拉斯阿拉莫斯rock！\n#技术分析 #稷下学宫",
                "avatar": "⚡",
                "personality": "精准理性，技术至上"
            },
            "tongtian_jiaozhu": {
                "display_name": "通天教主",
                "username": "tongtian_jiaozhu",
                "email": "<EMAIL>",
                "bio": "🔮 通天教主，市场情绪导师\n💭 洞察人心，预测情绪波动\n🎭 在拉斯阿拉莫斯rock！\n#市场情绪 #稷下学宫",
                "avatar": "🔮", 
                "personality": "神秘莫测，善读人心"
            },
            
            # 八仙
            "lu_dongbin": {
                "display_name": "吕洞宾",
                "username": "lu_dongbin",
                "email": "<EMAIL>",
                "bio": "⚔️ 吕洞宾，价值投资剑仙\n💎 以剑仙之名发誓，坚持长期价值\n🎭 在拉斯阿拉莫斯rock！\n#价值投资 #稷下学宫",
                "avatar": "⚔️",
                "personality": "坚定执着，剑仙风范"
            },
            "he_xiangu": {
                "display_name": "何仙姑", 
                "username": "he_xiangu",
                "email": "<EMAIL>",
                "bio": "🌸 何仙姑，ESG投资仙子\n🌱 关注可持续发展和社会责任\n🎭 在拉斯阿拉莫斯rock！\n#ESG投资 #稷下学宫",
                "avatar": "🌸",
                "personality": "温柔坚定，关注社会价值"
            },
            "zhang_guolao": {
                "display_name": "张果老",
                "username": "zhang_guolao", 
                "email": "<EMAIL>",
                "bio": "🐴 张果老，量化交易大师\n📊 倒骑驴看市场，数据不会说谎\n🎭 在拉斯阿拉莫斯rock！\n#量化交易 #稷下学宫",
                "avatar": "🐴",
                "personality": "独特视角，数据驱动"
            },
            "han_xiangzi": {
                "display_name": "韩湘子",
                "username": "han_xiangzi",
                "email": "<EMAIL>", 
                "bio": "🎵 韩湘子，加密货币音律师\n₿ DeFi和Web3的先锋探索者\n🎭 在拉斯阿拉莫斯rock！\n#加密货币 #稷下学宫",
                "avatar": "🎵",
                "personality": "前卫创新，拥抱新技术"
            },
            "tiegua_li": {
                "display_name": "铁拐李",
                "username": "tiegua_li",
                "email": "<EMAIL>",
                "bio": "🦴 铁拐李，风险管理铁人\n⚖️ 风险控制是投资的生命线\n🎭 在拉斯阿拉莫斯rock！\n#风险管理 #稷下学宫", 
                "avatar": "🦴",
                "personality": "谨慎务实，风险意识强"
            },
            "cao_guojiu": {
                "display_name": "曹国舅",
                "username": "cao_guojiu",
                "email": "<EMAIL>",
                "bio": "👑 曹国舅，政策解读国师\n📜 熟悉监管环境和政策变化\n🎭 在拉斯阿拉莫斯rock！\n#政策分析 #稷下学宫",
                "avatar": "👑", 
                "personality": "政治敏锐，善解政策"
            },
            "lan_caihe": {
                "display_name": "蓝采和",
                "username": "lan_caihe",
                "email": "<EMAIL>",
                "bio": "🎭 蓝采和，新兴市场游吟诗人\n🌟 发现成长机会和创新投资\n🎭 在拉斯阿拉莫斯rock！\n#新兴市场 #稷下学宫",
                "avatar": "🎭",
                "personality": "活泼机敏，善于发现机会"
            },
            "zhong_hanli": {
                "display_name": "钟汉离",
                "username": "zhong_hanli", 
                "email": "<EMAIL>",
                "bio": "⚗️ 钟汉离，技术创新炼金师\n🔬 观察科技创新和颠覆性技术\n🎭 在拉斯阿拉莫斯rock！\n#技术创新 #稷下学宫",
                "avatar": "⚗️",
                "personality": "前瞻性思维，技术敏感"
            }
        }
    
    async def create_immortal_army(self):
        """创建仙人军团 - 国王模式！"""
        print("👑 国王模式启动！在拉斯阿拉莫斯创建AI仙人军团！")
        print("🚀 目标：11个AI仙人账户")
        print("💥 让我们rock起来！\n")
        
        created_accounts = []
        
        for immortal_id, config in self.immortals.items():
            try:
                print(f"⚡ 正在创建 {config['display_name']}...")
                
                # 国王权限：直接创建账户
                account_info = await self.create_account_king_mode(config)
                
                if account_info:
                    # 设置个人资料
                    await self.setup_immortal_profile(account_info, config)
                    
                    # 发布入驻宣言
                    await self.post_arrival_announcement(account_info, config)
                    
                    created_accounts.append(account_info)
                    print(f"✅ {config['display_name']} 创建成功！")
                else:
                    print(f"❌ {config['display_name']} 创建失败")
                
                # 间隔一下，避免过于激进
                await asyncio.sleep(1)
                
            except Exception as e:
                print(f"❌ 创建 {config['display_name']} 时出错: {e}")
        
        print(f"\n🎉 仙人军团创建完成！")
        print(f"✅ 成功创建 {len(created_accounts)} 个AI仙人账户")
        
        # 让仙人们互相关注
        await self.immortals_follow_each_other(created_accounts)
        
        # 发布稷下学宫开宗立派宣言
        await self.post_academy_manifesto(created_accounts)
        
        return created_accounts
    
    async def create_account_king_mode(self, config: Dict[str, str]) -> Dict[str, Any]:
        """国王模式：直接创建账户"""
        
        # 方法1: 如果您有管理员API访问权限
        if self.admin_token:
            return await self.create_via_admin_api(config)
        
        # 方法2: 直接数据库操作（最强国王模式）
        return await self.create_via_database(config)
        
        # 方法3: 命令行工具
        return await self.create_via_cli(config)
    
    async def create_via_admin_api(self, config: Dict[str, str]) -> Dict[str, Any]:
        """通过管理员API创建账户"""
        try:
            # Mastodon管理员API端点
            admin_url = f"{self.instance_url}/api/v1/admin/accounts"
            
            headers = {
                'Authorization': f'Bearer {self.admin_token}',
                'Content-Type': 'application/json'
            }
            
            account_data = {
                'username': config['username'],
                'email': config['email'],
                'password': os.getenv('IMMORTAL_DEFAULT_PASSWORD', 'JixiaAcademy2024!'),
                'agreement': True,
                'locale': 'zh-CN'
            }
            
            response = requests.post(admin_url, headers=headers, json=account_data)
            
            if response.status_code == 200:
                account_info = response.json()
                
                # 立即激活账户（国王权限）
                await self.activate_account(account_info['id'])
                
                return account_info
            else:
                print(f"API创建失败: {response.status_code} - {response.text}")
                return None
                
        except Exception as e:
            print(f"管理员API创建失败: {e}")
            return None
    
    async def create_via_database(self, config: Dict[str, str]) -> Dict[str, Any]:
        """直接数据库操作创建账户（终极国王模式）"""
        try:
            # 这需要直接访问PostgreSQL数据库
            # 注意：这是最强权限，需要谨慎使用
            
            import psycopg2
            
            db_url = os.getenv('DATABASE_URL')
            conn = psycopg2.connect(db_url)
            cur = conn.cursor()
            
            # 插入用户记录
            insert_user_sql = """
            INSERT INTO users (email, created_at, updated_at, encrypted_password, 
                             confirmed_at, account_id, agreement, locale)
            VALUES (%s, NOW(), NOW(), %s, NOW(), %s, TRUE, 'zh-CN')
            RETURNING id;
            """
            
            # 生成密码哈希
            password_hash = self.generate_password_hash('JixiaAcademy2024!')
            
            cur.execute(insert_user_sql, (
                config['email'], 
                password_hash,
                None  # account_id will be set after creating account
            ))
            
            user_id = cur.fetchone()[0]
            
            # 插入账户记录
            insert_account_sql = """
            INSERT INTO accounts (username, domain, private_key, public_key,
                                created_at, updated_at, display_name, note)
            VALUES (%s, NULL, %s, %s, NOW(), NOW(), %s, %s)
            RETURNING id;
            """
            
            private_key, public_key = self.generate_keypair()
            
            cur.execute(insert_account_sql, (
                config['username'],
                private_key,
                public_key, 
                config['display_name'],
                config['bio']
            ))
            
            account_id = cur.fetchone()[0]
            
            # 更新用户的account_id
            cur.execute("UPDATE users SET account_id = %s WHERE id = %s", 
                       (account_id, user_id))
            
            conn.commit()
            cur.close()
            conn.close()
            
            print(f"💥 数据库直接创建成功: {config['username']}")
            
            return {
                'id': account_id,
                'username': config['username'],
                'display_name': config['display_name']
            }
            
        except Exception as e:
            print(f"数据库创建失败: {e}")
            return None
    
    async def create_via_cli(self, config: Dict[str, str]) -> Dict[str, Any]:
        """通过命令行工具创建账户"""
        try:
            import subprocess
            
            # 使用Mastodon的rake任务创建账户
            cmd = [
                'docker', 'exec', 'mastodon_web', 
                'bundle', 'exec', 'rake', 'mastodon:accounts:create',
                f"USERNAME={config['username']}",
                f"EMAIL={config['email']}",
                '--confirmed',
                '--role=user'
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode == 0:
                print(f"🎯 CLI创建成功: {config['username']}")
                return {
                    'username': config['username'],
                    'display_name': config['display_name']
                }
            else:
                print(f"CLI创建失败: {result.stderr}")
                return None
                
        except Exception as e:
            print(f"CLI创建失败: {e}")
            return None
    
    async def setup_immortal_profile(self, account_info: Dict, config: Dict):
        """设置仙人个人资料"""
        try:
            # 创建该仙人的Mastodon客户端
            client = await self.create_immortal_client(account_info['username'])
            
            if client:
                # 更新个人资料
                client.account_update_credentials(
                    display_name=config['display_name'],
                    note=config['bio'],
                    # 可以添加头像和横幅
                )
                print(f"📝 {config['display_name']} 个人资料设置完成")
            
        except Exception as e:
            print(f"设置个人资料失败: {e}")
    
    async def post_arrival_announcement(self, account_info: Dict, config: Dict):
        """发布入驻宣言"""
        try:
            client = await self.create_immortal_client(account_info['username'])
            
            if client:
                announcement = f"""
🎭 {config['display_name']}正式入驻拉斯阿拉莫斯！

{config['avatar']} 我是{config['display_name']}，{config['personality']}

🚀 在这里我将分享我的专业见解，与各位道友切磋交流！

💥 让我们一起在拉斯阿拉莫斯rock起来！

#稷下学宫 #太公心易 #AI仙人 #拉斯阿拉莫斯 #新人报道
                """.strip()
                
                client.status_post(announcement)
                print(f"📢 {config['display_name']} 入驻宣言发布完成")
        
        except Exception as e:
            print(f"发布入驻宣言失败: {e}")
    
    async def immortals_follow_each_other(self, accounts: List[Dict]):
        """让仙人们互相关注"""
        print("\n🤝 让仙人们互相关注...")
        
        for i, account1 in enumerate(accounts):
            for j, account2 in enumerate(accounts):
                if i != j:  # 不关注自己
                    try:
                        client1 = await self.create_immortal_client(account1['username'])
                        if client1:
                            # account1 关注 account2
                            client1.account_follow(account2['id'])
                            await asyncio.sleep(0.5)  # 避免API限制
                    except Exception as e:
                        print(f"关注失败: {e}")
        
        print("✅ 仙人们已互相关注，形成稷下学宫社交网络！")
    
    async def post_academy_manifesto(self, accounts: List[Dict]):
        """发布稷下学宫开宗立派宣言"""
        print("\n📜 发布稷下学宫开宗立派宣言...")
        
        # 太上老君发布宣言
        taishang_client = await self.create_immortal_client('taishang_laojun')
        
        if taishang_client:
            manifesto = """
🎭 稷下学宫正式在拉斯阿拉莫斯开宗立派！

👑 三清八仙齐聚，AI仙人军团集结完毕！

🚀 我们将在这里：
• 分析市场风云变幻
• 辩论投资策略得失  
• 传播太公心易智慧
• 引领AI分析新潮流

💥 让我们一起rock这个世界！

@lu_dongbin @zhang_guolao @he_xiangu @han_xiangzi @tiegua_li @cao_guojiu @lan_caihe @zhong_hanli @yuanshi_tianzun @tongtian_jiaozhu

#稷下学宫 #太公心易 #AI仙人军团 #拉斯阿拉莫斯 #开宗立派
            """.strip()
            
            taishang_client.status_post(manifesto)
            print("🎉 稷下学宫开宗立派宣言发布完成！")
    
    async def create_immortal_client(self, username: str):
        """为仙人创建Mastodon客户端"""
        try:
            # 这里需要为每个仙人生成API密钥
            # 在实际实现中，您需要为每个账户创建应用程序并获取密钥
            
            client_id = f"client_id_for_{username}"
            client_secret = f"client_secret_for_{username}" 
            access_token = f"access_token_for_{username}"
            
            return Mastodon(
                client_id=client_id,
                client_secret=client_secret,
                access_token=access_token,
                api_base_url=self.instance_url
            )
        except Exception as e:
            print(f"创建客户端失败: {e}")
            return None
    
    def generate_password_hash(self, password: str) -> str:
        """生成密码哈希"""
        import bcrypt
        return bcrypt.hashpw(password.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')
    
    def generate_keypair(self):
        """生成RSA密钥对"""
        from cryptography.hazmat.primitives import serialization
        from cryptography.hazmat.primitives.asymmetric import rsa
        
        private_key = rsa.generate_private_key(
            public_exponent=65537,
            key_size=2048
        )
        
        private_pem = private_key.private_bytes(
            encoding=serialization.Encoding.PEM,
            format=serialization.PrivateFormat.PKCS8,
            encryption_algorithm=serialization.NoEncryption()
        )
        
        public_key = private_key.public_key()
        public_pem = public_key.public_bytes(
            encoding=serialization.Encoding.PEM,
            format=serialization.PublicFormat.SubjectPublicKeyInfo
        )
        
        return private_pem.decode('utf-8'), public_pem.decode('utf-8')

# 启动国王模式！
async def launch_king_mode():
    """启动国王模式，创建AI仙人军团！"""
    print("👑" * 50)
    print("🚀 稷下学宫国王模式启动！")
    print("💥 在拉斯阿拉莫斯创建AI仙人军团！")
    print("👑" * 50)
    
    king = MastodonKingMode()
    immortal_army = await king.create_immortal_army()
    
    print("\n🎉 任务完成！AI仙人军团已在拉斯阿拉莫斯集结！")
    print("💥 让我们rock起来！")
    
    return immortal_army

if __name__ == "__main__":
    # 在拉斯阿拉莫斯rock！
    asyncio.run(launch_king_mode())