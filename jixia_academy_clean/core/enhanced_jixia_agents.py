#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强版稷下学宫智能体
直接通过MCP调用Zilliz数据，实现知情辩论
"""

import asyncio
import json
import logging
from datetime import datetime
from typing import Dict, List, Any, Optional
from dataclasses import dataclass

from autogen_mcp_server import mcp_server

logger = logging.getLogger(__name__)


@dataclass
class AgentConfig:
    """智能体配置"""
    name: str
    specialty: str
    bagua_position: str
    emoji: str
    personality: str
    investment_style: str


class EnhancedJixiaAgent:
    """增强版稷下学宫智能体 - 直接MCP数据访问"""
    
    def __init__(self, config: AgentConfig):
        self.config = config
        self.name = config.name
        self.specialty = config.specialty
        self.bagua_position = config.bagua_position
        self.emoji = config.emoji
        
        # 智能体状态
        self.current_context = {}
        self.debate_history = []
        self.confidence_level = 0.5
        
        logger.info(f"✅ 初始化智能体: {self.name} ({self.specialty})")
    
    async def prepare_for_debate(self, topic: str, analysis_depth: str = "high") -> Dict:
        """准备辩论 - 从Zilliz获取知情上下文"""
        try:
            logger.info(f"🔍 {self.name} 正在准备辩论: {topic}")
            
            # 1. 获取市场情报
            market_intelligence = await mcp_server.call_tool("query_market_intelligence", {
                "topic": topic,
                "analysis_depth": analysis_depth,
                "timeframe_days": 14
            })
            
            # 2. 获取个人历史记忆
            personal_memory = await mcp_server.call_tool("get_agent_historical_context", {
                "agent_name": self.name,
                "topic": topic
            })
            
            # 3. 搜索相似历史辩论
            similar_debates = await mcp_server.call_tool("search_similar_debates", {
                "current_topic": topic,
                "similarity_threshold": 0.7
            })
            
            # 4. 获取当前市场脉象
            market_pulse = await mcp_server.call_tool("get_market_pulse", {
                "sectors": ["all"],
                "timeframe": "1d"
            })
            
            # 5. 整合上下文
            self.current_context = {
                "topic": topic,
                "market_intelligence": market_intelligence.get("data", {}),
                "personal_memory": personal_memory.get("data", {}),
                "similar_debates": similar_debates.get("data", {}),
                "market_pulse": market_pulse.get("data", {}),
                "preparation_time": datetime.now().isoformat()
            }
            
            logger.info(f"✅ {self.name} 辩论准备完成")
            return self.current_context
            
        except Exception as e:
            logger.error(f"❌ {self.name} 辩论准备失败: {e}")
            return self._get_fallback_context(topic)
    
    def generate_informed_view(self, topic: str) -> str:
        """基于真实数据生成知情观点"""
        if not self.current_context:
            return self._generate_basic_view(topic)
        
        # 提取关键信息
        market_data = self.current_context.get("market_intelligence", {})
        personal_data = self.current_context.get("personal_memory", {})
        pulse_data = self.current_context.get("market_pulse", {})
        
        # 构建知情观点
        view_components = []
        
        # 1. 基于市场情报的分析
        if market_data.get("relevant_events"):
            recent_events = market_data["relevant_events"][:2]
            view_components.append(f"基于最近的市场事件分析：{', '.join([e.get('title', '') for e in recent_events])}")
        
        # 2. 基于个人历史的一致性
        if personal_data.get("consistency_score", 0) > 0.7:
            view_components.append(f"根据我过往{personal_data.get('accuracy_trend', '稳定')}的预测记录")
        
        # 3. 基于专业领域的观点
        specialty_view = self._get_specialty_perspective(topic, market_data, pulse_data)
        view_components.append(specialty_view)
        
        # 4. 基于八卦位置的哲学观点
        bagua_view = self._get_bagua_perspective(topic, pulse_data)
        view_components.append(bagua_view)
        
        # 5. 风险评估和建议
        risk_assessment = self._assess_risk(market_data, pulse_data)
        view_components.append(risk_assessment)
        
        # 整合观点
        informed_view = f"""
{self.emoji} **{self.name}** ({self.specialty}专家) 的知情观点：

{' '.join(view_components)}

**我的建议**: {self._generate_recommendation(topic, market_data, pulse_data)}

**置信度**: {self._calculate_confidence():.1%}
        """.strip()
        
        return informed_view
    
    def _get_specialty_perspective(self, topic: str, market_data: Dict, pulse_data: Dict) -> str:
        """基于专业领域的观点"""
        specialty_perspectives = {
            "主动投资": "从主动投资角度，我认为当前市场提供了不错的选股机会",
            "被动ETF投资": "从被动投资角度，建议通过ETF分散风险，定投策略更为稳妥",
            "传统价值投资": "从价值投资角度，应该关注基本面和长期价值",
            "meme币新兴投资": "从新兴投资角度，要关注市场情绪和社区热度",
            "热点追踪": "从热点追踪角度，当前市场主题轮动较快",
            "草根视角": "从草根投资者角度，要注意风险控制和资金管理",
            "机构观点": "从机构投资角度，需要考虑宏观经济和政策影响",
            "技术分析": "从技术分析角度，图表形态和趋势指标显示"
        }
        
        base_view = specialty_perspectives.get(self.specialty, f"从{self.specialty}角度分析")
        
        # 根据市场数据调整观点
        sentiment = market_data.get("sentiment_trend", "中性")
        if sentiment == "积极":
            return f"{base_view}，当前市场情绪偏乐观，可以适度积极"
        elif sentiment == "消极":
            return f"{base_view}，当前市场情绪偏谨慎，建议保守操作"
        else:
            return f"{base_view}，当前市场情绪中性，建议均衡配置"
    
    def _get_bagua_perspective(self, topic: str, pulse_data: Dict) -> str:
        """基于八卦位置的哲学观点"""
        bagua_philosophies = {
            "乾": "乾卦主动，应该积极进取，把握机会",
            "坤": "坤卦包容，应该稳健持有，厚德载物",
            "兑": "兑卦喜悦，应该保持乐观，但不可盲目",
            "艮": "艮卦止静，应该耐心等待，择时而动",
            "离": "离卦光明，应该追求热点，但防过热",
            "坎": "坎卦险阻，应该谨慎行事，防范风险",
            "震": "震卦雷动，应该果断决策，顺势而为",
            "巽": "巽卦风行，应该灵活应变，顺风而行"
        }
        
        philosophy = bagua_philosophies.get(self.bagua_position, "应该保持理性分析")
        return f"从{self.bagua_position}卦的哲学角度，{philosophy}"
    
    def _assess_risk(self, market_data: Dict, pulse_data: Dict) -> str:
        """评估风险"""
        risk_level = pulse_data.get("risk_level", "中等")
        volatility = pulse_data.get("volatility_index", 0.5)
        
        if risk_level == "高" or volatility > 0.8:
            return "当前市场风险较高，建议控制仓位"
        elif risk_level == "低" or volatility < 0.3:
            return "当前市场风险较低，可以适度加仓"
        else:
            return "当前市场风险适中，建议均衡配置"
    
    def _generate_recommendation(self, topic: str, market_data: Dict, pulse_data: Dict) -> str:
        """生成投资建议"""
        sentiment = market_data.get("sentiment_trend", "中性")
        risk_level = pulse_data.get("risk_level", "中等")
        
        if sentiment == "积极" and risk_level == "低":
            return "建议积极配置，分批建仓"
        elif sentiment == "消极" or risk_level == "高":
            return "建议谨慎观望，控制风险"
        else:
            return "建议均衡配置，动态调整"
    
    def _calculate_confidence(self) -> float:
        """计算置信度"""
        base_confidence = 0.5
        
        # 基于数据质量调整
        if self.current_context:
            market_data = self.current_context.get("market_intelligence", {})
            personal_data = self.current_context.get("personal_memory", {})
            
            # 如果有充足的市场数据
            if market_data.get("total_events", 0) > 5:
                base_confidence += 0.2
            
            # 如果个人历史准确率高
            if personal_data.get("consistency_score", 0) > 0.8:
                base_confidence += 0.2
            
            # 如果有相似历史案例
            similar_debates = self.current_context.get("similar_debates", {})
            if similar_debates.get("total_found", 0) > 0:
                base_confidence += 0.1
        
        return min(base_confidence, 0.95)  # 最高95%置信度
    
    def _generate_basic_view(self, topic: str) -> str:
        """生成基础观点（无数据时的降级方案）"""
        return f"""
{self.emoji} **{self.name}** ({self.specialty}专家) 的观点：

基于我在{self.specialty}领域的经验，对于{topic}这个话题，我认为需要从{self.bagua_position}卦的角度来分析。

**我的建议**: 建议保持谨慎乐观的态度，密切关注市场变化。

**置信度**: 50%（数据有限）
        """.strip()
    
    def _get_fallback_context(self, topic: str) -> Dict:
        """获取降级上下文"""
        return {
            "topic": topic,
            "market_intelligence": {"data_source": "fallback"},
            "personal_memory": {"data_source": "fallback"},
            "similar_debates": {"data_source": "fallback"},
            "market_pulse": {"data_source": "fallback"},
            "preparation_time": datetime.now().isoformat()
        }
    
    async def participate_in_debate(self, topic: str, other_views: List[str]) -> str:
        """参与辩论"""
        # 准备辩论上下文
        await self.prepare_for_debate(topic)
        
        # 生成知情观点
        my_view = self.generate_informed_view(topic)
        
        # 分析其他观点并回应
        if other_views:
            response = self._respond_to_other_views(other_views)
            my_view += f"\n\n**对其他观点的回应**: {response}"
        
        return my_view
    
    def _respond_to_other_views(self, other_views: List[str]) -> str:
        """回应其他观点"""
        # 简化的观点分析和回应
        if len(other_views) == 0:
            return "期待听到其他仙友的观点。"
        
        # 分析观点倾向
        positive_count = sum(1 for view in other_views if any(word in view for word in ["积极", "乐观", "看好", "建议买入"]))
        negative_count = sum(1 for view in other_views if any(word in view for word in ["谨慎", "悲观", "看空", "建议卖出"]))
        
        if positive_count > negative_count:
            return f"我注意到多数仙友持乐观态度，但从{self.specialty}角度，我认为仍需要注意风险控制。"
        elif negative_count > positive_count:
            return f"虽然多数仙友较为谨慎，但从{self.specialty}角度，我认为也要关注潜在机会。"
        else:
            return f"各位仙友观点不一，这正体现了市场的复杂性。从{self.specialty}角度，我坚持我的分析。"


# 预定义的八仙配置
EIGHT_IMMORTALS_CONFIGS = {
    "吕洞宾": AgentConfig(
        name="吕洞宾",
        specialty="主动投资",
        bagua_position="乾",
        emoji="⚔️",
        personality="积极进取，敢于决策",
        investment_style="主动选股，集中投资"
    ),
    "何仙姑": AgentConfig(
        name="何仙姑",
        specialty="被动ETF投资",
        bagua_position="坤",
        emoji="🌸",
        personality="稳健包容，长期持有",
        investment_style="ETF定投，分散配置"
    ),
    "张果老": AgentConfig(
        name="张果老",
        specialty="传统价值投资",
        bagua_position="兑",
        emoji="👴",
        personality="经验丰富，重视基本面",
        investment_style="价值投资，长期持有"
    ),
    "韩湘子": AgentConfig(
        name="韩湘子",
        specialty="meme币新兴投资",
        bagua_position="艮",
        emoji="👦",
        personality="年轻敏锐，追求新潮",
        investment_style="新兴资产，高风险高收益"
    ),
    "汉钟离": AgentConfig(
        name="汉钟离",
        specialty="热点追踪",
        bagua_position="离",
        emoji="🪭",
        personality="热情如火，追逐热点",
        investment_style="热点轮动，短线操作"
    ),
    "蓝采和": AgentConfig(
        name="蓝采和",
        specialty="草根视角",
        bagua_position="坎",
        emoji="💧",
        personality="朴实谨慎，关注风险",
        investment_style="小额投资，稳健为主"
    ),
    "曹国舅": AgentConfig(
        name="曹国舅",
        specialty="机构观点",
        bagua_position="震",
        emoji="👑",
        personality="权威专业，宏观视野",
        investment_style="机构策略，大资金配置"
    ),
    "铁拐李": AgentConfig(
        name="铁拐李",
        specialty="技术分析",
        bagua_position="巽",
        emoji="🥃",
        personality="技术精湛，灵活应变",
        investment_style="技术分析，趋势跟踪"
    )
}


def create_enhanced_agent(agent_name: str) -> EnhancedJixiaAgent:
    """创建增强版智能体"""
    if agent_name not in EIGHT_IMMORTALS_CONFIGS:
        raise ValueError(f"未知智能体: {agent_name}")
    
    config = EIGHT_IMMORTALS_CONFIGS[agent_name]
    return EnhancedJixiaAgent(config)


# 使用示例
async def test_enhanced_agent():
    """测试增强版智能体"""
    print("🧪 测试增强版稷下学宫智能体...")
    
    # 创建吕洞宾智能体
    lvdongbin = create_enhanced_agent("吕洞宾")
    
    # 测试辩论准备
    topic = "比特币突破10万美元的投资机会"
    context = await lvdongbin.prepare_for_debate(topic)
    print(f"📋 辩论准备完成: {json.dumps(context, ensure_ascii=False, indent=2)}")
    
    # 测试观点生成
    view = lvdongbin.generate_informed_view(topic)
    print(f"💭 知情观点:\n{view}")


if __name__ == "__main__":
    asyncio.run(test_enhanced_agent())
