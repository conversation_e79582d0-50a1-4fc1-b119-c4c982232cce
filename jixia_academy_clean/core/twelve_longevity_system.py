# -*- coding: utf-8 -*-
"""
🐒 十二境界 × 十二长生系统
基于西游记的投资者修仙路径

核心创新：
- 十二长生配对十二境界
- 四季分章，每季三境界
- 每个境界都有深刻的投资寓意

作者：太公心易BI系统
版本：v4.0 Twelve Longevity Edition
"""

from enum import Enum
from dataclasses import dataclass
from typing import Dict, List, Optional, Tuple
import pandas as pd

class TwelveLongevity(Enum):
    """十二长生"""
    YANG_BIRTH = ("长生", "阳生", "新生力量的萌发")
    YANG_BATH = ("沐浴", "阳浴", "初次接触，懵懂学习")
    YANG_CROWN = ("冠带", "阳冠", "初具规模，开始成形")
    YANG_OFFICIAL = ("临官", "阳官", "正式登场，承担责任")
    YANG_EMPEROR = ("帝旺", "阳帝", "达到巅峰，君临天下")
    YANG_DECLINE = ("衰", "阳衰", "盛极而衰，开始下滑")
    YANG_SICK = ("病", "阳病", "问题显现，需要调整")
    YANG_DEATH = ("死", "阳死", "彻底失败，面临终结")
    YANG_TOMB = ("墓", "阳墓", "埋葬过去，准备重生")
    YANG_EXTINCT = ("绝", "阳绝", "断绝旧路，寻找新径")
    YANG_FETUS = ("胎", "阳胎", "孕育新生，积蓄力量")
    YANG_NURTURE = ("养", "阳养", "精心培育，等待时机")

@dataclass
class MonkeyRealm:
    """猴王境界"""
    level: int
    name: str
    longevity: TwelveLongevity
    season: str
    story: str
    investment_meaning: str
    key_lesson: str
    unlock_condition: str
    power_level: int

class TwelveLongevitySystem:
    """十二长生修仙系统"""
    
    # 十二境界完整定义
    REALMS = [
        # 🌸 第一季·春 (1-3级) - 觉醒篇
        MonkeyRealm(
            level=1,
            name="日精月华",
            longevity=TwelveLongevity.YANG_BIRTH,
            season="春",
            story="石猴吸收日精月华，在花果山中诞生灵智",
            investment_meaning="初入市场，开始接触投资概念，如婴儿般纯真",
            key_lesson="万事开头难，但要保持初心",
            unlock_condition="开户入金，完成第一笔交易",
            power_level=10
        ),
        
        MonkeyRealm(
            level=2,
            name="一跃成王",
            longevity=TwelveLongevity.YANG_BATH,
            season="春",
            story="老猕猴说：谁能一跃便可成王。猴子一跃，大家鱼贯而入，却忘了立石猴为王。猴子大怒：人而无信，枉为人！",
            investment_meaning="初次成功后被忽视，体验到市场的不公和人性的复杂",
            key_lesson="成功不等于认可，市场不会因为你的努力而给你应得的回报",
            unlock_condition="首次盈利但随后被套",
            power_level=25
        ),
        
        MonkeyRealm(
            level=3,
            name="生死大限",
            longevity=TwelveLongevity.YANG_CROWN,
            season="春",
            story="猴王有生死，人皇亦然。何人能跳出三界外，不在五行中？我东胜神州解决不了，需到西牛贺洲",
            investment_meaning="意识到投资的风险和局限性，开始寻求更高层次的智慧",
            key_lesson="认识到自己的无知，是智慧的开始",
            unlock_condition="经历重大亏损，开始反思",
            power_level=40
        ),
        
        # ☀️ 第二季·夏 (4-6级) - 求道篇
        MonkeyRealm(
            level=4,
            name="舢板求道",
            longevity=TwelveLongevity.YANG_OFFICIAL,
            season="夏",
            story="猴王乘舢板漂洋过海，历经千辛万苦寻找长生不老之术",
            investment_meaning="主动学习投资知识，寻找适合自己的投资方法",
            key_lesson="求道路上必须付出代价，没有捷径可走",
            unlock_condition="开始系统学习投资理论",
            power_level=60
        ),
        
        MonkeyRealm(
            level=5,
            name="斜月三星洞",
            longevity=TwelveLongevity.YANG_EMPEROR,
            season="夏",
            story="在斜月三星洞拜须菩提祖师为师，学得七十二变和筋斗云",
            investment_meaning="找到投资导师或系统，掌握核心投资技能",
            key_lesson="师父领进门，修行在个人",
            unlock_condition="建立完整的投资体系",
            power_level=85
        ),
        
        MonkeyRealm(
            level=6,
            name="得偿所望",
            longevity=TwelveLongevity.YANG_DECLINE,
            season="夏",
            story="学成归来，在花果山大显神通，众猴敬仰",
            investment_meaning="投资技能初成，开始稳定盈利，信心满满",
            key_lesson="小成功容易让人骄傲，要保持谦逊",
            unlock_condition="连续3个月盈利",
            power_level=100
        ),
        
        # 🍂 第三季·秋 (7-9级) - 膨胀篇
        MonkeyRealm(
            level=7,
            name="傲气回山",
            longevity=TwelveLongevity.YANG_SICK,
            season="秋",
            story="自立为齐天大圣，不服天庭管束，傲气冲天",
            investment_meaning="过度自信，开始挑战市场，认为自己无所不能",
            key_lesson="傲慢是失败的开始",
            unlock_condition="年化收益超过50%",
            power_level=120
        ),
        
        MonkeyRealm(
            level=8,
            name="受招安",
            longevity=TwelveLongevity.YANG_DEATH,
            season="秋",
            story="被天庭招安为弼马温，后知真相大怒反叛",
            investment_meaning="被市场热点诱惑，偏离原有策略，最终发现被套路",
            key_lesson="不要被表面的利益蒙蔽双眼",
            unlock_condition="追逐热点导致亏损",
            power_level=90
        ),
        
        MonkeyRealm(
            level=9,
            name="蟠桃盛宴",
            longevity=TwelveLongevity.YANG_TOMB,
            season="秋",
            story="偷吃蟠桃、仙丹，醉酒闹事，彻底与天庭决裂",
            investment_meaning="在牛市中忘乎所以，满仓操作，忽视风险",
            key_lesson="贪婪会让人失去理智",
            unlock_condition="满仓操作遇到黑天鹅",
            power_level=70
        ),
        
        # ❄️ 第四季·冬 (10-12级) - 觉悟篇
        MonkeyRealm(
            level=10,
            name="大闹天宫",
            longevity=TwelveLongevity.YANG_EXTINCT,
            season="冬",
            story="与天兵天将大战，打破南天门，威震三界",
            investment_meaning="孤注一掷与市场对抗，短期内可能有所斩获",
            key_lesson="个人力量再强，也无法对抗整个系统",
            unlock_condition="全仓做空或做多",
            power_level=150
        ),
        
        MonkeyRealm(
            level=11,
            name="八卦炉炼",
            longevity=TwelveLongevity.YANG_FETUS,
            season="冬",
            story="被太上老君投入八卦炉中炼制，反而炼就火眼金睛",
            investment_meaning="经历极端市场考验，痛苦中获得真正的洞察力",
            key_lesson="真正的智慧来自于痛苦的磨炼",
            unlock_condition="账户回撤超过70%",
            power_level=50
        ),
        
        MonkeyRealm(
            level=12,
            name="五行山下",
            longevity=TwelveLongevity.YANG_NURTURE,
            season="冬",
            story="被如来佛祖压在五行山下五百年，静心思过，等待救赎",
            investment_meaning="彻底觉悟，重新审视投资哲学，耐心等待机会",
            key_lesson="真正的成功需要时间的沉淀和内心的平静",
            unlock_condition="重新开始，建立新的投资哲学",
            power_level=200
        )
    ]
    
    def __init__(self):
        self.current_realm = 1
        self.total_experience = 0
        self.season_progress = {"春": 0, "夏": 0, "秋": 0, "冬": 0}
    
    def get_realm_by_level(self, level: int) -> Optional[MonkeyRealm]:
        """根据等级获取境界"""
        for realm in self.REALMS:
            if realm.level == level:
                return realm
        return None
    
    def get_current_realm(self) -> MonkeyRealm:
        """获取当前境界"""
        return self.get_realm_by_level(self.current_realm)
    
    def get_season_realms(self, season: str) -> List[MonkeyRealm]:
        """获取指定季节的境界"""
        return [realm for realm in self.REALMS if realm.season == season]
    
    def create_realms_dataframe(self) -> pd.DataFrame:
        """创建境界表格"""
        data = []
        for realm in self.REALMS:
            data.append({
                "等级": realm.level,
                "境界名": realm.name,
                "十二长生": realm.longevity.value[0],
                "长生含义": realm.longevity.value[1],
                "季节": realm.season,
                "西游故事": realm.story[:50] + "..." if len(realm.story) > 50 else realm.story,
                "投资寓意": realm.investment_meaning[:40] + "..." if len(realm.investment_meaning) > 40 else realm.investment_meaning,
                "核心教训": realm.key_lesson,
                "解锁条件": realm.unlock_condition,
                "力量等级": realm.power_level
            })
        
        return pd.DataFrame(data)
    
    def get_realm_details(self, level: int) -> Dict:
        """获取境界详细信息"""
        realm = self.get_realm_by_level(level)
        if not realm:
            return {}
        
        return {
            "基本信息": {
                "等级": realm.level,
                "境界名": realm.name,
                "季节": realm.season,
                "力量等级": realm.power_level
            },
            "十二长生": {
                "长生名": realm.longevity.value[0],
                "阴阳": realm.longevity.value[1],
                "含义": realm.longevity.value[2]
            },
            "西游故事": realm.story,
            "投资寓意": realm.investment_meaning,
            "核心教训": realm.key_lesson,
            "解锁条件": realm.unlock_condition
        }
    
    def advance_realm(self) -> Tuple[bool, Optional[MonkeyRealm]]:
        """提升境界"""
        if self.current_realm < 12:
            self.current_realm += 1
            new_realm = self.get_current_realm()
            
            # 更新季节进度
            if new_realm:
                self.season_progress[new_realm.season] += 1
            
            return True, new_realm
        return False, None
    
    def get_progress_summary(self) -> Dict:
        """获取进度总结"""
        current = self.get_current_realm()
        total_realms = len(self.REALMS)
        progress_percent = (self.current_realm / total_realms) * 100
        
        return {
            "当前境界": current.name if current else "未知",
            "当前等级": self.current_realm,
            "总进度": f"{self.current_realm}/{total_realms}",
            "完成度": f"{progress_percent:.1f}%",
            "当前季节": current.season if current else "未知",
            "季节进度": self.season_progress,
            "当前长生": current.longevity.value[0] if current else "未知"
        }

# 使用示例
def demo_twelve_longevity():
    """演示十二长生系统"""
    print("🐒 十二境界 × 十二长生系统")
    print("=" * 60)
    
    system = TwelveLongevitySystem()
    
    # 显示完整表格
    df = system.create_realms_dataframe()
    print("\n📊 完整境界表格:")
    print(df.to_string(index=False))
    
    # 显示第一季详情
    print("\n🌸 第一季·春 详细信息:")
    spring_realms = system.get_season_realms("春")
    
    for realm in spring_realms:
        print(f"\n{realm.level}. {realm.name} ({realm.longevity.value[0]})")
        print(f"   故事: {realm.story}")
        print(f"   寓意: {realm.investment_meaning}")
        print(f"   教训: {realm.key_lesson}")
        print(f"   解锁: {realm.unlock_condition}")
    
    # 显示当前进度
    print("\n📈 当前进度:")
    progress = system.get_progress_summary()
    for key, value in progress.items():
        print(f"   {key}: {value}")

if __name__ == "__main__":
    demo_twelve_longevity()