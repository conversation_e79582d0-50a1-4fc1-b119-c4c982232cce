# 炼妖壶 MCP 工具配置指南

## 🎯 概述

炼妖壶提供了一套完整的MCP (Model Context Protocol) 工具，可以让Heroku MCP Agent直接调用我们的金融分析API。

## 🛠️ 可用工具

### 1. 市场情绪分析 (`market_sentiment`)
- **功能**: 分析当前市场情绪和趋势
- **数据源**: RSS、社交媒体、新闻
- **参数**:
  - `source`: 数据源 (all/rss/social/news)
  - `timeframe`: 时间范围 (1h/4h/1d/1w)
  - `symbols`: 股票代码列表 (可选)

### 2. 交易信号生成 (`trading_signal`)
- **功能**: 基于技术分析生成交易信号
- **参数**:
  - `symbol`: 股票代码 (必填)
  - `strategy`: 策略类型 (momentum/mean_reversion/breakout/default)
  - `timeframe`: 时间周期 (5m/15m/1h/4h/1d)
  - `risk_level`: 风险等级 (1-5)

### 3. 投资组合分析 (`portfolio_analysis`)
- **功能**: 分析投资组合风险收益特征
- **参数**:
  - `holdings`: 持仓列表 (必填)
  - `benchmark`: 基准指数 (默认: 000300.SH)

### 4. 风险评估 (`risk_assessment`)
- **功能**: 评估投资风险水平
- **参数**:
  - `symbol`: 股票代码 (必填)
  - `position_size`: 仓位大小 (默认: 0.1)
  - `time_horizon`: 投资期限 (short/medium/long)

### 5. 新闻影响分析 (`news_impact`)
- **功能**: 分析新闻对市场的影响
- **参数**:
  - `news_text`: 新闻内容 (必填)
  - `symbols`: 相关股票代码 (可选)

## 🚀 配置步骤

### 步骤1: 部署API服务

```bash
# 确保主API服务运行在Heroku上
git push heroku main

# 检查API状态
curl https://cauldron.herokuapp.com/api/mcp/tools
```

### 步骤2: 注册MCP工具

```bash
# 运行注册脚本
python scripts/register_mcp_tools.py

# 这将生成两个配置文件:
# - heroku_mcp_config.json (Heroku MCP配置)
# - mcp_client_config.json (客户端配置)
```

### 步骤3: 配置Heroku MCP

1. 登录Heroku Dashboard
2. 进入你的Inference模型设置
3. 在Toolkit Integration部分添加工具配置
4. 使用生成的 `heroku_mcp_config.json` 内容

### 步骤4: 设置环境变量

```bash
# 在Heroku中设置API密钥
heroku config:set CAULDRON_API_KEY=your-api-key -a cauldron

# 或者在本地.env文件中设置
echo "CAULDRON_API_KEY=your-api-key" >> .env
```

## 🔧 使用方法

### 通过Heroku CLI

```bash
# 调用市场情绪分析
heroku ai:agents:call HAIKU_MODEL -a cauldron --prompt "请分析当前A股市场情绪"

# 生成交易信号
heroku ai:agents:call HAIKU_MODEL -a cauldron --prompt "请为茅台(600519)生成交易信号"

# 分析投资组合
heroku ai:agents:call HAIKU_MODEL -a cauldron --prompt "请分析我的投资组合风险"
```

### 通过MCP客户端 (Claude Desktop, Cursor等)

1. 将 `mcp_client_config.json` 内容添加到客户端配置
2. 重启客户端
3. 在对话中直接使用:

```
用户: "帮我分析一下今天的市场情绪"
AI: [自动调用market_sentiment工具并返回分析结果]

用户: "茅台现在适合买入吗？"
AI: [自动调用trading_signal工具分析茅台]
```

## 📊 API端点

### 基础端点
- `GET /api/mcp/tools` - 列出所有工具
- `GET /api/mcp/tools/{tool_name}` - 获取工具信息
- `POST /api/mcp/tools/{tool_name}/call` - 调用工具

### 健康检查
- `GET /health` - 服务健康状态
- `GET /ping` - 简单ping检查

## 🔐 认证

目前支持两种认证方式:

1. **API Key认证** (推荐)
   ```
   Authorization: Bearer your-api-key
   ```

2. **无认证模式** (开发测试)
   - 有调用频率限制
   - 功能可能受限

## 💰 定价模式

### 免费版
- 每日100次调用
- 基础工具: market_sentiment, news_impact

### 高级版  
- 每日1000次调用
- 所有工具: 包含trading_signal, risk_assessment

### 至尊版
- 无限调用
- 所有工具 + 自定义工具
- 优先支持

## 🐛 故障排除

### 常见问题

1. **工具调用失败**
   ```bash
   # 检查API状态
   curl https://cauldron.herokuapp.com/health
   
   # 检查工具列表
   curl https://cauldron.herokuapp.com/api/mcp/tools
   ```

2. **认证失败**
   ```bash
   # 检查API密钥设置
   heroku config:get CAULDRON_API_KEY -a cauldron
   ```

3. **MCP客户端无法连接**
   - 检查配置文件格式
   - 确认环境变量设置
   - 重启MCP客户端

### 日志查看

```bash
# 查看Heroku应用日志
heroku logs --tail -a cauldron

# 查看特定组件日志
heroku logs --tail -a cauldron --dyno web
```

## 📞 支持

- **文档**: https://docs.cauldron.ai/mcp-tools
- **GitHub Issues**: https://github.com/your-username/cauldron/issues
- **邮箱**: <EMAIL>

## 🔄 更新日志

### v1.0.0 (2025-01-06)
- 初始版本发布
- 支持5个基础金融分析工具
- Heroku MCP集成
- 多层定价模式
