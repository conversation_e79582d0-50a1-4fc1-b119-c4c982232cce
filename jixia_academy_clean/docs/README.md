# 🔥 炼妖壶增强版 (Enhanced Cauldron) v2.0

**炼妖壶增强版**是全球首个**事件驱动的散户行为研究AI平台**，融合RSS实时监控、多智能体辩论、散户心理分析于一体的革命性金融AI系统。

## 🎯 核心创新

### 🚀 **事件驱动架构** - 全球首创
- **RSS实时监控**: 24/7监控全球财经、政治、科技新闻
- **智能影响力评分**: 自动评估事件对市场的影响程度
- **阈值触发机制**: 重大事件自动触发AI分析系统

### 🎭 **稷下学宫多智能体辩论** - 文化与AI的完美融合
- **三清论道**: 灵宝道君主持，公正客观
- **八仙过海**: 吕洞宾、何仙姑等八位AI辩手，各显神通
- **结构化辩论**: 正方立论→反方质疑→多轮交锋→智慧总结

### 🎯 **RSS RAG系统** - 基于Zilliz的检索增强生成
- **向量化存储**: RSS文章自动向量化存储到Zilliz云数据库
- **语义搜索**: 基于embedding的智能相似度匹配
- **智能问答**: RAG增强的LLM问答，基于实时新闻内容
- **热门话题**: 自动分析趋势关键词和热点事件

### 🎯 **RSS事件矩阵分析系统** - 多维度量化预测
- **矩阵乘法模型**: 股价 = 标的本命矩阵 × RSS事件矩阵
- **九维事件空间**: 货币政策、地缘政治、市场情绪等多维度分析
- **四大原型系统**: 七仙女(科技成长)、大白马(价值蓝筹)、妖股(高波动)、周期股
- **假设检验框架**: 应然价格 vs 实然价格的统计验证

### 🌈 **九大主演风险感知光谱** - 独创散户心理模型
- **洪珏(10%)**: 热血追涨，情绪狂热
- **陈琉(20%)**: 盲目乐观，风险无感
- **黄琥(35%)**: 技术迷信，指标至上
- **陆珀(50%)**: 跟风从众，缺乏主见
- **兰琪(65%)**: 消息灵通，过度解读
- **典瑛(75%)**: 恐惧贪婪，犹豫不决
- **梓珂(85%)**: 过度自信，控制幻觉
- **白瑞(95%)**: 痛定思痛，旁观清醒
- **贺珍(100%)**: 冷酷收割，操控市场

### 🧠 **记忆与学习系统** - AI角色持续进化
- **向量化记忆**: 使用ChromaDB存储投资经验
- **相似度检索**: 基于事件相似性学习历史教训
- **情绪状态管理**: 跟踪AI角色的情绪变化和信心水平
- **个性化成长**: 根据市场反馈调整投资风格

## 🆚 核心差异化优势

| 维度 | 传统金融AI | TradingAgents | 🔥 **炼妖壶增强版** |
|------|------------|---------------|-------------------|
| **触发方式** | 定时分析 | 手动输入 | **RSS事件驱动** |
| **响应速度** | 小时级 | 分钟级 | **秒级实时响应** |
| **研究焦点** | 技术指标 | 专业投资 | **散户行为研究** |
| **文化内涵** | 无 | 技术导向 | **太公心易+稷下学宫** |
| **学习能力** | 静态模型 | 简单记忆 | **向量化深度学习** |
| **娱乐价值** | 无 | 低 | **韭菜小剧场，寓教于乐** |

## 📚 技术文档

### 🏗️ 系统架构文档
详细的技术架构设计文档，请查看 **[docs/](docs/)** 目录：

- **[文档导航](docs/README.md)** - 完整文档索引
- **[系统概览](docs/01-system-overview.md)** - 整体架构概览
- **[三脑架构](docs/02-three-brain-architecture.md)** - 核心三脑架构设计
- **[数据流架构](docs/03-data-flow-architecture.md)** - 数据处理流程
- **[Zilliz演示](docs/zilliz-demo-presentation.md)** - 技术演示文档

### 🎯 核心特色
1. **🧠 三脑架构**: Zilliz(神经) + MongoDB(情报) + PostgreSQL(秩序)
2. **🔮 太公心易**: 太乙观澜、遁甲择时、六壬察心三大算法
3. **🏛️ 稷下学宫**: 八仙过海多智能体辩论 + 三清论道决策
4. **📡 实时RAG**: RSS新闻流的实时向量化和语义检索

### 🚀 技术实现
- **[多数据库协调器](jixia_academy/multi_database_coordinator.py)** - 三脑架构的核心实现
- **[远程MCP客户端](jixia_academy/remote_mcp_client.py)** - 连接韩国服务器的MCP客户端
- **[免费加密货币数据流](jixia_academy/free_crypto_newsflow.py)** - 免费数据源集成

## 🚀 快速开始

### 环境准备
```bash
# 设置API密钥
export OPENROUTER_API_KEY="your_openrouter_key"
export FINNHUB_API_KEY="your_finnhub_key"  # 可选
export DASHSCOPE_API_KEY="your_dashscope_key"  # 可选

# 克隆项目
git clone https://github.com/your-username/cauldron.git
cd cauldron

# 安装依赖
pip install -r requirements.txt
```

### 启动系统
```bash
# 方式1: 一键启动增强版系统
python start_enhanced_cauldron.py

# 方式2: 传统启动方式
python src/scripts/start_system.py start
```

### 使用示例
```python
from src.core.enhanced_cauldron_system import EnhancedCauldronSystem, load_config_from_env

# 加载配置
config = load_config_from_env()

# 创建系统实例
system = EnhancedCauldronSystem(config)

# 启动监控
await system.start_monitoring()

# 手动触发事件分析
result = await system.manual_trigger(
    "美联储意外加息50个基点",
    "美联储在今日议息会议上意外宣布加息50个基点，超出市场预期"
)
```

### 🎯 RSS RAG系统（推荐）

```bash
# 快速测试RAG系统
python test_rss_rag.py

# 启动完整RAG演示
python rss_rag_demo.py

# 功能选项:
# 1. 摄取RSS数据 - 自动向量化存储
# 2. 语义搜索 - 智能相似度匹配  
# 3. 智能问答 - RAG增强回答
# 4. 热门话题 - 趋势分析
```

**核心优势**：
- 🚀 **高效存储**: 基于Zilliz云向量数据库，支持海量数据
- 🔍 **语义理解**: 不只是关键词匹配，真正理解语义相似性
- 💬 **智能问答**: 基于最新新闻内容的AI问答，信息更准确
- ⚡ **实时更新**: RSS数据实时摄取，信息永远最新

### 🎯 RSS事件矩阵分析系统

```bash
# 测试矩阵系统功能
python test_matrix_system.py

# 启动矩阵分析系统
python rss_matrix_analysis.py

# 选择选项1: 启动Web界面（推荐）
# 选择选项2: 命令行矩阵分析
# 选择选项3: 运行假设检验
```

**核心功能**：
- 📊 **多维事件矩阵**: 9个维度的事件关联分析
- 🎭 **股票原型预测**: 七仙女、大白马、妖股、周期股的差异化预测
- 🧪 **假设检验**: 应然价格vs实然价格的统计验证
- 📈 **可视化分析**: 矩阵热力图、预测结果、重要性排序

## 🎭 系统架构

```
RSS事件监控 → 影响力评分 → 阈值触发 → 稷下学宫辩论 → 九大主演反应 → 记忆学习 → N8N后续处理
     ↓              ↓            ↓            ↓             ↓           ↓           ↓
  多源新闻聚合    智能算法评估   自动化触发    多智能体协作    散户心理模拟   持续进化    工作流集成
```

## 📊 核心组件

### 1. 增强版RSS监控系统
- **多源聚合**: RSS + FinnHub + Alpha Vantage + NewsAPI
- **智能去重**: 基于内容相似度的事件聚类
- **实时评分**: 影响力、紧急度、情绪多维评估

### 2. 稷下学宫辩论系统
- **角色设定**: 9位AI角色，各具特色
- **辩论流程**: 结构化的多轮辩论机制
- **智慧总结**: 太公心易的投资哲学点评

### 3. 记忆与学习系统
- **经验存储**: 向量化存储投资经验和教训
- **智能检索**: 基于相似度的经验匹配
- **情绪追踪**: 实时更新AI角色的情绪状态

### 4. N8N工作流集成
- **自动化**: 与N8N无缝集成，支持复杂工作流
- **通知系统**: 重大事件自动推送到各种渠道
- **数据导出**: 支持多种格式的数据导出

## 🎯 使用场景

### 📚 **投资者教育**
- 通过"韭菜小剧场"学习散户心理
- 理解不同风险感知水平的投资行为
- 培养独立思考和理性投资能力

### ⚠️ **风险预警**
- 实时监控市场情绪变化
- 预警潜在的市场风险事件
- 提供多角度的风险分析

### 🔬 **学术研究**
- 行为金融学的实验平台
- 散户行为数据的收集和分析
- AI在金融领域应用的研究

### 💼 **机构应用**
- 市场情绪分析和预测
- 客户行为研究和建模
- 投资策略的辅助决策

## 📖 文档导航

- [🚀 快速开始指南](docs/QUICK_START.md)
- [🏗️ 系统架构设计](docs/ARCHITECTURE.md)
- [⚙️ 配置说明](docs/CONFIGURATION.md)
- [🎭 稷下学宫介绍](docs/THEORY/jixia_academy.md)
- [🌈 风险感知光谱](docs/THEORY/risk_spectrum.md)
- [💡 太公心易理论](docs/THEORY/taigong_xinyi.md)
- [🤝 贡献指南](CONTRIBUTING.md)
- [🌟 开源路线图](OPENSOURCE_ROADMAP.md)

## 🤝 参与贡献

我们欢迎所有形式的贡献！

### 贡献方式
- 🐛 **报告Bug**: 发现问题请提交Issue
- 💡 **功能建议**: 有好想法请分享给我们
- 📝 **文档改进**: 帮助完善文档和教程
- 💻 **代码贡献**: 提交PR改进系统功能
- 🎨 **设计优化**: 改进UI/UX设计
- 📊 **数据贡献**: 提供更多数据源和测试用例

### 开发环境
```bash
# 克隆开发分支
git clone -b develop https://github.com/your-username/cauldron.git

# 安装开发依赖
pip install -r requirements-dev.txt

# 运行测试
python -m pytest tests/

# 代码格式化
black src/
isort src/
```

## 📄 开源协议

本项目采用 [Apache 2.0 License](LICENSE) 开源协议。

## 🙏 致谢

### 灵感来源
- **TradingAgents**: 感谢 [TauricResearch/TradingAgents](https://github.com/TauricResearch/TradingAgents) 提供的多智能体架构灵感
- **稷下学宫**: 致敬古代齐国的学术传统和百家争鸣精神
- **太公心易**: 传承中华古代智慧在现代金融中的应用

### 技术栈
- **LangChain**: 多智能体框架
- **ChromaDB**: 向量化存储
- **FastAPI**: API服务
- **Streamlit**: 用户界面
- **N8N**: 工作流自动化

## 📞 联系我们

- **GitHub Issues**: [提交问题和建议](https://github.com/your-username/cauldron/issues)
- **微信群**: 扫码加入技术交流群
- **邮箱**: <EMAIL>
- **官网**: https://cauldron.taigong-xinyi.com

---

**太公心易BI系统 - 在众人向左时，智者向右**

*让AI帮助每个人成为更理性的投资者* 🧘‍♂️✨