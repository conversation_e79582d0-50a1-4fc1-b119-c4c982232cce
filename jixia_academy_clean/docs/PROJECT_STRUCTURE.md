# 稷下学宫 - 炼妖壶项目结构

## 项目概述

**稷下学宫** 是炼妖壶项目的AI辩论系统核心模块，通过N8N事件驱动触发三清八仙的智能投资辩论。

### 核心理念
- **N8N事件驱动触发** - 真实市场事件通过N8N工作流处理，自动触发辩论
- **三清论道 + 八仙过海** - 分层决策架构，八仙平辈辩论，三清上层决策
- **AutoGen多智能体** - 基于AutoGen 0.6.2的现代化AI辩论框架
- **Mastodon社交集成** - 辩论过程实时发布到长毛象平台
- **零停机架构** - 支持实时模型切换和无缝升级

## 项目结构

```
jixia_academy_clean/
├── core/                              # 核心模块
│   ├── debate_system.py              # 辩论系统核心
│   ├── debate_controller.py          # 辩论控制器
│   ├── mastodon_agents.py            # 长毛象智能体
│   ├── n8n_mcp_integration.py        # N8N MCP集成
│   └── tusita_palace_mcp.py          # 兜率宫MCP服务
├── agents/                           # AI智能体配置
│   ├── sanqing/                      # 三清配置
│   └── baxian/                       # 八仙配置
├── config/                           # 配置文件
│   ├── mastodon_config.json         # 长毛象配置
│   └── autogen_config.json          # AutoGen配置
├── tests/                            # 测试文件
│   ├── test_debate_system.py        # 辩论系统测试
│   └── test_mastodon_integration.py # 长毛象集成测试
└── docs/                             # 文档
    ├── PROJECT_STRUCTURE.md         # 项目结构文档
    ├── DEBATE_ARCHITECTURE.md       # 辩论架构说明
    └── MASTODON_INTEGRATION.md      # 长毛象集成文档
```

## 核心模块说明

### 1. 辩论系统 (`core/debate_system.py`)

**功能**：
- 三清八仙智能体管理
- 辩论流程控制
- 论据生成和逻辑验证
- 决策结果输出

**关键类**：
- `JixiaAcademyDebateSystem` - 稷下学宫辩论系统
- `SanqingAgent` - 三清智能体基类
- `BaxianAgent` - 八仙智能体基类
- `DebateSession` - 辩论会话管理

**八仙投资专业分工**：
```python
immortals_specialization = {
    "吕洞宾": {"bagua": "乾", "specialty": "主动投资", "emoji": "⚔️"},
    "何仙姑": {"bagua": "坤", "specialty": "被动ETF投资", "emoji": "🌸"},
    "张果老": {"bagua": "兑", "specialty": "传统价值投资", "emoji": "👴"},
    "韩湘子": {"bagua": "艮", "specialty": "meme币新兴投资", "emoji": "👦"},
    "汉钟离": {"bagua": "离", "specialty": "热点追踪", "emoji": "🪭"},
    "蓝采和": {"bagua": "坎", "specialty": "草根视角", "emoji": "💧"},
    "曹国舅": {"bagua": "震", "specialty": "机构观点", "emoji": "👑"},
    "铁拐李": {"bagua": "巽", "specialty": "技术分析", "emoji": "🥃"}
}
```

**三清决策层级**：
```python
sanqing_hierarchy = {
    "太上老君": {"role": "督导者", "function": "召集会议，追踪聊天趋势，协调三清八仙", "emoji": "🖌️"},
    "灵宝道君": {"role": "秘书长", "function": "汇总论据，梳理逻辑，验证推理", "emoji": "🟢"},
    "元始天尊": {"role": "裁决者", "function": "技术分析，最终决策，一槌定音", "emoji": "☯️"}
}
```

### 2. N8N MCP集成 (`core/n8n_mcp_integration.py`)

**功能**：
- N8N工作流触发接收
- 市场事件数据解析
- 辩论主题生成
- 结果回传N8N

**关键类**：
```python
class N8NMCPIntegration:
    def __init__(self):
        self.mcp_client = MCPClient()
        self.debate_controller = DebateController()

    async def handle_market_event(self, event_data):
        """处理N8N传入的市场事件"""
        topic = self.extract_debate_topic(event_data)
        result = await self.debate_controller.start_debate(topic)
        return result
```

### 3. 长毛象集成 (`core/mastodon_agents.py`)

**主要功能**：
- 🏛️ 实时辩论发布 - 辩论过程同步到长毛象
- 🎭 角色扮演 - 每个仙人独立的长毛象账号
- 📊 社交互动 - @mention触发，关注网络
- ⚙️ 自动化管理 - 批量账号管理和内容发布

**集成特色**：
- 11个仙人独立长毛象账号
- 实时辩论过程展示
- 社交网络情感分析
- 自动化内容生成

### 4. 兜率宫MCP服务 (`core/tusita_palace_mcp.py`)

**功能**：
- MCP协议服务端实现
- 工具注册和管理
- 与N8N的双向通信
- 数据格式标准化

**MCP工具集**：
```python
mcp_tools = {
    "start_debate": "启动稷下学宫辩论",
    "get_debate_status": "获取辩论状态",
    "post_to_mastodon": "发布到长毛象",
    "query_market_data": "查询市场数据",
    "analyze_sentiment": "情感分析"
}
```

## 技术特色

### 1. 现代化AI架构
- AutoGen 0.6.2多智能体框架
- 异步处理和并发控制
- 智能体生命周期管理

### 2. N8N事件驱动
- 实时市场事件监控
- 工作流自动化触发
- MCP协议标准化通信

### 3. 社交网络集成
- 长毛象平台实时发布
- 社交互动和情感分析
- 分布式内容传播

### 4. 零停机架构
- 热更新和模型切换
- 容错和降级机制
- 状态持久化管理

## 部署和配置

### 环境变量
```bash
# AutoGen配置
OPENAI_API_KEY=your_openai_key
OPENROUTER_API_KEY=your_openrouter_key

# 长毛象配置
MASTODON_BASE_URL=https://mastodon.git4ta.fun
MASTODON_ADMIN_TOKEN=your_admin_token

# N8N MCP配置
N8N_WEBHOOK_URL=your_n8n_webhook
MCP_SERVER_PORT=8000

# 数据库配置
ZILLIZ_URI=your_zilliz_endpoint
ZILLIZ_TOKEN=your_zilliz_token
```

### 依赖包
```bash
pip install -r requirements.txt
```

### 启动命令
```bash
# 启动MCP服务
python -m core.tusita_palace_mcp

# 启动辩论系统
python -m core.debate_controller

# 启动Streamlit界面
streamlit run app.py --server.port 8501
```

## 测试和验证

### 1. 辩论系统测试 (`tests/test_debate_system.py`)
- 三清八仙智能体测试
- 辩论流程完整性测试
- 决策逻辑验证

### 2. 长毛象集成测试 (`tests/test_mastodon_integration.py`)
- 账号创建和管理测试
- 内容发布和互动测试
- 社交网络分析测试

### 3. N8N MCP测试
- MCP协议通信测试
- 工作流触发测试
- 数据格式验证

## 商业价值

### 1. 差异化竞争优势
- 全球首创的稷下学宫AI辩论系统
- 三清八仙分层决策架构
- N8N事件驱动的智能化触发

### 2. 用户粘性
- 实时市场事件驱动的内容更新
- 社交网络互动和传播
- 个性化投资观点呈现

### 3. 技术护城河
- 复杂的多智能体协作系统
- 先进的事件驱动架构
- 成熟的社交网络集成

## 未来扩展

### 1. 智能体扩展
- 更多投资流派智能体
- 个性化智能体定制
- 跨语言智能体支持

### 2. 平台集成
- 更多社交平台支持
- 实时交易平台集成
- 区块链和DeFi集成

### 3. 功能增强
- 实时策略回测
- 投资组合优化
- 风险管理系统

---

**项目**: 炼妖壶 - 稷下学宫
**版本**: v2.0
**更新时间**: 2025-01-07
**团队**: 炼妖壶开发团队
