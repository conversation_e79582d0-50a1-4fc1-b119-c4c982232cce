# 🎼 Ollama本地指挥系统安装指南

## 🎯 为什么需要Ollama指挥？

在炼妖壶的架构中，我们有：
- **四梁八柱**: 4个OpenRouter免费账户构成的稷下学宫辩论系统
- **交响乐团**: 九大主演的完整散户心理光谱
- **本地指挥**: Ollama提供的7×24小时本地决策系统

### 🎼 指挥的作用
1. **成本控制**: 过滤不重要事件，节省API调用费用
2. **实时决策**: 本地运行，无网络延迟，响应更快
3. **持续学习**: 结合RAG系统，积累历史经验
4. **智能调度**: 决定何时触发昂贵的云端AI分析

## 🚀 Ollama安装

### Windows安装
```bash
# 下载并安装Ollama
# 访问 https://ollama.ai 下载Windows安装包
# 或使用winget
winget install Ollama.Ollama
```

### macOS安装
```bash
# 使用Homebrew
brew install ollama

# 或下载安装包
# 访问 https://ollama.ai 下载macOS安装包
```

### Linux安装
```bash
# 一键安装脚本
curl -fsSL https://ollama.ai/install.sh | sh

# 或手动安装
wget https://ollama.ai/download/ollama-linux-amd64
sudo mv ollama-linux-amd64 /usr/local/bin/ollama
sudo chmod +x /usr/local/bin/ollama
```

### Docker安装
```bash
# 拉取Ollama镜像
docker pull ollama/ollama

# 运行Ollama容器
docker run -d -v ollama:/root/.ollama -p 11434:11434 --name ollama ollama/ollama
```

## 🧠 推荐模型配置

### 轻量级指挥模型（推荐）
```bash
# Llama 3.2 3B - 平衡性能和资源消耗
ollama pull llama3.2:3b

# Phi-3 Mini - 微软出品，效率很高
ollama pull phi3:mini

# Gemma 2B - Google出品，轻量高效
ollama pull gemma:2b
```

### 高性能指挥模型（如果资源充足）
```bash
# Llama 3.2 8B - 更强的推理能力
ollama pull llama3.2:8b

# Mistral 7B - 优秀的多语言支持
ollama pull mistral:7b

# CodeLlama 7B - 如果需要代码分析
ollama pull codellama:7b
```

### 中文优化模型
```bash
# Qwen 7B - 阿里出品，中文效果好
ollama pull qwen:7b

# ChatGLM3 6B - 清华出品
ollama pull chatglm3:6b

# Baichuan2 7B - 百川智能
ollama pull baichuan2:7b
```

## ⚙️ 系统配置

### 1. 验证安装
```bash
# 检查Ollama是否正常运行
ollama --version

# 查看已安装模型
ollama list

# 测试模型
ollama run llama3.2:3b "你好，请简单介绍一下你自己"
```

### 2. 性能优化
```bash
# 设置环境变量（可选）
export OLLAMA_HOST=0.0.0.0:11434
export OLLAMA_MODELS=/path/to/models
export OLLAMA_NUM_PARALLEL=4
export OLLAMA_MAX_LOADED_MODELS=3
```

### 3. 内存配置
```bash
# 对于8GB内存的机器
ollama pull llama3.2:3b  # 推荐

# 对于16GB内存的机器
ollama pull llama3.2:8b  # 推荐

# 对于32GB+内存的机器
ollama pull llama3.1:70b  # 可选
```

## 🔧 炼妖壶集成配置

### 1. 安装Python依赖
```bash
pip install ollama chromadb
```

### 2. 环境变量配置
```bash
# 添加到 .env 文件
OLLAMA_MODEL=llama3.2:3b
OLLAMA_HOST=http://localhost:11434
CONDUCTOR_ENABLED=true
RAG_DATA_DIR=./data/rag
```

### 3. 启动配置
```python
# 在 start_enhanced_cauldron.py 中添加
from src.core.local_conductor_system import ConductorIntegratedSystem, LocalConductor

# 创建指挥系统
conductor = LocalConductor(model_name="llama3.2:3b")

# 集成到主系统
integrated_system = ConductorIntegratedSystem(enhanced_system, conductor)
```

## 📊 性能基准测试

### 不同模型的性能对比

| 模型 | 大小 | 内存需求 | 响应时间 | 决策质量 | 推荐场景 |
|------|------|----------|----------|----------|----------|
| llama3.2:3b | 3B | 4GB | 0.5s | ⭐⭐⭐⭐ | 日常使用 |
| phi3:mini | 3.8B | 4GB | 0.3s | ⭐⭐⭐⭐ | 快速响应 |
| gemma:2b | 2B | 3GB | 0.2s | ⭐⭐⭐ | 资源受限 |
| llama3.2:8b | 8B | 8GB | 1.0s | ⭐⭐⭐⭐⭐ | 高质量决策 |
| mistral:7b | 7B | 8GB | 0.8s | ⭐⭐⭐⭐⭐ | 多语言支持 |
| qwen:7b | 7B | 8GB | 0.9s | ⭐⭐⭐⭐⭐ | 中文优化 |

### 成本效益分析

```
假设场景：每天100个事件
- 无指挥系统：100个事件 × $0.05 = $5.00/天
- 有指挥系统：20个事件 × $0.05 = $1.00/天
- 节省成本：$4.00/天 = $1,460/年

指挥系统投入：
- 硬件成本：$0（使用现有设备）
- 电费成本：约$0.10/天
- 净节省：$3.90/天 = $1,423/年
```

## 🎯 使用示例

### 基础使用
```python
from src.core.local_conductor_system import LocalConductor, LocalRAGSystem

# 创建RAG系统
rag = LocalRAGSystem()

# 创建指挥系统
conductor = LocalConductor(model_name="llama3.2:3b", rag_system=rag)

# 评估事件
assessment = await conductor.assess_event(
    "美联储加息50个基点",
    "美联储在今日会议上宣布加息50个基点，符合市场预期",
    75.0
)

print(f"决策: {assessment.decision.decision_type}")
print(f"理由: {assessment.decision.reasoning}")
```

### 集成使用
```python
from src.core.enhanced_cauldron_system import EnhancedCauldronSystem
from src.core.local_conductor_system import ConductorIntegratedSystem

# 创建增强系统
enhanced_system = EnhancedCauldronSystem(config)

# 创建集成系统
integrated_system = ConductorIntegratedSystem(enhanced_system)

# 运行指挥增强监控
results = await integrated_system.enhanced_monitoring_cycle()
```

## 🔍 故障排除

### 常见问题

#### 1. Ollama无法启动
```bash
# 检查端口占用
lsof -i :11434

# 重启Ollama服务
ollama serve

# 检查日志
ollama logs
```

#### 2. 模型下载失败
```bash
# 检查网络连接
ping ollama.ai

# 手动下载模型
ollama pull llama3.2:3b --verbose

# 使用代理（如果需要）
export HTTP_PROXY=http://proxy:8080
export HTTPS_PROXY=http://proxy:8080
```

#### 3. 内存不足
```bash
# 检查内存使用
free -h

# 使用更小的模型
ollama pull gemma:2b

# 限制并发模型数量
export OLLAMA_MAX_LOADED_MODELS=1
```

#### 4. 响应速度慢
```bash
# 检查CPU使用率
top

# 使用GPU加速（如果有NVIDIA GPU）
ollama pull llama3.2:3b
# Ollama会自动检测并使用GPU

# 优化模型参数
# 在代码中设置更低的temperature和top_p
```

### 性能优化建议

#### 硬件优化
- **CPU**: 至少4核心，推荐8核心以上
- **内存**: 至少8GB，推荐16GB以上
- **存储**: SSD硬盘，提高模型加载速度
- **GPU**: NVIDIA GPU可显著提升性能

#### 软件优化
```bash
# 1. 预加载模型
ollama run llama3.2:3b ""

# 2. 设置合理的并发数
export OLLAMA_NUM_PARALLEL=2

# 3. 使用模型缓存
export OLLAMA_KEEP_ALIVE=24h
```

## 📈 监控和维护

### 性能监控
```python
# 获取指挥系统性能报告
report = conductor.get_performance_report()
print(json.dumps(report, indent=2, ensure_ascii=False))
```

### 定期维护
```bash
# 1. 清理未使用的模型
ollama rm old_model

# 2. 更新模型
ollama pull llama3.2:3b

# 3. 备份RAG数据
cp -r ./data/rag ./backup/rag_$(date +%Y%m%d)

# 4. 检查磁盘空间
df -h
```

## 🚀 高级配置

### 多模型负载均衡
```python
class MultiModelConductor:
    def __init__(self):
        self.models = [
            "llama3.2:3b",    # 主力模型
            "phi3:mini",      # 备用模型
            "gemma:2b"        # 轻量模型
        ]
        self.current_model = 0
    
    async def assess_with_fallback(self, event):
        for model in self.models:
            try:
                return await self.assess_with_model(model, event)
            except Exception as e:
                continue
        raise Exception("所有模型都不可用")
```

### 自适应模型选择
```python
class AdaptiveConductor:
    def select_model(self, event_complexity):
        if event_complexity > 0.8:
            return "llama3.2:8b"  # 复杂事件用大模型
        elif event_complexity > 0.5:
            return "llama3.2:3b"  # 中等事件用中模型
        else:
            return "gemma:2b"     # 简单事件用小模型
```

---

**🎼 让本地指挥与云端交响乐团完美协作！**

*在成本控制与性能之间找到最佳平衡* ⚖️✨