# 三脑架构正确数据流设计

## 🧠 问题分析

您的观察非常准确！当前系统存在一个关键问题：

**现状：** RSS → 直接存入Zilliz ❌
**应该：** RSS → MongoDB → 处理分析 → Zilliz ✅

## 🔄 正确的三脑数据流

### 阶段一：原始数据收集 (MongoDB - 情报脑)
```
RSS源 → 青龙面板定时抓取 → MongoDB原始存储
```

**MongoDB存储内容：**
- 完整的RSS原文
- 元数据信息
- 来源标识
- 时间戳
- 原始格式

### 阶段二：数据处理分析 (PostgreSQL - 秩序脑)
```
MongoDB原始数据 → 分析处理 → PostgreSQL规则应用
```

**处理内容：**
- 文本清洗和标准化
- 情感分析
- 关键词提取
- 主题分类
- 重要性评分

### 阶段三：向量化存储 (Zilliz - 神经脑)
```
处理后的数据 → 向量化 → Zilliz语义存储
```

**向量化内容：**
- 语义嵌入向量
- 处理后的文本
- 分析结果
- 检索元数据

## 🚀 实施方案

### 方案一：两阶段定时任务
```
青龙任务1: RSS → MongoDB (每30分钟)
青龙任务2: MongoDB → Zilliz (每小时)
```

### 方案二：N8N工作流
```
N8N工作流: RSS → MongoDB → 处理 → Zilliz (事件驱动)
```

### 方案三：混合架构 (推荐)
```
青龙面板: RSS → MongoDB (稳定的定时采集)
N8N工作流: MongoDB → Zilliz (智能的数据处理)
```

## 📋 具体实施步骤

### 步骤1: RSS到MongoDB
- 青龙面板负责RSS定时抓取
- 存储完整原始数据到MongoDB
- 保持数据完整性和可追溯性

### 步骤2: MongoDB到Zilliz的ETL
- N8N工作流监控MongoDB新数据
- 执行数据清洗和分析
- 生成向量并存储到Zilliz

### 步骤3: PostgreSQL记录决策
- 记录所有处理过程
- 存储分析规则和结果
- 提供审计追踪

## 💡 架构优势

### 数据完整性
- MongoDB保存原始数据，永不丢失
- 可以重新处理历史数据
- 支持数据回溯和验证

### 处理灵活性
- 可以调整向量化策略
- 支持多种分析算法
- 便于A/B测试不同方案

### 性能优化
- 分离存储和计算
- 避免重复向量化
- 支持批量处理

## 🔧 技术实现

### MongoDB集合设计
```javascript
// rss_raw_articles 集合
{
  _id: ObjectId,
  article_id: "hash_id",
  title: "文章标题",
  content: "完整内容",
  source_url: "RSS源URL",
  published_at: ISODate,
  collected_at: ISODate,
  raw_data: {}, // 原始RSS数据
  processed: false, // 是否已处理
  processing_status: "pending|processing|completed|failed"
}
```

### 处理状态追踪
```javascript
// processing_status 集合
{
  _id: ObjectId,
  article_id: "hash_id",
  processing_stage: "collected|analyzed|vectorized",
  created_at: ISODate,
  updated_at: ISODate,
  error_message: null,
  retry_count: 0
}
```

### Zilliz向量集合
```python
# 向量化后的数据结构
{
  "id": "article_hash",
  "document": "处理后的文本",
  "embedding": [0.1, 0.2, ...], # 384维向量
  "sentiment": 0.75,
  "topics": "bitcoin,trading,market",
  "importance_score": 0.85,
  "published_ts": 1703123456,
  "source_category": "crypto_news",
  "mongo_ref": "mongodb_object_id" # 引用原始数据
}
```

## 🎯 关键改进点

### 1. 数据溯源
每个Zilliz中的向量都可以追溯到MongoDB中的原始数据

### 2. 重新处理能力
可以基于新的算法重新处理历史数据

### 3. 质量控制
在MongoDB到Zilliz的过程中进行数据质量检查

### 4. 增量更新
只处理新增或更新的MongoDB数据

## 📊 监控指标

### 数据流监控
- MongoDB新增文章数量
- 处理成功/失败率
- Zilliz向量化完成率
- 端到端处理延迟

### 质量监控
- 重复数据检测
- 向量质量评估
- 语义一致性检查
- 数据完整性验证

## 🔄 故障恢复

### 处理失败恢复
- 标记失败的文章
- 支持重新处理
- 错误日志记录
- 自动重试机制

### 数据一致性
- MongoDB和Zilliz数据对账
- 定期一致性检查
- 数据修复工具
- 备份恢复策略
