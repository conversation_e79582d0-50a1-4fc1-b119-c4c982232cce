# 韩国服务器MCP生态系统部署指南

## 🏗️ 架构设计

```
韩国服务器 (Korean Server)
├── N8N工作流引擎 (端口: 5678)
├── MCP服务器集群 (端口: 8000-8010)
│   ├── crypto-mcp-server (8001)
│   ├── news-mcp-server (8002)
│   ├── finance-mcp-server (8003)
│   └── taigong-bridge-server (8004)
├── Nginx反向代理 (端口: 80/443)
└── Redis缓存 (端口: 6379)
```

## 📦 部署清单

### 1. 基础环境准备

```bash
# 更新系统
sudo apt update && sudo apt upgrade -y

# 安装Docker和Docker Compose
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh
sudo curl -L "https://github.com/docker/compose/releases/download/v2.20.0/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

# 安装Node.js (N8N需要)
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# 安装Python (MCP服务器需要)
sudo apt install python3 python3-pip python3-venv -y
```

### 2. 目录结构

```bash
mkdir -p /opt/taigong-mcp-ecosystem
cd /opt/taigong-mcp-ecosystem

# 创建目录结构
mkdir -p {n8n,mcp-servers,nginx,redis,logs}
mkdir -p mcp-servers/{crypto,news,finance,bridge}
```

### 3. Docker Compose配置

创建 `docker-compose.yml`:

```yaml
version: '3.8'

services:
  # N8N工作流引擎
  n8n:
    image: n8nio/n8n:latest
    container_name: taigong-n8n
    restart: unless-stopped
    ports:
      - "5678:5678"
    environment:
      - N8N_BASIC_AUTH_ACTIVE=true
      - N8N_BASIC_AUTH_USER=admin
      - N8N_BASIC_AUTH_PASSWORD=your_secure_password
      - N8N_HOST=0.0.0.0
      - N8N_PORT=5678
      - N8N_PROTOCOL=http
      - WEBHOOK_URL=https://your-korean-server.com
    volumes:
      - ./n8n:/home/<USER>/.n8n
      - /var/run/docker.sock:/var/run/docker.sock
    depends_on:
      - redis

  # Redis缓存
  redis:
    image: redis:7-alpine
    container_name: taigong-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - ./redis:/data
    command: redis-server --appendonly yes

  # 加密货币MCP服务器
  crypto-mcp:
    build: ./mcp-servers/crypto
    container_name: taigong-crypto-mcp
    restart: unless-stopped
    ports:
      - "8001:8000"
    environment:
      - COINGECKO_API_KEY=${COINGECKO_API_KEY}
      - REDIS_URL=redis://redis:6379
    depends_on:
      - redis

  # 新闻MCP服务器
  news-mcp:
    build: ./mcp-servers/news
    container_name: taigong-news-mcp
    restart: unless-stopped
    ports:
      - "8002:8000"
    environment:
      - REDIS_URL=redis://redis:6379
    depends_on:
      - redis

  # 金融数据MCP服务器
  finance-mcp:
    build: ./mcp-servers/finance
    container_name: taigong-finance-mcp
    restart: unless-stopped
    ports:
      - "8003:8000"
    environment:
      - ALPHA_VANTAGE_API_KEY=${ALPHA_VANTAGE_API_KEY}
      - POLYGON_API_KEY=${POLYGON_API_KEY}
      - REDIS_URL=redis://redis:6379
    depends_on:
      - redis

  # 太公心易桥接服务器
  taigong-bridge:
    build: ./mcp-servers/bridge
    container_name: taigong-bridge-mcp
    restart: unless-stopped
    ports:
      - "8004:8000"
    environment:
      - REDIS_URL=redis://redis:6379
      - N8N_WEBHOOK_URL=http://n8n:5678/webhook
    depends_on:
      - redis
      - n8n

  # Nginx反向代理
  nginx:
    image: nginx:alpine
    container_name: taigong-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
    depends_on:
      - n8n
      - crypto-mcp
      - news-mcp
      - finance-mcp
      - taigong-bridge

networks:
  default:
    name: taigong-network
```

### 4. 环境变量配置

创建 `.env` 文件:

```bash
# API密钥
COINGECKO_API_KEY=your_coingecko_pro_key
ALPHA_VANTAGE_API_KEY=your_alpha_vantage_key
POLYGON_API_KEY=your_polygon_key

# 服务器配置
KOREAN_SERVER_DOMAIN=your-korean-server.com
N8N_BASIC_AUTH_PASSWORD=your_secure_n8n_password

# 数据库配置
REDIS_PASSWORD=your_redis_password

# 安全配置
JWT_SECRET=your_jwt_secret_key
API_RATE_LIMIT=1000
```

### 5. MCP服务器实现

#### 加密货币MCP服务器 (`mcp-servers/crypto/Dockerfile`)

```dockerfile
FROM python:3.11-slim

WORKDIR /app

COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .

EXPOSE 8000

CMD ["python", "crypto_mcp_server.py"]
```

#### 加密货币MCP服务器 (`mcp-servers/crypto/crypto_mcp_server.py`)

```python
#!/usr/bin/env python3
"""
韩国服务器 - 加密货币MCP服务器
提供实时加密货币数据和分析服务
"""

import os
import asyncio
from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
import redis.asyncio as redis
from datetime import datetime
import httpx

app = FastAPI(title="Taigong Crypto MCP Server", version="1.0.0")

# CORS配置
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Redis连接
redis_client = None

@app.on_event("startup")
async def startup_event():
    global redis_client
    redis_url = os.getenv("REDIS_URL", "redis://localhost:6379")
    redis_client = redis.from_url(redis_url)

@app.on_event("shutdown")
async def shutdown_event():
    if redis_client:
        await redis_client.close()

@app.get("/health")
async def health_check():
    return {"status": "healthy", "service": "crypto-mcp", "timestamp": datetime.now()}

@app.get("/api/mcp/crypto-data")
async def get_crypto_data(symbols: str = None):
    """获取加密货币数据"""
    try:
        # 实现加密货币数据获取逻辑
        # 这里可以集成您之前实现的免费API聚合逻辑
        
        # 缓存检查
        cache_key = f"crypto_data:{symbols or 'all'}"
        cached_data = await redis_client.get(cache_key)
        
        if cached_data:
            return json.loads(cached_data)
        
        # 获取新数据
        data = await fetch_crypto_data(symbols)
        
        # 缓存5分钟
        await redis_client.setex(cache_key, 300, json.dumps(data))
        
        return data
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

async def fetch_crypto_data(symbols):
    """获取加密货币数据的具体实现"""
    # 这里集成您的免费API聚合逻辑
    return {"message": "crypto data implementation"}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
```

### 6. N8N工作流配置

#### 自动化工作流示例

1. **定时数据收集工作流**:
   - 每30分钟触发
   - 调用各MCP服务器收集数据
   - 数据异常时发送告警

2. **事件驱动分析工作流**:
   - Webhook接收外部事件
   - 触发太公心易分析
   - 结果推送到指定渠道

3. **智能监控工作流**:
   - 监控MCP服务器健康状态
   - 自动重启故障服务
   - 性能指标收集

### 7. Nginx配置

创建 `nginx/nginx.conf`:

```nginx
events {
    worker_connections 1024;
}

http {
    upstream n8n_backend {
        server n8n:5678;
    }
    
    upstream crypto_mcp {
        server crypto-mcp:8000;
    }
    
    upstream news_mcp {
        server news-mcp:8000;
    }
    
    upstream finance_mcp {
        server finance-mcp:8000;
    }
    
    upstream bridge_mcp {
        server taigong-bridge:8000;
    }

    server {
        listen 80;
        server_name your-korean-server.com;
        
        # N8N工作流引擎
        location /n8n/ {
            proxy_pass http://n8n_backend/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
        
        # MCP API端点
        location /api/mcp/crypto/ {
            proxy_pass http://crypto_mcp/api/mcp/;
        }
        
        location /api/mcp/news/ {
            proxy_pass http://news_mcp/api/mcp/;
        }
        
        location /api/mcp/finance/ {
            proxy_pass http://finance_mcp/api/mcp/;
        }
        
        location /api/mcp/bridge/ {
            proxy_pass http://bridge_mcp/api/mcp/;
        }
        
        # 健康检查
        location /health {
            return 200 "OK";
        }
    }
}
```

### 8. 部署命令

```bash
# 克隆配置到韩国服务器
scp -r jixia_academy/korean_server_deployment.md user@korean-server:/opt/

# 在韩国服务器上执行
cd /opt/taigong-mcp-ecosystem

# 构建和启动服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f
```

### 9. 监控和维护

```bash
# 服务健康检查脚本
#!/bin/bash
echo "检查太公心易MCP生态系统状态..."

# 检查N8N
curl -f http://localhost:5678/healthz || echo "N8N服务异常"

# 检查MCP服务器
for port in 8001 8002 8003 8004; do
    curl -f http://localhost:$port/health || echo "MCP服务器 $port 异常"
done

# 检查Redis
redis-cli ping || echo "Redis服务异常"
```

### 10. 安全配置

1. **防火墙设置**:
```bash
sudo ufw allow 22    # SSH
sudo ufw allow 80    # HTTP
sudo ufw allow 443   # HTTPS
sudo ufw enable
```

2. **SSL证书**:
```bash
# 使用Let's Encrypt
sudo apt install certbot python3-certbot-nginx
sudo certbot --nginx -d your-korean-server.com
```

3. **API认证**:
- JWT令牌认证
- API密钥管理
- 请求频率限制

## 🚀 使用方式

### 本地连接韩国服务器

```python
# 在本地cauldron项目中
from remote_mcp_client import TaigongRemoteIntegration

integration = TaigongRemoteIntegration(
    server_url="https://your-korean-server.com",
    api_key="your_api_key"
)

# 获取增强情报
intelligence = await integration.get_enhanced_market_intelligence(
    query="比特币价格分析"
)
```

### N8N工作流触发

```bash
# 通过webhook触发分析
curl -X POST https://your-korean-server.com/webhook/market-analysis \
  -H "Content-Type: application/json" \
  -d '{"symbol": "BTC", "event": "price_surge", "change": 15.5}'
```

## 💰 成本估算

- **韩国VPS**: $10-20/月
- **域名**: $10/年
- **SSL证书**: 免费 (Let's Encrypt)
- **API调用**: 大部分免费额度
- **总计**: ~$15/月

这样的架构既保持了本地项目的轻量级，又通过韩国服务器提供了强大的MCP生态系统支持！
