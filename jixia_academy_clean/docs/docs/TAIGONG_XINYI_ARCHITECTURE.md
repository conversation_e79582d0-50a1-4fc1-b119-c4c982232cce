# 太公心易-炼妖壶技术架构文档

## 📋 文档概述

本文档详细描述了太公心易-炼妖壶(Cauldron)项目的完整技术架构，包括系统设计、数据流处理和部署架构。

---

## 🏗️ 1. 整体技术架构

### 架构概览

太公心易采用现代化的微服务架构，结合传统易学智慧与现代AI技术，构建了全球首个易学智能投资分析系统。

```mermaid
graph TB
    %% 用户层
    subgraph "👥 用户接入层"
        WebUI[🌐 Web界面<br/>Streamlit Dashboard]
        MobileApp[📱 移动应用<br/>React Native]
        API[🔌 REST API<br/>FastAPI]
        CLI[💻 命令行工具<br/>Python CLI]
    end

    %% 网关层
    subgraph "🚪 API网关层"
        Gateway[🛡️ API Gateway<br/>Nginx + Rate Limiting]
        Auth[🔐 认证授权<br/>JWT + OAuth2]
        LoadBalancer[⚖️ 负载均衡<br/>HAProxy]
    end

    %% 核心服务层
    subgraph "🧠 核心智能服务层"
        subgraph "🔮 太公心易分析引擎"
            TaiYi[太乙观澜<br/>趋势分析服务]
            DunJia[遁甲择时<br/>时机分析服务]
            LiuRen[六壬察心<br/>情绪分析服务]
        end
        
        subgraph "🏛️ 稷下学宫辩论系统"
            BaXian[八仙过海<br/>多智能体辩论]
            SanQing[三清论道<br/>决策综合]
            Moderator[辩论主持<br/>流程控制]
        end
        
        subgraph "🤖 智能问答系统"
            QAEngine[问答引擎<br/>LLM集成]
            ContextBuilder[上下文构建器<br/>RAG Pipeline]
            ResponseGen[回答生成器<br/>多模型融合]
        end
    end

    %% 数据处理层
    subgraph "⚙️ 数据处理层"
        subgraph "📡 实时数据采集"
            RSSCollector[RSS采集器<br/>多源新闻]
            CryptoAPI[加密货币API<br/>CoinGecko/CoinCap]
            StockAPI[股票API<br/>Yahoo Finance]
            NewsAPI[新闻API<br/>多渠道聚合]
        end
        
        subgraph "🔄 数据处理管道"
            ETLPipeline[ETL管道<br/>数据清洗转换]
            Vectorizer[向量化服务<br/>Sentence Transformers]
            SentimentAnalyzer[情感分析<br/>SnowNLP + BERT]
            TopicExtractor[主题提取<br/>关键词+分类]
        end
    end

    %% 三脑架构数据层
    subgraph "🧠 三脑架构数据层"
        subgraph "🔍 神经脑 - 语义检索"
            Zilliz[(🎯 Zilliz Cloud<br/>向量数据库<br/>• 语义检索<br/>• 相似度计算<br/>• 实时向量更新)]
        end
        
        subgraph "📚 情报脑 - 原始存储"
            MongoDB[(🗂️ MongoDB Atlas<br/>文档数据库<br/>• RSS原始数据<br/>• 新闻元数据<br/>• 结构化情报)]
        end
        
        subgraph "⚖️ 秩序脑 - 规则逻辑"
            PostgreSQL[(🧾 PostgreSQL<br/>关系数据库<br/>• 分析规则<br/>• 用户画像<br/>• 决策日志)]
        end
    end

    %% 缓存和消息层
    subgraph "⚡ 缓存与消息层"
        Redis[(🔥 Redis<br/>缓存数据库<br/>• 查询缓存<br/>• 会话存储<br/>• 实时计数)]
        MessageQueue[📬 消息队列<br/>RabbitMQ/Kafka<br/>• 异步任务<br/>• 事件驱动]
    end

    %% 工作流和监控层
    subgraph "🔧 工作流与监控层"
        N8N[🔄 N8N工作流<br/>• 定时任务<br/>• 事件触发<br/>• 数据同步]
        Monitoring[📊 监控告警<br/>Prometheus + Grafana<br/>• 性能监控<br/>• 错误追踪]
        Logging[📝 日志系统<br/>ELK Stack<br/>• 操作日志<br/>• 审计追踪]
    end

    %% 外部服务层
    subgraph "🌐 外部服务层"
        OpenRouter[🤖 OpenRouter<br/>多LLM API聚合]
        MCPServers[🔌 MCP服务器<br/>• CoinGecko MCP<br/>• News MCP<br/>• Finance MCP]
        ThirdPartyAPIs[🔗 第三方API<br/>• 金融数据<br/>• 新闻源<br/>• 社交媒体]
    end

    %% 部署基础设施层
    subgraph "☁️ 基础设施层"
        subgraph "🇰🇷 韩国服务器集群"
            K8s[⚙️ Kubernetes<br/>容器编排]
            Docker[🐳 Docker<br/>容器化部署]
            CDN[🌍 CDN<br/>全球加速]
        end
        
        subgraph "🔒 安全与备份"
            SSL[🔐 SSL/TLS<br/>加密传输]
            Backup[💾 数据备份<br/>定期备份策略]
            Security[🛡️ 安全防护<br/>WAF + DDoS]
        end
    end

    %% 连接关系
    WebUI --> Gateway
    MobileApp --> Gateway
    API --> Gateway
    CLI --> Gateway
    
    Gateway --> Auth
    Gateway --> LoadBalancer
    LoadBalancer --> TaiYi
    LoadBalancer --> DunJia
    LoadBalancer --> LiuRen
    LoadBalancer --> BaXian
    LoadBalancer --> QAEngine
    
    TaiYi --> Zilliz
    TaiYi --> MongoDB
    TaiYi --> PostgreSQL
    
    DunJia --> Zilliz
    DunJia --> PostgreSQL
    
    LiuRen --> MongoDB
    LiuRen --> Redis
    
    BaXian --> OpenRouter
    SanQing --> OpenRouter
    QAEngine --> OpenRouter
    
    ContextBuilder --> Zilliz
    ContextBuilder --> MongoDB
    
    RSSCollector --> ETLPipeline
    CryptoAPI --> ETLPipeline
    StockAPI --> ETLPipeline
    NewsAPI --> ETLPipeline
    
    ETLPipeline --> Vectorizer
    Vectorizer --> Zilliz
    ETLPipeline --> MongoDB
    
    SentimentAnalyzer --> MongoDB
    TopicExtractor --> MongoDB
    
    N8N --> RSSCollector
    N8N --> MessageQueue
    MessageQueue --> ETLPipeline
    
    MCPServers --> CryptoAPI
    ThirdPartyAPIs --> NewsAPI
    
    Monitoring --> K8s
    Logging --> K8s
    
    %% 样式定义
    classDef userLayer fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef coreService fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef dataLayer fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px
    classDef infrastructure fill:#fff3e0,stroke:#e65100,stroke-width:2px
    classDef external fill:#fce4ec,stroke:#880e4f,stroke-width:2px
    
    class WebUI,MobileApp,API,CLI userLayer
    class TaiYi,DunJia,LiuRen,BaXian,SanQing,QAEngine coreService
    class Zilliz,MongoDB,PostgreSQL,Redis dataLayer
    class K8s,Docker,CDN infrastructure
    class OpenRouter,MCPServers,ThirdPartyAPIs external
```

### 架构特点

#### 🧠 三脑架构设计
- **神经脑 (Zilliz)**: 负责语义理解和相似度检索
- **情报脑 (MongoDB)**: 存储原始文档和结构化情报
- **秩序脑 (PostgreSQL)**: 管理规则逻辑和决策审计

#### 🔮 太公心易核心算法
- **太乙观澜**: 基于九宫八卦的趋势分析算法
- **遁甲择时**: 时间和空间维度的择时分析
- **六壬察心**: 市场情绪和心理分析

#### 🏛️ 稷下学宫辩论系统
- **八仙过海**: 多智能体协作辩论
- **三清论道**: 决策综合和共识构建

---

## 🔄 2. 数据流处理架构

### 数据处理流程

系统采用实时数据处理架构，从多源数据采集到智能分析输出的完整链路。

```mermaid
flowchart TD
    %% 数据源层
    subgraph "📡 数据源层"
        RSS1[📰 财经RSS<br/>新浪财经/网易财经]
        RSS2[🌐 国际新闻<br/>Reuters/Bloomberg]
        RSS3[💰 加密货币<br/>CoinDesk/CoinTelegraph]
        API1[📊 股票API<br/>Yahoo Finance]
        API2[🪙 加密API<br/>CoinGecko/CoinCap]
        API3[📈 宏观数据<br/>Alpha Vantage]
    end

    %% 数据采集层
    subgraph "🔄 数据采集层"
        Scheduler[⏰ 定时调度器<br/>N8N Cron Jobs]
        RSSParser[📖 RSS解析器<br/>Feedparser]
        APIClient[🔌 API客户端<br/>HTTP Client Pool]
        DataValidator[✅ 数据验证器<br/>Schema Validation]
    end

    %% 数据处理层
    subgraph "⚙️ 数据处理层"
        subgraph "🧹 数据清洗"
            Deduplicator[🔍 去重器<br/>MD5 Hash]
            TextCleaner[📝 文本清洗<br/>正则表达式]
            LanguageDetector[🌍 语言检测<br/>langdetect]
        end
        
        subgraph "🔬 数据分析"
            SentimentEngine[😊 情感分析<br/>SnowNLP + BERT]
            KeywordExtractor[🏷️ 关键词提取<br/>jieba + TF-IDF]
            CategoryClassifier[📂 分类器<br/>机器学习模型]
        end
        
        subgraph "🧠 向量化处理"
            TextEmbedding[📊 文本向量化<br/>Sentence-BERT]
            DimensionReduction[📉 降维处理<br/>PCA/UMAP]
            VectorNormalization[⚖️ 向量标准化<br/>L2 Normalization]
        end
    end

    %% 三脑存储层
    subgraph "🧠 三脑存储架构"
        subgraph "🎯 神经脑 (Zilliz)"
            VectorIndex[📊 向量索引<br/>HNSW/IVF]
            SemanticSearch[🔍 语义检索<br/>Cosine Similarity]
            VectorCache[⚡ 向量缓存<br/>Hot Data]
        end
        
        subgraph "🗂️ 情报脑 (MongoDB)"
            RawDocuments[📄 原始文档<br/>Full Text]
            Metadata[📋 元数据<br/>Source/Time/Tags]
            FullTextIndex[🔎 全文索引<br/>Text Search]
        end
        
        subgraph "⚖️ 秩序脑 (PostgreSQL)"
            AnalysisRules[📏 分析规则<br/>Business Logic]
            UserProfiles[👤 用户画像<br/>Preferences]
            DecisionLogs[📝 决策日志<br/>Audit Trail]
        end
    end

    %% 智能分析层
    subgraph "🔮 太公心易智能分析层"
        subgraph "📊 量化分析模块"
            TrendAnalyzer[📈 趋势分析<br/>太乙观澜算法]
            TimingAnalyzer[⏰ 择时分析<br/>遁甲择时算法]
            SentimentAnalyzer[💭 情绪分析<br/>六壬察心算法]
        end
        
        subgraph "🏛️ 稷下学宫辩论"
            AgentOrchestrator[🎭 智能体编排<br/>八仙角色分配]
            DebateEngine[💬 辩论引擎<br/>多轮对话]
            ConsensusBuilder[🤝 共识构建<br/>三清论道]
        end
        
        subgraph "🤖 智能问答"
            QueryUnderstanding[❓ 查询理解<br/>Intent Recognition]
            ContextRetrieval[📚 上下文检索<br/>RAG Pipeline]
            ResponseGeneration[💡 回答生成<br/>LLM Integration]
        end
    end

    %% 应用服务层
    subgraph "🚀 应用服务层"
        WebAPI[🌐 Web API<br/>FastAPI]
        RealtimeWS[⚡ 实时推送<br/>WebSocket]
        ReportGenerator[📊 报告生成<br/>PDF/HTML]
        AlertSystem[🚨 预警系统<br/>实时监控]
    end

    %% 用户界面层
    subgraph "👥 用户界面层"
        Dashboard[📱 控制面板<br/>Streamlit]
        ChatInterface[💬 对话界面<br/>Chat UI]
        AnalysisView[📊 分析视图<br/>Charts & Graphs]
        SettingsPanel[⚙️ 设置面板<br/>User Config]
    end

    %% 数据流连接
    RSS1 --> Scheduler
    RSS2 --> Scheduler
    RSS3 --> Scheduler
    API1 --> APIClient
    API2 --> APIClient
    API3 --> APIClient
    
    Scheduler --> RSSParser
    APIClient --> DataValidator
    RSSParser --> DataValidator
    
    DataValidator --> Deduplicator
    Deduplicator --> TextCleaner
    TextCleaner --> LanguageDetector
    
    LanguageDetector --> SentimentEngine
    LanguageDetector --> KeywordExtractor
    LanguageDetector --> CategoryClassifier
    
    SentimentEngine --> TextEmbedding
    KeywordExtractor --> TextEmbedding
    CategoryClassifier --> TextEmbedding
    
    TextEmbedding --> DimensionReduction
    DimensionReduction --> VectorNormalization
    
    VectorNormalization --> VectorIndex
    TextCleaner --> RawDocuments
    SentimentEngine --> Metadata
    
    VectorIndex --> SemanticSearch
    RawDocuments --> FullTextIndex
    
    SemanticSearch --> TrendAnalyzer
    FullTextIndex --> TrendAnalyzer
    AnalysisRules --> TrendAnalyzer
    
    TrendAnalyzer --> AgentOrchestrator
    TimingAnalyzer --> AgentOrchestrator
    SentimentAnalyzer --> AgentOrchestrator
    
    AgentOrchestrator --> DebateEngine
    DebateEngine --> ConsensusBuilder
    
    ConsensusBuilder --> QueryUnderstanding
    SemanticSearch --> ContextRetrieval
    ContextRetrieval --> ResponseGeneration
    
    ResponseGeneration --> WebAPI
    WebAPI --> RealtimeWS
    WebAPI --> ReportGenerator
    WebAPI --> AlertSystem
    
    WebAPI --> Dashboard
    RealtimeWS --> ChatInterface
    ReportGenerator --> AnalysisView
    AlertSystem --> SettingsPanel

    %% 样式定义
    classDef dataSource fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    classDef processing fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    classDef storage fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    classDef intelligence fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    classDef application fill:#fce4ec,stroke:#c2185b,stroke-width:2px
    classDef interface fill:#f1f8e9,stroke:#689f38,stroke-width:2px
    
    class RSS1,RSS2,RSS3,API1,API2,API3 dataSource
    class Scheduler,RSSParser,APIClient,DataValidator,Deduplicator,TextCleaner,LanguageDetector,SentimentEngine,KeywordExtractor,CategoryClassifier,TextEmbedding,DimensionReduction,VectorNormalization processing
    class VectorIndex,SemanticSearch,VectorCache,RawDocuments,Metadata,FullTextIndex,AnalysisRules,UserProfiles,DecisionLogs storage
    class TrendAnalyzer,TimingAnalyzer,SentimentAnalyzer,AgentOrchestrator,DebateEngine,ConsensusBuilder,QueryUnderstanding,ContextRetrieval,ResponseGeneration intelligence
    class WebAPI,RealtimeWS,ReportGenerator,AlertSystem application
    class Dashboard,ChatInterface,AnalysisView,SettingsPanel interface
```

### 数据处理特点

#### 📡 多源数据采集
- **RSS源**: 财经新闻、国际资讯、加密货币新闻
- **API接口**: 股票数据、加密货币价格、宏观经济数据
- **实时性**: 分钟级数据更新频率

#### 🔄 智能数据处理
- **去重算法**: 基于内容哈希的智能去重
- **情感分析**: 中英文混合的情感识别
- **向量化**: 384维优化向量，平衡性能与精度

#### 🧠 三脑协作存储
- **热数据**: Zilliz向量检索，毫秒级响应
- **温数据**: MongoDB文档存储，秒级查询
- **冷数据**: PostgreSQL关系存储，分析审计

---

## ☁️ 3. 部署基础设施架构

### 云原生部署方案

采用Kubernetes容器编排，实现高可用、可扩展的云原生部署。

```mermaid
graph TB
    %% 用户访问层
    subgraph "🌍 全球用户访问"
        Users[👥 全球用户<br/>Web/Mobile/API]
        CDN[🌐 CDN加速<br/>CloudFlare/AWS CloudFront]
    end

    %% 负载均衡层
    subgraph "⚖️ 负载均衡层"
        LB[🔄 负载均衡器<br/>Nginx/HAProxy]
        SSL[🔒 SSL终端<br/>Let's Encrypt]
        WAF[🛡️ Web防火墙<br/>DDoS防护]
    end

    %% 韩国服务器集群
    subgraph "🇰🇷 韩国服务器集群"
        subgraph "🐳 容器化应用层"
            K8sMaster[⚙️ K8s Master<br/>集群管理]

            subgraph "📱 前端服务 Pod"
                StreamlitPod[🌐 Streamlit Pod<br/>Web界面]
                APIPod[🔌 FastAPI Pod<br/>REST API]
            end

            subgraph "🧠 核心服务 Pod"
                TaigongPod[🔮 太公心易 Pod<br/>分析引擎]
                JixiaPod[🏛️ 稷下学宫 Pod<br/>辩论系统]
                QAPod[🤖 问答系统 Pod<br/>RAG引擎]
            end

            subgraph "⚙️ 数据服务 Pod"
                RSSPod[📡 RSS采集 Pod<br/>数据采集]
                ETLPod[🔄 ETL处理 Pod<br/>数据处理]
                N8NPod[🔗 N8N工作流 Pod<br/>自动化]
            end
        end

        subgraph "💾 本地存储"
            RedisCluster[🔥 Redis集群<br/>缓存+会话]
            LocalFiles[📁 本地文件<br/>日志+备份]
        end
    end

    %% 云数据库层
    subgraph "☁️ 云数据库服务"
        subgraph "🎯 Zilliz Cloud"
            ZillizPrimary[🧠 主向量库<br/>实时检索]
            ZillizBackup[💾 备份向量库<br/>灾难恢复]
        end

        subgraph "🗂️ MongoDB Atlas"
            MongoCluster[📚 MongoDB集群<br/>文档存储]
            MongoBackup[💾 自动备份<br/>Point-in-time]
        end

        subgraph "🐘 Heroku PostgreSQL"
            PostgresPrimary[⚖️ 主数据库<br/>关系数据]
            PostgresReplica[📖 只读副本<br/>查询分离]
        end
    end

    %% 外部服务层
    subgraph "🌐 外部API服务"
        OpenRouterAPI[🤖 OpenRouter<br/>LLM API聚合]
        CoinGeckoAPI[🪙 CoinGecko<br/>加密货币数据]
        YahooAPI[📊 Yahoo Finance<br/>股票数据]
        NewsAPIs[📰 新闻API<br/>多源新闻]
    end

    %% 监控和运维层
    subgraph "📊 监控运维层"
        subgraph "📈 监控系统"
            Prometheus[📊 Prometheus<br/>指标收集]
            Grafana[📈 Grafana<br/>可视化面板]
            AlertManager[🚨 告警管理<br/>通知系统]
        end

        subgraph "📝 日志系统"
            ElasticSearch[🔍 ElasticSearch<br/>日志存储]
            Logstash[📝 Logstash<br/>日志处理]
            Kibana[📊 Kibana<br/>日志分析]
        end

        subgraph "🔧 运维工具"
            GitLabCI[🔄 GitLab CI/CD<br/>自动部署]
            Backup[💾 备份系统<br/>定时备份]
            Security[🔒 安全扫描<br/>漏洞检测]
        end
    end

    %% 开发环境
    subgraph "💻 开发环境"
        LocalDev[🖥️ 本地开发<br/>Docker Compose]
        TestEnv[🧪 测试环境<br/>Staging]
        GitRepo[📚 Git仓库<br/>GitHub/GitLab]
    end

    %% 连接关系
    Users --> CDN
    CDN --> LB
    LB --> SSL
    SSL --> WAF
    WAF --> K8sMaster

    K8sMaster --> StreamlitPod
    K8sMaster --> APIPod
    K8sMaster --> TaigongPod
    K8sMaster --> JixiaPod
    K8sMaster --> QAPod
    K8sMaster --> RSSPod
    K8sMaster --> ETLPod
    K8sMaster --> N8NPod

    StreamlitPod --> RedisCluster
    APIPod --> RedisCluster
    TaigongPod --> ZillizPrimary
    TaigongPod --> MongoCluster
    TaigongPod --> PostgresPrimary

    JixiaPod --> OpenRouterAPI
    QAPod --> OpenRouterAPI
    RSSPod --> NewsAPIs
    ETLPod --> CoinGeckoAPI
    ETLPod --> YahooAPI

    ZillizPrimary --> ZillizBackup
    MongoCluster --> MongoBackup
    PostgresPrimary --> PostgresReplica

    K8sMaster --> Prometheus
    Prometheus --> Grafana
    Prometheus --> AlertManager

    K8sMaster --> Logstash
    Logstash --> ElasticSearch
    ElasticSearch --> Kibana

    GitRepo --> GitLabCI
    GitLabCI --> K8sMaster

    LocalDev --> TestEnv
    TestEnv --> GitRepo

    Backup --> ZillizBackup
    Backup --> MongoBackup
    Backup --> LocalFiles

    Security --> K8sMaster

    %% 网络分区
    subgraph "🌐 网络架构"
        PublicSubnet[🌍 公网子网<br/>负载均衡器]
        PrivateSubnet[🔒 私网子网<br/>应用服务]
        DatabaseSubnet[🗄️ 数据库子网<br/>数据存储]
    end

    LB -.-> PublicSubnet
    K8sMaster -.-> PrivateSubnet
    ZillizPrimary -.-> DatabaseSubnet
    MongoCluster -.-> DatabaseSubnet
    PostgresPrimary -.-> DatabaseSubnet

    %% 样式定义
    classDef userAccess fill:#e3f2fd,stroke:#1976d2,stroke-width:3px
    classDef loadBalancer fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    classDef application fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    classDef database fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    classDef external fill:#fce4ec,stroke:#c2185b,stroke-width:2px
    classDef monitoring fill:#f1f8e9,stroke:#689f38,stroke-width:2px
    classDef development fill:#e0f2f1,stroke:#00695c,stroke-width:2px
    classDef network fill:#fafafa,stroke:#424242,stroke-width:1px,stroke-dasharray: 5 5

    class Users,CDN userAccess
    class LB,SSL,WAF loadBalancer
    class K8sMaster,StreamlitPod,APIPod,TaigongPod,JixiaPod,QAPod,RSSPod,ETLPod,N8NPod,RedisCluster,LocalFiles application
    class ZillizPrimary,ZillizBackup,MongoCluster,MongoBackup,PostgresPrimary,PostgresReplica database
    class OpenRouterAPI,CoinGeckoAPI,YahooAPI,NewsAPIs external
    class Prometheus,Grafana,AlertManager,ElasticSearch,Logstash,Kibana,GitLabCI,Backup,Security monitoring
    class LocalDev,TestEnv,GitRepo development
    class PublicSubnet,PrivateSubnet,DatabaseSubnet network
```

### 部署特点

#### 🐳 容器化架构
- **微服务拆分**: 每个功能模块独立部署
- **弹性伸缩**: 根据负载自动扩缩容
- **故障隔离**: 单个服务故障不影响整体

#### 🌍 全球化部署
- **CDN加速**: 全球用户访问优化
- **多地域备份**: 数据安全和灾难恢复
- **负载均衡**: 智能流量分发

#### 📊 完善监控
- **实时监控**: Prometheus + Grafana
- **日志分析**: ELK Stack
- **自动告警**: 异常情况及时通知

---

## 🎯 4. 技术亮点总结

### 🔮 创新性
1. **全球首创**: 易学+AI的投资分析系统
2. **三脑架构**: 多数据库协作的创新模式
3. **文化融合**: 传统文化的现代化技术实现

### 🚀 技术优势
1. **高性能**: Zilliz毫秒级向量检索
2. **高可用**: 99.9%服务可用性保证
3. **可扩展**: 支持百万级用户并发

### 💰 商业价值
1. **明确定位**: 华人投资者市场
2. **分层服务**: 免费版到企业版
3. **国际化**: 面向全球市场

### 🤝 合作价值
1. **技术案例**: Zilliz在金融领域的标杆应用
2. **市场拓展**: 进入中文金融科技市场
3. **品牌提升**: 支持创新文化+AI项目

---

## 📞 联系信息

- **项目地址**: https://github.com/jingminzhang/cauldron
- **技术文档**: 详见项目README和本架构文档
- **演示视频**: 可安排在线技术演示
- **合作洽谈**: 期待与Zilliz深度技术合作

---

**太公心易 × Zilliz = 传统智慧 × 现代技术的完美结合**

*让我们一起推动AI技术在中华文化传承中的创新应用！* 🚀
