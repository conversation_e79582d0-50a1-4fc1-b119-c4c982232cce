# 太公心易-炼妖壶 系统概览架构

## 🎯 系统概述

太公心易-炼妖壶是全球首个将传统易学智慧与现代向量检索技术相结合的智能投资分析系统。

### 核心数据指标
- **代码量**: 15,000+ 行Python代码
- **模块数**: 20+ 核心功能模块  
- **数据源**: 13+ RSS源 + API集成
- **向量维度**: 384维优化设计
- **预期规模**: 100万+ 文档向量

---

## 🏗️ 整体系统架构

### 七层架构设计

```mermaid
graph TB
    subgraph "👥 用户接入层"
        WebUI[🌐 Web界面<br/>Streamlit Dashboard]
        MobileApp[📱 移动应用<br/>React Native]
        API[🔌 REST API<br/>FastAPI]
        CLI[💻 命令行工具<br/>Python CLI]
    end

    subgraph "🚪 API网关层"
        Gateway[🛡️ API Gateway<br/>Nginx + Rate Limiting]
        Auth[🔐 认证授权<br/>JWT + OAuth2]
        LoadBalancer[⚖️ 负载均衡<br/>HAProxy]
    end

    subgraph "🧠 核心智能服务层"
        TaigongEngine[🔮 太公心易分析引擎<br/>太乙观澜 + 遁甲择时 + 六壬察心]
        JixiaDebate[🏛️ 稷下学宫辩论系统<br/>八仙过海 + 三清论道]
        QASystem[🤖 智能问答系统<br/>RAG Pipeline + LLM集成]
    end

    subgraph "⚙️ 数据处理层"
        DataCollection[📡 实时数据采集<br/>RSS + API多源聚合]
        DataProcessing[🔄 数据处理管道<br/>ETL + 向量化 + 情感分析]
    end

    subgraph "🧠 三脑架构数据层"
        Zilliz[🎯 神经脑 - Zilliz Cloud<br/>语义检索 + 向量存储]
        MongoDB[🗂️ 情报脑 - MongoDB Atlas<br/>文档存储 + 原始数据]
        PostgreSQL[⚖️ 秩序脑 - PostgreSQL<br/>规则逻辑 + 决策审计]
    end

    subgraph "⚡ 缓存与消息层"
        Redis[🔥 Redis缓存<br/>查询缓存 + 会话存储]
        MessageQueue[📬 消息队列<br/>异步任务 + 事件驱动]
    end

    subgraph "☁️ 基础设施层"
        K8s[⚙️ Kubernetes<br/>容器编排 + 自动扩缩容]
        Monitoring[📊 监控告警<br/>Prometheus + Grafana]
        Security[🔒 安全防护<br/>SSL + WAF + DDoS]
    end

    %% 连接关系
    WebUI --> Gateway
    MobileApp --> Gateway
    API --> Gateway
    CLI --> Gateway
    
    Gateway --> Auth
    Gateway --> LoadBalancer
    
    LoadBalancer --> TaigongEngine
    LoadBalancer --> JixiaDebate
    LoadBalancer --> QASystem
    
    TaigongEngine --> Zilliz
    TaigongEngine --> MongoDB
    TaigongEngine --> PostgreSQL
    
    JixiaDebate --> Redis
    QASystem --> Zilliz
    
    DataCollection --> DataProcessing
    DataProcessing --> Zilliz
    DataProcessing --> MongoDB
    
    MessageQueue --> DataProcessing
    
    K8s --> TaigongEngine
    K8s --> JixiaDebate
    K8s --> QASystem
    
    Monitoring --> K8s
    Security --> Gateway

    %% 样式定义
    classDef userLayer fill:#e1f5fe,stroke:#01579b,stroke-width:3px
    classDef gatewayLayer fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef serviceLayer fill:#e8f5e8,stroke:#1b5e20,stroke-width:3px
    classDef dataLayer fill:#fff3e0,stroke:#e65100,stroke-width:2px
    classDef storageLayer fill:#fce4ec,stroke:#880e4f,stroke-width:3px
    classDef cacheLayer fill:#f1f8e9,stroke:#689f38,stroke-width:2px
    classDef infraLayer fill:#e0f2f1,stroke:#00695c,stroke-width:2px
    
    class WebUI,MobileApp,API,CLI userLayer
    class Gateway,Auth,LoadBalancer gatewayLayer
    class TaigongEngine,JixiaDebate,QASystem serviceLayer
    class DataCollection,DataProcessing dataLayer
    class Zilliz,MongoDB,PostgreSQL storageLayer
    class Redis,MessageQueue cacheLayer
    class K8s,Monitoring,Security infraLayer
```

---

## 🎯 核心特色

### 🔮 太公心易分析引擎
- **太乙观澜**: 基于九宫八卦的趋势分析算法
- **遁甲择时**: 时间和空间维度的择时分析
- **六壬察心**: 市场情绪和心理分析

### 🏛️ 稷下学宫辩论系统
- **八仙过海**: 多智能体协作辩论
- **三清论道**: 决策综合和共识构建

### 📡 实时RAG系统
- **RSS新闻流**: 实时向量化和语义检索
- **多源数据**: 加密货币、股票、宏观数据
- **智能问答**: 基于实时新闻的增强生成

---

## 📊 技术指标

### 性能要求
| 指标 | 当前需求 | 峰值预期 |
|------|----------|----------|
| 查询QPS | 100-500 | 1000+ |
| 响应时间 | <200ms | <100ms |
| 并发用户 | 1000+ | 10000+ |
| 数据更新 | 1000+/小时 | 5000+/小时 |

### 可用性指标
- **服务可用性**: 99.9%
- **数据一致性**: 最终一致性
- **故障恢复**: <5分钟
- **备份策略**: 实时备份 + 定期快照

---

## 🌍 部署架构

### 全球化部署
- **韩国服务器集群**: 主要计算节点
- **CDN全球加速**: CloudFlare/AWS CloudFront
- **多地域备份**: 数据安全保障

### 云原生特性
- **容器化部署**: Docker + Kubernetes
- **自动扩缩容**: 基于负载的弹性伸缩
- **微服务架构**: 服务独立部署和升级

---

## 🔗 相关文档

- **下一步**: [三脑架构设计](02-three-brain-architecture.md)
- **数据流程**: [数据流架构](03-data-flow-architecture.md)
- **部署详情**: [部署架构](05-deployment-architecture.md)
