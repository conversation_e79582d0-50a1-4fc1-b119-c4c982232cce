# Heroku Docker & Tailscale 部署指南

## 概述

本指南详细说明了如何将集成了 Tailscale 的 Docker化应用部署到 Heroku，并通过 GitHub Actions 实现自动化部署。此方案旨在为应用提供一个稳定的出口 IP 地址，以访问需要 IP 白名单的外部服务（如 IB Gateway）。

## 核心架构

- **Docker**: 应用被容器化，确保了环境的一致性。
- **Tailscale**: 在容器内运行，为应用提供一个基于 Tailscale 私有网络的稳定 IP。
- **GitHub Actions**: 自动化构建 Docker 镜像并将其部署到 Heroku 的 `container` stack。
- **SOCKS5 代理**: 应用通过 Tailscale 提供的 SOCKS5 代理（`localhost:1055`）将流量路由出去。

## 部署步骤

### 1. 准备工作

**a. Heroku App 创建与配置**

```bash
# 登录 Heroku CLI
heroku login -i

# 创建一个新的 Heroku 应用
# 将 'your-app-name' 替换为你的应用名
heroku create your-app-name

# 关键：将应用的 stack 设置为 'container'，以支持 Docker 部署
heroku stack:set container -a your-app-name
```

**b. 生成 Tailscale Auth Key**

为了让 Heroku 容器能自动加入你的 Tailscale 网络，你需要一个授权密钥。

1.  访问 [Tailscale Admin Console](https://login.tailscale.com/admin/settings/keys)。
2.  点击 "Generate auth key..."。
3.  **重要配置**：
    *   **Reusable**: 选中此项，以便在多次部署或重启后密钥依然有效。
    *   **Ephemeral**: 选中此项，让容器下线后自动从你的网络中移除，保持网络整洁。
    *   **Tags**: （可选但推荐）添加一个标签，如 `tag:heroku-runner`，方便后续在 ACL 中管理权限。
4.  生成并**立即复制**这个密钥（`tskey-auth-...`），它只会显示一次。

### 2. 配置 GitHub & Heroku Secrets

为了让 GitHub Actions 能够安全地访问 Heroku 和 Tailscale，你需要在 GitHub 仓库中配置 Secrets。

进入你的 GitHub 仓库 -> `Settings` -> `Secrets and variables` -> `Actions`，然后添加以下 `Repository secrets`：

-   `HEROKU_EMAIL`: 你注册 Heroku 时使用的邮箱地址。
-   `HEROKU_API_KEY`: 在你的 [Heroku Account Settings](https://dashboard.heroku.com/account) 页面找到 API Key。
-   `HEROKU_APP_NAME`: 你在上面创建的 Heroku 应用名称 (`your-app-name`)。
-   `TAILSCALE_AUTHKEY`: 你刚刚生成的 Tailscale 授权密钥。

### 3. 触发自动部署

现在，一切准备就绪。你只需要将代码（包括 `Dockerfile`, `start.sh` 和 `.github/workflows/heroku-deploy.yml`）推送到 GitHub 的 `main` 分支即可。

```bash
git add .
git commit -m "feat: Add Tailscale integration and Heroku Docker deployment"
git push origin main
```

GitHub Actions 将会自动被触发，执行以下操作：
1.  从你的仓库拉取最新代码。
2.  根据 `Dockerfile` 构建 Docker 镜像。
3.  将构建好的镜像推送到 Heroku Container Registry。
4.  在 Heroku 上发布新版本，应用将使用 `start.sh` 脚本启动。

### 4. 获取 Tailscale IP 并配置 IB Gateway

1.  部署成功后，在 [Tailscale Admin Console](https://login.tailscale.com/admin/machines) 的设备列表中，你会看到一个新设备，其主机名应为 `cauldron`（或你在 `start.sh` 中设置的其他名称）。
2.  复制这个设备对应的 **Tailscale IP 地址**（通常是 `100.x.x.x` 格式）。
3.  登录你的 IB Gateway，将这个 IP 地址添加到信任 IP 列表中。

至此，你的 Heroku 应用就可以通过 Tailscale 的稳定 IP 安全地连接到 IB Gateway 了。

## 文件结构变更

新的部署方案引入了以下关键文件：

```
cauldron/
├── Dockerfile                # 定义了如何构建包含 Tailscale 和应用的 Docker 镜像
├── start.sh                  # 容器启动脚本，负责启动 Tailscale 和应用
├── requirements.txt          # Python 依赖 (包含 PySocks)
├── .github/workflows/
│   └── heroku-deploy.yml     # GitHub Actions 自动化部署工作流
└── scripts/
    └── ib_market_data_fetcher.py # 已修改为支持 SOCKS5 代理
```

## 环境变量说明

| 变量名 | 说明 | 设置位置 | 必需 |
|--------|------|----------|------|
| `TAILSCALE_AUTHKEY` | 用于 Tailscale 自动认证的临时密钥。 | GitHub Secrets | 是 |
| `DATABASE_URL` | PostgreSQL 连接字符串。 | Heroku Config Vars (自动) | 否 |
| `OPENROUTER_API_KEY_*` | OpenRouter API 密钥。 | Heroku Config Vars | 否 |
| `PORT` | 服务端口。 | Heroku Config Vars (自动) | 是 |

## 故障排除

### 1. 数据库连接问题

```bash
# 检查数据库状态
heroku pg:info

# 重置数据库
heroku pg:reset DATABASE_URL
```

### 2. 应用启动失败

```bash
# 查看详细日志
heroku logs --tail

# 重启应用
heroku restart
```

### 3. 依赖安装问题

```bash
# 清除构建缓存
heroku builds:cache:purge

# 重新部署
git commit --allow-empty -m "Rebuild"
git push heroku main

# 查看构建日志
heroku logs --tail --app your-app-name

# 检查requirements.txt格式
# 确保没有本地路径依赖
# 注意：PyQt5等GUI库在Heroku无头环境中会失败
```

### Python版本配置
- 使用`.python-version`文件而非`runtime.txt`
- 仅指定主版本号（如`3.10`），避免指定补丁版本
- 这样可以自动获取最新的安全更新

## 本地开发

```bash
# 安装依赖
pip install -r requirements.txt

# 启动应用
streamlit run app.py

# 访问应用
# http://localhost:8501
```

## 更新部署

```bash
# 更新代码
git add .
git commit -m "Update application"
git push heroku main

# 查看部署状态
heroku ps
```

## 监控和维护

### Heroku内置监控工具

```bash
# 查看应用指标
heroku metrics

# 查看实时日志
heroku logs --tail

# 查看数据库使用情况
heroku pg:info

# 备份数据库
heroku pg:backups:capture

# 查看应用状态
heroku ps

# 重启应用
heroku restart
```

### 应用内置监控功能

本系统已集成监控功能，无需额外配置：

- **性能监控**: 自动记录函数执行时间和调用次数
- **健康检查**: 监控CPU、内存、磁盘使用情况
- **错误追踪**: 自动记录和统计错误信息
- **实时指标**: 在Streamlit侧边栏查看系统状态

#### 使用方法

1. 在Streamlit应用侧边栏点击"🔍 系统健康检查"
2. 点击"📊 性能指标"查看应用性能数据
3. 查看Heroku日志获取详细监控信息：
   ```bash
   heroku logs --tail | grep "监控"
   ```

#### 监控告警

系统会自动记录以下情况：
- CPU使用率超过80%
- 内存使用率超过80%
- 磁盘使用率超过80%
- 函数执行时间超过1秒
- 应用错误和异常

### 性能优化建议

```bash
# 查看慢查询日志
heroku logs --tail | grep "慢查询警告"

# 监控内存使用
heroku logs --tail | grep "内存使用率"

# 查看错误统计
heroku logs --tail | grep "执行失败"
```

---

**注意**: 确保在部署前设置所有必需的环境变量，特别是OpenRouter API密钥，否则AI分析功能将无法正常工作。