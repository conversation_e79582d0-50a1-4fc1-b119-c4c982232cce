# 兜率宫 N8N 工作流配置

## 🏰 兜率宫概念设计

### 神话映射
- **太上老君** = N8N工作流引擎
- **兜率宫** = N8N工作流平台  
- **炼丹炉** = 各种数据处理节点
- **仙丹** = 处理后的智能分析结果
- **炼丹配方** = N8N工作流模板

---

## 🔥 核心炼丹工作流

### 1. RSS仙丹炼制工作流

```mermaid
flowchart TD
    A[📡 Webhook触发<br/>tusita-palace/rss_elixir] --> B[📰 RSS采集器<br/>多源新闻抓取]
    B --> C[🧹 数据清洗<br/>去重+格式化]
    C --> D[😊 情感分析<br/>SnowNLP+BERT]
    D --> E[🏷️ 关键词提取<br/>jieba+TF-IDF]
    E --> F[🧠 向量化<br/>Sentence-BERT]
    F --> G[💾 存储到MongoDB<br/>原始文档]
    F --> H[🎯 存储到Zilliz<br/>向量数据]
    G --> I[📊 记录到PostgreSQL<br/>处理日志]
    H --> I
    I --> J[✅ 返回仙丹<br/>智能情报文档]

    classDef triggerStyle fill:#ff6b6b,stroke:#d63031,stroke-width:2px,color:#fff
    classDef processStyle fill:#74b9ff,stroke:#0984e3,stroke-width:2px,color:#fff
    classDef storageStyle fill:#55a3ff,stroke:#2d3436,stroke-width:2px,color:#fff
    classDef resultStyle fill:#00b894,stroke:#00695c,stroke-width:2px,color:#fff
    
    class A triggerStyle
    class B,C,D,E,F processStyle
    class G,H,I storageStyle
    class J resultStyle
```

### 2. 市场灵丹炼制工作流

```mermaid
flowchart TD
    A[📡 Webhook触发<br/>tusita-palace/market_pill] --> B[📊 市场数据采集<br/>价格+成交量]
    B --> C[📈 技术指标计算<br/>MA+RSI+MACD]
    C --> D[🔮 太公心易分析<br/>太乙观澜+遁甲择时]
    D --> E[🏛️ 稷下学宫辩论<br/>八仙过海分析]
    E --> F[⚖️ 三清论道<br/>决策综合]
    F --> G[💾 存储分析结果<br/>MongoDB]
    G --> H[📝 记录决策日志<br/>PostgreSQL]
    H --> I[💊 返回灵丹<br/>投资建议]

    classDef triggerStyle fill:#ff6b6b,stroke:#d63031,stroke-width:2px,color:#fff
    classDef dataStyle fill:#74b9ff,stroke:#0984e3,stroke-width:2px,color:#fff
    classDef analysisStyle fill:#a29bfe,stroke:#6c5ce7,stroke-width:2px,color:#fff
    classDef storageStyle fill:#55a3ff,stroke:#2d3436,stroke-width:2px,color:#fff
    classDef resultStyle fill:#00b894,stroke:#00695c,stroke-width:2px,color:#fff
    
    class A triggerStyle
    class B,C dataStyle
    class D,E,F analysisStyle
    class G,H storageStyle
    class I resultStyle
```

### 3. 紧急炼丹工作流

```mermaid
flowchart TD
    A[🚨 紧急事件触发<br/>市场异动/重大新闻] --> B{事件类型判断}
    B -->|市场崩盘| C[💊 市场灵丹<br/>快速分析]
    B -->|突发新闻| D[🧪 RSS仙丹<br/>新闻处理]
    B -->|价格暴涨| E[🍶 辩论仙液<br/>多角度分析]
    B -->|重大事件| F[💎 智慧精华<br/>综合分析]
    
    C --> G[⚡ 加急处理<br/>优先级最高]
    D --> G
    E --> G
    F --> G
    
    G --> H[📢 实时通知<br/>Webhook/邮件/短信]
    H --> I[📊 应急报告<br/>决策支持]

    classDef emergencyStyle fill:#e17055,stroke:#d63031,stroke-width:3px,color:#fff
    classDef decisionStyle fill:#fdcb6e,stroke:#e84393,stroke-width:2px
    classDef processStyle fill:#74b9ff,stroke:#0984e3,stroke-width:2px,color:#fff
    classDef notifyStyle fill:#00b894,stroke:#00695c,stroke-width:2px,color:#fff
    
    class A,B emergencyStyle
    class C,D,E,F decisionStyle
    class G processStyle
    class H,I notifyStyle
```

---

## 🔧 N8N工作流配置

### 环境变量配置

```bash
# N8N基础配置
N8N_BASE_URL=http://localhost:5678
N8N_API_KEY=your_n8n_api_key
N8N_WEBHOOK_URL=http://localhost:5678/webhook

# 兜率宫专用配置
TUSITA_PALACE_ENABLED=true
TAISHANG_LAOJUN_MODE=active
ALCHEMY_MAX_CONCURRENT=5
EMERGENCY_ALCHEMY_TIMEOUT=30

# 三脑架构连接
ZILLIZ_ENDPOINT=your_zilliz_endpoint
MONGODB_URL=your_mongodb_url
POSTGRESQL_URL=your_postgresql_url
```

### Webhook端点设计

| 炼丹配方 | Webhook路径 | 功能描述 |
|----------|-------------|----------|
| RSS仙丹 | `/webhook/tusita-palace/rss_elixir` | RSS新闻智能处理 |
| 市场灵丹 | `/webhook/tusita-palace/market_pill` | 市场数据分析 |
| 辩论仙液 | `/webhook/tusita-palace/debate_potion` | 稷下学宫辩论 |
| 智慧精华 | `/webhook/tusita-palace/wisdom_essence` | 综合智能分析 |
| 紧急炼丹 | `/webhook/tusita-palace/emergency/{event_type}` | 应急响应处理 |

---

## 🎯 MCP vs Webhook 对比

### **MCP控制方案** (推荐)

**优势**:
- ✅ 标准化接口，更好的集成
- ✅ 支持复杂的工作流管理
- ✅ 更好的错误处理和重试机制
- ✅ 可以查询执行状态和结果
- ✅ 支持工作流的动态创建和修改

**实现**:
```python
# 通过MCP控制N8N
mcp_client = TusitaPalaceMCP()
result = await mcp_client.start_alchemy('rss_elixir', ingredients)
status = await mcp_client.check_alchemy_status(result.execution_id)
```

### **Webhook触发方案** (简单)

**优势**:
- ✅ 实现简单，配置容易
- ✅ 实时触发，响应快速
- ✅ 无需额外的API认证
- ✅ 适合事件驱动的场景

**实现**:
```python
# 通过Webhook触发N8N
webhook_url = "http://n8n:5678/webhook/tusita-palace/rss_elixir"
response = await httpx.post(webhook_url, json=data)
```

### **推荐方案**: MCP + Webhook 混合

```python
class HybridTusitaPalace:
    async def trigger_alchemy(self, recipe_id: str, data: dict):
        # 1. 通过Webhook快速触发
        execution_id = await self.webhook_trigger(recipe_id, data)
        
        # 2. 通过MCP API监控状态
        return await self.mcp_monitor(execution_id)
```

---

## 🚀 部署配置

### Docker Compose配置

```yaml
version: '3.8'

services:
  # 兜率宫 - N8N工作流引擎
  tusita-palace:
    image: n8nio/n8n:latest
    container_name: tusita-palace
    restart: unless-stopped
    ports:
      - "5678:5678"
    environment:
      - N8N_BASIC_AUTH_ACTIVE=true
      - N8N_BASIC_AUTH_USER=taishang_laojun
      - N8N_BASIC_AUTH_PASSWORD=${LAOJUN_PASSWORD}
      - WEBHOOK_URL=https://your-domain.com
      - N8N_METRICS=true
    volumes:
      - ./tusita-palace-data:/home/<USER>/.n8n
      - /var/run/docker.sock:/var/run/docker.sock
    depends_on:
      - redis
      - mongodb
      - postgres

  # 兜率宫MCP服务器
  tusita-mcp:
    build: ./jixia_academy
    container_name: tusita-mcp
    restart: unless-stopped
    ports:
      - "8005:8000"
    environment:
      - N8N_BASE_URL=http://tusita-palace:5678
      - N8N_API_KEY=${N8N_API_KEY}
    depends_on:
      - tusita-palace
```

---

## 🎭 使用示例

### 1. 启动RSS仙丹炼制

```python
from jixia_academy.tusita_palace_mcp import TusitaPalaceMCP

palace = TusitaPalaceMCP()

# 准备炼丹原料
ingredients = {
    'rss_sources': [
        'https://feeds.finance.yahoo.com/rss/2.0/headline',
        'https://feeds.reuters.com/reuters/businessNews'
    ],
    'analysis_depth': 'deep',
    'target_categories': ['cryptocurrency', 'stocks', 'economy']
}

# 开始炼制
result = await palace.start_alchemy('rss_elixir', ingredients)
print(f"🔥 炼丹已启动: {result.execution_id}")

# 监控进度
while True:
    status = await palace.check_alchemy_status(result.execution_id)
    if status.status == 'completed':
        final_result = await palace.get_alchemy_result(result.execution_id)
        print(f"✅ RSS仙丹炼制完成!")
        print(f"📊 产出: {final_result['result_data']}")
        break
    elif status.status == 'failed':
        print(f"❌ 炼丹失败: {status.error_message}")
        break
    
    await asyncio.sleep(10)  # 等待10秒后再检查
```

### 2. 紧急市场事件响应

```python
# 市场崩盘紧急响应
emergency_data = {
    'event': 'market_crash',
    'symbol': 'BTC',
    'price_change': -15.5,
    'volume_spike': 300,
    'timestamp': datetime.now().isoformat()
}

# 触发紧急炼丹
emergency_result = await palace.trigger_emergency_alchemy(
    'market_crash', 
    emergency_data
)

print(f"🚨 紧急炼丹启动: {emergency_result.execution_id}")
```

---

## 💡 总结

**兜率宫 (N8N) 的优势**:

1. **可视化工作流**: 太上老君的炼丹过程一目了然
2. **事件驱动**: 自动响应市场变化和新闻事件
3. **模块化设计**: 每个炼丹步骤都是独立的节点
4. **易于扩展**: 可以轻松添加新的炼丹配方
5. **监控友好**: 实时查看炼丹进度和结果

**推荐实现方案**: **MCP + Webhook 混合模式**
- Webhook负责快速触发
- MCP负责状态监控和结果获取
- 既保证了响应速度，又提供了完整的控制能力

这样的设计让太公心易系统更加智能和自动化！🚀
