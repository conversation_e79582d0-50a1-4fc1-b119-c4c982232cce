# 太公心易-炼妖壶 技术文档中心

## 📋 文档导航

### 🏗️ 系统架构文档
- **[架构图集 - 咖啡厅展示版](architecture-diagrams.md)** - 🔥 明天Zilliz展示专用
- **[01-系统概览架构](01-system-overview.md)** - 整体系统架构概览
- **[02-三脑架构设计](02-three-brain-architecture.md)** - 核心三脑架构详解
- **[03-数据流架构](03-data-flow-architecture.md)** - 数据处理流程架构
- **[完整技术架构](TAIGONG_XINYI_ARCHITECTURE.md)** - 详细技术文档合集

### 🤝 商务合作文档
- **[Zilliz演示文档](zilliz-demo-presentation.md)** - 🔥 专门用于Zilliz展示的文档
- **[Zilliz合作提案](zilliz-partnership-proposal.md)** - 与Zilliz的技术合作方案

### 🚀 部署运维文档
- **[韩国服务器部署指南](korean_server_deployment.md)** - 韩国服务器MCP生态部署
- **[本地开发环境](local-development.md)** - 本地开发环境搭建指南

### 🔧 技术实现文档
- **[API接口文档](api-documentation.md)** - REST API接口说明
- **[数据库设计](database-design.md)** - 三脑架构数据库设计
- **[算法实现](algorithm-implementation.md)** - 太公心易核心算法

## 🎯 快速导航

### 对于技术展示
推荐阅读顺序：`01-系统概览` → `02-三脑架构` → `03-数据流架构` → `Zilliz演示文档`

### 对于技术实现
推荐阅读顺序：`02-三脑架构` → `数据库设计` → `算法实现` → `API接口文档`

### 对于部署运维
推荐阅读顺序：`05-部署架构` → `韩国服务器部署` → `本地开发环境`

---

**太公心易-炼妖壶** - 传统智慧与现代技术的完美融合 🚀
