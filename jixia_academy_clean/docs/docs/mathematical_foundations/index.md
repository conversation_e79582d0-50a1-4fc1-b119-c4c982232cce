# 数学基础理论

欢迎来到太公心易-炼妖壶的数学基础理论文档。这里包含了项目核心算法和理论框架的数学原理。

## 📚 理论体系概览

### 🏔️ 岩石力学金融框架
将岩石力学的应力-应变理论应用于金融市场分析，从物理学角度理解市场行为。

- [岩石力学金融理论](rock_mechanics_theory.md) - 核心理论基础和数学建模

### ☯️ 八卦对卦反调系统
基于先天八卦的多维度验证框架，通过对立统一的辩证过程探寻市场本质。

- [八卦数学模型](bagua_mathematical_model.md) - 八卦的数学表示和对立统一算法

### 📊 非高斯分布理论
处理真实市场的复杂性和非线性，超越传统正态分布假设。

- [非高斯分布框架](non_gaussian_framework.md) - 理论基础和实际应用

### 🎭 AI辩论数学模型
稷下学宫AI辩论系统的数学基础，模拟高质量的多角度分析。

- [辩论博弈论模型](debate_game_theory.md) - 博弈论基础和信息聚合理论

## 🔬 应用实例

### 核心创新总结
- [核心创新](core_innovations.md) - 项目最有价值的数学原理和实际应用

### 代码实现
项目的数学理论都有对应的代码实现，可以在以下位置找到：
- `examples/strategies/rock_mechanics_financial_framework.py` - 岩石力学框架
- `examples/strategies/non_gaussian_strategy_framework.py` - 非高斯策略
- `src/core/simple_retail_theater.py` - AI辩论系统

## 🎯 数学符号约定

为了保持文档的一致性，我们采用以下数学符号约定：

- **市场变量**：$S_t$ (价格), $V_t$ (成交量), $R_t$ (收益率)
- **应力变量**：$\sigma$ (应力), $\varepsilon$ (应变), $\tau$ (时间)
- **分布参数**：$\alpha$ (稳定性), $\beta$ (偏度), $H$ (Hurst指数)
- **八卦符号**：$\mathcal{B}_i$ (第i卦), $\mathcal{D}_{ij}$ (对卦关系)

## 🚀 如何使用这些文档

1. **理论学习**：从基础理论开始，逐步深入
2. **代码实现**：结合代码示例理解算法
3. **实际应用**：通过案例分析掌握应用方法
4. **创新发展**：基于理论框架开发新策略

---

*"在数学的严谨中寻找市场的真理，在理论的深度中发现投资的智慧。"* - 太公心易