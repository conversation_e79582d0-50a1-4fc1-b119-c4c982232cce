# Cauldron (炼妖壶) 环境配置文件示例
# 复制此文件为 .env 并填入实际值

# ===========================================
# 核心应用配置
# ===========================================
DEBUG=True
LOG_LEVEL=INFO

# ===========================================
# 主数据库 (PostgreSQL)
# ===========================================
DB_HOST=localhost
DB_PORT=5432
DB_NAME=cauldron_db
DB_USER=postgres
DB_PASSWORD=

# ===========================================
# Interactive Brokers (IBKR) API
# ===========================================
IB_HOST=127.0.0.1
IB_PORT=4002
IB_CLIENT_ID=1
IB_TIMEOUT=30
IB_RETRY_COUNT=3
IB_MARKET_DATA_TYPE=1 # 1=Live, 2=Frozen, 3=Delayed, 4=Delayed-Frozen
IB_REQUEST_TIMEOUT=10

# ===========================================
# 第三方服务API密钥
# ===========================================

# Tushare (财经数据)
tushare=

# Blockchair (区块链数据)
blockchair=

# Deepnote configuration removed - module moved outside project

# MongoDB
mongopublickkey=
mongoprivatekey=

# ===========================================
# N8N 工作流自动化
# ===========================================
N8N_WEBHOOK_URL=
N8N_API_KEY=

# ===========================================
# 八仙论道 (Jixia Academy) 系统配置
# ===========================================
JIXIA_ACADEMY_ENABLED=True
JIXIA_ACADEMY_PORT=8000
JIXIA_ACADEMY_HOST=localhost

# Jixia Academy 数据库 (PostgreSQL)
JIXIA_DB_HOST=localhost
JIXIA_DB_PORT=5432
JIXIA_DB_NAME=jixia_academy
JIXIA_DB_USER=postgres
JIXIA_DB_PASSWORD=

# Jixia 辩论配置
DEBATE_MAX_ROUNDS=8
DEBATE_TIME_LIMIT=300
DEBATE_AUTO_START=False

# OpenRouter API 密钥 (用于 Jixia 辩论模型)
# 建议配置多个密钥以实现负载均衡
OPENROUTER_API_KEY_1=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
OPENROUTER_API_KEY_2=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
OPENROUTER_API_KEY_3=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
OPENROUTER_API_KEY_4=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

# LiteLLM 配置 (用于 Jixia 裁判和统计员模型)
LITELLM_BASE_URL=
LITELLM_API_KEY=

# ===========================================
# 其他可选配置
# ===========================================

# Heroku PostgreSQL 连接（如果使用）
# DATABASE_URL=

# OpenAI API (如果直接使用)
# OPENAI_API_KEY=

# Alpha Vantage API
# ALPHA_VANTAGE_API_KEY=

# Polygon.io API
# POLYGON_API_KEY=