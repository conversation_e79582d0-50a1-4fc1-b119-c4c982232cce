[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "cauldron"
version = "2.0.0"
description = "一个集数据、分析、交易和AI辩论于一体的综合性量化金融研究平台"
authors = [
    {name = "<PERSON>", email = "<EMAIL>"},
]
readme = "README.md"
requires-python = ">=3.11"
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Financial and Insurance Industry",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
]

dependencies = [
    # Core Trading and Market Data
    "ib-insync>=0.9.86",
    "yfinance>=0.2.40",
    "pandas>=2.0.0",
    "numpy>=1.24.0",
    "scipy>=1.10.0",
    
    # Web Framework (替代Django)
    "fastapi[standard]>=0.104.0",
    "uvicorn[standard]>=0.24.0",
    
    # UI Framework
    "streamlit>=1.28.0",
    "plotly>=5.17.0",
    
    # HTTP and Async
    "aiohttp>=3.8.0",
    "httpx>=0.25.0",
    "nest-asyncio>=1.5.8",
    
    # Data Processing
    "beautifulsoup4>=4.12.0",
    "html2text>=2020.1.16",
    "pyyaml>=6.0",
    "psutil>=5.9.0",
    
    # Configuration
    "python-decouple>=3.8",
    "python-dotenv>=1.0.0",
    
    # Core Dependencies
    "pillow>=10.0.0",
    "typing-extensions>=4.0.0",
    "pydantic>=2.0.0,<3.0.0",
    "protobuf>=5.29.0",
    "opentelemetry-api>=1.20.0",
    "jsonref>=1.1.0",
    "rich>=13.0.0",
    "tomli>=2.0.0",
    "tomli-w>=1.0.0",
    "packaging>=23.0.0",
    
    # Database (PostgreSQL)
    "sqlmodel>=0.0.14",
    "psycopg2-binary>=2.9.0",
    "psycopg>=3.1.0",
    "alembic>=1.12.0",
    
    # AI and ML
    "openai>=1.52.2",
    "tiktoken>=0.8.0",
    "grpcio>=1.0.0",
    
    # Jixia Academy (AI Debate System)
    "autogen-agentchat>=0.6.1",
    "autogen-core>=0.6.1",
    "autogen-ext[openai]>=0.6.1",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.0.0",
    "pytest-asyncio>=0.21.0",
    "black>=23.0.0",
    "isort>=5.12.0",
    "flake8>=6.0.0",
    "mypy>=1.5.0",
    "mkdocs>=1.5.0",
]

# deepnote dependencies removed - module moved outside project

production = [
    "gunicorn>=20.1.0",
    "supervisor>=4.2.0",
]

[project.urls]
Homepage = "https://github.com/ben/cauldron"
Repository = "https://github.com/ben/cauldron.git"
Issues = "https://github.com/ben/cauldron/issues"

[project.scripts]
cauldron = "src.scripts.start_system:main"
jixia-academy = "scripts.start_jixia_academy:main"
api-server = "api_server:main"

[tool.hatch.build.targets.wheel]
packages = ["src", "app", "jixia_academy"]

[tool.black]
line-length = 100
target-version = ['py311']
include = '\.pyi?$'
extend-exclude = '''
(
  /(
      \.eggs
    | \.git
    | \.hg
    | \.mypy_cache
    | \.tox
    | \.venv
    | _build
    | buck-out
    | build
    | dist
    | archive
  )/
)
'''

[tool.isort]
profile = "black"
line_length = 100
multi_line_output = 3
include_trailing_comma = true
force_grid_wrap = 0
use_parentheses = true
ensure_newline_before_comments = true

[tool.mypy]
python_version = "3.11"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[tool.pytest.ini_options]
minversion = "7.0"
addopts = "-ra -q --strict-markers"
testpaths = [
    "tests",
    "src/tests",
    "jixia_academy/tests",
]
python_files = [
    "test_*.py",
    "*_test.py",
]
python_classes = [
    "Test*",
]
python_functions = [
    "test_*",
]

[tool.coverage.run]
source = ["src", "app", "jixia_academy"]
omit = [
    "*/tests/*",
    "*/test_*",
    "*/__pycache__/*",
    "*/migrations/*",
    "archive/*",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]