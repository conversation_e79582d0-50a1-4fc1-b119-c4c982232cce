{"name": "Intelligent News Aggregation & War Room Trigger", "nodes": [{"parameters": {"rule": {"interval": [{"field": "minutes", "minutesInterval": 30}]}}, "id": "news-aggregation-trigger", "name": "News Aggregation Trigger (30min)", "type": "n8n-nodes-base.scheduleTrigger", "typeVersion": 1.2, "position": [240, 300]}, {"parameters": {"jsCode": "// 定义新闻源配置\nconst newsFeeds = [\n  // 主流综合新闻社\n  { name: 'Reuters', feedUrl: 'https://feeds.reuters.com/reuters/topNews', category: 'mainstream', weight: 1.0 },\n  { name: 'Associated Press', feedUrl: 'https://feeds.apnews.com/rss/apf-topnews', category: 'mainstream', weight: 1.0 },\n  { name: '<PERSON><PERSON><PERSON>', feedUrl: 'http://www.xinhuanet.com/english/rss/englishnews.xml', category: 'mainstream', weight: 0.8 },\n  { name: 'PRNewswire', feedUrl: 'https://www.prnewswire.com/rss/news-releases-list.rss', category: 'corporate', weight: 0.6 },\n  \n  // 重要金融媒体\n  { name: 'Wall Street Journal', feedUrl: 'https://feeds.a.dj.com/rss/RSSMarketsMain.xml', category: 'financial', weight: 1.2 },\n  { name: 'Financial Times', feedUrl: 'https://www.ft.com/rss/home', category: 'financial', weight: 1.2 },\n  { name: 'ZeroHedge', feedUrl: 'https://feeds.feedburner.com/zerohedge/feed', category: 'alternative', weight: 0.9 },\n  \n  // 科技新闻\n  { name: 'TechCrunch', feedUrl: 'https://techcrunch.com/feed/', category: 'tech', weight: 0.8 },\n  { name: 'The Verge', feedUrl: 'https://www.theverge.com/rss/index.xml', category: 'tech', weight: 0.8 },\n  { name: 'Ars Technica', feedUrl: 'https://feeds.arstechnica.com/arstechnica/index', category: 'tech', weight: 0.8 }\n];\n\n// 返回新闻源列表\nreturn newsFeeds.map(feed => ({\n  json: {\n    name: feed.name,\n    feedUrl: feed.feedUrl,\n    category: feed.category,\n    weight: feed.weight,\n    timestamp: new Date().toISOString()\n  }\n}));"}, "id": "news-sources-config", "name": "Configure News Sources", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [460, 300]}, {"parameters": {}, "id": "split-feeds", "name": "Split News Feeds", "type": "n8n-nodes-base.splitOut", "typeVersion": 1, "position": [680, 300]}, {"parameters": {"url": "={{$json.feedUrl}}", "options": {"response": {"response": {"neverError": true}}}}, "id": "fetch-rss-feeds", "name": "Fetch RSS Feeds", "type": "n8n-nodes-base.rssFeedRead", "typeVersion": 1.1, "position": [900, 300]}, {"parameters": {"jsCode": "// 处理和标准化RSS数据\nconst items = $input.all();\nconst processedNews = [];\nconst currentTime = new Date();\nconst timeWindow = 24 * 60 * 60 * 1000; // 24小时窗口\n\nfor (const item of items) {\n  const feedData = item.json;\n  \n  // 检查文章时间是否在24小时内\n  const articleTime = new Date(feedData.isoDate || feedData.pubDate);\n  const timeDiff = currentTime - articleTime;\n  \n  if (timeDiff <= timeWindow) {\n    processedNews.push({\n      title: feedData.title,\n      description: feedData.description || feedData.summary || '',\n      content: feedData.content || feedData.description || '',\n      link: feedData.link,\n      source: feedData.meta?.title || 'Unknown',\n      category: $('Configure News Sources').item.json.category,\n      weight: $('Configure News Sources').item.json.weight,\n      publishedAt: articleTime.toISOString(),\n      fetchedAt: currentTime.toISOString(),\n      // 提取关键词用于情感分析\n      keywords: extractKeywords(feedData.title + ' ' + (feedData.description || ''))\n    });\n  }\n}\n\n// 简单关键词提取函数\nfunction extractKeywords(text) {\n  const emotionalKeywords = [\n    // 恐慌相关\n    'crash', 'collapse', 'panic', 'crisis', 'emergency', 'disaster', 'catastrophe',\n    'plunge', 'tumble', 'nosedive', 'bloodbath', 'meltdown', 'chaos',\n    // 疯狂/兴奋相关\n    'surge', 'soar', 'rocket', 'explode', 'boom', 'rally', 'euphoria',\n    'frenzy', 'mania', 'bubble', 'skyrocket', 'unprecedented',\n    // 市场相关\n    'volatility', 'uncertainty', 'risk', 'fear', 'greed', 'speculation',\n    'manipulation', 'correction', 'bear', 'bull', 'momentum'\n  ];\n  \n  const lowerText = text.toLowerCase();\n  return emotionalKeywords.filter(keyword => lowerText.includes(keyword));\n}\n\nreturn [{ json: { articles: processedNews, totalCount: processedNews.length } }];"}, "id": "process-news-data", "name": "Process & Filter News", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1120, 300]}, {"parameters": {"text": "={{JSON.stringify($json.articles)}}", "options": {"systemMessage": "你是一个专业的金融市场情感分析师。请分析提供的新闻文章，并为每篇文章打分：\n\n评分标准 (0-10)：\n- 0-2: 极度恐慌/悲观 (市场崩溃、危机等)\n- 3-4: 恐慌/担忧 (下跌、风险警告等)\n- 5: 中性 (常规新闻)\n- 6-7: 乐观/积极 (上涨、好消息等)\n- 8-10: 极度乐观/疯狂 (暴涨、泡沫等)\n\n请返回JSON格式：\n{\n  \"overall_sentiment_score\": 平均分,\n  \"panic_level\": 恐慌程度(0-10),\n  \"euphoria_level\": 狂热程度(0-10),\n  \"volatility_indicator\": 波动性指标(0-10),\n  \"article_scores\": [\n    {\"title\": \"...\", \"score\": 数字, \"reasoning\": \"原因\"}\n  ],\n  \"key_themes\": [\"主要主题1\", \"主题2\"],\n  \"market_mood\": \"overall description\"\n}\n\n重点关注：恐慌、疯狂、不确定性、波动性等情绪指标。"}, "promptType": "define"}, "id": "sentiment-analysis", "name": "AI Sentiment Analysis", "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.9, "position": [1340, 300]}, {"parameters": {"jsCode": "// 计算RSI风格的情感指标\nconst sentimentData = JSON.parse($json.output);\nconst currentTime = new Date();\n\n// 获取历史基线数据 (模拟24小时滑动窗口)\n// 在实际应用中，这里应该从数据库获取历史数据\nconst historicalBaseline = {\n  avg_sentiment: 5.0,\n  avg_panic: 3.0,\n  avg_euphoria: 3.0,\n  avg_volatility: 4.0\n};\n\n// 计算当前相对于基线的偏差\nconst sentimentDeviation = sentimentData.overall_sentiment_score - historicalBaseline.avg_sentiment;\nconst panicDeviation = sentimentData.panic_level - historicalBaseline.avg_panic;\nconst euphoriaDeviation = sentimentData.euphoria_level - historicalBaseline.avg_euphoria;\nconst volatilityDeviation = sentimentData.volatility_indicator - historicalBaseline.avg_volatility;\n\n// 计算RSI风格的指标 (0-100)\nfunction calculateRSI(current, baseline, maxDeviation = 5) {\n  const deviation = current - baseline;\n  const normalizedDeviation = Math.max(-maxDeviation, Math.min(maxDeviation, deviation));\n  return 50 + (normalizedDeviation / maxDeviation) * 50;\n}\n\nconst emotionalRSI = {\n  sentiment_rsi: calculateRSI(sentimentData.overall_sentiment_score, historicalBaseline.avg_sentiment),\n  panic_rsi: calculateRSI(sentimentData.panic_level, historicalBaseline.avg_panic),\n  euphoria_rsi: calculateRSI(sentimentData.euphoria_level, historicalBaseline.avg_euphoria),\n  volatility_rsi: calculateRSI(sentimentData.volatility_indicator, historicalBaseline.avg_volatility)\n};\n\n// 计算综合触发指标\nconst triggerScore = Math.max(\n  emotionalRSI.panic_rsi > 80 ? emotionalRSI.panic_rsi : 0,\n  emotionalRSI.euphoria_rsi > 80 ? emotionalRSI.euphoria_rsi : 0,\n  emotionalRSI.volatility_rsi > 80 ? emotionalRSI.volatility_rsi : 0\n);\n\n// 判断是否触发War Room\nconst shouldTrigger = triggerScore > 80;\nconst triggerReason = [];\n\nif (emotionalRSI.panic_rsi > 80) triggerReason.push('极度恐慌');\nif (emotionalRSI.euphoria_rsi > 80) triggerReason.push('极度狂热');\nif (emotionalRSI.volatility_rsi > 80) triggerReason.push('极高波动');\n\nreturn [{\n  json: {\n    timestamp: currentTime.toISOString(),\n    sentiment_analysis: sentimentData,\n    emotional_rsi: emotionalRSI,\n    trigger_score: triggerScore,\n    should_trigger_war_room: shouldTrigger,\n    trigger_reason: triggerReason,\n    market_condition: triggerScore > 80 ? 'EXTREME' : triggerScore > 60 ? 'HIGH' : triggerScore < 20 ? 'CALM' : 'NORMAL',\n    recommendation: triggerScore > 80 ? '立即启动War Room' : triggerScore < 20 ? '建议离场观望' : '继续监控'\n  }\n}];"}, "id": "calculate-trigger-score", "name": "Calculate Trigger Score (RSI Style)", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1560, 300]}, {"parameters": {"conditions": {"boolean": [{"value1": "={{$json.should_trigger_war_room}}", "value2": true}]}}, "id": "check-trigger-threshold", "name": "<PERSON> Trigger Threshold", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [1780, 300]}, {"parameters": {"requestMethod": "POST", "url": "${JIXIA_ACADEMY_WEBHOOK}/start-debate", "jsonParameters": true, "bodyParametersJson": "={\n  \"trigger_type\": \"news_sentiment\",\n  \"trigger_score\": $json.trigger_score,\n  \"market_condition\": $json.market_condition,\n  \"trigger_reason\": $json.trigger_reason,\n  \"sentiment_data\": $json.sentiment_analysis,\n  \"emotional_rsi\": $json.emotional_rsi,\n  \"timestamp\": $json.timestamp,\n  \"debate_config\": {\n    \"topic\": \"当前市场情绪异常分析与应对策略\",\n    \"urgency\": \"high\",\n    \"duration_minutes\": 20,\n    \"participants\": [\n      \"taishang_laojun\",\n      \"yuanshi_tianzun\",\n      \"lingbao_daojun\",\n      \"market_sentiment_expert\",\n      \"risk_management_specialist\"\n    ],\n    \"context\": {\n      \"news_sources\": $json.sentiment_analysis.key_themes,\n      \"market_mood\": $json.sentiment_analysis.market_mood,\n      \"panic_level\": $json.sentiment_analysis.panic_level,\n      \"euphoria_level\": $json.sentiment_analysis.euphoria_level\n    }\n  }\n}"}, "id": "trigger-jixia-debate", "name": "Trigger Jixia Academy Debate", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [2000, 200]}, {"parameters": {"text": "={{JSON.stringify($json.sentiment_analysis)}}", "options": {"systemMessage": "基于提供的情感分析数据，生成一份简洁的市场情绪摘要报告。\n\n请包含：\n1. 当前市场情绪概况\n2. 主要风险点\n3. 关键主题分析\n4. 建议关注事项\n\n格式要求：\n- 使用中文\n- 条理清晰\n- 重点突出\n- 适合IM分发"}, "promptType": "define"}, "id": "generate-summary", "name": "Generate Market Summary", "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.9, "position": [2000, 400]}, {"parameters": {"channel": "#market-intelligence", "text": "🚨 市场情绪监控报告\n\n触发分数: {{$('Calculate Trigger Score (RSI Style)').item.json.trigger_score.toFixed(1)}}\n市场状态: {{$('Calculate Trigger Score (RSI Style)').item.json.market_condition}}\n\n{{$json.output}}", "otherOptions": {"username": "Market Sentiment Bot", "icon_emoji": ":chart_with_upwards_trend:"}, "attachments": [{"color": "={{$('Calculate Trigger Score (RSI Style)').item.json.trigger_score > 80 ? 'danger' : $('Calculate Trigger Score (RSI Style)').item.json.trigger_score < 20 ? 'good' : 'warning'}}", "fields": [{"title": "恐慌指数 RSI", "value": "={{$('Calculate Trigger Score (RSI Style)').item.json.emotional_rsi.panic_rsi.toFixed(1)}}", "short": true}, {"title": "狂热指数 RSI", "value": "={{$('Calculate Trigger Score (RSI Style)').item.json.emotional_rsi.euphoria_rsi.toFixed(1)}}", "short": true}, {"title": "波动指数 RSI", "value": "={{$('Calculate Trigger Score (RSI Style)').item.json.emotional_rsi.volatility_rsi.toFixed(1)}}", "short": true}, {"title": "建议行动", "value": "={{$('Calculate Trigger Score (RSI Style)').item.json.recommendation}}", "short": true}]}]}, "id": "send-slack-alert", "name": "Send <PERSON><PERSON><PERSON>", "type": "n8n-nodes-base.slack", "typeVersion": 1, "position": [2220, 400], "credentials": {"slackApi": {"id": "slack-credentials", "name": "Slack API"}}}, {"parameters": {"requestMethod": "POST", "url": "${DATABASE_WEBHOOK_URL}/store-sentiment", "jsonParameters": true, "bodyParametersJson": "={{JSON.stringify($('Calculate Trigger Score (RSI Style)').item.json)}}"}, "id": "store-historical-data", "name": "Store Historical Data", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1780, 500]}], "connections": {"news-aggregation-trigger": {"main": [[{"node": "news-sources-config", "type": "main", "index": 0}]]}, "news-sources-config": {"main": [[{"node": "split-feeds", "type": "main", "index": 0}]]}, "split-feeds": {"main": [[{"node": "fetch-rss-feeds", "type": "main", "index": 0}]]}, "fetch-rss-feeds": {"main": [[{"node": "process-news-data", "type": "main", "index": 0}]]}, "process-news-data": {"main": [[{"node": "sentiment-analysis", "type": "main", "index": 0}]]}, "sentiment-analysis": {"main": [[{"node": "calculate-trigger-score", "type": "main", "index": 0}]]}, "calculate-trigger-score": {"main": [[{"node": "check-trigger-threshold", "type": "main", "index": 0}, {"node": "generate-summary", "type": "main", "index": 0}, {"node": "store-historical-data", "type": "main", "index": 0}]]}, "check-trigger-threshold": {"main": [[{"node": "trigger-jixia-debate", "type": "main", "index": 0}]]}, "generate-summary": {"main": [[{"node": "send-slack-alert", "type": "main", "index": 0}]]}}, "active": true, "settings": {"timezone": "Asia/Shanghai", "saveManualExecutions": true, "callerPolicy": "workflowOwner"}, "versionId": "1", "meta": {"templateCredsSetupCompleted": true}, "id": "intelligent-news-trigger", "tags": [{"createdAt": "2024-01-01T00:00:00.000Z", "updatedAt": "2024-01-01T00:00:00.000Z", "id": "news-intelligence", "name": "News Intelligence"}, {"createdAt": "2024-01-01T00:00:00.000Z", "updatedAt": "2024-01-01T00:00:00.000Z", "id": "sentiment-analysis", "name": "Sentiment Analysis"}]}