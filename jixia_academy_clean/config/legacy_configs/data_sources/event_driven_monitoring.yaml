# 事件驱动异常监控配置
# Event-Driven Anomaly Monitoring Configuration

# 环境变量配置
environment:
  ib_api_key: ${IB_API_KEY}
  n8n_webhook_url: ${N8N_WEBHOOK_URL}
  war_room_webhook: ${WAR_ROOM_WEBHOOK_URL}
  alert_email: ${ALERT_EMAIL}
  slack_webhook: ${SLACK_WEBHOOK_URL}

# 监控标的配置
watchlist:
  # 黄金相关
  gold:
    - symbol: "GLD"  # SPDR Gold Trust ETF
      exchange: "NYSE"
      type: "ETF"
    - symbol: "XAUUSD"  # 现货黄金
      exchange: "FOREX"
      type: "SPOT"
    - symbol: "GC"  # 黄金期货
      exchange: "COMEX"
      type: "FUTURE"
  
  # 比特币相关
  bitcoin:
    - symbol: "BTCUSD"  # 比特币现货
      exchange: "CRYPTO"
      type: "SPOT"
    - symbol: "GBTC"  # Grayscale Bitcoin Trust
      exchange: "NASDAQ"
      type: "ETF"
    - symbol: "MSTR"  # MicroStrategy (比特币代理股)
      exchange: "NASDAQ"
      type: "STOCK"
  
  # 原油相关
  oil:
    - symbol: "CL"  # WTI原油期货
      exchange: "NYMEX"
      type: "FUTURE"
    - symbol: "USO"  # United States Oil Fund ETF
      exchange: "NYSE"
      type: "ETF"
    - symbol: "XOM"  # 埃克森美孚
      exchange: "NYSE"
      type: "STOCK"

# 异常检测规则
anomaly_detection:
  # 交易量异常
  volume_anomaly:
    threshold_multiplier: 5.0  # 超过平均交易量5倍
    baseline_period: 20  # 基于过去20个交易日的平均值
    min_volume_threshold: 1000000  # 最小交易量阈值
    
  # 价格波动异常
  price_volatility:
    intraday_change_threshold: 0.05  # 日内涨跌幅超过5%
    volatility_spike_multiplier: 3.0  # 波动率超过历史平均3倍
    price_gap_threshold: 0.03  # 跳空缺口超过3%
    
  # 新闻事件相关
  news_driven:
    sentiment_threshold: 0.8  # 新闻情感分析阈值
    news_volume_spike: 10  # 相关新闻数量激增
    social_mention_spike: 5.0  # 社交媒体提及量激增

# 数据源配置
data_sources:
  # IB实时数据
  interactive_brokers:
    endpoint: "${IB_GATEWAY_URL}"
    port: 7497
    client_id: 1
    refresh_interval: 5  # 秒
    
  # 新闻数据源
  news_sources:
    dow_jones:
      api_key: ${DOW_JONES_API_KEY}
      endpoint: "https://api.dowjones.com/alpha/extractions/documents"
      categories: ["commodities", "cryptocurrency", "energy"]
      
    fly_on_the_wall:
      api_key: ${FLY_API_KEY}
      endpoint: "https://api.flyonthewall.com/news"
      filters: ["breaking", "earnings", "upgrades"]
      
    ib_news_flow:
      endpoint: "${IB_NEWS_ENDPOINT}"
      categories: ["TOP", "LIVE", "BRK"]
      languages: ["en", "zh"]

# 监控时间窗口
monitoring_windows:
  # 实时监控（交易时间）
  realtime:
    enabled: true
    market_hours:
      us: "09:30-16:00 EST"
      asia: "09:00-15:00 HKT"
      europe: "08:00-16:30 GMT"
    
  # 盘前盘后监控
  extended_hours:
    enabled: true
    pre_market: "04:00-09:30 EST"
    after_hours: "16:00-20:00 EST"
    
  # 周末加密货币监控
  crypto_24_7:
    enabled: true
    symbols: ["BTCUSD", "ETHUSD"]

# 告警配置
alert_configuration:
  # 告警级别
  severity_levels:
    low:
      threshold_score: 3.0
      notification_delay: 300  # 5分钟延迟
      
    medium:
      threshold_score: 5.0
      notification_delay: 60   # 1分钟延迟
      
    high:
      threshold_score: 8.0
      notification_delay: 0    # 立即通知
      
    critical:
      threshold_score: 10.0
      notification_delay: 0
      auto_trigger_war_room: true

  # 通知渠道
  notification_channels:
    email:
      enabled: true
      recipients: ["${ALERT_EMAIL}"]
      
    slack:
      enabled: true
      webhook_url: "${SLACK_WEBHOOK_URL}"
      channel: "#trading-alerts"
      
    n8n_workflow:
      enabled: true
      webhook_url: "${N8N_WEBHOOK_URL}"
      
    war_room:
      enabled: true
      webhook_url: "${WAR_ROOM_WEBHOOK_URL}"
      auto_trigger_threshold: 8.0

# 事件评分算法
scoring_algorithm:
  weights:
    volume_anomaly: 0.3
    price_volatility: 0.25
    news_sentiment: 0.2
    social_sentiment: 0.15
    technical_indicators: 0.1
    
  # 时间衰减因子
  time_decay:
    half_life_minutes: 30  # 30分钟半衰期
    max_age_hours: 4       # 4小时后事件失效

# 历史数据存储
data_storage:
  database:
    type: "postgresql"
    connection_string: "${DATABASE_URL}"
    
  retention_policy:
    raw_data: "30 days"
    aggregated_data: "1 year"
    alert_history: "2 years"

# 调试和测试
debug_settings:
  log_level: "INFO"
  test_mode: false
  mock_data: false
  dry_run: false  # 设为true时不发送真实告警