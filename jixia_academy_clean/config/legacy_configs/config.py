# Mattermost Bot 和 LLM 集成配置

class Config:
    # Mattermost 配置
    MATTERMOST_URL = 'https://your-mattermost-server.com'  # Mattermost 服务器地址
    BOT_TOKEN = 'your-bot-token'  # Bot 访问令牌
    WEBHOOK_SECRET = 'your-webhook-secret'  # Webhook 密钥
    
    # LLM 配置
    LLM_API_ENDPOINT = 'https://your-llm-api-endpoint'  # LLM API 端点
    LLM_API_KEY = 'your-llm-api-key'  # LLM API 密钥
    
    # 集成配置
    ALLOWED_CHANNELS = ['channel-id-1', 'channel-id-2']  # 允许 Bot 响应的频道列表
    MAX_CONCURRENT_REQUESTS = 5  # 最大并发请求数
    REQUEST_TIMEOUT = 30  # 请求超时时间（秒）

    # 增强型智能体配置（autogen 0.6.1）
    AGENT_ROLES = {
        '灵宝道君': {'type': 'moderator', 'turn_strategy': 'qiankun_order'},
        '太上老君': {
            'type': 'groupchat',
            'agents': [
                {'name': 'FactEngine', 'role': 'data_scientist', 'tools': ['ib_fundamental']},
                {'name': 'Validator', 'role': 'fact_checker'}
            ],
            'workflow': 'concurrent'
        },
        '元始天尊': {
            'type': 'judge',
            'scoring_criteria': {
                'data_support': 0.4,
                'logical_coherence': 0.3,
                'innovation': 0.3
            }
        }
    }
    
    # 消息处理配置
    MAX_MESSAGE_LENGTH = 2000  # 最大消息长度
    RATE_LIMIT = 60  # 每分钟最大请求次数
    