# OpenRouter 八仙桌辩论团队配置
# 四个账户负载均衡，使用免费模型实现永动机效应

models:
# ========== 乾卦 - 吕洞宾 (DeepSeek R1) ==========
# Account 1
  - provider: autogen_ext.models.openai.OpenAIChatCompletionClient
    config:
      model: deepseek/deepseek-r1-zero:free
      base_url: https://openrouter.ai/api/v1
      api_key: ${OPENROUTER_API_KEY_1}
      max_tokens: 2500
      temperature: 0.7
      model_info:
        function_calling: true
        json_output: true
        vision: false
        family: openrouter
      price: [0.0, 0.0]

# ========== 坤卦 - 何仙姑 (Gemma 3 27B) ==========
# Account 2
  - provider: autogen_ext.models.openai.OpenAIChatCompletionClient
    config:
      model: google/gemma-3-27b-it:free
      base_url: https://openrouter.ai/api/v1
      api_key: ${OPENROUTER_API_KEY_2}
      max_tokens: 2500
      temperature: 0.7
      model_info:
        function_calling: true
        json_output: true
        vision: false
        family: openrouter
      price: [0.0, 0.0]

# ========== 兑卦 - 张果老 (GLM-4-32B) ==========
# Account 3
  - provider: autogen_ext.models.openai.OpenAIChatCompletionClient
    config:
      model: thudm/glm-4-32b:free
      base_url: https://openrouter.ai/api/v1
      api_key: ${OPENROUTER_API_KEY_3}
      max_tokens: 2500
      temperature: 0.7
      model_info:
        function_calling: true
        json_output: true
        vision: false
        family: openrouter
      price: [0.0, 0.0]

# ========== 艮卦 - 韩湘子 (Nvidia Nemotron Ultra) ==========
# Account 4 - 更换为Nvidia Nemotron Ultra模型
  - provider: autogen_ext.models.openai.OpenAIChatCompletionClient
    config:
      model: nvidia/llama-3.1-nemotron-ultra-253b-v1:free
      base_url: https://openrouter.ai/api/v1
      api_key: ${OPENROUTER_API_KEY_4}
      max_tokens: 2500
      temperature: 0.7
      model_info:
        function_calling: true
        json_output: true
        vision: false
        family: openrouter
      price: [0.0, 0.0]

# ========== 离卦 - 汉钟离 (DeepSeek Chat V3) ==========
# Account 1 (负载均衡)
  - provider: autogen_ext.models.openai.OpenAIChatCompletionClient
    config:
      model: deepseek/deepseek-chat-v3-0324:free
      base_url: https://openrouter.ai/api/v1
      api_key: ${OPENROUTER_API_KEY_1}
      max_tokens: 2500
      temperature: 0.7
      model_info:
        function_calling: true
        json_output: true
        vision: false
        family: openrouter
      price: [0.0, 0.0]

# ========== 坎卦 - 蓝采和 (Llama 4 Scout) ==========
# Account 2 (负载均衡) - 更换为Llama 4 Scout模型
  - provider: autogen_ext.models.openai.OpenAIChatCompletionClient
    config:
      model: meta-llama/llama-4-scout:free
      base_url: https://openrouter.ai/api/v1
      api_key: ${OPENROUTER_API_KEY_2}
      max_tokens: 2500
      temperature: 0.7
      model_info:
        function_calling: true
        json_output: true
        vision: true
        family: openrouter
      price: [0.0, 0.0]

# ========== 震卦 - 曹国舅 (Qwen QwQ 32B) ==========
# Account 3 (负载均衡)
  - provider: autogen_ext.models.openai.OpenAIChatCompletionClient
    config:
      model: arliai/qwq-32b-arliai-rpr-v1:free
      base_url: https://openrouter.ai/api/v1
      api_key: ${OPENROUTER_API_KEY_3}
      max_tokens: 2500
      temperature: 0.7
      model_info:
        function_calling: true
        json_output: true
        vision: false
        family: openrouter
      price: [0.0, 0.0]

# ========== 巽卦 - 铁拐李 (Meta-maverick) ==========
# Account 4 (负载均衡)
  - provider: autogen_ext.models.openai.OpenAIChatCompletionClient
    config:
      model: meta-llama/llama-4-maverick:free
      base_url: https://openrouter.ai/api/v1
      api_key: ${OPENROUTER_API_KEY_4}
      max_tokens: 2500
      temperature: 0.7
      model_info:
        function_calling: true
        json_output: true
        vision: false
        family: openrouter
      price: [0.0, 0.0]

# ========== 元始天尊 - 克制点评评委 (Phi-4 Reasoning Plus) ==========
  - provider: autogen_ext.models.openai.OpenAIChatCompletionClient
    config:
      model: microsoft/phi-4-reasoning-plus:free
      base_url: https://openrouter.ai/api/v1
      api_key: ${OPENROUTER_API_KEY_3}
      max_tokens: 2500
      temperature: 0.7
      model_info:
        function_calling: true
        json_output: true
        vision: false
        family: openrouter
      price: [0.0, 0.0]

# ========== 灵宝道君 - 主持人兼现场点评 (DeepHermes 3 Mistral 24B) ==========
# Account 4 (负载均衡)
  - provider: autogen_ext.models.openai.OpenAIChatCompletionClient
    config:
      model: nousresearch/deephermes-3-mistral-24b-preview:free
      base_url: https://openrouter.ai/api/v1
      api_key: ${OPENROUTER_API_KEY_4}
      max_tokens: 2500
      temperature: 0.7
      model_info:
        function_calling: true
        json_output: true
        vision: false
        family: openrouter
      price: [0.0, 0.0]

# ========== 太上老君 - 技术分析评委 (Phi-4 Reasoning Plus) ==========
  - provider: autogen_ext.models.openai.OpenAIChatCompletionClient
    config:
      model: microsoft/phi-4-reasoning-plus:free
      base_url: https://openrouter.ai/api/v1
      api_key: ${OPENROUTER_API_KEY_4}
      max_tokens: 2500
      temperature: 0.7
      model_info:
        function_calling: true
        json_output: true
        vision: false
        family: openrouter
      price: [0.0, 0.0]

# ========== 统计员 - Claude 3.7 Sonnet (Bedrock) ==========
  - provider: autogen_ext.models.openai.OpenAIChatCompletionClient
    config:
      model: us.anthropic.claude-3-7-sonnet-20250219-v1:0
      base_url: ${LITELLM_BASE_URL}
      api_key: ${LITELLM_API_KEY}
      model_info:
        function_calling: true
        json_output: true
        vision: false
        family: litellm
      price: [0.0, 0.0]
