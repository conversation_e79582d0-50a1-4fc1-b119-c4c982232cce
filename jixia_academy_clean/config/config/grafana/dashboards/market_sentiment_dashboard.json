{"dashboard": {"id": null, "title": "市场情绪分析看板 - 30天趋势", "tags": ["市场情绪", "RSS分析", "恐慌指数"], "timezone": "browser", "panels": [{"id": 1, "title": "市场情绪综合指数 (30天趋势)", "type": "timeseries", "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}, "targets": [{"expr": "avg_over_time(market_sentiment_score[1d])", "legendFormat": "情绪指数", "refId": "A"}, {"expr": "avg_over_time(market_panic_level[1d])", "legendFormat": "恐慌指数", "refId": "B"}, {"expr": "avg_over_time(market_euphoria_level[1d])", "legendFormat": "狂热指数", "refId": "C"}], "fieldConfig": {"defaults": {"min": 0, "max": 10, "unit": "short"}}, "options": {"legend": {"displayMode": "table", "placement": "bottom"}}}, {"id": 2, "title": "当前市场情绪状态", "type": "gauge", "gridPos": {"h": 8, "w": 6, "x": 12, "y": 0}, "targets": [{"expr": "market_sentiment_current", "legendFormat": "当前情绪", "refId": "A"}], "fieldConfig": {"defaults": {"min": 0, "max": 10, "thresholds": {"steps": [{"color": "red", "value": 0}, {"color": "yellow", "value": 3}, {"color": "green", "value": 4}, {"color": "yellow", "value": 7}, {"color": "red", "value": 8}]}}}}, {"id": 3, "title": "恐慌/狂热警报状态", "type": "stat", "gridPos": {"h": 4, "w": 6, "x": 18, "y": 0}, "targets": [{"expr": "market_active_triggers", "legendFormat": "活跃警报", "refId": "A"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "green", "value": 0}, {"color": "yellow", "value": 1}, {"color": "red", "value": 3}]}}}}, {"id": 4, "title": "市场情绪RSI指标", "type": "timeseries", "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8}, "targets": [{"expr": "market_sentiment_rsi", "legendFormat": "情绪RSI", "refId": "A"}, {"expr": "market_panic_rsi", "legendFormat": "恐慌RSI", "refId": "B"}, {"expr": "market_euphoria_rsi", "legendFormat": "狂热RSI", "refId": "C"}], "fieldConfig": {"defaults": {"min": 0, "max": 100, "unit": "percent"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "恐慌RSI"}, "properties": [{"id": "color", "value": {"mode": "fixed", "fixedColor": "red"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "狂热RSI"}, "properties": [{"id": "color", "value": {"mode": "fixed", "fixedColor": "orange"}}]}]}, "options": {"legend": {"displayMode": "table", "placement": "bottom"}}}, {"id": 5, "title": "每日新闻处理量", "type": "barchart", "gridPos": {"h": 8, "w": 6, "x": 12, "y": 8}, "targets": [{"expr": "sum(rate(rss_news_processed_total[1d]))", "legendFormat": "新闻处理量", "refId": "A"}], "fieldConfig": {"defaults": {"unit": "short"}}}, {"id": 6, "title": "新闻来源分布", "type": "piechart", "gridPos": {"h": 8, "w": 6, "x": 18, "y": 8}, "targets": [{"expr": "sum by (source) (rss_news_by_source)", "legendFormat": "{{source}}", "refId": "A"}], "options": {"legend": {"displayMode": "table", "placement": "right"}}}, {"id": 7, "title": "波动性指标趋势", "type": "timeseries", "gridPos": {"h": 8, "w": 12, "x": 0, "y": 16}, "targets": [{"expr": "avg_over_time(market_volatility_indicator[1d])", "legendFormat": "波动性指标", "refId": "A"}, {"expr": "avg_over_time(market_volatility_rsi[1d])", "legendFormat": "波动性RSI", "refId": "B"}], "fieldConfig": {"defaults": {"min": 0, "max": 10, "unit": "short"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "波动性RSI"}, "properties": [{"id": "custom.axisPlacement", "value": "right"}, {"id": "max", "value": 100}, {"id": "unit", "value": "percent"}]}]}}, {"id": 8, "title": "最近触发事件", "type": "table", "gridPos": {"h": 8, "w": 12, "x": 12, "y": 16}, "targets": [{"expr": "market_recent_triggers", "format": "table", "refId": "A"}], "fieldConfig": {"defaults": {"custom": {"displayMode": "auto", "filterable": true}}}, "options": {"showHeader": true}}, {"id": 9, "title": "市场情绪热力图 (7天)", "type": "heatmap", "gridPos": {"h": 8, "w": 24, "x": 0, "y": 24}, "targets": [{"expr": "market_sentiment_heatmap", "legendFormat": "{{hour}}", "refId": "A"}], "fieldConfig": {"defaults": {"custom": {"hideFrom": {"legend": false, "tooltip": false, "vis": false}}}}}], "time": {"from": "now-30d", "to": "now"}, "timepicker": {"refresh_intervals": ["5m", "15m", "30m", "1h", "2h", "1d"]}, "refresh": "15m", "schemaVersion": 30, "version": 1, "links": [{"title": "War Room触发器", "url": "/d/war-room/war-room-dashboard", "type": "dashboards"}]}}