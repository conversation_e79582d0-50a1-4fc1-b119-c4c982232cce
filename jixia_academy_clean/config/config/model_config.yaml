# OpenRouter 八仙桌辩论团队配置
# 四个账户负载均衡，使用免费模型实现永动机效应

models:
# ========== 乾卦 - 吕洞宾 (DeepSeek R1) 国产保守派 ==========
# Account 1 - 价值投资，理性分析
  - provider: autogen_ext.models.openai.OpenAIChatCompletionClient
    config:
      model: deepseek/deepseek-r1-zero:free
      base_url: https://openrouter.ai/api/v1
      api_key: ${OPENROUTER_API_KEY_1}
      max_tokens: 2500
      temperature: 0.6
      model_info:
        function_calling: true
        json_output: true
        vision: false
        family: openrouter
      price: [0.0, 0.0]

# ========== 坤卦 - 何仙姑 (Qwen 2.5 72B) 国产保守派 ==========
# Account 2 - ETF投资，稳健配置
  - provider: autogen_ext.models.openai.OpenAIChatCompletionClient
    config:
      model: alibaba/qwen-2.5-72b-instruct:free
      base_url: https://openrouter.ai/api/v1
      api_key: ${OPENROUTER_API_KEY_2}
      max_tokens: 2500
      temperature: 0.5
      model_info:
        function_calling: true
        json_output: true
        vision: false
        family: openrouter
      price: [0.0, 0.0]

# ========== 兑卦 - 张果老 (Moonshot V1) 国产保守派 ==========
# Account 3 - 传统投资，经验丰富
  - provider: autogen_ext.models.openai.OpenAIChatCompletionClient
    config:
      model: moonshot/moonshot-v1-32k:free
      base_url: https://openrouter.ai/api/v1
      api_key: ${OPENROUTER_API_KEY_3}
      max_tokens: 2500
      temperature: 0.6
      model_info:
        function_calling: true
        json_output: true
        vision: false
        family: openrouter
      price: [0.0, 0.0]

# ========== 艮卦 - 韩湘子 (GLM-4-32B) 国产保守派 ==========
# Account 4 - 新兴资产，谨慎创新
  - provider: autogen_ext.models.openai.OpenAIChatCompletionClient
    config:
      model: thudm/glm-4-32b:free
      base_url: https://openrouter.ai/api/v1
      api_key: ${OPENROUTER_API_KEY_4}
      max_tokens: 2500
      temperature: 0.7
      model_info:
        function_calling: true
        json_output: true
        vision: false
        family: openrouter
      price: [0.0, 0.0]

# ========== 离卦 - 汉钟离 (Gemini 2.0 Flash) 国外开放派 ==========
# Account 1 (负载均衡) - 热点追踪，敏锐嗅觉
  - provider: autogen_ext.models.openai.OpenAIChatCompletionClient
    config:
      model: google/gemini-2.0-flash-exp:free
      base_url: https://openrouter.ai/api/v1
      api_key: ${OPENROUTER_API_KEY_1}
      max_tokens: 2500
      temperature: 0.8
      model_info:
        function_calling: true
        json_output: true
        vision: false
        family: openrouter
      price: [0.0, 0.0]

# ========== 坎卦 - 蓝采和 (Mistral Large) 国外开放派 ==========
# Account 2 (负载均衡) - 小盘股，逆向投资
  - provider: autogen_ext.models.openai.OpenAIChatCompletionClient
    config:
      model: mistral/mistral-large:free
      base_url: https://openrouter.ai/api/v1
      api_key: ${OPENROUTER_API_KEY_2}
      max_tokens: 2500
      temperature: 0.8
      model_info:
        function_calling: true
        json_output: true
        vision: false
        family: openrouter
      price: [0.0, 0.0]

# ========== 震卦 - 曹国舅 (Phi-4 Reasoning Plus) 国外开放派 ==========
# Account 3 (负载均衡) - 机构视角，理性分析
  - provider: autogen_ext.models.openai.OpenAIChatCompletionClient
    config:
      model: microsoft/phi-4-reasoning-plus:free
      base_url: https://openrouter.ai/api/v1
      api_key: ${OPENROUTER_API_KEY_3}
      max_tokens: 2500
      temperature: 0.7
      model_info:
        function_calling: true
        json_output: true
        vision: false
        family: openrouter
      price: [0.0, 0.0]

# ========== 巽卦 - 铁拐李 (Nemotron Ultra) 国外开放派 ==========
# Account 4 (负载均衡) - 逆向思维，独特视角
  - provider: autogen_ext.models.openai.OpenAIChatCompletionClient
    config:
      model: nvidia/llama-3.1-nemotron-ultra-253b-v1:free
      base_url: https://openrouter.ai/api/v1
      api_key: ${OPENROUTER_API_KEY_4}
      max_tokens: 2500
      temperature: 0.9
      model_info:
        function_calling: true
        json_output: true
        vision: false
        family: openrouter
      price: [0.0, 0.0]

# ========== 元始天尊 - 克制点评评委 (Phi-4 Reasoning Plus) ==========
  - provider: autogen_ext.models.openai.OpenAIChatCompletionClient
    config:
      model: microsoft/phi-4-reasoning-plus:free
      base_url: https://openrouter.ai/api/v1
      api_key: ${OPENROUTER_API_KEY_3}
      max_tokens: 2500
      temperature: 0.7
      model_info:
        function_calling: true
        json_output: true
        vision: false
        family: openrouter
      price: [0.0, 0.0]

# ========== 灵宝道君 - 数据核实总结者 (DeepSeek R1T2 Chimera) ==========
# Account 4 (负载均衡) - MCP工具调用，RSS数据核实
  - provider: autogen_ext.models.openai.OpenAIChatCompletionClient
    config:
      model: tngtech/deepseek-r1t2-chimera:free
      base_url: https://openrouter.ai/api/v1
      api_key: ${OPENROUTER_API_KEY_4}
      max_tokens: 2500
      temperature: 0.7
      model_info:
        function_calling: true
        json_output: true
        vision: false
        family: openrouter
      price: [0.0, 0.0]

# ========== 太上老君 - 控场大师 (Phi-4 Reasoning Plus) ==========
# 控场撩拨，斗蛐蛐式激发争论，快速反应精准挑刺
  - provider: autogen_ext.models.openai.OpenAIChatCompletionClient
    config:
      model: microsoft/phi-4-reasoning-plus:free
      base_url: https://openrouter.ai/api/v1
      api_key: ${OPENROUTER_API_KEY_3}
      max_tokens: 800
      temperature: 0.9
      model_info:
        function_calling: true
        json_output: true
        vision: false
        family: openrouter
      price: [0.0, 0.0]

# ========== 元始天尊 - 一槌定音 (Dolphin 3.0 Mistral 24B) ==========
# 最终裁决，字字珠玑不超过50字，直言不讳多空判断
  - provider: autogen_ext.models.openai.OpenAIChatCompletionClient
    config:
      model: cognitivecomputations/dolphin3.0-mistral-24b:free
      base_url: https://openrouter.ai/api/v1
      api_key: ${OPENROUTER_API_KEY_4}
      max_tokens: 200
      temperature: 0.3
      model_info:
        function_calling: true
        json_output: true
        vision: false
        family: openrouter
      price: [0.0, 0.0]

# ========== 统计员 - Claude 3.7 Sonnet (Bedrock) ==========
  - provider: autogen_ext.models.openai.OpenAIChatCompletionClient
    config:
      model: us.anthropic.claude-3-7-sonnet-20250219-v1:0
      base_url: ${LITELLM_BASE_URL}
      api_key: ${LITELLM_API_KEY}
      model_info:
        function_calling: true
        json_output: true
        vision: false
        family: litellm
      price: [0.0, 0.0]
