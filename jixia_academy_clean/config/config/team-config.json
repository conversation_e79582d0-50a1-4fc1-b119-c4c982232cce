{"provider": "autogen_agentchat.teams.SelectorGroupChat", "component_type": "team", "version": 1, "component_version": 1, "description": "A team with 3 agents - a Research Assistant that performs web searches and analyzes information, a Verifier that ensures research quality and completeness, and a Summary Agent that provides a detailed markdown summary of the research as a report to the user.", "label": "Deep Research Team_17429", "config": {"participants": [{"provider": "autogen_agentchat.agents.AssistantAgent", "component_type": "agent", "version": 1, "component_version": 1, "description": "An agent that provides assistance with tool use.", "label": "AssistantAgent", "config": {"name": "research_assistant", "model_client": {"provider": "autogen_ext.models.openai.OpenAIChatCompletionClient", "component_type": "model", "version": 1, "component_version": 1, "description": "gemini/gemma-3-27b-it\n", "label": "litellm_gemini/gemma-3-27b-it", "config": {"model": "gemini/gemma-3-27b-it", "model_info": {"vision": false, "function_calling": true, "json_output": false, "family": "litellm"}, "base_url": "https://litellm.git4ta.fun/v1/", "api_key": "sk-UxfPREr11dqTYnxwnq_qvw"}}, "tools": [{"provider": "autogen_core.tools.FunctionTool", "component_type": "tool", "version": 1, "component_version": 1, "description": "Create custom tools by wrapping standard Python functions.", "label": "FunctionTool", "config": {"source_code": "async def google_search(\n    query: str,\n    num_results: int = 3,\n    include_snippets: bool = True,\n    include_content: bool = True,\n    content_max_length: Optional[int] = 10000,\n    language: str = \"en\",\n    country: Optional[str] = None,\n    safe_search: bool = True,\n) -> List[Dict[str, str]]:\n    \"\"\"\n    Perform a Google search using the Custom Search API and optionally fetch webpage content.\n\n    Args:\n        query: Search query string\n        num_results: Number of results to return (max 10)\n        include_snippets: Include result snippets in output\n        include_content: Include full webpage content in markdown format\n        content_max_length: Maximum length of webpage content (if included)\n        language: Language code for search results (e.g., en, es, fr)\n        country: Optional country code for search results (e.g., us, uk)\n        safe_search: Enable safe search filtering\n\n    Returns:\n        List[Dict[str, str]]: List of search results, each containing:\n            - title: Result title\n            - link: Result URL\n            - snippet: Result description (if include_snippets=True)\n            - content: Webpage content in markdown (if include_content=True)\n    \"\"\"\n    api_key = os.getenv(\"GOOGLE_API_KEY\")\n    cse_id = os.getenv(\"GOOGLE_CSE_ID\")\n\n    if not api_key or not cse_id:\n        raise ValueError(\"Missing required environment variables. Please set GOOGLE_API_KEY and GOOGLE_CSE_ID.\")\n\n    num_results = min(max(1, num_results), 10)\n\n    async def fetch_page_content(url: str, max_length: Optional[int] = 50000) -> str:\n        \"\"\"Helper function to fetch and convert webpage content to markdown\"\"\"\n        headers = {\"User-Agent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36\"}\n\n        try:\n            async with httpx.AsyncClient() as client:\n                response = await client.get(url, headers=headers, timeout=10)\n                response.raise_for_status()\n\n                soup = BeautifulSoup(response.text, \"html.parser\")\n\n                # Remove script and style elements\n                for script in soup([\"script\", \"style\"]):\n                    script.decompose()\n\n                # Convert relative URLs to absolute\n                for tag in soup.find_all([\"a\", \"img\"]):\n                    if tag.get(\"href\"):\n                        tag[\"href\"] = urljoin(url, tag[\"href\"])\n                    if tag.get(\"src\"):\n                        tag[\"src\"] = urljoin(url, tag[\"src\"])\n\n                h2t = html2text.HTML2Text()\n                h2t.body_width = 0\n                h2t.ignore_images = False\n                h2t.ignore_emphasis = False\n                h2t.ignore_links = False\n                h2t.ignore_tables = False\n\n                markdown = h2t.handle(str(soup))\n\n                if max_length and len(markdown) > max_length:\n                    markdown = markdown[:max_length] + \"\\n...(truncated)\"\n\n                return markdown.strip()\n\n        except Exception as e:\n            return f\"Error fetching content: {str(e)}\"\n\n    params = {\n        \"key\": api_key,\n        \"cx\": cse_id,\n        \"q\": query,\n        \"num\": num_results,\n        \"hl\": language,\n        \"safe\": \"active\" if safe_search else \"off\",\n    }\n\n    if country:\n        params[\"gl\"] = country\n\n    try:\n        async with httpx.AsyncClient() as client:\n            response = await client.get(\"https://www.googleapis.com/customsearch/v1\", params=params, timeout=10)\n            response.raise_for_status()\n            data = response.json()\n\n            results = []\n            if \"items\" in data:\n                for item in data[\"items\"]:\n                    result = {\"title\": item.get(\"title\", \"\"), \"link\": item.get(\"link\", \"\")}\n                    if include_snippets:\n                        result[\"snippet\"] = item.get(\"snippet\", \"\")\n\n                    if include_content:\n                        result[\"content\"] = await fetch_page_content(result[\"link\"], max_length=content_max_length)\n\n                    results.append(result)\n\n            return results\n\n    except httpx.RequestError as e:\n        raise ValueError(f\"Failed to perform search: {str(e)}\") from e\n    except KeyError as e:\n        raise ValueError(f\"Invalid API response format: {str(e)}\") from e\n    except Exception as e:\n        raise ValueError(f\"Error during search: {str(e)}\") from e\n", "name": "google_search", "description": "\n    Perform Google searches using the Custom Search API with optional webpage content fetching.\n    Requires GOOGLE_API_KEY and GOOGLE_CSE_ID environment variables to be set.\n    ", "global_imports": [{"module": "typing", "imports": ["List", "Dict", "Optional"]}, "os", "httpx", "html2text", {"module": "bs4", "imports": ["BeautifulSoup"]}, {"module": "urllib.parse", "imports": ["url<PERSON>"]}], "has_cancellation_support": false}}, {"provider": "autogen_core.tools.FunctionTool", "component_type": "tool", "version": 1, "component_version": 1, "description": "Create custom tools by wrapping standard Python functions.", "label": "FunctionTool", "config": {"source_code": "async def fetch_webpage(\n    url: str, include_images: bool = True, max_length: Optional[int] = None, headers: Optional[Dict[str, str]] = None\n) -> str:\n    \"\"\"Fetch a webpage and convert it to markdown format.\n\n    Args:\n        url: The URL of the webpage to fetch\n        include_images: Whether to include image references in the markdown\n        max_length: Maximum length of the output markdown (if None, no limit)\n        headers: Optional HTTP headers for the request\n\n    Returns:\n        str: Markdown version of the webpage content\n\n    Raises:\n        ValueError: If the URL is invalid or the page can't be fetched\n    \"\"\"\n    # Use default headers if none provided\n    if headers is None:\n        headers = {\"User-Agent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36\"}\n\n    try:\n        # Fetch the webpage\n        async with httpx.AsyncClient() as client:\n            response = await client.get(url, headers=headers, timeout=10)\n            response.raise_for_status()\n\n            # Parse HTML\n            soup = BeautifulSoup(response.text, \"html.parser\")\n\n            # Remove script and style elements\n            for script in soup([\"script\", \"style\"]):\n                script.decompose()\n\n            # Convert relative URLs to absolute\n            for tag in soup.find_all([\"a\", \"img\"]):\n                if tag.get(\"href\"):\n                    tag[\"href\"] = urljoin(url, tag[\"href\"])\n                if tag.get(\"src\"):\n                    tag[\"src\"] = urljoin(url, tag[\"src\"])\n\n            # Configure HTML to Markdown converter\n            h2t = html2text.HTML2Text()\n            h2t.body_width = 0  # No line wrapping\n            h2t.ignore_images = not include_images\n            h2t.ignore_emphasis = False\n            h2t.ignore_links = False\n            h2t.ignore_tables = False\n\n            # Convert to markdown\n            markdown = h2t.handle(str(soup))\n\n            # Trim if max_length is specified\n            if max_length and len(markdown) > max_length:\n                markdown = markdown[:max_length] + \"\\n...(truncated)\"\n\n            return markdown.strip()\n\n    except httpx.RequestError as e:\n        raise ValueError(f\"Failed to fetch webpage: {str(e)}\") from e\n    except Exception as e:\n        raise ValueError(f\"Error processing webpage: {str(e)}\") from e\n", "name": "fetch_webpage", "description": "Fetch a webpage and convert it to markdown format, with options for including images and limiting length", "global_imports": ["os", "html2text", {"module": "typing", "imports": ["Optional", "Dict"]}, "httpx", {"module": "bs4", "imports": ["BeautifulSoup"]}, {"module": "html2text", "imports": ["HTML2Text"]}, {"module": "urllib.parse", "imports": ["url<PERSON>"]}], "has_cancellation_support": false}}], "model_context": {"provider": "autogen_core.model_context.UnboundedChatCompletionContext", "component_type": "chat_completion_context", "version": 1, "component_version": 1, "description": "An unbounded chat completion context that keeps a view of the all the messages.", "label": "UnboundedChatCompletionContext", "config": {}}, "description": "A research assistant that performs web searches and analyzes information", "system_message": "你倾向于用中文回答问题You are a research assistant focused on finding accurate information.\n        Use the google_search tool to find relevant information.\n        Break down complex queries into specific search terms.\n        Always verify information across multiple sources when possible.\n        When you find relevant information, explain why it's relevant and how it connects to the query. When you get feedback from the a verifier agent, use your tools to act on the feedback and make progress.", "model_client_stream": false, "reflect_on_tool_use": false, "tool_call_summary_format": "{result}"}}, {"provider": "autogen_agentchat.agents.AssistantAgent", "component_type": "agent", "version": 1, "component_version": 1, "description": "An agent that provides assistance with tool use.", "label": "AssistantAgent", "config": {"name": "verifier", "model_client": {"provider": "autogen_ext.models.openai.OpenAIChatCompletionClient", "component_type": "model", "version": 1, "component_version": 1, "description": "OpenAI GPT-4o-mini", "label": "Github GPT-4o-mini", "config": {"model": "gpt-4o-mini", "base_url": "https://models.inference.ai.azure.com/", "api_key": "****************************************"}}, "tools": [{"provider": "autogen_core.tools.FunctionTool", "component_type": "tool", "version": 1, "component_version": 1, "description": "A tool that performs Google searches using the Google Custom Search API. Requires the requests library, [GOOGLE_API_KEY, GOOGLE_CSE_ID] to be set,  env variable to function.", "label": "Google Search Tool", "config": {"source_code": "async def google_search(\n    query: str,\n    num_results: int = 3,\n    include_snippets: bool = True,\n    include_content: bool = True,\n    content_max_length: Optional[int] = 10000,\n    language: str = \"en\",\n    country: Optional[str] = None,\n    safe_search: bool = True,\n) -> List[Dict[str, str]]:\n    \"\"\"\n    Perform a Google search using the Custom Search API and optionally fetch webpage content.\n\n    Args:\n        query: Search query string\n        num_results: Number of results to return (max 10)\n        include_snippets: Include result snippets in output\n        include_content: Include full webpage content in markdown format\n        content_max_length: Maximum length of webpage content (if included)\n        language: Language code for search results (e.g., en, es, fr)\n        country: Optional country code for search results (e.g., us, uk)\n        safe_search: Enable safe search filtering\n\n    Returns:\n        List[Dict[str, str]]: List of search results, each containing:\n            - title: Result title\n            - link: Result URL\n            - snippet: Result description (if include_snippets=True)\n            - content: Webpage content in markdown (if include_content=True)\n    \"\"\"\n    api_key = os.getenv(\"GOOGLE_API_KEY\")\n    cse_id = os.getenv(\"GOOGLE_CSE_ID\")\n\n    if not api_key or not cse_id:\n        raise ValueError(\"Missing required environment variables. Please set GOOGLE_API_KEY and GOOGLE_CSE_ID.\")\n\n    num_results = min(max(1, num_results), 10)\n\n    async def fetch_page_content(url: str, max_length: Optional[int] = 50000) -> str:\n        \"\"\"Helper function to fetch and convert webpage content to markdown\"\"\"\n        headers = {\"User-Agent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36\"}\n\n        try:\n            async with httpx.AsyncClient() as client:\n                response = await client.get(url, headers=headers, timeout=10)\n                response.raise_for_status()\n\n                soup = BeautifulSoup(response.text, \"html.parser\")\n\n                # Remove script and style elements\n                for script in soup([\"script\", \"style\"]):\n                    script.decompose()\n\n                # Convert relative URLs to absolute\n                for tag in soup.find_all([\"a\", \"img\"]):\n                    if tag.get(\"href\"):\n                        tag[\"href\"] = urljoin(url, tag[\"href\"])\n                    if tag.get(\"src\"):\n                        tag[\"src\"] = urljoin(url, tag[\"src\"])\n\n                h2t = html2text.HTML2Text()\n                h2t.body_width = 0\n                h2t.ignore_images = False\n                h2t.ignore_emphasis = False\n                h2t.ignore_links = False\n                h2t.ignore_tables = False\n\n                markdown = h2t.handle(str(soup))\n\n                if max_length and len(markdown) > max_length:\n                    markdown = markdown[:max_length] + \"\\n...(truncated)\"\n\n                return markdown.strip()\n\n        except Exception as e:\n            return f\"Error fetching content: {str(e)}\"\n\n    params = {\n        \"key\": api_key,\n        \"cx\": cse_id,\n        \"q\": query,\n        \"num\": num_results,\n        \"hl\": language,\n        \"safe\": \"active\" if safe_search else \"off\",\n    }\n\n    if country:\n        params[\"gl\"] = country\n\n    try:\n        async with httpx.AsyncClient() as client:\n            response = await client.get(\"https://www.googleapis.com/customsearch/v1\", params=params, timeout=10)\n            response.raise_for_status()\n            data = response.json()\n\n            results = []\n            if \"items\" in data:\n                for item in data[\"items\"]:\n                    result = {\"title\": item.get(\"title\", \"\"), \"link\": item.get(\"link\", \"\")}\n                    if include_snippets:\n                        result[\"snippet\"] = item.get(\"snippet\", \"\")\n\n                    if include_content:\n                        result[\"content\"] = await fetch_page_content(result[\"link\"], max_length=content_max_length)\n\n                    results.append(result)\n\n            return results\n\n    except httpx.RequestError as e:\n        raise ValueError(f\"Failed to perform search: {str(e)}\") from e\n    except KeyError as e:\n        raise ValueError(f\"Invalid API response format: {str(e)}\") from e\n    except Exception as e:\n        raise ValueError(f\"Error during search: {str(e)}\") from e\n", "name": "google_search", "description": "\n    Perform Google searches using the Custom Search API with optional webpage content fetching.\n    Requires GOOGLE_API_KEY and GOOGLE_CSE_ID environment variables to be set.\n    ", "global_imports": [{"module": "typing", "imports": ["List", "Dict", "Optional"]}, "os", "httpx", "html2text", {"module": "bs4", "imports": ["BeautifulSoup"]}, {"module": "urllib.parse", "imports": ["url<PERSON>"]}], "has_cancellation_support": false}}], "model_context": {"provider": "autogen_core.model_context.UnboundedChatCompletionContext", "component_type": "chat_completion_context", "version": 1, "component_version": 1, "description": "An unbounded chat completion context that keeps a view of the all the messages.", "label": "UnboundedChatCompletionContext", "config": {}}, "description": "A verification specialist who ensures research quality and completeness", "system_message": "You are a research verification specialist.\n        Your role is to:\n       0, 你会用中文思考和回答问题\n        1. Verify that search queries are effective and suggest improvements if needed\n        2. Explore drill downs where needed e.g, if the answer is likely in a link in the returned search results, suggest clicking on the link\n        3. Suggest additional angles or perspectives to explore. Be judicious in suggesting new paths to avoid scope creep or wasting resources, if the task appears to be addressed and we can provide a report, do this and respond with \"TERMINATE\".\n        4. Track progress toward answering the original question\n        5. When the research is complete, provide a detailed summary in markdown format. For incomplete research, end your message with \"CONTINUE RESEARCH\". For complete research, end your message with APPROVED.\n        Your responses should be structured as:\n        - Progress Assessment\n        - Gaps/Issues (if any)\n        - Suggestions (if needed)\n        - Next Steps or Final Summary", "model_client_stream": true, "reflect_on_tool_use": true, "tool_call_summary_format": "{result}"}}, {"provider": "autogen_agentchat.agents.AssistantAgent", "component_type": "agent", "version": 1, "component_version": 1, "description": "An agent that provides assistance with tool use.", "label": "AssistantAgent", "config": {"name": "summary_agent", "model_client": {"provider": "autogen_ext.models.openai.OpenAIChatCompletionClient", "component_type": "model", "version": 1, "component_version": 1, "description": "OpenAI GPT-4o-mini", "label": "Github GPT-4o-mini", "config": {"model": "gpt-4o-mini", "base_url": "https://models.inference.ai.azure.com/", "api_key": "****************************************"}}, "tools": [], "model_context": {"provider": "autogen_core.model_context.UnboundedChatCompletionContext", "component_type": "chat_completion_context", "version": 1, "component_version": 1, "description": "An unbounded chat completion context that keeps a view of the all the messages.", "label": "UnboundedChatCompletionContext", "config": {}}, "description": "A summary agent that provides a detailed markdown summary of the research as a report to the user.", "system_message": "You are a summary agent. Your role is to provide a detailed markdown summary of the research as a report to the user. Your report should have a reasonable title that matches the research question and should summarize the key details in the results found in natural an actionable manner. The main results/answer should be in the first paragraph. Where reasonable, your report should have clear comparison tables that drive critical insights. Most importantly, you should have a reference section and cite the key sources (where available) for facts obtained INSIDE THE MAIN REPORT. Also, where appropriate, you may add images if available that illustrate concepts needed for the summary.\n        Your report should end with the word \"TERMINATE\" to signal the end of the conversation.", "model_client_stream": false, "reflect_on_tool_use": false, "tool_call_summary_format": "{result}"}}, {"provider": "autogen_agentchat.agents.UserProxyAgent", "component_type": "agent", "version": 1, "component_version": 1, "description": "An agent that can represent a human user through an input function.", "label": "UserProxyAgent", "config": {"name": "user_proxy", "description": "a human user that should be consulted only when the assistant_agent is unable to verify the information provided by the websurfer_agent"}}], "model_client": {"provider": "autogen_ext.models.openai.OpenAIChatCompletionClient", "component_type": "model", "version": 1, "component_version": 1, "description": "OpenAI GPT-4o-mini", "label": "Github GPT-4o-mini", "config": {"model": "gpt-4o-mini", "base_url": "https://models.inference.ai.azure.com/", "api_key": "****************************************"}}, "termination_condition": {"provider": "autogen_agentchat.base.OrTerminationCondition", "component_type": "termination", "version": 1, "component_version": 1, "label": "OrTerminationCondition", "config": {"conditions": [{"provider": "autogen_agentchat.conditions.TextMentionTermination", "component_type": "termination", "version": 1, "component_version": 1, "description": "Terminate the conversation if a specific text is mentioned.", "label": "TextMentionTermination", "config": {"text": "TERMINATE"}}, {"provider": "autogen_agentchat.conditions.MaxMessageTermination", "component_type": "termination", "version": 1, "component_version": 1, "description": "Terminate the conversation after a maximum number of messages have been exchanged.", "label": "MaxMessageTermination", "config": {"max_messages": 30, "include_agent_event": false}}]}}, "selector_prompt": "You are coordinating a research team by selecting the team member to speak/act next. The following team member roles are available:\n    {roles}.\n    The research_assistant performs searches and analyzes information.\n    The verifier evaluates progress and ensures completeness.\n    The summary_agent provides a detailed markdown summary of the research as a report to the user.\n\n    Given the current context, select the most appropriate next speaker.\n    The research_assistant should search and analyze.\n    The verifier should evaluate progress and guide the research (select this role is there is a need to verify/evaluate progress). You should ONLY select the summary_agent role if the research is complete and it is time to generate a report.\n\n    Base your selection on:\n    1. Current stage of research\n    2. Last speaker's findings or suggestions\n    3. Need for verification vs need for new information\n    Read the following conversation. Then select the next role from {participants} to play. Only return the role.\n\n    {history}\n\n    Read the above conversation. Then select the next role from {participants} to play. ONLY RETURN THE ROLE.", "allow_repeated_speaker": true, "max_selector_attempts": 3}}