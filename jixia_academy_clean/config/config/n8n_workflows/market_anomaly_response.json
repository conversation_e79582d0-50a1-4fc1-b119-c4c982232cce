{"name": "Market Anomaly Response Workflow", "nodes": [{"parameters": {"httpMethod": "POST", "path": "market-anomaly", "responseMode": "responseNode", "options": {}}, "id": "webhook-trigger", "name": "Webhook Trigger", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 300], "webhookId": "market-anomaly-webhook"}, {"parameters": {"conditions": {"string": [{"value1": "={{$json.data.severity}}", "operation": "equal", "value2": "critical"}]}}, "id": "severity-check", "name": "Check Severity", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [460, 300]}, {"parameters": {"functionCode": "// 解析异常事件数据\nconst eventData = $input.first().json.data;\nconst symbol = eventData.symbol;\nconst score = eventData.score;\nconst severity = eventData.severity;\nconst description = eventData.description;\nconst timestamp = eventData.timestamp;\n\n// 根据标的类型确定响应策略\nlet responseStrategy = 'default';\nlet urgencyLevel = 'medium';\n\nif (symbol.includes('BTC') || symbol.includes('CRYPTO')) {\n  responseStrategy = 'crypto';\n  urgencyLevel = 'high';\n} else if (symbol.includes('XAU') || symbol.includes('GLD')) {\n  responseStrategy = 'gold';\n  urgencyLevel = 'high';\n} else if (symbol.includes('CL') || symbol.includes('USO')) {\n  responseStrategy = 'oil';\n  urgencyLevel = 'medium';\n}\n\n// 计算风险等级\nlet riskLevel = 'low';\nif (score >= 8.0) {\n  riskLevel = 'critical';\n} else if (score >= 6.0) {\n  riskLevel = 'high';\n} else if (score >= 4.0) {\n  riskLevel = 'medium';\n}\n\n// 生成告警消息\nconst alertMessage = `🚨 ${severity.toUpperCase()} ALERT: ${symbol}\n` +\n  `异常评分: ${score.toFixed(2)}/10\n` +\n  `风险等级: ${riskLevel}\n` +\n  `描述: ${description}\n` +\n  `时间: ${new Date(timestamp).toLocaleString('zh-CN')}\n` +\n  `响应策略: ${responseStrategy}`;\n\nreturn {\n  symbol,\n  score,\n  severity,\n  riskLevel,\n  responseStrategy,\n  urgencyLevel,\n  alertMessage,\n  timestamp,\n  originalData: eventData\n};"}, "id": "process-event", "name": "Process Event Data", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [680, 300]}, {"parameters": {"channel": "#trading-alerts", "text": "={{$json.alertMessage}}", "otherOptions": {"username": "Market Monitor Bot", "icon_emoji": ":warning:"}, "attachments": [{"color": "danger", "fields": [{"title": "Symbol", "value": "={{$json.symbol}}", "short": true}, {"title": "Risk Level", "value": "={{$json.riskLevel}}", "short": true}, {"title": "Score", "value": "={{$json.score}}", "short": true}, {"title": "Strategy", "value": "={{$json.responseStrategy}}", "short": true}]}]}, "id": "slack-notification", "name": "Send <PERSON><PERSON><PERSON>", "type": "n8n-nodes-base.slack", "typeVersion": 1, "position": [900, 200], "credentials": {"slackApi": {"id": "slack-credentials", "name": "Slack API"}}}, {"parameters": {"to": "${ALERT_EMAIL}", "subject": "🚨 Market Anomaly Alert: {{$json.symbol}}", "text": "{{$json.alertMessage}}\n\n详细信息:\n{{JSON.stringify($json.originalData, null, 2)}}", "options": {"allowUnauthorizedCerts": false}}, "id": "email-notification", "name": "Send <PERSON><PERSON>", "type": "n8n-nodes-base.emailSend", "typeVersion": 1, "position": [900, 300], "credentials": {"smtp": {"id": "email-smtp", "name": "SMTP Email"}}}, {"parameters": {"conditions": {"string": [{"value1": "={{$json.riskLevel}}", "operation": "equal", "value2": "critical"}]}}, "id": "war-room-check", "name": "Check War Room Trigger", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [900, 400]}, {"parameters": {"requestMethod": "POST", "url": "${WAR_ROOM_WEBHOOK_URL}", "jsonParameters": true, "options": {}, "bodyParametersJson": "={\n  \"event_type\": \"market_anomaly\",\n  \"symbol\": $json.symbol,\n  \"severity\": $json.severity,\n  \"risk_level\": $json.riskLevel,\n  \"score\": $json.score,\n  \"timestamp\": $json.timestamp,\n  \"action\": \"initiate_war_room\",\n  \"participants\": [\n    \"risk_manager\",\n    \"portfolio_manager\",\n    \"senior_trader\",\n    \"compliance_officer\"\n  ],\n  \"priority\": \"urgent\",\n  \"estimated_duration\": \"30_minutes\",\n  \"meeting_platform\": \"zoom\",\n  \"data\": $json.originalData\n}"}, "id": "trigger-war-room", "name": "Trigger War Room", "type": "n8n-nodes-base.httpRequest", "typeVersion": 1, "position": [1120, 500]}, {"parameters": {"functionCode": "// 启动稷下学宫辩论系统\nconst eventData = $input.first().json;\nconst symbol = eventData.symbol;\nconst severity = eventData.severity;\nconst description = eventData.originalData.description;\n\n// 构建辩论主题\nconst debateTopic = `关于${symbol}异常波动的市场分析与应对策略`;\n\n// 根据标的类型选择专业辩手\nlet specializedDebaters = [];\nif (symbol.includes('BTC') || symbol.includes('CRYPTO')) {\n  specializedDebaters = ['crypto_expert', 'blockchain_analyst', 'defi_specialist'];\n} else if (symbol.includes('XAU') || symbol.includes('GLD')) {\n  specializedDebaters = ['precious_metals_expert', 'macro_economist', 'inflation_analyst'];\n} else if (symbol.includes('CL') || symbol.includes('USO')) {\n  specializedDebaters = ['energy_analyst', 'geopolitical_expert', 'supply_chain_specialist'];\n}\n\n// 生成辩论配置\nconst debateConfig = {\n  topic: debateTopic,\n  urgency: severity === 'critical' ? 'emergency' : 'high',\n  duration_minutes: severity === 'critical' ? 15 : 30,\n  participants: [\n    'taishang_laojun',  // MCP事实核查\n    'yuanshi_tianzun',  // 主观分析\n    'lingbao_daojun',   // 流程主持\n    ...specializedDebaters\n  ],\n  context: {\n    symbol: symbol,\n    anomaly_score: eventData.score,\n    market_data: eventData.originalData.raw_data,\n    detection_time: eventData.timestamp,\n    severity: severity\n  },\n  mcp_requirements: {\n    real_time_data: true,\n    news_sources: ['ib_news_flow', 'dow_jones', 'fly_on_the_wall'],\n    fundamental_data: true,\n    sentiment_analysis: true\n  }\n};\n\nreturn {\n  debate_config: debateConfig,\n  webhook_url: process.env.JIXIA_ACADEMY_WEBHOOK,\n  action: 'start_emergency_debate'\n};"}, "id": "prepare-debate", "name": "Prepare Jixia Academy Debate", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [1120, 300]}, {"parameters": {"requestMethod": "POST", "url": "={{$json.webhook_url}}", "jsonParameters": true, "options": {}, "bodyParametersJson": "={{JSON.stringify($json.debate_config)}}"}, "id": "start-debate", "name": "Start Jixia Academy Debate", "type": "n8n-nodes-base.httpRequest", "typeVersion": 1, "position": [1340, 300]}, {"parameters": {"functionCode": "// 数据库记录和监控\nconst eventData = $input.first().json;\n\n// 记录响应行动\nconst responseLog = {\n  event_id: eventData.originalData.event_id,\n  symbol: eventData.symbol,\n  timestamp: new Date().toISOString(),\n  actions_taken: [\n    'slack_notification_sent',\n    'email_alert_sent',\n    eventData.riskLevel === 'critical' ? 'war_room_triggered' : null,\n    'jixia_debate_initiated'\n  ].filter(Boolean),\n  response_time_seconds: Math.floor((new Date() - new Date(eventData.timestamp)) / 1000),\n  escalation_level: eventData.riskLevel,\n  next_review_time: new Date(Date.now() + 30 * 60 * 1000).toISOString() // 30分钟后复查\n};\n\nreturn {\n  response_log: responseLog,\n  status: 'completed',\n  message: `Market anomaly response completed for ${eventData.symbol}`\n};"}, "id": "log-response", "name": "Log Response Actions", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [1560, 300]}, {"parameters": {"respondWith": "json", "responseBody": "={{$json}}"}, "id": "response-node", "name": "Response", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [1780, 300]}, {"parameters": {"amount": 1800, "unit": "seconds"}, "id": "wait-30min", "name": "Wait 30 Minutes", "type": "n8n-nodes-base.wait", "typeVersion": 1, "position": [1560, 500]}, {"parameters": {"functionCode": "// 30分钟后的跟进检查\nconst originalEvent = $input.first().json;\n\n// 检查市场状况是否有改善\nconst followUpCheck = {\n  original_event_id: originalEvent.response_log.event_id,\n  symbol: originalEvent.response_log.symbol,\n  check_time: new Date().toISOString(),\n  action: 'follow_up_market_check',\n  check_type: 'post_anomaly_review'\n};\n\nreturn followUpCheck;"}, "id": "follow-up-check", "name": "Follow-up Market Check", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [1780, 500]}, {"parameters": {"requestMethod": "POST", "url": "${EVENT_MONITOR_WEBHOOK_URL}/follow-up", "jsonParameters": true, "options": {}, "bodyParametersJson": "={{JSON.stringify($json)}}"}, "id": "trigger-follow-up", "name": "Trigger Follow-up Analysis", "type": "n8n-nodes-base.httpRequest", "typeVersion": 1, "position": [2000, 500]}], "connections": {"webhook-trigger": {"main": [[{"node": "severity-check", "type": "main", "index": 0}]]}, "severity-check": {"main": [[{"node": "process-event", "type": "main", "index": 0}], [{"node": "process-event", "type": "main", "index": 0}]]}, "process-event": {"main": [[{"node": "slack-notification", "type": "main", "index": 0}, {"node": "email-notification", "type": "main", "index": 0}, {"node": "war-room-check", "type": "main", "index": 0}, {"node": "prepare-debate", "type": "main", "index": 0}]]}, "war-room-check": {"main": [[{"node": "trigger-war-room", "type": "main", "index": 0}]]}, "prepare-debate": {"main": [[{"node": "start-debate", "type": "main", "index": 0}]]}, "start-debate": {"main": [[{"node": "log-response", "type": "main", "index": 0}]]}, "log-response": {"main": [[{"node": "response-node", "type": "main", "index": 0}, {"node": "wait-30min", "type": "main", "index": 0}]]}, "wait-30min": {"main": [[{"node": "follow-up-check", "type": "main", "index": 0}]]}, "follow-up-check": {"main": [[{"node": "trigger-follow-up", "type": "main", "index": 0}]]}}, "active": true, "settings": {"timezone": "Asia/Shanghai", "saveManualExecutions": true, "callerPolicy": "workflowOwner"}, "versionId": "1", "meta": {"templateCredsSetupCompleted": true}, "id": "market-anomaly-response", "tags": [{"createdAt": "2024-01-01T00:00:00.000Z", "updatedAt": "2024-01-01T00:00:00.000Z", "id": "trading", "name": "Trading"}, {"createdAt": "2024-01-01T00:00:00.000Z", "updatedAt": "2024-01-01T00:00:00.000Z", "id": "alerts", "name": "<PERSON><PERSON><PERSON>"}]}